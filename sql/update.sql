-- 0304 角色表增加角色类型字段

ALTER TABLE `UcRole` ADD COLUMN `roleType` varchar(255) NULL COMMENT '角色类型' AFTER `roleManagescope`;
INSERT INTO `usercenter`.`UcDict`(`dictId`, `dictType`, `dictName`, `dictCode`, `dictVal`, `dictMemo`, `dictLevelcode`, `dictSeq`) VALUES ('DICT20190501010000000001', 'roletype', '分校业务', 'school_business', 'school_business', '分校业务', '0000000002', 2);
INSERT INTO `usercenter`.`UcDict`(`dictId`, `dictType`, `dictName`, `dictCode`, `dictVal`, `dictMemo`, `dictLevelcode`, `dictSeq`) VALUES ('DICT20190501010000000002', 'roletype', '分校财务', 'school_finance', 'school_finance', '分校财务', '0000000003', 3);
INSERT INTO `usercenter`.`UcDict`(`dictId`, `dictType`, `dictName`, `dictCode`, `dictVal`, `dictMemo`, `dictLevelcode`, `dictSeq`) VALUES ('DICT20190501010000000003', 'roletype', '分校校长', 'school_headmaster', 'school_headmaster', '分校校长', '0000000004', 4);
INSERT INTO `usercenter`.`UcDict`(`dictId`, `dictType`, `dictName`, `dictCode`, `dictVal`, `dictMemo`, `dictLevelcode`, `dictSeq`) VALUES ('DICT20190501010000000004', 'roletype', '总部业务', 'main_business', 'main_business', '总部业务', '0000000005', 5);
INSERT INTO `usercenter`.`UcDict`(`dictId`, `dictType`, `dictName`, `dictCode`, `dictVal`, `dictMemo`, `dictLevelcode`, `dictSeq`) VALUES ('DICT20190501010000000005', 'roletype', '总部财务', 'main_finance', 'main_finance', '总部财务', '0000000006', 6);
INSERT INTO `usercenter`.`UcDict`(`dictId`, `dictType`, `dictName`, `dictCode`, `dictVal`, `dictMemo`, `dictLevelcode`, `dictSeq`) VALUES ('DICT20190501010000000006', 'roletype', '总部出纳', 'main_cashier', 'main_cashier', '总部出纳', '0000000007', 7);
INSERT INTO `usercenter`.`UcDict`(`dictId`, `dictType`, `dictName`, `dictCode`, `dictVal`, `dictMemo`, `dictLevelcode`, `dictSeq`) VALUES ('DICT20190501010000000007', 'roletype', '总经办', 'main_office', 'main_office', '总经办', '0000000008', 8);

-- 0316 更新角色字段
ALTER TABLE `usercenter`.`UcRole`
ADD COLUMN `roleIpstatus` varchar(1) NULL COMMENT '是否允许授权ip' AFTER `roleType`,
ADD COLUMN `roleMobilecount` int(255) NULL COMMENT '允许查看手机号次数' AFTER `roleIpstatus`;
update UcRole set roleMobilecount = 0;
update UcRole set roleIpstatus = roleAdminstatus;

-- 20201123 角色表增加roleIdcardcount和roleWeixincount字段
ALTER TABLE `usercenter`.`UcRole`
ADD COLUMN `roleIdcardcount` int(255) NULL COMMENT '允许查看身份证号次数' AFTER `roleMobilecount`,
ADD COLUMN `roleWeixincount` int(255) NULL COMMENT '允许查看微信次数' AFTER `roleIdcardcount`;
update UcRole set roleIdcardcount = 0, roleWeixincount = 0;
update UcRole set roleIdcardcount = 100000, roleWeixincount = 100000 where roleAdminstatus = 'Y';
-- 20201123 回滚脚本
update `usercenter`.`UcRole` SET roleIdcardcount = null;
update `usercenter`.`UcRole` SET roleWeixincount = null;
alter table `usercenter`.`UcRole` drop column roleIdcardcount;
alter table `usercenter`.`UcRole` drop column roleWeixincount;