<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.youlu</groupId>
	<artifactId>niceloo-usercenter</artifactId>
	<version>3.0.70</version>
	<packaging>war</packaging>
	<name>用户中心</name>

	<!-- 手动指定 -->
	<repositories>
		<repository>
			<id>youlu-nexus</id>
			<url>http://nexus.niceloo.com:11081/repository/maven-public/</url>
			<layout>default</layout>
		</repository>
	</repositories>

	<!-- 项目SVN地址 -->
	<scm>
		<url>https://192.168.10.80:8443/svn/WebProject/MicroService/UserCenter</url>
	</scm>

	<!-- bug管理地址 -->
	<issueManagement>
		<url>https://www.tapd.cn/40396619/bugtrace/bugreports/my_view</url>
	</issueManagement>

	<!-- 持续集成 -->
	<ciManagement>
		<url>[待定]</url>
	</ciManagement>

	<!-- 项目开发者信息 -->
	<developers>
		<developer>
			<id>niaoshuai</id>
			<name>任帅鹏</name>
			<email><EMAIL></email>
			<organization>优路教育</organization>
			<roles>
				<role>核心开发</role>
			</roles>
		</developer>
	</developers>


	<dependencies>
<!--		<dependency>-->
<!--			<groupId>org.mongodb</groupId>-->
<!--			<artifactId>mongo-java-driver</artifactId>-->
<!--			<version>3.11.2</version>-->
<!--		</dependency>-->

		<dependency>
			<groupId>com.youlu</groupId>
			<artifactId>niceloo-core</artifactId>
			<version>1.4.19-SNAPSHOT</version>
		</dependency>
		
		<dependency>
			<groupId>org.nobject</groupId>
			<artifactId>nobject-common</artifactId>
			<version>2.1.41-SNAPSHOT</version>
		</dependency>
		<!-- javassist -->
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
			<version>3.24.1-GA</version>
		</dependency>

		<!-- mysql -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.44</version>
		</dependency>

		<!-- sqlserver -->
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
			<version>6.4.0.jre8</version>
			<scope>runtime</scope>
		</dependency>

		<!-- servlet api -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.1.0</version>
			<scope>provided</scope>
		</dependency>

		<!-- 单元测试4 -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>

		<!-- TestNG -->
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>6.14.3</version>
            <scope>test</scope>
        </dependency>
		<dependency>
			<groupId>com.youlu.pdm</groupId>
			<artifactId>pdm-generator</artifactId>
			<version>1.0.0-SNAPSHOT</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>io.undertow</groupId>
			<artifactId>undertow-core</artifactId>
			<version>2.0.19.Final</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>io.undertow</groupId>
			<artifactId>undertow-servlet</artifactId>
			<version>2.0.19.Final</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>3.9</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>3.9</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-scratchpad</artifactId>
			<version>3.9</version>
			<scope>test</scope>
		</dependency>
	</dependencies>


	<build>
		<plugins>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.0</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>3.2.2</version>
				<configuration>
					<outputDirectory>target/${project.version}</outputDirectory>
					<warName>usercenter</warName>
				</configuration>
			</plugin>

			<!-- https://mvnrepository.com/artifact/org.eclipse.jetty/jetty-maven-plugin -->
			<!-- <plugin> <groupId>org.eclipse.jetty</groupId> <artifactId>jetty-maven-plugin</artifactId> 
				<version>9.4.14.v20181114</version> </plugin> -->

			<!-- tomcat8 运行插件 -->
			<plugin>
				<groupId>org.apache.tomcat.maven</groupId>
				<artifactId>tomcat8-maven-plugin</artifactId>
				<version>3.0-r1756463</version>
			</plugin>
		</plugins>
	</build>
</project>