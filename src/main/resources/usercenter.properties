#JDBC
jdbc.uc_read.driverClassName=org.gjt.mm.mysql.Driver
jdbc.uc_read.url=*******************************************************************************************************************
jdbc.uc_read.username=root
jdbc.uc_read.password=123456
jdbc.uc_read.initSize=1
jdbc.uc_read.maxSize=50
jdbc.uc_read.maxTimeout=10000
jdbc.uc_read.maxIdleTime=60000
jdbc.uc_read.showsql=true

#JDBC
jdbc.uc_write.driverClassName=org.gjt.mm.mysql.Driver
jdbc.uc_write.url=*******************************************************************************************************************&rewriteBatchedStatements=true
jdbc.uc_write.username=root
jdbc.uc_write.password=123456
jdbc.uc_write.initSize=1
jdbc.uc_write.maxSize=50
jdbc.uc_write.maxTimeout=10000
jdbc.uc_write.maxIdleTime=60000
jdbc.uc_write.showsql=true


#JDBC
jdbc.bd_write.driverClassName=org.gjt.mm.mysql.Driver
jdbc.bd_write.url=*****************************************************************************************************************
jdbc.bd_write.username=root
jdbc.bd_write.password=123456
jdbc.bd_write.initSize=1
jdbc.bd_write.maxSize=4
jdbc.bd_write.maxTimeout=10000
jdbc.bd_write.testInterval=5000
jdbc.bd_write.testQuery=SELECT 1
jdbc.bd_write.showsql=true
#JDBC
jdbc.fd_read.driverClassName=org.gjt.mm.mysql.Driver
jdbc.fd_read.url=*******************************************************************************************************************
jdbc.fd_read.username=root
jdbc.fd_read.password=123456
jdbc.fd_read.initSize=1
jdbc.fd_read.maxSize=50
jdbc.fd_read.maxTimeout=10000
jdbc.fd_read.maxIdleTime=60000
jdbc.fd_read.showsql=true


#JDBC-CRM
jdbc.crm_read.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
jdbc.crm_read.url=********************************************************
jdbc.crm_read.username=sa
jdbc.crm_read.password=sa123456
jdbc.crm_read.initSize=1
jdbc.crm_read.maxSize=10
jdbc.crm_read.maxTimeout=10000
jdbc.crm_read.maxIdleTime=60000
jdbc.crm_read.showsql=true

#JDBC-CRM
jdbc.crm_write.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
jdbc.crm_write.url=********************************************************
jdbc.crm_write.username=sa
jdbc.crm_write.password=sa123456
jdbc.crm_write.initSize=1
jdbc.crm_write.maxSize=10
jdbc.crm_write.maxTimeout=10000
jdbc.crm_write.maxIdleTime=60000
jdbc.crm_write.showsql=true

#JDBC-IPG
jdbc.ipg_read.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
jdbc.ipg_read.url=*******************************************************
jdbc.ipg_read.username=bigdataReader
jdbc.ipg_read.password=oOVMu34K9T#A0Li
jdbc.ipg_read.initSize=1
jdbc.ipg_read.maxSize=10
jdbc.ipg_read.maxTimeout=10000
jdbc.ipg_read.maxIdleTime=60000
jdbc.ipg_read.showsql=true

#临时目录
path.tmp=/root/upload_tmp/usercenter

#本地文件服务器存储地址
file.path_local=/root/upload/usercenter

#网站联盟地址
sl.url=http://**************:8091/siteleague
es.url=http://**************:8071/extensionsystem

#API.会话.客户端类型_检测
#api.Context.ctype_require=true

mq.type=rabbitmq
mq.host=**************
mq.username=admin
mq.password=123456
mq.port=5672
mq.connectionTimeout=10000
mq.channel.basicQos=10

#刷题宝注册用户aes加密密码
aes.key=sdnjfhzo423KKdup
aes.vi=hY3rLpQboK8B2v2I
#刷题宝注册用户路径
#stb.register.url=http://www.niceloo.com/GuestFeedBack/FeedBackSubmit_stb.ashx
stb.register.url=http://wwwdev.niceloo.com/UserNormalAjax.ashx?Action=region
#是否开启刷题宝用户注册
stb.register.enable=true
#项目id
stb.project.id=5a68167c-afdd-4de8-9fe9-9827f20ba89e
#CRM数据字典
stb.source.id=356
#行为动作,0注册,1登录,2其他
stb.behavior.action=0
#是否加密,1不加密,2加密
stb.encrypt=1
talker.server.enable=true
talker.server.port=8031

#解密uk
uk.key=sdnjfhzo423KKdupkL921GcmKweSjvlf3532
uk.salt=hY3rLpQboK8B2v2I
uk.iteration=20
uk.rediskey=Niceloo_UCenter
#uk.sync.user.address=http://**************:8055/api/User/GetUserInfo
#uk.sync.user.address=http://**************:8056/api/User/GetUserInfo

#网站联盟redis
redis.uk.host=**************
redis.uk.port=6380
redis.uk.password=redis2017
redis.uk.maxTotal=10

uk.android_key=mKweSjdu3Kp678KLsdlfoPKiumjvghz98512
uk.android_salt=h1234v8rbILoBYpQ
uk.android_iteration=10

uk.ios_key=Kdu3KL921GcmKweSjvlfpksdnjfhzo423532
uk.ios_salt=hQboK8B2vY3rLp2I
uk.ios_iteration=10

#新老后台单点交互
cas.enc.key=PUSvITe89DzGHtuU9J0NsAgRvjoki3cl
cas.enc.salt=ES9nNJZ9kUxzCcG7
cas.enc.iteration=20
#过期时间
cas.expire.time=86400
cas.login.name=caskeyframe
#全局事务
server.http.port=8080
tx.enable=true
#直接使用注册中心，不再配置地址
tx.manager.server=http://**************:9810/servicemanager/
tx.commit_timeout=30000
#基础数据
bd.url=http://**************:8081/basedata
#资金系统
fd.url=http://**************:8066/fundsystem
#获取UK地址
#net.uk.address=http://**************:8055/api/users/ukey
#注册中心
#regist.center.url=**************:9811
#regist.server.name=usercenter
#regist.server.ip=**************
#regist.server.carenames=all
#ED配置
ed.address=http://**************:8100/externaldata
ed.identify=api/ex/idcard/identify
ed.mobile=api/ex/mobile/attribute
#CRM配置
crm.address=http://*************:8000
crm.api.modify_Information=api/customers/Microservice/ModifyInformation
crm.rsa.public_key=<RSAKeyValue><Modulus>s0FcazUQRuVzaCoqIvMnqJtBrdIIS3YyjAiyEamoQQuIKvfzo0x4RaT1f8zeaoqFL0hBbT50X5005zFmgt8VEqtrBhmQ46Fbi7ihOopHPHmmYoUOK+3i7qMf3IxCwSXWV1EcYCZdbCXHbvt6QrU7sbmpoqmCwrYIvax8Pz8cuQc=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>
crm.rsa.private_key=<RSAKeyValue><Modulus>s0FcazUQRuVzaCoqIvMnqJtBrdIIS3YyjAiyEamoQQuIKvfzo0x4RaT1f8zeaoqFL0hBbT50X5005zFmgt8VEqtrBhmQ46Fbi7ihOopHPHmmYoUOK+3i7qMf3IxCwSXWV1EcYCZdbCXHbvt6QrU7sbmpoqmCwrYIvax8Pz8cuQc=</Modulus><Exponent>AQAB</Exponent><P>8tLX0I4quQ4Lf0G2IURPSEe+REhbzXzl7oqXHBkx7OueGZppczV3a7qj0G4rZDWtRm1UzYdLQCCmmSg1qt6kwQ==</P><Q>vPt3WQXbuoiNmsc11ZZ+0oPxK2UAK6sdzRMasv7+LkfKUFoaM/J/ay/8DcT8LD9xi6AnsgIZZDvuGgUG8wFnxw==</Q><DP>s7L9K8ZKL7EJKSAEHRJkaeVOFVJHPgcUUjgWJCExJVgpJGF1cGE9A2iYGoOtcPeYR+pZO9DIC6keIXH0ZYNnAQ==</DP><DQ>Tw0JcW2n6jEwuqvV8xBO4JRcj4BiP+IGR0BUjMdpX8ab9FycVVp8vkOGNeROb9viDTvjHu0N3gXLpYqwy3mtBQ==</DQ><InverseQ>TbK4r5DXt2UwtH1Ov+eAk6LkCwE0MOFOIyF6q0kwayd+tLmNwYR9FzRJMaLj2nQjbxYN2xrjNRvj4W08AA/ApA==</InverseQ><D>YYaGCa3tFSVxDJMkKa+xPAJ8UV2cNbIw/g8dEo8Dg6AKjZYpcAcCyKFfEBEeUUjb/UWKky0Pk5WsWrx90WYdyAec08ruAEgOhxHSO63pqILi6dwPuBSmPXeHUklpB1CKJQCOp2BubUxixixfq1zuzK5GPl1H1u42hHRfkEa5BAE=</D></RSAKeyValue>

crm.old_identity=false

sys.clusterSeq=34
fs.url=http://**************:8093/fileservice

lock.enable=true
lock.key.prefix=_userlock_


cache.type=redis
cache.redis.host=**************
cache.redis.port=6379
cache.redis.password=123456


api.doFilter=true

uk.client_key=sdnjfhzo423KKdupkL921GcmKweSjvlf3532
uk.client_salt=hY3rLpQboK8B2v2I
uk.client_iteration=20
#
net.address=http://**************:8056
net.user.info=api/User/GetUserInfo
net.user.ukey=api/users/ukey
net.user.regist=api/users/register
net.user.pwd_edit=api/users/password/edit

log.write.enable=true
esearch.url=http://**************:9200
esearch.username=elastic
esearch.password=123456

uk.fire_key=hrh5g72m

tc.url=
#企业端地址
cr.url=http://**************:8430/corpservice
#添加企业用户
cr.corpuser_add=api/cs/corpuser/add
#修改企业用户
cr.corpuser_edit=api/cs/corpuser/edit


mq.crm.type=rabbitmq
mq.crm.host=**************
mq.crm.username=admin
mq.crm.password=123456
mq.crm.port=5672
mq.crm.connectionTimeout=10000
mq.crm.exchange.name=marketing
mq.crm.channel.basicQos=10
