lognc.rootLogger=DEBUG,stdout,file,errorFile

lognc.appender.stdout.target=System.out
lognc.appender.stdout.pattern=[%d{yyyy-MM-dd HH:mm:ss SSS}][%p] %m%n

lognc.appender.file.file=../logs/usercenter.log
lognc.appender.file.hisFile=../logs/usercenter.log
lognc.appender.file.hisFileSuffix='.'yyyyMMdd'.log'
lognc.appender.file.pattern=[%d{yyyy-MM-dd HH:mm:ss SSS}][%p] %m%n

lognc.appender.errorFile.file=../logs/usercenter1.log
lognc.appender.errorFile.hisFile=../logs/usercenter1.log
lognc.appender.errorFile.hisFileSuffix='.'yyyyMMdd'.log'
lognc.appender.errorFile.pattern=[%d{yyyy-MM-dd HH:mm:ss SSS}][%p] %m%n
lognc.appender.errorFile.levels=ERROR


lognc.logger.org.nobject=DEBUG
lognc.logger.com.niceloo.core.mongo=TRACE