package com.niceloo.uc.service;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.model.BdDpt;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.nobject.common.lang.StringUtils.isEmpty;

/**
 * 部门 服务
 * <p>
 * maning 20201205
 */
@Service
public class UcDptService extends CoreBaseService {

    @SuppressWarnings("unused")
    @Autowired
    private CommonDao commonDao_bd_write;

    /**
     * 根据 levelcode 获取当前部门的组织架构名称，全部门名
     */
    @SuppressWarnings("unchecked")
    public String getFullDptnameByLevelcode(String dptLevelcode) throws Exception {
        List<String> levelcodes = new ArrayList<>();
        if (dptLevelcode.length() > 10) {
            // 拆分 dptLevelcode
            parseLevelcode(dptLevelcode, levelcodes);
        } else {
            levelcodes.add(dptLevelcode);
        }

        StringBuilder sql = new StringBuilder("SELECT dptId, dptName FROM BdDpt WHERE ");
        sql.append("dptLevelcode ='").append(levelcodes.get(0)).append("' ");
        if (levelcodes.size() > 1) {
            for (String le : levelcodes) {
                sql.append("OR dptLevelcode ='").append(le).append("' ");
            }
        }
        sql.append("ORDER BY dptLevelcode");
        List<Map> data = commonDao_bd_write.queryMaps(sql.toString(), null, null);

        StringBuilder name = new StringBuilder();
        for (Map map : data) {
            name.append(map.get("dptName").toString()).append("/");
        }
        return name.substring(0, name.length() - 1);
    }

    /**
     * 查询大区
     */
    public String findAreaByDptId(String dptId) throws Exception {
        String pdptId = "";
        BdDpt dpt = this.findById(dptId);
        if (dpt == null) {
            return pdptId;
        }

        String LevelCode = dpt.getDptLevelcode();
        while (!isEmpty(LevelCode) && LevelCode.length() > UcConst.LEVELCODE_LENGTH) {
            String pLevelCode = LevelCode.substring(0, LevelCode.length() - UcConst.LEVELCODE_LENGTH);
            BdDpt pdpt = this.findByLevelcode(pLevelCode);
            if (pdpt != null && "A".equals(pdpt.getDptType())) {
                pdptId = pdpt.getDptId();
                break;
            }
            LevelCode = pLevelCode;
        }
        return pdptId;
    }

    /**
     * 查询部门
     *
     * @param id 部门标识
     */
    public BdDpt findById(String id) throws Exception {
        return commonDao_bd_write.queryObject("SELECT * FROM BdDpt WHERE dptId=? ", new Object[]{id}, BdDpt.class);
    }

    /**
     * 按照层级编码查找部门
     */
    @SuppressWarnings("WeakerAccess")
    public BdDpt findByLevelcode(String superLever) throws Exception {
        return commonDao_bd_write.queryObject("SELECT * FROM BdDpt WHERE dptLevelcode=? ", new Object[]{superLever}, BdDpt.class);
    }


    /**
     * 拆分 dptLevelcode
     */
    private void parseLevelcode(String dptLevelcode, List<String> levelcodes) {
        levelcodes.add(dptLevelcode);

        String sonLevelvode = dptLevelcode.substring(0, dptLevelcode.length() - 10);
        levelcodes.add(sonLevelvode);
        if (sonLevelvode.length() > 10) {
            parseLevelcode(sonLevelvode, levelcodes);
        }
    }


}