package com.niceloo.uc.service;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.uc.model.UcUserbrand;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.db.SQLUtils;
import org.nobject.common.db.Transactional;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.SetUtils;

import java.util.*;


/**
 * 用户品牌关系-业务类
 *
 * <AUTHOR>
 * @Date 2019-12-04 16:27:05
 */
@Service
public class UcUserbrandService extends CoreBaseService {

	@Autowired
	private CommonDao commonDao;

	/**
	 * 根据用户获取他的品牌
	 * @param userId
	 * @return
	 * @throws DBException
	 */
	public Set<String> queryBrandIdsByUser(String userId) throws DBException {
		String sql = "select brandId from UcUserbrand where userId = ?";
		String[] currBrands = commonDao.queryStrings(sql, new Object[]{userId});
		return SetUtils.toSet(currBrands);
	}

	/**
	 * 查询用户的品牌 ，包含名称
	 * @param userId
	 * @return
	 * @throws DBException
	 */
	public List<Map> queryBrandsByUser(String userId) throws DBException{
		String sql = "select brandId ,brandName from UcBrand " +
				"where brandId in (select brandId from UcUserbrand where userId = ?)" +
				" and brandAvlstatus = 'Y' and brandDelstatus = 'N' order by brandSeq";
		List list = commonDao.queryMaps(sql, new Object[]{userId},new HashMap<>());
		return list;
	}

	/**
	 * 获取当前用户的品牌
	 * 删除当前用户有，但是此次设置中没有的，
	 * insert
	 *
	 * @param currUserId
	 * @param userId
	 * @param brandIds
	 */
	@Transactional
	public void setUserBrand(String currUserId, String userId, List<String> brandIds) throws DBException {
		if (brandIds == null) {
			brandIds=new ArrayList<>();
		}
		String sql = "select brandId from UcUserbrand where userId = ?";
		Set<String> currUserBrands = SetUtils.toSet(commonDao.queryStrings(sql, new Object[]{currUserId}));

		Set<UcUserbrand> addList = new HashSet<>();
		for (String brandId : brandIds) {
			for (String currBrand : currUserBrands) {
				if (currBrand.equals(brandId)) {
					UcUserbrand ucUserbrand = new UcUserbrand();
					ucUserbrand.setUserbrId(commonDao.genSerial("UcUserbrand", "userbrId", "USERBR", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
					ucUserbrand.setUserId(userId);
					ucUserbrand.setBrandId(brandId);
					addList.add(ucUserbrand);
				}
			}
		}
		if(currUserBrands.size()>0){
			sql = "delete from UcUserbrand where userId = ? and brandId in (" + SQLUtils.getIn(currUserBrands) + ")";
			commonDao.execute(sql, new String[]{userId});
		}
		if(addList.size()>0){
			commonDao.save(addList);
		}

	}

	public Map<String,String> queryBranMap() throws DBException {
		String sql = "select brandId , brandName from UcBrand where  brandAvlstatus = 'Y' and brandDelstatus = 'N' order by brandSeq";
		List<Map> list = commonDao.queryMaps(sql, new Object[]{},new HashMap<>());
		Map brandMap = new LinkedHashMap();
		for (Map o : list) {
			brandMap.put(o.get("brandId"),o.get("brandName"));
		}
		return brandMap;
	}
}