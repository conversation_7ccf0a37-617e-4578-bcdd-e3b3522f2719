package com.niceloo.uc.service;

import java.util.List;
import java.util.Map;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.MapUtils;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.uc.model.UcBrand;
/**
 * UcBrandService
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class UcBrandService {

	/** commonDao_uc_read */
	@Autowired
	private CommonDao commonDao_uc_read;
	
	/** commonDao_uc_read */
	@Autowired
	private CommonDao commonDao_uc_write;
	
	/**
	 * 品牌列表
	 * @param orderVal 
	 * @param orderKey 
	 * @throws DBException 
	 * 
	 * @throws Exception
	 */
	public QueryResult queryBrandList(Integer pageIndex, Integer pageCount, Map params, String orderKey,
			String orderVal) throws DBException {

		String sql = 
			"SELECT "
				+ "brandId,"
				+ "brandName,"
				+ "brandAvlstatus,"
				+ "brandSeq,"
				+ "brandCreater,"
				+ "brandCreatedate,"
				+ "brandModifier,"
				+ "brandModifieddate,"
				+ "brandMemo"
			+ " FROM"
				+ " UcBrand"
			+ " WHERE"
				+ " brandDelstatus = 'N' ";
		
		if(!isEmpty((String)params.get("brandName"))){
			sql += " AND brandName=:brandName ";
		}
		
		if(!isEmpty((String)params.get("brandCreater"))){
			sql += " AND brandCreater=:brandCreater";
		}
		
		if(!isEmpty((String)params.get("brandCreatedate_begin"))&&!isEmpty((String)params.get("brandCreatedate_end"))){
			sql += " AND str_to_date(brandCreatedate, '%Y-%m-%d %h:%i:%s')>=str_to_date(:brandCreatedate_begin, '%Y-%m-%d %h:%i:%s') AND str_to_date(brandCreatedate, '%Y-%m-%d %h:%i:%s')<=str_to_date(:brandCreatedate_end, '%Y-%m-%d %h:%i:%s') ";
		}
		
		if(!isEmpty((String)params.get("brandModifier"))){
			sql += " AND brandModifier=:brandModifier";
		}
		
		if(!isEmpty((String)params.get("brandModifieddate_begin"))&&!isEmpty((String)params.get("brandModifieddate_end"))){
			sql += " AND str_to_date(brandCreatedate, '%Y-%m-%d %h:%i:%s')>=str_to_date(:brandModifieddate_begin, '%Y-%m-%d %h:%i:%s') AND str_to_date(brandCreatedate, '%Y-%m-%d %h:%i:%s')<=str_to_date(:brandModifieddate_end, '%Y-%m-%d %h:%i:%s') ";
		}
	
		if(!isEmpty((String)params.get("brandAvlstatus"))){
			sql += " AND brandAvlstatus=:brandAvlstatus";
		}
		
		if(!isEmpty(orderKey)){
			
			sql += " ORDER BY "+ orderKey;
			
			if(orderVal.equals("Y")){
				
				sql += "ASC";
				
			}else if(orderVal.equals("N")){
				
				sql += " DESC";
				
			}
		}
		QueryResult  qr=commonDao_uc_read.queryMapsCount(sql, pageIndex, pageCount, params,UcBrand.class);
		
		return qr;
	}

	
	
	/**
	 * 是否为空<br>
	 * 可鉴别Tab键，换行键
	 * 
	 * @param str 字符串
	 */
	public static boolean isEmpty(String str) {
		int strLen;
		if (str == null || (strLen = str.length()) == 0) return true;
		for (int i = 0; i < strLen; i++) {
			if ((Character.isWhitespace(str.charAt(i)) == false)) return false;
		}
		return true;
	}

	/**
	新增
	 * @throws Exception 
	 */

	public void add(UcBrand brand) throws Exception {
		
		brand.setBrandId(CoreUtils.genSerial(commonDao_uc_write, "UcBrand", "brandId", "BRAND", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
		brand.setBrandDelstatus("N");
		brand.setBrandCreatedate(DateUtils.getNowDString());
		commonDao_uc_write.save(brand);
	}


	/**
	根据ID查询
	 * @throws DBException 
	 */

	public UcBrand queryBrandById(String brandId) throws DBException {
		
		Map param=MapUtils.toMap(new Object[][]{
			{"brandId",brandId},
		});
		
		String sql=
			"SELECT"
				+ " *"
			+ " FROM"
				+ " UcBrand"
			+ " WHERE"
				+ " brandId=:brandId"
			+ " AND"
				+ " brandDelstatus = 'N'";
	
		return commonDao_uc_read.queryObject(sql, param, UcBrand.class);
	}


	/**
	根据ID修改
	 * @throws DBException 
	 */

	public void edit(UcBrand brand) throws DBException {

		brand.setBrandModifieddate(DateUtils.getNowDString());

		commonDao_uc_write.update(brand);

		
	}


	/**
	根据ID删除
	 * @throws DBException 
	 */
	public void del(UcBrand brand) throws DBException {

		String sql = 
			"UPDATE "
				+ "UcBrand"
			+ " SET"
				+ " brandDelstatus = 'Y'"
			+ " WHERE"
				+ " brandId=?";
		commonDao_uc_write.execute(sql,new Object[]{brand.getBrandId()});
		
	}
	
	/**
	 * 根据名称查询
	 * @param brandName
	 * @return
	 * @throws DBException
	 */
	public UcBrand queryBrandByName(String brandName) throws DBException {
		Map param=MapUtils.toMap(new Object[][]{
			{"brandName",brandName},
		});
		
		String sql=
			"SELECT"
				+ " *"
			+ " FROM"
				+ " UcBrand"
			+ " WHERE"
				+ " brandName=:brandName"
			+ " AND"
				+ " brandDelstatus = 'N'";
	
		return commonDao_uc_read.queryObject(sql, param, UcBrand.class);
	}


	/**
	 * 根据品牌名称查询品牌
	 * @param brandNames
	 * @return
	 * @throws DBException
	 */
	public List<UcBrand> findByBrandNames(String brandIds) throws DBException {
		String sql = "SELECT"
						+ " *"
						+ " FROM"
							+ " UcBrand"
						+ " WHERE"
							+ " brandId IN ("+brandIds+")"
						+ " AND"
							+ " brandDelstatus = 'N'";
		return commonDao_uc_read.queryObjects(sql, null, UcBrand.class);
	}
}
