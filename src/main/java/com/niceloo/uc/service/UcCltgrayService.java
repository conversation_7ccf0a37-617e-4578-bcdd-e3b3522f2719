package com.niceloo.uc.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.db.SQLUtils;
import org.nobject.common.db.member.*;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.DoResult;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.uc.model.UcCltgray;


/**
 * 客户端灰度-业务类
 * <AUTHOR>
 * @Date 2019-09-25
 */
@Service
public class UcCltgrayService extends CoreBaseService {

	@Autowired
	private CommonDao commonDao;

	/**
	 * 客户端灰度-添加
	 * @param ucCltgray 客户端灰度模型
	 * @return
	 * @throws DBException
	 */
	public DoResult add(UcCltgray ucCltgray) throws Exception {
		ucCltgray.setCltgrayId(commonDao.genSerial("UcCltgray", "cltgrayId", "CLTGRAY", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
		return commonDao.save(ucCltgray);
	}

	/**
	 * 客户端灰度-删除
	 * @param cltgrayIds 客户端灰度模型
	 * @return
	 * @throws DBException
	 */
	public void delete(String cltgrayIds) throws DBException {
		String[] split = cltgrayIds.split(",");
		String in = SQLUtils.getIn(split);
		commonDao.execute("DELETE FROM UcCltgray WHERE cltgrayId IN (" + in + ")", new Object[]{});
	}

	/**
	 * 客户端灰度-修改
	 * @param ucCltgray 客户端灰度模型
	 * @throws DBException
	 */
	public void edit(UcCltgray ucCltgray) throws DBException {
		commonDao.update(ucCltgray);
	}

	/**
	 * 客户端灰度-详情
	 * @param cltgrayId 主键
	 * @return
	 * @throws DBException
	 */
	public UcCltgray findById(String cltgrayId) throws DBException {
		Map param = MapUtils.toMap(new Object[][] { { "cltgrayId", cltgrayId }, });
		String sql = "SELECT * FROM UcCltgray WHERE cltgrayId=:cltgrayId";
		return commonDao.queryObject(sql, param, UcCltgray.class);
	}

	/**
	 * 分页数据
	 * @param mWhere where条件
	 * @param mGroup 组合条件
	 * @param mOrder 排序
	 * @param pageIndex 分页起始
	 * @param pageSize 分页数量
	 * @throws Exception
	 */
	public QueryResult findMapsCount(Map mWhere, Map mGroup, Map mOrder, int pageIndex, int pageSize)
			throws Exception {

		SqlWhere sqlWhere = getSqlWhere(sqlWE_findMapsCount, mWhere);
		SqlOrder sqlOrder = getSqlOrder(sqlOE_findMapsCount, mOrder);
		SqlGroup sqlGroup = null;
		String sql = " FROM UcCltgray  ucCltgray WHERE 1=1 ";
		return queryMapsCountBySqlWhere(commonDao,sql, sqlSelect_findMapsCount,sqlWhere,sqlGroup,sqlOrder,pageIndex,pageSize,new Class[]{UcCltgray.class});	};

	/**
	 * 分页数据
	 * @param mWhere where条件
	 * @param mGroup group
	 * @param mOrder 排序
	 * @param pageIndex 分页起始
	 * @param pageCount 分页数量
	 * @return
	 * @throws Exception
	 */
	public QueryResult queryMapsCount(Map mWhere, Map mGroup, Map mOrder, int pageIndex, int pageCount)
			throws Exception {

		SqlWhere sqlWhere = getSqlWhere(sqlWE_findMapsCount, mWhere);
		SqlOrder sqlOrder = getSqlOrder(sqlOE_findMapsCount, mOrder);
		SqlGroup sqlGroup = null;
		String sql = "";
		QueryResult obj = queryMapsCountBySqlWhere(commonDao, " FROM UcCltgray ucCltgray WHERE 1=1 " + sql, sqlSelect_findMapsCount,
			sqlWhere, sqlGroup, sqlOrder, pageIndex, pageCount, new Class[] { UcCltgray.class });
		return obj;
	}

	/**
	 * 分页条件
	 */
	private final SqlWE[] sqlWE_findMapsCount = new SqlWE[] { 
		new SqlWE("cltgrayId", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("clttypeId", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltgrayType", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltgrayObject", SqlWE.Compare.like, SqlWE.Type.STR),
		new SqlWE("cltgrayObjectname", SqlWE.Compare.like, SqlWE.Type.STR),
		new SqlWE("cltgrayCreatedate", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltgrayCreater", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltgrayCreatername", SqlWE.Compare.equal, SqlWE.Type.STR),
	};

	/**
	 * 分页排序
	 */
	private final SqlOE[] sqlOE_findMapsCount = new SqlOE[] { 
		new SqlOE("cltgrayId", SqlOE.Option.both),
		new SqlOE("clttypeId", SqlOE.Option.both),
		new SqlOE("cltgrayType", SqlOE.Option.both),
		new SqlOE("cltgrayObject", SqlOE.Option.both),
		new SqlOE("cltgrayObjectname", SqlOE.Option.both),
		new SqlOE("cltgrayCreatedate", SqlOE.Option.both),
		new SqlOE("cltgrayCreater", SqlOE.Option.both),
		new SqlOE("cltgrayCreatername", SqlOE.Option.both),
	};

	/**
	 * 查询结果
	 */
	private final SqlSelect sqlSelect_findMapsCount=new SqlSelect(new String[]{ 
		"cltgrayId",
		"clttypeId",
		"cltgrayType",
		"cltgrayObject",
		"cltgrayObjectname",
		"cltgrayCreatedate",
		"cltgrayCreater",
		"cltgrayCreatername",
	});


	/**
	 * 根据某一列查询
	 * @param colsMap
	 * @return
	 * @throws DBException
	 */
	public UcCltgray findByCols(Map<String,Object> colsMap) throws DBException {
		StringBuffer sql = new StringBuffer("SELECT * FROM UcCltgray WHERE 1=1");
		Map<String,Object> params = new HashMap<String, Object>();
		for(String col : colsMap.keySet()) {
			//判断是否有值
			Object value = colsMap.get(col);
			if(value == null) {
				continue;
			}
			if(StringUtils.isEmpty(value.toString())) {
				continue;
			}
			params.put(col, value);
			sql.append(" and ").append(col).append("=");
			//验证value的类型
			if(value instanceof java.lang.String) {
				sql.append("'").append(value.toString()).append("'");
			}else {
				sql.append(value);
			}
		}
		return commonDao.queryObject(sql.toString(), params, UcCltgray.class);
	}

	/**
	 * 获取已经存在的灰度升级对象
	 * @param ids
	 * @param clttypeId
	 * @param cltgrayType
	 * @return
	 * @throws DBException
	 */
	public String[] findByCltgrayObjectsAndclttypeId(String[] ids, String clttypeId, String cltgrayType) throws DBException {
		String in = SQLUtils.getIn(ids);
		String sql = "SELECT cltgrayObject FROM UcCltgray WHERE "
						+ "cltgrayObject IN ("+in+") "
						+ "AND clttypeId = ? "
						+ "AND cltgrayType = ?";
		return commonDao.queryStrings(sql, new Object[] {clttypeId,cltgrayType});
	}

	/**
	 * 校验是否是灰度升级对象
	 * @param clttypeId
	 * @param userId
	 * @param terminalId
	 * @throws DBException 
	 */
	public boolean checkGray(String clttypeId, String userId, String terminalId) throws DBException {
		String sql = "SELECT cltgrayId FROM UcCltgray WHERE "
						+ "(cltgrayObject = ? OR cltgrayObject = ?)"
						+ "AND clttypeId = ? ";
		String queryString = commonDao.queryString(sql, new Object[] {userId,terminalId,clttypeId});
		if(StringUtils.isEmpty(queryString)) {
			return false;
		}else {
			return true;
		}
	}
}