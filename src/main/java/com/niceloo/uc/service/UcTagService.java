package com.niceloo.uc.service;

import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.mq.MQService;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.model.UcUser;
import com.niceloo.uc.model.UcUserTag;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.ObjectUtils;

import static org.nobject.common.lang.StringUtils.isEmpty;

@Service
@SuppressWarnings("unused")
public class UcTagService {
    @Autowired
    private CommonDao commonDao_uc_write;
    @Autowired
    private MQService mqService;


    public UcUserTag modifyTag(String userId, String operatorId, String tagType, String tagAvlstatus, String tagDelstatus) throws Exception {
        if ((!isEmpty(tagAvlstatus) && isEmpty(tagType)) || (isEmpty(tagAvlstatus) && !isEmpty(tagType))) {
            throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "添加标签时,标签类型与状态必填", null, null);
        }
        StringBuilder sql = new StringBuilder("select * from UcUser where userId = '" + userId + "'");
        UcUser ucUser = commonDao_uc_write.queryObject(sql.toString(), null, UcUser.class);
        if (ObjectUtils.isEmpty(ucUser) || UcConst.Delstatus.YES.equals(ucUser.getUserDelstatus()) || UcConst.Avlstatus.NO.equals(ucUser.getUserAvlstatus())) {
            throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT, "用户不存在", null);
        }

        sql = new StringBuilder("select * from UcUserTag where userId = '" + userId + "'");
        UcUserTag ucUserTag = commonDao_uc_write.queryObject(sql.toString(), null, UcUserTag.class);
        if (ObjectUtils.isEmpty(tagDelstatus)) {
            if (UcConst.Avlstatus.YES.equals(tagAvlstatus)) {
                tagDelstatus = UcConst.Delstatus.NO;
            } else {
                tagDelstatus = UcConst.Delstatus.YES;
            }
        }
        //1.修改标签
        if (!ObjectUtils.isEmpty(ucUserTag)) {
            if (ucUserTag.getTagType() != null && ucUserTag.getTagType().equals(tagType)) {
                ucUserTag.setTagAvlstatus(tagAvlstatus);
                ucUserTag.setTagDelstatus(tagDelstatus);
                ucUserTag.setTagModifier(operatorId);
                ucUserTag.setTagModifieddate(DateUtils.getNowDString());
                commonDao_uc_write.update(ucUserTag);
            }
        } else {
            //2.新增标签
            ucUserTag = new UcUserTag();
            ucUserTag.setTagId(commonDao_uc_write.genSerial("UcUserTag", "tagId", "TAG", 10));
            ucUserTag.setUserId(userId);
            ucUserTag.setTagCreator(operatorId);
            ucUserTag.setTagCreatedate(DateUtils.getNowDString());
            ucUserTag.setTagType(tagType);
            ucUserTag.setTagAvlstatus(tagAvlstatus);
            ucUserTag.setTagDelstatus(tagDelstatus);
            commonDao_uc_write.save(ucUserTag);

        }
        return ucUserTag;
    }

    /**
     * 根据用户ID获取标签信息
     */
    public UcUserTag findTagById(String userId, String tagType) throws Exception {
        return commonDao_uc_write.queryObject("select * from UcUserTag where userId = '" + userId + "' and tagType = '" + tagType + "'", null, UcUserTag.class);
    }

}
