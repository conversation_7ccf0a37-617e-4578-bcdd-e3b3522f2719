package com.niceloo.uc.service;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.DoResult;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.job.core.Scheduled;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.model.UcLoginip;
import com.niceloo.uc.model.UcUser;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.db.member.*;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.NumberUtils;
import org.nobject.common.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static com.niceloo.uc.service.UcBrandService.isEmpty;


/**
 * ip名单-业务类
 *
 * <AUTHOR>
 * @Date 2020-03-14 14:03:54
 */
@Service
public class UcLoginipService extends CoreBaseService {

	@Autowired
	private CommonDao commonDao;

	/**
	 * ip名单-添加
	 *
	 * @param ucLoginip ip名单模型
	 * @return
	 * @throws DBException
	 */
	public DoResult save(UcLoginip ucLoginip) throws Exception {
		ucLoginip.setLoginipCreateddate(DateUtils.getNowDString());
		anylizeIp(ucLoginip);
		ucLoginip.setLoginipId(CoreUtils.genSerial(commonDao, "UcLoginip", "loginipId", "LOGINIP", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
		return commonDao.save(ucLoginip);
	}


	private void anylizeIp(UcLoginip ucLoginip) throws ApplicationException {
		String loginip = ucLoginip.getLoginip();
		if (loginip.indexOf("-") == -1) {

			if (!pattern.matcher(loginip).matches()) {
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "IP格式不正确", null);
			}

			ucLoginip.setLoginipAccuracy("A");
			ucLoginip.setLoginipStart(getIp2long(loginip).toString());
			ucLoginip.setLoginipEnd(getIp2long(loginip).toString());
		} else {
			String[] split1 = loginip.split("-");
			String s = split1[0];
			if (!pattern.matcher(s).matches() || !NumberUtils.isNumberString(split1[1])) {
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "IP格式不正确", null);
			}
			String substring = split1[0].substring(split1[0].lastIndexOf(".") + 1);
			Integer integer = NumberUtils.toInt(substring);
			Integer integer1 = NumberUtils.toInt(split1[1]);
			if (integer > integer1 || integer1 > 255) {
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "IP格式不正确", null);
			}
			ucLoginip.setLoginipAccuracy("R");
			String[] split = loginip.split("-");
			String end = getIp2long(split[0].substring(0, split[0].lastIndexOf(".") + 1) + split[1]).toString();
			ucLoginip.setLoginipStart(getIp2long(split[0]).toString());
			ucLoginip.setLoginipEnd(end);
		}
	}

	/**
	 * ip名单-删除
	 *
	 * @param ucLoginip ip名单模型
	 * @return
	 * @throws DBException
	 */
	public void delete(UcLoginip ucLoginip) throws DBException {
		commonDao.execute("DELETE FROM UcLoginip WHERE loginipId=?", new Object[]{ucLoginip.getLoginipId()});
	}

	/**
	 * ip名单-修改
	 *
	 * @param ucLoginip ip名单模型
	 * @throws DBException
	 */
	public void update(UcLoginip ucLoginip) throws Exception {
//        if(!ucLoginip.getLoginip().equals(loginip)) {
//            if (isExist(loginip)) {
//                throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "ip/ip范围已存在", null, null);
//            }
//        }
//           ucLoginip.setLoginip(loginip);
		anylizeIp(ucLoginip);
		commonDao.update(ucLoginip);
	}

	/**
	 * ip名单-详情
	 *
	 * @param loginipId 主键
	 * @return
	 * @throws DBException
	 */
	public UcLoginip findById(String loginipId) throws DBException {
		Map param = MapUtils.toMap(new Object[][]{{"loginipId", loginipId},});
		String sql = "SELECT * FROM UcLoginip WHERE loginipId=:loginipId";
		return commonDao.queryObject(sql, param, UcLoginip.class);
	}

	/**
	 * 判断ip是否存在
	 * 如果此时想要新增一个永久性ip，那么校验只在永久ip范围内校验
	 *
	 * @param ip
	 * @return
	 * @throws DBException
	 */
	public Boolean isExist(String ip, boolean containsTemp, String excludeId) throws DBException {
		String start;
		String end;
		if (ip.indexOf("-") == -1) {
			start = getIp2long(ip).toString();
			end = getIp2long(ip).toString();
		} else {
			String[] split = ip.split("-");
			start = getIp2long(split[0]).toString();
			end = getIp2long(split[0].substring(0, split[0].lastIndexOf(".") + 1) + split[1]).toString();
		}
		String sql = "select count(*) count from UcLoginip where loginipType='W' " +
				(containsTemp ? "" : " AND loginipChecktype = 'P' ") +
				(StringUtils.isEmpty(excludeId) ? "" : " AND loginipId <> :ID") +
				" AND (loginipStart <= " + start + " and loginipEnd >= " + end + ") ";
		Map map = new HashMap();
		if (!StringUtils.isEmpty(excludeId)) {
			map.put("ID", excludeId);
		}
		int count = commonDao.queryInt(sql, map);
		return count > 0;
	}


	/**
	 * 根据ip类型查询 黑/白 名单  不传查全部
	 *
	 * @return
	 * @throws DBException
	 */
	public List<UcLoginip> findByType(String type) throws DBException {
		Object[] objects = new Object[]{};
		String sql = "SELECT * FROM UcLoginip";
		if (!isEmpty(type)) {
			sql += " where loginipType = ?";
			objects = new Object[]{type};
		}
		return commonDao.queryObjects(sql, objects, UcLoginip.class);
	}

	/**
	 * ip名单--删除(清理临时ip)
	 *
	 * @return
	 * @throws DBException
	 */
	@Scheduled(cron = "0 0 0 * * ?")
	public void clearTemp() throws DBException {
		String sql = "DELETE FROM UcLoginip WHERE loginipChecktype='T'";
		commonDao.execute(sql, new Object[]{});
	}


	/**
	 * ip校验
	 *
	 * @return
	 * @throws Exception
	 */
	public Boolean checkIp(String ip) throws Exception {
		if(!pattern.matcher(ip).matches()){
			throw new ApplicationException(UcConst.Errorcode.IP_ERROR, "ip不正确", null);
		}
		String ipLong = getIp2long(ip).toString();
		String sql = "select count(*) count from UcLoginip where loginipType='W' " +
				"AND (loginipStart <= " + ipLong + " and loginipEnd >= " + ipLong + ") ";
		int count = commonDao.queryInt(sql, new Object[0]);
		return count > 0;
	}

	private Long getIp2long(String ip) {
		String[] ips = ip.split("\\.");
		Long ipLong = 0L;
		for (String ipNumber : ips) {
			ipLong = ipLong << 8 | Integer.parseInt(ipNumber);
		}
		return ipLong;
	}

	/**
	 * 添加临时ip
	 *
	 * @param ip
	 * @param user
	 * @throws Exception
	 */
	public void addTempIp(String ip, UcUser user) throws Exception {
		if (!checkIp(ip)) {
			UcLoginip ucLoginip = new UcLoginip();
			ucLoginip.setLoginipAccuracy("A");
			ucLoginip.setLoginipCreateddate(DateUtils.getNowDString());
			ucLoginip.setLoginipChecktype("T");
			ucLoginip.setLoginipCreator(user.getUserId());
			ucLoginip.setLoginipCreatorname(user.getUserName());
			ucLoginip.setLoginip(ip);
			ucLoginip.setLoginipType("W");
			ucLoginip.setLoginipStart(getIp2long(ip).toString());
			ucLoginip.setLoginipEnd(getIp2long(ip).toString());
			ucLoginip.setLoginipId(CoreUtils.genSerial(commonDao, "UcLoginip", "loginipId", "LOGINIP", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
			commonDao.save(ucLoginip);
		}
	}

	//private Pattern pattern = Pattern.compile("((25[0-5]|2[0-4]\\d|1?\\d?\\d)\\.){3}(25[0-5]|2[0-4]\\d|1?\\d?\\d) ");
	private static Pattern pattern = Pattern.compile("^((25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d)))\\.){3}(25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d)))$");




	/**
	 * 分页数据
	 *
	 * @param mWhere    where条件
	 * @param mGroup    group
	 * @param mOrder    排序
	 * @param pageIndex 分页起始
	 * @param pageCount 分页数量
	 * @return
	 * @throws Exception
	 */
	public QueryResult queryMapsCount(Map mWhere, Map mGroup, Map mOrder, int pageIndex, int pageCount)
			throws Exception {
		SqlWhere sqlWhere = getSqlWhere(sqlWE_query, mWhere);
		SqlOrder sqlOrder = getSqlOrder(sqlOE_query, mOrder);
		SqlGroup sqlGroup = null;
		StringBuilder builder = new StringBuilder();
		builder.append("select * from UcLoginip where 1=1 ");
		String loginip = (String) mWhere.get("loginip");
		if (!isEmpty(loginip)) {
			boolean matches = pattern.matcher(loginip).matches();
			// 精确ip
			if (matches) {
				Long ip2long = getIp2long(loginip);
				builder.append(" and loginipStart <= " + ip2long + " and loginipEnd >= " + ip2long);
			} else {
				// 其他，比如输入192.168.15  查询所有15段ip
				builder.append(" and loginip like '%" + loginip + "%'");
			}
		}
		if (!isEmpty((String) mWhere.get("startDate"))) {
			builder.append(" AND loginipCreateddate >='" + mWhere.get("startDate") + "'");
		}
		String endDate = (String) mWhere.get("endDate");
		if (!isEmpty((endDate))) {
			builder.append(" AND loginipCreateddate <='" + endDate + "'");
		}

		return queryMapsCountBySqlWhere(commonDao, builder.toString(), null, sqlWhere, sqlGroup, sqlOrder, pageIndex, pageCount, new Class[]{UcLoginip.class});
	}

	/**
	 * 分页条件
	 */
	private final SqlWE[] sqlWE_query = new SqlWE[]{
			new SqlWE("loginipType", SqlWE.Compare.equal, SqlWE.Type.STR),
			new SqlWE("loginipCreator", SqlWE.Compare.equal, SqlWE.Type.STR),
			new SqlWE("loginipChecktype", SqlWE.Compare.equal, SqlWE.Type.STR),
			new SqlWE("loginipMemo", SqlWE.Compare.equal, SqlWE.Type.STR),
			new SqlWE("loginipAccuracy", SqlWE.Compare.equal, SqlWE.Type.STR),
			new SqlWE("loginipCreatorname", SqlWE.Compare.like, SqlWE.Type.STR),

	};

	/**
	 * 分页排序
	 */
	private final SqlOE[] sqlOE_query = new SqlOE[]{
			new SqlOE("loginipId", SqlOE.Option.both),
			new SqlOE("loginip", SqlOE.Option.both),
			new SqlOE("loginipCreateddate", SqlOE.Option.both),
	};

	/**
	 * 查询结果
	 */
	private final SqlSelect sqlSelect_query = new SqlSelect(new String[]{
			"loginipId",
			"loginip",
			"loginipType",
			"loginipCreator",
			"loginipCreateddate",
			"loginipChecktype",
			"loginipMemo",
			"loginipAccuracy",
			"loginipCreatorname",
	});

}