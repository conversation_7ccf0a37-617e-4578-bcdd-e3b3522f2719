package com.niceloo.uc.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.db.SQLUtils;
import org.nobject.common.db.member.*;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.DoResult;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.uc.common.UcConst.Avlstatus;
import com.niceloo.uc.common.UcConst.Delstatus;
import com.niceloo.uc.model.UcClttype;


/**
 * 客户端类型-业务类
 * <AUTHOR>
 * @Date 2019-09-25
 */
@Service
public class UcClttypeService extends CoreBaseService {

	@Autowired
	private CommonDao commonDao;

	/**
	 * 客户端类型-添加
	 * @param ucClttype 客户端类型模型
	 * @return
	 * @throws DBException
	 */
	public DoResult add(UcClttype ucClttype) throws Exception {
		ucClttype.setClttypeId(commonDao.genSerial("UcClttype", "clttypeId", "CLTTYPE", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
		return commonDao.save(ucClttype);
	}

	/**
	 * 客户端类型-删除
	 * @param ucClttype 客户端类型模型
	 * @return
	 * @throws DBException
	 */
	public void delete(UcClttype ucClttype) throws DBException {
		commonDao.execute("UPDATE UcClttype SET clttypeDelstatus=? WHERE clttypeId=?", new Object[]{Avlstatus.YES,ucClttype.getClttypeId()});
	}

	/**
	 * 客户端类型-修改
	 * @param ucClttype 客户端类型模型
	 * @throws DBException
	 */
	public void edit(UcClttype ucClttype) throws DBException {
		String sql = "UPDATE UcClttype SET "
						+ "clttypeCode=?, "
						+ "clttypeName=?, "
						+ "clttypeTerminal=? "
						+ "WHERE clttypeId=? AND "
						+ "clttypeDelstatus=?";
		commonDao.execute(sql, new Object[] {ucClttype.getClttypeCode(),
											 ucClttype.getClttypeName(),
											 ucClttype.getClttypeTerminal(),
											 ucClttype.getClttypeId(),
											 ucClttype.getClttypeDelstatus()});
	}

	/**
	 * 客户端类型-详情
	 * @param clttypeId 主键
	 * @return
	 * @throws DBException
	 */
	public UcClttype findById(String clttypeId) throws DBException {
		Map param = MapUtils.toMap(new Object[][] { { "clttypeId", clttypeId }, });
		String sql = "SELECT * FROM UcClttype WHERE clttypeId=:clttypeId";
		return commonDao.queryObject(sql, param, UcClttype.class);
	}

	/**
	 * 分页数据
	 * @param mWhere where条件
	 * @param mGroup 组合条件
	 * @param mOrder 排序
	 * @param pageIndex 分页起始
	 * @param pageSize 分页数量
	 * @throws Exception
	 */
	public QueryResult findMapsCount(Map mWhere, Map mGroup, Map mOrder, int pageIndex, int pageSize)
			throws Exception {

		SqlWhere sqlWhere = getSqlWhere(sqlWE_findMapsCount, mWhere);
		SqlOrder sqlOrder = getSqlOrder(sqlOE_findMapsCount, mOrder);
		SqlGroup sqlGroup = null;
		String sql = " FROM UcClttype  ucClttype WHERE 1=1 ";
		return queryMapsCountBySqlWhere(commonDao,sql, sqlSelect_findMapsCount,sqlWhere,sqlGroup,sqlOrder,pageIndex,pageSize,new Class[]{UcClttype.class});	};

	/**
	 * 分页数据
	 * @param mWhere where条件
	 * @param mGroup group
	 * @param mOrder 排序
	 * @param pageIndex 分页起始
	 * @param pageCount 分页数量
	 * @return
	 * @throws Exception
	 */
	public QueryResult queryMapsCount(Map mWhere, Map mGroup, Map mOrder, int pageIndex, int pageCount)
			throws Exception {

		SqlWhere sqlWhere = getSqlWhere(sqlWE_findMapsCount, mWhere);
		SqlOrder sqlOrder = getSqlOrder(sqlOE_findMapsCount, mOrder);
		SqlGroup sqlGroup = null;
		String sql = "";
		QueryResult obj = queryMapsCountBySqlWhere(commonDao, " FROM UcClttype ucClttype WHERE 1=1 " + sql, sqlSelect_findMapsCount,
			sqlWhere, sqlGroup, sqlOrder, pageIndex, pageCount, new Class[] { UcClttype.class });
		return obj;
	}

	/**
	 * 分页条件
	 */
	private final SqlWE[] sqlWE_findMapsCount = new SqlWE[] { 
		new SqlWE("clttypeId", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("clttypeCode", SqlWE.Compare.like, SqlWE.Type.STR),
		new SqlWE("clttypeName", SqlWE.Compare.like, SqlWE.Type.STR),
		new SqlWE("clttypeTerminal", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("clttypeDelstatus", SqlWE.Compare.equal, SqlWE.Type.STR),
	};

	/**
	 * 分页排序
	 */
	private final SqlOE[] sqlOE_findMapsCount = new SqlOE[] { 
		new SqlOE("clttypeId", SqlOE.Option.both),
		new SqlOE("clttypeCode", SqlOE.Option.both),
		new SqlOE("clttypeName", SqlOE.Option.both),
		new SqlOE("clttypeTerminal", SqlOE.Option.both),
		new SqlOE("clttypeDelstatus", SqlOE.Option.both),
	};

	/**
	 * 查询结果
	 */
	private final SqlSelect sqlSelect_findMapsCount=new SqlSelect(new String[]{ 
		"clttypeId",
		"clttypeCode",
		"clttypeName",
		"clttypeTerminal",
		"clttypeDelstatus",
	});


	/**
	 * 根据某一列查询
	 * @param colsMap
	 * @return
	 * @throws DBException
	 */
	public UcClttype findByCols(Map<String,Object> colsMap) throws DBException {
		StringBuffer sql = new StringBuffer("SELECT * FROM UcClttype WHERE 1=1");
		Map<String,Object> params = new HashMap<String, Object>();
		for(String col : colsMap.keySet()) {
			//判断是否有值
			Object value = colsMap.get(col);
			if(value == null) {
				continue;
			}
			if(StringUtils.isEmpty(value.toString())) {
				continue;
			}
			params.put(col, value);
			sql.append(" and ").append(col).append("=");
			//验证value的类型
			if(value instanceof java.lang.String) {
				sql.append("'").append(value.toString()).append("'");
			}else {
				sql.append(value);
			}
		}
		return commonDao.queryObject(sql.toString(), params, UcClttype.class);
	}

	/**
	 * 判断是否存在
	 * @param clttypeCode
	 * @param clttypeTerminal
	 * @param clttypeId
	 * @return
	 * @throws DBException 
	 */
	public UcClttype exist(String clttypeCode, String clttypeTerminal, String clttypeId) throws DBException {
		Map param =MapUtils.toMap(new Object[][] {
						{ "clttypeCode", clttypeCode },
						{"clttypeTerminal",clttypeTerminal},
						{"clttypeId",clttypeId},
						{"clttypeDelstatus",Delstatus.NO}
					});
		String sql = "SELECT * FROM UcClttype WHERE clttypeCode =:clttypeCode AND clttypeTerminal=:clttypeTerminal AND clttypeId != :clttypeId AND clttypeDelstatus = :clttypeDelstatus";
		return commonDao.queryObject(sql, param, UcClttype.class);
	}

	/**
	 * 查询所有符合条件的客户端类型
	 * @param $params
	 * @return
	 * @throws DBException 
	 */
	public Map findAll(Map params) throws DBException {
		String sql = "SELECT * FROM UcClttype WHERE 1=1 ";
		String clttypeIds = (String) params.get("clttypeIds");
		if(!StringUtils.isEmpty(clttypeIds)) {
			String[] clttypeIdArr = clttypeIds.split(",");
			sql += " AND clttypeId in ("+SQLUtils.getIn(clttypeIdArr)+")";
		}
		String clttypeCode = (String) params.get("clttypeCode");
		if(!StringUtils.isEmpty(clttypeCode)) {
			sql += " AND clttypeCode = '"+ clttypeCode+"'";
		}
		String clttypeName = (String) params.get("clttypeName");
		if(!StringUtils.isEmpty(clttypeName)) {
			sql += " AND clttypeName = '"+ clttypeName+"'";
		}
		String clttypeTerminal = (String) params.get("clttypeTerminal");
		if(!StringUtils.isEmpty(clttypeTerminal)) {
			sql += " AND clttypeTerminal = '"+ clttypeTerminal+"'";
		}
		String clttypeDelstatus = (String) params.get("clttypeDelstatus");
		if(!StringUtils.isEmpty(clttypeDelstatus)) {
			sql += " AND clttypeDelstatus = '"+ clttypeDelstatus+"'";
		}
		String orderKey = (String) params.get("orderKey");
		if(!StringUtils.isEmpty(orderKey)) {
			sql += " ORDER BY " + orderKey;
			String orderVal = (String) params.get("orderVal");
			if(!StringUtils.isEmpty(orderVal) && "N".equals(orderVal)) {
				sql += " DESC";
			}else {
				sql += " ASC";
			}
		}
		
		List queryMaps = commonDao.queryMaps(sql, null, null);
		return MapUtils.toMap(new Object[][] {
			{"data",queryMaps}
		});
	}

}