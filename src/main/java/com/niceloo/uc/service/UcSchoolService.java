package com.niceloo.uc.service;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.uc.model.BdDpt;
import com.niceloo.uc.model.BdSchool;
import com.niceloo.uc.utils.UcLevelcodeUtils;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.MapUtils;

import java.util.*;

import static org.nobject.common.lang.StringUtils.isEmpty;


/**
 * 学院 业务
 * <p>
 * maning 20201205
 */
@Service
public class UcSchoolService extends CoreBaseService {

    @SuppressWarnings("unused")
    @Autowired
    private CommonDao commonDao_bd_write;

    /**
     * 学校-详情
     *
     * @param schoolId 主键
     */
    public BdSchool findById(String schoolId) throws DBException {
        Map param = MapUtils.toMap(new Object[][]{{"schoolId", schoolId},});
        String sql = "SELECT * FROM BdSchool WHERE schoolId=:schoolId";
        return commonDao_bd_write.queryObject(sql, param, BdSchool.class);
    }

    /**
     * 根据areacode和rangeType获取所有分校
     */
    public QueryResult findByAreaCode(String areacode, String rangeType, String schoolAvlstatus, String schoolDelstatus) throws Exception {
        //1.根据地区编码&地区分类获取获取所有分校ID
        //2.根据分校ID获取部门表ID和层级编码，dptType in ("A","S")
        StringBuilder sql = new StringBuilder();
        sql.append("select * from BdSchool bs left join BdDpt bd on bs.schoolId = bd.dptRelationid where bs.schoolAvlstatus = '").append(schoolAvlstatus).append("'").append(" and bs.schoolDelstatus = '").append(schoolDelstatus).append("' and bd.dptType in ('A','S') ");
        //判断rangeType为P省级/C市级/A区级
        if ("P".equals(rangeType)) {
            //省级
            sql.append(" and bs.schoolAreacode like '").append(areacode, 0, 2).append("%'");
        } else if ("C".equals(rangeType)) {
            //市级
            sql.append(" and bs.schoolAreacode like '").append(areacode, 0, 4).append("%'");
        } else if ("A".equals(rangeType)) {
            //区级
            sql.append(" and bs.schoolAreacode = '").append(areacode).append("'");
        }
        List<BdDpt> bdDpts = commonDao_bd_write.queryObjects(sql.toString(), null, BdDpt.class);
        //3.根据层级编码获取上级大区ID
        Set<String> ids = new HashSet<>();
        for (BdDpt dpt : bdDpts) {
            ids.add(dpt.getDptId());
            String dptLevelcode = dpt.getDptLevelcode();
            if (!isEmpty(dptLevelcode)) {
                String parentCode = UcLevelcodeUtils.getParentLevelcode(dptLevelcode);
                String parentDptSql = "select * from BdDpt where dptLevelcode = '" + parentCode + "' and dptType in ('A','S')";
                BdDpt parentDpt = commonDao_bd_write.queryObject(parentDptSql, null, BdDpt.class);
                if (parentDpt != null) {
                    ids.add(parentDpt.getDptId());
                }
            }
        }
        logger.info("findByAreaCode:id列表个数=" + ids.size());
        QueryResult result = new QueryResult();
        result.setData(Arrays.asList(ids.toArray()));
        result.setCount(ids.size());
        return result;
    }

}