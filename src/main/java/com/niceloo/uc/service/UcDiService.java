package com.niceloo.uc.service;

import static org.nobject.common.lang.StringUtils.isEmpty;

import java.util.HashMap;
import java.util.Map;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.db.Transactional;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.DateUtils;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.mvc.MVCUtils;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.uc.common.UcConst.Avlstatus;
import com.niceloo.uc.common.UcConst.UserFlag;
import com.niceloo.uc.model.UcUser;
@Service
public class UcDiService {

	/** commonDao_uc_read */
	@Autowired
	private CommonDao commonDao_uc_read;
	
	/** commonDao_uc_read */
	@Autowired
	private CommonDao commonDao_uc_write;
	/** 
 	 * 根据ID查询用户
 	 * @param userId 用户ID
	 * @throws DBException 
 	 */
	@Transactional(rollbackFor=Exception.class)
	public UcUser queryById(String userSourceid) throws DBException {
		return commonDao_uc_read.queryObject("SELECT * FROM UcUser WHERE userSourceid=?",new Object[]{userSourceid},UcUser.class);
	}

	/** 
	 * 更新密码
	 * @param userId 用户ID
	 * @param userLoginpwd 密码
	 * @throws DBException 
	 */
	@Transactional(rollbackFor=Exception.class)
	public void editLoginpwd(UcUser user) throws DBException {
		user.setUserModifieddate(DateUtils.getNowDString());
		commonDao_uc_write.update(user);
	}

 	/** 
	 * 删除
	 * @param 删除用户
	 * @param 
 	 * @throws DBException 
	 */
	@Transactional(rollbackFor=Exception.class)
	public void delete(UcUser user) throws DBException {
		user.setUserModifieddate(DateUtils.getNowDString());
		commonDao_uc_write.update(user);
	}

	/** 
	 * 可用
	 * @param 可用状态
	 * @param 
 	 * @throws DBException 
	 */
	@Transactional(rollbackFor=Exception.class)
	public void disable(UcUser user) throws DBException {
		user.setUserModifieddate(DateUtils.getNowDString());
		commonDao_uc_write.update(user);
	}
	
	
	
	
}
