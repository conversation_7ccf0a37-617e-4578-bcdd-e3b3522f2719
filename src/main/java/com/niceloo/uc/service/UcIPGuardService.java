package com.niceloo.uc.service;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.model.UcUser;
import com.niceloo.uc.model.log.UcAdminloginlog;
import com.niceloo.uc.model.log.UcLoginlog;
import com.niceloo.uc.utils.CoreLogUtil;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.StringUtils;

import java.sql.Timestamp;


/**
 * IPG 服务类
 * <p>
 * <AUTHOR> 20201205
 */
@Service
@SuppressWarnings("unused")
public class UcIPGuardService extends CoreBaseService {

    @Autowired
    private CommonDao commonDao_ipg_read;

    @Autowired
    private UcUserroleService ucUserroleService;

    @Autowired
    private UcEeService ucEeService;

    @Autowired
    private UcUserService ucUserService;

    private static final long ONE_HOUR = 60 * 60 * 1000;

    @SuppressWarnings("Duplicates")
    public void checkIpguard(String userId) throws Exception {
        if (!ucUserroleService.checkUserRoleIpguardstatus(userId)) {
            return;
        }

        String eeNo = ucEeService.findNoByUserId(userId);
        if (StringUtils.isEmpty(eeNo)) {
            String eMsg = "ipguard认证异常: 没有找到员工编号";
            logErr(userId, eMsg);
            throw new ApplicationException(UcConst.Errorcode.IP_GUARD_ERR, eMsg, null);
        }

        Timestamp lastOnlineTime = queryLastOnlineTime(eeNo);
        if (lastOnlineTime == null || System.currentTimeMillis() - lastOnlineTime.getTime() > ONE_HOUR) {
            String eMsg = "ipguard认证异常: 账号不在线";
            logErr(userId, eMsg);
            throw new ApplicationException(UcConst.Errorcode.IP_GUARD_ERR, eMsg, null);
        }
    }

    private Timestamp queryLastOnlineTime(String eeNo) throws DBException {
        String sql = "select AGT_ONLINE_TIME from AGENT where AGT_ALIAS=?";
        return commonDao_ipg_read.queryValue(sql, new Object[]{eeNo}, Timestamp.class);
    }

    private void logErr(String userId, String eMsg) throws Exception {
        UcUser ucUser = ucUserService.queryById(userId);
        UcLoginlog loginLog = new UcAdminloginlog();
        loginLog.setUserId(ucUser.getUserId());
        loginLog.setUserLoginname(ucUser.getUserLoginname());
        loginLog.setUserMobile(ucUser.getUserMobile());
        loginLog.setUserName(ucUser.getUserName());
        loginLog.setLogLoginerrormsg(eMsg);
        CoreLogUtil.send(loginLog);
    }

}