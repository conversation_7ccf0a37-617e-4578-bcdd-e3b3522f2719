package com.niceloo.uc.service;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.dao.CoreDao;
import com.niceloo.core.dao.DoResult;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.uc.model.UcRole;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.db.SQLUtils;
import org.nobject.common.db.Transactional;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.SetUtils;
import org.nobject.common.lang.StringUtils;

import java.util.*;


/**
 * 角色-业务类
 *
 * <AUTHOR>
 * @Date 2019-11-18 10:35:12
 */
@Service
public class UcRoleService extends CoreBaseService {

	@Autowired
	private CoreDao commonDao;

	@Autowired
	private UcRolemenuService ucRolemenuService;
	@Autowired
	private UcRoleService ucRoleService;
	@Autowired
	private UcUserroleService ucUserroleService;


	/**
	 * 角色-添加
	 *
	 * @param ucRole 角色模型
	 * @return
	 * @throws DBException
	 */
	@Transactional
	public DoResult save(UcRole ucRole, String userId, String brandId) throws Exception {

		if (!StringUtils.isEmpty(ucRole.getRoleType())) {
			String sql = "select * from UcRole where roleType = ? and brandId = ?";
			List<UcRole> list = commonDao.queryObjects(sql, new Object[]{ucRole.getRoleType(), ucRole.getBrandId()}, UcRole.class);
			if (list != null && list.size() > 0) {
				String roleName = list.get(0).getRoleName();
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "职能类型已被" + roleName + "占用，请重新选择", null);
			}
		}

		ucRole.setRoleId(commonDao.genSerial("UcRole", "roleId", "ROLE", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
		ucRole.setRoleAdminstatus("N");
		return commonDao.save(ucRole);
	}

	/**
	 * 角色-删除
	 *
	 * @param ucRole 角色模型
	 * @return
	 * @throws DBException
	 */
	@Transactional
	public void delete(UcRole ucRole) throws DBException, ApplicationException {

		String ss = "select count(*) count from UcUserrole where roleId = ?";
		Integer integer = commonDao.queryInt(ss, new Object[]{ucRole.getRoleId()});
		if (integer != null && integer.intValue() > 0) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "当前角色正在使用不能删除", null);
		}

		commonDao.delete(ucRole);

		String sql = "select * from UcRole where roleId in (" +
				"select roleId from UcRole where roleManagerole like ?)";
		List<UcRole> ucRoles = commonDao.queryObjects(sql, new Object[]{"%" + ucRole.getRoleId() + "%"}, UcRole.class);
		for (UcRole role : ucRoles) {
			String roleManagerole = role.getRoleManagerole();
			String[] split = roleManagerole.split(",");
			StringBuilder sb = new StringBuilder("");
			int i = 0;
			for (String s : split) {
				if (!"".equals(s) && !ucRole.getRoleId().equals(s)) {
					sb.append(s);
				}
				if (i != split.length - 1) {
					sb.append(",");
				}
			}
			role.setRoleManagerole(sb.toString());
		}
		if (ucRoles.size() > 0) {
			commonDao.update(ucRoles);
		}
		ucRolemenuService.deleteByRoleId(ucRole.getRoleId());
		ucUserroleService.deleteByRoleId(ucRole.getRoleId());
	}

	/**
	 * 角色-修改
	 *
	 * @param ucRole 角色模型
	 * @throws DBException
	 */
	public void update(UcRole ucRole) throws DBException, ApplicationException {

		if ("Y".equals(ucRole.getRoleAdminstatus())) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "不允许修改超级管理员", null);
		}

		if (!StringUtils.isEmpty(ucRole.getRoleType())) {
			String sql = "select * from UcRole where roleType = ? and brandId = ? and roleId <> ? ";
			List<UcRole> list = commonDao.queryObjects(sql, new Object[]{ucRole.getRoleType(), ucRole.getBrandId(), ucRole.getRoleId()}, UcRole.class);
			if (list != null && list.size() > 0) {
				String roleName = list.get(0).getRoleName();
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "职能类型已被" + roleName + "占用，请重新选择", null);
			}
		}

		ucRole.setRoleAdminstatus("N");
		commonDao.update(ucRole);
	}

	/**
	 * 复制一个角色
	 *
	 * @param copyRoleId
	 * @param roleName
	 */
	@Transactional
	public UcRole copyRole(String copyRoleId, String roleName, String createrid, String creatername, String brandId) throws Exception {
		UcRole byId = this.findById(copyRoleId);
		if (byId == null) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "复制的角色不存在", null);
		}
		UcRole role = new UcRole();
		role.setRoleName(roleName);
		role.setRoleMemo(byId.getRoleMemo());
		role.setRoleManagerole(byId.getRoleManagerole());
		role.setRoleSeq(byId.getRoleSeq());
		role.setRoleCreatedate(DateUtils.getNowDString());
		role.setRoleCreaterid(createrid);
		role.setRoleCreatername(creatername);
		role.setRoleIpstatus(byId.getRoleIpstatus());
		role.setRoleMobilecount(byId.getRoleMobilecount());
		role.setRoleIdcardcount(byId.getRoleIdcardcount());
		role.setRoleWeixincount(byId.getRoleWeixincount());
		role.setRoleManagescope(byId.getRoleManagescope());
		role.setRoleAgentcount(byId.getRoleAgentcount());
		role.setRoleIpguardstatus(byId.getRoleIpguardstatus());
		if (brandId == null || "".equals(brandId)) {
			role.setBrandId(byId.getBrandId());
		} else {
			role.setBrandId(brandId);
		}
		role.setRoleAdminstatus("N");
		role.setRoleId(commonDao.genSerial("UcRole", "roleId", "ROLE", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
		commonDao.save(role);

		ucRolemenuService.copyRoleMenuRelations(copyRoleId, role, createrid);
		return role;
	}

	/**
	 * 角色-详情
	 *
	 * @param roleId 主键
	 * @return
	 * @throws DBException
	 */
	public UcRole findById(String roleId) throws DBException {
		Map param = MapUtils.toMap(new Object[][]{{"roleId", roleId},});
		String sql = "SELECT * FROM UcRole WHERE roleId=:roleId";
		return commonDao.queryObject(sql, param, UcRole.class);
	}

	/**
	 * 查询全部角色，根据名称和品牌，可以不限制品牌，不区分管理范围，适用于超级管理员
	 *
	 * @param roleName
	 * @return
	 */
	public List<UcRole> queryRoleListByName(String roleName, String brandId) throws DBException {
		Map params = new HashMap();
		String sql = "select * from UcRole where 1=1 ";
		if (!StringUtils.isEmpty(roleName)) {
			sql += " and roleName like :roleName";
			params.put("roleName", "%" + roleName + "%");
		}
		if (!StringUtils.isEmpty(brandId)) {
			sql += " and brandId = :brandId";
			params.put("brandId", brandId);
		}
		sql += " order by brandId , roleSeq";
		List<UcRole> ucRoles = commonDao.queryObjects(sql, params, UcRole.class);
		return ucRoles;
	}

	/**
	 * 获取角色列表(根据当前登录用户所属品牌过滤)
	 *
	 * @param userId
	 * @param brandId
	 * @param roleName
	 * @return
	 * @throws DBException
	 */
	public List<UcRole> queryRoleListByUser(String userId, String brandId, String roleName) throws DBException {

		// 根据管理策略获取
		String sql = "select * from UcRole where roleId in " +
				"(select roleId from UcUserrole where userId = :userId and brandId = :brandId )";
		List<UcRole> ucRoles = commonDao.queryObjects(sql, MapUtils.toMap(new Object[][]{
				{"userId", userId},
				{"brandId", brandId}
		}), UcRole.class);
		// 管理范围，自定义范围或当前品牌
		String managescope = "C";
		for (UcRole ucRole : ucRoles) {
			if (ucRole.getRoleManagescope() != null && ucRole.getRoleManagescope().equals("B")) {
				managescope = "B";
				break;
			}
		}
		if ("B".equals(managescope)) {
			Map p = new HashMap();
			sql = "select * from UcRole where brandId =:brandId ";
			if (!StringUtils.isEmpty(roleName)) {
				sql += " and roleName like :roleName";
				p.put("roleName", "%" + roleName + "%");
			}
			p.put("brandId", brandId);
			sql += " order by roleSeq";
			List<UcRole> list = commonDao.queryObjects(sql, p, UcRole.class);
			return list;
		}

		Set<String> manageRoleIds = new LinkedHashSet<>();
		for (UcRole ucRole : ucRoles) {
			String roleManagerole = ucRole.getRoleManagerole();
			if (!StringUtils.isEmpty(roleManagerole)) {
				String[] split = roleManagerole.split(",");
				for (String s : split) {
					manageRoleIds.add(s);
				}
			}
		}
		// 有管理的角色，根据id查询
		Map p = new HashMap();
		if (!manageRoleIds.isEmpty()) {
			sql = "select * from UcRole" +
					" where (roleId in (" + SQLUtils.getIn(manageRoleIds) + ") or (roleCreaterid = :userId and brandId = :brandId) )";
		} else {
			sql = "select * from UcRole" +
					" where (roleCreaterid = :userId and brandId = :brandId )";
		}
		if (!StringUtils.isEmpty(roleName)) {
			sql += " and roleName like :roleName ";
			p.put("roleName", "%" + roleName + "%");
		}
		p.put("userId", userId);
		p.put("brandId", brandId);
		sql += " order by roleSeq";
		List<UcRole> list = commonDao.queryObjects(sql, p, UcRole.class);
		return list;
	}


	/**
	 * 获取存在的角色id集合
	 *
	 * @param list
	 * @return
	 * @throws DBException
	 */
	public Set<String> findExistRoles(List<String> list) throws DBException {
		String sql = "select roleId from UcRole where roleId in (" + SQLUtils.getIn(list) + ")";
		String[] strings = commonDao.queryStrings(sql, new Object[]{});
		return SetUtils.toSet(strings);
	}

	/**
	 * 根据Id列表获得角色
	 *
	 * @param set
	 * @return
	 * @throws DBException
	 */
	public List<UcRole> findByIds(Set set) throws DBException {
		if (set == null || set.size() == 0) {
			return new ArrayList<>();
		}
		String sql = "select * from UcRole where roleId in (" + SQLUtils.getIn(set) + ")";
		return commonDao.queryObjects(sql, new Object[]{}, UcRole.class);
	}

	/**
	 * 角色导出查询
	 */
	public List<Map> queryForExport(List list) throws DBException {
		String sql = "select roleName,roleSeq,roleMemo from UcRole where roleId in (" + SQLUtils.getIn(list) + ")";
		return commonDao.queryMaps(sql, new Object[]{}, null);
	}

	/**
	 * 移动角色
	 *
	 * @param sourceId
	 * @param destId
	 * @param mode
	 * @throws ApplicationException 
	 */
	@Transactional
	public void move(String sourceId, String destId, String mode) throws DBException, ApplicationException {
		UcRole source = this.findById(sourceId);

		UcRole dest = this.findById(destId);
		if (source == null || dest == null) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "source和dest不能为空", null);
		}
		if ("Y".equals(source.getRoleAdminstatus())) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "不允许移动超级管理员", null);
		}
		if (!StringUtils.isEmpty(dest.getBrandId()) && !source.getBrandId().equals(dest.getBrandId())) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "不允许跨品牌移动", null);
		}

		String sql = "select * from UcRole where brandId = ? order by roleSeq";
		List<UcRole> ucRoles = commonDao.queryObjects(sql, new Object[]{source.getBrandId()}, UcRole.class);
		Iterator<UcRole> iterator = ucRoles.iterator();
		int destIndex = 0;
		while (iterator.hasNext()) {
			UcRole next = iterator.next();
			if (next.getRoleId().equals(source.getRoleId())) {
				iterator.remove();
				break;
			}
		}
		for (int j = 0; j < ucRoles.size(); j++) {
			if (ucRoles.get(j).getRoleId().equals(destId)) {
				destIndex = j;
			}
		}
		// after 在dest之后
		if ("A".equals(mode)) {
			ucRoles.add(destIndex + 1, source);
		} else {
			ucRoles.add(destIndex, source);
		}
		for (int i = 0; i < ucRoles.size(); i++) {
			UcRole role = ucRoles.get(i);
			role.setRoleSeq(i * 10 + 10);
		}
		commonDao.update(ucRoles);

	}

	/**
	 * 查出所有的角色类型
	 *
	 * @return
	 * @throws DBException
	 */
	public List<Map> findRoleTypes() throws DBException {
		String sql = "select dictCode roletypeCode,dictName roletypeName,dictSeq roleTypeSeq from UcDict where dictType = 'roletype' order by dictSeq";
		List<Map> list = commonDao.queryMaps(sql, new Object[0], null);
		return list;
	}

	/**
	 * 根据类型查询，如果不传，查出所有类型不为空的
	 *
	 * @param roleType
	 * @param brandId
	 * @return
	 * @throws DBException
	 */
	public List<UcRole> findByRoleTypes(String roleType, String brandId) throws DBException {
		if (StringUtils.isEmpty(roleType)) {
			String sql = "select * from UcRole where roleType is not null and roleType <> '' and brandId = ?";
			List<UcRole> ucRoles = commonDao.queryObjects(sql, new Object[]{brandId}, UcRole.class);
			return ucRoles;
		} else {
			String sql = "select * from UcRole where roleType = ? and brandId = ?";
			List<UcRole> ucRoles = commonDao.queryObjects(sql, new Object[]{roleType, brandId}, UcRole.class);
			return ucRoles;
		}


	}

	public String[] findUsersByrole(String roleId) throws DBException {
		String sql = "select userId from UcUserrole where roleId  = ?";
		String[] ucRoles = commonDao.queryStrings(sql, new Object[]{roleId});
		return ucRoles;
	}
}

