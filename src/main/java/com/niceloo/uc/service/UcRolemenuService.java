package com.niceloo.uc.service;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.dao.CoreDao;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.uc.model.UcRole;
import com.niceloo.uc.model.UcRolemenu;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.db.SQLUtils;
import org.nobject.common.db.Transactional;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.CollectionUtils;
import org.nobject.common.lang.ListUtils;
import org.nobject.common.lang.ObjectUtils;
import org.nobject.common.lang.SetUtils;

import java.util.*;


/**
 * 角色权限-业务类
 *
 * <AUTHOR>
 * @Date 2019-11-18 10:35:12
 */
@Service
public class UcRolemenuService extends CoreBaseService {

	@Autowired
	private CoreDao commonDao;

	@Autowired
	private UcUserroleService ucUserroleService;

//	/**
//	 * 角色权限-添加
//	 *
//	 * @param ucRolemenus 角色权限模型集合
//	 * @return
//	 * @throws DBException
//	 */
//	public DoResult saves(List<UcRolemenu> ucRolemenus) throws Exception {
//		for (UcRolemenu ucRolemenu : ucRolemenus) {
//			genId(ucRolemenu);
//		}
//		return commonDao.save(ucRolemenus);
//	}

	private void genId(UcRolemenu ucRolemenu) throws DBException {
		ucRolemenu.setRolemenuId(commonDao.genSerial("UcRolemenu", "rolemenuId", "ROLEMENU", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
	}

	/**
	 * 根据roleId查询权限，主要用于修改
	 *
	 * @param roleId
	 * @return
	 * @throws DBException
	 */
	public List<UcRolemenu> queryByRoleId(String roleId) throws DBException {
		String sql = "select * from UcRolemenu where roleId = ?";
		List<UcRolemenu> ucRolemenus = commonDao.queryObjects(sql, new Object[]{roleId}, UcRolemenu.class);
		return ucRolemenus;
	}

	/**
	 * 根据role集合查询对应的权限，主要用于用户权限判断
	 *
	 * @param roleId
	 * @return
	 * @throws DBException
	 */
	public List queryByRoleIds(List<String> roleId) throws DBException {
		String sql = "select * from UcRolemenu where roleId in ?";
		List<UcRolemenu> ucRolemenus = commonDao.queryObjects(sql, new Object[]{roleId}, UcRolemenu.class);
		return ucRolemenus;
	}

	/**
	 * 根据roleId删除对应的权限关系
	 *
	 * @param roleId
	 * @throws DBException
	 */
	public void deleteByRoleId(String roleId) throws DBException {
		commonDao.execute("delete from UcRolemenu where roleId = ?", new Object[]{roleId});
	}


	/**
	 * 逻辑：
	 * 根据当前用户查询他管理范围内的菜单以及菜单所属的数据权限
	 * 对于传入的menus，当前人有的menus，以及权限内的
	 * menus中有，但是权限中没有的，忽略，可能是越权访问
	 * menus中有，权限汇总也有，但是数据权限超出范围的，越权访问
	 * menus中有，权限中也有，但是当前用户没有的，新增
	 * menus中有，权限中也有，当前用户也有的，更新数据范围
	 * 更新数据范围时，对比权限中的数据范围，对于用户有但是权限范围内没有的，不能剔除
	 * 对于权限中有但是menus中没有的，删除
	 * 当前用户有，但是权限中没有的，保留不变
	 * 权限中有，但是menus中没有的，需要删除
	 * <p>
	 * 代码方案
	 * 1. 查出角色原有menus，datapolicy，对比权限数据，将不能删除的menus和datapolicy构造出来
	 * 2. 结合权限内的菜单和要设置的菜单，构建删除和新增的list
	 * 3. 合并上述两个list即可
	 *
	 * @param ucRole 设置的角色
	 * @param userId 当前登录的用户
	 * @param menus  要设置的菜单
	 * @throws Exception
	 */
	@Transactional
	public void setMenusbyRoleId(UcRole ucRole, String userId, List<Map> menus) throws Exception {
		if ("Y".equals(ucRole.getRoleAdminstatus())) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "不允许修改超级管理员", null);
		}
		// 超级管理员，直接删除全部，新增即可
		if (ucUserroleService.isSuperAdmin(userId)) {
			setRolemenuByAdmin(ucRole, menus);
			return;
		}

		String brandId = ucRole.getBrandId();
		Map<String, Set> menusInThisUser = this.ucUserroleService.getMenusByUser(userId, brandId);
		List<UcRolemenu> rolemenus = this.queryByRoleId(ucRole.getRoleId());

		Map<String, Set> menusNeedRemain = getRemainsMenuFromRole(menusInThisUser, rolemenus);


		List<UcRolemenu> ucRolemenus = new ArrayList<>();
		Iterator<Map> iterator = menus.iterator();
		while (iterator.hasNext()) {
			Map menu = iterator.next();
			String menuId = (String) menu.get("menuId");
			List<String> roleDatapolicy = (List) menu.get("roleDatapolicy");

			/* 越权校验 */
			Set set = menusInThisUser.get(menuId);
			if (set == null) {
				// 越权菜单，不设置
				iterator.remove();
				continue;
			}
			if (roleDatapolicy != null && roleDatapolicy.size() > 0) {
				Iterator<String> iterator1 = roleDatapolicy.iterator();
				while (iterator1.hasNext()) {
					String s = iterator1.next();
					if (!set.contains(s)) {
						iterator1.remove();
					}
				}
			}

			/* 构建保存列表 */
			UcRolemenu rolemenu = new UcRolemenu();
			rolemenu.setMenuId(menuId);
			rolemenu.setRoleId(ucRole.getRoleId());
			Set policyRemains = menusNeedRemain.get(menuId);
			if (policyRemains != null && policyRemains.size() > 0) {
				roleDatapolicy.addAll(policyRemains);
			}
			if(!ObjectUtils.isEmpty(roleDatapolicy)){
				String s = roleDatapolicy.toString().replaceAll("\\s", "");
				rolemenu.setRoleDatapolicy(s.substring(1, s.length() - 1));
			}
			this.genId(rolemenu);
			ucRolemenus.add(rolemenu);
			menusNeedRemain.remove(menuId);
		}
		if (!menusNeedRemain.isEmpty()) {
			for (Map.Entry<String, Set> entry : menusNeedRemain.entrySet()) {
				UcRolemenu rolemenu = new UcRolemenu();
				rolemenu.setMenuId(entry.getKey());
				rolemenu.setRoleId(ucRole.getRoleId());
				String s = entry.getValue().toString().replaceAll("\\s", "");
				rolemenu.setRoleDatapolicy(s.substring(1, s.length() - 1));
				this.genId(rolemenu);
				ucRolemenus.add(rolemenu);
			}
		}
		this.deleteByRoleId(ucRole.getRoleId());
		if (!CollectionUtils.isEmpty(ucRolemenus)) {
			this.commonDao.save(ucRolemenus);
		}
	}

	/**
	 * 提取当前已有，但是本人无权访问的菜单
	 *
	 * @param menusInThisUser
	 * @param rolemenus
	 * @return
	 */
	private Map<String, Set> getRemainsMenuFromRole(Map<String, Set> menusInThisUser, List<UcRolemenu> rolemenus) {
		Map<String, Set> menusNeedRemain = new HashMap<>();
		if (rolemenus != null && rolemenus.size() > 0) {
			for (UcRolemenu rolemenu : rolemenus) {
				String menuId = rolemenu.getMenuId();
				Set policyInUser = menusInThisUser.get(menuId);
				String roleDatapolicy = rolemenu.getRoleDatapolicy();
				// 当前用户管理不到此菜单
				if (policyInUser == null) {
					if (roleDatapolicy != null && !"".equals(roleDatapolicy)) {
						Set<String> set = SetUtils.toSet(roleDatapolicy.split(","));
						menusNeedRemain.put(menuId, set);
					} else {
						menusNeedRemain.put(menuId, new HashSet());
					}
				} else {
					// 当前用户管理到此菜单，校验数据权限的管理范围
					if (roleDatapolicy != null && !"".equals(roleDatapolicy)) {
						Set<String> set = SetUtils.toSet(roleDatapolicy.split(","));
						Set policyNeedRemain = new HashSet();
						for (String s : set) {
							if (!policyInUser.contains(s)) {
								policyNeedRemain.add(s);
							}
						}
						menusNeedRemain.put(menuId, policyNeedRemain);
					}
					// 没有数据权限，不需保留
				}
			}
		}
		return menusNeedRemain;
	}

	/**
	 * 超级管理员无需管理越权等问题，单独列出
	 *
	 * @param ucRole
	 * @param menus
	 * @throws DBException
	 */
	private void setRolemenuByAdmin(UcRole ucRole, List<Map> menus) throws DBException {
		List<UcRolemenu> ucRolemenus = new ArrayList<>();
		for (Map menu : menus) {
			UcRolemenu rolemenu = new UcRolemenu();
			String menuId = (String) menu.get("menuId");
			rolemenu.setMenuId(menuId);
			rolemenu.setRoleId(ucRole.getRoleId());
			Object roleDatapolicy = menu.get("roleDatapolicy");
			this.genId(rolemenu);
			if (roleDatapolicy != null && roleDatapolicy instanceof List) {
				List list = (List) roleDatapolicy;
				if (list.size() > 0) {
					String s = list.toString().replaceAll("\\s", "");
					rolemenu.setRoleDatapolicy(s.substring(1, s.length() - 1));
				}
			}
			ucRolemenus.add(rolemenu);
		}
		this.deleteByRoleId(ucRole.getRoleId());
		commonDao.save(ucRolemenus);
		return;
	}

	/**
	 * 复制角色权限关系
	 *
	 * @param oldRoleId
	 */
	public void copyRoleMenuRelations(String oldRoleId, UcRole ucRole, String currUserId) throws Exception {
		String sql = "select menuId , roleDatapolicy from UcRolemenu where roleId = ?";
		List<Map> list = commonDao.queryMaps(sql, new Object[]{oldRoleId}, new HashMap<>());
		for (Map map : list) {
			String roleDatapolicy = (String) map.get("roleDatapolicy");
			if (roleDatapolicy != null) {
				String[] split = roleDatapolicy.split(",");
				List list1 = ListUtils.toList(split);
				map.put("roleDatapolicy", list1);
			}
		}
		this.setMenusbyRoleId(ucRole, currUserId, list);
	}

	/**
	 * 根据menuId删除对应的权限关系
	 *
	 * @param menuId
	 * @throws DBException
	 */
	public void deleteByMenuId(String menuId) throws DBException {
		commonDao.execute("delete from UcRolemenu where menuId = ?", new Object[]{menuId});
	}

	/**
	 * 查询角色集合中是否包含对应的code
	 * @param menuCode
	 * @param roleSet
	 * @return
	 * @throws DBException
	 */
	public Boolean queryCodeExists(String menuCode, Set roleSet) throws DBException {
		String sql = "select menuCode,menuExtcode from UcRolemenu r join UcMenu m on r.menuId = m.menuId where " +
				"  r.roleId in (" + SQLUtils.getIn(roleSet) + ") and m.menuCode is not null";
		List<Map> list = commonDao.queryMaps(sql, new Object[]{},null);
		if(list == null ||list.size() ==0){
			return false;
		}
		for (Map map : list) {
			String menuCode1 = (String)map.get("menuCode");
			if(menuCode1 != null && menuCode1.equals(menuCode)){
				return true;
			}
			String menuExtcode = (String)map.get("menuExtcode");
			if(menuExtcode !=null && menuExtcode.length()>0){
				String[] split = menuExtcode.split(",");
				for (String s : split) {
					if(s != null && s.trim().equals(menuCode)){
						return true;
					}
				}
			}
		}
		return  false;
	}


}