package com.niceloo.uc.service;

import static org.nobject.common.lang.StringUtils.isEmpty;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.js.JSONUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.mq.MQService;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.uc.common.UcConfig;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.model.UcDict;
import com.niceloo.uc.utils.DictUtils;

@Service
public class UcDictService extends CoreBaseService{
	
	/** commonDao_uc_read */
	@Autowired
	private CommonDao commonDao_uc_read;
	
	/** commonDao_uc_read */
	@Autowired
	private CommonDao commonDao_uc_write;
	@Autowired
	private MQService mqService;
	
	public int LEVELCODE_LENGTH=10;

	public Map queryDictTree(Map where) throws DBException {
		
	String sql = 
		"SELECT "
			+ "dictId,"
			+ "dictType,"
			+ "dictName,"
			+ "dictCode,"
			+ "dictLevelcode,"
			+ "dictSeq,"
			+ "dictVal,"
			+ "dictMemo"
		+ " FROM "
			+ "UcDict"
		+ " WHERE"
			+ " dicttype=:dicttype "
		+ " ORDER BY "
			+ " LENGTH(dictLevelcode), "
			+ " dictSeq, "
			+ " dictLevelcode ";
		
		List<UcDict> data=commonDao_uc_read.queryObjects(sql, null, null, where, UcDict.class);
		
		Map<String,Map> nodes	=new HashMap();
		List childs				=new LinkedList();
		for(UcDict dict:data){
			Map mDict			=BeanUtils.bean2Map(dict);
			String pLevelcode	=dict.getDictLevelcode().substring(0, dict.getDictLevelcode().length()-LEVELCODE_LENGTH); 
			nodes.put(dict.getDictLevelcode(), mDict);
			if(isEmpty(pLevelcode)){
				childs.add(mDict);
			}else{
				Map pDict=nodes.get(pLevelcode);
				if(pDict== null) {
					continue;
				}
				if(!pDict.containsKey("children")){
					pDict.put("children", new LinkedList());
				}
				List pChilds=(List)pDict.get("children");
				pChilds.add(mDict);
				pDict.put("children", pChilds);
			}
		}
		
		QueryResult qr=new QueryResult();
		//转换成Map返回
		qr.setData(childs);
		
		Map prt = BeanUtils.bean2Map(qr);
		
		return prt;
	}

	
	/*
	 * 字典类型
	 * */
	public QueryResult queryDictType() throws DBException {

		String sql = 
			"SELECT"
				+ " dictType"
			+ " FROM"
				+ " UcDict"
			+ " GROUP BY"
				+ " dictType";
		
		QueryResult qr = new QueryResult();
		
		List list = commonDao_uc_read.queryObjects(sql, null, null, null,UcDict.class);
		
		qr.setData(list);
		
		return qr;
	}

	/*
	 * 根据ID查询
	 * */
	public UcDict queryDictById(String dictId) throws DBException {
		
		Map param=MapUtils.toMap(new Object[][]{
			{"dictId",dictId},
		});
		
		String sql=
			"SELECT"
				+ " *"
			+ " FROM"
				+ " UcDict"
			+ " WHERE "
				+ "dictId=:dictId";
	
		return commonDao_uc_read.queryObject(sql, param, UcDict.class);
	}

	/*
	 * 修改
	 * */
	public void edit(UcDict dict) throws Exception {
		
		commonDao_uc_write.update(dict);
		pushMq(dict.getDicttype());
	}

	/*
	 * 新增
	 * */
	public void add(UcDict dict, String pId) throws Exception {
		
		Map param = null;
		String maxClevel = "";
		String newMaxClevel = "";
		String plevelcode = "";
		int plength = 0;
		
		if(!isEmpty(pId)){
			 
			 param=MapUtils.toMap(new Object[][]{
				{"pId",pId}
			});
		
		String sql=
				"SELECT"
					+ " *"
				+ " FROM "
					+ "UcDict "
				+ "WHERE "
					+ "dictId=:pId";
		
		UcDict pdict = commonDao_uc_read.queryObject(sql, param, UcDict.class);
		
		if(pdict==null){throw new  ApplicationException(UcConst.Errorcode.PARENT_EMPTY,"父级节点不存在",null);}
		
		 plength = pdict.getDictLevelcode().length();
		
		 plevelcode = pdict.getDictLevelcode();
		 
		}
		
		param=MapUtils.toMap(new Object[][]{
			{"plevelcode",plevelcode},
			{"plength",plength},
	});
	
		
		String sql2=
				"SELECT "
					+ "max(dictLevelcode) "
				+ " FROM "
					+ "UcDict "
				+ "WHERE "
					+ "LENGTH(dictLevelcode)-10=:plength "
				+ "AND "
					+ "substring(dictLevelcode,1,LENGTH(dictLevelcode)-10) = :plevelcode";
		
		 maxClevel = commonDao_uc_read.queryString(sql2, param);
		
		if(isEmpty(maxClevel)){
			 newMaxClevel = plevelcode + "0000000001";
		}else{
			int maxClevel_end = Integer.parseInt(maxClevel.substring(plength, maxClevel.length()));
			maxClevel_end++;
			newMaxClevel = plevelcode + String.format("%010d", maxClevel_end);
		}
		
		dict.setDictLevelcode(newMaxClevel);
		dict.setDictId(CoreUtils.genSerial(commonDao_uc_write, "UcDict", "dictId", "DICT", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
	
		commonDao_uc_write.save(dict);
		pushMq(dict.getDicttype());
	}

	/*
	 *删除
	 * */
	public void del(String dictId) throws Exception {
		
		UcDict dict = queryDictById(dictId);
		
		String dictLevelcode = dict.getDictLevelcode().toString()+"%";
		
		String sql = 
			"DELETE"
			+ " FROM"
				+ " UcDict"
			+ " WHERE"
				+ " dictLevelcode LIKE ?";
		commonDao_uc_write.execute(sql,new Object[]{dictLevelcode});
		pushMq(dict.getDicttype());
	}
	
	/**
	 * 推送mq消息给niceloo-base更新字典表信息
	 * @param type
	 * @throws Exception
	 */
	public void pushMq(String type) throws Exception {
		mqService.publish(UcConst.Mqtype.DICT_MQ_SUBCREABER_EDIT, JSONUtils.toString(MapUtils.toMap(new Object[][] {
			{"dictType"			,type			},
		})));
	}
	
	/**
	 * 查询字典根据级层编码
	 */
	public UcDict queryDictByLevelcode(String dictLevelcode) throws DBException {
		
		Map param=MapUtils.toMap(new Object[][]{
			{"dictLevelcode",dictLevelcode},
		});
		
		String sql=
			"SELECT"
				+ " *"
			+ " FROM"
				+ " UcDict"
			+ " WHERE "
				+ "dictLevelcode=:dictLevelcode";
	
		return commonDao_uc_read.queryObject(sql, param, UcDict.class);
	}
	//查询字典层级
	public Map rank(Map $params) throws Exception {
		
		String sql = 
				"SELECT "
					+ "dictId,"
					+ "dictName,"
					+ "dictCode,"
					+ "dictLevelcode,"
					+ "dictSeq,"
					+ "dictVal,"
					+ "dictMemo"
				+ " FROM "
					+ "UcDict"
				+ " WHERE"
					+ " dicttype = '"+(String)$params.get("dicttype")+"'";
					
		Object dictId = $params.get("dictId");
		Object pId = $params.get("pId");
		UcDict dictByid= null;
		int length=0;
		if(StringUtils.isEmpty((String) dictId) && StringUtils.isEmpty((String) pId)) {
		}else {
			if(!StringUtils.isEmpty((String) dictId)) {
				dictByid = queryDictById((String)dictId);
			}else {
				if(pId != null) {
					dictByid = queryDictByLevelcode((String)pId);
				}
			}
			if(dictByid == null) {
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "没有该条数据", null);
			}
			String dictLevelcode = dictByid.getDictLevelcode();
			length = dictLevelcode.length();
			sql =sql+ " and dictLevelcode like '" + dictByid.getDictLevelcode()+"%'"
					+ " and dictLevelcode != '" + dictByid.getDictLevelcode() + "'";
		}
			
		
					Object rank = $params.get("rank");
					if(rank != null) {
						int sqllength=length+LEVELCODE_LENGTH*(int)rank;
						sql=sql + 	"and LENGTH(dictLevelcode) <=" + sqllength;
					}
					sql= sql+ " ORDER BY "
					+ " LENGTH(dictLevelcode), "
					+ " dictSeq, "
					+ " dictLevelcode ";
		List<Map> data=commonDao_uc_read.queryMaps(sql, null, null);
		//组装树结构
		boolean isTree = (boolean) $params.get("isTree");
		Map prt=null;
		if(isTree) {
			Map<String,Map> nodes	=new HashMap();
			List childs				=new LinkedList();
			for(Map dict:data){
				String levelCode = (String) dict.get("dictLevelcode");
				String pLevelcode	=levelCode.substring(0, levelCode.length()-LEVELCODE_LENGTH); 
				nodes.put(levelCode, dict);
				if(isEmpty(pLevelcode) || length==pLevelcode.length()){
					childs.add(dict);
				}else{
					Map pDict=nodes.get(pLevelcode);
					if(pDict== null) {
						continue;
					}
					if(!pDict.containsKey("children")){
						pDict.put("children", new LinkedList());
					}
					List pChilds=(List)pDict.get("children");
					pChilds.add(dict);
					pDict.put("children", pChilds);
				}
			}
			QueryResult qr=new QueryResult();
			//转换成Map返回
			qr.setData(childs);
			prt= BeanUtils.bean2Map(qr);
		}else {
			QueryResult qr=new QueryResult();
			//转换成Map返回
			qr.setData(data);
			prt=BeanUtils.bean2Map(qr);
		}
		prt.remove("count");
		return prt;
	}

	/**
	 * 获取字典树
	 * @param dictCode  字典编码
	 * @param rank		字典向下层数
	 * @param needArea 
	 * @param totalRank	字典总层数
	 * @return
	 * @throws Exception 
	 */
	public List<Map> areaTree(String dictCode, int rank, String needArea) throws Exception {
		List<Map> areas = new ArrayList<>();
		List<Map> dicts = DictUtils.getDicts("area");
		if(!StringUtils.isEmpty(dictCode)) {
			if("0000".equals(dictCode.substring(2)) && "N".equals(needArea)) {
				//不需要直辖市的
				for (Map dict : dicts) {
					if(dictCode.equals(dict.get("dictCode"))) {
						dicts=(List<Map>) dict.get("children");
						if(dicts != null) {
							Map dictMap = dicts.get(0);
							dictCode = (String) dictMap.get("dictCode");
							ArrayList<String> mdCodeList = getMdCodeList();
							if(mdCodeList.contains(dictCode)) {
								ArrayList<Map> children = new ArrayList<>();
								for (Map map : dicts) {
									children.addAll((List<Map>)map.get("children"));
								}
								dicts = children;
							}
							rank++;
							break;
						}
					}
				}
			}else {
				//全部都需要的
				dicts=getDictChildren(dicts, dictCode);
			}
		}
		composeArea(areas, dicts, rank, needArea);
		return areas;
	}
	
	/**
	 * 根据字典编码获取子
	 * @param dicts
	 * @param dictCode
	 * @return
	 */
	public List<Map> getDictChildren(List<Map> dicts,String dictCode){
		if(dicts == null) {
			return null;
		}
		for (Map map : dicts) {
			if(dictCode.equals(map.get("dictCode"))) {
				return (List<Map>) map.get("children");
			}else {
				List<Map> children =getDictChildren((List<Map>) map.get("children"), dictCode);
				if(children != null) {
					return children;
				}
			}
		}
		return null;
	}
	
	/**
	 * 组织地区编码树
	 * @param areas  		待添加数组
	 * @param dicts	 		获取到的数据
	 * @param rank  		层数
	 * @param needArea   	是否需要市辖区
	 */
	public void composeArea(List<Map> areas,List<Map> dicts, int rank, String needArea) {
		if(dicts == null || dicts.size() < 1) {
			return;
		}
		for (Map dict : dicts) {
			Map<String, Object> area = getAreaByDict(dict);
			areas.add(area);
			setChildren(area, rank-1,(List<Map>) dict.get("children"),needArea);
		}
	}
	
	/**
	 * 获得直辖市编码
	 * @return
	 */
	public ArrayList<String> getMdCodeList() {
		ArrayList<String> mdCodeList = new ArrayList<>();
		String[] split = UcConfig.Area.mdCode.split(",");
		for (String string : split) {
			mdCodeList.add(string);
		}
		return mdCodeList;
	}
	
	/**
	 * 设置地区的子
	 * @param areas
	 * @param rank
	 * @param children 
	 */
	public void setChildren(Map<String, Object> area,int rank, List<Map> children,String needArea) {
		String dictCode = (String) area.get("areaCode");
		if("0000".equals(dictCode.substring(2)) && "N".equals(needArea)) {
			//不需要
			if(children != null && children.size()>0) {
				Map map = children.get(0);
				dictCode = (String) map.get("dictCode");
				ArrayList<String> mdCodeList = getMdCodeList();
				if(mdCodeList.contains(dictCode)) {
					rank = rank - 1;
					ArrayList<Map> realChildren = new ArrayList<>();
					for (Map dictMap : children) {
						Object object = dictMap.get("children");
						if(object != null) {
							realChildren.addAll((List<Map>)object);
						}
					}
					children = realChildren;
//					children = (List<Map>) map.get("children");
				}
			}
		}
		if(rank>0) {
			if(children != null && children.size() > 0) {
				List<Map> areas = new ArrayList<>();
				composeArea(areas, children, rank, needArea);
				area.put("children", areas);
			}
		}
		if(children != null && children.size() > 0) {
			area.put("existNode", false);//不是叶子节点
		}else {
			area.put("existNode", true);//是叶子节点
		}
	}
	
	public Map<String, Object> getAreaByDict(Map dict) {
		HashMap<String,Object> area = new HashMap<>();
		String dictCode = (String) dict.get("dictCode");
		String dictName = (String) dict.get("dictName");
		area.put("areaCode", dictCode);
		area.put("areaName", dictName);
		Object object = dict.get("children");
		if(object == null) {
			area.put("existNode", "N");
		}else {
			area.put("existNode", "Y");
		}
		return area;
	}

	/**
	 * 根据字典标识集合查询字典
	 * @param dictId
	 * @return
	 * @throws DBException 
	 */
	public List queryListIds(List<String> dictIds) throws DBException {
		if(dictIds == null || dictIds.size() <1) {
			return new ArrayList<>();
		}
		StringBuilder sb = new StringBuilder();
		for (int j = 0; j < dictIds.size(); j++) {
			sb.append("SELECT * FROM UcDict WHERE dictId = '" + dictIds.get(j) + "' ");
            if (j != dictIds.size() - 1) {
                sb.append(" UNION ");
            }
		}
		return commonDao_uc_read.queryMaps(sb.toString(), null, null);
	}
	
}
