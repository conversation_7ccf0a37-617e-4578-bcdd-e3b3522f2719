package com.niceloo.uc.service;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.uc.model.BdDptee;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;

import java.util.List;
import java.util.Map;

/**
 * 部门员工关系 服务
 * <p>
 * maning 20201205
 */
@Service
public class UcDpteeService extends CoreBaseService {

    @SuppressWarnings("unused")
    @Autowired
    private CommonDao commonDao_bd_write;

    /**
     * 查询员工的部门信息及岗位信息
     */
    @SuppressWarnings({"unchecked", "StringBufferReplaceableByString"})
    public List<Map> findOtherNameByeeId(String eeId) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT eeId, dpt.dptId, pos.posId, dpt.dptName, pos.posName, dpt.dptLevelcode ");
        sql.append("FROM BdDptee de  ");
        sql.append("LEFT JOIN BdDpt dpt on de.dptId = dpt.dptId ");
        sql.append("LEFT JOIN BdPos pos on de.posId = pos.posId ");
        sql.append("WHERE de.eeId = ? AND de.dpteeRelation = 'P' ");
        return commonDao_bd_write.queryMaps(sql.toString(), new Object[]{eeId}, null);
    }

    /**
     * 查询部门负责人信息
     *
     * @param eeId 部门普通员工标识
     */
    public BdDptee findSuperByeeId(String eeId) throws Exception {
        String sql = "SELECT * FROM BdDptee WHERE dptId = (SELECT dptId FROM BdDptee WHERE eeId = ? AND dpteeRelation = 'P' LIMIT 1) AND dpteeRelation = 'L'";
        return commonDao_bd_write.queryObject(sql, new Object[]{eeId}, BdDptee.class);
    }


}