package com.niceloo.uc.service;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.dao.CoreDao;
import com.niceloo.core.dao.DoResult;
import com.niceloo.uc.model.UcDatapolicy;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.db.Transactional;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;

import java.util.*;


/**
 * 资源策略-业务类
 *
 * <AUTHOR>
 * @Date 2019-11-18 15:26:54
 */
@Service
public class UcDatapolicyService {

    @Autowired
    private CoreDao commonDao;


    /**
     * 资源策略-添加
     *
     * @param ucDatapolicy 资源策略模型
     * @return
     * @throws DBException
     */
    public DoResult save(UcDatapolicy ucDatapolicy) throws Exception {
        checkCode(ucDatapolicy);
        ucDatapolicy.setDatapolicyId(commonDao.genSerial("UcDatapolicy", "datapolicyId", "DATAPOLICY", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
        return commonDao.save(ucDatapolicy);
    }

    private void checkCode(UcDatapolicy ucDatapolicy) throws DBException, ApplicationException {
        String datapolicyCode = ucDatapolicy.getDatapolicyCode();
        if (StringUtils.isEmpty(datapolicyCode)) {
        	throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "code不能为空", null);
        }
        Map params = new LinkedHashMap();
        String sql = "select count(*) from UcDatapolicy where datapolicyCode = :datapolicyCode ";
        if (ucDatapolicy.getDatapolicyId() != null) {
            sql += " and datapolicyId = :datapolicyId";
        }
        int count = commonDao.queryInt(sql, params);
        if (count > 0) {
        	throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "code已存在！", null);
        }
    }

    /**
     * 资源策略-删除
     *
     * @param ucDatapolicy 资源策略模型
     * @return
     * @throws DBException
     */
    public void delete(UcDatapolicy ucDatapolicy) throws DBException {
        commonDao.delete(ucDatapolicy);
    }

    /**
     * 资源策略-修改
     *
     * @param ucDatapolicy 资源策略模型
     * @throws DBException
     * @throws ApplicationException 
     */
    public void update(UcDatapolicy ucDatapolicy) throws DBException, ApplicationException {
        checkCode(ucDatapolicy);
        commonDao.update(ucDatapolicy);
    }

    /**
     * 资源策略-详情
     *
     * @param datapolicyId 主键
     * @return
     * @throws DBException
     */
    public UcDatapolicy findById(String datapolicyId) throws DBException {
        Map param = MapUtils.toMap(new Object[][]{{"datapolicyId", datapolicyId},});
        String sql = "SELECT * FROM UcDatapolicy WHERE datapolicyId=:datapolicyId";
        return commonDao.queryObject(sql, param, UcDatapolicy.class);
    }

    /**
     * 分页数据
     *
     * @return
     * @throws Exception
     */
    public List<UcDatapolicy> queryList(String datapolicyName, String datapolicyGroup)
            throws Exception {
        Map params = new HashMap();
        String sql = "select * from UcDatapolicy where 1=1 ";
        if (!StringUtils.isEmpty(datapolicyName)) {
            sql += " and datapolicyName like :datapolicyName ";
            params.put("datapolicyName", "%" + datapolicyName + "%");
        }
        if (!StringUtils.isEmpty(datapolicyGroup)) {
            sql += " and datapolicyGroup like :datapolicyGroup ";
            params.put("datapolicyGroup", "%" + datapolicyGroup + "%");
        }
        sql += " order by datapolicygroup, datapolicySeq,datapolicyName";

        List<UcDatapolicy> ucDatapolicies = commonDao.queryObjects(sql, params, UcDatapolicy.class);
        return ucDatapolicies;
    }

    /**
     * 资源策略还原
     */
    @Transactional
    public void rollback(List list) throws DBException {
        String sql = "delete from  UcDatapolicy";
        commonDao.execute(sql, new Object[]{});
        List datapolicyList = new ArrayList();
        if (list != null && list.size() > 0) {
            for (Object o : list) {
                UcDatapolicy ucDatapolicy = new UcDatapolicy();
                Map map = (Map) o;
                BeanUtils.setBean(ucDatapolicy, map);
                datapolicyList.add(ucDatapolicy);
            }
        }
        commonDao.save(datapolicyList);
    }

}