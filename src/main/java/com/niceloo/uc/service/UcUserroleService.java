package com.niceloo.uc.service;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.dao.CoreDao;
import com.niceloo.core.dao.DoResult;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.uc.model.UcRole;
import com.niceloo.uc.model.UcRolemenu;
import com.niceloo.uc.model.UcUserrole;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.db.SQLUtils;
import org.nobject.common.db.Transactional;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;

import java.util.*;


/**
 * 用户角色关系-业务类
 *
 * <AUTHOR>
 * @Date 2019-12-05 10:15:56
 */
@Service
public class UcUserroleService extends CoreBaseService {

	@Autowired
	private CoreDao commonDao;

	@Autowired
	private UcRoleService ucRoleService;

	/**
	 * 用户角色关系-添加
	 *
	 * @param ucUserrole 用户角色关系模型
	 * @return
	 * @throws DBException
	 */
	public DoResult save(UcUserrole ucUserrole) throws Exception {
		ucUserrole.setUserroleId(commonDao.genSerial("UcUserrole", "userroleId", "USERROLE", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
		return commonDao.save(ucUserrole);
	}

	/**
	 * 删除用户某品牌下的角色
	 *
	 * @param userId
	 */
	public void deleteRolesByUser(String userId, String brandId) throws DBException {
		commonDao.execute("delete from UcUserrole where userId = ? and brandId = ?", new Object[]{userId, brandId});
	}

	/**
	 * 删除角色id，userid删除角色
	 *
	 * @param roleIds
	 */
	public void deleteByRoleIds(String userId, Set<String> roleIds) throws DBException {
		if (roleIds != null && roleIds.size() > 0) {
			commonDao.execute("delete from UcUserrole where userId = ? and roleId in (" + SQLUtils.getIn(roleIds) + ")", new Object[]{userId});
		}
	}

	/**
	 * 根据角色id删除用户关系
	 */
	public void deleteByRoleId(String roleId) throws DBException {
		DoResult execute = commonDao.execute("delete from UcUserrole where roleId = ?", new Object[]{roleId});
	}

	/**
	 * 给用户设置角色
	 *
	 * @param userId
	 * @param roleIds
	 * @return 返回设置完成后的角色id集合
	 */
	@Transactional
	public String[] serUserRoles(String currUserId, String userId, List<String> roleIds, String brandId) throws DBException {

		List<UcRole> manageRolesByUserId;
		if (isSuperAdmin(currUserId)) {
			manageRolesByUserId = ucRoleService.queryRoleListByName(null, brandId);
		} else {
			manageRolesByUserId = ucRoleService.queryRoleListByUser(currUserId, brandId, null);
		}

		Map<String, String> statusmap = new HashMap<>();

		Set<String> manageIds = new HashSet<>();
		for (UcRole role : manageRolesByUserId) {
			manageIds.add(role.getRoleId());
			statusmap.put(role.getRoleId(), role.getRoleAdminstatus());
		}

		String[] roleIdsByUserId = queryRoleIdsByUserId(userId, brandId);
		Set<String> deleteList = new HashSet<>();

		for (String roleId : roleIdsByUserId) {
			if (manageIds.contains(roleId)) {
				deleteList.add(roleId);
			}
		}

		this.deleteByRoleIds(userId, deleteList);

		if (roleIds != null || roleIds.size() >= 0) {
			List relations = new ArrayList();
			for (String roleId : roleIds) {
				UcUserrole r = new UcUserrole();
				r.setUserId(userId);
				r.setUserroleId(genId());
				r.setRoleId(roleId);
				r.setBrandId(brandId);
				String s = statusmap.get(roleId);
				if (StringUtils.isEmpty(s)) {
					r.setRoleAdminstatus("N");
				} else {
					r.setRoleAdminstatus(s);
				}
				relations.add(r);
			}
			if (relations.size() > 0) {
				this.commonDao.save(relations);
			}
		}
		String sql = "select roleId from UcUserrole where userId =?";
		String[] strings = commonDao.getWriteDao().queryStrings(sql, new Object[]{userId});
		return strings;
	}

	/**
	 * 是否是超级管理员
	 *
	 * @param userId
	 * @return
	 * @throws DBException
	 */
	public boolean isSuperAdmin(String userId) throws DBException {
		String sql = "select count(*) from UcUserrole where userId = ? and roleAdminstatus = 'Y'";
		Integer integer = commonDao.queryInt(sql, new Object[]{userId});
		if (integer != null && integer > 0) {
			return true;
		}
		return false;
	}


	/**
	 * 获取用户的角色，剔除超管，并按品牌筛选，因为超管没有品牌
	 *
	 * @param userId
	 * @param brandId
	 * @return
	 * @throws DBException
	 */
	public List<UcRole> findUserRoles(String userId, String brandId) throws DBException {
		List<UcRole> ucRoles = this.findAllUserRoles(userId);
		if (ucRoles != null && ucRoles.size() > 0) {
			Iterator<UcRole> iterator = ucRoles.iterator();
			while (iterator.hasNext()) {
				UcRole next = iterator.next();
				if ("Y".equals(next.getRoleAdminstatus())) {
					continue;
				}
				if (!brandId.equals(next.getBrandId())) {
					iterator.remove();
				}
			}
		}
		return ucRoles;
	}

	/**
	 * 获取用户的所有角色
	 *
	 * @param userId
	 * @return
	 * @throws DBException
	 */
	public List<UcRole> findAllUserRoles(String userId) throws DBException {
		String sql = "select * from UcRole where roleId in (select roleId from UcUserrole where userId = ?)";
		List<UcRole> ucRoles = commonDao.queryObjects(sql, new Object[]{userId}, UcRole.class);
		return ucRoles;
	}

	/**
	 * @param userId
	 * @return
	 */
	public Boolean checkUserRoleIpstatus(String userId) throws DBException {
		String sql = "select count(*) from UcRole where roleId in (select roleId from UcUserrole where userId = ?) " +
				" and roleIpstatus = 'Y'";
		int count = commonDao.queryInt(sql, new Object[]{userId});
		return count > 0;
	}

	public Boolean checkUserRoleIpguardstatus(String userId) throws DBException {
		String sql = "select count(*) from UcRole where roleId in (select roleId from UcUserrole where userId = ?) " +
				" and roleIpguardstatus = 'Y'";
		int count = commonDao.queryInt(sql, new Object[]{userId});
		return count > 0;
	}

	/**
	 * 此方法不考虑超级管理员
	 *
	 * @param userId
	 * @param brandId
	 * @return
	 * @throws DBException
	 */
	public Map getMenusByUser(String userId, String brandId) throws DBException {

		String sql = "select * from UcRolemenu where roleId in " +
				"(SELECT roleId FROM UcUserrole WHERE userId= :userId and brandId= :brandId)";
		Map map1 = MapUtils.toMap(new Object[][]{
				{"userId", userId},
				{"brandId", brandId}
		});
		List<UcRolemenu> ucRolemenus = commonDao.queryObjects(sql, map1, UcRolemenu.class);
		// 合并菜单
		Map<String, Set> map = new LinkedHashMap<>();
		for (UcRolemenu ucRolemenu : ucRolemenus) {
			String menuId = ucRolemenu.getMenuId();
			Set set = map.get(menuId);
			if (set == null) {
				set = new LinkedHashSet();
				map.put(menuId, set);
			}
			String roleDatapolicy = ucRolemenu.getRoleDatapolicy();
			if (!StringUtils.isEmpty(roleDatapolicy)) {
				String[] split = roleDatapolicy.split(",");
				for (String s : split) {
					set.add(s);
				}
			}
		}
		return map;
	}


	/**
	 * 获取menuCode
	 *
	 * @param userId
	 * @param menuCode
	 * @param onlyF
	 * @return
	 * @throws DBException
	 * @throws ApplicationException 
	 */
	public Map getMenuCodesByUser(String userId, String brandId, String menuCode, boolean onlyF) throws DBException, ApplicationException {

		Map params = new HashMap();
		String sql;
		boolean superAdmin = isSuperAdmin(userId);
		if (superAdmin) {
			sql = "select m.menuCode menuCode , m.menuDatapolicy roleDatapolicy , m.menuExtcode menuExtcode from UcMenu m where 1=1 ";
		} else {
			sql = "select m.menuCode menuCode ,r.roleDatapolicy roleDatapolicy , m.menuExtcode menuExtcode from UcMenu m join UcRolemenu r on r.menuId = m.menuId where " +
					" r.roleId in (select roleId from UcUserrole where userId= :userId and brandId = :brandId)";
			params.put("brandId", brandId);
		}

		params.put("userId", userId);
		if (!StringUtils.isEmpty(menuCode) && !"0".equalsIgnoreCase(menuCode)) {
			String ss = "select menuLevelcode from UcMenu where menuCode = ?";
			String menuLevelcodeP = commonDao.queryString(ss, new Object[]{menuCode});
			if (StringUtils.isEmpty(menuLevelcodeP)) {
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "menuCode" + menuCode + "不存在！", null);
			}
			sql += " and m.menuLevelcode like :levelCode ";
			params.put("levelCode", "%" + menuLevelcodeP + "%");
		}
		sql += " and m.menuDisablestatus = 'E' and m.menuType in ('funcGroup','func') ";
		sql += " and m.menuCode <> '' and m.menuCode is not null ";
		List<Map> maps = commonDao.queryMaps(sql, params, new Object[]{});
		// 合并菜单
		Map<String, Set> map = new LinkedHashMap<>();
		for (Map menuMap : maps) {
			String code = (String) menuMap.get("menuCode");
			Set set = map.get(code);
			if (set == null) {
				set = new LinkedHashSet();
				map.put(code, set);
			}
			// 增加菜单的依赖code
			String extCode = (String) menuMap.get("menuExtcode");
			if(extCode !=null && extCode.length()>0){
				String[] split = extCode.split(",");
				for (String s : split) {
					if(s == null){
						continue;
					}
					s = s.trim();
					if(map.get(s) == null){
						map.put(s,new HashSet());
					}
				}
			}
			String roleDatapolicy = (String) menuMap.get("roleDatapolicy");
			if (!StringUtils.isEmpty(roleDatapolicy)) {
				String[] split = roleDatapolicy.split(",");
				for (String s : split) {
					if(superAdmin && ("enableProject".equals(s) || "enableUsersource".equals(s))){
						continue;
					}
					set.add(s);
				}
			}
		}
		return map;
	}

	/**
	 * 获取menuCode
	 *
	 * @param userId
	 * @param menuCode
	 * @return
	 * @throws DBException
	 */
	public List<String> getDatapolicysByCode(String userId, String brandId, String menuCode) throws DBException {

		Map params = new HashMap();
		String sql;
		if (isSuperAdmin(userId)) {
			sql = "select m.menuDatapolicy roleDatapolicy from UcMenu m where 1=1 ";
		} else {
			sql = "select r.roleDatapolicy roleDatapolicy from UcMenu m join UcRolemenu r on r.menuId = m.menuId where " +
					" r.roleId in (select roleId from UcUserrole where userId= :userId and brandId = :brandId)";
			params.put("brandId", brandId);
		}

		params.put("userId", userId);

		sql += " and m.menuCode = :menuCode";
		params.put("menuCode", menuCode);


		String[] strings = commonDao.queryStrings(sql, params);
		List<String> list = new ArrayList<>();
		if (strings != null && strings.length > 0) {
			for (String string : strings) {
				if (!StringUtils.isEmpty(string)) {
					String[] split = string.split(",");
					for (String s : split) {
						if (!StringUtils.isEmpty(s)) {
							list.add(s);
						}
					}
				}
			}
		}
		return list;

	}

	/**
	 * 获取用户自身的角色id列表
	 *
	 * @param userId
	 * @return
	 * @throws DBException
	 */
	public String[] queryRoleIdsByUserId(String userId, String brandId) throws DBException {

		String sql = "select roleId from UcUserrole where  userId = ? and brandId = ?";
		Object[] params;
		params = new Object[]{userId, brandId};

		String[] roleIds = commonDao.queryStrings(sql, params);
		return roleIds;
	}


	private String genId() throws DBException {
		return commonDao.genSerial("UcUserrole", "userroleId", "USERROLE", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq);
	}


}