package com.niceloo.uc.service;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.QueryResult;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.exception.DBException;

import java.util.List;

/**
 * 数据同步业务
 *
 * zzz
 * 2020-12-24
 */
@Service
public class UcSynchronyService {
    @SuppressWarnings("unused")
    @Autowired
    private CommonDao commonDao_bd_write;

    /**
     * 根据时间获取`分校`数据列表
     */
    public QueryResult getSchoolModifyDataList(String date) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT schoolId,schoolName,schoolAreacode,schoolAvlstatus,schoolDelstatus,schoolCreateddate,schoolModifieddate FROM BdSchool where schoolModifieddate >= '").append(date).append("'");
        return getQueryResult(sql);
    }

    /**
     * 根据时间获取`员工`数据列表
     */
    public QueryResult getEeModifyDataList(String date) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT eeId,userId,eeNo,schoolId,userName,eeWorkstatus,eeAvlstatus,eeDelstatus,eeCreateddate,eeModifieddate FROM BdEe where eeModifieddate >= '").append(date).append("'");
        return getQueryResult(sql);
    }

    /**
     * 根据sql获取数据列表，放入qr
     */
    private QueryResult getQueryResult(StringBuilder sql) throws DBException {
        List list = commonDao_bd_write.queryMaps(sql.toString(), null, null);
        QueryResult qr = new QueryResult();
        qr.setCount(list.size());
        qr.setData(list);
        return qr;
    }
}
