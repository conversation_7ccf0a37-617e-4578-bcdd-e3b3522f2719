package com.niceloo.uc.service;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.bean.CoreLevelcode;
import com.niceloo.core.dao.CoreDao;
import com.niceloo.core.dao.DoResult;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.core.mvc.MVCUtils;
import com.niceloo.core.service.CoreLevelcodeService;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.uc.common.UcConfig;
import com.niceloo.uc.model.UcMenu;
import com.niceloo.uc.model.UcRole;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.db.SQLUtils;
import org.nobject.common.db.Transactional;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;

import java.util.*;

import static org.nobject.common.lang.StringUtils.isEmpty;


/**
 * 菜单-业务类
 *
 * <AUTHOR>
 * @Date 2019-11-18 10:35:12
 */
@Service
public class UcMenuService extends CoreBaseService {

    @Autowired
    private CoreDao coreDao;
    @Autowired
    private UcRolemenuService ucRolemenuService;
    @Autowired
    private UcDatapolicyService ucDatapolicyService;
    @Autowired
    private UcUserroleService ucUserroleService;

    private CoreLevelcodeService coreLevelcodeService;


    public int LEVELCODE_LENGTH = 10;

    public final static String MENU_TYPE_MODEL = "model";
    public final static String MENU_TYPE_MENU = "menu";
    public final static String MENU_TYPE_FUNC = "func";

    /**
     * 菜单-添加
     *
     * @param ucMenu 菜单模型
     * @param pid
     * @return
     * @throws DBException
     */
//    public DoResult save(UcMenu ucMenu, String pid) throws Exception {
//        String levelcode;
//        if (!StringUtils.isEmpty(pid)) {
//            UcMenu byId = this.findById(pid);
//            if (byId == null) {
//                throw new DBException("没有找到对应的父节点");
//            }
//            String menuLevelcode = byId.getMenuLevelcode();
//            String sql = "select max(menuLevelCode) from UcMenu where length(menuLevelCode) = " + (menuLevelcode.length() + 10)
//                    + " and left(menuLevelCode," + menuLevelcode.length() + ") = ? ";
//            String s = coreDao.queryString(sql, new Object[]{menuLevelcode});
//            if (StringUtils.isEmpty(s)) {
//                levelcode = menuLevelcode + "0000000001";
//            } else {
//                s = s.substring(menuLevelcode.length());
//                int l = Integer.parseInt(s);
//                String s1 = StringUtils.fillEmptyWithStr(l + 1, 10, "0");
//                levelcode = menuLevelcode + s1;
//            }
//        } else {
//            // pid为0，为跟路径上的菜单，查询length=10的最大code
//            String sql = "select max(menuLevelCode) from UcMenu where length(menuLevelCode) = 10 ";
//            String s = coreDao.queryString(sql, new Object[]{});
//            if (StringUtils.isEmpty(s)) {
//                levelcode = "0000000001";
//            } else {
//                int i = Integer.parseInt(s);
//                String s1 = StringUtils.fillEmptyWithStr(i + 1, 10, "0");
//                levelcode = s1;
//            }
//        }
//        ucMenu.setMenuLevelcode(levelcode);
//		ucMenu.setMenuId(coreDao.genSerial("UcMenu", "menuId", "MENU", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
//		this.checkMenu(ucMenu);
//		this.handleIcon(ucMenu);
//		ucMenu.setMenuCreatedate(DateUtils.getNowDString());
//		ucMenu.setMenuModifieddate(DateUtils.getNowDString());
//		return coreDao.save(ucMenu);
//	}
    public DoResult save(UcMenu ucMenu, String pid) throws Exception {
        //pid 不能为空 (标识系统)
        String levelcode;
        UcMenu byId = this.findById(pid);
        if (byId == null) {
        	throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "没有找到对应的父节点", null);
        }
        String menuLevelcode = byId.getMenuLevelcode();
        String sql = "select max(menuLevelCode) from UcMenu where length(menuLevelCode) = " + (menuLevelcode.length() + 10)
                + " and left(menuLevelCode," + menuLevelcode.length() + ") = ? ";
        String s = coreDao.queryString(sql, new Object[]{menuLevelcode});
        if (StringUtils.isEmpty(s)) {
            levelcode = menuLevelcode + "0000000001";
        } else {
            s = s.substring(menuLevelcode.length());
            int l = Integer.parseInt(s);
            String s1 = StringUtils.fillEmptyWithStr(l + 1, 10, "0");
            levelcode = menuLevelcode + s1;
        }
        ucMenu.setMenuLevelcode(levelcode);
        ucMenu.setMenuId(coreDao.genSerial("UcMenu", "menuId", "MENU", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
        this.checkMenu(ucMenu);
        this.handleIcon(ucMenu);
        ucMenu.setMenuCreatedate(DateUtils.getNowDString());
        ucMenu.setMenuModifieddate(DateUtils.getNowDString());
        return coreDao.save(ucMenu);
    }


    /**
     * icon中图标临时转正式
     *
     * @param ucMenu
     * @throws Exception
     */
    private void handleIcon(UcMenu ucMenu) throws Exception {
        String menuIcon = ucMenu.getMenuIcon();
        if (!StringUtils.isEmpty(menuIcon) && menuIcon.startsWith("file:")) {
            String url = menuIcon.replace("file:", "");
            Map params = new LinkedHashMap();
            params.put("filePaths", url);
            Object request = CoreUtils.request(MVCUtils.getProperty("fs.url"), "api/file/confirm", params, "usercenter", "fileservice");
        }
    }

    /**
     * 检查code唯一性
     *
     * @param ucMenu
     * @throws DBException
     * @throws ApplicationException 
     */
    private void checkMenu(UcMenu ucMenu) throws DBException, ApplicationException {
        if("page".equalsIgnoreCase(ucMenu.getMenuType())){
            return;
        }
        if (StringUtils.isEmpty(ucMenu.getMenuCode())) {
            // 如果是功能，code必须不为空
            if (MENU_TYPE_FUNC.equalsIgnoreCase(ucMenu.getMenuType())) {
            	throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "功能菜单code不能为空！", null);
            }
//			else {
//				// 非功能，直接使用id即可
//				ucMenu.setMenuCode(ucMenu.getMenuId());
//			}
        } else {
            Object[] params;
            if (ucMenu.getMenuCode() != null && ucMenu.getMenuCode().length() > 0) {
                String sql = "select count(*) from UcMenu where menuType <> 'page' and menuCode = ? ";
                if (!StringUtils.isEmpty(ucMenu.getMenuId())) {
                    sql += " and menuId <> ?";
                    params = new Object[]{ucMenu.getMenuCode(), ucMenu.getMenuId()};
                } else {
                    params = new Object[]{ucMenu.getMenuCode()};
                }
                Integer integer = coreDao.queryInt(sql, params);
                if (integer != null && integer > 0) {
                	throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "菜单的code必须唯一！", null);
                }
            }
//            String menuExtcode = ucMenu.getMenuExtcode();
//            if(menuExtcode !=null && menuExtcode.length()>0){
//                String[] split = menuExtcode.split(",");
//                Set<String> set = new HashSet();
//                for (String s : split) {
//                    if(s !=null && s.length()>0 && s.trim().length()>0){
//                        set.add(s);
//                    }
//                }
//                String sql = "select menuCode from UcMenu where menuCode in ("+ SQLUtils.getIn(set) +") ";
//                String[] strings = coreDao.queryStrings(sql, new Object[]{});
//                List list = ListUtils.toList(strings);
//                for (String o : set) {
//                    if(!list.contains(o)){
//                        throw new DBException("不存在的权限code："+o);
//                    }
//                }
//
//            }

        }
    }

    /**
     * 菜单-删除
     *
     * @param ucMenu 菜单模型
     * @return
     * @throws DBException
     */
    @Transactional
    public void delete(UcMenu ucMenu) throws Exception {
        List<UcMenu> ucMenus = this.queryChildren(ucMenu.getMenuId(), null);
        if (ucMenus != null && ucMenus.size() > 0) {
        	throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "当前菜单有下级，不允许删除", null);
        }
        coreDao.delete(ucMenu);
        String menuId = ucMenu.getMenuId();
        ucRolemenuService.deleteByMenuId(menuId);
    }

    /**
     * 菜单-修改
     *
     * @param ucMenu 菜单模型
     * @throws DBException
     */
    public void update(UcMenu ucMenu) throws Exception {
        this.checkMenu(ucMenu);
        this.handleIcon(ucMenu);
        ucMenu.setMenuModifieddate(DateUtils.getNowDString());
        coreDao.update(ucMenu);
    }

    /**
     * 菜单-详情
     *
     * @param menuId 主键
     * @return
     * @throws DBException
     */
    public UcMenu findById(String menuId) throws DBException {
        Map param = MapUtils.toMap(new Object[][]{{"menuId", menuId},});
        String sql = "SELECT * FROM UcMenu WHERE menuId=:menuId";
        return coreDao.queryObject(sql, param, UcMenu.class);
    }

    /**
     * 查询菜单
     *
     * @param menuCode
     * @return
     * @throws DBException
     */
    public UcMenu findByCode(String menuCode) throws DBException {
        if (StringUtils.isEmpty(menuCode)) {
            return null;
        }
        Map param = MapUtils.toMap(new Object[][]{{"menuCode", menuCode},});
        String sql = "SELECT * FROM UcMenu WHERE menuCode=:menuCode";
        return coreDao.queryObject(sql, param, UcMenu.class);
    }

    /**
     * 查询菜单树
     *
     * @param containsFunc
     * @return
     * @throws DBException
     */
    public List queryMenuTree(boolean containsFunc, boolean containsDisabled, String sysId, boolean containsFuncgroup) throws Exception {
        return this.queryMenuTree(containsFunc, containsDisabled, null, sysId, containsFuncgroup);
    }

    /**
     * 根据用户查询菜单树，此方法不考虑超级管理员
     *
     * @param userId
     * @param brandId
     * @param containsF
     * @param containsD
     * @return
     * @throws DBException
     */
    public List queryMenuTreeByUser(String userId, String brandId, boolean containsF, boolean containsD, String sysId, boolean containsFG) throws Exception {
        Map menusByUser = ucUserroleService.getMenusByUser(userId, brandId);
        if (menusByUser != null && menusByUser.size() > 0) {
            List<Map> menuList = queryMenuTree(containsF, containsD, menusByUser.keySet(), sysId, containsFG);
            setDatapolicy(menuList, menusByUser);
            return menuList;
        } else {
            return new ArrayList();
        }
    }

    /**
     * 移除没有权限的菜单和数据范围
     *
     * @param menuList
     * @param menusByUser
     */
    private void setDatapolicy(List<Map> menuList, Map menusByUser) {
        Iterator<Map> iterator = menuList.iterator();
        while (iterator.hasNext()) {
            Map map = iterator.next();
            Object menuId = map.get("menuId");
            if (menusByUser.get(menuId) == null) {
                iterator.remove();
                continue;
            }
            String[] menuDatapolicy = (String[]) map.get("menuDatapolicy");
            if (menuDatapolicy != null && menuDatapolicy.length > 0) {
                map.put("menuDatapolicy", menusByUser.get(menuId));
            }
            Object children = map.get("children");
            if (children != null) {
                List<Map> ccc = (List) children;
                setDatapolicy(ccc, menusByUser);
            }

        }
    }


    /**
     * 查询菜单树
     *
     * @param containsFunc
     * @return
     * @throws DBException
     */

//    public List queryMenuTree(boolean containsFunc,boolean containsDisabled,Collection<String> menuIds) throws DBException {
//
//        String sql = "select menuId,menuName,menuType,menuCode,menuParam,menuUrl,menuSeq,menuDisablestatus,menuIcon,menuLevelcode,menuDatapolicy from UcMenu where 1=1 ";
//        if (!containsFunc) {
//            sql += " and menuType != 'func' ";
//
//        }
//        if(!containsDisabled){
//            sql += " and menuDisablestatus = 'E'";
//        }
//        if(menuIds!=null &&menuIds.size()>0){
//            sql += " and menuId in ("+ SQLUtils.getIn(menuIds) +")";
//        }
//        sql += " order by LENGTH(menuLevelcode), menuSeq,menuLevelcode ";
//
//        List<UcMenu> data = coreDao.queryObjects(sql, null, null, new HashMap(), UcMenu.class);
//
//        Map<String, Map> nodes = new HashMap();
//        List childs = new LinkedList();
//        for (UcMenu ucMenu : data) {
//            Map mDict = BeanUtils.bean2Map(ucMenu);
//            if(!StringUtils.isEmpty(ucMenu.getMenuDatapolicy())){
//                String menuDatapolicy = ucMenu.getMenuDatapolicy();
//                mDict.put("menuDatapolicy",menuDatapolicy.split(","));
//            }
//            String pLevelcode = ucMenu.getMenuLevelcode().substring(0, ucMenu.getMenuLevelcode().length() - LEVELCODE_LENGTH);
//            nodes.put(ucMenu.getMenuLevelcode(), mDict);
//            if (isEmpty(pLevelcode)) {
//                childs.add(mDict);
//            } else {
//                Map pDict = nodes.get(pLevelcode);
//                if (pDict == null) {
//                    continue;
//                }
//                if (!pDict.containsKey("children")) {
//                    pDict.put("children", new LinkedList());
//                }
//                List pChilds = (List) pDict.get("children");
//                pChilds.add(mDict);
//                pDict.put("children", pChilds);
//            }
//        }
//
//        return childs;
//    }
    public List queryMenuTree(boolean containsFunc, boolean containsDisabled, Collection<String> menuIds, String sysId, boolean containsFG) throws Exception {

        String sql = "select menuId,menuName,menuExtcode,menuType,menuCode,menuParam,menuUrl,menuSeq,menuDisablestatus,menuIcon,menuLevelcode,menuDatapolicy from UcMenu where 1=1 ";
        if (!isEmpty(sysId)) {
            UcMenu byId = findById(UcConfig.Sys.sysId.get(sysId));
            if (byId == null) {
            	throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "父节点不存在", null);
            }
            sql += " and menuLevelcode like '" + byId.getMenuLevelcode() + "%' and length(menuLevelcode)>10";
        }
        if (!containsFunc) {
            sql += " and menuType != 'func' ";

        }
        if (!containsFG) {
            sql += " and menuType != 'funcGroup' ";

        }
        if (!containsDisabled) {
            sql += " and menuDisablestatus = 'E'";
        }
        if (menuIds != null && menuIds.size() > 0) {
            sql += " and menuId in (" + SQLUtils.getIn(menuIds) + ")";
        }
        sql += " order by LENGTH(menuLevelcode), menuSeq,menuLevelcode ";
        List<UcMenu> data = coreDao.queryObjects(sql, null, null, new HashMap(), UcMenu.class);

        Map<String, Map> nodes = new HashMap();
        List childs = new LinkedList();
        for (UcMenu ucMenu : data) {
            Map mDict = BeanUtils.bean2Map(ucMenu);
            if (!StringUtils.isEmpty(ucMenu.getMenuDatapolicy())) {
                String menuDatapolicy = ucMenu.getMenuDatapolicy();
                mDict.put("menuDatapolicy", menuDatapolicy.split(","));
            }
            String pLevelcode = ucMenu.getMenuLevelcode().substring(0, ucMenu.getMenuLevelcode().length() - LEVELCODE_LENGTH);
            nodes.put(ucMenu.getMenuLevelcode(), mDict);

            if (isEmpty(sysId) && isEmpty(pLevelcode)) {
                childs.add(mDict);
            } else if (!isEmpty(sysId) && pLevelcode.length() == 10) {
                childs.add(mDict);
            } else {
                Map pDict = nodes.get(pLevelcode);
                if (pDict == null) {
                    continue;
                }
                if (!pDict.containsKey("children")) {
                    pDict.put("children", new LinkedList());
                }
                List pChilds = (List) pDict.get("children");
                pChilds.add(mDict);
                pDict.put("children", pChilds);
            }
        }
        return childs;
    }

    /**
     * 根据父节点查询所有子节点，只有一级
     * 不能为空，标识系统
     *
     * @param pid
     * @param menuName
     * @return
     * @throws ApplicationException 
     */
    public List<UcMenu> queryChildren(String pid, String menuName) throws DBException, ApplicationException {
        String sql;
        Object[] params;
//        if ("0".equals(pid)) {
//            sql = "select * from UcMenu where length(menuLevelcode) = 10 ";
//            params = new Object[0];
//        } else {
        UcMenu byId = this.findById(pid);
        if (byId == null) {
        	throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "父节点不存在", null);
        }
        String menuLevelcode = byId.getMenuLevelcode();
        sql = "select * from UcMenu where length(menuLevelcode) = " + (menuLevelcode.length() + 10)
                + " and left(menuLevelcode, " + menuLevelcode.length() + ") = ? ";
        params = new Object[]{menuLevelcode};
        // }
        if (!StringUtils.isEmpty(menuName)) {
            sql = sql + " and menuName like ? ";
            Object[] p = new Object[params.length + 1];
            System.arraycopy(params, 0, p, 0, params.length);
            p[params.length] = "%" + menuName + "%";
            params = p;
        }
        sql += " order by menuSeq ";

//		sql = "select * from UcMenu";
//		params = new Object[0];

        List<UcMenu> ucMenus = coreDao.queryObjects(sql, params, UcMenu.class);
        return ucMenus;
    }


    /**
     * 根据父节点查询子节点
     *
     * @param pid
     */
    public List queryBackup(String pid) throws DBException {
        Map map = MapUtils.toMap(new Object[][]{{"menuId", pid}});
        String sql = null;
        if ("0".equals(pid)) {
            sql = "SELECT * from UcMenu";
        } else {
            UcMenu byId = findById(pid);
            if (byId == null) {
                return null;
            }
            String menuLevelcode = byId.getMenuLevelcode();
            sql = "select * from UcMenu WHERE menuLevelcode like '" + menuLevelcode + "%'";
        }
        List<UcMenu> ucMenus = coreDao.queryObjects(sql, map, UcMenu.class);
        return ucMenus;
    }

    /**
     * 执行还原
     */
    @Transactional
    public void executeRollback(Map result) throws DBException {
        //菜单
        Map map = (Map) result.get("menu");
        List add = (List) map.get("add");
        List del = (List) map.get("del");
        List update = (List) map.get("update");
        if (del != null && del.size() > 0) {
            coreDao.delete(del);
            for (Object o : del) {
                ucRolemenuService.deleteByMenuId(((UcMenu) o).getMenuId());
            }
        }
        if (add != null && add.size() > 0) {
            coreDao.save(add);
        }
        if (update != null && update.size() > 0) {
            coreDao.update(update);
        }

        //资源策略
        List datapolicy = (List) result.get("datapolicy");
        if (datapolicy != null && datapolicy.size() > 0) {
            ucDatapolicyService.rollback(datapolicy);
        }

    }

    public List<Map> queryRolesByMenu(String menuId, String roleName) throws DBException {
        Map params = new HashMap();
        String sql = "select roleId,roleName,roleMemo,roleCreatedate,roleCreaterid,roleCreaterName from UcRole where roleId in (" +
                "select roleId from UcRolemenu where menuId = :menuId order by roleSeq" +
                ")";
        params.put("menuId", menuId);
        if (!StringUtils.isEmpty(roleName)) {
            sql += " and roleName like :roleName";
            params.put("roleName", "%" + roleName + "%");
        }
        List<Map> ucRoles = coreDao.queryMaps(sql, params, UcRole.class);
        return ucRoles;
    }

    /**
     * 移动菜单
     *
     * @param sourceId 要移动的
     * @param destId   移动到位置
     * @param mode     移动到他前面还是后面 P A
     */
    @Transactional
    public void move(String sourceId, String destId, String mode) throws Exception {
        initLevelCodeService();
        this.coreLevelcodeService.move(coreLevelcode, destId, sourceId, mode);
    }

    private CoreLevelcode coreLevelcode;

    private void initLevelCodeService() {
        if (this.coreLevelcodeService == null) {
            this.coreLevelcodeService = new CoreLevelcodeService();
            this.coreLevelcodeService.setCommonDao(coreDao.getWriteDao());
            this.coreLevelcode = new CoreLevelcode(UcMenu.class);
        }
    }
}