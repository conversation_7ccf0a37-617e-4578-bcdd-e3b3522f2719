package com.niceloo.uc.service;

import static org.nobject.common.lang.StringUtils.isEmpty;
import static org.nobject.common.lang.StringUtils.toString0;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.db.SQLUtils;
import org.nobject.common.db.Transactional;
import org.nobject.common.db.member.SqlWE;
import org.nobject.common.encrypt.DesUtils;
import org.nobject.common.encrypt.HexUtils;
import org.nobject.common.encrypt.MD5Utils;
import org.nobject.common.encrypt.SHA1Utils;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.exception.HandleException;
import org.nobject.common.http.HttpClient;
import org.nobject.common.js.JSONUtils;
import org.nobject.common.lang.CollectionUtils;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.ListUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.ObjectUtils;
import org.nobject.common.lang.StringUtils;
import org.nobject.common.log.Logger;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.bean.CoreConfig.Sys;
import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.bean.CoreConst.ErrorCode;
import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.lock.service.LockService;
import com.niceloo.core.mq.MQService;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.core.mvc.MVCUtils;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.core.utils.ServerSelecter;
import com.niceloo.uc.common.NickNameReplace;
import com.niceloo.uc.common.UcConfig;
import com.niceloo.uc.common.UcConfig.Crm;
import com.niceloo.uc.common.UcConfig.User;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.common.UcConst.Avlstatus;
import com.niceloo.uc.common.UcConst.Brand;
import com.niceloo.uc.common.UcConst.Code.Lockstatus;
import com.niceloo.uc.common.UcConst.Datatype;
import com.niceloo.uc.common.UcConst.Delstatus;
import com.niceloo.uc.common.UcConst.Errorcode;
import com.niceloo.uc.common.UcConst.IDcardType;
import com.niceloo.uc.common.UcConst.UkType;
import com.niceloo.uc.common.UcConst.UserFlag;
import com.niceloo.uc.common.UcConst.UserIDcardStatus;
import com.niceloo.uc.common.UcConst.UserLoginpwdtype;
import com.niceloo.uc.model.UcUser;
import com.niceloo.uc.utils.CRMRSAUtils;
import com.niceloo.uc.utils.CSharpOrJavaUtil;
import com.niceloo.uc.utils.HttpUtils;
import com.niceloo.uc.utils.UserUtils;

/**
 * 用户管理类
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class UcUserService extends CoreBaseService{

	/** commonDao_uc_write */
	@Autowired
	private CommonDao commonDao_uc_write;

	/** commonDao_bd_write */
	@Autowired
	private CommonDao commonDao_bd_write;

	/** commonDao_uc_read */
	@Autowired
	private CommonDao commonDao_uc_read;

	/** commonDao_uc_write_crm */
	@Autowired
	private CommonDao commonDao_crm_write;

	/** commonDao_crm_read */
	@Autowired
	private CommonDao commonDao_crm_read;

	@Autowired
	private MQService mqService;

	@Autowired
	private UcUserService ucUserService;

	private LockService lockService;


	private Logger logger = Logger.getLogger(UcUserService.class);

	/**
	 * getUserInstance
	 * @return
	 */
	public UcUser getUserInstance(){
		return commonDao_uc_read.getPOJOInstance(UcUser.class);
	}

	/**
	 * 邮箱是否已存在
	 * @param email 邮箱
	 */
	public boolean ifExsistAvlByEmail(String flag,String email,String brandId) throws Exception{
		Map param=MapUtils.toMap(new Object[][]{
			{"userFlag"			,flag},
			{"userEmail"		,email},
			{"brandId"			,brandId},
			{"userDelstatus"	,Delstatus.NO}
		});
		String sql="SELECT COUNT(1) FROM UcUser WHERE userFlag=:userFlag AND userEmail=:userEmail AND userEmailstatus='Y' AND userDelstatus=:userDelstatus ";
		if(UcConst.UserFlag.student.equals(flag)) {
			sql+="  AND brandId=:brandId ";
		}
		return commonDao_uc_write.queryInt(sql,param)>0;
	}

	/**
	 * 手机是否已存在
	 * @param mobile 手机
	 */
	public boolean ifExsistAvlByMobile(String flag,String mobile,String brandId) throws Exception{
		Map param=MapUtils.toMap(new Object[][]{
			{"userFlag"			,flag},
			{"userMobile"		,mobile},
			{"brandId"			,brandId},
			{"userDelstatus"	,Delstatus.NO}
		});
		String sql="SELECT COUNT(1) FROM UcUser WHERE userFlag=:userFlag AND userMobile=:userMobile AND userMobilestatus='Y' AND userDelstatus=:userDelstatus ";
		if(UcConst.UserFlag.student.equals(flag)) {
			sql+="  AND brandId=:brandId ";
		}
		return commonDao_uc_write.queryInt(sql,param)>0;
	}

	/**
	 * 用户名是否已存在
	 * @param loginname 用户名
	 */
	public boolean ifExsistAvlByLoginname(String flag,String loginname,String brandId) throws Exception{
		Map param=MapUtils.toMap(new Object[][]{
			{"userFlag"			,flag},
			{"userLoginname"	,loginname},
			{"brandId"			,brandId},
			{"userDelstatus"	,Delstatus.NO}
		});
		String sql="SELECT COUNT(1) FROM UcUser WHERE userFlag=:userFlag AND userLoginname=:userLoginname  AND userDelstatus=:userDelstatus ";
		if(UcConst.UserFlag.student.equals(flag)) {
			sql+="  AND brandId=:brandId ";
		}
		return commonDao_uc_write.queryInt(sql,param)>0;
	}

	/**
	 * 用户名是否已存在
	 * @param sourceid
	 * @param userFlag
	 * @return
	 * @throws Exception
	 */
	public boolean ifExsistAvlBySource(String sourceid,String userFlag) throws Exception{
		return commonDao_uc_write.queryInt("SELECT COUNT(1) FROM UcUser WHERE userSourceid=? AND userDelstatus=? AND userFlag = ?",new Object[]{sourceid,Delstatus.NO,userFlag})>0;
	}

	/**
	 * 邮箱是否已存在
	 * @param email 邮箱
	 * @param excludeUserId 排除的用户标识
	 */
	public boolean ifExsistAvlByEmail$ExclueUser(String flag,String email,String excludeUserId,String brandId) throws Exception{
		Map param=MapUtils.toMap(new Object[][]{
			{"userFlag"			,flag},
			{"userEmail"		,email},
			{"brandId"			,brandId},
			{"userDelstatus"	,Delstatus.NO},
			{"userId",excludeUserId}
		});
		String sql="SELECT COUNT(1) FROM UcUser WHERE userFlag=:userFlag AND userEmail=:userEmail AND userDelstatus=:userDelstatus AND userId!=:userId";
		if(UcConst.UserFlag.student.equals(flag)) {
			sql+="  AND brandId=:brandId ";
		}
		return commonDao_uc_write.queryInt(sql,param)>0;
	}

	/**
	 * 手机是否已存在
	 * @param mobile 手机
	 * @param excludeUserId 排除的用户标识
	 */
	public boolean ifExsistAvlByMobile$ExclueUser(String flag,String mobile,String excludeUserId,String brandId) throws Exception{

		Map param=MapUtils.toMap(new Object[][]{
			{"userFlag"			,flag},
			{"userMobile"		,mobile},
			{"brandId"			,brandId},
			{"userDelstatus"	,Delstatus.NO},
			{"userId",excludeUserId}
		});
		String sql="SELECT COUNT(1) FROM UcUser WHERE userFlag=:userFlag AND userMobile=:userMobile  AND userDelstatus=:userDelstatus AND userId!=:userId";
		if(UcConst.UserFlag.student.equals(flag)) {
			sql+="  AND brandId=:brandId ";
		}
		return commonDao_uc_write.queryInt(sql,param)>0;

	}

	/**
	 * 用户名是否已存在
	 * @param loginname 用户名
	 * @param excludeUserId 排除的用户标识
	 */
	public boolean ifExsistAvlByLoginname$ExclueUser(String flag,String loginname,String excludeUserId,String brandId) throws Exception{

		Map param=MapUtils.toMap(new Object[][]{
			{"userFlag"			,flag},
			{"userLoginname"	,loginname},
			{"brandId"			,brandId},
			{"userDelstatus"	,Delstatus.NO},
			{"userId",excludeUserId}
		});
		String sql="SELECT COUNT(1) FROM UcUser WHERE userFlag=:userFlag AND userLoginname=:userLoginname  AND userDelstatus=:userDelstatus AND userId!=:userId";
		if(UcConst.UserFlag.student.equals(flag)) {
			sql+="  AND brandId=:brandId ";
		}
		return commonDao_uc_write.queryInt(sql,param)>0;
	}

	/**
	 * 用户ID是否已存在
	 * @param id 用户ID
	 */
	public boolean ifExsistById(String id) throws Exception{
		return commonDao_uc_write.queryInt("SELECT COUNT(1) FROM UcUser WHERE userId=?",new Object[]{id})>0;
	}


 	/**
 	 * 添加用户#需同步事务
 	 * @param user 用户
 	 * @throws Exception 该用户已存在/该Email已被占用
 	 */
	@Transactional(rollbackFor=Exception.class,global=true)
 	public Map add(UcUser user,Map ext) throws Exception{

		String userLoginpwd = user.getUserLoginpwd();

		String sql = "SELECT ";

		//判断用户源标识是否被已存在
		if(!isEmpty(user.getUserSourceid())) {
			if(ifExsistAvlBySource(user.getUserSourceid(),user.getUserFlag())){
				throw new ApplicationException(UcConst.Errorcode.USER_SOURCEID_EXIST,"用户源标识已存在",null);
			}
		}

		//判断是否有同名用户
 		if(!isEmpty(user.getUserLoginname())&&!user.getUserLoginname().equals("匿名")){

 			//TODO 如果并发锁(Redis的锁 UserFlag+loginName)锁存在，则等待或退出

 			//从数据库中监测存在
 			if(ifExsistAvlByLoginname(user.getUserFlag(),user.getUserLoginname(),user.getBrandId())){
 				throw new ApplicationException(UcConst.Errorcode.USER_LOGIN_EXIST,"用户名已存在",null);
 			}

 			//TODO 放Redis里一个欲添加的UserFlag+loginName
 		}

		//判断有同EMAIL用户
 		if(!isEmpty(user.getUserEmail())&&!user.getUserEmail().equals("暂无")&&!user.getUserEmail().equals("x@x.x")){
 			if(ifExsistAvlByEmail(user.getUserFlag(),user.getUserEmail(),user.getBrandId())){
 			 	throw new ApplicationException(UcConst.Errorcode.USER_EMAIL_EXIST,"邮箱已占用",null);
 			}
	 	}


 			//判断有同手机用户
 		if(!isEmpty(user.getUserMobile())&&!user.getUserMobile().equals("暂无")){
 			if(ifExsistAvlByMobile(user.getUserFlag(),user.getUserMobile(),user.getBrandId())){
 				throw new ApplicationException(UcConst.Errorcode.USER_PHONE_EXIST,"手机号已注册",null);
 			}
 		}


 		user.setUserCreateddate	(DateUtils.getNowDString());
 		user.setUserModifieddate(DateUtils.getNowDString());

 		//密码转换(来自于其它系统的用户密码不转换)
 		if(isEmpty(user.getUserSourceid())){
 			if(!isEmpty(user.getUserLoginpwd())){
 	 			user.setUserLoginpwd(UserUtils.getEncryptPwd(user.getUserLoginpwd(), user.getUserLoginpwdtype()));
 	 		}
 		}


 		user.setUserIdcardstatus(UserIDcardStatus.NO);

		if(UserFlag.student.equals(user.getUserFlag()) && !StringUtils.isEmpty(user.getUserMobile())) {
			//网站注册用户信息
//			String userSourceid = ucUserService.netRegist(user, Sys.ip);
//			Map userMap = netGetUser(userSourceid);
//			UcUser resoutUser = apiAdd(userMap,user.getBrandId(),UcConst.SourceType.YOULU_WEB);

			UcUser resoutUser = apiAdd(netRegistNoEs(user.getUserMobile(),userLoginpwd),user.getBrandId(),user.getUserSourcetype(),"");
			resoutUser.setUserLoginpwd(user.getUserLoginpwd());
			resoutUser.setUserLoginpwdtype(user.getUserLoginpwdtype());
			resoutUser.setUserLoginpwdstatus(user.getUserLoginpwdstatus());
			commonDao_uc_write.update(resoutUser);
			user = resoutUser;
			//企业用户添加
		}else if(UserFlag.corp.equals(user.getUserFlag())){
			corpUseradd(user);
		}else{
			user.setUserSourcetype(UcConst.SourceType.YOULU_WEB);
			user.setBrandId("SYSTEM");
			user.setUserId(CoreUtils.genSerial(commonDao_uc_write, "UcUser", "userId", "USER", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
			commonDao_uc_write.save(user);
		}
 		Map rtn=BeanUtils.bean2Map(user);
 		//扩展信息
 		if(ext==null){
			ext=new HashMap();
		}
		ext.putAll(rtn);
		//代理商
		if(UserFlag.agent.equals(user.getUserFlag())){
			Map rtn0=(Map)CoreUtils.request(MVCUtils.requireProperty("sl.url"), "api/sl/agent/add", ext,"UC","SL");
			rtn.put("ext",rtn0);
		}
		return rtn;

	}

	/**
	 * 添加企业用户
	 * @param user
	 * @throws Exception
	 */
 	public void corpUseradd(UcUser user) throws Exception {
 		user.setUserSourcetype(UcConst.SourceType.YOULU_WEB);
		user.setUserId(CoreUtils.genSerial(commonDao_uc_write, "UcUser", "userId", "USER", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
		commonDao_uc_write.save(user);
		//推送到企业系统中
		CoreUtils.request(UcConfig.Cr.url, UcConfig.Cr.corpuser_add, MapUtils.toMap(new Object[][] {
			{"userId",user.getUserId()},
			{"userMobile",user.getUserMobile()}
		}), "UC", "TB");

	}

	/**
 	 * 修改用户
 	 * @param user 用户
 	 * @throws Exception
 	 */
 	public UcUser edit(UcUser user) throws Exception{
		//U:更新用户
 		commonDao_uc_write.update(user);
 		return user;
 	}

 	/**
 	 * 修改用户
 	 * @param user 新用户
 	 * @param oUser 旧用户
 	 */
 	@Transactional(rollbackFor=Exception.class)
 	public Map edit(UcUser user,UcUser oUser,Map ext,String sourceType) throws Exception{
 		//C:用户名
 		if(!isEmpty(user.getUserLoginname())&&!user.getUserLoginname().equals(oUser.getUserLoginname())){
			if(ifExsistAvlByLoginname$ExclueUser(user.getUserFlag(),user.getUserLoginname(),user.getUserId(),user.getBrandId())) {
				throw new ApplicationException(UcConst.Errorcode.USER_LOGIN_EXIST,"用户名已被占用",null);
			}
 		}

 		//C:邮箱
		if(!isEmpty(user.getUserEmail())&&!user.getUserEmail().equals(oUser.getUserEmail())){
			if(ifExsistAvlByEmail$ExclueUser(user.getUserFlag(),user.getUserEmail(),user.getUserId(),user.getBrandId())) {
				throw new ApplicationException(UcConst.Errorcode.USER_EMAIL_EXIST, "邮箱已被占用", null);
			}
		}

		//C:手机号
 		if(!isEmpty(user.getUserMobile())&&!user.getUserMobile().equals(oUser.getUserMobile())){
 			if(ifExsistAvlByMobile$ExclueUser(user.getUserFlag(),user.getUserMobile(),user.getUserId(),user.getBrandId())) {
 				throw new ApplicationException(UcConst.Errorcode.USER_PHONE_INVALIDE,"手机已被占用: "+user.getUserMobile(),null);
 			}
 		}

 		//密码转换
		if(!isEmpty(user.getUserLoginpwd())&&!user.getUserLoginpwd().equals(oUser.getUserLoginpwd())){
 			user.setUserLoginpwd(UserUtils.getEncryptPwd(user.getUserLoginpwd(), oUser.getUserLoginpwdtype()));
 		}

		//头像
		if(!isEmpty(user.getUserAvatar())&&!user.getUserAvatar().equals(oUser.getUserAvatar())){
 			//转换用户头像为正式文件
			CoreUtils.request(MVCUtils.requireProperty("fs.url"), "api/file/confirm", MapUtils.toMap(new Object[][] {
				{"filePaths",user.getUserAvatar()}
			}),"UC","FS");
 		}

		//
 		user.setUserModifieddate(DateUtils.getNowDString());

		//U:更新用户
 		commonDao_uc_write.update(user);
 		Map rtn=BeanUtils.bean2Map(user);

 		//扩展信息
 		if(ext==null){
			ext=new HashMap();
		}
		ext.putAll(BeanUtils.bean2Map(user));

 		//代理商
		if(UserFlag.agent.equals(oUser.getUserFlag())){
			Map rtn0=(Map)CoreUtils.request(MVCUtils.requireProperty("sl.url"), "api/sl/agent/edit", ext,"UC","SL");
			rtn.put("ext",rtn0);
		}
		//修改tq和风云账号
		if(UserFlag.inner.equals(oUser.getUserFlag())) {
			CoreUtils.request(MVCUtils.requireProperty("bd.url"), "api/bd/ee/editUserInfo", ext,"UC","BD");
		}

//		user = commonDao_uc_write.queryObject("SELECT * FROM UcUser WHERE userId=?",new Object[]{user.getUserId()},UcUser.class);
		pushUserProfileEdit(user,sourceType);
		return rtn;
 	}

// 	/**
// 	 * edit
// 	 * @param user
// 	 * @throws Exception
// 	 */
// 	@Transactional(rollbackFor=Exception.class)
// 	public void edit(UcUser user) throws Exception{
// 		user.setUserModifieddate(DateUtils.getNowDString());
//		//U:更新用户
// 		commonDao_uc_write.update(user);
// 	}

// 	/**
// 	 * 删除
// 	 * @param 删除用户
// 	 * @param fromSub 是否来自子系统
// 	 */
// 	public void delete(UcUser user) throws Exception{
// 		//C:检查是否为系统用户
// 		if(user.getUserType().equals(UcUser.Type.system)) 		throw new Exception("系统用户不能删除:"+user.getUserLoginname());
//
// 		//D:用户角色关联
// 		List<UcUserrole> userroles=ucUserroleService.queryLByUserId(user.getUserId());
// 		for(UcUserrole userole:userroles){
// 			ucUserroleService.delete(userole);
//		}
//
//		//D:用户
//		commonDao_uc_write.execute("DELETE FROM UcUser WHERE userId=?",new Object[]{user.getUserId()});
//
//		//D:照片
//		if(!StringUtils.isEmpty(user.getUserPhoto())){
////			try {
////				FileUtils.delete(CoreConfig.Path.upload+"/"+user.getUserPhoto());
////			} catch (IOException e) {
////				throw new Exception(e);
////			}
//		}
//	}

 	/**
 	 * 密码是否匹配
 	 * @param pwd 密码
 	 * @param encryptedPwd 已加密密码
 	 * @param pwdtype 密码加密类型
 	 */
 	public boolean matchPwd(String pwd,String encryptedPwd,String pwdtype){
 		return UserUtils.getEncryptPwd(pwd, pwdtype).equalsIgnoreCase(encryptedPwd);
 	}


	/**
	 * 更新密码
	 * @param user 用户
	 * @param loginpwd 密码
	 */
 	@Transactional(rollbackFor=Exception.class)
	public void editLoginpwd(UcUser user, String loginpwd) throws Exception {
 		String pwdType = user.getUserLoginpwdtype();
		if(StringUtils.isEmpty(user.getUserLoginpwdtype())) {
			pwdType = User.loginpwdtype_default;
		}
		String passWord = loginpwd;
		//密码加密
		if(pwdType.equals("SHA1")){
			loginpwd=SHA1Utils.encrypt(loginpwd).toUpperCase();
 		}else if(pwdType.equals("MD5")){
 			loginpwd=MD5Utils.encrypt(loginpwd).toUpperCase();
 		}
		if(loginpwd.equalsIgnoreCase(user.getUserLoginpwd())) {
			throw new ApplicationException(UcConst.Errorcode.USER_PWD_INVALIDE,"不能与修改前密码一致",null);
		}
		commonDao_uc_write.execute("UPDATE UcUser SET userLoginpwd=?,userLoginpwdtype=? , userLastloginpwddate= ? WHERE userId=? ",new Object[]{loginpwd,pwdType,DateUtils.getNowDString(),user.getUserId()});
		if(UcConst.UserFlag.student.equals(user.getUserFlag())) {
			netPwdEdit(user.getUserSourceid(), passWord);
		}
 	}

	/**
	 * 根据ID查询用户
	 * @param id 用户ID
	 */
	public UcUser queryById(String id) throws Exception{
		return commonDao_uc_write.queryObject("SELECT * FROM UcUser WHERE userId=?",new Object[]{id},UcUser.class);
	}

	/**
	 * 根据ID查询可用用户
	 *
	 * @param id      用户ID
	 * @param brandId 品牌ID
	 */
	@SuppressWarnings("WeakerAccess")
	public UcUser queryActiveUserById(String id, String brandId) throws Exception {
		String sql = "SELECT * FROM UcUser WHERE userId=? AND userDelstatus='N' AND brandId=?";
		return commonDao_uc_write.queryObject(sql, new Object[]{id, brandId}, UcUser.class);
	}


 	/**
	 * 更新密码
	 * @throws DBException
	 */
	@Transactional(rollbackFor=Exception.class)
	public void editDiLoginpwd(UcUser user) throws DBException {
		commonDao_uc_write.update(user);
	}

 	/***************************************************************************/

	/**
	 * 根据用户名查询用户
	 * @param loginname 用户名
	 * @param brandId
	 */
	public UcUser queryByLoginname$Flag$Avlstatus(String loginname,String flag,String avlstatus, String brandId) throws Exception {

		Map param=MapUtils.toMap(new Object[][]{
			{"userLoginname"	,loginname},
			{"userFlag"			,flag},
			{"userAvlstatus"	,avlstatus},
			{"brandId"			,brandId}
		});

		String sql="SELECT * FROM UcUser WHERE userLoginname=:userLoginname AND userFlag=:userFlag AND userDelstatus = 'N'";
		if(!isEmpty(avlstatus)){
			sql+="  AND userAvlstatus=:userAvlstatus ";
		}
		if(UcConst.UserFlag.student.equals(flag)) {
			sql+="  AND brandId=:brandId ";
		}
		return (UcUser)commonDao_uc_write.queryObject(sql,param,UcUser.class);
	}

 	/**
	 * 查询用户
	 * @param email 邮箱
	 */
	public UcUser queryByEmail$Flag$Avlstatus(String email,String flag,String avlstatus, String brandId) throws Exception {

		Map param=MapUtils.toMap(new Object[][]{
			{"userEmail"	,email},
			{"userFlag"		,flag},
			{"userAvlstatus",avlstatus},
			{"brandId"		,brandId}
		});

		String sql="SELECT * FROM UcUser WHERE userEmail=:userEmail AND userFlag=:userFlag AND userDelstatus = 'N'";
		if(!isEmpty(avlstatus)){
			sql+="  AND userAvlstatus=:userAvlstatus ";
		}
		if(UcConst.UserFlag.student.equals(flag)) {
			sql+="  AND brandId=:brandId ";
		}
		return (UcUser)commonDao_uc_write.queryObject(sql,param,UcUser.class);
	}


	/**
 	 * 根据ID查询用户
 	 * @param userSourceid
 	 */
 	public UcUser queryBySourceId(String userSourceid) throws Exception{
		return commonDao_uc_write.queryObject("SELECT * FROM UcUser WHERE userSourceid=? AND userDelstatus = 'N' ",new Object[]{userSourceid},UcUser.class);
	}
	/**
	 * 查询用户
	 * @param mobile 手机
	 * @param brandId
	 */
	public UcUser queryByMobile$Flag$Avlstatus(String mobile,String flag,String avlstatus, String brandId) throws Exception {

		Map param=MapUtils.toMap(new Object[][]{
			{"userMobile"	,mobile},
			{"userFlag"		,flag},
			{"userAvlstatus",avlstatus},
			{"brandId"		,brandId}
		});

		String sql="SELECT * FROM UcUser WHERE userMobile=:userMobile AND userFlag=:userFlag  AND userDelstatus = 'N'";
		if(!isEmpty(avlstatus)){
			sql+="  AND userAvlstatus=:userAvlstatus ";
		}
		if(UcConst.UserFlag.student.equals(flag)) {
			sql+="  AND brandId=:brandId ";
		}
		return (UcUser)commonDao_uc_write.queryObject(sql,param,UcUser.class);
	}






	private final SqlWE[] sqlWE_queryLmC=new SqlWE[]{
		new SqlWE("userName"		,SqlWE.Compare.like		,SqlWE.Type.STR),
		new SqlWE("userLoginname"	,SqlWE.Compare.like		,SqlWE.Type.STR),
		new SqlWE("userLockstatus"	,SqlWE.Compare.equal	,SqlWE.Type.STR),
		new SqlWE("userMobile"		,SqlWE.Compare.equal	,SqlWE.Type.STR),
		new SqlWE("userEmail"		,SqlWE.Compare.like		,SqlWE.Type.STR),
		new SqlWE("userIdcard"		,SqlWE.Compare.like		,SqlWE.Type.STR),
		new SqlWE("userGender"		,SqlWE.Compare.equal	,SqlWE.Type.STR),
	};

	/**
	 * queryLmCBySqlMember
	 * @param queryType
	 * @param sqlSelect
	 * @param sqlWhere
	 * @param sqlGroup
	 * @param sqlOrder
	 * @param orderKey
	 * @param orderVal
	 * @return
	 * @throws Exception
	 */
	public QueryResult queryMapsCount(Integer pageIndex,Integer pageSize,Map params,String orderKey, String orderVal) throws Exception {
		String sql = "FROM UcUser  WHERE 1=1";

		if(!isEmpty((String)params.get("userName"))){
			sql += " AND userName=:userName ";
		}

		if(!isEmpty((String)params.get("userLoginname"))){
			sql += " AND userLoginname=:userLoginname";
		}

		if(!isEmpty((String)params.get("userEmail"))){
			sql += " AND userEmail=:userEmail";
		}

		if(!isEmpty((String)params.get("userMobile"))){
			sql += " AND userMobile=:userMobile";
		}

		if(!isEmpty((String)params.get("userGender"))){
			sql += " AND userGender=:userGender";
		}

		if(!isEmpty((String)params.get("userIdcard"))){
			sql += " AND userIdcard=:userIdcard";
		}

		if(!isEmpty((String)params.get("userFlag"))){
			sql += " AND userFlag=:userFlag";
		}

		if(!isEmpty((String)params.get("userLockstatus"))){
			sql += " AND userLockstatus=:userLockstatus";
		}

		if(!isEmpty((String)params.get("userDelstatus"))){
			sql += " AND userDelstatus=:userDelstatus";
		}

		if(!isEmpty((String)params.get("userSourcecreateddate_begin"))){
			sql += " AND userCreateddate>=:userSourcecreateddate_begin";
		}

		if(!isEmpty((String)params.get("userSourcecreateddate_end"))){
			sql += " AND userCreateddate<=:userSourcecreateddate_end";
		}

		if(!isEmpty((String)params.get("userAvlstatus"))) {
			sql += " AND userAvlstatus=:userAvlstatus";
		}
		if(!isEmpty((String)params.get("brandId"))) {
			sql += " AND brandId=:brandId";
		}
		if(!isEmpty((String)params.get("userId"))){
			sql += " AND userId IN (";
			String strids = "" ;
			String []   ids = params.get("userId").toString().split(",");
			for (int i = 0; i <ids.length; i++) {
				strids += 	"'"+ids[i] +"',";
			}
			strids = strids.substring(0, strids.length()-1);
			sql += strids+	")";
		}

		if(!isEmpty(orderKey)){

			sql += " ORDER BY "+ orderKey;

			if(orderVal.equals("Y")){

				sql += " ASC";

			}else if(orderVal.equals("N")){

				sql += " DESC";

			}
		}else{

			sql += " ORDER BY  userId  DESC" ;

		}
		QueryResult result = commonDao_uc_read.queryMapsCount("SELECT * " + sql, pageIndex, pageSize, params, UcUser.class);
		if(!isEmpty((String)params.get("userFlag")) && "S".equals((String)params.get("userFlag"))) {
			excuseUserList(result.getData());
		}
		return result;
	}

	/**
	 * 处理用户列表
	 * @param userList
	 * @throws DBException
	 */
	public void excuseUserList(List<Map> userList) throws DBException {
		if(userList != null && userList.size() > 0) {
			//组装userIdlist
			ArrayList<String> userIds = new ArrayList<>();
			for (Map userMap : userList) {
				userIds.add((String)userMap.get("userId"));
			}
			Map studentMap = findStudentDeposittypeIdByUserIds(SQLUtils.getIn(userIds));
			for (Map userMap : userList) {
				String studentDeposittype = (String) studentMap.get((String)userMap.get("userId"));
				if(StringUtils.isEmpty(studentDeposittype)) {
					studentDeposittype = "C";
				}
				userMap.put("studentDeposittype", studentDeposittype);
			}

		}
	}

	public QueryResult queryMapsCount_I(Map mWhere,Map mGroup,Map mOrder,int pageIndex, int pageCount) throws Exception {
		QueryResult qr=new QueryResult();
		List data=commonDao_uc_read.queryMaps("SELECT SELECT userId,userName,userFlag,userLoginname,userLoginpwdstatus,userLoginpwdtype,userLockstatus,userLastloginip,userLastlogindate,userCreateddate,userCreatetype,userModifieddate,userModifiedtype,userMobile,userMobilestatus,userEmail,userEmailstatus,userIdcard,userGender,userSourceid,userDelstatus,userAvlstatus FROM UcUser_I ucUser WHERE 1=1 LIMIT 0,1",null,null );
		qr.setData(data);
		return qr;
	}

	public QueryResult queryMapsCount_M(Map mWhere,Map mGroup,Map mOrder,int pageIndex, int pageCount) throws Exception {
		QueryResult qr=new QueryResult();
		List data=commonDao_uc_read.queryMaps("SELECT SELECT userId,userName,userFlag,userLoginname,userLoginpwdstatus,userLoginpwdtype,userLockstatus,userLastloginip,userLastlogindate,userCreateddate,userCreatetype,userModifieddate,userModifiedtype,userMobile,userMobilestatus,userEmail,userEmailstatus,userIdcard,userGender,userSourceid,userDelstatus,userAvlstatus FROM UcUser_M ucUser WHERE 1=1 LIMIT 0,1",null,null );
		qr.setData(data);
		return qr;
	}


	/**
	 * 锁定用户
	 * @param id 用户ID
	 */
	public void lock(String id) throws Exception {
		//检测用户是否存在
 		if(!ifExsistById(id)) {
 			throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT,"用户信息不存在",null);
 		}
		commonDao_uc_write.execute("UPDATE UcUser SET userLockstatus=? WHERE userId=? ",new Object[]{Lockstatus.YES,id});
	}

	/**
	 * 解锁用户
	 * @param id 用户ID
	 */
	public void unlock(String id) throws Exception {
		//检测用户是否存在
 		if(!ifExsistById(id)) {
 			throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT,"用户信息不存在",null);
 		}
 		commonDao_uc_write.execute("UPDATE UcUser SET userLockstatus=? WHERE userId=? ",new Object[]{Lockstatus.NO,id});
	}

	/**
	 * 更新用户最后使用角色
	 * @param id 用户ID
	 * @param roleId 角色ID
	 */
	public void updateLastrole(String id,String roleId) throws Exception {
		commonDao_uc_write.execute("UPDATE UcUser SET userLastrole=? WHERE userId=?",new Object[]{roleId,id});
	}

	/**
	 * 更新用户最后登录时间及IP
	 * @param id 用户ID
	 * @param lastloginIp 用户最后登录IP
	 * @param lastlogindate 用户最后登录时间
	 */
	public void updateLastlogin(String id,String lastloginIp,String lastlogindate) throws Exception {
		commonDao_uc_write.execute("UPDATE UcUser SET userLastlogindate=?,userLastloginip=? WHERE userId=?",new Object[]{lastlogindate,lastloginIp,id});
	}

	/**
	 * impFromCRM
	 * @throws Exception
	 */
	public void impFromCRM() throws Exception{
		int count=commonDao_crm_read.queryInt("SELECT COUNT(1) FROM SYS_User ", new Object[]{});
		logger.info("[CRM用户导入][总量]"+count);
		int batch	=200;
		int pos		=0;

		Map<String,UcUser> mUsers	=ListUtils.extract2Map(commonDao_uc_read.queryMaps("SELECT userSourceid FROM UcUser WHERE userSourceid ",null,UcUser.class), "userSourceid");

		while(pos<count){
			List<Map> users=commonDao_crm_read.queryMaps("SELECT * from SYS_User ORDER BY CreatedOn", pos, batch, new Object[]{}, null);
			logger.info("[CRM用户导入][进度]"+pos+"/"+count);
			for(Map mUser:users){
				String sourceid	=(String)mUser.get("UserId");
				impFromCRM(mUser, mUsers.get(sourceid));
			}
			pos+=users.size();
		}
	}

	/**
	 * impFromCRM
	 * @param user
	 */
	public UcUser impFromCRM(Map mUser,UcUser oUser) throws Exception{
		String modifieddate			=DateUtils.toString((java.sql.Timestamp)mUser.get("ModifiedOn"));
		boolean update				=false;
		if(oUser==null){
			oUser=new UcUser();
		}else{
			if(ObjectUtils.equal(oUser.getUserModifieddate(), modifieddate)){
				return null;
			}
			update=true;
		}
		oUser.setUserCreateddate		(DateUtils.toString((java.sql.Timestamp)mUser.get("CreatedOn")));
		oUser.setUserCreatetype		(Datatype.CRM_IMPORT);
		oUser.setUserModifieddate	(modifieddate);
		oUser.setUserModifiedtype	(Datatype.CRM_IMPORT);
		oUser.setUserLoginname		((String)mUser.get("UserName"));
		oUser.setUserLoginpwd		((String)mUser.get("UserPassword"));
		oUser.setUserLoginpwdtype	(UserLoginpwdtype.MD5);
		oUser.setUserAvlstatus		(!"1".equals(toString0(mUser.get("IsDisabled")))?Avlstatus.YES:Avlstatus.NO);
		oUser.setUserDelstatus		(!"1".equals(toString0(mUser.get("IsDeleted")))	?Delstatus.YES:Delstatus.NO);
		oUser.setUserSourceid		((String)mUser.get("UserId"));
		oUser.setUserFlag			(UserFlag.inner);
		if(!update){
			oUser.setUserId			(CoreUtils.genSerial(commonDao_uc_write, "UcUser", "userId", "USER", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
			commonDao_uc_write.save(oUser);
		}else{
			commonDao_uc_write.update(oUser);
		}
		return oUser;
	}


	/**
	 * list_d
	 * @param idlist用户标识集合
	 * @throws DBException
	 */
	public List list_d(String[] idlist) throws DBException {
		String in = SQLUtils.getIn(idlist);
		String sql = "SELECT * FROM UcUser WHERE userId IN ("+in+")";
		List<UcUser> list = commonDao_uc_read.queryObjects(sql, null , UcUser.class);
		if(list != null && list.size() > 0) {
			for (UcUser user : list) {
				if(!isEmpty(user.getUserNickname())){
					user.setUserNickname(NickNameReplace.QueryReplace(user.getUserNickname()));
				}
			}
		}
		return list;
	}


	/**
	 * profile_view
	 * @param 用户信息
	 * @throws DBException
	 */
	public Map profile_view(String userId) throws DBException {
		UcUser user =	commonDao_uc_write.queryObject("SELECT * FROM UcUser WHERE userId=?",new Object[]{userId},UcUser.class);
		return BeanUtils.bean2Map(user);
	}


	/**
	 * wechat_add
	 * @param 微信用户添加账户
	 * @throws Exception
	 * @throws DBException
	 */
	@Transactional(rollbackFor = Exception.class,global=true)
	public String wechatAdd(UcUser user) throws Exception {
		user.setUserId(CoreUtils.genSerial(commonDao_uc_write, "UcUser", "userId", "USER", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
		user.setUserCreateddate(DateUtils.getNowDString());
		user.setUserFlag("S");
		user.setUserLockstatus("N");
		user.setUserMobilestatus("N");
		user.setUserEmailstatus("N");
		user.setUserDelstatus("N");
		user.setUserAvlstatus("Y");
		user.setUserMarrystatus("N");
		user.setUserIdcardstatus(UserIDcardStatus.NO);
		commonDao_uc_write.save(user);
		Map map = new HashMap();
		map.put("userId", user.getUserId());
		map.put("studentSourcetype", user.getUserSourcetype());
		String 		url_bd			= MVCUtils.requireProperty("bd.url");
		String 		url_fd			= MVCUtils.requireProperty("fd.url");
		CoreUtils.request(url_bd,"api/bd/student/app/add", map, "usercenter", "basedata");
		CoreUtils.request(url_fd,"api/fd/account/app/add", map, "usercenter", "fundsystem");
 		return user.getUserId();
	}


	/**
	 * 查询同一手机号下最早的用户
	 * @param selectFirstUserByMobile
	 * @throws Exception
	 * @throws DBException
	 */
	public UcUser selectFirstUserByMobile(String userMobile) throws DBException {
		return (UcUser)commonDao_uc_read.queryObject("SELECT * FROM UcUser WHERE userMobile =? ORDER BY userCreateddate ASC LIMIT 1",new Object[]{userMobile},UcUser.class);
	}


	/**
	 * 删除同一手机号下其他的用户
	 * @param deleteOtherUsers
	 * @throws Exception
	 * @throws DBException
	 */
	public Map deleteOtherUsers(UcUser user) throws DBException {
		List<UcUser> list = commonDao_uc_write.queryObjects("SELECT * FROM UcUser WHERE userId!=:userId AND userMobile =:userMobile", null, null, MapUtils.toMap(new Object[][]{{"userId",user.getUserId()},{"userMobile",user.getUserMobile()}}), UcUser.class);
		commonDao_uc_write.execute("DELETE FROM UcUser WHERE userId!=? AND userMobile =?",new Object[]{user.getUserId(),user.getUserMobile()});
		String ids = "";
		Map map = new HashMap();
		for (UcUser ouser : list) {
			ids += ouser.getUserId()+",";
		}
		map.put("ids", ids);
		return map;
	}

	/**
	 * 绑定手机号需要自己填写信息不能依赖于同步程序
	 * @param user
	 * @param ip
	 * @param brandId
	 * @param isPush
	 * @throws Exception
	 */
	public void bindPhone(UcUser user, String ip, String brandId, String isPush) throws Exception {

		UcUser queryById = queryById(user.getUserId());
//		UcUser queryById = queryActiveUserById(user.getUserId(), brandId);
		if(queryById == null) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"用户不存在",null,null);
		}
		String userMobile = queryById.getUserMobile();
		if( userMobile != null && !"".equals(userMobile)) {
			throw new ApplicationException(Errorcode.USER_PHONE_EXIST,"您已绑定手机号",null,null);
		}
		if(lockService==null){
 			lockService = MVCUtils.getBean(LockService.class);
 		}
 		String selfMark = UUID.randomUUID().toString();

 		String key = user.getUserMobile() + user.getBrandId() + user.getUserFlag();
 		try{
			Boolean flag = lockService.tryLock(key, selfMark, 100000L, 10000, 300L);
			if(flag){
				UcUser ucUser = queryByMobile$Flag$Avlstatus(user.getUserMobile(), queryById.getUserFlag(), "",brandId);
				if(ucUser != null) {
					throw new ApplicationException(Errorcode.USER_PHONE_INVALIDE,"该手机号已被占用",MapUtils.toMap(new Object[][] {
						{"userId",ucUser.getUserId()}
					}));
				}
				queryById.setUserMobile(user.getUserMobile());
				if("Y".equals(isPush)) {
					ucUserService.netUserRegist(queryById, ip);
				}else {
					ucUserService.netRegistNoEs(queryById);
				}
			}else {
				throw new ApplicationException(UcConst.Errorcode.USER_MOBILE_BIND_ERROR, "绑定失败,请重试!", null);
			}
		}finally{
			lockService.unlock(key, selfMark);
		}
	}

	@Transactional
	public void netRegistNoEs(UcUser user) throws Exception {
		Map map = netRegistNoEs(user.getUserMobile(), "");
		String userSourceid=(String)map.get("userId");
		user.setUserModifieddate(DateUtils.getNowDString());
		user.setUserSourceid(userSourceid);
		user.setUserMobilestatus("Y");
        String userName=(String)map.get("realName");
		if(!isEmpty(userName)){
			userName=decrypt(userName,"realName");
			if("error".equals(userName)){
				userName=null;
			}
			user.setUserName			(userName);
		}else{
			user.setUserName			(null);
		}
		String pwd = (String)map.get("passWord");
        if(!StringUtils.isEmpty(pwd)) {
        	user.setUserLoginpwd(pwd);
        	user.setUserLoginpwdtype("MD5");
        	user.setUserLoginpwdstatus("S");
        }
        String userLoginname = (String) map.get("userName");
        if(!StringUtils.isEmpty(userLoginname)) {
        	user.setUserLoginname(userLoginname);
        }
        user.setUserSourceid(userSourceid.toUpperCase());
		commonDao_uc_write.update(user);
	}

	@Transactional
	public void netUserRegist(UcUser user, String ip) throws DBException, Exception {
		netRegist(user, ip,"");
	}

	/**
	 * 网站平台注册
	 * @param user
	 * @param ip
	 * @throws Exception
	 * @throws DBException
	 */
	public String netRegist(UcUser user, String ip,String sourceChannel) throws Exception, DBException {
		String sourceid=callnet(ip,sourceChannel,user);
		sourceid = sourceid.toUpperCase();
		UcUser queryBySourceId = queryBySourceId(sourceid);
		if(queryBySourceId == null) {
			user.setUserSourceid(sourceid);
			user.setUserMobilestatus("Y");
			user.setUserModifieddate(DateUtils.getNowDString());
			commonDao_uc_write.update(user);
		}else {
			throw new ApplicationException(Errorcode.USER_PHONE_INVALIDE,"该手机号已被占用",MapUtils.toMap(new Object[][] {
				{"userId",queryBySourceId.getUserId()}
			}));
		}
		//更新手机号
		mqService.publish(UcConst.Mqtype.USER_MQ_SUBCREABER_BUIND_MOBILE, JSONUtils.toString(MapUtils.toMap(new Object[][] {
			{"userId",user.getUserId()},
			{"phone",user.getUserMobile()}
		})));
		return sourceid;
	}


	/**
	 * 调用网站平台接口
	 */
	private String callnet(String ip,String sourceChannel,UcUser user) throws Exception {
        String registUrl = MVCUtils.requireProperty("stb.register.url");
        String projectId = MVCUtils.requireProperty("stb.project.id");
        if(StringUtils.isEmpty(sourceChannel)) {
        	sourceChannel = MVCUtils.requireProperty("stb.source.id");
        }
        String BehaviorAction = MVCUtils.requireProperty("stb.behavior.action");
        String IsDesEncrypt = MVCUtils.requireProperty("stb.encrypt");
        String param = "&IsDesEncrypt=" + IsDesEncrypt + "&Phone=" + user.getUserMobile() + "&ProjectId="
            + projectId + "&Source=" + sourceChannel + "&BehaviorAction=" + BehaviorAction + "";
        logger.info("[绑定手机开户][REQ]" + registUrl+"?"+param);
        try {
            String returndate = HttpUtils.post(registUrl, param, "utf-8");
            logger.info("[绑定手机开户][RES]" + returndate);
            Map map = JSONUtils.toMap(returndate);
            String userSourceid = (String)map.get("UserId");
            String userName=(String)map.get("RealName");
			if(!isEmpty(userName)){
				userName=decrypt(userName,"RealName");
				if("error".equals(userName)){
					userName=null;
				}
				user.setUserName			(userName);
			}else{
				user.setUserName			(null);
			}
			String pwd = (String)map.get("PassWord");
            if(!StringUtils.isEmpty(pwd)) {
            	user.setUserLoginpwd(pwd);
            	user.setUserLoginpwdtype("MD5");
            	user.setUserLoginpwdstatus("S");
            }
            String userLoginname = (String) map.get("UserName");
            if(!StringUtils.isEmpty(userLoginname)) {
            	user.setUserLoginname(userLoginname);
            }
            user.setUserSourceid(userSourceid);
		    return userSourceid;
        } catch (Exception e) {
            throw new ApplicationException(Errorcode.USER_NET_INVALIDE, "网站平台开户异常" + e.getMessage(), null, e);
        }
	}

	/**
	 * 解绑手机号
	 * @param user
	 * @throws Exception
	 */
	public void unbundlingPhone(UcUser user) throws Exception {
		UcUser queryById = queryById(user.getUserId());
		if(queryById == null) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"用户不存在",null,null);
		}
		String userMobile = queryById.getUserMobile();
		if( userMobile != null && !"".equals(userMobile)) {
		}else {
			throw new ApplicationException(ErrorCode.argument_invalide,"您没有绑定手机号",null,null);
		}
		commonDao_uc_write.execute("UPDATE UcUser SET userMobile=?,userMobilestatus=? ,userModifieddate=? WHERE userId=? ",new Object[]{"",null,DateUtils.getNowDString("yyyyMMddHHmmss"),user.getUserId()});

	}

	/**
	 * 绑定邮箱
	 * @param user
	 * @throws Exception
	 */
	public void bindEmail(UcUser user) throws Exception {
		UcUser queryById = queryById(user.getUserId());
		if(queryById == null) {
			throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT,"用户不存在",null,null);
		}
		String userEmail = queryById.getUserEmail();
		if( userEmail != null && !"".equals(userEmail)) {
			throw new ApplicationException(UcConst.Errorcode.USER_EMAIL_EXIST,"您已绑定邮箱",null,null);
		}
		if(ifExsistAvlByEmail$ExclueUser(queryById.getUserFlag(),user.getUserMobile(),user.getUserId(),user.getBrandId())){
			throw new ApplicationException(UcConst.Errorcode.USER_EMAIL_EXIST,"邮箱已被占用",null,null);
		}
		//更新邮箱号
		commonDao_uc_write.execute("UPDATE UcUser SET userEmail=?,userEmailstatus=? ,userModifieddate=?  WHERE userId=? ",new Object[]{user.getUserEmail(),Avlstatus.YES,DateUtils.getNowDString("yyyyMMddHHmmss"),user.getUserId()});

	}

////修改用户手机号
//	public void editPhone(UcUser user) throws Exception {
//		UcUser queryById = queryById(user.getUserId());
//		if(queryById == null) {
//
//			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"用户不存在",null,null);
//		}
//		String userMobile = queryById.getUserMobile();
//		if( userMobile != null && !"".equals(userMobile)) {
//			if(userMobile.equals(user.getUserMobile())) {
//				throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"该手机已是您绑定的手机",null,null);
//
//			}
//		}else {
//			throw new ApplicationException(ErrorCode.USER_ARGUMENT_INVALIDE,"您没有绑定手机号",null,null);
//		}
//		if(ifExsistAvlByMobile$ExclueUser(queryById.getUserFlag(),user.getUserMobile(),user.getUserId())) throw new ApplicationException(ErrorCode.execute_faild,"手机已被占用",null,null);
//		//更新手机号
//		commonDao_uc_write.execute("UPDATE UcUser SET userMobile=?,userMobilestatus=? ,userModifieddate=? WHERE userId=? ",new Object[]{user.getUserMobile(),Avlstatus.yes,DateUtils.getNowDString("yyyyMMddHHmmss"),user.getUserId()});
//		//推送
//		mqService.publish(UcConst.Mqtype.USER_MQ_SUBCREABER_BUIND_MOBILE, JSONUtils.toString(MapUtils.toMap(new Object[][] {
//			{"userId",user.getUserId()},
//			{"phone",user.getUserMobile()}
//		})));
//	}
//
//	public void editMail(UcUser user) throws Exception {
//		UcUser queryById = queryById(user.getUserId());
//		if(queryById == null) {
//
//			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"用户不存在",null,null);
//		}
//		String userEmail = queryById.getUserEmail();
//		if( userEmail != null && !"".equals(userEmail)) {
//			if(userEmail.equals(user.getUserEmail())) {
//				throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"该邮箱已是您绑定的邮箱",null,null);
//
//			}
//		}else {
//			throw new ApplicationException(ErrorCode.execute_faild,"您没有绑定邮箱",null,null);
//		}
//		if(ifExsistAvlByEmail$ExclueUser(queryById.getUserFlag(),user.getUserMobile(),user.getUserId())) throw new ApplicationException(ErrorCode.execute_faild,"邮箱已被占用",null,null);
//		//更新邮箱号
//		commonDao_uc_write.execute("UPDATE UcUser SET userEmail=?,userEmailstatus=?,userModifieddate=? WHERE userId=? ",new Object[]{user.getUserEmail(),Avlstatus.yes,DateUtils.getNowDString("yyyyMMddHHmmss"),user.getUserId()});
//
//	}

	/**
	 * 修改用户手机号
	 * @param user
	 * @throws Exception
	 */
	@Transactional(rollbackFor=Exception.class)
	public void phone_edit(UcUser user) throws Exception {
		UcUser queryById = queryById(user.getUserId());
		if(queryById == null) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"用户不存在",null,null);
		}
		if(UcConst.Code.Lockstatus.YES.equals(queryById.getUserLockstatus())) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"账号已锁定，请联系管理员",null,null);
		}
		if(UcConst.Avlstatus.NO.equals(queryById.getUserAvlstatus())) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"账号已禁用，请联系管理员",null,null);
		}
		//判断是否是企业用户
		if(!UcConst.UserFlag.corp.equals(queryById.getUserFlag())) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"只有企业用户才可以修改手机号！",null);
		}
		String userMobile = queryById.getUserMobile();
		if( userMobile != null && !"".equals(userMobile)) {
			if(userMobile.equals(user.getUserMobile())) {
				throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"该手机已是您绑定的手机",null,null);
			}
		}else {
			throw new ApplicationException(ErrorCode.argument_invalide,"您没有绑定手机号",null,null);
		}
		if(ifExsistAvlByMobile$ExclueUser(queryById.getUserFlag(),user.getUserMobile(),user.getUserId(),user.getBrandId())){
			throw new ApplicationException(UcConst.Errorcode.USER_PHONE_INVALIDE,"手机已被占用",null,null);
		}
		//更新手机号
		commonDao_uc_write.execute("UPDATE UcUser SET userMobile=?,userMobilestatus=? ,userModifieddate=? WHERE userId=? ",new Object[]{user.getUserMobile(),Avlstatus.YES,DateUtils.getNowDString("yyyyMMddHHmmss"),user.getUserId()});
		CoreUtils.request(UcConfig.Cr.url, UcConfig.Cr.corpuser_edit, MapUtils.toMap(new Object[][] {
			{"cuId",queryById.getUserId()},
			{"cuuserName",queryById.getUserName()},
			{"cuuserMobile",user.getUserMobile()}
		}), "UC", "CR");

		//推送
		mqService.publish(UcConst.Mqtype.USER_MQ_SUBCREABER_BUIND_MOBILE, JSONUtils.toString(MapUtils.toMap(new Object[][] {
			{"userId",user.getUserId()},
			{"phone",user.getUserMobile()}
		})));
	}

	/**
	 * 修改邮箱
	 * @param user
	 * @throws Exception
	 */
	@Transactional(rollbackFor=Exception.class)
	public void editMail(UcUser user) throws Exception {
		UcUser queryById = queryById(user.getUserId());
		if(queryById == null) {

			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"用户不存在",null,null);
		}
		String userEmail = queryById.getUserEmail();
		if( userEmail != null && !"".equals(userEmail)) {
			if(userEmail.equals(user.getUserEmail())) {
				throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"该邮箱已是您绑定的邮箱",null,null);
			}
		}else {
			throw new ApplicationException(ErrorCode.argument_invalide,"您没有绑定邮箱",null,null);
		}
		if(ifExsistAvlByEmail$ExclueUser(queryById.getUserFlag(),user.getUserMobile(),user.getUserId(),user.getBrandId())){
			throw new ApplicationException(UcConst.Errorcode.USER_EMAIL_EXIST,"邮箱已被占用",null,null);
		}
		//更新邮箱号
		commonDao_uc_write.execute("UPDATE UcUser SET userEmail=?,userEmailstatus=?,userModifieddate=? WHERE userId=? ",new Object[]{user.getUserEmail(),Avlstatus.YES,DateUtils.getNowDString("yyyyMMddHHmmss"),user.getUserId()});

	}

	/**
	 * 根据用户手机查询用户信息
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	public Map findPhone(String userId) throws Exception {
		UcUser queryById = queryById(userId);
		if(queryById != null) {
			HashMap<String,String> hashMap = new HashMap<String,String>();
			hashMap.put("phone", queryById.getUserMobile());
			return hashMap;
		}else {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"用户不存在",null,null);
		}

	}

	public void changeLoginValidate(String userId, String validateType) throws Exception {
		UcUser queryById = queryById(userId);
		if(queryById == null) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"用户不存在",null,null);
		}
		Object[] param =new String[4];
		param[2]="0";
		param[3]=userId;
		if("1".equals(validateType)) {
			//密码校验
			param[0]="N";
			param[1]="N";

		}else if("2".equals(validateType)){

			//密码+手机
			if(StringUtils.isEmpty(queryById.getUserMobile())) {
				//手机号不存在
				throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"请先绑定手机号",null,null);
			}else {
				param[0]="Y";
				param[1]="N";
			}
		}else if("3".equals(validateType)) {
			//密码+邮箱
			if(StringUtils.isEmpty(queryById.getUserEmail())) {
				throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"请先绑定邮箱",null,null);
			}
			param[0]="N";
			param[1]="Y";
		}else {
			//参数错误
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"参数错误",null,null);
		}
		commonDao_uc_write.execute("UPDATE UcUser SET userCheckmobile=?,userCheckemail=?,userModifieddate=? WHERE userId=? ",param);
	}


	/**
	 * redis中邮箱是否已存在
	 * @param email 邮箱
	 */
	public String ifExsistAvlByEmailInRedis(String flag,String email) throws Exception{
		return CoreConst.redis.hget("DI_UserEmails"		,email+"&"+flag	)==null?"":(String) CoreConst.redis.hget("DI_UserEmails"		,email+"&"+flag	);
	}

	/**
	 * 手机是否已存在
	 * @param mobile 手机
	 */
	public String ifExsistAvlByMobileInRedis(String flag,String mobile) throws Exception{
		return CoreConst.redis.hget("DI_UserMobiles"	,mobile+"&"+flag)==null?"":(String)CoreConst.redis.hget("DI_UserMobiles"	,mobile+"&"+flag);
	}

	/**
	 * 用户名是否已存在
	 * @param loginname 用户名
	 */
	public String  ifExsistAvlByLoginnameInRedis(String flag,String loginname) throws Exception{
		return CoreConst.redis.hget("DI_UserLoginNames"	,loginname+"&"+flag)==null?"":(String)CoreConst.redis.hget("DI_UserLoginNames"	,loginname+"&"+flag);
	}

	/**
	 * 用户名是否已存在
	 * @param sourceid
	 * @param sourcetype
	 * @return
	 * @throws Exception
	 */
	public String ifExsistAvlBySourceInRedis(String sourceid,String sourcetype) throws Exception{

		return CoreConst.redis.hget("DI_UserSourceids"	,sourceid+"&"+sourcetype)==null?"":(String) CoreConst.redis.hget("DI_UserSourceids"	,sourceid+"&"+sourcetype);
	}

	/**
	 * 邮箱是否已存在
	 * @param email 邮箱
	 * @param excludeUserId 排除的用户标识
	 */
	public boolean ifExsistAvlByEmail$ExclueUserInRedis(String flag,String email,String excludeUserId) throws Exception{
		String id =CoreConst.redis.hget("DI_UserEmails"	,email+"&"+flag)==null?"":(String)CoreConst.redis.hget("DI_UserEmails"	,email+"&"+flag);
		return excludeUserId.equals(id);
	}

	/**
	 * 手机是否已存在
	 * @param mobile 手机
	 * @param excludeUserId 排除的用户标识
	 */
	public boolean ifExsistAvlByMobile$ExclueUserInRedis(String flag,String mobile,String excludeUserId) throws Exception{
		String id =CoreConst.redis.hget("DI_UserMobiles"	,mobile+"&"+flag)==null?"":(String)CoreConst.redis.hget("DI_UserMobiles"	,mobile+"&"+flag);
		return excludeUserId.equals(id);
	}

	/**
	 * 用户名是否已存在
	 * @param loginname 用户名
	 * @param excludeUserId 排除的用户标识
	 */
	public boolean ifExsistAvlByLoginname$ExclueUserInRedis(String flag,String loginname,String excludeUserId) throws Exception{
		String id =CoreConst.redis.hget("DI_UserLoginNames"	,loginname+"&"+flag)==null?"":(String)CoreConst.redis.hget("DI_UserLoginNames"	,loginname+"&"+flag);
		return excludeUserId.equals(id);
		}


	/**
	 * 不同步事务
	 * @return
	 * @throws Exception
	 */
	public Map nocheckadd(UcUser user) throws Exception {
		//判断用户源标识是否被已存在
		if(!isEmpty(user.getUserSourceid())) {
			if(ifExsistAvlBySource(user.getUserSourceid(),UserFlag.student)){
				throw new ApplicationException(UcConst.Errorcode.USER_SOURCEID_EXIST,"用户源标识已存在",null);
			}
		}
		Boolean again = false;

 		//密码转换(来自于其它系统的用户密码不转换)
 		if(isEmpty(user.getUserSourceid())){
 			if(!isEmpty(user.getUserLoginpwd())){
 	 			user.setUserLoginpwd(UserUtils.getEncryptPwd(user.getUserLoginpwd(), user.getUserLoginpwdtype()));
 	 		}
 		}


 		user.setUserId(CoreUtils.genSerial(commonDao_uc_write, "UcUser", "userId", "USER", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
 		user.setUserIdcardstatus(UserIDcardStatus.NO);
 		commonDao_uc_write.save(user);
 		Map rtn=BeanUtils.bean2Map(user);
 		//扩展信息
		Map	ext=new HashMap();
		ext.putAll(rtn);
		//代理商
		if(UserFlag.agent.equals(user.getUserFlag())){
			Map rtn0=(Map)CoreUtils.request(MVCUtils.requireProperty("sl.url"), "api/sl/agent/add", ext,"UC","SL");
			rtn.put("ext",rtn0);
		}
		return rtn;
	}

	public UcUser queryBySourceid$Flag$Avlstatus(String userSourceid, String flag, String avlstatus) throws DBException {
		Map param=MapUtils.toMap(new Object[][]{
			{"userSourceid"	,userSourceid},
			{"userFlag"		,flag},
			{"userAvlstatus",avlstatus}
		});

		String sql="SELECT * FROM UcUser WHERE userSourceid=:userSourceid AND userFlag=:userFlag AND userDelstatus = 'N'";
		if(!isEmpty(avlstatus)){
			sql+="  AND userAvlstatus=:userAvlstatus ";
		}

		return (UcUser)commonDao_uc_write.queryObject(sql,param,UcUser.class);
	}
	/**
	 * queryLmCBySqlMember
	 * @param queryType
	 * @param sqlSelect
	 * @param sqlWhere
	 * @param sqlGroup
	 * @param sqlOrder
	 * @param orderKey
	 * @param orderVal
	 * @return
	 * @throws Exception
	 */
	public QueryResult querymcMapsCount(Integer pageIndex,Integer pageSize,Map params,String orderKey, String orderVal) throws Exception {
		String sql = "SELECT userId,userName,userLoginname,userMobile FROM UcUser WHERE 1=1";
		String sqlCount="SELECT count(1) FROM UcUser WHERE 1=1";
		String sqlWhere="";
		if(!isEmpty((String)params.get("userFlag"))){
			sqlWhere += " AND userFlag='"+params.get("userFlag")+"'";
		}
		if(!isEmpty((String)params.get("userGender"))){
			sqlWhere += " AND userGender='"+params.get("userGender")+"'";
		}
		if(!isEmpty((String)params.get("userAreacode"))){
			sqlWhere += " AND userAreacode like '"+params.get("userAreacode")+"%'";
		}

		if(!isEmpty((String)params.get("userName"))){
			sqlWhere += " AND userName = '"+params.get("userName")+"'";
		}
		if(!isEmpty((String)params.get("userMobile"))){
			sqlWhere += " AND userMobile = '"+params.get("userMobile")+"'";
		}

		sql+=sqlWhere;
		sqlCount+=sqlWhere;
		if(!isEmpty(orderKey)){

			sql += " ORDER BY "+ orderKey;

			if(orderVal.equals("Y")){

				sql += " ASC";

			}else if(orderVal.equals("N")){

				sql += " DESC";

			}
		}else{
			sql += " ORDER BY  userId  DESC" ;

		}
		QueryResult queryResult = new QueryResult();
		List queryMaps = commonDao_uc_read.queryMaps(sql, pageIndex, pageSize, null, null);
		queryResult.setData(queryMaps);
		queryResult.setCount(commonDao_uc_read.queryInt(sqlCount, null));
		return queryResult;
	}

    /**
     * 通过用户ids查询用户信息
     *
     * @param userIds
     * @param noDelay
     * @return
     * @throws DBException
     */
    @SuppressWarnings("rawtypes")
    public List findByUserIds(List<String> userIds,String delstatus, boolean noDelay) throws DBException {
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>(1);
        }
        int length = userIds.size();
        int i = 0;
        StringBuilder sb = new StringBuilder(90 * length);
        for (String userId : userIds) {
            sb.append("SELECT * FROM UcUser WHERE userId = '" + userId + "' ");
            if(!StringUtils.isEmpty(delstatus)) {
            	sb.append(" AND userDelstatus = '"+delstatus+"' ");
            }
            if (i != length - 1) {
                sb.append(" UNION ALL ");
            }
            i++;
        }
        if(noDelay) {
        	return this.commonDao_uc_write.queryMaps(sb.toString(), new Object[] {}, UcUser.class);
        }else {
        	return this.commonDao_uc_read.queryMaps(sb.toString(), new Object[] {}, UcUser.class);
        }
    }

 	/**
     * di系统删除用户
     *
     * @param userIds
     * @return
     * @throws DBException
     */
	public void diDelete(String userId) throws DBException {
		// TODO Auto-generated method stub
		commonDao_uc_write.execute("DELETE FROM UcUser where userId = :userId ",  MapUtils.toMap(new Object[][]{{"userId",userId},}));
	}

	/**
	 * 网站平台同步用户信息并注册到本地
	 * @param userSourceid
	 * @param address
	 * @return
	 * @throws Exception
	 */
	public UcUser syncUserToNet(String userSourceid) throws Exception {
		Map userMap = netGetUser(userSourceid);
 		String mobile=(String)userMap.get("mobile");
 		mobile=decrypt(mobile,"Mobile");
 		if(lockService==null){
 			lockService = MVCUtils.getBean(LockService.class);
 		}
 		String selfMark = UUID.randomUUID().toString();
 		String key = mobile + "YOULU" + "S";
 		try{
			Boolean flag = lockService.tryLock(key, selfMark, 60000L, 10000, 300L);
			if(flag){
				UcUser user = queryBySourceid$Flag$Avlstatus(userSourceid, "S", null);
				if(user != null) {
					return user;
				}else {
					return ucUserService.apiAdd(userMap,"YOULU",UcConst.SourceType.YOULU_WEB,"");
				}
			}else {
				UcUser user = queryBySourceid$Flag$Avlstatus(userSourceid, "S", null);
				if(user != null) {
					return user;
				}else {
					throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT, "用户不存在", null);
				}
			}
		}finally{
			lockService.unlock(key, selfMark);
		}
	}

	/**
	 * 调用网站获取用户信息
	 * @param userSourceid
	 * @return
	 * @throws Exception
	 */
	public Map netGetUser(String userSourceid) throws Exception {
		HttpClient httpClient = new HttpClient();
		String address = ServerSelecter.getBalancedUrl(UcConfig.Net.address);
 		httpClient.getHTML(address+"/"+UcConfig.Net.User.info+"?userId="+userSourceid+"&immediately=true", "utf-8");
 		if(httpClient.getCode() == 200) {
 			String reponseText = httpClient.getReponseText();
 	 		if(StringUtils.isEmpty(reponseText)) {
 	 			throw new ApplicationException(Errorcode.USER_NET_INVALIDE, "调用网站失败", null);
 	 		}
 	 		Map userMap =(Map) JSONUtils.toMap(reponseText).get("data");
 			return userMap;
		}else {
			String reponseErrorText = httpClient.getReponseErrorText();
			logger.info("[返回]["+address+"/"+UcConfig.Net.User.info+"]["+httpClient.getCode()+"]"+reponseErrorText);
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"调用网站失败:返回参数"+reponseErrorText,null);
		}
	}


	/**
	 * 网站注册用户同步延迟拉取用户
	 * @param map
	 * @param brandId
	 * @param sourceType
	 * @param userName
	 * @return
	 * @throws Exception
	 */
	@Transactional
	public UcUser apiAdd(Map map,String brandId,String sourceType, String userName) throws Exception{
		String nowDate = DateUtils.getNowDString();

		UcUser user					= commonDao_uc_write.getPOJOInstance(UcUser.class);
		//学习卡账户
		Map learnAccount 		= new HashMap();
		learnAccount.put("accountType", "LEARNCARD");
		//普通账户
		Map plainAccount 		= new HashMap();
		plainAccount.put("accountType", "BASE");
		//学员
		Map stu 				= new HashMap();

		String UserId=(String)map.get("userId");
		//手机
		if(map.containsKey("mobile")){
			String Mobile=(String)map.get("mobile");
			if(!isEmpty(Mobile)){
				Mobile=decrypt(Mobile,"mobile");
				if("error".equals(Mobile)){
					Mobile=null;
					user.setUserMemo("手机解密失败:"+(String)map.get("mobile"));
				}
				user.setUserMobile			(Mobile);
			}else{
				user.setUserMobile			(null);		//手机
			}
		}

		//用户名
		if(map.containsKey("realName")){
			String RealName=(String)map.get("realName");
			if(!isEmpty(RealName)){
				RealName=decrypt(RealName,"realName");
				if("error".equals(RealName)){
					RealName=null;
					user.setUserMemo("姓名解密失败:"+(String)map.get("realName"));
				}
				user.setUserName			(RealName);
			}else{
				user.setUserName			(null);		//用户名
			}
		}
		//如果传递的有用户姓名的话就修改用户姓名 不拿老网站的用户姓名
		if(!StringUtils.isEmpty(userName)) {
			user.setUserName(userName);		//用户名
		}
		//上次登录时间
		if(map.containsKey("lastLoginTime")){
			user.setUserLastlogindate	(nowDate);//最后登录时间
		}

		if(map.containsKey("lastLoginIP"))		user.setUserLastloginip		((String) map.get("lastLoginIP"));	//最后登录IP
		if(map.containsKey("passWord"))			user.setUserLoginpwd		((String) map.get("passWord"));		//密码
		if(map.containsKey("qq"))				user.setUserQq				((String) map.get("qq"));			//qq
		if(map.containsKey("userName"))			user.setUserLoginname		((String) map.get("userName"));		//登录名
		if(map.containsKey("certifiNo"))		user.setUserIdcard			((String) map.get("certifiNo"));	//证件号
		//证件类型  默认身份证
		if(!isEmpty(user.getUserIdcard())){
			user.setUserIdcardtype("I");
		}
		user.setUserCreateddate(nowDate);
		user.setUserModifieddate(nowDate);
//		if(map.containsKey("createdOn"))		user.setUserCreateddate		(fomatDate((String)map.get("createdOn")));
//		if(map.containsKey("modifiedOn"))		user.setUserModifieddate	(fomatDate((String)map.get("modifiedOn")));
		if(map.containsKey("areaId"))			user.setUserAreacode		((String) map.get("areaId"));		//地区编号
		if(map.containsKey("nickName"))			user.setUserNickname		((String) map.get("nickName"));		//昵称
		if(map.containsKey("postcode"))			user.setUserPostcode		((String) map.get("postcode"));		//邮编
		if(map.containsKey("address"))			user.setUserAddress			((String) map.get("address"));		//住址
		if(map.containsKey("avatar"))			user.setUserAvatar			((String) map.get("avatar"));		//个人信息头像
		if(map.containsKey("description"))		user.setUserMemo			((String) map.get("description"));	//描述
		if(map.containsKey("birthDay"))			user.setUserBirthday		((String) map.get("birthDay"));		//出生日期
		if(map.containsKey("autograph"))		user.setUserSignature		((String) map.get("autograph"));	//签名
		user.setUserSourceid		(UserId.toUpperCase());		//默认来源类型 NICELOO
		stu.put("studentSourceid", UserId.toUpperCase());
		stu.put("studentSourcetype", sourceType);
		user.setUserSourcetype		(sourceType);
		user.setUserFlag			("S");
		if(map.containsKey("isDisabled"))	{
			user.setUserAvlstatus		(((Boolean)map.get("isDisabled"))	?"N":"Y");//是否启用
			stu.put("studentAvlstatus", ((Boolean)map.get("isDisabled"))	?"N":"Y");
		}
		if(map.containsKey("isDeleted")){
			user.setUserDelstatus		(((Boolean)map.get("isDeleted"))	?"Y":"N");//是否删除
			stu.put("studentAvlstatus", ((Boolean)map.get("isDeleted"))		?"Y":"N");
		}	//用户类型   A:代表学员
		Object married = map.get("married");
		if(married == null) {
			user.setUserMarrystatus("N");
		}else {
			if(map.containsKey("married"))			user.setUserMarrystatus		("0"	.equals(map.get("married"))		?"N":"Y");//是否结婚	0:未婚 ;1 已婚
		}

		if(map.containsKey("virtualCurrency"))	learnAccount.put("accountAvlamount", String.valueOf(map.get("virtualCurrency")));
		if(map.containsKey("cash"))				plainAccount.put("accountAvlamount", String.valueOf(map.get("cash")));
		if(map.containsKey("allowMachineTimes"))stu.put("studentLicensecount"	, Integer.parseInt(map.get("allowMachineTimes").toString()));
		stu.put("userName"				, user.getUserName());
		if(map.containsKey("school"))			stu.put("studentEduschool"		, (String) map.get("school"));
		if(map.containsKey("professional"))		stu.put("studentEduprofessional", (String) map.get("professional"));
		if(map.containsKey("job"))				stu.put("studentWorkunit"		, (String) map.get("job"));
		if(map.containsKey("vipLevel"))			stu.put("studentViplevel"		, Integer.parseInt(map.get("vipLevel").toString()));
		if(map.containsKey("sex")){
			String sex = "";
			if(!isEmpty((String) map.get("sex"))){
				switch((String) map.get("sex")){
					case "0":sex ="M";
					break;
					case "1":sex ="F";
					break;
					case "男":sex ="M";
					break;
					case "女":sex ="F";
					break;
				}
			}
			user.setUserGender(sex);
		}
		if(map.containsKey("userType")){
			String UserType = String.valueOf(map.get("userType"));
			String ytype = null;
			if("1".equals(UserType)) ytype="C";
			if("2".equals(UserType)) ytype="T";
			stu.put("studentDeposittype", ytype);
		}

		//品牌
		if(map.containsKey("brandId")){
			String BrandId		=(String) map.get("brandId");
			String brandId_new	="";
			if("bfb5ca6a-6d18-4aac-8571-ec38fd2d3bdc".equals(BrandId)){
				brandId_new="YOULU";
			}else if("dbc475ed-3257-4df4-a6b3-ddea6be9ed32".equals(BrandId)){
				brandId_new="GEEDU";
			}else if("d5a8cb67-54ba-448c-bab9-4044d74081a7".equals(BrandId)){
				brandId_new="LIANXUE";
			}
			learnAccount.put("brandId", brandId_new);
			plainAccount.put("brandId", brandId_new);
		}

		if(map.containsKey("branchId"))	{
			String schoolId = findSchoolIdBySourceId((String)map.get("branchId"))== null?"NICELOO":findSchoolIdBySourceId((String)map.get("branchId"));
			stu.put("schoolId", schoolId);
		}

		//密码状态
		if(!isEmpty(user.getUserLoginpwd())){
			if(isEmpty(user.getUserLoginpwdstatus())){
				user.setUserLoginpwdstatus("S");//自设定
			}
		}else{
			user.setUserLoginpwdstatus(null);
		}

		//锁定状态
		if(isEmpty(user.getUserLockstatus())){
			user.setUserLockstatus("N");//未锁定
		}

		//手机
		if(!isEmpty(user.getUserMobile())){
			if(isEmpty(user.getUserMobilestatus())){
				user.setUserMobilestatus("Y");//已验证
			}
		}else{
			user.setUserMobilestatus(null);
		}

		//邮箱
		if(!isEmpty(user.getUserEmail())){
			if(isEmpty(user.getUserEmailstatus())){
				user.setUserEmailstatus("Y");//已验证
			}
		}else{
			user.setUserEmailstatus(null);
		}

		//判断用户源标识是否被已存在
		if(!isEmpty(user.getUserSourceid())) {
			if(ifExsistAvlBySource(user.getUserSourceid(),UserFlag.student)){
				throw new ApplicationException(UcConst.Errorcode.USER_SOURCEID_EXIST, "用户源标识已存在", null);
			}
		}

		Boolean again = false;
		//判断是否有同名用户

 		//密码转换(来自于其它系统的用户密码不转换)
 		if(isEmpty(user.getUserSourceid())){
 			if(!isEmpty(user.getUserLoginpwd())){
 	 			user.setUserLoginpwd(UserUtils.getEncryptPwd(user.getUserLoginpwd(), user.getUserLoginpwdtype()));
 	 		}
 		}


 		user.setUserId(CoreUtils.genSerial(commonDao_uc_write, "UcUser", "userId", "USER", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
 		user.setBrandId(brandId);
 		user.setUserLoginpwdtype("MD5");
 		commonDao_uc_write.save(user);
 		Map rtn=BeanUtils.bean2Map(user);

		 		//扩展信息

		Map	ext=new HashMap();

		ext.putAll(rtn);

		//代理商
		if(UserFlag.agent.equals(user.getUserFlag())){
			Map rtn0=(Map)CoreUtils.request(MVCUtils.requireProperty("sl.url"), "api/sl/agent/add", ext,"UC","SL");
			rtn.put("ext",rtn0);
		}


		String 		url_bd			= MVCUtils.requireProperty("bd.url");
		String 		url_fd			= MVCUtils.requireProperty("fd.url");
		stu.put("userId", user.getUserId());
		//学员类型(O:老学员;N:新学员)
		stu.put("studentType", "N");
		Map o = (Map)CoreUtils.request(url_bd,"api/bd/student/add", stu, "usercenter", "basedata");
		plainAccount.put("userId", user.getUserId());
		learnAccount.put("userId", user.getUserId());
		CoreUtils.request(url_fd,"api/fd/account/add", plainAccount, "usercenter", "fundsystem");
		CoreUtils.request(url_fd,"api/fd/account/add", learnAccount, "usercenter", "fundsystem");
		return user;
	}
	public static String fomatDate(String str) {
		if(StringUtils.isEmpty(str)) {
			return "";
		}
		str = Pattern.compile("[^0-9]").matcher(str).replaceAll("").trim();
        Date date = new Date();
        date.setTime(Long.parseLong(str));
		return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
	}


	/**
 	 * 修改用户
 	 * @param user 新用户
 	 * @param oUser 旧用户
 	 */
 	@Transactional(rollbackFor=Exception.class)
 	public Map diEdit(UcUser user,UcUser oUser,Map ext ,String ids) throws Exception{

 		user.setUserModifieddate(DateUtils.getNowDString());

		//U:更新用户
 		commonDao_uc_write.update(user);
// 		Map rtn=BeanUtils.bean2Map(user);
//
// 		if(!isEmpty(ids)){
// 			commonDao_uc_write.execute("DELETE FROM UcUserrole WHERE userId = ?", new Object[]{user.getUserId()});
// 	 		List<String> roleIds = ListUtils.toList(ids, ",");
// 	 		for (String str : roleIds) {
// 	 			UcUserrole urole = new UcUserrole();
// 	 			urole.setUseroleId(CoreUtils.genSerial(commonDao_uc_write, "UcUserrole", "useroleId", "USERROLE", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
// 	 			urole.setUserId(user.getUserId());
// 	 			urole.setRoleId(str);
// 	 			commonDao_uc_write.save(urole);
// 			}
// 		}
// 		//扩展信息
// 		if(ext==null){
//			ext=new HashMap();
//		}
//		ext.putAll(BeanUtils.bean2Map(user));
//
//
// 		//TODO 发送MQ
//
//		MQUtils.bindSubscriber();
//		user = commonDao_uc_write.queryObject("SELECT * FROM UcUser WHERE userId=?",new Object[]{user.getUserId()},UcUser.class);
//		mqService.publish(UcConst.Mqtype.USER_MQ_SUBCREABER_PROFILE_EDIT, JSONUtils.toString(MapUtils.toMap(new Object[][] {
//			{"userId"			,user.getUserId()			},
//			{"userName"			,user.getUserName()			},
//			{"userGender"		,user.getUserGender()		},
//			{"userNickname"		,NickNameReplace.QueryReplace(user.getUserNickname())},
//			{"userAvatar"		,user.getUserAvatar()		},
//			{"userQq"			,user.getUserQq()			},
//			{"userWeixin"		,user.getUserWeixin()		},
//			{"userMarrystatus"	,user.getUserMarrystatus()	},
//			{"userPostcode"		,user.getUserPostcode()		},
//			{"userAddress"		,user.getUserAddress()		},
//			{"userBirthday"		,user.getUserBirthday()		},
//			{"userAreacode"		,user.getUserAreacode()		},
//			{"userMobile"		,user.getUserMobile()		},
//		})));

		return null;
 	}


 	public static void main(String[] args) {
		String decrypt = decrypt("9E3B628355A183752E6470A6C9A66022", "");
		System.out.println(decrypt + "=====");
	}


	/**
	 * decrypt
	 * @param source
	 * @return
	 * @throws HandleException
	 */
	public static String decrypt(String source,String type) {
		try {
			return new String(DesUtils.decrypt(HexUtils.decodeHex(source.toCharArray()), "hrh5g72m"),"GBK");
		} catch (Exception e) {
			return "error";
		}
	}



	/**
 	 * 根据来源标识查学校标识
 	 * @param schoolSourceid 学校标识
 	 */
	public  String findSchoolIdBySourceId(String schoolSourceid) throws DBException {
		Map param = MapUtils.toMap(new Object[][] { { "schoolSourceid", schoolSourceid }});
		String sql = "SELECT schoolId FROM BdSchool WHERE schoolSourceid=:schoolSourceid ";
		return commonDao_bd_write.queryString(sql, param);
	}


	/**
 	 * 根据学校名称查学校标识
 	 * @param schoolName 学校标识
 	 */
	public  String findSchoolIdByName(String schoolName) throws DBException {
		Map param = MapUtils.toMap(new Object[][] { { "schoolName", schoolName }});
		String sql = "SELECT schoolId FROM BdSchool WHERE schoolName=:schoolName ";
		return commonDao_bd_write.queryString(sql, param);
	}

	/**
 	 * 根据用户标识查学员充值类型
 	 * @param userId 用户名称
 	 */
	public  String findStudentDeposittypeIdByUserId(String userId) throws DBException {
		Map param = MapUtils.toMap(new Object[][] { { "userId", userId }});
		String sql = "SELECT studentDeposittype FROM BdStudent WHERE userId=:userId ";
		String studentDeposittype = commonDao_bd_write.queryString(sql, param);
		if(StringUtils.isEmpty(studentDeposittype)) {
			studentDeposittype = "C";
		}
		return studentDeposittype;
	}

	/**
	 * 根据用户标识查询学员充值状态
	 * @param userIds
	 * @return
	 * @throws DBException
	 */
	public  Map findStudentDeposittypeIdByUserIds(String userIds) throws DBException {
		String sql = "SELECT userId,studentDeposittype FROM BdStudent WHERE userId IN ("+userIds+") ";
		List<Map> queryMaps = commonDao_bd_write.queryMaps(sql, null, null);
		HashMap<String,String> hashMap = new HashMap<>();
		for (Map map : queryMaps) {
			hashMap.put((String)map.get("userId"), (String)map.get("studentDeposittype"));
		}
		return hashMap;
	}



	/**
	 * 根据用户标识查询学员信息
	 * @param userId
	 * @return
	 * @throws DBException
	 */
	public Map findStudentByUserId(String userId) throws DBException {
		Map param = MapUtils.toMap(new Object[][] { { "userId", userId }});
		String sql = "SELECT studentDeposittype FROM BdStudent WHERE userId=:userId ";
		return commonDao_bd_write.queryMap(sql, param, null);
	}

	/**
	 * 获取UK
	 * @param userSourceid
	 * @param type
	 * @param phoneBrand
	 * @param machineCode
	 * @return
	 * @throws Exception
	 */
	public String getUk(String userSourceid, String type, String machineCode, String phoneBrand) throws Exception {
		HashMap<Object,Object> hashMap = new HashMap<>();
		hashMap.put("userId", userSourceid);
		if(UkType.WAP.equals(type)) {
			hashMap.put("uKeyType", 1);
		}else if(UkType.PC.equals(type)){
			hashMap.put("uKeyType", 2);
		}else if(UkType.APP.equals(type)) {
			hashMap.put("uKeyType", 3);
			hashMap.put("machineCode", machineCode);
			hashMap.put("phoneBrand", phoneBrand);
		}else if(UkType.PC_CLIENT.equals(type)) {
			hashMap.put("uKeyType", 4);
			hashMap.put("phoneBrand", phoneBrand);
		}
		try {
			HttpClient httpClient = new HttpClient();
			String params = JSONUtils.toString(hashMap);
			String address = ServerSelecter.getBalancedUrl(UcConfig.Net.address);
			logger.info("[请求]["+address+"/"+UcConfig.Net.User.ukey+"]"+params);
			httpClient.postHTML0("POST", address+"/"+UcConfig.Net.User.ukey, params, "utf-8", 6000, 6000, "application/json", null);
			int code = httpClient.getCode();
			if(code != 200) {
				String reponseErrorText = httpClient.getReponseErrorText();
				if(!StringUtils.isEmpty(reponseErrorText)) {
					logger.debug("网站返回的错误信息:"+reponseErrorText);
				}
			}
			String returndate = httpClient.getReponseText();
			logger.debug("网站返回的参数:"+returndate);
			Map map = JSONUtils.toMap(returndate);
			return (String) map.get("data");
		} catch (Exception e) {
            logger.error("o0o0o, uk异常", e);
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"从网站获取UK失败!",null);
		}
	}

	/**
	 * 获取用户id和用户名的MAP的对象
	 * @param ids
	 * @return
	 * @throws DBException
	 */
	public Map getUserIdNameMap(String[] ids) throws DBException {
		List<UcUser> list_d = list_d(ids);
		Map resoutMap = new HashMap<>();
		if(list_d != null && list_d.size() > 0) {
			for (UcUser user : list_d) {
				resoutMap.put(user.getUserId(), user.getUserLoginname());
			}
		}
		return resoutMap;
	}

	/**
	 * 身份认证
	 * @param ucUser
	 * @throws Exception
	 */
	@Transactional
	public void idcardIdentify(UcUser ucUser) throws Exception {
		//告诉CRM身份认证成功
		ucUser.setUserModifieddate(DateUtils.getNowDString());
		edit(ucUser);
		if(UcConst.UserFlag.student.equals(ucUser.getUserFlag())) {
			if(UcConfig.Crm.old_identity) {
				try {
					//老CRM下线后需要移除
					crmIdentify(ucUser);
				} catch (Exception e) {
					logger.debug("身份信息认证失败的用户信息:"+JSONUtils.toString(ucUser));
				}
			}
			HashMap<Object,Object> params = new  HashMap<>();
			params.put("userId",ucUser.getUserId());
			params.put("userName",ucUser.getUserName());
			params.put("studentModifier",ucUser.getUserId());
			//调用基础数据修改姓名
			CoreUtils.request(MVCUtils.requireProperty("bd.url"), "api/bd/student/username/edit", params, "UC", "BD");
			//推送mq消息通知其他服务
			mqService.publish(UcConst.Mqtype.USER_MQ_IDENTITY_EDIT, UserUtils.getMqHeard(UcConst.Mqtype.USER_MQ_IDENTITY_EDIT), MapUtils.toMap(new Object[][] {
				{"UserId",ucUser.getUserId()},
				{"Mobile",ucUser.getUserMobile()},
				{"BrandId",ucUser.getBrandId()},
				{"Name",ucUser.getUserName()},
				{"IdCardType",ucUser.getUserIdcardtype()},
				{"IdCard",ucUser.getUserIdcard()},
				{"CreateDate",DateUtils.getNowDString()},
				{"Pass",true}
			}));
		//企业用户认证身份信息
		}else if(UcConst.UserFlag.corp.equals(ucUser.getUserFlag())) {
			//通知企业端修改用户姓名
			CoreUtils.request(UcConfig.Cr.url, UcConfig.Cr.corpuser_edit, MapUtils.toMap(new Object[][] {
				{"cuId",ucUser.getUserId()},
				{"cuuserName",ucUser.getUserName()},
				{"cuuserMobile",ucUser.getUserMobile()}
			}), "UC", "CR");
		}
		//广播一条用户信息修改消息
		pushUserProfileEdit(ucUser,"");
	}

	/**
	 * CRM身份认证    老CRM下线后需要移除
	 * @param ucUser
	 * @throws Exception
	 */
	public void crmIdentify(UcUser ucUser) throws Exception {
		if(StringUtils.isEmpty(Crm.address)) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"CRM身份认证路径未配置",null);
		}
		HttpClient client = new HttpClient();
		HashMap<Object,Object> params = new HashMap<>();
		String userIdcardtype = ucUser.getUserIdcardtype();
		if(IDcardType.IDCARD.equals(userIdcardtype)) {
			params.put("IdType", 1);
		}else if(IDcardType.HGJMLW.equals(userIdcardtype)) {
			params.put("IdType", 3);
		}else if(IDcardType.TWJMLW.equals(userIdcardtype)){
			params.put("IdType", 4);
		}else if(IDcardType.OFFICER.equals(userIdcardtype)){
			params.put("IdType", 7);
		}else{
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"证件类型错误!",null);
		}
		params.put("CertifiNo", ucUser.getUserIdcard());
		params.put("Mobile", ucUser.getUserMobile());
		params.put("RealName", ucUser.getUserName());
		String brandId = ucUser.getBrandId();
		if("YOULU".equals(brandId)) {
			brandId = Brand.YOULU;
		}else if("GEEDU".equals(brandId)) {
			brandId = Brand.GEEDU;
		}else if("LIANXUE".equals(brandId)) {
			brandId = Brand.LIANXUE;
		}
		params.put("BrandId",brandId);
		HashMap<String,String> heards = new HashMap<>();
		heards.put("sign", getSing());
		String contentType="application/json; charset=UTF-8";
		String reqStr = JSONUtils.toString(params);
		logger.debug("CRM请求参数:"+reqStr);
		client.postHTML0("PUT", ServerSelecter.getBalancedUrl(Crm.address)+"/"+Crm.Api.modify_Information+"/"+ucUser.getUserMobile(), reqStr, "UTF-8", 15000, 15000, contentType, heards);
		String reponseText = client.getReponseText();
		Map map = JSONUtils.toMap(reponseText);
		logger.debug("CRM返回参数:"+reponseText);
		int code = (int) map.get("Code");
		if(code != 1) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,(String) map.get("Msg"),null);
		}
	}

	/**
	 * 根据用户标识获取品牌标识
	 * @return
	 * @throws DBException
	 */
	public String getBrandIdByUserid(String userId) throws DBException {
		Map param = MapUtils.toMap(new Object[][] { { "userId", userId }});
		String sql = "select b.brandId from BdStudentbrand b LEFT JOIN BdStudent s ON b.studentId = s.studentId WHERE s.userId = :userId";
		return commonDao_bd_write.queryString(sql, param);
	}

	/**
	 * 获取CRM的验签  老CRM下线后需要移除
	 */
	public String getSing() throws Exception {
		String publicKeyFromXml = CSharpOrJavaUtil.publicKeyFromXml(Crm.Rsa.public_key);
		Random random = new Random();
		int nextInt = random.nextInt(1000);
		String wait = "whoami" + "&" + (System.currentTimeMillis()/1000) + "&" + nextInt;
		return CRMRSAUtils.encrypt(wait, CRMRSAUtils.getPublicKey(publicKeyFromXml));
	}

	/**
	 * 根据用户手机号查询用户信息
	 * @param brandMobilesMap  手机号集合
	 * @return
	 * @throws DBException
	 */
	public List<UcUser> findByUserMoblies(HashMap<String, ArrayList<String>> brandMobilesMap) throws DBException {
		ArrayList<UcUser> resoutList = new ArrayList<>();
		Set<String> keySet = brandMobilesMap.keySet();
		for (String brandId : keySet) {
			String sql = "SELECT * FROM UcUser WHERE userMobile IN ("+SQLUtils.getIn(brandMobilesMap.get(brandId))+") AND userDelstatus = 'N' AND brandId = '"+brandId+"' AND userFlag = 'S'";
			List<UcUser> queryMaps = commonDao_uc_write.queryObjects(sql, null, UcUser.class);
			resoutList.addAll(queryMaps);
		}
		return resoutList;
	}

	/**
	 * crm添加用户
	 * @param user 用户信息
	 * @param isMust
	 * @param sourceChannel
	 * @param brandId
	 * @return
	 * @throws Exception
	 */
	public UcUser crm_add(UcUser user, String isMust) throws Exception{
		UcUser moblieUser = queryByMobile$Flag$Avlstatus$BrandId(user.getUserMobile(), UserFlag.student, null,user.getBrandId());
		if(moblieUser != null) {
			return moblieUser;
		}

		if(lockService==null){
 			lockService = MVCUtils.getBean(LockService.class);
 		}
 		String selfMark = UUID.randomUUID().toString();
 		String key = user.getUserMobile() + user.getBrandId() + "S";
 		try{
			Boolean flag = lockService.tryLock(key, selfMark, 60000L, 10000, 300L);
			if(flag){
				String userLoginpwd = user.getUserLoginpwd();
				Map userMap = netRegistNoEs(user.getUserMobile(),userLoginpwd,user.getUserName());
				UcUser queryUser = queryBySourceid$Flag$Avlstatus((String)userMap.get("userId"), "S", null);
				if(queryUser != null) {
					//如果是Y需要更新用户姓名
					if("Y".equals(isMust)) {
						updateName(queryUser);
					}
					return queryUser;
				}else {
					String userName = "";
					if("Y".equals(isMust)) {
						userName = user.getUserName();
					}
					return ucUserService.apiAdd(userMap,user.getBrandId(),user.getUserSourcetype(),userName);
				}
			}else {
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"注册失败请重试",null);
			}
		}finally{
			lockService.unlock(key, selfMark);
		}

	}

	/**
	 * 获取用户信息
	 * @param userMobile
	 * @param student
	 * @param object
	 * @param brandId
	 * @return
	 * @throws DBException
	 */
	private UcUser queryByMobile$Flag$Avlstatus$BrandId(String userMobile, String student, Object object,
			String brandId) throws DBException {
		String sql="SELECT * FROM UcUser WHERE userMobile=:userMobile AND userFlag=:userFlag  AND userDelstatus = 'N' AND brandId=:brandId";
		Map param = MapUtils.toMap(new Object[][] {
			{"userFlag",student},
			{"userMobile",userMobile},
			{"brandId",brandId}
		});
		return commonDao_uc_read.queryObject(sql,param,UcUser.class);
	}

	/**
	 * 获取本机ip地址
	 * @return
	 * @throws UnknownHostException
	 */
	public static String getHostIp() throws UnknownHostException {
		if(StringUtils.isEmpty(Sys.ip)) {
			InetAddress address = InetAddress.getLocalHost();
	        System.out.println(address);//获取计算机名称和ip地址
	        Sys.ip = address.getHostAddress();
		}
		return Sys.ip;
	}

	public String getSchoolId(UcUser ucUser) throws Exception {
		Map param = MapUtils.toMap(new Object[][] { { "userId", ucUser.getUserId() }});
		String sql = "select schoolId from BdStudent WHERE userId = :userId";
		String schoolId = commonDao_bd_write.queryString(sql, param);
		if(StringUtils.isEmpty(schoolId)) {
			schoolId = getSchoolIdBySourceId(ucUser.getUserSourceid());
		}
		if(StringUtils.isEmpty(schoolId)) {
			try {
				Map netUser = netRegistNoEs(ucUser.getUserMobile(),"");
				String sourceId = (String) netUser.get("userId");
				schoolId = getSchoolIdBySourceId(sourceId);
			} catch (Exception e) {
				logger.debug(e.getMessage(),e);
			}
		}
		return schoolId;
	}

	private String getSchoolIdBySourceId(String userSourceid) throws DBException {
		if(StringUtils.isEmpty(userSourceid)) {
			return "";
		}
		String schoolSourceId = getUserByNet(userSourceid);
		Map param = MapUtils.toMap(new Object[][] { { "schoolSourceid", schoolSourceId }});
		String sql = "select schoolId from BdSchool WHERE schoolSourceid = :schoolSourceid";
		return commonDao_bd_write.queryString(sql, param);
	}



	/**
	 * 调用网站平台接口获取用户数据
	 */
	private String getUserByNet(String sourceId){
		try {
			HttpClient httpClient = new HttpClient();
			String address = ServerSelecter.getBalancedUrl(UcConfig.Net.address);
			logger.info("[请求]["+address+"/"+UcConfig.Net.User.info+"]"+"userId="+sourceId);
			httpClient.getHTML(address+"/"+UcConfig.Net.User.info+"?userId="+sourceId+"&immediately=true", "utf-8");
			if(httpClient.getCode() == 200) {
				String reponseText = httpClient.getReponseText();
				logger.info("[返回]["+address+"/"+UcConfig.Net.User.info+"]["+httpClient.getCode()+"]"+reponseText);
				Map user = (Map) JSONUtils.toMap(reponseText).get("data");
				return (String) user.get("branchId");
			}else {
				String reponseErrorText = httpClient.getReponseErrorText();
				logger.info("[返回]["+address+"/"+UcConfig.Net.User.info+"]["+httpClient.getCode()+"]"+reponseErrorText);
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"调用网站失败:返回参数"+reponseErrorText,null);
			}
		} catch (Exception e) {
			logger.debug(e.getMessage(), e);
			return "";
		}
	}

	/**
     * 通过用户userSourceids查询用户信息
     *
     * @param userIds
     * @return
     * @throws DBException
     */
    @SuppressWarnings("rawtypes")
    public List findByuserSourceids(List<String> userSourceids) throws DBException {
        if (CollectionUtils.isEmpty(userSourceids)) {
            return new ArrayList<>(1);
        }
        int length = userSourceids.size();
        int i = 0;
        StringBuilder sb = new StringBuilder(90 * length);
        for (String userSourceid : userSourceids) {
            sb.append("SELECT * FROM UcUser WHERE userSourceid = '" + userSourceid + "' AND userDelstatus = 'N' ");
            if (i != length - 1) {
                sb.append(" UNION ALL ");
            }
            i++;
        }
        return this.commonDao_uc_read.queryMaps(sb.toString(), new Object[] {}, UcUser.class);
    }

    /**
     * 删除用户信息
     * @param user
     * @throws DBException
     */
	public void delete(UcUser user) throws DBException {
		user.setUserModifieddate(DateUtils.getNowDString());
		user.setUserDelstatus(Delstatus.YES);
		user.setUserAvlstatus(Avlstatus.NO);
		commonDao_uc_write.update(user);
	}


	/**
	 * 根据用户手机号和用户类型查询用户信息
	 * @param userMobiles
	 * @param userFlag
	 * @return
	 */
	public List findByuserMobiles(List<String> userMobiles, String userFlag ,String brandId) throws DBException {
		String sql = "SELECT userId,userMobile,userFlag,userLoginname,userLastlogindate FROM UcUser WHERE "
						+ "usermobile IN(" + SQLUtils.getIn(userMobiles) + ") "
						+ "AND userDelstatus = 'N' "
						+ "AND userFlag = '"+userFlag+"'";
		if(!StringUtils.isEmpty(brandId)) {
			sql = sql + "AND brandId = '"+brandId+"' ";
		}
		return commonDao_uc_read.queryMaps(sql, new Object[] {}, UcUser.class);
	}

	/**
	 * 在上面的基础上，增加几个字段,用户名和idcard
	 * @param userMobiles
	 * @param userFlag
	 * @param brandId
	 * @return
	 * @throws DBException
	 */
	public List findByuserMobilesMore(List<String> userMobiles, String userFlag, String brandId) throws DBException {
		String sql = "SELECT userId,userMobile,userFlag,userLoginname,userName,userIdcard,userLastlogindate FROM UcUser WHERE "
				+ "usermobile IN(" + SQLUtils.getIn(userMobiles) + ") "
				+ "AND userDelstatus = 'N' "
				+ "AND userFlag = '"+userFlag+"' ";
		if(!StringUtils.isEmpty(brandId)) {
			sql = sql + "AND brandId = '"+brandId+"' ";
		}
		return commonDao_uc_read.queryMaps(sql, new Object[] {}, UcUser.class);
	}

	/**
	 * 根据用户手机号和用户类型查询用户信息 单个
	 * @param userMobile
	 * @param userFlag
	 * @return
	 */
	public Map findByuserMobile(String userMobile, String userFlag) throws DBException {
		String sql = "SELECT * FROM UcUser WHERE "
						+ "usermobile = '"+userMobile+"' "
						+ "AND userDelstatus = 'N' "
						+ "AND userFlag = '"+userFlag+"'";

		return commonDao_uc_read.queryMap(sql, new Object[] {}, UcUser.class);
	}

	/**
	 * 启用禁用账号
	 * @throws DBException
	 */
	public void edituserAvlstatus(List<String> userIds,String userAvlstatus) throws DBException {
		if (CollectionUtils.isEmpty(userIds)) {
			return;
        }

		String sql = "UPDATE UcUser SET userAvlstatus = '"+userAvlstatus+"',userModifieddate = '"+DateUtils.getNowDString()+"'"
				+"WHERE userDelstatus = '"+UcConst.Delstatus.NO+"' AND userId IN("+SQLUtils.getIn(userIds)+")";
        commonDao_uc_write.execute(sql, new HashMap<>());
	}

	/**
	 * 根据用户登录名获取用户信息
	 * @param userLoginnameList
	 * @param userFlag
	 * @param noDelay
	 * @return
	 * @throws DBException
	 */
	public Object findByuserLoginName(List<String> userLoginnameList, String userFlag, boolean noDelay) throws DBException {
		String sql = "SELECT userId,userMobile,userFlag,userLoginname FROM UcUser WHERE "
				+ "userLoginname IN(" + SQLUtils.getIn(userLoginnameList) + ") "
				+ "AND userDelstatus = 'N' "
				+ "AND userAvlstatus = 'Y' "
				+ "AND userFlag = '"+userFlag+"'";
		if(noDelay) {
			return commonDao_uc_write.queryMaps(sql, new Object[] {}, UcUser.class);
		}else {
			return commonDao_uc_read.queryMaps(sql, new Object[] {}, UcUser.class);
		}
	}

	/**
	 * 根据某一列查询
	 * @param colsMap
	 * @return
	 * @throws DBException
	 */
	public UcUser findByCols(Map<String,Object> colsMap) throws DBException {
		StringBuffer sql = new StringBuffer("SELECT * FROM UcUser WHERE 1=1");
		Map<String,Object> params = new HashMap<String, Object>();
		for(String col : colsMap.keySet()) {
			//判断是否有值
			Object value = colsMap.get(col);
			if(value == null) {
				continue;
			}
			if(StringUtils.isEmpty(value.toString())) {
				continue;
			}
			params.put(col, value);
			sql.append(" and ").append(col).append("=");
			//验证value的类型
			if(value instanceof java.lang.String) {
				sql.append("'").append(value.toString()).append("'");
			}else {
				sql.append(value);
			}
		}
		return commonDao_uc_read.queryObject(sql.toString(), params, UcUser.class);
	}

	/**
	 * 处理用户信息
	 * @param rtn
	 * @throws Exception
	 */
	public void executeUser(Map rtn) throws Exception {
		String userLoginname = (String) rtn.get("userLoginname");
		String userSourceid = (String) rtn.get("userSourceid");
		String userId = (String) rtn.get("userId");
		if(StringUtils.isEmpty(userLoginname)&&!StringUtils.isEmpty(userSourceid)) {
			Map userMap = netGetUser(userSourceid);
			userLoginname = (String) userMap.get("userName");
			commonDao_uc_write.execute("UPDATE  UcUser SET userLoginname = ? WHERE userId = ?", new Object[]{userLoginname,userId});
			rtn.put("userLoginname", userLoginname);
		}
	}

	/**
	 * 老网站注册不推送crm
	 * @param phone
	 * @return
	 * @throws Exception
	 */
	public Map netRegistNoEs(String phone,String userLoginpwd) throws Exception {
		return netRegistNoEs(phone, userLoginpwd, "");
	}

	/**
	 * 老网站注册
	 * @param phone
	 * @param userLoginpwd
	 * @param userName
	 * @return
	 * @throws Exception
	 */
	public Map netRegistNoEs(String phone,String userLoginpwd,String userName) throws Exception {
		HttpClient httpClient = new HttpClient();
		HashMap<String,String> params = new HashMap<>();
		params.put("phone", phone);
		if(!StringUtils.isEmpty(userLoginpwd)) {
			params.put("password", userLoginpwd);
		}
		if(!StringUtils.isEmpty(userName)) {
			params.put("realName", userName);
		}
		String address = ServerSelecter.getBalancedUrl(UcConfig.Net.address);
		logger.info("[请求]["+address+"/"+UcConfig.Net.User.regist+"]"+JSONUtils.toString(params));
		httpClient.postHTML0("POST", address+"/"+UcConfig.Net.User.regist, UserUtils.joinFormdata(params), "UTF-8", 15*1000, 15*1000 ,"multipart/form-data; boundary=" + UserUtils.BOUNDARY,null);
		String reponseText = httpClient.getReponseText();
 		if(httpClient.getCode() != 200) {
 			String reponseErrorText = httpClient.getReponseErrorText();
 			logger.info("[返回]["+address+"/"+UcConfig.Net.User.regist+"]["+httpClient.getCode()+"]"+reponseErrorText);
 			throw new ApplicationException(Errorcode.USER_NET_INVALIDE, "调用老网站失败", null);
 		}else {
 			logger.info("[返回]["+address+"/"+UcConfig.Net.User.regist+"]["+httpClient.getCode()+"]"+reponseText);
 		}
 		Map userMap =(Map) JSONUtils.toMap(reponseText).get("data");
		return userMap;
	}

	/**
	 *
	 * @param userMobile
	 * @param brandId
	 * @param student
	 * @return
	 * @throws Exception
	 */
	@Transactional
	public Map nocheck_register(String userMobile, String brandId, String student ,String ip ,String sourceChannel) throws Exception {
		//网站注册用户信息
		UcUser resoutUser = apiAdd(netRegistNoEs(userMobile,""),brandId,UcConst.SourceType.YOULU_WEB,"");
		//默认密码
		resoutUser.setUserLoginpwd(SHA1Utils.encrypt(UcConfig.User.loginpwd_default));
		resoutUser.setUserLoginpwdtype(UcConst.UserLoginpwdtype.SHA1);
		resoutUser.setUserLoginpwdstatus("I");
		commonDao_uc_write.update(resoutUser);
		return BeanUtils.bean2Map(resoutUser);
	}

	/**
	 * 模糊查询用户信息
	 * @param params
	 * @return
	 * @throws DBException
	 */
	public List likeQuery(HashMap<String, String> params) throws DBException {
		StringBuffer sql = new StringBuffer("SELECT * FROM UcUser WHERE userDelstatus = 'N' ");
		String userName = params.get("userName");
		String userLoginname = params.get("userLoginname");
		String userMobile = params.get("userMobile");
		String userFlag = params.get("userFlag");


		if(!StringUtils.isEmpty(userName)) {
			sql.append(" and ").append("userName").append(" like ");
			sql.append("'").append(userName).append("%'");
		}
		if(!StringUtils.isEmpty(userLoginname)) {
			sql.append(" and ").append("userLoginname").append(" like ");
			sql.append("'").append(userLoginname).append("%'");
		}
		if(!StringUtils.isEmpty(userMobile)) {
			sql.append(" and ").append("userMobile").append(" like ");
			sql.append("'").append(userMobile).append("%'");
		}
		if(!StringUtils.isEmpty(userFlag)) {
			sql.append(" and ").append("userFlag").append(" = ");
			sql.append("'").append(userFlag).append("'");
		}

		return commonDao_uc_read.queryMaps(sql.toString(), new Object[] {}, UcUser.class);
	}

	/**
	 * 老网站密码修改
	 * @param userSourceid
	 * @param passWord
	 * @throws Exception
	 */
	public void netPwdEdit(String userSourceid,String passWord) throws Exception {
		if(StringUtils.isEmpty(userSourceid)) {
			return;
		}
		HttpClient httpClient = new HttpClient();
		HashMap<String,String> params = new HashMap<>();
		params.put("userId", userSourceid);
		params.put("password", passWord);
		logger.debug("调用网站参数:userSourceid:"+userSourceid+";password:"+passWord);
		String address = ServerSelecter.getBalancedUrl(UcConfig.Net.address);
		httpClient.postHTML0("POST", address+"/"+UcConfig.Net.User.pwd_edit, UserUtils.joinFormdata(params), "UTF-8", 15*1000, 15*1000 ,"multipart/form-data; boundary=" + UserUtils.BOUNDARY,null);
 		if(httpClient.getCode()!=200) {
 			String reponseErrorText = httpClient.getReponseErrorText();
 			Map map = JSONUtils.toMap(reponseErrorText);
 			String message = (String) map.get("message");
 			logger.error(reponseErrorText);
 			throw new ApplicationException(Errorcode.USER_NET_INVALIDE, message, null);
 		}else {
 			String reponseText = httpClient.getReponseText();
 			logger.debug("调用网站返回数据:"+reponseText);
 			if(StringUtils.isEmpty(reponseText)) {
 				throw new ApplicationException(Errorcode.USER_NET_INVALIDE, "调用网站失败", null);
 			}
 		}
	}

	/**
	 * 根据用户标识获取用户姓名手机号信息
	 * @param userIds
	 * @return
	 * @throws DBException
	 */
	public List findNameMobileByUserIds(List<String> userIds) throws DBException {

        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>(1);
        }
        int length = userIds.size();
        int i = 0;
        StringBuilder sb = new StringBuilder();
        for (String userId : userIds) {
            sb.append("SELECT userId,userName,userMobile FROM UcUser WHERE userId = '" + userId + "' ");
            if (i != length - 1) {
                sb.append(" UNION ALL ");
            }
            i++;
        }
        return this.commonDao_uc_read.queryMaps(sb.toString(), new Object[] {}, UcUser.class);
	}

	/**
	 * 用户姓名修改
	 * @param userId
	 * @param userName
	 * @throws Exception
	 */
	@Transactional(rollbackFor = Exception.class,global=true)
	public void nameEdit(String userId, String userName) throws Exception {
		UcUser ucUser = ucUserService.queryById(userId);
		if(ucUser == null) {
			throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT,"用户不存在",null);
		}
		if(!UcConst.UserFlag.student.equals(ucUser.getUserFlag())) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"只有学生用户可以修改姓名",null);
		}
		String userIdcardstatus = ucUser.getUserIdcardstatus();
		if(UcConst.UserIDcardStatus.SUCCESS.equals(userIdcardstatus)) {
			throw new ApplicationException(UcConst.Errorcode.USER_IDCARD_EXIST,"身份信息已存在",MapUtils.toMap(new Object[][] {
				{"userName",ucUser.getUserName()},
				{"userIdcard",ucUser.getUserIdcard()},
				{"userIdcardtype",ucUser.getUserIdcardtype()}
			}));
		}
		ucUser.setUserName(userName);
		ucUserService.edit(ucUser);
		HashMap<Object,Object> params = new  HashMap<>();
		params.put("userId",ucUser.getUserId());
		params.put("userName",ucUser.getUserName());
		params.put("studentModifier",ucUser.getUserId());
		//调用基础数据修改姓名
		CoreUtils.request(MVCUtils.requireProperty("bd.url"), "api/bd/student/username/edit", params, "UC", "BD");
		pushUserProfileEdit(ucUser, "");
	}

	/**
	 * 更新姓名
	 * @param existUser
	 * @throws Exception
	 */
	@Transactional(rollbackFor = Exception.class,global=true)
	public void updateName(UcUser existUser) throws Exception {
		String userIdcardstatus = existUser.getUserIdcardstatus();
		//未身份认证就可以修改姓名
		if(!UcConst.UserIDcardStatus.SUCCESS.equals(userIdcardstatus)) {
			HashMap<Object,Object> params = new  HashMap<>();
			params.put("userId",existUser.getUserId());
			params.put("userName",existUser.getUserName());
			params.put("studentModifier",existUser.getUserId());
			CoreUtils.request(MVCUtils.requireProperty("bd.url"), "api/bd/student/username/edit", params, "UC", "BD");
			commonDao_uc_write.execute("UPDATE UcUser SET  userName= ? WHERE userId=? ",new Object[]{existUser.getUserName(),existUser.getUserId()});
			//广播一条用户信息修改的mq消息
			pushUserProfileEdit(existUser,"");
		}
	}

	/**
	 * 推送用户信息变更
	 * @param sourceType
	 * @param userId
	 * @throws Exception
	 */
	public void pushUserProfileEdit(UcUser user, String sourceType) throws Exception {
		//传递header为了支持crm现有框架后续需要crm那边支持
		String userIdcardstatus = user.getUserIdcardstatus();
		if(StringUtils.isEmpty(userIdcardstatus)){
			userIdcardstatus = "N";
		}
		mqService.publish(UcConst.Mqtype.USER_MQ_SUBCREABER_PROFILE_EDIT, UserUtils.getMqHeard(UcConst.Mqtype.USER_MQ_SUBCREABER_PROFILE_EDIT),JSONUtils.toString(MapUtils.toMap(new Object[][] {
			{"userId"			,user.getUserId()			},
			{"userName"			,user.getUserName()			},
			{"userGender"		,user.getUserGender()		},
			{"userNickname"		,NickNameReplace.QueryReplace(user.getUserNickname())},
			{"userAvatar"		,user.getUserAvatar()		},
			{"userQq"			,user.getUserQq()			},
			{"userWeixin"		,user.getUserWeixin()		},
			{"userMarrystatus"	,user.getUserMarrystatus()	},
			{"userPostcode"		,user.getUserPostcode()		},
			{"userWorkunit"		,user.getUserWorkunit()		},//用户工作单位名称
			{"userWorkyear"		,user.getUserWorkyear()		},//用户工作年限
			{"userEdulevel"		,user.getUserEdulevel()		},//用户学历
			{"userEdumajor"		,user.getUserEdumajor()		},//用户专业
			{"userEduschool"	,user.getUserEduschool()	},
			{"userAddress"		,user.getUserAddress()		},
			{"userBirthday"		,user.getUserBirthday()		},
			{"userAreacode"		,user.getUserAreacode()		},
			{"userMobile"		,user.getUserMobile()		},
			{"brandId"			,user.getBrandId()},
			{"userIdcardtype"	,user.getUserIdcardtype()},
			{"userIdcardstatus"	,userIdcardstatus},
			{"userIdcard"		,user.getUserIdcard()},
			{"sourceType"		,sourceType},
			{"timeStamp"		,System.currentTimeMillis()}
		})));
	}


	/**
	 * 获取用户推送信息
	 * @param userIds
	 * @return
	 * @throws DBException
	 */
	public Map push_list(String[] userIds) throws DBException {
		String sql = "";
		for (int i = 0; i < userIds.length; i++) {
			if(i != 0) {
				sql+=" UNION ";
			}
			sql += " SELECT userId,userName,userLoginname,brandId,userMobile FROM UcUser where userId = '"+userIds[i]+"' ";
		}
		List<Map> queryMaps = commonDao_uc_read.queryMaps(sql, null, null);
		if(!ObjectUtils.isEmpty(queryMaps)) {
			for (Map map : queryMaps) {
				String userId = (String) map.get("userId");
				map.put("userIdEncrypt", UserUtils.change10TO62(userId.replaceAll("[a-zA-Z]", "")));
			}
		}
		return MapUtils.toMap(new Object[][] {
			{"data",queryMaps}
		});
	}

	/**
	 * 根据用户邮箱和用户类型查询用户信息
	 *
	 * @param userEmails 用户邮箱集合
	 * @param userFlag   用户类型
	 * @param moreInfo   是否需要 userName和userIdcard [枚举] Y N
	 * @return
	 */
	public List findByUserEmails(List<String> userEmails, String userFlag, String moreInfo) throws DBException {
		StringBuilder sqlBuilder = new StringBuilder();
		sqlBuilder.append(" SELECT ");
		sqlBuilder.append("     userId, userMobile, userEmail, userFlag, userLoginname, userLastlogindate ");
		if ("Y".equals(moreInfo)) {
			sqlBuilder.append(" , userName, userIdcard ");
		}
		sqlBuilder.append(" FROM ");
		sqlBuilder.append("     UcUser ");
		sqlBuilder.append(" WHERE ");
		sqlBuilder.append("     userEmail IN (" + SQLUtils.getIn(userEmails) + ") ");
		sqlBuilder.append("     AND userDelstatus = 'N' ");
		sqlBuilder.append("     AND userFlag = '" + userFlag + "' ");
		return commonDao_uc_read.queryMaps(sqlBuilder.toString(), new Object[]{}, UcUser.class);
	}

	/**
	 * 根据用户标识获取用户名称（批量）
	 * @param idlist 用户标识列表
	 * @param avlStatus 可用状态
	 * @throws DBException
	 */
	public List getUserNameListByUserIds(String[] idlist, String avlStatus) throws DBException {
		String in = SQLUtils.getIn(idlist);
		StringBuilder sqlBuilder = new StringBuilder();
		sqlBuilder.append(" SELECT ");
		sqlBuilder.append("     userId, ");
		sqlBuilder.append("     userName ");
		sqlBuilder.append(" FROM ");
		sqlBuilder.append("     UcUser ");
		sqlBuilder.append(" WHERE ");
		sqlBuilder.append("     userId IN ( " + SQLUtils.getIn(idlist) +" ) ");
		sqlBuilder.append("     AND userDelstatus = 'N' ");
		if (!StringUtils.isEmpty(avlStatus)) {
			sqlBuilder.append(" AND userAvlstatus = '" + avlStatus + "' ");
		}
		return commonDao_uc_read.queryMaps(sqlBuilder.toString(), null , null);
	}

	/**
	 * 获取用户信息
	 * <p>
	 * 用于企业学习平台同步数据
	 * <p>
	 *
	 * maning
	 */
	@SuppressWarnings("unchecked")
	public List<Map<String, Object>> queryUcUser4CorpSync(List<String> userIds) throws DBException {
		String in = SQLUtils.getIn(userIds);
		String sql = String.format("select * from UcUser where userId in (%s)", in);
		return commonDao_uc_read.queryMaps(sql, new Object[0], new Object[0]);
	}

    @Transactional(rollbackFor = Exception.class)
    public void unsubscribe(UcUser user) throws Exception {
        user.setUserDelstatus("Y");
        user.setUserAvlstatus("N");
        String now = DateUtils.getNowDString();
        user.setUserModifieddate(now);
        user.setUserModifiedtype("unsubscribe");
        commonDao_uc_write.update(user);

        mqService.publish(
                UcConst.Mqtype.USER_MQ_UNSUBSCRIBE,
                UserUtils.getMqHeard(UcConst.Mqtype.USER_MQ_UNSUBSCRIBE),
                JSONUtils.toString(MapUtils.toMap(new Object[][]{
                        {"userId", user.getUserId()},
                        {"userSourceid", user.getUserSourceid()},
                        {"userMobile", user.getUserMobile()}
                })));
    }

	/**
	 * 通过用户ids查询用户信息
	 * @param userIds
	 * @param delstatus
	 * @param noDelay
	 * @return
	 * @throws DBException
	 */
	@SuppressWarnings("rawtypes")
	public List findByUserIdsNoAuth(List<String> userIds,String delstatus, boolean noDelay) throws DBException {
		if (CollectionUtils.isEmpty(userIds)) {
			return new ArrayList<>(1);
		}
		int length = userIds.size();
		int i = 0;
		StringBuilder sb = new StringBuilder(90 * length);
		for (String userId : userIds) {
			sb.append("SELECT * FROM UcUser WHERE userId = '" + userId + "' ");
			sb.append(" AND brandId NOT in  ('SYSTEM','ADMIN') ");
			if(!StringUtils.isEmpty(delstatus)) {
				sb.append(" AND userDelstatus = '"+delstatus+"' ");
			}
			if (i != length - 1) {
				sb.append(" UNION ALL ");
			}
			i++;
		}
		return this.commonDao_uc_read.queryMaps(sb.toString(), new Object[] {}, UcUser.class);
	}

	/**
	 * 根据用户ID和可用状态修改用户的可用状态
	 * @param user
	 * @param userAvlstatus
	 * @throws DBException
	 */
	public void modifyUserAvailabilityStatus(UcUser user,String userAvlstatus) throws DBException{
		String sql = "UPDATE UcUser SET userAvlstatus = '"+userAvlstatus+"',userModifieddate = '"+DateUtils.getNowDString()+"'"
				+"WHERE userId = '" + user.getUserId() +"'";
		commonDao_uc_write.execute(sql, new HashMap<>());
	}

    /**
     * @throws Exception 用户不存在
     */
    public Map getIdsByParam(String userIdCard, String userName, String userMobile, String userAvlstatus, String userDelstatus) throws Exception {
        StringBuilder sql = new StringBuilder("select userId from UcUser where 1 = 1");

        if (!isEmpty(userIdCard)) {
            sql.append(" and userIdCard = '" + userIdCard + "'");
        }
        if (!isEmpty(userName)) {
            sql.append(" and userName = '" + userName + "'");
        }
        if (!isEmpty(userMobile)) {
            sql.append(" and userMobile = '" + userMobile + "'");
        }
        if (!isEmpty(userAvlstatus)) {
            sql.append(" and userAvlstatus = '" + userAvlstatus + "'");
        }
        if (!isEmpty(userDelstatus)) {
            sql.append(" and userDelstatus = '" + userDelstatus + "'");
        }

        List<Map> userIds = commonDao_uc_read.queryMaps(sql.toString(), null, null);
        if (userIds == null || userIds.size() == 0) {
            throw new ApplicationException("用户不存在");
        }
        List ids = new ArrayList();
        for (Map map : userIds) {
            ids.add(map.get("userId"));
        }
        Map<String, List> result = new HashMap<>();
        result.put("userIds", ids);
        return result;
    }
}