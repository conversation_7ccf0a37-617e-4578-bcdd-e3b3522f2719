package com.niceloo.uc.service;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.db.Transactional;
import org.nobject.common.db.member.SqlGroup;
import org.nobject.common.db.member.SqlOE;
import org.nobject.common.db.member.SqlOrder;
import org.nobject.common.db.member.SqlSelect;
import org.nobject.common.db.member.SqlWE;
import org.nobject.common.db.member.SqlWhere;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.bean.CoreConst.ErrorCode;
import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.DoResult;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.uc.common.UcConst.Errorcode;
import com.niceloo.uc.model.UcConfig;


/**
 * 配置-业务类
 * <AUTHOR>
 * @Date 2019年1月7日 15:06:22
 */
@Service
public class UcConfigService extends CoreBaseService {

	@Autowired
	private CommonDao commonDao_uc_read;

	@Autowired
	private CommonDao commonDao_uc_write;
	/**
	 * 配置-添加
	 * @param ucConfig 配置模型
	 * @return
	 * @throws DBException
	 */
	public DoResult save(UcConfig ucConfig) throws Exception {
		UcConfig configByCode = getConfigByCode(ucConfig);
		if(configByCode != null) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "配置已存在不能重复添加", null, null);
		}
		
		ucConfig.setConfigId(CoreUtils.genSerial(commonDao_uc_write, "UcConfig", "configId", "CONFIG", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
		return commonDao_uc_write.save(ucConfig);
	}

	/**
	 * 配置-删除
	 * @param ucConfig 配置模型
	 * @return
	 * @throws DBException
	 */
	public void delete(UcConfig ucConfig) throws DBException {
		commonDao_uc_write.execute("DELETE FROM UcConfig WHERE configId=?", new Object[]{ucConfig.getConfigId()});
	}

	/**
	 * 配置-修改
	 * @param ucConfig 配置模型
	 * @throws DBException
	 */
	public void update(UcConfig ucConfig) throws DBException {
		commonDao_uc_write.update(ucConfig);
	}

	/**
	 * 配置-详情
	 * @param configId 主键
	 * @return
	 * @throws DBException
	 */
	public UcConfig findById(String configId) throws DBException {
		Map param = MapUtils.toMap(new Object[][] { { "configId", configId }, });
		String sql = "SELECT * FROM UcConfig WHERE configId=:configId";
		return commonDao_uc_read.queryObject(sql, param, UcConfig.class);
	}

	/**
	 * 分页数据
	 * @param mWhere where条件
	 * @param mGroup group
	 * @param mOrder 排序
	 * @param pageIndex 分页起始
	 * @param pageCount 分页数量
	 * @return
	 * @throws Exception
	 */
	//只能查询到全局和角色的配置
	public QueryResult queryMapsCount(Map mWhere, Map mGroup, Map mOrder, int pageIndex, int pageCount)
			throws Exception {

		SqlWhere sqlWhere = getSqlWhere(sqlWE_query, mWhere);
		SqlOrder sqlOrder = getSqlOrder(sqlOE_query, mOrder);
		SqlGroup sqlGroup = null;
		String sql = "";
		QueryResult obj = queryMapsCountBySqlWhere(commonDao_uc_read, " FROM UcConfig ucConfig WHERE 1=1 AND configScopetype IN ('R','G') " + sql, sqlSelect_query,
			sqlWhere, sqlGroup, sqlOrder, pageIndex, pageCount, new Class[] { UcConfig.class });
		return obj;
	}

	/**
	 * 分页条件
	 */
	private final SqlWE[] sqlWE_query = new SqlWE[] { 
		new SqlWE("configId", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("configType", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("configCode", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("configValue", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("configScopetype", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("configScopeobj", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("configUnittype", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("configUnitobj", SqlWE.Compare.equal, SqlWE.Type.STR),
	};

	/**
	 * 分页排序
	 */
	private final SqlOE[] sqlOE_query = new SqlOE[] { 
		new SqlOE("configId", SqlOE.Option.both),
		new SqlOE("configType", SqlOE.Option.both),
		new SqlOE("configCode", SqlOE.Option.both),
		new SqlOE("configValue", SqlOE.Option.both),
		new SqlOE("configScopetype", SqlOE.Option.both),
		new SqlOE("configScopeobj", SqlOE.Option.both),
		new SqlOE("configUnittype", SqlOE.Option.both),
		new SqlOE("configUnitobj", SqlOE.Option.both),
	};

	/**
	 * 查询结果
	 */
	private final SqlSelect sqlSelect_query=new SqlSelect(new String[]{ 
		"configId",
		"configType",
		"configCode",
		"configValue",
		"configScopetype",
		"configScopeobj",
		"configUnittype",
		"configUnitobj",
	});


	/**
	 * 根据某一列查询
	 * @param colsMap
	 * @return
	 * @throws DBException
	 */
	public UcConfig findByCols(Map<String,Object> colsMap) throws DBException {		StringBuffer sql = new StringBuffer("SELECT * FROM UcConfig WHERE 1=1");
		Map<String,Object> params = new HashMap<String, Object>();
		for(String col : colsMap.keySet()) {
			//判断是否有值
			Object value = colsMap.get(col);
			if(value == null) {
				continue;
			}
			if(StringUtils.isEmpty(value.toString())) {
				continue;
			}
			params.put(col, value);
			sql.append(" and ").append(col).append("=");
			//验证value的类型
			if(value instanceof java.lang.String) {
				sql.append("'").append(value.toString()).append("'");
			}else {
				sql.append(value);
			}
		}
		return commonDao_uc_read.queryObject(sql.toString(), params, UcConfig.class);
	}

	public UcConfig getConfigByCode(UcConfig ucConfig) throws DBException {
		Map param = MapUtils.toMap(new Object[][] { 
			{ "configScopetype", ucConfig.getConfigScopetype() }, 
			{ "configType", ucConfig.getConfigType() },
			{ "configCode", ucConfig.getConfigCode() },
			
			{ "configScopeobj", ucConfig.getConfigScopeobj() },
			});
		String sql = "SELECT * FROM UcConfig WHERE configScopeobj=:configScopeobj and configType =:configType and configCode=:configCode and configScopetype =:configScopetype";
		return commonDao_uc_read.queryObject(sql, param, UcConfig.class);
	}
	public void userConfigSave(UcConfig ucConfig) throws Exception {
		UcConfig configByCode = getConfigByCode(ucConfig);
		if(configByCode != null) {
			ucConfig.setConfigId(configByCode.getConfigId());
			//如果查询到就更新
			commonDao_uc_write.update(ucConfig);
		}else{
			//否则保存
			ucConfig.setConfigId(CoreUtils.genSerial(commonDao_uc_write, "UcConfig", "configId", "CONFIG", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
			commonDao_uc_write.save(ucConfig);
		}
	}

	@Transactional
	public String saves(ArrayList<UcConfig> list) throws Exception {
		String ids="";
		int i=0;
		for (UcConfig ucConfig : list) {
			userConfigSave(ucConfig);
			if(i==0) {
				ids+=ucConfig.getConfigId();
			}else {
				ids+=","+ucConfig.getConfigId();
			}
			i++;
		}
		return ids;
	}

	public List find(Map $params) throws DBException {
		String queryType = (String)$params.get("queryType");
		String sql=" select * from UcConfig where 1=1 ";
		String configTypes = (String) $params.get("configType");
		String configCode = (String) $params.get("configCode");
		String configScopetype = (String) $params.get("configScopetype");
		String configScopeobj = (String) $params.get("configScopeobj");
		if(!StringUtils.isEmpty(configScopeobj)) {
			sql+="and configScopeobj = '"+configScopeobj+"'";
		}
		sql+="and configScopetype = '"+configScopetype+"'";
		if("S".equals(queryType)) {
			//直接等于和in
			sql+="and configType = '"+configTypes+"'";
			if(!StringUtils.isEmpty(configCode)) {
				sql+="and configCode in "+splitJoint(configCode);
			}
		}else {
			//直接in
			sql+="and configType in "+splitJoint(configTypes);
			
		}
		return commonDao_uc_read.queryMaps(sql, null, null);
	}
	private String splitJoint(String str) {
		String join="";
		if(StringUtils.isEmpty(str)) {
		}else {
			String[] split = str.split(",");
			for (int i = 0; i < split.length; i++) {
				if(i== 0) {
					join+="('"+split[i]+"'";
				}else if(i==(split.length-1)){
					join+=",'"+split[i]+"'";
				}else {
					join += ",'"+split[i]+"'";
				}
			}
		}
		return join+")";
	}

	/**
	 * 批量保存配置信息如果有Id的话就修改没有的话就新增
	 * @param configs 配置信息
	 * @param notExistConfigList 
	 * @throws Exception 
	 */
	@Transactional
	public void batch_save(List<UcConfig> existConfigList, ArrayList<UcConfig> notExistConfigList) throws Exception {
		//修改已经存在的配置信息
		if(existConfigList.size() > 0) {
			for (UcConfig ucConfig : existConfigList) {
				update(ucConfig);
			}
		}
		//添加不存的配置信息
		if(notExistConfigList.size() > 0) {
			for (UcConfig ucConfig : notExistConfigList) {
				save(ucConfig);
			}
		}
		
	}
}