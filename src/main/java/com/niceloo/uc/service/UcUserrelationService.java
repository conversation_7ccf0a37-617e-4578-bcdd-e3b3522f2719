package com.niceloo.uc.service;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.dao.CoreDao;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.uc.model.UcUserrelation;
import com.niceloo.uc.utils.CloseResource;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.db.Connection;
import org.nobject.common.db.SQLUtils;
import org.nobject.common.db.Transactional;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.CollectionUtils;
import org.nobject.common.lang.ListUtils;
import org.nobject.common.lang.StringUtils;
import org.nobject.common.log.Logger;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 用户关系-业务类
 *
 * <AUTHOR> 2019-11-18 10:35:12
 */
@SuppressWarnings("unused")
@Service
public class UcUserrelationService extends CoreBaseService {

	private static final Logger LOGGER = Logger.getLogger(UcUserrelationService.class);

	@Autowired
	private CoreDao commonDao;

	public static final String MODE_ALL = "ALL";

	public static final String MODE_PART = "PART";


	/**
	 * 保存用户关系
	 *
	 * @param userId 用户id
	 */
	@SuppressWarnings({"unchecked", "Duplicates"})
	@Transactional
	public void saveRelations(String userId, String part, String brandId, Map params) throws DBException, ApplicationException {

		// id与code的list size必须一致，在此方法之外就校验

		List<String> dptIds = (List<String>) params.get("dptIds");
		List<String> projectIds = (List<String>) params.get("projectIds");
		List<String> usersourceCodes = (List<String>) params.get("usersourceCodes");
		List<String> dptLevelcodes = (List<String>) params.get("dptLevelcodes");
		List<String> projectLevelcodes = (List<String>) params.get("projectLevelcodes");
		List<String> usersourceLevelcodes = (List<String>) params.get("usersourceLevelcodes");

		List<String> types = new LinkedList<>();
		if (StringUtils.isEmpty(part)) {
			types = ListUtils.toList(new String[]{"dpt", "project", "usersource"});
		} else {
			String[] split = part.split(",");
			for (String s : split) {
				if (s.equalsIgnoreCase("dpt")) {
					types.add("dpt");
				} else if (s.equalsIgnoreCase("project")) {
					types.add("project");
				} else if (s.equalsIgnoreCase("usersource")) {
					types.add("usersource");
				} else {
					throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "不支持的更新part：" + s, null);
				}
			}
		}
		List<UcUserrelation> list = new ArrayList<>();
		if (dptIds != null && dptIds.size() > 0 && types.contains("dpt")) {
			for (int i = 0; i < dptIds.size(); i++) {
				String dptId = dptIds.get(i);
				String levelcode = dptLevelcodes.get(i);
				UcUserrelation ucUserrelation = new UcUserrelation();
				ucUserrelation.setUserId(userId);
				ucUserrelation.setUserreType("dpt");
				ucUserrelation.setUserreRelationid(dptId);
				ucUserrelation.setUserreRelationlevelcode(levelcode == null ? "" : levelcode);
				ucUserrelation.setUserreId(genId());
				ucUserrelation.setBrandId(brandId);
				list.add(ucUserrelation);
			}
		}
		if (projectIds != null && projectIds.size() > 0 && types.contains("project")) {
			// 全选标记
			for (int i = 0; i < projectIds.size(); i++) {
				String projectId = projectIds.get(i);
				String levelCode = projectLevelcodes.get(i);
				UcUserrelation ucUserrelation = new UcUserrelation();
				ucUserrelation.setUserId(userId);
				ucUserrelation.setUserreType("project");
				ucUserrelation.setUserreRelationid(projectId);
				ucUserrelation.setUserreRelationlevelcode(levelCode == null ? "" : levelCode);
				ucUserrelation.setUserreId(genId());
				ucUserrelation.setBrandId(brandId);
				list.add(ucUserrelation);
			}
		}
		if (usersourceCodes != null && usersourceCodes.size() > 0 && types.contains("usersource")) {
			// 全选标记
			for (int i = 0; i < usersourceCodes.size(); i++) {
				String usersourceId = usersourceCodes.get(i);
				String levelcode = usersourceLevelcodes.get(i);
				UcUserrelation ucUserrelation = new UcUserrelation();
				ucUserrelation.setUserId(userId);
				ucUserrelation.setUserreType("usersource");
				ucUserrelation.setUserreRelationid(usersourceId);
				ucUserrelation.setUserreRelationlevelcode(levelcode == null ? "" : levelcode);
				ucUserrelation.setUserreId(genId());
				ucUserrelation.setBrandId(brandId);
				list.add(ucUserrelation);
			}
		}
		this.deleteRelationsByUser(userId, types);
		if (list.size() > 0) {
			this.commonDao.save(list);
		}
	}

	public void saveRelationsBatch(List<String> userIds, String part, String brandId, Map params)
			throws DBException, ApplicationException, SQLException {
		Connection conn = null;
		boolean preIsAuthCommit = true;
		try{
			conn = commonDao.getWriteDao().getDbFactory().getConnection();
			preIsAuthCommit = conn.conn.getAutoCommit();
			conn.conn.setAutoCommit(false);

			if (userIds.size() > 20) {
				List<List<String>> userIdsFragmentDepot = new ArrayList<>();
				List<String> userIdsFragment = null;
				// 构建userIdsFragmentDepot
				for (int i = 0; i < userIds.size(); i++) {
					if (i % 20 == 0) {
						userIdsFragment = new ArrayList<>();
						userIdsFragmentDepot.add(userIdsFragment);
					}
					String userId = userIds.get(i);
					userIdsFragment.add(userId);
				}
				for (List<String> node : userIdsFragmentDepot) {
					saveRelationsBatchWorker(node, part, brandId, params, conn);
				}
			} else {
				saveRelationsBatchWorker(userIds, part, brandId, params, conn);
			}
			conn.conn.commit();
		} catch (Throwable e) {
			if (conn != null) {
				conn.conn.rollback();
			}
			throw e;
		} finally {
			if (conn != null) {
				conn.conn.setAutoCommit(preIsAuthCommit);
			}
			commonDao.getWriteDao().getDbFactory().releaseConnection(conn);
		}
	}

	@SuppressWarnings({"unchecked", "Duplicates"})
	private void saveRelationsBatchWorker(List<String> userIds, String part, String brandId, Map params, Connection conn)
			throws DBException, ApplicationException, SQLException {
		Object dptIdOjbs = params.get("dptIds");
		List<String> dptIds = dptIdOjbs == null ? new ArrayList<>() : (List<String>) dptIdOjbs;
		Object projectIdOjbs = params.get("projectIds");
		List<String> projectIds = projectIdOjbs == null ? new ArrayList<>() : (List<String>) projectIdOjbs;
		Object usersourceDoceOjbs = params.get("usersourceCodes");
		List<String> usersourceCodes = usersourceDoceOjbs == null ? new ArrayList<>() : (List<String>) usersourceDoceOjbs;

		// 老逻辑, 无变更
		List<String> dptLevelcodes = (List<String>) params.get("dptLevelcodes");
		List<String> projectLevelcodes = (List<String>) params.get("projectLevelcodes");
		List<String> usersourceLevelcodes = (List<String>) params.get("usersourceLevelcodes");

		List<String> types = buildTypes(part);
		List<UcUserrelation> urs = queryRelationsByUserId4BatchSet(userIds, types, brandId, conn);
		Map<String, UcUserrelation> urDepot = buildURDepot(urs);

		List<String> delURIds = new ArrayList<>();
		List<UcUserrelation> addURs = new ArrayList<>();


		if (types.contains("dpt")) {
			List<String> delURIds4Dpt = urs.stream()
					.parallel()
					.filter(ur -> "dpt".equalsIgnoreCase(ur.getUserreType()))
					.filter(ur -> !dptIds.contains(ur.getUserreRelationid()))
					.map(UcUserrelation::getUserreId)
					.collect(Collectors.toList());
			delURIds.addAll(delURIds4Dpt);

			for (int i = 0; i < dptIds.size(); i++) {
				String dptId = dptIds.get(i);
				String levelcode = dptLevelcodes.get(i);

				for (String userId : userIds) {
					String urK = buildURDepotK(userId, dptId, "dpt");
					if (!urDepot.containsKey(urK)) {
						String _levelCode = levelcode == null ? "" : levelcode;
						UcUserrelation ucUserrelation = new UcUserrelation(
								genId(), "dpt", userId, dptId, _levelCode, brandId
						);
						addURs.add(ucUserrelation);
					}
				}

			}
		}

		if (types.contains("project")) {
			List<String> delURIds4Project = urs.stream()
					.filter(ur -> "project".equalsIgnoreCase(ur.getUserreType()))
					.filter(ur -> !projectIds.contains(ur.getUserreRelationid()))
					.map(UcUserrelation::getUserreId)
					.collect(Collectors.toList());
			delURIds.addAll(delURIds4Project);

			// 全选标记
			for (int i = 0; i < projectIds.size(); i++) {
				String projectId = projectIds.get(i);
				String levelCode = projectLevelcodes.get(i);

				for (String userId : userIds) {
					String urK = buildURDepotK(userId, projectId, "project");
					if (!urDepot.containsKey(urK)) {
						String _levelCode = levelCode == null ? "" : levelCode;
						UcUserrelation ucUserrelation = new UcUserrelation(
								genId(), "project", userId, projectId, _levelCode, brandId
						);
						addURs.add(ucUserrelation);
					}
				}
			}
		}


		if (types.contains("usersource")) {
			List<String> delURIds4Project = urs.stream()
					.filter(ur -> "usersource".equalsIgnoreCase(ur.getUserreType()))
					.filter(ur -> !usersourceCodes.contains(ur.getUserreRelationid()))
					.map(UcUserrelation::getUserreId)
					.collect(Collectors.toList());
			delURIds.addAll(delURIds4Project);

			// 全选标记
			for (int i = 0; i < usersourceCodes.size(); i++) {
				String usersourceId = usersourceCodes.get(i);
				String levelCode = usersourceLevelcodes.get(i);

				for (String userId : userIds) {
					String urK = buildURDepotK(userId, usersourceId, "usersource");
					if (!urDepot.containsKey(urK)) {
						String _levelCode = levelCode == null ? "" : levelCode;
						UcUserrelation ucUserrelation = new UcUserrelation(
								genId(), "usersource", userId, usersourceId, _levelCode, brandId
						);
						addURs.add(ucUserrelation);
					}
				}
			}
		}

		Statement delStmt = null;
		PreparedStatement addPstmt = null;
		try {
			if (!CollectionUtils.isEmpty(delURIds)) {
				List<List<String>> delFragmentDepot = new ArrayList<>();
				List<String> delFragment = null;
				for (int i = 0; i < delURIds.size(); i++) {
					if (i % 500 == 0) {
						delFragment = new ArrayList<>();
						delFragmentDepot.add(delFragment);
					}
					String delURId = delURIds.get(i);
					delFragment.add(delURId);
				}

				delStmt = conn.conn.createStatement();

				for (List<String> delNode: delFragmentDepot) {
					String delSql = String.format("DELETE FROM UcUserrelation WHERE userreId IN (%s)", SQLUtils.getIn(delNode));
					delStmt.executeUpdate(delSql);
				}
			}

			if (!CollectionUtils.isEmpty(addURs)) {
				String addSqlFields = "userreId,userreType,userId,userreRelationid,userreRelationlevelcode,brandId";
				String addSqlValues = "?,?,?,?,?,?";
				String addSql = String.format("INSERT INTO UcUserrelation(%s) VALUES(%s)", addSqlFields, addSqlValues);
				addPstmt = conn.conn.prepareStatement(addSql);
				for (int i = 0; i < addURs.size(); i++) {
					UcUserrelation addUr = addURs.get(i);
					addPstmt.setString(1, addUr.getUserreId());
					addPstmt.setString(2, addUr.getUserreType());
					addPstmt.setString(3, addUr.getUserId());
					addPstmt.setString(4, addUr.getUserreRelationid());
					addPstmt.setString(5, addUr.getUserreRelationlevelcode());
					addPstmt.setString(6, addUr.getBrandId());
					addPstmt.addBatch();

					if (i % 1000 == 0) {
						addPstmt.executeBatch();
						addPstmt.clearBatch();
					}
				}
				addPstmt.executeBatch();
			}
		} finally {
			CloseResource.closeQuietly(delStmt, addPstmt);
		}

	}

	@SuppressWarnings("Duplicates")
	private List<String> buildTypes(String part) throws ApplicationException {
		List<String> types = new ArrayList<>();
		if (StringUtils.isEmpty(part)) {
			types = Arrays.asList("dpt", "project", "usersource");
		} else {
			String[] split = part.split(",");
			for (String s : split) {
				if ("dpt".equalsIgnoreCase(s)) {
					types.add("dpt");
				} else if ("project".equalsIgnoreCase(s)) {
					types.add("project");
				} else if ("usersource".equalsIgnoreCase(s)) {
					types.add("usersource");
				} else {
					throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "不支持的更新part：" + s, null);
				}
			}
		}
		return types;
	}

	private Map<String, UcUserrelation> buildURDepot(List<UcUserrelation> urs) {
		Map<String, UcUserrelation> r = new HashMap<>();
		urs.forEach(ur -> r.put(buildURDepotK(ur), ur));
		return r;
	}

	private String buildURDepotK(UcUserrelation ur) {
		return buildURDepotK(ur.getUserId(), ur.getUserreRelationid(), ur.getUserreType());
	}

	private String buildURDepotK(String userId, String relationId, String type) {
		return userId + "::" + relationId + "::" + type;
	}

	/**
	 * 根据用户id查询角色列表
	 *
	 * @param userreType   类型，如果为null，则认为查询除角色以外的其他
	 */
	public List<UcUserrelation> queryRelationsByUserId(String userId, String userreType, String brandId ) throws DBException {
		String sql = "select * from UcUserrelation where userId = ? and brandId= ?";
		Object[] params = new Object[]{userId, brandId};
		if (!StringUtils.isEmpty(userreType)) {
			sql += " and userreType = ?";
			params = new Object[]{userId, brandId, userreType};
		}
		return commonDao.queryObjects(sql, params, UcUserrelation.class);
	}

	public List<UcUserrelation> queryRelationsByUserId2(List<String> userIds, List<String> userreTypes, String brandId)
			throws DBException {
		if (CollectionUtils.isEmpty(userIds)) {
			return new ArrayList<>();
		}

		String sql = String.format(
				"SELECT * FROM UcUserrelation WHERE userId IN (%s) AND brandId='%s' AND userreType IN (%s)",
				SQLUtils.getIn(userIds),
				brandId,
				SQLUtils.getIn(userreTypes));

		return commonDao.queryObjects(sql, new Object[]{}, UcUserrelation.class);
	}

	private List<UcUserrelation> queryRelationsByUserId4BatchSet(List<String> userIds, List<String> userreTypes, String brandId, Connection conn)
			throws SQLException {
		if (CollectionUtils.isEmpty(userIds)) {
			return new ArrayList<>();
		}

		String sql = String.format(
				"SELECT userreId,userreType,userId,userreRelationid FROM UcUserrelation WHERE userId IN (%s) AND brandId='%s' AND userreType IN (%s)",
				SQLUtils.getIn(userIds),
				brandId,
				SQLUtils.getIn(userreTypes));
		Statement stmt = null;
		ResultSet rs = null;
		try{
			stmt = conn.conn.createStatement();
			rs = stmt.executeQuery(sql);
			List<UcUserrelation> r = new ArrayList<>();
			while (rs.next()) {
				String userreId = rs.getString(1);
				String userreType = rs.getString(2);
				String userId = rs.getString(3);
				String userreRelationid = rs.getString(4);
				r.add(new UcUserrelation(userreId, userreType, userId, userreRelationid));
			}
			return r;
		} finally {
			CloseResource.closeQuietly(rs, stmt);
		}

//		return commonDao.queryObjects(sql, new Object[]{}, UcUserrelation.class);
	}

	/**
	 * 根据用户和范围类型删除其用户范围
	 *
	 * @param types  类型数组，不能为null
	 */
	private void deleteRelationsByUser(String userId, List types) throws DBException, ApplicationException {
		if (types.size() == 0) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "type不能为null", null);
		}
		commonDao.execute("delete from UcUserrelation where userId = ? and userreType in (" + SQLUtils.getIn(types) + ")", new Object[]{userId});
	}


	private String genId() throws DBException {
		return commonDao.genSerial("UcUserrelation", "userreId", "USERRE", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq);
	}
}