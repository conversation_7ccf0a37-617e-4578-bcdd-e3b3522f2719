package com.niceloo.uc.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.db.Transactional;
import org.nobject.common.db.member.*;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.ObjectUtils;
import org.nobject.common.lang.StringUtils;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.DoResult;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.uc.common.UcConfig;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.common.UcConst.Avlstatus;
import com.niceloo.uc.common.UcConst.PublishType;
import com.niceloo.uc.model.UcClttype;
import com.niceloo.uc.model.UcCltver;


/**
 * 客户端版本-业务类
 * <AUTHOR>
 * @Date 2019-09-25
 */
@Service
public class UcCltverService extends CoreBaseService {

	@Autowired
	private CommonDao commonDao;
	@Autowired
	private UcCltgrayService ucCltgrayService;
	/**
	 * 客户端版本-添加
	 * @param ucCltver 客户端版本模型
	 * @return
	 * @throws DBException
	 */
	public DoResult add(UcCltver ucCltver) throws Exception {
		ucCltver.setCltverId(commonDao.genSerial("UcCltver", "cltverId", "CLTVER", 10,"yyyyMMdd"+CoreConfig.Sys.clusterSeq));
		return commonDao.save(ucCltver);
	}

	/**
	 * 客户端版本-删除
	 * @param ucCltver 客户端版本模型
	 * @return
	 * @throws DBException
	 */
	public void delete(UcCltver ucCltver) throws DBException {
		commonDao.execute("DELETE FROM UcCltver WHERE cltverId=?", new Object[]{ucCltver.getCltverId()});
	}

	/**
	 * 客户端版本-修改
	 * @param ucCltver 客户端版本模型
	 * @throws DBException
	 */
	public void edit(UcCltver ucCltver) throws DBException {
		commonDao.update(ucCltver);
	}

	/**
	 * 客户端版本-详情
	 * @param cltverId 主键
	 * @return
	 * @throws DBException
	 */
	public UcCltver findById(String cltverId) throws DBException {
		Map param = MapUtils.toMap(new Object[][] { { "cltverId", cltverId }, });
		String sql = "SELECT * FROM UcCltver WHERE cltverId=:cltverId";
		return commonDao.queryObject(sql, param, UcCltver.class);
	}

	/**
	 * 分页数据
	 * @param mWhere where条件
	 * @param mGroup 组合条件
	 * @param mOrder 排序
	 * @param pageIndex 分页起始
	 * @param pageSize 分页数量
	 * @throws Exception
	 */
	public QueryResult findMapsCount(Map mWhere, Map mGroup, Map mOrder, int pageIndex, int pageSize)
			throws Exception {

		SqlWhere sqlWhere = getSqlWhere(sqlWE_findMapsCount, mWhere);
		SqlOrder sqlOrder = getSqlOrder(sqlOE_findMapsCount, mOrder);
		SqlGroup sqlGroup = null;
		String sql = " FROM UcCltver  ucCltver WHERE 1=1 ";
		return queryMapsCountBySqlWhere(commonDao,sql, sqlSelect_findMapsCount,sqlWhere,sqlGroup,sqlOrder,pageIndex,pageSize,new Class[]{UcCltver.class});	};

	/**
	 * 分页数据
	 * @param mWhere where条件
	 * @param mGroup group
	 * @param mOrder 排序
	 * @param pageIndex 分页起始
	 * @param pageCount 分页数量
	 * @return
	 * @throws Exception
	 */
	public QueryResult queryMapsCount(Map mWhere, Map mGroup, Map mOrder, int pageIndex, int pageCount)
			throws Exception {

		SqlWhere sqlWhere = getSqlWhere(sqlWE_findMapsCount, mWhere);
		SqlOrder sqlOrder = getSqlOrder(sqlOE_findMapsCount, mOrder);
		SqlGroup sqlGroup = null;
		String sql = "";
		QueryResult obj = queryMapsCountBySqlWhere(commonDao, " FROM UcCltver ucCltver WHERE 1=1 " + sql, sqlSelect_findMapsCount,
			sqlWhere, sqlGroup, sqlOrder, pageIndex, pageCount, new Class[] { UcCltver.class });
		return obj;
	}

	/**
	 * 分页条件
	 */
	private final SqlWE[] sqlWE_findMapsCount = new SqlWE[] { 
		new SqlWE("cltverId", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("clttypeId", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverMemo", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverAddress", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverCode", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverPackagetype", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverPublishtype", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverPublishdate", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverItertype", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverDelstatus", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverCreatedate", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverCreater", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverCreatername", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverModifieddate", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverModifier", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverModifiername", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverMarkcheckstatus", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverAvlstatus", SqlWE.Compare.equal, SqlWE.Type.STR),
		new SqlWE("cltverDelaymaxday", SqlWE.Compare.equal, SqlWE.Type.STR),
	};

	/**
	 * 分页排序
	 */
	private final SqlOE[] sqlOE_findMapsCount = new SqlOE[] { 
		new SqlOE("cltverId", SqlOE.Option.both),
		new SqlOE("clttypeId", SqlOE.Option.both),
		new SqlOE("cltverMemo", SqlOE.Option.both),
		new SqlOE("cltverAddress", SqlOE.Option.both),
		new SqlOE("cltverCode", SqlOE.Option.both),
		new SqlOE("cltverPackagetype", SqlOE.Option.both),
		new SqlOE("cltverPublishtype", SqlOE.Option.both),
		new SqlOE("cltverPublishdate", SqlOE.Option.both),
		new SqlOE("cltverItertype", SqlOE.Option.both),
		new SqlOE("cltverDelstatus", SqlOE.Option.both),
		new SqlOE("cltverCreatedate", SqlOE.Option.both),
		new SqlOE("cltverCreater", SqlOE.Option.both),
		new SqlOE("cltverCreatername", SqlOE.Option.both),
		new SqlOE("cltverModifieddate", SqlOE.Option.both),
		new SqlOE("cltverModifier", SqlOE.Option.both),
		new SqlOE("cltverModifiername", SqlOE.Option.both),
		new SqlOE("cltverMarkcheckstatus", SqlOE.Option.both),
		new SqlOE("cltverAvlstatus", SqlOE.Option.both),
		new SqlOE("cltverDelaymaxday", SqlOE.Option.both),
		
		
	};

	/**
	 * 查询结果
	 */
	private final SqlSelect sqlSelect_findMapsCount=new SqlSelect(new String[]{ 
		"cltverId",
		"clttypeId",
		"cltverMemo",
		"cltverAddress",
		"cltverCode",
		"cltverPackagetype",
		"cltverPublishtype",
		"cltverPublishdate",
		"cltverItertype",
		"cltverDelstatus",
		"cltverCreatedate",
		"cltverCreater",
		"cltverCreatername",
		"cltverModifieddate",
		"cltverModifier",
		"cltverModifiername",
		"cltverMarkcheckstatus",
		"cltverAvlstatus",
		"cltverDelaymaxday"
	});


	/**
	 * 根据某一列查询
	 * @param colsMap
	 * @return
	 * @throws DBException
	 */
	public UcCltver findByCols(Map<String,Object> colsMap) throws DBException {
		StringBuffer sql = new StringBuffer("SELECT * FROM UcCltver WHERE 1=1");
		Map<String,Object> params = new HashMap<String, Object>();
		for(String col : colsMap.keySet()) {
			//判断是否有值
			Object value = colsMap.get(col);
			if(value == null) {
				continue;
			}
			if(StringUtils.isEmpty(value.toString())) {
				continue;
			}
			params.put(col, value);
			sql.append(" and ").append(col).append("=");
			//验证value的类型
			if(value instanceof java.lang.String) {
				sql.append("'").append(value.toString()).append("'");
			}else {
				sql.append(value);
			}
		}
		return commonDao.queryObject(sql.toString(), params, UcCltver.class);
	}

	/**
	 * 校验版本号是否可用
	 * @param clttypeId
	 * @param cltverCode
	 * @return
	 * @throws DBException 
	 */
	public boolean valdateClverSeq(String clttypeId, String cltverCode) throws Exception {
		UcCltver cltver = commonDao.queryObject("SELECT * FROM UcCltver WHERE clttypeId = ? AND cltverDelstatus = ? AND cltverPublishtype = ?", new Object[] {clttypeId,Avlstatus.NO,PublishType.NOT}, UcCltver.class);
		if(cltver != null) {
			throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT,"每个客户端只允许一个未发布的存在!",null);
		}
		if(!codeValidate(cltverCode)) {
			throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT,"客户端版本号不合法!",null);
		}
		cltver = commonDao.queryObject("SELECT * FROM UcCltver WHERE clttypeId = ? AND cltverDelstatus = ?  ORDER BY cltverPublishdate DESC", new Object[] {clttypeId,Avlstatus.NO}, UcCltver.class);
		if(cltver != null) {
			return !compareVersion(cltverCode,cltver.getCltverCode());
		}else {
			return false;
		}
	}

	public static void main(String[] args) {
		boolean compareCode = compareVersion("3.0.0", "3.0.0.1");
		System.out.println(compareCode);
	}
	
	/**
	 * v1如果大于v2的话就返回true 否则就是false
	 * @param v1 数据库中存的
	 * @param v2 传递过来的
	 * @return  true 升级  false 不升级
	 */
	public static  boolean compareVersion(String v1, String v2) {
        if (v1.equals(v2)) {
            return false;
        }
        String[] version1Array = v1.split("[._]");
        String[] version2Array = v2.split("[._]");
        int index = 0;
        int minLen = Math.min(version1Array.length, version2Array.length);
        long diff = 0;
        
        while (index < minLen
                && (diff = Long.parseLong(version1Array[index])
                - Long.parseLong(version2Array[index])) == 0) {
            index++;
        }
        if (diff == 0) {
            for (int i = index; i < version1Array.length; i++) {
                if (Long.parseLong(version1Array[i]) > 0) {
                    return true;
                }
            }
            
            for (int i = index; i < version2Array.length; i++) {
                if (Long.parseLong(version2Array[i]) > 0) {
                    return false;
                }
            }
            return false;
        } else {
            return diff > 0 ? true : false;
        }
    }

	/**
	 * 校验code是否合法
	 * @param cltverCode
	 * @return
	 */
	private boolean codeValidate(String cltverCode) {
		String[] split = cltverCode.split("\\.");
		for (String string : split) {
			try {
				Integer.parseInt(string);
			} catch (Exception e) {
				return false;
			}
		}
		return true;
	}
	
	/**
	 * 最大升级版本缓存包括灰度版本
	 */
	public static HashMap<String, HashMap<String,Object>> cachaMaxCltver = new HashMap<>();
	
	/**
	 * 获取最大升级版本
	 * @param clttypeId
	 * @return
	 * @throws DBException
	 */
	public UcCltver findMaxCltver(String clttypeId) throws DBException {
		HashMap<String,Object> cachaMap = cachaMaxCltver.get(clttypeId);
		if(cachaMap != null) {
			long lastTime = (long) cachaMap.get("lastTime");
			long currentTimeMillis = System.currentTimeMillis();
			if(UcConfig.Cltver.cachaTime<(currentTimeMillis-lastTime)) {
				return createMaxCltver(clttypeId);
			}else {
				return (UcCltver) cachaMap.get("ucCltver");
			}
		}else {
			return createMaxCltver(clttypeId);
		}
	}
	
	/**
	 * 创建最大升级版本缓存
	 * @param clttypeId
	 * @return
	 * @throws DBException
	 */
	private UcCltver createMaxCltver(String clttypeId) throws DBException {
		Map param = MapUtils.toMap(new Object[][] { { "clttypeId", clttypeId },{"cltverPublishtype",PublishType.NOT},{"cltverDelstatus","Y"} });
		String sql = "SELECT * FROM UcCltver WHERE clttypeId=:clttypeId AND cltverPublishtype != :cltverPublishtype AND cltverDelstatus !=:cltverDelstatus ORDER BY cltverPublishdate DESC";
		UcCltver ucCltver = commonDao.queryObject(sql, param, UcCltver.class);
		HashMap<String,Object> cacha = new HashMap<>();
		cacha.put("ucCltver", ucCltver);
		long currentTimeMillis = System.currentTimeMillis();
		cacha.put("lastTime", currentTimeMillis);
		cachaMaxCltver.put(clttypeId, cacha);
		return ucCltver;
	}

	/**
	 * 缓存非灰度升级最大版本缓存时间一分钟
	 */
	public static HashMap<String, HashMap<String,Object>> cachaMaxCltverNotGary = new HashMap<>();
	
	/**
	 * 获取非灰度升级版本
	 * @param clttypeId
	 * @return
	 * @throws DBException
	 */
	public UcCltver findMaxCltverNotGary(String clttypeId) throws DBException {
		HashMap<String,Object> cachaMap = cachaMaxCltverNotGary.get(clttypeId);
		if(cachaMap != null) {
			long lastTime = (long) cachaMap.get("lastTime");
			long currentTimeMillis = System.currentTimeMillis();
			if(UcConfig.Cltver.cachaTime<(currentTimeMillis-lastTime)) {
				return createMaxCltverNoGaryCacha(clttypeId);
			}else {
				return (UcCltver) cachaMap.get("ucCltver");
			}
		}else {
			return createMaxCltverNoGaryCacha(clttypeId);
		}
	}

	/**
	 * 创建非灰度客户端最大版本缓存
	 * @param clttypeId
	 * @return
	 * @throws DBException
	 */
	public UcCltver createMaxCltverNoGaryCacha(String clttypeId) throws DBException {
		Map param = MapUtils.toMap(new Object[][] { { "clttypeId", clttypeId }});
		String sql = "SELECT * FROM UcCltver WHERE clttypeId=:clttypeId AND cltverPublishtype = '"+ PublishType.COMMON +"' ORDER BY cltverPublishdate DESC";
		UcCltver ucCltver = commonDao.queryObject(sql, param, UcCltver.class);
		HashMap<String,Object> cacha = new HashMap<>();
		cacha.put("ucCltver", ucCltver);
		long currentTimeMillis = System.currentTimeMillis();
		cacha.put("lastTime", currentTimeMillis);
		cachaMaxCltverNotGary.put(clttypeId, cacha);
		return ucCltver;
	}
	
	/**
	 * 校验升级
	 * @param ucClttype
	 * @param userId
	 * @param terminalId
	 * @return
	 * @throws DBException
	 */
	public Map check(UcClttype ucClttype, String cltverCode, String userId, String terminalId) throws DBException {
		HashMap<String,String> resoutMap = new HashMap<>();
		String clttypeId = ucClttype.getClttypeId();
		//获取最大升级版本
		UcCltver ucCltver = findMaxCltver(clttypeId);
		//判断是否需要升级如果需要升级就进去
		if(ucCltver != null && compareVersion(ucCltver.getCltverCode(),cltverCode)) {
			//是灰度升级就进去
			if(PublishType.GRAY.equals(ucCltver.getCltverPublishtype())) {
				//校验灰度升级
				validateGary(userId, terminalId, resoutMap, clttypeId, ucCltver,cltverCode);
			}else {
				getResoutMap(resoutMap, ucCltver);
			}
		}else {
			resoutMap.put("updateType", PublishType.NOT);
		}
		return resoutMap;
	}

	/**
	 * 强制升级和选择升级校验
	 * @param resoutMap
	 * @param ucCltver
	 */
	private void getResoutMap(HashMap<String, String> resoutMap, UcCltver ucCltver) {
		resoutMap.put("updateType", ucCltver.getCltverItertype());
		resoutMap.put("cltverItertype", ucCltver.getCltverPublishtype());
		resoutMap.put("cltverAddress", ucCltver.getCltverAddress());
		resoutMap.put("cltverCode", ucCltver.getCltverCode());
		resoutMap.put("cltverPackagetype", ucCltver.getCltverPackagetype());
		resoutMap.put("cltverMemo", ucCltver.getCltverMemo());
		resoutMap.put("cltverDelaymaxday", ucCltver.getCltverDelaymaxday()+"");
	}

	/**
	 * 灰度升级
	 * @param userId
	 * @param terminalId
	 * @param resoutMap
	 * @param clttypeId
	 * @param ucCltver
	 * @throws DBException
	 */
	private void validateGary(String userId, String terminalId, HashMap<String, String> resoutMap, String clttypeId,
			UcCltver ucCltver,String cltverCode) throws DBException {
		//校验是否是灰度升级对象
		boolean checkGray = ucCltgrayService.checkGray(clttypeId, userId, terminalId);
		if(!checkGray) {
			//不是灰度升级用户
			ucCltver = findMaxCltverNotGary(clttypeId);
			if(ucCltver == null || !compareVersion(ucCltver.getCltverCode() , cltverCode)) {
				//不需要升级
				resoutMap.put("updateType", PublishType.NOT);
				return;
			}
		}
		getResoutMap(resoutMap, ucCltver);
	}

	/**
	 * 缓存版本信息缓存时间一分钟
	 */
	public static HashMap<String, Map> cltverCache = new HashMap<>();
	
	/**
	 * 查询 升级版本 通过版本号 客户端类型删除状态
	 * @param clttypeId
	 * @param cltverCode
	 * @param clttypeDelstatus
	 * @return
	 * @throws DBException 
	 */
	public UcCltver queryCltverByTypeidCodeDel(String clttypeId, String cltverCode, String clttypeDelstatus) throws DBException {
		Map cltverCacheMap = cltverCache.get(clttypeId+cltverCode+clttypeDelstatus);
		if(!ObjectUtils.isEmpty(cltverCacheMap)) {
			Long lastTime = (Long) cltverCacheMap.get("lastTime");
			long currentTimeMillis = System.currentTimeMillis();
			if(UcConfig.Cltver.cachaTime<(currentTimeMillis-lastTime)) {
				return createCltverCache(clttypeId, cltverCode, clttypeDelstatus);
			}else {
				return (UcCltver) cltverCacheMap.get("ucCltver");
			}
		}else {
			return createCltverCache(clttypeId, cltverCode, clttypeDelstatus);
		}
	}

	/**
	 * 生成缓存
	 * @param clttypeId
	 * @param cltverCode
	 * @param clttypeDelstatus
	 * @return
	 * @throws DBException
	 */
	private UcCltver createCltverCache(String clttypeId, String cltverCode, String clttypeDelstatus) throws DBException {
		long currentTimeMillis = System.currentTimeMillis();
		UcCltver ucCltver = findByCols(MapUtils.toMap(new Object[][] {
			{"clttypeId",clttypeId},
			{"cltverCode",cltverCode},
			{"cltverDelstatus",clttypeDelstatus}
		}));
		cltverCache.put(clttypeId+cltverCode+clttypeDelstatus, MapUtils.toMap(new Object[][] {
			{"ucCltver",ucCltver},
			{"lastTime",currentTimeMillis}
		}));
		return ucCltver;
	}

	/**
	 * 发布版本
	 * @param ucCltver
	 * @throws DBException 
	 */
	@Transactional
	public void publish(UcCltver ucCltver) throws DBException {
		//判断这个版本迭代类型是否是强制迭代
		if(UcConst.IterType.FORCE.equals(ucCltver.getCltverItertype())) {
			//强制迭代需要把这个版本前的所有版本都改为不可用
			//因为一个客户端类型只会存在一个未发布的版本所以需要把除了这个版本的所有版本都更新为不可用
			editHisAvlNo(ucCltver);
		}
		//修改发布状态
		edit(ucCltver);
	}
	
	/**
	 * 修改历史所有版本都为不可用
	 * @param ucCltver
	 * @throws DBException
	 */
	public void editHisAvlNo(UcCltver ucCltver) throws DBException {
		String cltverId = ucCltver.getCltverId();
		String clttypeId = ucCltver.getClttypeId();
		String sql = "UPDATE UcCltver SET cltverAvlstatus = 'N' WHERE cltverDelstatus = 'N'  AND cltverId != ? AND clttypeId = ?";
		commonDao.execute(sql, new Object[] {cltverId,clttypeId});
	}

	/**
	 * 根据版本标识查询列表
	 * @param clttypeId
	 * @throws DBException 
	 */
	public List<UcCltver> findCltverByClttypeId(String clttypeId) throws DBException {
		String sql = "SELECT * FROM UcCltver WHERE clttypeId=:clttypeId AND  cltverDelstatus = 'N' AND cltverAvlstatus = 'Y' AND  cltverPublishtype != '"+ PublishType.NOT + "'";
		Map param = MapUtils.toMap(new Object[][] { { "clttypeId", clttypeId }});
		return commonDao.queryObjects(sql, param, UcCltver.class);
	}

	/**
	 * 批量修改
	 * @param cltverList
	 * @throws DBException
	 */
	public void editCltvers(List<UcCltver> cltverList) throws DBException {
		if(cltverList != null && cltverList.size() > 0) {
			commonDao.update(cltverList);
		}
	}
}