package com.niceloo.uc.service;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.CoreDao;
import com.niceloo.uc.model.UcUserauth;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.db.Transactional;
import org.nobject.common.exception.DBException;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Auther hepangui
 * @Date 2020/9/15 0015
 */
@Service
public class UcUserauthService {

	@Autowired
	CoreDao coreDao;

	/**
	 * 获取用户的认证策略
	 * @param userId
	 * @return
	 */
	public String [] getAuthTypeByUser(String userId) throws DBException {
		String sql = "select userauthType from UcUserauth where userId = ?";
		return coreDao.queryStrings(sql, new Object[]{userId});
	}

	@Transactional
	public void saveAuthTypeByUser(String userId, Set<String> authType) throws DBException {
		String sql = "delete from UcUserauth where userId =?";
		coreDao.execute(sql,new Object[]{userId});

		List<UcUserauth> list = new ArrayList<>();
		for (String s : authType) {
			String s1 = coreDao.genSerial("UcUserauth", "userauthId", "USERAUTH", 10);
			UcUserauth ucUserauth = new UcUserauth();
			ucUserauth.setUserauthId(s1);
			ucUserauth.setUserauthType(s);
			ucUserauth.setUserId(userId);
			list.add(ucUserauth);
		}
		if(list.size()>0){
			coreDao.save(list);
		}
	}
}
