package com.niceloo.uc.service;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.mq.MQService;
import com.niceloo.core.mvc.CoreBaseService;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.model.BdEe;
import com.niceloo.uc.model.BdSchool;
import com.niceloo.uc.model.UcUser;
import com.niceloo.uc.model.UcUserTag;
import com.niceloo.uc.model.log.UcLoginlog;
import com.niceloo.uc.utils.CoreLogUtil;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.db.SQLUtils;
import org.nobject.common.db.Transactional;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.CollectionUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.ObjectUtils;
import org.nobject.common.lang.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.niceloo.uc.common.UcConst.Errorcode.EE_HAS_LEFT;
import static org.nobject.common.lang.ObjectUtils.isEmpty;


/**
 * 员工 服务类
 * <p>
 * maning 20201205
 */
@Service
@SuppressWarnings("unused")
public class UcEeService extends CoreBaseService {

    @Autowired
    private CommonDao commonDao_bd_write;

    @Autowired
    private UcSchoolService ucSchoolService;

    @Autowired
    private UcUserService ucUserService;
    @Autowired
    private UcUserroleService ucUserroleService;
    @Autowired
    private UcUserbrandService ucUserbrandService;
    @Autowired
    private UcTagService ucTagService;
    @Autowired
    private MQService mqService;

    /**
     * 员工-详情
     *
     * @param eeId 主键
     */
    public BdEe findById(String eeId) throws DBException {
        Map param = MapUtils.toMap(new Object[][]{{"eeId", eeId},});
        String sql = "SELECT * FROM BdEe WHERE eeId=:eeId";
        return commonDao_bd_write.queryObject(sql, param, BdEe.class);
    }

    /**
     * 员工-详情_根据用户标识查询
     *
     * @param userIds 主键
     */
    public List<BdEe> findByUserIds(String userIds) throws DBException {
        String sql = "SELECT * FROM BdEe WHERE userId in (" + SQLUtils.getIn(userIds.split(",")) + ")";
        return commonDao_bd_write.queryObjects(sql, null, BdEe.class);
    }

    /**
     * 根据员工编号获取员工
     *
     * @param eeNo 员工编号
     */
    public BdEe findByNo(String eeNo) throws Exception {
        String sql = "SELECT * FROM BdEe WHERE eeNo=? AND eeDelstatus='N'";
        return commonDao_bd_write.queryObject(sql, new Object[]{eeNo}, BdEe.class);
    }

    @SuppressWarnings("WeakerAccess")
    public String findNoByUserId(String userId) throws Exception {
        String sql = "SELECT eeNo FROM BdEe WHERE userId=? AND eeDelstatus='N'";
        return commonDao_bd_write.queryString(sql, new Object[]{userId});
    }

    /**
     * 查询员工部门,分校信息
     */
    @SuppressWarnings({"unchecked", "StringConcatenationInsideStringBufferAppend"})
    public Map findOrgInfoByUserId(String eeId) throws Exception {
        StringBuffer sb_sql = new StringBuffer();
        sb_sql.append(" SELECT ee.eeId,dp.dptId,dp.dptName,dp.dptCode,dp.dptLevelcode,dp.dptRelationid as schoolId");
        sb_sql.append(" from BdEe ee ");
        sb_sql.append(" LEFT JOIN BdDptee de on ee.eeId = de.eeId ");
        sb_sql.append(" LEFT JOIN BdDpt dp on dp.dptId = de.dptId ");
        sb_sql.append(" where ee.eeId=:eeId ");
        sb_sql.append(" AND de.dpteeRelation = 'P' ");
        Map resultMap = commonDao_bd_write.queryMap(sb_sql.toString(), MapUtils.toMap(new Object[][]{{"eeId", eeId}}), null);

        if (resultMap != null) {
            if (ObjectUtils.isEmpty(resultMap.get("schoolId"))) {
                if (resultMap.get("dptLevelcode") == null) {
                    resultMap.put("schoolName", null);
                } else {
                    String dptLevelcode = String.valueOf(resultMap.get("dptLevelcode"));

                    List levelcodeList = new ArrayList();
                    while (dptLevelcode.length() > UcConst.LEVELCODE_LENGTH) {
                        dptLevelcode = dptLevelcode.substring(0, dptLevelcode.length() - UcConst.LEVELCODE_LENGTH);
                        levelcodeList.add(dptLevelcode);
                    }
                    if (CollectionUtils.isEmpty(levelcodeList)) {
                        resultMap.put("schoolName", null);
                        return resultMap;
                    }
                    sb_sql = new StringBuffer();
                    sb_sql.append(" SELECT sc.schoolId,sc.schoolName ");
                    sb_sql.append(" from BdSchool sc ");
                    sb_sql.append(" LEFT JOIN BdDpt dp ON sc.schoolId = dp.dptRelationid ");
                    sb_sql.append(" WHERE dp.dptLevelcode in ( " + SQLUtils.getIn(levelcodeList) + ") ");
                    Map schoolMap = commonDao_bd_write.queryMap(sb_sql.toString(), null, null);
                    if (schoolMap != null) {
                        resultMap.put("schoolId", schoolMap.get("schoolId"));
                        resultMap.put("schoolName", schoolMap.get("schoolName"));
                    } else {
                        resultMap.put("schoolName", null);
                    }
                }

            } else {
                String schoolId = String.valueOf(resultMap.get("schoolId"));
                BdSchool bdSchool = ucSchoolService.findById(schoolId);
                if (bdSchool != null) {
                    resultMap.put("schoolName", bdSchool.getSchoolName());
                } else {
                    resultMap.put("schoolName", null);
                }
            }

            if (!ObjectUtils.isEmpty(resultMap.get("schoolId"))) {
                sb_sql = new StringBuffer();
                sb_sql.append(" SELECT dptId as relationId,dptName as relationName");
                sb_sql.append(" from BdDpt  ");
                sb_sql.append(" where dptRelationid =:dptRelationid ");
                Map dptMap = commonDao_bd_write.queryMap(sb_sql.toString(), MapUtils.toMap(new Object[][]{{"dptRelationid", resultMap.get("schoolId")}}), null);
                if (!isEmpty(dptMap)) {
                    resultMap.put("relationId", dptMap.get("relationId"));
                    resultMap.put("relationName", dptMap.get("relationName"));
                }
            }
            return resultMap;
        }
        return new HashMap();
    }

    public void checkWorkStatus(UcUser user, UcLoginlog loginlog) throws Exception {
        if (!user.isEe()) {
            return;
        }

        String userId = user.getUserId();
        String sql = String.format("SELECT eeWorkstatus FROM BdEe WHERE userId='%s' AND eeDelstatus='N'", userId);
        String workStatus = commonDao_bd_write.queryString(sql, new Object[]{});
        if (hasLeft(workStatus)) {
            String eMsg = "员工已离职";
            loginlog.setLogLoginerrormsg(eMsg);
            CoreLogUtil.send(loginlog);
            throw new ApplicationException(EE_HAS_LEFT, eMsg, null);
        }
    }

    private boolean hasLeft(String workStatus) {
        return "L".equals(workStatus);
    }


    /**
     * 模糊查询-用于员工搜索下拉框
     */
    @SuppressWarnings("unchecked")
    public List<Map> listforSearch(String userName, String eeAvlstatus, String eeEdutype, String eeWorkstatus, String schoolId, Integer pageIndex, Integer pageSize) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ee.eeId, ee.userId, ee.userName, ee.eeWorkstatus, ee.schoolId, sc.schoolName ");
        sql.append("FROM BdEe ee ");
        sql.append("LEFT JOIN BdSchool sc on ee.schoolId = sc.schoolId ");

        // 筛选查询条件
        sql.append("WHERE ee.userName LIKE '%").append(userName).append("%' ");
        sql.append("AND eeDelstatus = 'N' ");
        if(!isEmpty(eeWorkstatus)){
            sql.append(" AND ee.eeWorkstatus= '").append(eeWorkstatus).append("' ");
        }
        if(!isEmpty(eeEdutype)) {
            sql.append("AND ee.eeEdutype = '").append(eeEdutype).append("' ");
        }
        if(!isEmpty(eeAvlstatus)) {
            sql.append("AND ee.eeAvlstatus = '").append(eeAvlstatus).append("' ");
        }
        if(!isEmpty(schoolId)) {
            sql.append("AND ee.schoolId = '").append(schoolId).append("' ");
        }
        sql.append("ORDER BY CHAR_LENGTH(ee.userName), ee.eeWorkstatus DESC ");
        sql.append("LIMIT ").append(pageIndex).append(",").append(pageSize);

        // 查询员工数据
        List<Map> eelist = commonDao_bd_write.queryMaps(sql.toString(), null, null);

        List<String> eeids = eelist.stream().map(map -> map.get("eeId").toString()).collect(Collectors.toList());
        // 查询所属部门数据
        if(eeids.size() > 0){
            fillDptNameByEeids(eeids, eelist);
        }

        return eelist;
    }

    /**
     * 根据员工标识，查询员工对应的所属部门，并合并到员工列表中
     */
    @SuppressWarnings({"unchecked", "WeakerAccess"})
    public void fillDptNameByEeids(List<String> eeids, List<Map> eeMaps) throws Exception{

        String sql = "SELECT " +
                "de.eeId, de.dptId, dp.dptName " +
                "FROM BdDptee de " +
                "LEFT JOIN BdDpt dp ON de.dptId = dp.dptId " +
                "WHERE dpteeRelation = 'P' " +
                "AND eeId IN (" + SQLUtils.getIn(eeids) + ") ";
        List<Map> dptlist = commonDao_bd_write.queryMaps(sql,null,null);

        Map<String, Map> dptmaps = new HashMap<>();
        if(dptlist.size() > 0){
            dptmaps = dptlist.stream().collect(Collectors.toMap(map -> map.get("eeId").toString(), Function.identity(), (k1, k2) -> k1));
        }
        // 添加部门标识到员工数据中
        String eeid;
        for(Map eemap : eeMaps){
            eeid = eemap.get("eeId").toString();
            if(isEmpty(dptmaps.get(eeid))){
                eemap.put("dptId", "");
                eemap.put("dptName", "");
            }else {
                eemap.put("dptId", dptmaps.get(eeid).get("dptId"));
                eemap.put("dptName", dptmaps.get(eeid).get("dptName"));
            }
        }
    }

    /**
     * 根据部门，查询直属员工及子部门
     */
    @SuppressWarnings({"StringConcatenationInsideStringBufferAppend", "unchecked"})
    public Map<String, List<Map>> findDptAndEeByDptId(String dptId, String eeSearchtype, String userName, String eeEdutype) throws DBException {
        Map<String, List<Map>> map = new LinkedHashMap<>();

        // 暂时定义三个 0 为获取到根节点下的大区部门
        if("000".equals(dptId)){
            String dptsql = "SELECT dpt.dptId, dpt.dptName FROM BdDpt dpt WHERE LENGTH(dpt.dptLevelcode) = "+ UcConst.LEVELCODE_LENGTH  +
                    " AND dpt.dptAvlstatus = 'Y' AND dpt.dptDelstatus = 'N' " +
                    "ORDER BY dptSeq, dptLevelcode";
            List<Map> dptlist = commonDao_bd_write.queryMaps(dptsql, new Object[]{}, null);
            map.put("ees", new ArrayList<>());
            map.put("dpts", dptlist);

        }else {
            // 获取所有子级部门的信息
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT paren.dptLevelcode parent, dpt.dptId, dpt.dptName, dpt.dptLevelcode son FROM BdDpt dpt ");
            sql.append("JOIN (SELECT dptLevelcode FROM BdDpt WHERE dptId = ?) paren ");
            sql.append("WHERE dpt.dptLevelcode LIKE CONCAT(paren.dptLevelcode,'%') ");
            sql.append("AND dpt.dptAvlstatus = 'Y' AND dpt.dptDelstatus = 'N' ");
            sql.append("ORDER BY LENGTH(dpt.dptLevelcode), dpt.dptSeq, dpt.dptLevelcode");
            List<Map> dptlist = commonDao_bd_write.queryMaps(sql.toString(), new Object[]{dptId}, null);

            if(dptlist.size() > 0){
                // [枚举值]A:全部员工;D:直属员工
                List<String> eeDptlist = new ArrayList<>();
                if("A".equals(eeSearchtype)){
                    eeDptlist = dptlist.stream().map(map1 -> map1.get("dptId").toString()).collect(Collectors.toList());
                }else {
                    eeDptlist.add(dptId);
                }

                // 获取所有子级部门的员工信息
                sql = new StringBuilder();
                sql.append("SELECT ee.eeId, ee.userId, ee.userName, dee.dptId ");
                sql.append("FROM BdEe ee ");
                sql.append("LEFT JOIN BdDptee dee ON dee.eeId = ee.eeId ");
                sql.append("WHERE dee.dptId IN (" + SQLUtils.getIn(eeDptlist)+ ") AND dpteeRelation = 'P' ");
                sql.append("AND eeWorkstatus = 'O' AND eeAvlstatus = 'Y' AND eeDelstatus = 'N' ");
                if(!isEmpty(userName)){
                    sql.append("AND userName LIKE '%").append(userName).append("%' ");
                }
                if(!isEmpty(eeEdutype)){
                    sql.append("AND eeEdutype = '").append(eeEdutype).append("' ");
                }
                List<Map> eelist = commonDao_bd_write.queryMaps(sql.toString(), null, null);
                map.put("ees", eelist);

                // 获取所有直属子级部门的信息
                List<Map> dplist = new ArrayList<>();
                String plevelcode = dptlist.get(0).get("parent").toString();
                dptlist.stream().filter(map1 -> {
                    //noinspection RedundantIfStatement
                    if(map1.get("son").toString().length() - plevelcode.length() == UcConst.LEVELCODE_LENGTH){
                        return true;
                    }else {
                        return false;
                    }
                }).collect(Collectors.toList()).forEach(map1 -> {
                    map1.remove("parent");
                    map1.remove("son");
                    dplist.add(map1);
                });
                map.put("dpts", dplist);

            }else {
                map.put("ees", new ArrayList<>());
                map.put("dpts", new ArrayList<>());
            }
        }
        return map;
    }

    /**
     * 分页数据
     *
     * @param mWhere    where条件
     * @param mGroup    group
     * @param mOrder    排序
     * @param pageIndex 分页起始
     * @param pageCount 分页数量
     * @param auth      true/带权限  false不带权限
     */
    @SuppressWarnings("unchecked")
    public QueryResult queryMapsCount(Map mWhere, Map mGroup, Map mOrder, int pageIndex, int pageCount, boolean auth)
            throws Exception {
        QueryResult obj = new QueryResult();
        int count = 0;
        boolean hasDpt = false;

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ee.eeModifieddate,ee.eeCreater,ee.eeModifier,ee.eeId, ee.userId, ee.schoolId, ee.userName, ee.eePhone, ee.eeInnerphone, eeRoles,eeBrands, ee.eeNo, " +
                "ee.eeWorkstatus, ee.eeOfficialstatus, ee.eeEdutype, ee.eeCreateddate, sc.schoolName, ");
        if (!isEmpty(mWhere.get("dptId"))) {
            sql.append("dee.dpteeId ");
            hasDpt = true;
        } else {
            sql.append("'' as dpteeId ");
        }
        sql.append("FROM BdEe ee ");
        sql.append("LEFT JOIN BdSchool sc on ee.schoolId = sc.schoolId ");
        // 筛选查询条件
        argsSql(mWhere, sql, hasDpt, auth);
        sql.append("AND ee.eeEhrstatus='I' "); // 只要优路员工的数据
        sql.append("ORDER BY ee.eeCreateddate DESC ");
        sql.append("LIMIT ").append(pageIndex).append(",").append(pageCount);
        // 查询分页数据
        List<Map> data = commonDao_bd_write.queryMaps(sql.toString(), null, null);

        if (data.size() > 0) {
            sql = new StringBuilder();
            sql.append("SELECT dee.eeId, dee.dptId, dee.posId, dpt.dptName, pos.posName FROM BdDptee dee ");
            sql.append("LEFT JOIN BdDpt dpt on dee.dptId = dpt.dptId ");
            sql.append("LEFT JOIN BdPos pos on dee.posId = pos.posId ");

            // 是否按照部门查询
            if (hasDpt) {
                List<String> dpteeIds = data.stream().map(map -> map.get("dpteeId").toString()).collect(Collectors.toList());
                sql.append("WHERE dee.dpteeId IN (").append(SQLUtils.getIn(dpteeIds)).append(") ");
            } else {
                List<String> eeIds = data.stream().map(map -> map.get("eeId").toString()).collect(Collectors.toList());
                sql.append("WHERE dee.eeId IN (").append(SQLUtils.getIn(eeIds)).append(") ");
                sql.append("AND dee.dpteeRelation = 'P' ");
            }
            List<Map> dptlist = commonDao_bd_write.queryMaps(sql.toString(), null, null);
            Map<String, Map> dptmaps = dptlist.stream().collect(Collectors.toMap(map -> map.get("eeId").toString(), Function.identity(), (k1, k2) -> k2));

            // 添加部门标识到员工数据中
            String eeid;
            for (Map eemap : data) {
                if (null != eemap.get("eePhone") && !StringUtils.isEmpty((String) eemap.get("eePhone"))) {
                    eemap.put("eePhone", eemap.get("eePhone").toString().replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
                }
                if (null != eemap.get("eeInnerphone") && !StringUtils.isEmpty((String) eemap.get("eeInnerphone"))) {
                    eemap.put("eeInnerphone", eemap.get("eeInnerphone").toString().replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
                }
                eemap.remove("dpteeId");
                eeid = eemap.get("eeId").toString();
                if (null == dptmaps.get(eeid)) {
                    eemap.put("dptId", "");
                    eemap.put("dptName", "");
                    eemap.put("posId", "");
                    eemap.put("posName", "");
                } else {
                    eemap.putAll(dptmaps.get(eeid));
                }
                eemap.put("userAvlstatus", "");
            }

            // 查询数据总数
            sql = new StringBuilder();
            sql.append("SELECT COUNT(1) FROM BdEe ee ");
            argsSql(mWhere, sql, hasDpt, auth);
            sql.append(" AND ee.eeEhrstatus='I' "); // 只要优路员工的数据
            count = commonDao_bd_write.queryInt(sql.toString(), null);

            setUserNameByData(data);
        }
        obj.setCount(count);
        obj.setData(data);
        return obj;
    }

    /**
     * 用于转换创建人id和修改人id为创建人姓名和修改人姓名，适用于查询处的数据list为List<Map>且map中创建人id和修改人id分别为*Creater和*Modifier
     *
     * @param data 待转换数据
     * <AUTHOR> 2020/01/02 15:33
     */
    @SuppressWarnings({"unchecked", "SuspiciousMethodCalls"})
    private void setUserNameByData(List<Map> data) throws Exception {
        Set<String> userIds = new HashSet<>();
        //员工ids
        Set<String> eeIds = new HashSet<>();
        for (Map map : data) {
            Set<Map.Entry<String, Object>> entrySet = map.entrySet();
            for (Map.Entry entry : entrySet) {
                if (!ObjectUtils.isEmpty(entry.getValue()) && (entry.getKey().toString().endsWith("Creater") || entry.getKey().toString().endsWith("Modifier"))) {
                    userIds.add(entry.getValue().toString());
                }
            }
            if (!ObjectUtils.isEmpty(map.get("userId"))) {
                eeIds.add(map.get("userId").toString());
            }
        }
        List<Map> usersMap = ucUserService.findByUserIds(new ArrayList<>(userIds), null, false);
        if (CollectionUtils.isEmpty(usersMap)) {
            return;
        }
        List<UcUser> users = BeanUtils.setBeansByClass(UcUser.class, usersMap);
        Map<String, String> userMap = users.stream().collect(Collectors.toMap(UcUser::getUserId, s -> s.getUserName() == null ? "" : s.getUserName()));
        //员工列表，用来获取员工的UcUser表信息
        List<Map> eeMaps = ucUserService.findByUserIds(new ArrayList<>(eeIds), null, false);
        if (CollectionUtils.isEmpty(eeMaps)) {
            return;
        }
        for (Map map : data) {
            Set<Map.Entry<String, Object>> entrySet = map.entrySet();
            for (Map.Entry entry : entrySet) {
                if (!ObjectUtils.isEmpty(entry.getValue()) && (entry.getKey().toString().endsWith("Creater") || entry.getKey().toString().endsWith("Modifier"))) {
                    entry.setValue(userMap.get(entry.getValue()));
                }
                if ((entry.getKey().toString().endsWith("userAvlstatus"))) {
                    List<UcUser> ees = BeanUtils.setBeansByClass(UcUser.class, eeMaps);
                    Map<String, String> userAvlstatus = ees.stream().collect(Collectors.toMap(UcUser::getUserId, s -> s.getUserAvlstatus() == null ? "" : s.getUserAvlstatus()));
                    entry.setValue(userAvlstatus.get(map.get("userId")));
                }
            }
        }
    }


    /**
     * 分页数据筛选查询条件
     *
     * @param mWhere 查询条件
     * @param sql    sql
     * @param hasDpt 是否按照部门查询
     * @param auth   是否按照权限查询
     */
    private void argsSql(Map mWhere, StringBuilder sql, boolean hasDpt, boolean auth) {
        // 是否按照部门查询
        if (hasDpt) {
            sql.append("LEFT JOIN BdDptee dee ON dee.eeId = ee.eeId ");
            sql.append("WHERE dee.dptId IN (");

            if (auth) {
                sql.append(SQLUtils.getIn(((String) mWhere.get("dptId")).split(",")));
            } else {
                sql.append("SELECT dpt.dptId FROM BdDpt dpt JOIN (SELECT dptId, dptLevelcode FROM BdDpt WHERE dptId in (");
                sql.append(SQLUtils.getIn(((String) mWhere.get("dptId")).split(","))).append(")) d WHERE dpt.dptLevelcode LIKE CONCAT(d.dptLevelcode, '%')");
            }

            sql.append(") ");
            sql.append("AND dee.dpteeRelation = 'P' ");
        } else {
            sql.append("WHERE 1=1 ");
        }
        if (!isEmpty(mWhere.get("userName"))) {
            sql.append("AND ee.userName LIKE '%");
            sql.append(mWhere.get("userName"));
            sql.append("%' ");
        }
        if (!isEmpty(mWhere.get("eePhone"))) {
            sql.append("AND ee.eePhone = '");
            sql.append(mWhere.get("eePhone"));
            sql.append("' ");
        }
        if (!isEmpty(mWhere.get("eeInnerphone"))) {
            sql.append("AND ee.eeInnerphone = '");
            sql.append(mWhere.get("eeInnerphone"));
            sql.append("' ");
        }
        if (!isEmpty(mWhere.get("eeNo"))) {
            sql.append("AND ee.eeNo = '");
            sql.append(mWhere.get("eeNo"));
            sql.append("' ");
        }
        if (!isEmpty(mWhere.get("schoolId"))) {
            sql.append("AND ee.schoolId = '");
            sql.append(mWhere.get("schoolId"));
            sql.append("' ");
        }
        if (!isEmpty(mWhere.get("eeWorkstatus"))) {
            sql.append(" AND ee.eeWorkstatus= '").append(mWhere.get("eeWorkstatus")).append("' ");
        }
        if (!isEmpty(mWhere.get("eeOfficialstatus"))) {
            sql.append(" AND ee.eeOfficialstatus= '").append(mWhere.get("eeOfficialstatus")).append("' ");
        }
        if (!isEmpty(mWhere.get("eeEdutype"))) {
            sql.append("AND ee.eeEdutype = '");
            sql.append(mWhere.get("eeEdutype"));
            sql.append("' ");
        }
        if (!isEmpty(mWhere.get("startDate"))) {
            sql.append("AND ee.eeCreateddate >= '");
            sql.append(mWhere.get("startDate"));
            sql.append("' ");
        }
        if (!isEmpty(mWhere.get("endDate"))) {
            sql.append("AND ee.eeCreateddate <= '");
            sql.append(mWhere.get("endDate"));
            sql.append("' ");
        }
        if (!isEmpty(mWhere.get("modifiedStartDate"))) {
            sql.append("AND ee.eeModifieddate >= '");
            sql.append(mWhere.get("modifiedStartDate"));
            sql.append("' ");
        }
        if (!isEmpty(mWhere.get("modifiedEndDate"))) {
            sql.append("AND ee.eeModifieddate <= '");
            sql.append(mWhere.get("modifiedEndDate"));
            sql.append("' ");
        }
        if (!isEmpty(mWhere.get("eeAvlstatus"))) {
            sql.append("AND ee.eeAvlstatus = '");
            sql.append(mWhere.get("eeAvlstatus"));
            sql.append("' ");
        }
        if (!isEmpty(mWhere.get("eeDelstatus"))) {
            sql.append("AND ee.eeDelstatus = '");
            sql.append(mWhere.get("eeDelstatus"));
            sql.append("' ");
        }
        if (!isEmpty(mWhere.get("eeRole"))) {
            sql.append("AND ee.eeRoles like '%")
                    .append(mWhere.get("eeRole"))
                    .append("%' ");
        }
        if (!isEmpty(mWhere.get("eeBrand"))) {
            sql.append("AND ee.eeBrands like '%")
                    .append(mWhere.get("eeBrand"))
                    .append("%' ");
        }
    }


    /**
     * 编辑，并保存角色
     * todo 全局性事务
     */
    @Transactional
    public void edit(BdEe bdEe, String brandId, String currUserId, List<String> eeRoles, List<String> eeBrands, String tagType, String tagAvlstatus) throws Exception {
        boolean setRoles = false;
        if (eeRoles != null && eeRoles.size() > 0) {
            String s = eeRoles.toString().replaceAll("\\s", "");
            String substring = s.substring(1, s.length() - 1);
            if (bdEe.getEeRoles() == null || !bdEe.getEeRoles().equals(substring)) {
                bdEe.setEeRoles(substring);
                setRoles = true;
            }
        } else {
            bdEe.setEeRoles(null);
            eeRoles = new ArrayList<>();
            setRoles = true;
        }

        boolean setBrands = false;
        if (eeBrands != null && eeBrands.size() > 0) {
            String s = eeBrands.toString().replaceAll("\\s", "");
            String substring = s.substring(1, s.length() - 1);
            if (bdEe.getEeBrands() == null || !bdEe.getEeBrands().equals(substring)) {
                bdEe.setEeBrands(substring);
                setBrands = true;
            }
        } else {
            bdEe.setEeBrands(null);
            eeBrands = new ArrayList<>();
            setBrands = true;
        }

        //DataPushUtils.pushMessage(new Object[]{bdEe}, "BdEe", Const.TYPE_UPDATE);
        String userId = bdEe.getUserId();
        try {
            if (setRoles) {
                String[] strings = ucUserroleService.serUserRoles(currUserId, userId, eeRoles, brandId);
                if (strings != null) {
                    String s = Arrays.asList(strings).toString().replaceAll("\\s", "");
                    bdEe.setEeRoles(s.substring(1, s.length() - 1));
                }
            }

            if (setBrands) {
                ucUserbrandService.setUserBrand(currUserId, userId, eeBrands);
            }
        } catch (Exception e) {
            throw new DBException("修改失败" + e.getMessage(), e);
        }
        commonDao_bd_write.update(bdEe);
        //修改答疑标签
        if (!isEmpty(tagType) && !isEmpty(tagAvlstatus)) {
            UcUserTag ucUserTag = ucTagService.modifyTag(bdEe.getUserId(), currUserId, tagType, tagAvlstatus, null);
            Map data = BeanUtils.bean2Map(ucUserTag);
            data.put("eeWorkstatus", bdEe.getEeWorkstatus());
            data.put("teacherName",bdEe.getUserName());
            data.put("eeNo",bdEe.getEeNo());
            //答疑老师所属分校
            data.put("schoolId",bdEe.getSchoolId());
            String schoolName = commonDao_bd_write.queryString("select schoolName from BdSchool where schoolId = '" + bdEe.getSchoolId() + "'", null);
            data.put("schoolName",schoolName);
            mqService.publish(UcConst.Mqtype.USER_MQ_TAG_EDIT, data);
        }
    }


}