package com.niceloo.uc.handler;

import static org.nobject.common.lang.StringUtils.isEmpty;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JComboBox.KeySelectionManager;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.MethodDesc;
import org.nobject.common.code.describer.ParamDesc;
import org.nobject.common.code.describer.Return2Desc;
import org.nobject.common.code.describer.ReturnDesc;
import org.nobject.common.code.describer.Validate;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;
import org.nobject.common.log.Logger;

import com.niceloo.core.bean.CoreConst.ErrorCode;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.mvc.MVCUtils;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.uc.common.UcConfig.Uk;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.common.UcConst.GrayType;
import com.niceloo.uc.model.UcCltgray;
import com.niceloo.uc.model.UcUser;
import com.niceloo.uc.service.UcCltgrayService;
import com.niceloo.uc.service.UcUserService;
import com.niceloo.uc.utils.AESUtils;

/**
 * 客户端灰度-业务处理类
 * <AUTHOR>
 * @Date 2019-09-25
 */
@Service
@CoreRule(protocol = "uc/cltgray")
public class UcCltgrayHandler {
	
	/** logger */
	private Logger logger = Logger.getLogger(UcCltgrayHandler.class);
	@Autowired
	private UcCltgrayService ucCltgrayService;
	@Autowired
	private UcUserService ucUserService;
	/**
	 * 客户端灰度-添加
	 * @param clttypeId 客户端类型标识
	 * @param cltgrayType 客户端灰度类型
	 * @param cltgrayObject 客户端灰度对象
	 * @param cltgrayObjectname 客户端灰度对象名称
	 * @param cltgrayCreatedate 客户端灰度创建时间
	 * @param cltgrayCreater 客户端灰度创建人
	 * @param cltgrayCreatername 客户端灰度创建人姓名
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "adds", needLogin = false)
	@MethodDesc(comment = "客户端灰度-批量添加", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "cltgrayId", comment = "客户端灰度标识", type = String.class)
	}))
	public Map add(
		@ParamDesc(comment="客户端类型标识",validate=Validate.R_ID,length = 36 ,unnull=true)	String clttypeId,	
		@ParamDesc(comment="客户端灰度类型",validate=Validate.M_SAFE,length = 1 ,unnull=true)	String cltgrayType,	
		@ParamDesc(comment="客户端灰度对象",validate=Validate.M_SAFE,length = 370 ,unnull=true)	String cltgrayObjects,	
		@ParamDesc(comment="用户标识",validate=Validate.R_ID,length = 36 ,unnull=true)		String userId,	
		@ParamDesc(comment="用户姓名",validate=Validate.M_SAFE,length = 200 ,unnull=true)		String userName,	
		Map $params
	)throws Exception {
		//用户手机号转换成用户标识
		String[] ids = cltgrayObjects.split(",");
		//把所有的手机号都转换成用户标识
		if(GrayType.USER.equals(cltgrayType)) {
			ids = convertMobileTouserId(ids);
		}
		String[] objects = ucCltgrayService.findByCltgrayObjectsAndclttypeId(ids,clttypeId,cltgrayType);
		ArrayList<String> diff = diff(ids,objects);
		Map userMap = null;
		if(GrayType.USER.equals(cltgrayType)) {
			userMap=ucUserService.getUserIdNameMap(ids);
		}
		//添加灰度对象到数据库中
		String cltgrayIds = "";
		if(diff != null && diff.size() > 0) {
			for (String id : diff) {
				UcCltgray ucCltgray = new UcCltgray();
				BeanUtils.setBean(ucCltgray, $params);
				ucCltgray.setCltgrayObject(id);
				if(GrayType.USER.equals(cltgrayType)) {
					if(!userMap.containsKey(id)) {
						throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT,id+"用户不存在",null);
					}
					ucCltgray.setCltgrayObjectname((String) userMap.get(id));
				}else {
					ucCltgray.setCltgrayObjectname(id);
				}
				ucCltgray.setCltgrayCreatedate(DateUtils.getNowDString());
				ucCltgray.setCltgrayCreater(userId);
				ucCltgray.setCltgrayCreatername(userName);
				ucCltgrayService.add(ucCltgray);
				cltgrayIds+=ucCltgray.getCltgrayId()+",";
			}
		}else {
			throw new ApplicationException(ErrorCode.argument_invalide, "需要添加的客户端灰度都已存在", null, null);
		}
		return MapUtils.toMap(new Object[][] { { "cltgrayIds", cltgrayIds } });
	}
	
	private String[] convertMobileTouserId(String[] ids) throws Exception {
		
		List<Map> users = ucUserService.findByuserMobiles(Arrays.asList(ids), "S","");
		if(users!= null && users.size() > 0) {
//			提取所有的手机号和用户标识对应关系
			HashMap<String,String> moblieUserIdMap = new HashMap<>();
			for (Map user : users) {
				moblieUserIdMap.put((String)user.get("userMobile"), (String)user.get("userId"));
			}
			for (int i = 0; i < ids.length; i++) {
				String userId = moblieUserIdMap.get(ids[i]);
				if(!StringUtils.isEmpty(userId)) {
					ids[i] = userId;
				}
			}
		}
		return ids;
	}

	/**
	 * 获取未添加的对象集合
	 * @param ids
	 * @param objects
	 * @return
	 */
	private ArrayList<String> diff(String[] ids, String[] objects) {
		ArrayList<String> resoutList= new ArrayList<>(Arrays.asList(ids));
		for (String string : objects) {
			boolean contains = resoutList.contains(string);
			if(contains) {
				int index=getListIndex(resoutList,string);
				if(-1 != index) {
					resoutList.remove(index);
				}
			}
		}
		return resoutList;
	}
	
	/**
	 * 获取元素在集合中的位置
	 * @param list
	 * @param string
	 * @return
	 */
	private int getListIndex(ArrayList<String> list,String string) {
		for (int i = 0; i < list.size(); i++) {
			if(string.equals(list.get(i))) {
				return i;
			}
		}
		return -1;
	}

	/**
	 * 客户端灰度-删除
	 * @param cltgrayId 客户端灰度标识
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "deletes", needLogin = false)
	@MethodDesc(comment = "客户端灰度-删除", returns = @ReturnDesc(subs = {}))
	public void delete(
		@ParamDesc(comment="客户端灰度标识",validate=Validate.R_IDS,unnull = true)	String cltgrayIds,	
		Map $params
	)throws Exception { 
		ucCltgrayService.delete(cltgrayIds);
	}

	/**
	 * 客户端灰度-修改
	 * @param cltgrayId 客户端灰度标识
	 * @param clttypeId 客户端类型标识
	 * @param cltgrayType 客户端灰度类型
	 * @param cltgrayObject 客户端灰度对象
	 * @param cltgrayObjectname 客户端灰度对象名称
	 * @param cltgrayCreatedate 客户端灰度创建时间
	 * @param cltgrayCreater 客户端灰度创建人
	 * @param cltgrayCreatername 客户端灰度创建人姓名
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "edit", needLogin = false)
	@MethodDesc(comment = "客户端灰度-编辑", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "cltgrayId", comment = "客户端灰度标识", type = String.class)
	}))
	public void edit(
		@ParamDesc(comment="客户端灰度标识",validate=Validate.R_ID,length = 36 ,unnull=true)	String cltgrayId,	
		@ParamDesc(comment="客户端类型标识",validate=Validate.R_ID,length = 36 ,unnull=true)	String clttypeId,	
		@ParamDesc(comment="客户端灰度类型",validate=Validate.M_SAFE,length = 1 ,unnull=true)	String cltgrayType,	
		@ParamDesc(comment="客户端灰度对象",validate=Validate.M_SAFE,length = 200 ,unnull=true)	String cltgrayObject,	
		@ParamDesc(comment="客户端灰度对象名称",validate=Validate.M_SAFE,length = 200 ,unnull=true)	String cltgrayObjectname,	
		@ParamDesc(comment="客户端灰度创建时间",validate=Validate.M_SAFE,length = 19 ,unnull=true)	String cltgrayCreatedate,	
		@ParamDesc(comment="客户端灰度创建人",validate=Validate.M_SAFE,length = 36 ,unnull=true)	String cltgrayCreater,	
		@ParamDesc(comment="客户端灰度创建人姓名",validate=Validate.M_SAFE,length = 200 ,unnull=true)	String cltgrayCreatername,	
		Map $params
	)throws Exception {
		UcCltgray ucCltgray = ucCltgrayService.findById(cltgrayId);
		if (ucCltgray == null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "客户端灰度不存在", null, null);
		}
		ucCltgray.setClttypeId(clttypeId);
		ucCltgray.setCltgrayType(cltgrayType);
		ucCltgray.setCltgrayObject(cltgrayObject);
		ucCltgray.setCltgrayObjectname(cltgrayObjectname);
		
		ucCltgrayService.edit(ucCltgray);
	}

	/**
	 * 客户端灰度-详情
	 * @param cltgrayId 客户端灰度标识
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "info", needLogin = false)
	@MethodDesc(comment = "客户端灰度-详情", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "cltgrayId", comment = "客户端灰度标识", type = String.class),
			@Return2Desc(name = "clttypeId", comment = "客户端类型标识", type = String.class),
			@Return2Desc(name = "cltgrayType", comment = "客户端灰度类型", type = String.class),
			@Return2Desc(name = "cltgrayObject", comment = "客户端灰度对象", type = String.class),
			@Return2Desc(name = "cltgrayObjectname", comment = "客户端灰度对象名称", type = String.class),
			@Return2Desc(name = "cltgrayCreatedate", comment = "客户端灰度创建时间", type = String.class),
			@Return2Desc(name = "cltgrayCreater", comment = "客户端灰度创建人", type = String.class),
			@Return2Desc(name = "cltgrayCreatername", comment = "客户端灰度创建人姓名", type = String.class),
	}))
	public Map info(
		@ParamDesc(comment="客户端灰度标识",validate=Validate.R_ID,unnull=true,length=36)	String cltgrayId,	
		Map $params
	)throws Exception {
		UcCltgray ucCltgray = ucCltgrayService.findById(cltgrayId);
		if (ucCltgray == null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "客户端灰度不存在", null, null);
		}
		//清除掉用户隐私信息
		Map rtn=BeanUtils.bean2Map(ucCltgray);
		return rtn;
	}	


	/**
	 * 客户端灰度-分页
	 * @param pageIndex 分页起始
	 * @param pageSize 分页数量
	 * @params orderKey 排序字段
	 * @params orderVal 排序字段
	 * @params cltgrayId 客户端灰度标识
	 * @params clttypeId 客户端类型标识
	 * @params cltgrayType 客户端灰度类型
	 * @params cltgrayObject 客户端灰度对象
	 * @params cltgrayObjectname 客户端灰度对象名称
	 * @params cltgrayCreatedate 客户端灰度创建时间
	 * @params cltgrayCreater 客户端灰度创建人
	 * @params cltgrayCreatername 客户端灰度创建人姓名
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "list", needLogin = false)
	@MethodDesc(comment = "客户端灰度-分页", returns = @ReturnDesc(subs = {
		@Return2Desc(name="count",comment="数量",type=Integer.class),
		@Return2Desc(name="data",comment="数据",type=List.class),
		@Return2Desc(name = "data[n].cltgrayId", comment = "客户端灰度标识", type = String.class),
		@Return2Desc(name = "data[n].clttypeId", comment = "客户端类型标识", type = String.class),
		@Return2Desc(name = "data[n].cltgrayType", comment = "客户端灰度类型", type = String.class),
		@Return2Desc(name = "data[n].cltgrayObject", comment = "客户端灰度对象", type = String.class),
		@Return2Desc(name = "data[n].cltgrayObjectname", comment = "客户端灰度对象名称", type = String.class),
		@Return2Desc(name = "data[n].cltgrayCreatedate", comment = "客户端灰度创建时间", type = String.class),
		@Return2Desc(name = "data[n].cltgrayCreater", comment = "客户端灰度创建人", type = String.class),
		@Return2Desc(name = "data[n].cltgrayCreatername", comment = "客户端灰度创建人姓名", type = String.class),
	}))
	public Map list(
		@ParamDesc(comment="分页起始",validate=Validate.M_STARTINDEX,defVal = "0")	Integer pageIndex,
		@ParamDesc(comment="分页数量",validate=Validate.M_COUNT,defVal = "10")	Integer pageSize,
		@ParamDesc(comment="排序字段")	String orderKey,
		@ParamDesc(comment="排序字段",validate="R:[YN]",memo="[枚举]Y:正序;N:倒序")	String orderVal,
		@ParamDesc(comment="组合条件")	Map searchCombination,
		@ParamDesc(comment="客户端灰度标识",validate=Validate.R_ID,memo="客户端灰度标识")	String cltgrayId,
		@ParamDesc(comment="客户端类型标识",validate=Validate.R_ID,memo="客户端类型标识")	String clttypeId,
		@ParamDesc(comment="客户端灰度类型",validate=Validate.M_SAFE,memo="客户端灰度类型")	String cltgrayType,
		@ParamDesc(comment="客户端灰度对象",validate=Validate.M_SAFE,memo="客户端灰度对象")	String cltgrayObject,
		@ParamDesc(comment="客户端灰度对象名称",validate=Validate.M_SAFE,memo="客户端灰度对象名称")	String cltgrayObjectname,
		Map $params
	)throws Exception {
		Map where=CoreUtils.getWhere($params);
		Map order=isEmpty(orderKey)?null:MapUtils.toMap(new Object[][]{
			{orderKey	,orderVal},
		});
		QueryResult qr=ucCltgrayService.findMapsCount(where, null, order, pageIndex, pageSize);
		return BeanUtils.bean2Map(qr);
	}
	
	/**
	 * 客户端灰度-校验
	 * @param cltgrayObject 灰度对象
	 * @param $params
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "check", needLogin = false)
	@MethodDesc(comment = "客户端灰度-校验", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "status", comment = "状态", type = String.class,memo = "[枚举]:Y:是;N:不是"),
	}))
	public Map check(
		@ParamDesc(comment="客户端灰度对象",validate=Validate.R_ID,unnull=true,length=36)	String cltgrayObject,
		Map $params
	)throws Exception {
		
		UcCltgray findByCols = ucCltgrayService.findByCols(MapUtils.toMap(new Object[][] {
			{"cltgrayObject",cltgrayObject}
		}));
		String status = "N";
		if(findByCols != null) {
			status = "Y";
		}
		return MapUtils.toMap(new Object[][] {
			{"status",status}
		});
	}
	
	/**
	 * 客户端灰度-uk校验
	 * @param cltgrayObject 灰度对象
	 * @param $params
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "user/check", needLogin = false)
	@MethodDesc(comment = "灰度用户-校验", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "status", comment = "状态", type = String.class,memo = "[枚举]:Y:是;N:不是"),
	}))
	public Map user_check(
		@ParamDesc(comment="用户来源标识",validate=Validate.M_SAFE,unnull=true)	String userSourceid,
		Map $params
	)throws Exception {
			
		UcUser user = null;
		user = ucUserService.queryBySourceid$Flag$Avlstatus(userSourceid, UcConst.UserFlag.student, null);
		if(user == null) {
			return MapUtils.toMap(new Object[][] {
				{"status","N"}
			});
		}else {
			return check(user.getUserId(), MapUtils.toMap(new Object[][] {
				{"cltgrayObject",user.getUserId()}
			}));
		}
	}	
}