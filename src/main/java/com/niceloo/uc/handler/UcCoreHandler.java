package com.niceloo.uc.handler;

import java.util.Map;

import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.MethodDesc;
import org.nobject.common.code.describer.ParamDesc;
import org.nobject.common.code.describer.ReturnDesc;
import org.nobject.common.code.describer.Validate;

import com.niceloo.core.utils.CoreRule;

/**
 * Uc<PERSON>oreHandler
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@CoreRule(protocol="uc/core")
public class UcCoreHandler {

	/** 
	 * 登录检测
	 */
	@CoreRule(protocol="job/stop",needLogin=false)
	@MethodDesc(comment="登录检测",returns=@ReturnDesc(subs={
		
	}))
	public Map check(
		@ParamDesc(comment="任务标识"		,validate=Validate.R_ID) String jobId,
		@ParamDesc(comment="任务状态"		,validate=Validate.R_ID) String jobStatus
	)throws Exception{
		return null;
	}
	
}
