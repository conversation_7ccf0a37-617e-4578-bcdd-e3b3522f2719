package com.niceloo.uc.handler;

import com.niceloo.core.bean.CoreConst.ErrorCode;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.uc.model.UcMenu;
import com.niceloo.uc.model.UcRole;
import com.niceloo.uc.model.UcUser;
import com.niceloo.uc.model.UcUserrelation;
import com.niceloo.uc.service.*;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.*;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.lang.CollectionUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.SetUtils;
import org.nobject.common.log.Logger;

import java.util.*;
import java.util.stream.Collectors;

import static org.nobject.common.lang.StringUtils.isEmpty;

/**
 * 菜单-业务处理类
 *
 * <AUTHOR>
 * @Date 2019-11-18 10:35:12
 */
@SuppressWarnings("Duplicates")
@Service
@CoreRule(protocol = "uc/auth/user")
public class UcAuthHandler {

    private static final Logger LOGGER = Logger.getLogger(UcAuthHandler.class);

    @Autowired
    private UcMenuService ucMenuService;

    @Autowired
    private UcUserService ucUserService;

    @Autowired
    private UcUserrelationService ucUserrelationService;

    @Autowired
    private UcRoleService ucRoleService;

    @Autowired
    private UcUserbrandService ucUserbrandService;

    @Autowired
    private UcUserroleService ucUserroleService;

    @Autowired
    private UcRolemenuService ucRolemenuService;

    /**
     * 查询用户的菜单权限
     *
     * @return
     * @throws Exception
     */
    @CoreRule(protocol = "menucodes")
    @MethodDesc(comment = "获取用户的菜单权限(内部)", returns = @ReturnDesc(subs = {}, memo = "{menuId1:[policy1,policy2],menuId2:[policy1,policy2]}"))
    public Map getMenuCodesByUser(
            @ParamDesc(comment = "当前用户id", validate = Validate.R_ID, length = 36, memo = "当前的登录的用户id") String currUserId,
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 36, memo = "如果不传，则为当前登录的用户id") String userId,
            @ParamDesc(comment = "父菜单/模块的code", validate = Validate.M_SAFE, defVal = "0", memo = "获取此模块下所有页面/功能的权限") String menuCode,
            @ParamDesc(comment = "是否只查功能的", validate = Validate.M_SAFE, defVal = "N", memo = "枚举 Y:是 N:否") String onlyFunc,
            @ParamDesc(comment = "品牌id", validate = Validate.M_SAFE) String brandId,
            Map $params
    ) throws Exception {
        if (isEmpty(userId)) {
            userId = currUserId;
        }
        boolean onlyF = "Y".equalsIgnoreCase(onlyFunc);
        return ucUserroleService.getMenuCodesByUser(userId, brandId, menuCode, onlyF);
    }


    /**
     * 查询用户角色管理关系
     *
     * @return
     * @throws Exception
     */
    @SuppressWarnings("Duplicates")
    @CoreRule(protocol = "datascopes")
    @MethodDesc(comment = "获取用户的数据范围", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "dpt", comment = "部门id集合,ALL表示全部", type = List.class),
            @Return2Desc(name = "project", comment = "项目id集合,ALL表示全部", type = List.class),
            @Return2Desc(name = "usersource", comment = "客户来源code集合,ALL表示全部", type = List.class),

            @Return2Desc(name = "dptLevelcode", comment = "部门levelcode集合,ALL表示全部", type = List.class),
            @Return2Desc(name = "projectLevelcode", comment = "项目levelcode集合,ALL表示全部", type = List.class),
            @Return2Desc(name = "usersourceLevelcode", comment = "客户来源levelcode集合,ALL表示全部", type = List.class),
    }))
    public Map getUserDataScope(
            @ParamDesc(comment = "当前用户id", validate = Validate.R_ID, length = 36, memo = "当前的登录的用户id") String currUserId,
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 36, memo = "如果不传，则为当前用户的id") String userId,
            @ParamDesc(comment = "数据范围类型", validate = "R:(dpt)|(project)|(usersource)", length = 36,
                    memo = "枚举 部门:dpt 项目:project 客户来源:usersource 不传就是查全部") String userreType,
            @ParamDesc(comment = "品牌标识", validate = Validate.R_ID, unnull = true, memo = "不传，网关自动填写") String brandId,
            Map $params
    ) throws Exception {
        if (isEmpty(userId)) {
            userId = currUserId;
        }
//		UcUser ucUser = ucUserService.queryById(userId);
//		if (ucUser == null) {
//			throw new ApplicationException(ErrorCode.argument_invalide, "用户不存在", null, null);
//		}
        List<UcUserrelation> list = ucUserrelationService.queryRelationsByUserId(userId, userreType, brandId);
        Map<String, Set> map = new LinkedHashMap<>();
        if (list != null) {
            for (UcUserrelation ucUserrelation : list) {
                String userreType1 = ucUserrelation.getUserreType();
                Set list1 = map.get(userreType1);
                if (list1 == null) {
                    list1 = new LinkedHashSet();
                    map.put(userreType1, list1);
                }
                list1.add(ucUserrelation.getUserreRelationid());
                Set list2 = map.get(userreType1 + "Levelcode");
                if (list2 == null) {
                    list2 = new LinkedHashSet();
                    map.put(userreType1 + "Levelcode", list2);
                }
                list2.add(ucUserrelation.getUserreRelationlevelcode());
            }

            // 处理levelcode
            for (Map.Entry<String, Set> entry : map.entrySet()) {
                if (entry.getKey().endsWith("Levelcode")) {
                    Set<String> value = entry.getValue();
                    Iterator<String> iterator = value.iterator();
                    while (iterator.hasNext()) {
                        String levelcode = iterator.next();
                        while (levelcode != null && levelcode.length() > 10) {
                            levelcode = levelcode.substring(0, levelcode.length() - 10);
                            if (value.contains(levelcode)) {
                                iterator.remove();
                            }
                        }

                    }
                }
            }

        }
        HashSet value = new HashSet(0);
        if (isEmpty(userreType)) {
            if (map.get("dpt") == null) {
                map.put("dpt", value);
                map.put("dptLevelcode", value);
            }
            if (map.get("project") == null) {
                map.put("project", value);
                map.put("projectLevelcode", value);
            }
            if (map.get("usersource") == null) {
                map.put("usersource", value);
                map.put("usersourceLevelcode", value);
            }
        } else {
            if (map.get(userreType) == null) {
                map.put(userreType, value);
                map.put(userreType + "Levelcode", value);
            }
        }
        return map;
    }

    /**
     * 批量获取用户的数据范围
     *
     */
    @CoreRule(protocol = "datascopes/batch")
    @MethodDesc(comment = "批量获取用户的数据范围", returns = @ReturnDesc())
    public Map getUserDataScopeBatch(
            @ParamDesc(comment = "用户id集合", validate = Validate.M_SAFE) List<String> userIds,
            @ParamDesc(comment = "数据范围类型", validate = Validate.M_SAFE,
                    memo = "枚举 部门:dpt 项目:project 客户来源:usersource 不传就是不查") List<String> userreTypes,
            @ParamDesc(comment = "品牌标识", validate = Validate.R_ID, unnull = true, memo = "不传，网关自动填写") String brandId,
            Map $params
    ) throws Exception {
        List<UcUserrelation> urs = ucUserrelationService.queryRelationsByUserId2(userIds, userreTypes, brandId);
        // (userId, UR)
        Map<String, List<UcUserrelation>> urDepot1ByUserId = urs.stream().collect(Collectors.groupingBy(UcUserrelation::getUserId));
        // (userId, (userreType, UR))
        Map<String, Map<String, List<UcUserrelation>>> urDepot2ByType = urDepot1ByUserId.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                en -> {
                    List<UcUserrelation> v = en.getValue();
                    return v.stream().collect(Collectors.groupingBy(UcUserrelation::getUserreType));
                }));
        List<Map<String, Object>> datas = new ArrayList<>();
//        urDepot2.forEach((k, v) -> {
//            Map<String, List<String>> _v = v.entrySet().stream().collect(Collectors.toMap(
//                    Map.Entry::getKey,
//                    en -> en.getValue().stream().map(UcUserrelation::getUserreRelationid).collect(Collectors.toList())
//            ));
//            Map<String, Object> dataNode = new LinkedHashMap<>();
//            dataNode.put("userId", k);
//            dataNode.putAll(_v);
//            datas.add(dataNode);
//        });
        for (String userId: userIds) {
            Map<String, Object> dataNode = new LinkedHashMap<>();
            dataNode.put("userId", userId);
            Map<String, List<UcUserrelation>> urByType = urDepot2ByType.get(userId);
            for (String userreType: userreTypes) {
                if (MapUtils.isEmpty(urByType)){
                    dataNode.put(userreType, new ArrayList<>());
                } else {
                    List<UcUserrelation> _urs = urByType.get(userreType);
                    if (CollectionUtils.isEmpty(_urs)){
                        dataNode.put(userreType, new ArrayList<>());
                    } else {
                        dataNode.put(userreType, _urs.stream().map(UcUserrelation::getUserreRelationid).collect(Collectors.toList()));
                    }
                }
            }
            datas.add(dataNode);
        }
        Map<String, List> r = new HashMap<>();
        r.put("data", datas);
        return r;
    }



    /**
     * 设置用户关系
     *
     * @return
     * @throws Exception
     */
    @SuppressWarnings("Duplicates")
    @CoreRule(protocol = "datascopes/set", auth = "uc/auth/user/datascopes/set")
    @MethodDesc(comment = "设置用户数据范围", returns = @ReturnDesc(subs = {
    }))
    public void setUserDataScope(
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 36, unnull = true) String userId,
            @ParamDesc(comment = "部门id集合", validate = Validate.M_SAFE, memo = "三种集合至少传入一个,ALL表示全部部门") List dptIds,
            @ParamDesc(comment = "部门levelcode集合", validate = Validate.M_SAFE, memo = "与id的list数量必须一致,ALL表示全部部门") List dptLevelcodes,
            @ParamDesc(comment = "项目id集合", validate = Validate.M_SAFE) List projectIds,
            @ParamDesc(comment = "项目levelcode集合", validate = Validate.M_SAFE) List projectLevelcodes,
            @ParamDesc(comment = "用户来源code集合", validate = Validate.M_SAFE, memo = "即字典中的code,ALL表示全部") List usersourceCodes,
            @ParamDesc(comment = "用户来源levelcode集合", validate = Validate.M_SAFE, memo = "字典中的dictLevelCode,ALL表示全部") List usersourceLevelcodes,
            @ParamDesc(comment = "更新的部分", validate = Validate.M_SAFE, defVal = "", memo = "更新的部分：dpt,project,usersource  多个用逗号分隔，不传则更新全部") String part,
            @ParamDesc(comment = "品牌标识", validate = Validate.R_ID, defVal = "YOULU", memo = "不传，网关自动填写") String brandId,
            Map $params
    ) throws Exception {

//        if ((dptIds == null || dptIds.size() == 0)
//                && (projectIds == null || projectIds.size() == 0)
//                && (usersourceCodes == null || usersourceCodes.size() == 0)) {
//            throw new ApplicationException(ErrorCode.argument_invalide, "至少传入一种用户范围", null, null);
//        }
        UcUser ucUser = ucUserService.queryById(userId);
        if (ucUser == null) {
            throw new ApplicationException(ErrorCode.argument_invalide, "用户不存在", null, null);
        }
        if (!compareSize(dptIds, dptLevelcodes)) {
            throw new ApplicationException(ErrorCode.argument_invalide, "部门levelcode与id不匹配", null, null);
        }
        if (!compareSize(projectIds, projectLevelcodes)) {
            throw new ApplicationException(ErrorCode.argument_invalide, "项目levelcode与id不匹配", null, null);
        }
        if (!compareSize(usersourceCodes, usersourceLevelcodes)) {
            throw new ApplicationException(ErrorCode.argument_invalide, "用户来源levelcode与id不匹配", null, null);
        }
        ucUserrelationService.saveRelations(userId, part, brandId, $params);
    }

    /**
     * 批量设置用户关系
     *
     */
    @SuppressWarnings({"Duplicates"})
    @CoreRule(protocol = "datascopes/batch/set", auth = "uc/auth/user/datascopes/batch/set")
    @MethodDesc(comment = "批量设置用户数据范围", returns = @ReturnDesc(subs = {
    }))
    public void batchSetUserDataScope(
            @ParamDesc(comment = "用户id", validate = Validate.M_SAFE, length = 36, unnull = true) List<String> userIds,
            @ParamDesc(comment = "部门id集合", validate = Validate.M_SAFE, memo = "三种集合至少传入一个,ALL表示全部部门") List dptIds,
            @ParamDesc(comment = "部门levelcode集合", validate = Validate.M_SAFE, memo = "与id的list数量必须一致,ALL表示全部部门") List dptLevelcodes,
            @ParamDesc(comment = "项目id集合", validate = Validate.M_SAFE) List projectIds,
            @ParamDesc(comment = "项目levelcode集合", validate = Validate.M_SAFE) List projectLevelcodes,
            @ParamDesc(comment = "用户来源code集合", validate = Validate.M_SAFE, memo = "即字典中的code,ALL表示全部") List usersourceCodes,
            @ParamDesc(comment = "用户来源levelcode集合", validate = Validate.M_SAFE, memo = "字典中的dictLevelCode,ALL表示全部") List usersourceLevelcodes,
            @ParamDesc(comment = "更新的部分", validate = Validate.M_SAFE, defVal = "", memo = "更新的部分：dpt,project,usersource  多个用逗号分隔，不传则更新全部") String part,
            @ParamDesc(comment = "品牌标识", validate = Validate.R_ID, defVal = "YOULU", memo = "不传，网关自动填写") String brandId,
            Map $params
    ) throws Exception {

        if (!compareSize(dptIds, dptLevelcodes)) {
            throw new ApplicationException(ErrorCode.argument_invalide, "部门levelcode与id不匹配", null, null);
        }
        if (!compareSize(projectIds, projectLevelcodes)) {
            throw new ApplicationException(ErrorCode.argument_invalide, "项目levelcode与id不匹配", null, null);
        }
        if (!compareSize(usersourceCodes, usersourceLevelcodes)) {
            throw new ApplicationException(ErrorCode.argument_invalide, "用户来源levelcode与id不匹配", null, null);
        }

        try {
            ucUserrelationService.saveRelationsBatch(userIds, part, brandId, $params);
        } catch (Throwable t) {
            LOGGER.error("执行异常", t);
            throw t;
        }


    }


    private boolean compareSize(List dptIds, List dptLevelCodes) {
        int size1 = dptIds == null ? 0 : dptIds.size();
        int size2 = dptLevelCodes == null ? 0 : dptLevelCodes.size();
        return size1 == size2;
    }

    /**
     * 根据用户获取他可以授权的角色列表
     *
     * @return
     * @throws Exception
     */
    @CoreRule(protocol = "roles/managed")
    @MethodDesc(comment = "获取用户可授权角色列表", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "data", comment = "数据", type = List.class),
            @Return2Desc(name = "data[n].roleId", comment = "角色标识", type = String.class),
            @Return2Desc(name = "data[n].roleName", comment = "角色名称", type = String.class),
            @Return2Desc(name = "data[n].roleMemo", comment = "角色描述", type = String.class),
    }))
    public Map getManagedRoles(
            @ParamDesc(comment = "当前用户id", validate = Validate.R_ID, length = 36, memo = "网关自动填写，当前的登录的用户id") String currUserId,
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 36, memo = "如果不传，则为当前用户的id") String userId,
            @ParamDesc(comment = "品牌id", validate = Validate.R_ID, length = 36, memo = "网关填写，当前的品牌id", unnull = true) String brandId,
            @ParamDesc(comment = "角色名称", validate = Validate.M_SAFE, length = 100, memo = "名称模糊搜索") String roleName,
            Map $params
    ) throws Exception {
        if (isEmpty(userId)) {
            userId = currUserId;
        }
        List<UcRole> roleList;
        if (ucUserroleService.isSuperAdmin(userId)) {
            // 此接口用于授权时调用，授权时只查本品牌的
            roleList = ucRoleService.queryRoleListByName(roleName, brandId);
        } else {
            roleList = ucRoleService.queryRoleListByUser(userId, brandId, roleName);
        }
        List<Map> list = new ArrayList<>();
        for (UcRole role : roleList) {
            Map map = new HashMap();
            map.put("roleId", role.getRoleId());
            map.put("roleName", role.getRoleName());
            map.put("roleMemo", role.getRoleMemo());
            list.add(map);
        }

        return MapUtils.toMap(new Object[][]{
                {"data", list}
        });
    }

    /**
     * 根据用户获取他可以授权的角色列表
     *
     * @return
     * @throws Exception
     */
    @CoreRule(protocol = "roles")
    @MethodDesc(comment = "获取用户当前的角色", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "data", comment = "数据", type = List.class),
            @Return2Desc(name = "data[n].roleId", comment = "角色标识", type = String.class),
            @Return2Desc(name = "data[n].roleName", comment = "角色名称", type = String.class),
            @Return2Desc(name = "data[n].roleType", comment = "角色类型", type = String.class),
            @Return2Desc(name = "data[n].roleAdminstatus", comment = "角色类型", type = String.class),
    }))
    public Map getUserRoles(
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 36, memo = "当前用户id") String userId,
            @ParamDesc(comment = "品牌id", validate = Validate.M_SAFE, length = 36, memo = "品牌id", unnull = true) String brandId,
            Map $params
    ) throws Exception {

        List<Map> list = new ArrayList<>();
        List<UcRole> roleList = ucUserroleService.findUserRoles(userId, brandId);
        for (UcRole role : roleList) {
            Map map = new HashMap();
            map.put("roleId", role.getRoleId());
            map.put("roleName", role.getRoleName());
            map.put("roleType", role.getRoleType() == null ? "" : role.getRoleType());
            map.put("roleAdminstatus", role.getRoleAdminstatus());
            list.add(map);
        }

        return MapUtils.toMap(new Object[][]{
                {"data", list}
        });
    }

    /**
     * 根据用户获取品牌列表
     *
     * @return
     * @throws Exception
     */
    @CoreRule(protocol = "brands")
    @MethodDesc(comment = "获取用户的品牌列表", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "data", comment = "数据", type = List.class),
            @Return2Desc(name = "data[n].brandId", comment = "角色标识", type = String.class),
            @Return2Desc(name = "data[n].brandName", comment = "角色名称", type = String.class),
            @Return2Desc(name = "data[n].loginFlag", comment = "是否当前登录的  Y  N", type = String.class),
    }))
    public Map getBrandsByUser(
            @ParamDesc(comment = "当前用户id", validate = Validate.R_ID, length = 36, memo = "当前的登录的用户id") String currUserId,
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 36, memo = "如果不传，则为当前用户的id") String userId,
            @ParamDesc(comment = "当前登录的brandId", validate = Validate.R_ID, length = 36, memo = "网关传入") String brandId,
            Map $params
    ) throws Exception {
        if (isEmpty(userId)) {
            userId = currUserId;
        }
        List<Map> list = ucUserbrandService.queryBrandsByUser(userId);
        for (Map map : list) {
            if (map.get("brandId") != null && map.get("brandId").equals(brandId)) {
                map.put("loginFlag", "Y");
            } else {
                map.put("loginFlag", "N");
            }
        }
        return MapUtils.toMap(new Object[][]{
                {"data", list}
        });
    }

    /**
     * 给用户设置品牌
     *
     * @return
     * @throws Exception
     */
    @CoreRule(protocol = "brands/set", needLogin = false)
    @MethodDesc(comment = "给用户设置品牌", returns = @ReturnDesc(subs = {}))
    public void setUserBrand(
            @ParamDesc(comment = "当前用户id", validate = Validate.R_ID, length = 36, memo = "网关传入", unnull = true) String currUserId,
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 36, unnull = true) String userId,
            @ParamDesc(comment = "品牌id集合", validate = Validate.M_SAFE, memo = "[\"id1\",\"id2\"]") List<String> brandIds,
            Map $params
    ) throws Exception {
        ucUserbrandService.setUserBrand(currUserId, userId, brandIds);
        return;
    }


    /**
     * 给用户设置角色
     * todo 测试此方法的性能
     *
     * @return
     * @throws Exception
     */
    @CoreRule(protocol = "roles/set", needLogin = false)
    @MethodDesc(comment = "给用户设置角色", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "data", comment = "角色id集合", type = List.class),
    }))
    public Map setUserRole(
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 36, unnull = true) String userId,
            @ParamDesc(comment = "当前用户id", validate = Validate.R_ID, length = 36, unnull = true, memo = "网关填写") String currUserId,
            @ParamDesc(comment = "角色id集合", validate = Validate.M_SAFE, memo = "[\"id1\",\"id2\"]") List<String> roleIds,
            @ParamDesc(comment = "品牌id", validate = Validate.M_SAFE, unnull = true, memo = "网关填写，当前品牌id") String brandId,
            Map $params
    ) throws Exception {

        String[] strings = this.ucUserroleService.serUserRoles(currUserId, userId, roleIds, brandId);
        return MapUtils.toMap(new Object[][]{
                {"data", strings}
        });
    }

    /**
     * 菜单树，登录时获取，比较重要
     */
    @CoreRule(protocol = "menu/tree")
    @MethodDesc(comment = "根据用户获取菜单树", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "data", comment = "数据集合", type = String.class),
            @Return2Desc(name = "data[n].menuId", comment = "菜单标识", type = String.class),
            @Return2Desc(name = "data[n].menuName", comment = "菜单名称", type = String.class),
            @Return2Desc(name = "data[n].menuType", comment = "菜单类型", type = String.class, memo = "枚举 工作台:desktop 应用:app 模块:model 页面:page 功能组合:funcGroup 功能:func"),
            @Return2Desc(name = "data[n].menuUrl", comment = "菜单地址", type = String.class),
            @Return2Desc(name = "data[n].menuCode", comment = "菜单编码", type = String.class, memo = "唯一值，若为功能，需与protocol一致"),
            @Return2Desc(name = "data[n].menuParam", comment = "菜单参数", type = String.class),
            @Return2Desc(name = "data[n].menuSeq", comment = "菜单顺序", type = Integer.class, memo = "越小越靠前"),
            @Return2Desc(name = "data[n].menuDisablestatus", comment = "菜单禁用状态", type = String.class, memo = "枚举：E启用，D禁用"),
            @Return2Desc(name = "data[n].menuIcon", comment = "菜单图标", type = String.class),
            @Return2Desc(name = "data[n].menuDatapolicy", comment = "功能数据权限", type = List.class),
            @Return2Desc(name = "data[n].children", comment = "子集合", type = List.class),
    }))
    public Map getMenuTreeByUser(
            @ParamDesc(comment = "当前用户id", validate = Validate.R_ID, length = 36, memo = "当前的登录的用户id") String currUserId,
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 36, memo = "如果不传，则为当前登录者的id") String userId,
            @ParamDesc(comment = "是否包含功能", validate = "R:[YN]", defVal = "Y", memo = "枚举:YN,默认N") String containsFunc,
            @ParamDesc(comment = "是否包含禁用的", validate = "R:[YN]", defVal = "N", memo = "枚举:YN,默认N") String containsDisabled,
            @ParamDesc(comment = "是否包含功能组合", validate = "R:[YN]", defVal = "N", memo = "枚举:YN,默认N") String containsFuncgroup,
            @ParamDesc(comment = "品牌标识", validate = Validate.R_ID, memo = "不传，网关自动填写", unnull = true) String brandId,
            @ParamDesc(comment = "系统标识", validate = Validate.M_SAFE, memo = "默认智能运营平台", defVal = "ADMIN_WEB") String sysId,
            Map $params
    ) throws Exception {
        if (isEmpty(userId)) {
            userId = currUserId;
        }
        boolean containsF = "Y".equalsIgnoreCase(containsFunc);
        boolean containsD = "Y".equalsIgnoreCase(containsDisabled);
        boolean containsFG = "Y".equalsIgnoreCase(containsFuncgroup);
        if (ucUserroleService.isSuperAdmin(userId)) {
            List<Map> menuList = ucMenuService.queryMenuTree(containsF, containsD, null, sysId, containsFG);
            return MapUtils.toMap(new Object[][]{{"data", menuList}});
        } else {
            List<Map> menuList = ucMenuService.queryMenuTreeByUser(userId, brandId, containsF, containsD, sysId, containsFG);
            return MapUtils.toMap(new Object[][]{{"data", menuList}});
        }
    }

    /**
     * 判断用户是否有菜单权限
     *
     * @return
     * @throws Exception
     */
    @CoreRule(protocol = "checkcode")
    @MethodDesc(comment = "判断用户是否有菜单权限", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "isGranted", comment = "是否有菜单权限", type = Boolean.class),
    }))
    public Map getMenucodeByUser(
            @ParamDesc(comment = "当前用户id", validate = Validate.R_ID, length = 36, memo = "当前的登录的用户id") String currUserId,
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 36, memo = "如果不传，则为当前登录的用户id") String userId,
            @ParamDesc(comment = "品牌id", validate = Validate.M_SAFE) String brandId,
            @ParamDesc(comment = "菜单编码", validate = Validate.M_SAFE) String menuCode,
            Map $params
    ) throws Exception {
        if (isEmpty(userId)) {
            userId = currUserId;
        }
        boolean isGranted = true;
        if (ucUserroleService.isSuperAdmin(userId)) {
            return MapUtils.toMap(new Object[][]{
                    {"isGranted", isGranted}
            });
        }
        //用户角色
        String[] strings = ucUserroleService.queryRoleIdsByUserId(userId, brandId);
        if (strings.length == 0) {
            isGranted = false;
        } else {
            isGranted = ucRolemenuService.queryCodeExists(menuCode, SetUtils.toSet(strings));
        }
        return MapUtils.toMap(new Object[][]{
                {"isGranted", isGranted}
        });
    }


    /**
     * 查询用户的菜单权限
     *
     * @return
     * @throws Exception
     */
    @CoreRule(protocol = "menucode/datapolicy")
    @MethodDesc(comment = "根据菜单code值获取对应的数据策略", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "data", comment = "数据策略集合", type = List.class),

    }, memo = "{menuId1:[policy1,policy2],menuId2:[policy1,policy2]}"))
    public Map getMenucodesByUser(
            @ParamDesc(comment = "当前用户id", validate = Validate.R_ID, length = 36, memo = "当前的登录的用户id") String currUserId,
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 36, memo = "如果不传，则为当前登录的用户id") String userId,
            @ParamDesc(comment = "菜单code", validate = Validate.M_SAFE, unnull = true, memo = "菜单code") String menuCode,
            @ParamDesc(comment = "品牌id", validate = Validate.M_SAFE) String brandId,
            Map $params
    ) throws Exception {
        if (isEmpty(userId)) {
            userId = currUserId;
        }
        if (ucUserroleService.isSuperAdmin(userId)) {
            UcMenu byCode = ucMenuService.findByCode(menuCode);
            if (byCode != null) {
                String menuDatapolicy = byCode.getMenuDatapolicy();
                if (menuDatapolicy != null && !menuDatapolicy.equals("")) {
                    String[] split = menuDatapolicy.split(",");
                    return MapUtils.toMap(new Object[][]{
                            {"data", split}
                    });
                }
            }
            return MapUtils.toMap(new Object[][]{
                    {"data", new ArrayList<>()}
            });
        }
        List policys = ucUserroleService.getDatapolicysByCode(userId, brandId, menuCode);
        return MapUtils.toMap(new Object[][]{
                {"data", policys}
        });
    }

    /**
     * 获取用户的手机号查询次数
     *
     * @return
     * @throws Exception
     */
    @CoreRule(protocol = "mobilecount")
    @MethodDesc(comment = "获取用户的手机号查询次数", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "count", comment = "次数", type = Integer.class),
    }))
    public Map findMobileCountByUserId (
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 55, unnull = true) String userId
    ) throws Exception {
        List<UcRole> list = ucUserroleService.findAllUserRoles(userId);
        int count = 0;
        if (list != null && list.size() > 0) {
            for (UcRole ucRole : list) {
                Integer roleMobilecount = ucRole.getRoleMobilecount();
                if (roleMobilecount != null && roleMobilecount > count) {
                    count = roleMobilecount;
                }
            }
        }
        return MapUtils.toMap(new Object[][]{
                {"count", count}
        });
    }

    /**
     * 获取用户的身份证号查询次数
     *
     * @return Map
     * @throws Exception
     */
    @CoreRule(protocol = "idcardcount")
    @MethodDesc(comment = "获取用户的身份证号查询次数", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "count", comment = "次数", type = Integer.class),
    }))
    public Map findIdCardCountByUserId(
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 55, unnull = true) String userId
    ) throws Exception {
        List<UcRole> list = ucUserroleService.findAllUserRoles(userId);
        int count = 0;
        if (list != null && list.size() > 0) {
            for (UcRole ucRole : list) {
                Integer roleIdcardcount = ucRole.getRoleIdcardcount();
                if (roleIdcardcount != null && roleIdcardcount > count) {
                    count = roleIdcardcount;
                }
            }
        }
        return MapUtils.toMap(new Object[][]{
                {"count", count}
        });
    }

    /**
     * 获取用户的微信号查询次数
     *
     * @return Map
     * @throws Exception
     */
    @CoreRule(protocol = "weixincount")
    @MethodDesc(comment = "获取用户的微信号查询次数", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "count", comment = "次数", type = Integer.class),
    }))
    public Map findWeixinCountByUserId (
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 55, unnull = true) String userId
    ) throws Exception {
        List<UcRole> list = ucUserroleService.findAllUserRoles(userId);
        int count = 0;
        if (list != null && list.size() > 0) {
            for (UcRole ucRole : list) {
                Integer roleWeixincount = ucRole.getRoleWeixincount();
                if (roleWeixincount != null && roleWeixincount > count) {
                    count = roleWeixincount;
                }
            }
        }
        return MapUtils.toMap(new Object[][]{
                {"count", count}
        });
    }


    /**
     * 获取用户的代理商查询次数
     */
    @CoreRule(protocol = "agentcount")
    @MethodDesc(comment = "获取用户的代理商查询次数", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "count", comment = "次数", type = Integer.class),
    }))
    public Map findAgentCountByUserId(
            @ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 55, unnull = true) String userId
    ) throws Exception {
        List<UcRole> list = ucUserroleService.findAllUserRoles(userId);
        int count = 0;
        if (list != null && list.size() > 0) {
            for (UcRole ucRole : list) {
                Integer roleAgentcount = ucRole.getRoleAgentcount();
                if (roleAgentcount != null && roleAgentcount > count) {
                    count = roleAgentcount;
                }
            }
        }
        return MapUtils.toMap(new Object[][]{
                {"count", count}
        });
    }

}