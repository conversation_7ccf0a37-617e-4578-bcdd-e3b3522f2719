package com.niceloo.uc.handler;

import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.log.CoreLogService;
import com.niceloo.core.mvc.MVCUtils;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.uc.common.UcConfig.Uk;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.common.UcConst.Avlstatus;
import com.niceloo.uc.model.UcUser;
import com.niceloo.uc.model.log.UcAdminloginlog;
import com.niceloo.uc.model.log.UcLoginlog;
import com.niceloo.uc.model.log.UcUserloginlog;
import com.niceloo.uc.service.*;
import com.niceloo.uc.utils.AESUtils;
import com.niceloo.uc.utils.FireUkUtils;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.*;
import org.nobject.common.encrypt.MD5Utils;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.js.JSONUtils;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.ObjectUtils;
import org.nobject.common.lang.StringUtils;
import org.nobject.common.log.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.nobject.common.lang.StringUtils.isEmpty;

/**
 * UcLoginHandler
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@CoreRule(protocol = "uc/login")
public class UcLoginHandler {

	public static Logger logger = Logger.getLogger(UcLoginHandler.class);
	/** ucUserService */
	@Autowired
	private UcUserService ucUserService;

	@Autowired
	private UcUserbrandService ucUserbrandService;

	@Autowired
	private CoreLogService coreLogService;

	@Autowired
	private UcUserroleService ucUserroleService;

	@Autowired
	private UcLoginipService ucLoginipService;

	@Autowired
	private UcEeService ucEeService;

	@Autowired
	private UcIPGuardService ucIPGuardService;


	/**
	 * 登录检测
	 */
	@CoreRule(protocol = "check", needLogin = false)
	@MethodDesc(comment = "登录检测", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "userId", comment = "用户标识", type = String.class),
			@Return2Desc(name = "userName", comment = "用户姓名", type = String.class),
			@Return2Desc(name = "userFlag", comment = "用户类型", type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户"),
			@Return2Desc(name = "userLoginname", comment = "用户登录帐号", type = String.class),
			@Return2Desc(name = "userLoginpwdstatus", comment = "用户登录密码状态", type = String.class, memo = "[枚举]Y:正常;I:初始化"),
			@Return2Desc(name = "userLockstatus", comment = "用户锁定状态", type = String.class, memo = "[枚举]Y:锁定;N:正常"),
			@Return2Desc(name = "userLastloginip", comment = "用户上次登录IP", type = String.class),
			@Return2Desc(name = "userLastlogindate", comment = "用户上次登录时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
			@Return2Desc(name = "userCreatedate", comment = "用户创建时间", type = String.class),
			@Return2Desc(name = "userCreatesrc", comment = "用户创建来源", type = String.class),
			@Return2Desc(name = "userCreatetype", comment = "用户创建类型", type = String.class),
			@Return2Desc(name = "userMobile", comment = "用户手机", type = String.class),
			@Return2Desc(name = "userMobilestatus", comment = "用户手机状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证;"),
			@Return2Desc(name = "userEmail", comment = "用户邮箱", type = String.class),
			@Return2Desc(name = "userEmailstatus", comment = "用户邮箱状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证;"),
			@Return2Desc(name = "userIdcard", comment = "用户身份证号", type = String.class),
			@Return2Desc(name = "userGender", comment = "用户性别", type = String.class, memo = "[枚举]M:男性;F:女性"),
			@Return2Desc(name = "userProstatus", comment = "用户协议接受状态", type = String.class, memo = "[枚举]Y:已接受;N:未接受;"), }))
	public Map check(@ParamDesc(comment = "用户登录帐号", validate = Validate.R_LOGINNAME) String userLoginname,
			@ParamDesc(comment = "用户登录密码"	, validate = Validate.M_SAFE) String userLoginpwd,
			@ParamDesc(comment = "用户类型"		, validate = "R:[SAIC]", memo = "[枚举]S:学生;A:代理商;I:内部用户;C:企业用户", unnull = true) String userFlag,
			@ParamDesc(comment = "用户手机"		, validate = Validate.R_PHONE) String userMobile,
			@ParamDesc(comment = "用户邮箱"		, validate = Validate.R_EMAIL) String userEmail,
			@ParamDesc(comment = "企业微信绑定的userId"		, validate = Validate.R_ID) String userId_workwx,
			@ParamDesc(comment = "IP"			, validate = "R:[0-9\\.]*") String ip,
			@ParamDesc(comment = "是否校验IP"	, validate = "R:[YN]",defVal = "N") String checkIp,
			@ParamDesc(comment = "扩展信息"	, validate = Validate.M_SAFE) Map ext,
			@ParamDesc(comment = "品牌标识"	, validate = Validate.M_SAFE,defVal = "YOULU") String brandId,
			@ParamDesc(comment = "客户端类型"	, validate = Validate.M_SAFE) String ctype,
			Map $params) throws Exception {
		UcLoginlog loginLog;
		if("I".equalsIgnoreCase(userFlag)){
			loginLog = new UcAdminloginlog();
		}else{
			loginLog = new UcUserloginlog();
		}

		// 获取用户
		UcUser user = getUcUser(userFlag, userLoginname, userMobile, userEmail, userId_workwx, loginLog,brandId);

		this.buildUserLogInfo(loginLog,user);

		user.setUserLastloginip(ip);

		// 密码->禁用->锁定
		this.checkPwdAndStatus(userLoginpwd, user, loginLog);

		UcUser userLoginMsg4update = buildLastLoginMsg(ip, loginLog, user);

		// 内部员工校验ip
		if("I".equalsIgnoreCase(userFlag) && "Y".equals(checkIp)){
			this.checkIp(user,loginLog);
		}

		// 检查在职状态
		ucEeService.checkWorkStatus(user, loginLog);

		//构建登录ip信息，品牌信息等
		userLoginMsg4update.setUserLastlogindate(DateUtils.getNowDString());
		setUserApushId(userLoginMsg4update,ext,ctype);

		ucUserService.edit(userLoginMsg4update);

		loginLog.setLogLoginstatus(UcLoginlog.LOGINSTATUS_SUCCESS);
		this.recordLoginLog(loginLog);

		Map rtn = BeanUtils.bean2Map(user);
		rtn.remove("userLoginpwd");

		String rtn0 = new String();
		if (user.getUserFlag().equals("A")) {
			Map map = new HashMap();
			map.put("userId", user.getUserId());
			rtn0 = (String) CoreUtils.request(MVCUtils.requireProperty("sl.url"), "api/sl/agent/protocal/status", map,
					"UC", "SL");
		}
		rtn.put("userProstatus", rtn0);
		return rtn;
	}

	/**
	 * 设置推送标识
	 * @param ucUser
	 * @param ext
	 * @param ctype
	 */
	public void setUserApushId(UcUser ucUser, Map ext,String ctype) {
		if(!ObjectUtils.isEmpty(ext)) {
			String apushId = (String) ext.get("apushId");
			if(!StringUtils.isEmpty(apushId) && !StringUtils.isEmpty(ctype)) {
				String userApushid = ucUser.getUserApushid();
				Map apush = new HashMap<>();
				if(!StringUtils.isEmpty(userApushid)) {
					try {
						apush = JSONUtils.toMap(userApushid);
					} catch (Exception e) {
						
					}
				}
				apush.put(ctype, apushId);
				ucUser.setUserApushid(JSONUtils.toString(apush));
			}
		}
	}

	/**
	 * 检查用户ip是否在ip范围内,
	 */
	private void checkIp(UcUser user, UcLoginlog loginLog) throws Exception {
		String userIp = user.getUserLastloginip();
		if (ucUserroleService.checkUserRoleIpstatus(user.getUserId())) {
			ucLoginipService.addTempIp(userIp, user);
		} else {
			boolean b = ucLoginipService.checkIp(userIp);
			if (!b) {
				String errMsg = "IP校验未授权不允许登陆.IP=" + userIp;
				loginLog.setLogLoginerrormsg(errMsg);
				loginLog.setLogLoginstatus(UcLoginlog.LOGINSTATUS_ERROR);
				this.recordLoginLog(loginLog);
				throw new ApplicationException(UcConst.Errorcode.IP_ERROR, errMsg, null);
			}
		}
	}

	/**
	 * 记录用户最后一次登录的信息
	 * @param ip
	 * @param loginLog
	 * @param user
	 * @throws Exception
	 */
	private UcUser buildLastLoginMsg(String ip, UcLoginlog loginLog, UcUser user) throws Exception {
		UcUser user4update = ucUserService.getUserInstance();
		user4update.setUserId(user.getUserId());
		user.setUserLastloginip(ip);
		user4update.setUserLastloginip(ip);
		loginLog.setLogLoginip(ip);
		user4update.setUserLastlogindate(DateUtils.getNowDString());
		String brandId = user.getBrandId();
		// 内部员工多品牌
		if("SYSTEM".equals(brandId)){
			Set<String> set = ucUserbrandService.queryBrandIdsByUser(user.getUserId());
			if(set == null || set.size() == 0){
				user.setUserLastloginbrand("YOULU");
				user4update.setUserLastloginbrand("YOULU");
			}
			if(StringUtils.isEmpty(user.getUserLastloginbrand()) ||!set.contains(user.getUserLastloginbrand()) ){
				for (String s : set) {
					user.setUserLastloginbrand(s);
					user4update.setUserLastloginbrand(s);
					break;
				}
			}
		}else{
			user.setUserLastloginbrand(user.getBrandId());
		}

		return user4update;
	}

	/**
	 * 检查用户密码和状态
	 * @param userLoginpwd
	 * @param loginLog
	 * @param user
	 * @throws Exception
	 */
	private void checkPwdAndStatus( String userLoginpwd, UcUser user,UcLoginlog loginLog) throws Exception {
		// 密码验证
		if (!isEmpty(userLoginpwd)) {
			//
			if (isEmpty(user.getUserLoginpwd())) {
				loginLog.setLogLoginerrormsg("密码不正确");
				this.recordLoginLog(loginLog);
				throw new ApplicationException(UcConst.Errorcode.USER_LOGIN_ERROR,"用户名或密码不正确",MapUtils.toMap(new Object[][] {
					{"errType","P"}//P 账号密码错误
				}));
			}

			if (!ucUserService.matchPwd(userLoginpwd, user.getUserLoginpwd(), user.getUserLoginpwdtype())) {
				loginLog.setLogLoginerrormsg("密码不正确");
				this.recordLoginLog(loginLog);
				throw new ApplicationException(UcConst.Errorcode.USER_LOGIN_ERROR,"用户名或密码不正确",MapUtils.toMap(new Object[][] {
					{"errType","P"}//P 账号密码错误
				}));
			}
		}

		//
		if (Avlstatus.NO.equals(user.getUserAvlstatus())) {
			loginLog.setLogLoginerrormsg("账号已禁用");
			this.recordLoginLog(loginLog);
			throw new ApplicationException(UcConst.Errorcode.USER_LOGIN_ERROR,"该账号已禁用，请联系管理员",MapUtils.toMap(new Object[][] {
				{"errType","A"}//AVL forbidden   账号禁用
			}));
		}

		// 锁定状态
		if ("Y".equals(user.getUserLockstatus())) {
			loginLog.setLogLoginerrormsg("账户已锁定");
			this.recordLoginLog(loginLog);
			throw new ApplicationException(UcConst.Errorcode.USER_LOGIN_ERROR,"账户已锁定",MapUtils.toMap(new Object[][] {
				{"errType","L"}//L lock   账号锁定
			}));
		}
	}

	/**
	 * 获取用户
	 * @param userLoginname 登录名
	 * @param userFlag
	 * @param userMobile
	 * @param userEmail
	 * @param userId_workwx
	 * @param loginLog
	 * @param brandId 
	 * @return
	 * @throws Exception
	 */
	private UcUser getUcUser(String userFlag,  String userLoginname,  String userMobile,  String userEmail, String userId_workwx, UcLoginlog loginLog, String brandId) throws Exception {
		UcUser user;
		if (!isEmpty(userLoginname)) {
			user = ucUserService.queryByLoginname$Flag$Avlstatus(userLoginname, userFlag, null,brandId);
			loginLog.setLogLogintype(UcLoginlog.LOGINTYPE_ACCOUNT_PASSWORD);
			if (user == null) {
				throw new ApplicationException(UcConst.Errorcode.USER_LOGIN_ERROR,"用户名或密码不正确",MapUtils.toMap(new Object[][] {
					{"errType","P"}//P 账号
				}));
			}
		} else if (!isEmpty(userMobile)) {
			user = ucUserService.queryByMobile$Flag$Avlstatus(userMobile, userFlag, null,brandId);
			loginLog.setLogLogintype(UcLoginlog.LOGINTYPE_PHONE_PASSWORD);
			if (user == null) {
				throw new ApplicationException(UcConst.Errorcode.USER_LOGIN_ERROR,"用户名或密码不正确",MapUtils.toMap(new Object[][] {
					{"errType","M"}//M 手机号错误
				}));
			}
		} else if (!isEmpty(userEmail)) {
			user = ucUserService.queryByEmail$Flag$Avlstatus(userEmail, userFlag, null,brandId);
			loginLog.setLogLogintype(UcLoginlog.LOGINTYPE_MAIL_PASSWORD);
			if (user == null) {
				throw new ApplicationException(UcConst.Errorcode.USER_LOGIN_ERROR,"用户名或密码不正确",MapUtils.toMap(new Object[][] {
					{"errType","E"}//E 邮箱错误
				}));
			}
		}else if(!isEmpty(userId_workwx) && "I".equalsIgnoreCase(userFlag)) {
			user = ucUserService.queryById(userId_workwx);
			loginLog.setLogLogintype(UcLoginlog.LOGINTYPE_WORKWX);
			if (user == null) {
				throw new ApplicationException(UcConst.Errorcode.USER_LOGIN_ERROR,"用户不存在",null);
			}
		}else {
			throw new ApplicationException(UcConst.Errorcode.USER_LOGIN_ERROR,"用户登录帐号、邮箱、手机不能同时为空",null);
		}
		return user;
	}

	/**
	 * 记录用户信息
	 * @param loginLog
	 * @param user
	 */
	private void buildUserLogInfo(UcLoginlog loginLog, UcUser user) {
		loginLog.setUserId(user.getUserId());
		loginLog.setUserLoginname(user.getUserLoginname());
		loginLog.setUserMobile(user.getUserMobile());
		loginLog.setUserName(user.getUserName());
	}

	/**
	 * 记录登录日志
	 * @param loginLog
	 */
	private void recordLoginLog(UcLoginlog loginLog) {
		this.coreLogService.send(loginLog);
	}

	/**
	 *
	 * @param userId
	 * @param brandId
	 * @param $params
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "switchBrand", needLogin = false)
	@MethodDesc(comment = "切换品牌", returns = @ReturnDesc(subs = {}))
	public Map switchBrand(
			@ParamDesc(comment = "用户id", validate = Validate.R_ID,memo = "当前的登录的用户id",unnull = true) 	String userId,
			@ParamDesc(comment = "品牌id", validate = Validate.R_ID,memo = "要切换的品牌",unnull = true) 	String brandId,
			Map $params) throws Exception {
		Set<String> set = ucUserbrandService.queryBrandIdsByUser(userId);
		if(set.contains(brandId)){
			UcUser _user = ucUserService.getUserInstance();
			_user.setUserId(userId);
			_user.setUserLastloginbrand(brandId);
			ucUserService.edit(_user);
		}else{
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"品牌id错误",null);
		}
		return MapUtils.toMap(new Object[][]{
				{"brandId",brandId}
		});
	}



	/**
	 * uk登录检测
	 */
	@CoreRule(protocol = "uk", needLogin = false)
	@MethodDesc(comment = "UK登录", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "userId", comment = "用户标识", type = String.class),
			@Return2Desc(name = "userName", comment = "用户姓名", type = String.class),
			@Return2Desc(name = "userFlag", comment = "用户类型", type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户"),
			@Return2Desc(name = "userLoginname", comment = "用户登录帐号", type = String.class),
			@Return2Desc(name = "userLoginpwdstatus", comment = "用户登录密码状态", type = String.class, memo = "[枚举]Y:正常;I:初始化"),
			@Return2Desc(name = "userLockstatus", comment = "用户锁定状态", type = String.class, memo = "[枚举]Y:锁定;N:正常"),
			@Return2Desc(name = "userLastloginip", comment = "用户上次登录IP", type = String.class),
			@Return2Desc(name = "userLastlogindate", comment = "用户上次登录时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
			@Return2Desc(name = "userCreatedate", comment = "用户创建时间", type = String.class),
			@Return2Desc(name = "userCreatesrc", comment = "用户创建来源", type = String.class),
			@Return2Desc(name = "userCreatetype", comment = "用户创建类型", type = String.class),
			@Return2Desc(name = "userMobile", comment = "用户手机", type = String.class),
			@Return2Desc(name = "userMobilestatus", comment = "用户手机状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证;"),
			@Return2Desc(name = "userEmail", comment = "用户邮箱", type = String.class),
			@Return2Desc(name = "userEmailstatus", comment = "用户邮箱状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证;"),
			@Return2Desc(name = "userIdcard", comment = "用户身份证号", type = String.class),
			@Return2Desc(name = "userGender", comment = "用户性别", type = String.class, memo = "[枚举]M:男性;F:女性"),
			@Return2Desc(name = "userProstatus", comment = "用户协议接受状态", type = String.class, memo = "[枚举]Y:已接受;N:未接受;"), }))
	public Map sourceCheck(
			@ParamDesc(comment = "用户类型", validate = "R:[SAI]", memo = "[枚举]S:学生;A:代理商;I:内部用户", unnull = true) 	String userFlag,
			@ParamDesc(comment = "UK", validate = Validate.M_SAFE, unnull = true) 										String uk,
			@ParamDesc(comment = "IP", validate = "R:[0-9\\.]*") 														String ip,
			@ParamDesc(comment = "请求端", validate = Validate.M_SAFE) 													String type,
			@ParamDesc(comment = "强制同步数据", validate = "R:[YN]") 														String compel,
			Map $params
			) throws Exception {
		// Q&C:用户
		String userSourceid=null;
		logger.debug("[UK][encryption]["+uk+"]");
		//解密的时候注意是Android ios 和网站端的key salt 和迭代次数
		if("A".equals(type)) {
			userSourceid = AESUtils.ukAesDecrypt(uk, Uk.android_key, Uk.android_salt, Uk.android_iteration);
		}else if("I".equals(type)) {
			userSourceid=AESUtils.ukAesDecrypt(uk, Uk.ios_key, Uk.ios_salt, Uk.ios_iteration);
		}else if("C".equals(type)){
			userSourceid=AESUtils.ukAesDecrypt(uk, Uk.client_key, Uk.client_salt, Uk.client_iteration);
			String[] split = userSourceid.split("&");
			userSourceid = split[0];
			//消防
		}else if("F".equals(type)) {
			String decryptString = FireUkUtils.decryptString(Uk.fire_key, uk);
			String[] split = decryptString.split("\\*");
			userSourceid =split[2];
		}else{
			userSourceid=webEncrypt(uk);
		}

		UcLoginlog loginlog = new UcUserloginlog();
		loginlog.setLogLogintype("UK");
		UcUser user = null;
		user = ucUserService.queryBySourceid$Flag$Avlstatus(userSourceid, userFlag, null);
		if (user == null) {
				//去网站平台拉取用户信息
			user=ucUserService.syncUserToNet(userSourceid);
			if(user== null) {
				throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT, "用户不存在!", null);
			}
		}
		this.buildUserLogInfo(loginlog,user);
		if (Avlstatus.NO.equals(user.getUserAvlstatus())) {
			loginlog.setLogLoginerrormsg("账号已禁用");
			this.recordLoginLog(loginlog);
			throw new ApplicationException(UcConst.Errorcode.USER_AVL_ERROR, "该账号已禁用，请联系管理员", null);
		}
		// 锁定状态
		if ("Y".equals(user.getUserLockstatus())) {
			loginlog.setLogLoginerrormsg("账户已锁定");
			this.recordLoginLog(loginlog);
			throw new ApplicationException(UcConst.Errorcode.USER_LOCK_ERROR, "账户已锁定", null);
		}
		logger.debug("[USER][ID]["+user.getUserId()+"]");
		UcUser _user = ucUserService.getUserInstance();
		_user.setUserId(user.getUserId());
		_user.setUserLastloginip(ip);
		_user.setUserLastlogindate(DateUtils.getNowDString());
		ucUserService.edit(_user);
		Map rtn = BeanUtils.bean2Map(user);
		rtn.remove("userLoginpwd");
		loginlog.setLogLoginstatus(UcLoginlog.LOGINSTATUS_SUCCESS);
		this.recordLoginLog(loginlog);
		return rtn;
	}
	
	/**
	 * cas登录检测
	 * @param userFlag
	 * @param encryptedData
	 * @param ip
	 * @param $params
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "cas", needLogin = false)
	@MethodDesc(comment = "CAS登录", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "userId", comment = "用户标识", type = String.class),
			@Return2Desc(name = "userName", comment = "用户姓名", type = String.class),
			@Return2Desc(name = "userFlag", comment = "用户类型", type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户"),
			@Return2Desc(name = "userLoginname", comment = "用户登录帐号", type = String.class),
			@Return2Desc(name = "userLoginpwdstatus", comment = "用户登录密码状态", type = String.class, memo = "[枚举]Y:正常;I:初始化"),
			@Return2Desc(name = "userLockstatus", comment = "用户锁定状态", type = String.class, memo = "[枚举]Y:锁定;N:正常"),
			@Return2Desc(name = "userLastloginip", comment = "用户上次登录IP", type = String.class),
			@Return2Desc(name = "userLastlogindate", comment = "用户上次登录时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
			@Return2Desc(name = "userCreatedate", comment = "用户创建时间", type = String.class),
			@Return2Desc(name = "userCreatesrc", comment = "用户创建来源", type = String.class),
			@Return2Desc(name = "userCreatetype", comment = "用户创建类型", type = String.class),
			@Return2Desc(name = "userMobile", comment = "用户手机", type = String.class),
			@Return2Desc(name = "userMobilestatus", comment = "用户手机状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证;"),
			@Return2Desc(name = "userEmail", comment = "用户邮箱", type = String.class),
			@Return2Desc(name = "userEmailstatus", comment = "用户邮箱状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证;"),
			@Return2Desc(name = "userIdcard", comment = "用户身份证号", type = String.class),
			@Return2Desc(name = "userGender", comment = "用户性别", type = String.class, memo = "[枚举]M:男性;F:女性"),
			@Return2Desc(name = "userProstatus", comment = "用户协议接受状态", type = String.class, memo = "[枚举]Y:已接受;N:未接受;"), }))
	public Map cas(
			@ParamDesc(comment = "用户类型", validate = "R:[SAI]", memo = "[枚举]S:学生;A:代理商;I:内部用户", unnull = true) 	String userFlag,
			@ParamDesc(comment = "加密数据", validate = Validate.M_SAFE, unnull = true) 									String encryptedData,
			@ParamDesc(comment = "IP", validate = "R:[0-9\\.]*") 														String ip,
			@ParamDesc(comment = "品牌标识"	, validate = Validate.M_SAFE,defVal = "YOULU") String brandId,
			Map $params
			) throws Exception {
		logger.debug("[encryptedData]["+encryptedData+"]");
		String key = MVCUtils.getProperty("cas.enc.key");
		if(StringUtils.isEmpty(key)) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"[cas.enc.key]未配置",null);
		}
		String salt = MVCUtils.getProperty("cas.enc.salt");
		if(StringUtils.isEmpty(salt)) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"[cas.enc.salt]未配置",null);
		}
		String iteration = MVCUtils.getProperty("cas.enc.iteration");
		if(StringUtils.isEmpty(iteration)) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"[cas.enc.iteration]未配置",null);
		}
		int itera = 0;
		try {
			itera = Integer.parseInt(iteration.trim());
		} catch (Exception e) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"[cas.enc.iteration]配置错误",null);
		}
		String expire_time = MVCUtils.getProperty("cas.expire.time", "86400");
		long expireTime = Long.parseLong(expire_time);
		String decrypt ="";
		try {
			//解密
			decrypt = AESUtils.ukAesDecrypt(encryptedData, key, salt, itera);
			//TODO 解密过后前32位是对方的用户标识  后10位是时间戳 
		} catch (Exception e) {
			throw new ApplicationException(UcConst.Errorcode.DECRYPT_ERROR, "解密失败", null);
		}
		//时间戳
		String timeStamp = decrypt.substring(decrypt.length()-10);
		long currentTimeMillis = System.currentTimeMillis();
		currentTimeMillis=currentTimeMillis/1000;
		long difference =currentTimeMillis-Long.parseLong(timeStamp);
		if(expireTime < difference) {
			throw new ApplicationException(UcConst.Errorcode.DECRYPT_ERROR,"该加密串已失效",null);
		}

		UcUser user = ucUserService.queryByLoginname$Flag$Avlstatus(MVCUtils.getProperty("cas.login.name"), userFlag, null,"");
		if (user == null) {
			throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT, "用户不存在", null);
		}
		if (Avlstatus.NO.equals(user.getUserAvlstatus())) {
			throw new ApplicationException(UcConst.Errorcode.USER_AVL_ERROR, "该账号已禁用，请联系管理员", null);
		}
		// 锁定状态
		if ("Y".equals(user.getUserLockstatus())) {
			throw new ApplicationException(UcConst.Errorcode.USER_LOCK_ERROR, "该账户已锁定，请联系管理员", null);
		}
		logger.debug("[USER][ID]["+user.getUserId()+"]");
		UcUser _user = ucUserService.getUserInstance();
		_user.setUserId(user.getUserId());
		_user.setUserLastloginip(ip);
		_user.setUserLastlogindate(DateUtils.getNowDString());
		ucUserService.edit(_user);

		Map rtn = BeanUtils.bean2Map(user);
		rtn.remove("userLoginpwd");
		return rtn;
	}
	
	/**
	 * 解密UK
	 * @param uk
	 * @return
	 * @throws Exception
	 */
	public static String webEncrypt(String uk) throws Exception {
		String ukAesDecrypt = AESUtils.ukAesDecrypt(uk, Uk.key, Uk.salt, Uk.iteration);
		logger.debug("[UK][decrypt]["+ukAesDecrypt+"]");
		String[] split = ukAesDecrypt.split("&");
		String userSourceid = split[0];
		Map hget = (Map) UcConst.redis.hget(Uk.rediskey, userSourceid);
		if(!getEncrypt("PCLastTime", hget, userSourceid).equals(split[1])&&!getEncrypt("WapLastTime", hget, userSourceid).equals(split[1])) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"uk已失效", null);
		}
		return userSourceid;
	}
	
	/**
	 * 加密UK过期时间
	 * @param type
	 * @param hget
	 * @param userSourceid
	 * @return
	 * @throws Exception
	 */
	public static String getEncrypt(String type,Map hget,String userSourceid) throws Exception {
		if(hget == null) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"uk未从网站redis获取到用户信息",null);
		}
		String oriMd5 = userSourceid
				+upperCaseOne(String.valueOf(hget.get("IsDisabled")))
				+upperCaseOne(String.valueOf(hget.get("IsDeleted")))
				+String.valueOf(hget.get(type))
				+String.valueOf(hget.get("PCPassValid"));
		String encrypt = MD5Utils.encrypt(oriMd5);
		return encrypt.toUpperCase();
	}
	
	/**
	 * 首字母大写
	 * @param str
	 * @return
	 */
	public static String upperCaseOne(String str) {
		if(StringUtils.isEmpty(str)) {
			return str;
		}
		return  str.substring(0, 1).toUpperCase() + str.substring(1);
	}

	/**
	 * ipg认证
	 *
	 */
	@CoreRule(protocol = "check/ipguard")
	@MethodDesc(comment = "ipg认证", returns = @ReturnDesc())
	public void checkIgguard(
			@ParamDesc(comment = "用户标识", validate = Validate.R_ID, unnull = true) String userId
	) throws Exception {
		ucIPGuardService.checkIpguard(userId);
	}

}
