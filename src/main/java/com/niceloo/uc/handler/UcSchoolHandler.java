package com.niceloo.uc.handler;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.service.UcSchoolService;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.*;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.log.Logger;

import java.util.List;
import java.util.Map;

@SuppressWarnings({"unused", "Duplicates"})
@Service
@CoreRule(protocol = "uc/school")
public class UcSchoolHandler {

    /**
     * logger
     */
    private final Logger logger = Logger.getLogger(UcUserAuthHandler.class);
    @Autowired
    private UcSchoolService ucSchoolService;

    @Autowired
    private CommonDao commonDao_uc_read;

    /**
     * 地区所属省份下所有分校ids列表
     */
    @CoreRule(protocol = "areacode/list")
    @MethodDesc(comment = "地区所属省份下所有分校ids", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "count", comment = "数量", type = Integer.class),
            @Return2Desc(name = "data", comment = "数据", type = List.class),
            @Return2Desc(name = "data[n].depIds", comment = "分校ids", type = String.class),
    }))
    public Map listType(
            @ParamDesc(comment = "区域编码", validate = Validate.R_NUM, memo = "区域编码", unnull = true) String areacode,
            @ParamDesc(comment = "地区类型", validate = "R:[PCA]", memo = "[枚举值]P:查询省及以下数据;C:查询市及以下数据;A:查询区及以下数据", unnull = true) String rangeType,
            @ParamDesc(comment = "学校禁用状态(Y:可用;N:禁用)", validate = "R:[YN]", memo = "[枚举值]Y:可用;N:禁用", defVal = "Y") String schoolAvlstatus,
            @ParamDesc(comment = "学校删除状态(Y:删除;N:未删除)", validate = "R:[YN]", memo = "[枚举值]Y:删除;N:未删除", defVal = "N") String schoolDelstatus,
            Map $params
    ) throws Exception {
        logger.info("listType开始：地区类型=" + rangeType + ",区域编码=" + areacode + ",分校禁用状态=" + schoolAvlstatus + ",分校删除状态=" + schoolDelstatus);
        if(areacode.length() != 6){
            throw new ApplicationException(UcConst.Errorcode.USER_ARGUMENT_INVALIDE,"区域编码不正确", null);
        }
        //校验邮编格式
        QueryResult qr = ucSchoolService.findByAreaCode(areacode, rangeType, schoolAvlstatus, schoolDelstatus);
        return BeanUtils.bean2Map(qr);
    }

}
