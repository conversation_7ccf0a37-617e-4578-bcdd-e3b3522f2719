package com.niceloo.uc.handler;

import static org.nobject.common.lang.StringUtils.isEmpty;

import java.util.*;

import javax.servlet.http.HttpServletRequest;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.MethodDesc;
import org.nobject.common.code.describer.ParamDesc;
import org.nobject.common.code.describer.Return2Desc;
import org.nobject.common.code.describer.ReturnDesc;
import org.nobject.common.code.describer.Validate;
import org.nobject.common.db.SQLUtils;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.ObjectUtils;
import org.nobject.common.lang.StringUtils;
import org.nobject.common.log.Logger;

import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.bean.CoreConst.ErrorCode;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.lock.service.LockService;
import com.niceloo.core.log.CoreLogService;
import com.niceloo.core.mvc.MVCUtils;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.uc.common.UcConfig;
import com.niceloo.uc.common.UcConfig.Ed;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.common.UcConst.Avlstatus;
import com.niceloo.uc.common.UcConst.Errorcode;
import com.niceloo.uc.common.UcConst.IDcardType;
import com.niceloo.uc.common.UcConst.UkType;
import com.niceloo.uc.common.UcConst.UserFlag;
import com.niceloo.uc.common.UcConst.UserIDcardStatus;
import com.niceloo.uc.common.UcConst.UserLoginpwdtype;
import com.niceloo.uc.model.UcBrand;
import com.niceloo.uc.model.UcUser;
import com.niceloo.uc.model.log.UcLoginlog;
import com.niceloo.uc.model.log.UcMobileviewlog;
import com.niceloo.uc.model.log.UcRegistlog;
import com.niceloo.uc.model.log.UcUserloginlog;
import com.niceloo.uc.service.UcBrandService;
import com.niceloo.uc.service.UcUserService;
import com.niceloo.uc.utils.DictUtils;
import com.niceloo.uc.utils.IDCardUtils;

/**
 * UcUserHandler
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@CoreRule(protocol = "uc/user")
public class UcUserHandler {

	/** logger */
	private Logger logger = Logger.getLogger(UcUserHandler.class);

	/** UcUserService */
	@Autowired
	private UcUserService ucUserService;

	@Autowired
	private UcBrandService ucBrandService;

	@Autowired
	private CoreLogService coreLogService;

	private LockService lockService;

	/**
	 * 用户列表
	 */
	@CoreRule(protocol = "list_m", needLogin = false)
	@MethodDesc(comment = "用户列表", returns = @ReturnDesc(subs = {}))
	public Map list_m(@ParamDesc(comment = "用户姓名", validate = Validate.R_NAME) String userName,
			@ParamDesc(comment = "用户登录帐号", validate = Validate.R_LOGINNAME) String userLoginname,
			@ParamDesc(comment = "分页起始", validate = Validate.M_STARTINDEX, defVal = "0") Integer pageIndex,
			@ParamDesc(comment = "分页数量", validate = Validate.M_COUNT, defVal = "10") Integer pageSize)
			throws Exception {
		Map where = MapUtils.toMap(new Object[][] { { "userName", userName }, { "userLoginname", userLoginname }, });
		Map order = null;
		QueryResult qr = ucUserService.queryMapsCount_M(where, null, order, pageIndex, pageSize);
		return BeanUtils.bean2Map(qr);
	}

	/**
	 * 用户列表
	 */
	@CoreRule(protocol = "list_i", needLogin = false)
	@MethodDesc(comment = "用户列表", returns = @ReturnDesc(subs = {}))
	public Map list_i(@ParamDesc(comment = "用户姓名", validate = Validate.R_NAME) String userName,
			@ParamDesc(comment = "用户登录帐号", validate = Validate.R_LOGINNAME) String userLoginname,
			@ParamDesc(comment = "分页起始", validate = Validate.M_STARTINDEX, defVal = "0") Integer pageIndex,
			@ParamDesc(comment = "分页数量", validate = Validate.M_COUNT, defVal = "10") Integer pageSize)
			throws Exception {
		Map where = MapUtils.toMap(new Object[][] { { "userName", userName }, { "userLoginname", userLoginname }, });
		Map order = null;
		QueryResult qr = ucUserService.queryMapsCount_I(where, null, order, pageIndex, pageSize);
		return BeanUtils.bean2Map(qr);
	}

	/**
	 * 用户列表
	 */
	@CoreRule(protocol = "list", needLogin = false)
	@MethodDesc(comment = "用户列表", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "count"								, comment = "数量"			, type = Integer.class										),
			@Return2Desc(name = "data"								, comment = "数据"			, type = String.class										),
			@Return2Desc(name = "data[n].userId"					, comment = "用户标识"		, type = String.class										),
			@Return2Desc(name = "data[n].userName"					, comment = "用户姓名"		, type = String.class										),
			@Return2Desc(name = "data[n].userFlag"					, comment = "用户类型"		, type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师"	),
			@Return2Desc(name = "data[n].userLoginname"				, comment = "用户登录帐号"		, type = String.class										),
			@Return2Desc(name = "data[n].userLoginpwdstatus"		, comment = "用户登录密码状态"	, type = String.class, memo = "[枚举]S:自设定;I:初始化"			),
			@Return2Desc(name = "data[n].userLockstatus"			, comment = "用户锁定状态"		, type = String.class, memo = "[枚举]Y:锁定;N:未锁定"			),
			@Return2Desc(name = "data[n].userLastloginip"			, comment = "用户上次登录IP"	, type = String.class										),
			@Return2Desc(name = "data[n].userLastlogindate"			, comment = "用户上次登录时间"	, type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"	),
			@Return2Desc(name = "data[n].userCreateddate"			, comment = "用户创建时间"		, type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"	),
			@Return2Desc(name = "data[n].userCreatesrc"				, comment = "用户创建来源"		, type = String.class										),
			@Return2Desc(name = "data[n].userCreatetype"			, comment = "用户创建类型"		, type = String.class										),
			@Return2Desc(name = "data[n].userMobile"				, comment = "用户手机"		, type = String.class										),
			@Return2Desc(name = "data[n].userMobilestatus"			, comment = "用户手机状态"		, type = String.class, memo = "[枚举]Y:已验证;N:未验证"			),
			@Return2Desc(name = "data[n].userEmail"					, comment = "用户邮箱"		, type = String.class										),
			@Return2Desc(name = "data[n].userEmailstatus"			, comment = "用户邮箱状态"		, type = String.class, memo = "[枚举]Y:已验证;N:未验证"			),
			@Return2Desc(name = "data[n].userIdcard"				, comment = "用户身份证号"		, type = String.class										),
			@Return2Desc(name = "data[n].userGender"				, comment = "用户性别"		, type = String.class, memo = "[枚举]M:男性;F:女性"			),
			@Return2Desc(name = "data[n].studentDeposittype"		, comment = "用户学员类型"		, type = String.class, memo = "[枚举]C:会员;T:学员"			),
	}))
	public Map list(
			@ParamDesc(comment = "用户标识集合"	, validate = Validate.R_IDS											) String userId,
			@ParamDesc(comment = "用户姓名"		, validate = Validate.M_SAFE										) String userName,
			@ParamDesc(comment = "用户登录帐号"	, validate = Validate.R_LOGINNAME									) String userLoginname,
			@ParamDesc(comment = "用户邮箱"		, validate = Validate.R_EMAIL										) String userEmail,
			@ParamDesc(comment = "用户手机号"		, validate = Validate.R_PHONE										) String userMobile,
			@ParamDesc(comment = "用户性别"		, validate = "R:[MF]"				, memo = "[枚举]M:男性;F:女性"		) String userGender,
			@ParamDesc(comment = "用户学员类型"	, validate = "R:[CT]"				, memo = "[枚举]C:会员;T:学员"		) String studentDeposittype,
			@ParamDesc(comment = "用户身份证号"	, validate = Validate.R_IDCARD										) String userIdcard,
			@ParamDesc(comment = "用户锁定状态"	, validate = "R:[YN]"				, memo = "[枚举]Y:锁定;N:未锁定"	) String userLockstatus,
			@ParamDesc(comment = "用户可用状态"	, validate = "R:[YN]"				, memo = "[枚举]Y:可用;N:不可用"	) String userAvlstatus,
			@ParamDesc(comment = "用户删除状态"	, validate = "R:[YN]"				, memo = "[枚举]Y:删除;N:未删除"	) String userDelstatus,
			@ParamDesc(comment = "用户类型"		, validate = "R:[SAITC]"			, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师;C:企业用户") String userFlag,
			@ParamDesc(comment = "排序字段"		, validate = "R:[YN]"				, memo = "[枚举]Y:正序;N:倒序"		) String orderVal,
			@ParamDesc(comment = "分页起始"		, validate = Validate.M_STARTINDEX	, defVal = "0"					) Integer pageIndex,
			@ParamDesc(comment = "分页数量"		, validate = Validate.M_SAFE		, defVal = "10"					) Integer pageSize,
			@ParamDesc(comment = "品牌标识"		, validate = Validate.M_SAFE										) String brandId,
			@ParamDesc(comment="排序字段"																				) String orderKey,
			@ParamDesc(comment="用户创建时间起始"		,validate=Validate.M_YMDHMS) 								String userSourcecreateddate_begin,
			@ParamDesc(comment="用户创建时间结束"		,validate=Validate.M_YMDHMS) 						String userSourcecreateddate_end,
			Map $params
			)throws Exception {
		if(pageSize < 1) {
			QueryResult qr = new QueryResult();
			return MapUtils.toMap0(qr);
		}
		return MapUtils.toMap0(ucUserService.queryMapsCount(pageIndex, pageSize,$params,orderKey,orderVal));
	}

	/**
	 * add
	 */
	@CoreRule(protocol = "add", needLogin = false)
	@MethodDesc(comment = "用户添加", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "userId", comment = "用户标识", type = String.class), }))
	public Map add(
			@ParamDesc(comment = "用户姓名"		, validate = Validate.M_SAFE														) String userName,
			@ParamDesc(comment = "用户登录帐号"	, validate = Validate.R_LOGINNAME														) String userLoginname,
			@ParamDesc(comment = "用户登录密码"	, validate = Validate.M_SAFE															) String userLoginpwd,
			@ParamDesc(comment = "用户登录密码状态"	, validate = "R:[SI]"				, memo = "[枚举]S:自设定;I:初始化"						) String userLoginpwdstatus,
			@ParamDesc(comment = "用户锁定状态"	, validate = "R:[YN]"				, memo = "[枚举]Y:锁定;N:未锁定"						) String userLockstatus,
			@ParamDesc(comment = "用户类型"		, validate = "R:[SAITC]"			, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师;C:企业用户"		) String userFlag,
			@ParamDesc(comment = "用户手机"		, validate = Validate.R_PHONE															) String userMobile,
			@ParamDesc(comment = "用户手机状态"	, validate = "R:[YN]"				, memo = "[枚举]Y:已验证;N:未验证"						) String userMobilestatus,
			@ParamDesc(comment = "用户邮箱"		, validate = Validate.R_EMAIL															) String userEmail,
			@ParamDesc(comment = "用户邮箱状态"	, validate = "R:[YN]"				, memo = "[枚举]Y:已验证;N:未验证"						) String userEmailstatus,
			@ParamDesc(comment = "用户身份证号"	, validate = Validate.R_IDCARD															) String userIdcard,
			@ParamDesc(comment = "用户性别"		, validate = "R:[MF]"				, memo = "[枚举]M:男性;F:女性"							) String userGender,
			@ParamDesc(comment = "用户删除状态 "	, validate = "R:[YN]"				, memo = "[枚举]Y:已删除;N:未删除", defVal = Avlstatus.NO) String userDelstatus,
			@ParamDesc(comment = "用户可用状态  "	, validate = "R:[YN]"				, memo = "[枚举]Y:有效;N:无效"	  , defVal = Avlstatus.YES) String userAvlstatus,
			@ParamDesc(comment = "品牌标识"		, validate = Validate.M_SAFE		, memo = "[枚举]YOULU:优路"	,defVal="YOULU"			) String brandId,
			@ParamDesc(comment = "来源类型"		, validate = Validate.M_SAFE									,defVal="YOULU.WEB"		) String userSourcetype,
			@ParamDesc(comment = "扩展信息"		) Map ext,
			HttpServletRequest request,
			Map $params) throws Exception {

		UcUser user = new UcUser();
		BeanUtils.setBean(user, $params);

		// 密码状态
		if (!isEmpty(userLoginpwd)) {
			if (isEmpty(userLoginpwdstatus)) {
				user.setUserLoginpwdstatus("S");// 自设定
			}
		} else {
			//初始化
			user.setUserLoginpwdstatus("I");
			user.setUserLoginpwd(UcConfig.User.loginpwd_default);
		}

		// 锁定状态
		if (isEmpty(userLockstatus)) {
			user.setUserLockstatus("N");// 未锁定
		}

		// 手机
		if (!isEmpty(userMobile)) {
			if (isEmpty(userMobilestatus)) {
				user.setUserMobilestatus("Y");// 已验证
			}
		} else {
			user.setUserMobilestatus(null);
		}

		// 邮箱
		if (!isEmpty(userEmail)) {
			if (isEmpty(userEmailstatus)) {
				user.setUserEmailstatus("Y");// 已验证
			}
		} else {
			user.setUserEmailstatus(null);
		}

		//
		if(isEmpty(user.getUserLoginpwdtype())){
			user.setUserLoginpwdtype(UserLoginpwdtype.SHA1);
		}

		if(lockService==null){
 			lockService = MVCUtils.getBean(LockService.class);
 		}
 		String selfMark = UUID.randomUUID().toString();

 		String key = user.getUserMobile() + brandId + userFlag;
 		try{
			Boolean flag = lockService.tryLock(key, selfMark, 100000L, 10000, 300L);
			if(flag){
				Map add = ucUserService.add(user, ext);
				String cType = $params.get("cType")== null ? (String)$params.get("cType"):request.getParameter("cType");
				BeanUtils.setBean(user,add);
				this.recordRegistLog(user,cType);
				return add;
			}else {
				throw new ApplicationException(UcConst.Errorcode.USER_ADD_ERROR,"注册失败，请稍后重试。", null);
			}
		}finally{
			lockService.unlock(key, selfMark);
		}
	}

	/**
	 * 记录注册信息
	 * @param user
	 * @param cType
	 */
	private void recordRegistLog(UcUser user, String cType) {
		if("S".equals(user.getUserFlag())){
			UcRegistlog registLog = new UcRegistlog();
			registLog.setUserId(user.getUserId());
			registLog.setUserName(user.getUserName());
			registLog.setUserMobile(user.getUserMobile());
			registLog.setUserLoginname(user.getUserLoginname());
			if(cType == null || "".equals(cType)){
				cType = "web";
			}
			registLog.setLogRegisttype(cType);

			if("wechat_app".equals(cType)){
				String detail = "微信id："+user.getUserOpenid()+";"+user.getUserMemo()
						+ ";注册来源:"+user.getUserSourcetype();
				registLog.setLogRegistdetail(detail);
			}
			registLog.setLogRegiststatus(UcRegistlog.STATUS_SUCCESS);
			this.coreLogService.send(registLog);
		}

	}

	/**
	 * edit
	 */
	@CoreRule(protocol = "edit", needLogin = false)
	@MethodDesc(comment = "用户修改", returns = @ReturnDesc(subs = {}))
	public Map edit(
			@ParamDesc(comment = "用户标识"		, validate = Validate.R_ID			, unnull = true					) String userId,
			@ParamDesc(comment = "用户姓名"		, validate = Validate.M_SAFE		, unnull = false				) String userName,
			@ParamDesc(comment = "用户锁定状态"	, validate = "R:[YN]"				, memo = "[枚举]Y:锁定;N:未锁定"	) String userLockstatus,
			@ParamDesc(comment = "用户邮箱"		, validate = Validate.R_EMAIL										) String userEmail,
			@ParamDesc(comment = "用户邮箱状态"	, validate = "R:[YN]"				, memo = "[枚举]Y:已验证;N:未验证"	) String userEmailstatus,
			@ParamDesc(comment = "用户性别"		, validate = "R:[MF]"				, memo = "[枚举]M:男性;F:女性"		) String userGender,
			@ParamDesc(comment = "用户地区编码"	, validate = Validate.M_SAFE										) String userAreacode,
			@ParamDesc(comment = "用户删除状态 "	, validate = "R:[YN]"				, memo = "[枚举]Y:已删除;N:未删除"	, defVal = Avlstatus.NO) String userDelstatus,
			@ParamDesc(comment = "用户可用状态  "	, validate = "R:[YN]"				, memo = "[枚举]Y:有效;N:无效"		, defVal = Avlstatus.YES) String userAvlstatus,
			@ParamDesc(comment = "用户微信"		, validate = Validate.M_SAFE										) 	String userWeixin,
			@ParamDesc(comment = "用户qq"		, validate = Validate.M_SAFE										) 	String userQq,
			@ParamDesc(comment = "用户昵称"		, validate = Validate.M_SAFE										) 	String userNickname,
			@ParamDesc(comment = "婚姻状态"		,validate="R:[YN]"					,memo="[枚举]Y:已婚;N:未婚") 			String userMarrystatus,
			@ParamDesc(comment = "用户邮编"		, validate = Validate.M_SAFE										) 	String userPostcode,
			@ParamDesc(comment = "用户居住地址"	, validate = Validate.M_SAFE										) 	String userAddress,
			@ParamDesc(comment = "用户电话"		, validate = Validate.M_SAFE										) 	String userTel,
			@ParamDesc(comment = "用户头像"		, validate = Validate.M_SAFE										) 	String userAvatar,
			@ParamDesc(comment = "用户备注"		, validate = Validate.M_SAFE										) 	String userMemo,
			@ParamDesc(comment = "用户出生日期"	, validate = Validate.M_SAFE										) 	String userBirthday,
			@ParamDesc(comment = "用户签名"		, validate = Validate.M_SAFE										) 	String userSignature,
			@ParamDesc(comment = "用户工作单位名称"	, validate = Validate.M_SAFE										) 	String userWorkunit,
			@ParamDesc(comment = "用户参加工作年份"	, validate = Validate.M_SAFE										) 	String userWorkyear,
			@ParamDesc(comment = "用户毕业院校"	, validate = Validate.M_SAFE										) 	String userEduschool,
			@ParamDesc(comment = "用户学历"		, validate = Validate.M_SAFE										) 	String userEdulevel,
			@ParamDesc(comment = "用户专业"		, validate = Validate.M_SAFE										) 	String userEdumajor,
			@ParamDesc(comment = "扩展信息"																			) Map ext, 
			@ParamDesc(comment = "修改来源"		, validate = Validate.M_SAFE										) 	String sourceType,
			Map $params_desc
			) throws Exception {
		//
		MapUtils.removeEmpty($params_desc);

		// Q&C:
		UcUser oUser = ucUserService.queryById(userId);
		if (oUser == null) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "用户不存在", null, null);
		}
		//
		String userIdcardstatus = oUser.getUserIdcardstatus();
		if(UcConst.UserIDcardStatus.SUCCESS.equals(userIdcardstatus)) {
			$params_desc.remove("userName");
		}
		UcUser user = ucUserService.getUserInstance();
 		BeanUtils.setBean(user, MapUtils.removeEmpty($params_desc));
		//
		return ucUserService.edit(user, oUser, ext,sourceType);
	}

	 /**
	 * delete
	 */
	 @CoreRule(protocol="delete",needLogin=false)
	 @MethodDesc(comment="用户删除",returns=@ReturnDesc(subs={
	 }))
	 public void delete(
	 @ParamDesc(comment="用户标识" ,validate=Validate.R_ID) String userId,
	 Map $params
	 )throws Exception{
		 UcUser user= ucUserService.queryById(userId);
		 if(user==null) {
			 throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"用户不存在",null);
		 }
		 ucUserService.delete(user);
	 }

	/**
	 * view
	 */
	@CoreRule(protocol = "view", needLogin = false)
	@MethodDesc(comment = "用户查看", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "userId", comment = "用户标识", type = String.class),
			@Return2Desc(name = "userName", comment = "用户姓名", type = String.class),
			@Return2Desc(name = "userFlag", comment = "用户类型", type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师"),
			@Return2Desc(name = "userLoginname", comment = "用户登录帐号", type = String.class),
			@Return2Desc(name = "userLoginpwdstatus", comment = "用户登录密码状态", type = String.class, memo = "[枚举]S:自设定;I:初始化"),
			@Return2Desc(name = "userLockstatus", comment = "用户锁定状态", type = String.class, memo = "[枚举]Y:锁定;N:未锁定"),
			@Return2Desc(name = "userLastloginip", comment = "用户上次登录IP", type = String.class),
			@Return2Desc(name = "userLastlogindate", comment = "用户上次登录时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
			@Return2Desc(name = "userCreateddate", comment = "用户创建时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
			@Return2Desc(name = "userCreatesrc", comment = "用户创建来源", type = String.class),
			@Return2Desc(name = "userCreatetype", comment = "用户创建类型", type = String.class),
			@Return2Desc(name = "userMobile", comment = "用户手机", type = String.class),
			@Return2Desc(name = "userMobilestatus", comment = "用户手机状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证"),
			@Return2Desc(name = "userEmail", comment = "用户邮箱", type = String.class),
			@Return2Desc(name = "userEmailstatus", comment = "用户邮箱状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证"),
			@Return2Desc(name = "userIdcard", comment = "用户身份证号", type = String.class),
			@Return2Desc(name = "userGender", comment = "用户性别", type = String.class, memo = "[枚举]M:男性;F:女性"), }))
	public Map view(@ParamDesc(comment = "用户标识", validate = Validate.R_ID) String userId,
			@ParamDesc(comment = "用户登录帐号", validate = Validate.R_LOGINNAME) String userLoginname,
			@ParamDesc(comment = "用户手机", validate = Validate.R_PHONE) String userMobile,
			@ParamDesc(comment = "用户邮箱", validate = Validate.R_EMAIL) String userEmail,
			@ParamDesc(comment = "用户类型", validate = "R:[SAITC]", memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师,C:企业用户",defVal = "S") String userFlag,
			@ParamDesc(comment = "检测是否为空", validate = "R:[YN]", memo = "[枚举]Y:检测;N:不检测", defVal = "Y") String checkNull,
			@ParamDesc(comment = "品牌标识"	, validate = Validate.M_SAFE,defVal = "YOULU") String brandId,
			Map $params) throws Exception {
		UcUser user = null;
		if (!isEmpty(userId)) {
			user = ucUserService.queryById(userId);
		} else if (!isEmpty(userFlag)) {
			if (!isEmpty(userLoginname)) {
				user = ucUserService.queryByLoginname$Flag$Avlstatus(userLoginname, userFlag, null,brandId);
			} else if (!isEmpty(userMobile)) {
				user = ucUserService.queryByMobile$Flag$Avlstatus(userMobile, userFlag, null,brandId);
			} else if (!isEmpty(userEmail)) {
				user = ucUserService.queryByEmail$Flag$Avlstatus(userEmail, userFlag, null,brandId);
			} else {
				throw new Exception("用户登录帐号、邮箱、手机不能同时为空");
			}
		}

		// Q&C:用户
		if ("Y".equals(checkNull)) {
			if (user == null)
				throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "用户不存在", null, null);
		}

		//
		if (user == null) {
			return null;
		}
		// 清除掉用户隐私信息
		Map rtn = BeanUtils.bean2Map(user);
		String studentDeposittype = ucUserService.findStudentDeposittypeIdByUserId(userId);
		rtn.remove("userLoginpwd");
		rtn.put("studentDeposittype", studentDeposittype);
		return rtn;
	}

	/**
	 * 修改密码
	 */
	@CoreRule(protocol = "pwd/edit", needLogin = false)
	@MethodDesc(comment = "密码修改", returns = @ReturnDesc(subs = {

	}))
	public void pwd_edit(@ParamDesc(comment = "用户标识", validate = Validate.R_ID, unnull = true) String userId,
			@ParamDesc(comment = "用户密码", validate = Validate.M_SAFE) String userLoginpwd,
			@ParamDesc(comment = "用户密码新", validate = Validate.R_PASSWORD, unnull = true) String userLoginpwd_new,
			Map $params) throws Exception {
		// Q&C:用户
		UcUser user = ucUserService.queryById(userId);
		if (user == null) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "用户不存在", null, null);
		}

		//
		if (!isEmpty(user.getUserLoginpwd()) && isEmpty(userLoginpwd)) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "原密码不能为空", null, null);
		}

		//如果没有初始密码就直接修改密码 如果有密码就校验原密码是否正确
		if (!isEmpty(user.getUserLoginpwd()) &&
			!ucUserService.matchPwd(userLoginpwd, user.getUserLoginpwd(), user.getUserLoginpwdtype())) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "原密码不正确", null, null);
		}
		ucUserService.editLoginpwd(user, userLoginpwd_new);
	}


	/**
	 * 修改密码
	 */
	@CoreRule(protocol="di/pwd/edit",needLogin=false)
	@MethodDesc(comment="密码修改",returns=@ReturnDesc(subs={

	}))
	public void di_pwd_edit(
		@ParamDesc(comment="用户来源标识"		,validate=Validate.R_ID			,unnull=true) 		String userSourceid,
		@ParamDesc(comment="用户密码"			,validate=Validate.M_SAFE					) 		String userLoginpwd,
		@ParamDesc(comment="用户密码类型"		,validate=Validate.M_SAFE					) 		String userLoginpwdtype,
		Map $params
	)throws Exception{
		UcUser user= ucUserService.queryBySourceId(userSourceid);
 		if(user==null){
 			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"用户不存在",null,null);
 		}
 		user.setUserLoginpwd(userLoginpwd);
 		user.setUserLoginpwdtype(userLoginpwdtype);
 		String nowDString = DateUtils.getNowDString();
 		user.setUserLastloginpwddate(nowDString);
 		user.setUserModifieddate(nowDString);
 		ucUserService.editDiLoginpwd(user);
	}




	/**
	 * 密码重置
	 */
	@CoreRule(protocol = "pwd/reset", needLogin = false)
	@MethodDesc(comment = "密码重置", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "userId", comment = "用户标识", type = String.class),
			@Return2Desc(name = "userName", comment = "用户姓名", type = String.class),
			@Return2Desc(name = "userFlag", comment = "用户类型", type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师"),
			@Return2Desc(name = "userLoginname", comment = "用户登录帐号", type = String.class),
			@Return2Desc(name = "userLoginpwdstatus", comment = "用户登录密码状态", type = String.class, memo = "[枚举]S:自设定;I:初始化"),
			@Return2Desc(name = "userLockstatus", comment = "用户锁定状态", type = String.class, memo = "[枚举]Y:锁定;N:未锁定"),
			@Return2Desc(name = "userLastloginip", comment = "用户上次登录IP", type = String.class),
			@Return2Desc(name = "userLastlogindate", comment = "用户上次登录时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
			@Return2Desc(name = "userCreateddate", comment = "用户创建时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
			@Return2Desc(name = "userCreatesrc", comment = "用户创建来源", type = String.class),
			@Return2Desc(name = "userCreatetype", comment = "用户创建类型", type = String.class),
			@Return2Desc(name = "userMobile", comment = "用户手机", type = String.class),
			@Return2Desc(name = "userMobilestatus", comment = "用户手机状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证"),
			@Return2Desc(name = "userEmail", comment = "用户邮箱", type = String.class),
			@Return2Desc(name = "userEmailstatus", comment = "用户邮箱状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证"),
			@Return2Desc(name = "userIdcard", comment = "用户身份证号", type = String.class),
			@Return2Desc(name = "userGender", comment = "用户性别", type = String.class, memo = "[枚举]M:男性;F:女性"), }))
	public UcUser pwd_reset(
			@ParamDesc(comment = "用户类型", validate = "R:[SAITC]", memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师;C:企业用户", unnull = true) String userFlag,
			@ParamDesc(comment = "用户手机", validate = Validate.R_PHONE) String userMobile,
			@ParamDesc(comment = "用户邮箱", validate = Validate.R_EMAIL) String userEmail,
			@ParamDesc(comment = "用户登陆账号", validate = Validate.M_SAFE) String userLoginname,
			@ParamDesc(comment = "用户密码", validate = Validate.R_PASSWORD, unnull = true) String userLoginpwd,
			@ParamDesc(comment = "品牌标识"	, validate = Validate.M_SAFE,defVal = "YOULU") String brandId,
			Map $params) throws Exception {
		// Q&C:用户
		UcUser user = null;
		if (!isEmpty(userMobile)) {
			user = ucUserService.queryByMobile$Flag$Avlstatus(userMobile, userFlag, null,brandId);
		} else if (!isEmpty(userEmail)) {
			user = ucUserService.queryByEmail$Flag$Avlstatus(userEmail, userFlag, null,brandId);
		} else if(!isEmpty(userLoginname)){
			user = ucUserService.queryByLoginname$Flag$Avlstatus(userLoginname, userFlag, null,brandId);
		}else{
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "邮箱、手机不能同时为空", null);
		}

		if (user == null) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "用户不存在", null);
		}
		if(UcConst.Code.Lockstatus.YES.equals(user.getUserLockstatus())) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"账号已锁定，请联系管理员",null,null);
		}
		if(UcConst.Avlstatus.NO.equals(user.getUserAvlstatus())) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"账号已禁用，请联系管理员",null,null);
		}
		ucUserService.editLoginpwd(user, userLoginpwd);
		return user;
	}

	Thread tImp = null;

	/**
	 * impFromCRM
	 */
	@CoreRule(protocol = "impFromCRM", needLogin = false)
	@MethodDesc(comment = "从CRM中导入用户")
	public void impFromCRM() throws Exception {
		if (tImp != null) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "正在导入用户", null);
		}
		new Thread() {
			public void run() {
				try {
					ucUserService.impFromCRM();
				} catch (Exception e) {
					logger.error("[CRM用户导入]:" + e.getMessage());
				} finally {
					UcUserHandler.this.tImp = null;
				}

			}
		}.start();
	}

	/**
	 * 用户列表
	 */
	@CoreRule(protocol = "list_d", needLogin = false)
	@MethodDesc(comment = "用户列表", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据", type = String.class),
			@Return2Desc(name = "data[n].userId", comment = "用户标识", type = String.class),
			@Return2Desc(name = "data[n].userName", comment = "用户姓名", type = String.class),
			@Return2Desc(name = "data[n].userFlag", comment = "用户类型", type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师"),
			@Return2Desc(name = "data[n].userLoginname", comment = "用户登录帐号", type = String.class),
			@Return2Desc(name = "data[n].userLoginpwdstatus", comment = "用户登录密码状态", type = String.class, memo = "[枚举]S:自设定;I:初始化"),
			@Return2Desc(name = "data[n].userLockstatus", comment = "用户锁定状态", type = String.class, memo = "[枚举]Y:锁定;N:未锁定"),
			@Return2Desc(name = "data[n].userLastloginip", comment = "用户上次登录IP", type = String.class),
			@Return2Desc(name = "data[n].userLastlogindate", comment = "用户上次登录时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
			@Return2Desc(name = "data[n].userCreateddate", comment = "用户创建时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
			@Return2Desc(name = "data[n].userCreatesrc", comment = "用户创建来源", type = String.class),
			@Return2Desc(name = "data[n].userCreatetype", comment = "用户创建类型", type = String.class),
			@Return2Desc(name = "data[n].userMobile", comment = "用户手机", type = String.class),
			@Return2Desc(name = "data[n].userMobilestatus", comment = "用户手机状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证"),
			@Return2Desc(name = "data[n].userEmail", comment = "用户邮箱", type = String.class),
			@Return2Desc(name = "data[n].userEmailstatus", comment = "用户邮箱状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证"),
			@Return2Desc(name = "data[n].userIdcard", comment = "用户身份证号", type = String.class),
			@Return2Desc(name = "data[n].userGender", comment = "用户性别", type = String.class, memo = "[枚举]M:男性;F:女性"),
			@Return2Desc(name = "data[n].userAreacode", comment = "地区编号", type = String.class),
			@Return2Desc(name = "data[n].userNickname", comment = "用户昵称", type = String.class),
			@Return2Desc(name = "data[n].userMarrystatus", comment = "婚姻状态", type = String.class, memo = "[枚举]Y:已婚;N:未婚"),
			@Return2Desc(name = "data[n].userPostcode", comment = "用户邮编", type = String.class),
			@Return2Desc(name = "data[n].userAddress", comment = "用户居住地址", type = String.class),
			@Return2Desc(name = "data[n].userTel", comment = "用户电话", type = String.class),
			@Return2Desc(name = "data[n].userIdcardtype", comment = "用户证件类型", type = String.class),
			@Return2Desc(name = "data[n].userAvatar", comment = "用户头像", type = String.class),
			@Return2Desc(name = "data[n].userMemo", comment = "用户备注", type = String.class),
			@Return2Desc(name = "data[n].userBirthday", comment = "用户出生日期", type = String.class),
			@Return2Desc(name = "data[n].userSignature", comment = "用户签名", type = String.class),

	}))
	public List list_d(@ParamDesc(comment = "用户标识集合", validate = Validate.R_IDS) String ids) throws Exception {

		String[] idlist = ids.split(",");

		return ucUserService.list_d(idlist);
	}

	/**
	 * 个人信息
	 *
	 * @param userId
	 *            用户标识
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "profile/view", needLogin = false)
	@MethodDesc(comment = "个人信息", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "userName", comment = "用户姓名", type = String.class),
			@Return2Desc(name = "userGender", comment = "用户性别", type = String.class, memo = "[枚举]M:男性;F:女性"),
			@Return2Desc(name = "userMobile", comment = "用户手机", type = String.class),
			@Return2Desc(name = "userLoginname", comment = "用户登陆账号", type = String.class),
			@Return2Desc(name = "userLastlogindate", comment = "用户上次登录时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
			@Return2Desc(name = "userEmail", comment = "用户邮箱", type = String.class),
			@Return2Desc(name = "userAvatar", comment = "用户头像", type = String.class),
			@Return2Desc(name = "userBirthday", comment = "用户生日", type = String.class),
			@Return2Desc(name = "userAreacode", comment = "用户地区编码", type = String.class),
			@Return2Desc(name = "userAreadesc", comment = "用户地区名称", type = String.class),
			@Return2Desc(name = "userSignature", comment = "用户签名", type = String.class),
			@Return2Desc(name = "userIdcard", comment = "证件号", type = String.class),
			@Return2Desc(name = "userIdcardstatus", comment = "证件状态", type = String.class,memo = "[枚举]N:未填写;W:待审核;F:审核失败;S:审核成功"),
			@Return2Desc(name = "studentDeposittype", comment = "学员充值状态", type = String.class,memo = "[枚举]T:学员,C:会员"),
			}))
	public Map profile_view(
			@ParamDesc(comment = "用户标识", validate = Validate.R_ID, unnull = true) String userId,
			Map $params
			) throws Exception {
		Map rtn = ucUserService.profile_view(userId);
		if(rtn == null) {
			throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT,"用户不存在!",null);
		}
		String userFlag = (String) rtn.get("userFlag");
		if("S".equals(userFlag)) {
			ucUserService.executeUser(rtn);
			rtn.remove("userLoginpwd");
			String userIdcardstatus = (String) rtn.get("userIdcardstatus");
			String userIdcardtype = (String) rtn.get("userIdcardtype");
			Map dict = DictUtils.getDict("idcard", userIdcardtype);
			if(dict != null) {
				String dictName = (String) dict.get("dictName");
				rtn.put("userIdcardtypename", dictName);
			}else {
				rtn.put("userIdcardtypename", "");
			}
			String userIdcard = (String) rtn.get("userIdcard");
			if(StringUtils.isEmpty(userIdcardstatus)) {
				if(StringUtils.isEmpty(userIdcard)) {
					rtn.put("userIdcardstatus", "N");
				}else {
					rtn.put("userIdcardstatus", "W");
				}
			}
		}
		String userAreacode = (String)rtn.get("userAreacode");
		String userAreadesc = "";
		if(!StringUtils.isEmpty(userAreacode)) {
			Map dict = DictUtils.getDict("area", userAreacode);
			if(dict != null) {
				userAreadesc = (String) dict.get("dictName");
			}
		}
		rtn.put("userAreadesc", userAreadesc);
		return rtn;
	}

	/**
	 * profile_edit
	 */
	@CoreRule(protocol = "profile/edit", needLogin = false)
	@MethodDesc(comment = "用户资料修改", returns = @ReturnDesc(subs = {}))
	public Map profile_edit(
			@ParamDesc(comment = "用户标识"		, validate = Validate.R_ID, 					   unnull = true) String userId,
			@ParamDesc(comment = "用户性别"		, validate = "R:[MF]"				 , memo = "[枚举]M:男性;F:女性") String userGender,
			@ParamDesc(comment = "用户昵称"		 																) String userNickname,
			@ParamDesc(comment = "用户头像"		, validate = Validate.M_SAFE									) String userAvatar,
			@ParamDesc(comment = "用户QQ"		, validate = Validate.R_NUM										) String userQq,
			@ParamDesc(comment = "用户微信"		, validate = Validate.M_SAFE									) String userWeixin,
			@ParamDesc(comment = "结婚状态"		, validate = "R:[YN]"				 , memo = "[枚举]Y:已婚;N:未婚") String userMarrystatus,
			@ParamDesc(comment = "用户邮编"		, validate = Validate.M_SAFE									) String userPostcode,
			@ParamDesc(comment = "用户地址"		, validate = Validate.M_SAFE									) String userAddress,
			@ParamDesc(comment = "用户生日"		, validate = Validate.M_SAFE									) String userBirthday,
			@ParamDesc(comment = "用户签名"		, validate = Validate.M_SAFE									) String userSignature,
			@ParamDesc(comment = "用户工作单位名称"	, validate = Validate.M_SAFE									) String userWorkunit,
			@ParamDesc(comment = "用户参加工作年份"	, validate = Validate.R_NUM										) String userWorkyear,
			@ParamDesc(comment = "用户毕业院校"	, validate = Validate.M_SAFE									) String userEduschool,
			@ParamDesc(comment = "用户学历"		, validate = Validate.R_NUM										) String userEdulevel,
			@ParamDesc(comment = "用户专业"		, validate = Validate.M_SAFE									) String userEdumajor,
			@ParamDesc(comment = "扩展信息") 																  Map ext,
			Map $params)
			throws Exception {
		UcUser oUser = ucUserService.queryById(userId);
		if (oUser == null) throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "用户不存在", null, null);
		UcUser user = (UcUser) BeanUtils.clone(oUser);
		BeanUtils.setBean(user, MapUtils.removeEmpty($params));
		user.setUserSignature(userSignature);
		
		Map userMap = ucUserService.edit(user, oUser, ext,"");

		if(!isEmpty(oUser.getUserAvatar())&&!oUser.getUserAvatar().equals(user.getUserAvatar())){
			CoreUtils.request(MVCUtils.requireProperty("fs.url"), "api/file/delete", MapUtils.toMap(new Object[][] {
				{"filePath",oUser.getUserAvatar()}
			}),"UC","FS");
		}
		return userMap;
	}

	/**
	 * app添加用户
	 */
	@CoreRule(protocol = "app/add", needLogin = false)
	@MethodDesc(comment = "app添加用户", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "userId", comment = "用户标识", type = String.class), }))
	public Map wechat_add(
			@ParamDesc(comment = "用户OPENID", validate = Validate.R_ID) String userOpenid,
			@ParamDesc(comment = "app标识", validate = Validate.R_ID) 	String appId,
			@ParamDesc(comment = "来源类型",	validate = Validate.M_SAFE,defVal = "YOULU.WX") String userSourcetype,
			@ParamDesc(comment = "品牌标识",	validate = Validate.M_SAFE,defVal = "YOULU") String brandId,
		Map ext, Map $params
		)throws Exception {
		UcUser user = new UcUser();
		user.setUserOpenid(userOpenid);
		user.setUserMemo("appId:" + appId);
		user.setUserSourcetype(userSourcetype);
		user.setBrandId(brandId);
		String id = ucUserService.wechatAdd(user);
		Map map = new HashMap();
		map.put("userId", id);
		this.recordRegistLog(user,"wechat_app");
		return map;
	}

	/**
	 * mesh
	 */
	@CoreRule(protocol = "mesh", needLogin = false)
	@MethodDesc(comment = "用户合并", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "userId", comment = "用户标识", type = String.class), }))
	public Map mesh(@ParamDesc(comment = "用户手机", validate = Validate.R_PHONE) String userMobile, Map $params)
		throws Exception {
		UcUser user = ucUserService.selectFirstUserByMobile(userMobile);
		return ucUserService.deleteOtherUsers(user);
	}

	@CoreRule(protocol = "bind/phone", needLogin = false)
	@MethodDesc(comment = "绑定用户手机号", returns = @ReturnDesc(subs = {

	}))
	public void bindPhone(@ParamDesc(comment = "用户标识", validate = Validate.R_ID, unnull = true) String userId,
			@ParamDesc(comment = "用户手机", validate = Validate.R_PHONE, unnull = true) String verifyPhone,
			@ParamDesc(comment = "访问IP",  unnull = true)String ip,
			@ParamDesc(comment = "品牌标识"	, validate = Validate.M_SAFE,defVal = "YOULU") String brandId,
			@ParamDesc(comment = "是否推送"	, validate = Validate.M_SAFE,defVal = "Y") String isPush,
			
			Map $params)
			throws Exception {

		UcUser user = new UcUser();
		user.setUserId(userId);
		user.setUserMobile(verifyPhone);
		ucUserService.bindPhone(user,ip,brandId,isPush);
	}

	//暂不开放
//	@CoreRule(protocol = "unbundling/phone", needLogin = false)
//	@MethodDesc(comment = "解绑用户手机号", returns = @ReturnDesc(subs = {
//
//	}))
//	public void unbundlingPhone(@ParamDesc(comment = "用户标识", validate = Validate.R_ID, unnull = true) String userId,
//			Map $params) throws Exception {
//		UcUser user = new UcUser();
//		user.setUserId(userId);
//		ucUserService.unbundlingPhone(user);
//	}

	@CoreRule(protocol = "bind/mail", needLogin = false)
	@MethodDesc(comment = "绑定用户邮箱", returns = @ReturnDesc(subs = {

	}))
	public void bindMail(@ParamDesc(comment = "用户标识", validate = Validate.R_ID, unnull = true) String userId,
			@ParamDesc(comment = "用户邮箱", validate = Validate.R_EMAIL, unnull = true) String verifyEmail, Map $params)
			throws Exception {
		UcUser user = new UcUser();
		user.setUserId(userId);
		user.setUserEmail(verifyEmail);
		ucUserService.bindEmail(user);
	}

	/**
	 * 修改用户手机号暂时只对企业用户开放
	 * @param userId
	 * @param $params
	 * @throws Exception
	 */
	@CoreRule(protocol = "phone/edit", needLogin = false)
	@MethodDesc(comment = "修改用户手机号", returns = @ReturnDesc(subs = {
	}))
	public void phone_edit(
			@ParamDesc(comment = "用户标识", validate = Validate.R_ID, unnull = true) String userId,
			@ParamDesc(comment = "用户手机", validate = Validate.R_PHONE, unnull = true) String userMobile,
			Map $params)
			throws Exception {
		UcUser user = new UcUser();
		user.setUserId(userId);
		user.setUserMobile(userMobile);
		ucUserService.phone_edit(user);
	}

	@CoreRule(protocol = "edit/mail", needLogin = false)
	@MethodDesc(comment = "修改用户邮箱", returns = @ReturnDesc(subs = {

	}))
	public void editMail(@ParamDesc(comment = "用户标识", validate = Validate.R_ID, unnull = true) String userId,
			@ParamDesc(comment = "用户邮箱", validate = Validate.R_EMAIL, unnull = true) String verifyEmail, Map $params)
			throws Exception {
		UcUser user = new UcUser();
		user.setUserId(userId);
		user.setUserEmail(verifyEmail);
		ucUserService.editMail(user);
	}

	/**
	 * 查询用户手机号
	 * @param userId
	 * @param $params
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "find/phone", needLogin = false)
	@MethodDesc(comment = "查询用户手机号", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "phone", comment = "用户手机", type = String.class), }))
	public Map editMail(
		@ParamDesc(comment="用户标识"	,validate=Validate.R_ID			,unnull=true) 		String userId,
		Map $params
	)throws Exception {
		return ucUserService.findPhone(userId);
	}

	/**
	 * 开启登录验证
	 * @param userId
	 * @param validateType
	 * @param $params
	 * @throws Exception
	 */
	@CoreRule(protocol = "login/validate", needLogin = false)
	@MethodDesc(comment = "开启登录验证", returns = @ReturnDesc(subs = {
			}))
	public void changeLoginValidate(
		@ParamDesc(comment="用户标识"	,validate=Validate.R_ID			,unnull=true) 		String userId,
		@ParamDesc(comment="校验方式"	,validate=Validate.R_NUM		,unnull=true,memo = "[枚举]:1:密码校验;2:密码+手机;3:密码+邮箱;") 		String validateType,
		Map $params
	)throws Exception {

		ucUserService.changeLoginValidate(userId,validateType);
	}

	/**
	 * 用户列表
	 */
	@CoreRule(protocol = "list_mc", needLogin = false)
	@MethodDesc(comment = "用户列表", returns = @ReturnDesc(subs = {
		@Return2Desc(name = "count"								, comment = "数量"			, type = Integer.class										),
		@Return2Desc(name = "data"								, comment = "数据"			, type = String.class										),
		@Return2Desc(name = "data[n].userId"					, comment = "用户标识"		, type = String.class										),
		@Return2Desc(name = "data[n].userName"					, comment = "用户姓名"		, type = String.class										),
		@Return2Desc(name = "data[n].userLoginname"				, comment = "用户登录帐号"		, type = String.class										),
		@Return2Desc(name = "data[n].userMobile"				, comment = "用户手机"		, type = String.class										) }))
	public Map listMc(
		@ParamDesc(comment = "用户性别"		, validate = "R:[MF]"				, memo = "[枚举]M:男性;F:女性"		) String userGender,
		@ParamDesc(comment = "用户类型"		, validate = "R:[SAIT]"				, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师"		) String userFlag,
		@ParamDesc(comment = "地区编码"		, validate = Validate.M_SAFE				 								) String userAreacode,
		@ParamDesc(comment = "用户名"			, validate = Validate.M_SAFE			) String userName,
		@ParamDesc(comment = "用户手机号"		, validate = Validate.M_SAFE			) String userMobile,
		@ParamDesc(comment = "排序字段"		, validate = "R:[YN]"				, memo = "[枚举]Y:正序;N:倒序"		) String orderVal,
		@ParamDesc(comment = "分页起始"		, validate = Validate.M_STARTINDEX	, defVal = "0"					) Integer pageIndex,
		@ParamDesc(comment = "分页数量"		, validate = Validate.M_STARTINDEX		, defVal = "10"					) Integer pageSize,
		@ParamDesc(comment="排序字段"																				) String orderKey
			)throws Exception {
		Map where = MapUtils.toMap(new Object[][] { { "userFlag", userFlag }, { "userAreacode", userAreacode },{ "userGender", userGender }, { "userName", userName },{ "userMobile", userMobile }});

		QueryResult qr = ucUserService.querymcMapsCount(pageIndex, pageSize,where,orderKey,orderVal);
		return BeanUtils.bean2Map(qr);
	}

	/**
	 * 根据用户标识list获取用户信息 - 调用服务:消息中心
	 * @date 2019-06-11
	 * @param userIds
	 * @return
	 * @throws DBException
	 */
	@CoreRule(protocol = "id/list", needLogin = false)
    @MethodDesc(comment = "用户列表", returns = @ReturnDesc(subs = {
        @Return2Desc(name = "data", comment = "数据", type = String.class),
        @Return2Desc(name = "data[n].userId", comment = "用户标识", type = String.class),
        @Return2Desc(name = "data[n].userName", comment = "用户姓名", type = String.class),
        @Return2Desc(name = "data[n].userFlag", comment = "用户类型", type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师"),
        @Return2Desc(name = "data[n].userLoginname", comment = "用户登录帐号", type = String.class),
        @Return2Desc(name = "data[n].userLoginpwdstatus", comment = "用户登录密码状态", type = String.class, memo = "[枚举]S:自设定;I:初始化"),
        @Return2Desc(name = "data[n].userLockstatus", comment = "用户锁定状态", type = String.class, memo = "[枚举]Y:锁定;N:未锁定"),
        @Return2Desc(name = "data[n].userLastloginip", comment = "用户上次登录IP", type = String.class),
        @Return2Desc(name = "data[n].userLastlogindate", comment = "用户上次登录时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
        @Return2Desc(name = "data[n].userCreateddate", comment = "用户创建时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
        @Return2Desc(name = "data[n].userCreatesrc", comment = "用户创建来源", type = String.class),
        @Return2Desc(name = "data[n].userCreatetype", comment = "用户创建类型", type = String.class),
        @Return2Desc(name = "data[n].userMobile", comment = "用户手机", type = String.class),
        @Return2Desc(name = "data[n].userMobilestatus", comment = "用户手机状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证"),
        @Return2Desc(name = "data[n].userEmail", comment = "用户邮箱", type = String.class),
        @Return2Desc(name = "data[n].userEmailstatus", comment = "用户邮箱状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证"),
        @Return2Desc(name = "data[n].userIdcard", comment = "用户身份证号", type = String.class),
        @Return2Desc(name = "data[n].userGender", comment = "用户性别", type = String.class, memo = "[枚举]M:男性;F:女性"),
        @Return2Desc(name = "data[n].userAreacode", comment = "地区编号", type = String.class),
        @Return2Desc(name = "data[n].userNickname", comment = "用户昵称", type = String.class),
        @Return2Desc(name = "data[n].userMarrystatus", comment = "婚姻状态", type = String.class, memo = "[枚举]Y:已婚;N:未婚"),
        @Return2Desc(name = "data[n].userPostcode", comment = "用户邮编", type = String.class),
        @Return2Desc(name = "data[n].userAddress", comment = "用户居住地址", type = String.class),
        @Return2Desc(name = "data[n].userTel", comment = "用户电话", type = String.class),
        @Return2Desc(name = "data[n].userIdcardtype", comment = "用户证件类型", type = String.class),
        @Return2Desc(name = "data[n].userAvatar", comment = "用户头像", type = String.class),
        @Return2Desc(name = "data[n].userMemo", comment = "用户备注", type = String.class),
        @Return2Desc(name = "data[n].userBirthday", comment = "用户出生日期", type = String.class),
        @Return2Desc(name = "data[n].userSignature", comment = "用户签名", type = String.class),
    }))
	public List id_list(
			@ParamDesc(comment = "用户id集合"     , validate = Validate.M_SAFE              , memo = "[说明]用户id集合"        ) List<String> userIds, 
			@ParamDesc(comment = "不缓存"     	, validate = Validate.M_SAFE      ,defVal="false"        , memo = "不缓存"        ) boolean noDelay,
			Map $params
			) throws DBException {
	   return this.ucUserService.findByUserIds(userIds,null,noDelay);
	}

	/**
	 * 根据用户标识list获取用户姓名手机号 - 调用服务:孟泽纬  移动端考勤列表使用
	 * @param userIds
	 * @return
	 * @throws DBException
	 */
	@CoreRule(protocol = "id/list/namemobile", needLogin = false)
    @MethodDesc(comment = "用户列表", returns = @ReturnDesc(subs = {
        @Return2Desc(name = "data", comment = "数据", type = String.class),
        @Return2Desc(name = "data[n].userId", comment = "用户标识", type = String.class),
        @Return2Desc(name = "data[n].userName", comment = "用户姓名", type = String.class),
        @Return2Desc(name = "data[n].userMobile", comment = "用户手机", type = String.class),
    }))
	public Map list_namemobile(@ParamDesc(comment = "用户id集合"     , validate = Validate.M_SAFE              , memo = "[说明]用户id集合"        ) List<String> userIds, Map $params) throws DBException {
		List data = this.ucUserService.findNameMobileByUserIds(userIds);
		if(data == null) {
			data = new ArrayList<>();
		}
		return MapUtils.toMap(new Object[][] {
			{"data",data}
		});
	}

	/**
	 * di系统添加用户  同步程序添加用户  老网站废弃后需要移除
	 */
	@CoreRule(protocol="di_add",needLogin=false)
	@MethodDesc(comment="用户添加",returns=@ReturnDesc(subs={
		@Return2Desc(name="userId"			,comment="用户标识"	,type=String.class),
	}))
	public Map di_add(
		@ParamDesc(comment="用户姓名"			) 																								String userName,
		@ParamDesc(comment="用户登录帐号"		) 																								String userLoginname,
		@ParamDesc(comment="用户登录密码"		) 																								String userLoginpwd,
		@ParamDesc(comment="用户登录密码状态"		,validate="R:[SI]"						,memo="[枚举]S:自设定;I:初始化") 						String userLoginpwdstatus,
		@ParamDesc(comment="用户锁定状态"			,validate="R:[YN]"						,memo="[枚举]Y:锁定;N:未锁定") 							String userLockstatus,
		@ParamDesc(comment="用户类型"				,validate="R:[SAIT]"						,memo="[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师")					String userFlag,
		@ParamDesc(comment="用户来源标识"			,validate=Validate.R_ID					)                             						String userSourceid,
		@ParamDesc(comment="用户来源类型"		)                             																	String userSourcetype,
		@ParamDesc(comment="用户创建类型"	    	,validate=Validate.R_EN$CN$NUM)                             								String userCreatetype,
		@ParamDesc(comment="用户手机"			) 																								String userMobile,
		@ParamDesc(comment="用户手机状态"			,validate="R:[YN]"						,memo="[枚举]Y:已验证;N:未验证") 						String userMobilestatus,
		@ParamDesc(comment="用户邮箱"			) 																								String userEmail,
		@ParamDesc(comment="用户邮箱状态"			,validate="R:[YN]"						,memo="[枚举]Y:已验证;N:未验证")   						String userEmailstatus,
		@ParamDesc(comment="用户身份证号"	    )                                  																String userIdcard,
		@ParamDesc(comment="用户性别"			,validate="R:[MF]"							,memo="[枚举]M:男性;F:女性")      						String userGender,
		@ParamDesc(comment="用户删除状态 "		,validate="R:[YN]"							,memo="[枚举]Y:已删除;N:未删除"	,defVal=Avlstatus.NO)   String userDelstatus,
		@ParamDesc(comment="用户可用状态  "		,validate="R:[YN]"							,memo="[枚举]Y:有效;N:无效"	,defVal=Avlstatus.YES)  String userAvlstatus,
		@ParamDesc(comment="用户微信"			) 																		String userWeixin,
		@ParamDesc(comment="用户qq"			) 																		String userQq,
		@ParamDesc(comment="地区编号"			) 																		String userAreacode,
		@ParamDesc(comment="用户昵称"			) 																		String userNickname,
		@ParamDesc(comment="婚姻状态"			,validate="R:[YN]"							,memo="[枚举]Y:已婚;N:未婚") 	String userMarrystatus,
		@ParamDesc(comment="用户邮编"			) 																		String userPostcode,
		@ParamDesc(comment="用户居住地址"		) 																		String userAddress,
		@ParamDesc(comment="用户电话"			) 																		String userTel,
		@ParamDesc(comment="用户证件类型"		) 																		String userIdcardtype,
		@ParamDesc(comment="用户头像"			) 																		String userAvatar,
		@ParamDesc(comment="用户备注"			) 																		String userMemo,
		@ParamDesc(comment="用户出生日期"		) 																		String userBirthday,
		@ParamDesc(comment="用户签名"			) 																		String userSignature,
		@ParamDesc(comment = "品牌标识",	validate = Validate.M_SAFE,defVal = "YOULU") 								String brandId,
		Map $params
	)throws Exception{
		UcUser user=new UcUser();
		BeanUtils.setBean(user, $params);

	//密码状态
		if(!isEmpty(user.getUserLoginpwd())){
			if(isEmpty(user.getUserLoginpwdstatus())){
				user.setUserLoginpwdstatus("S");//自设定
			}
		}else{
			user.setUserLoginpwdstatus(null);
		}

		//锁定状态
		if(isEmpty(user.getUserLockstatus())){
			user.setUserLockstatus("N");//未锁定
		}

		//手机
		if(!isEmpty(user.getUserMobile())){
			if(isEmpty(user.getUserMobilestatus())){
				user.setUserMobilestatus("Y");//已验证
			}
		}else{
			user.setUserMobilestatus(null);
		}

		//邮箱
		if(!isEmpty(user.getUserEmail())){
			if(isEmpty(user.getUserEmailstatus())){
				user.setUserEmailstatus("Y");//已验证
			}
		}else{
			user.setUserEmailstatus(null);
		}

		//添加
		ucUserService.nocheckadd(user);

		//
		return MapUtils.toMap(new Object[][]{
			{"userId",user.getUserId()}
		});
	}

	/**
	 * di系统删除用户
	 */
	@CoreRule(protocol="di_delete",needLogin=false)
	@MethodDesc(comment="用户删除",returns=@ReturnDesc(subs={
	}))
	public void di_delete(
		@ParamDesc(comment="用户标识"			) 				String userId
	)throws Exception{
		ucUserService.diDelete(userId);
	}

	/**
	 * edit
	 * 老网站废弃后需要移除
	 */
	@CoreRule(protocol = "di_edit", needLogin = false)
	@MethodDesc(comment = "同步系统用户修改", returns = @ReturnDesc(subs = {}))
	public Map di_edit(
			@ParamDesc(comment = "用户标识"		, validate = Validate.R_ID			, unnull = true					) String userId,
			@ParamDesc(comment = "用户姓名"																			) String userName,
			@ParamDesc(comment = "用户登录帐号"																		) String userLoginname,
			@ParamDesc(comment = "用户登录密码"																		) String userLoginpwd,
			@ParamDesc(comment = "用户登录密码状态"	, validate = "R:[SI]"				, memo = "[枚举]S:自设定;I:初始化"	) String userLoginpwdstatus,
			@ParamDesc(comment = "用户类型"		, validate = "R:[SAIT]"				, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师") String userFlag,
			@ParamDesc(comment = "用户锁定状态"	, validate = "R:[YN]"				, memo = "[枚举]Y:锁定;N:未锁定"	) String userLockstatus,
			@ParamDesc(comment = "用户手机"																			) String userMobile,
			@ParamDesc(comment = "用户手机状态"	, validate = "R:[YN]"				, memo = "[枚举]Y:已验证;N:未验证"	) String userMobilestatus,
			@ParamDesc(comment = "用户邮箱"																			) String userEmail,
			@ParamDesc(comment = "用户邮箱状态"	, validate = "R:[YN]"				, memo = "[枚举]Y:已验证;N:未验证"	) String userEmailstatus,
			@ParamDesc(comment = "用户身份证号"																		) String userIdcard,
			@ParamDesc(comment = "用户性别"		, validate = "R:[MF]"				, memo = "[枚举]M:男性;F:女性"		) String userGender,
			@ParamDesc(comment = "用户地区编码"																		) String userAreacode,
			@ParamDesc(comment = "用户删除状态 "	, validate = "R:[YN]"				, memo = "[枚举]Y:已删除;N:未删除"	, defVal = Avlstatus.NO) String userDelstatus,
			@ParamDesc(comment = "用户可用状态  "	, validate = "R:[YN]"				, memo = "[枚举]Y:有效;N:无效"		, defVal = Avlstatus.YES) String userAvlstatus,
			@ParamDesc(comment = "用户来源标识"																		)  	String userSourceid,
			@ParamDesc(comment = "用户微信"																			) 	String userWeixin,
			@ParamDesc(comment = "用户qq"																			) 	String userQq,
			@ParamDesc(comment = "用户昵称"																			) 	String userNickname,
			@ParamDesc(comment = "婚姻状态"		,validate="R:[YN]"					,memo="[枚举]Y:已婚;N:未婚") 			String userMarrystatus,
			@ParamDesc(comment = "用户邮编"																			) 	String userPostcode,
			@ParamDesc(comment = "用户居住地址"																		) 	String userAddress,
			@ParamDesc(comment = "用户电话"																			) 	String userTel,
			@ParamDesc(comment = "用户证件类型"																		) 	String userIdcardtype,
			@ParamDesc(comment = "用户头像"																			) 	String userAvatar,
			@ParamDesc(comment = "用户备注"																			) 	String userMemo,
			@ParamDesc(comment = "用户出生日期"																		) 	String userBirthday,
			@ParamDesc(comment = "用户签名"																			) 	String userSignature,
			@ParamDesc(comment = "扩展信息"																			) Map ext, Map $params
			) throws Exception {

		//
		MapUtils.removeEmpty($params);

		// Q&C:
		UcUser oUser = ucUserService.queryById(userId);
		if (oUser == null)
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "用户不存在", null, null);

		//
		UcUser user = ucUserService.getUserInstance();
 		BeanUtils.setBean(user, MapUtils.removeEmpty($params));
 		user.setUserLoginpwdtype(UcConst.UserLoginpwdtype.MD5);
		//
		return ucUserService.diEdit(user, oUser, ext,null);
	}

	/**
	 *	老网站废弃后需要移除
	 *
	 */
	@CoreRule(protocol = "uk", needLogin = false)
	@MethodDesc(comment = "获取用户UK", returns = @ReturnDesc(subs = {
			@Return2Desc(name="uk"			,comment="UK"	,type=String.class),
	}))
	public Map uk(
			@ParamDesc(comment = "用户标识", 		validate = Validate.R_ID,	unnull = true) 												String userId,
			@ParamDesc(comment = "请求端类型", 	validate = "R:[WPAC]",		unnull = true,memo="[枚举]W:wap站;P:pc;A:app,C:pc客户端") 		String type,
			@ParamDesc(comment = "IP", 			validate = Validate.M_SAFE,	unnull = true) 												String ip,
			@ParamDesc(comment = "极光CODE", 	validate = Validate.M_SAFE,	unnull = false) 											String machineCode,
			@ParamDesc(comment = "手机品牌", 		validate = Validate.M_SAFE,	unnull = false) 											String phoneBrand,
			Map $params_desc
			)throws Exception {
		if(UkType.APP.equals(type)) {
			if(StringUtils.isEmpty(phoneBrand)) {
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"手机品牌不能为空!",null);
			}
		}
		if(UkType.PC_CLIENT.equals(type)) {
			if(StringUtils.isEmpty(phoneBrand)) {
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"品牌不能为空!",null);
			}
		}
		UcUser user = ucUserService.queryById(userId);
		String userSourceid = user.getUserSourceid();
		if(StringUtils.isEmpty(userSourceid)) {
			//注册用户
			String userMobile = user.getUserMobile();
			if(StringUtils.isEmpty(userMobile)) {
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"请先绑定手机号!",null);
			}else {
				userSourceid = ucUserService.netRegist(user, ip,"");
			}
		}
		String uk=ucUserService.getUk(userSourceid,type,machineCode,phoneBrand);
		return MapUtils.toMap(new Object[][] {
			{"uk",	uk}
		});
	}

	/**
	 *
	 */
	@CoreRule(protocol = "idcard/identify", needLogin = false)
	@MethodDesc(comment = "身份认证", returns = @ReturnDesc(subs = {
	}))
	public void idcard_identify(
			@ParamDesc(comment = "用户证件类型", 	validate = "R:[IHTO]",		unnull = true,memo="[枚举]I:身份证;H:港澳通行证;T:台湾来往大陆通行证;O:军官证") 	String userIdcardtype,
			@ParamDesc(comment = "证件号", 		validate = Validate.M_SAFE,	unnull = true) 													String userIdcard,
			@ParamDesc(comment = "姓名", 		validate = Validate.M_SAFE,	unnull = true) 													String userName,
			@ParamDesc(comment = "用户标识", 		validate = Validate.M_SAFE,	unnull = true) 													String userId,
			Map $params_desc
			)throws Exception {
		UcUser ucUser = ucUserService.queryById(userId);
		if(ucUser == null) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"用户不存在",null);
		}
		String userIdcardstatus = ucUser.getUserIdcardstatus();
		if(UserIDcardStatus.SUCCESS.equals(userIdcardstatus)) {
			throw new ApplicationException(Errorcode.USER_IDCARD_EXIST,"证件已存在",null);
		}
		if(IDcardType.IDCARD.equals(userIdcardtype)) {
			IDCardUtils.idcardValidate(userIdcard, userName, Ed.address, Ed.identify);
		}else if(IDcardType.HGJMLW.equals(userIdcardtype)) {
			IDCardUtils.hongKongValidate(userIdcard);
		}else if(IDcardType.TWJMLW.equals(userIdcardtype)) {
			IDCardUtils.taiWanValidate(userIdcard);
		}else if(IDcardType.OFFICER.equals(userIdcardtype)){
			IDCardUtils.officerValidate(userIdcard);
		}else{
			throw new ApplicationException(Errorcode.USER_IDCARD_ERROR,"证件类型错误!",null);
		}
		ucUser.setUserIdcard(userIdcard);
		ucUser.setUserIdcardtype(userIdcardtype);
		ucUser.setUserName(userName);
		ucUser.setUserIdcardstatus(UserIDcardStatus.SUCCESS);
		ucUserService.idcardIdentify(ucUser);
	}

	/**
	 * 根据用户来源标识获取用户信息
	 */
	@CoreRule(protocol="sourceId/view",needLogin=false)
	@MethodDesc(comment="来源标识获取用户",returns=@ReturnDesc(subs={
		@Return2Desc(name="userId"			,comment="用户标识"	,type=String.class),
	}))
	public Map sourceid_view(
		@ParamDesc(comment="用户来源标识"	,validate=Validate.M_SAFE ) 		String userSourceid,
		Map $params
	)throws Exception{
		UcUser ucUser = ucUserService.queryBySourceId(userSourceid);
		if(ucUser == null) {
			throw new ApplicationException(UcConst.Errorcode.USER_ARGUMENT_INVALIDE, "用户找不到", null);
		}
		return MapUtils.toMap(new Object[][]{
			{"userId",ucUser.getUserId()}
		});
	}

	/**
	 * CRM注册用户   李小龙专用
	 * @throws Exception
	 */
	@CoreRule(protocol="regist/crm",needLogin=false)
	@MethodDesc(comment="crm注册用户",returns=@ReturnDesc(subs={
		@Return2Desc(name="data[n]"							,comment="数据信息"		,type=String.class),
		@Return2Desc(name="data[n].userId"					,comment="用户标识"		,type=String.class),
		@Return2Desc(name="data[n].userMobile"				,comment="用户手机号"		,type=String.class),
		@Return2Desc(name="data[n].brandId"					,comment="用户所属品牌"	,type=String.class),
	}))
	public Map regist_crm(
		@ParamDesc(comment="用户信息"			,validate=Validate.M_SAFE ,unnull= true) 			List userList,
		@ParamDesc(comment="是必须"			,validate="R:[YN]" ,	defVal="N") 				String isMust,
		Map $params
	) throws Exception {
		return regist_batch(userList,"YOULU.CRM",isMust, $params);
	}

	/**
	 * 批量注册用户  企业团培
	 * @param userList
	 * @param userSourcetype
	 * @param $params
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol="regist/batch",needLogin=false)
	@MethodDesc(comment="批量注册用户",returns=@ReturnDesc(subs={
		@Return2Desc(name="data[n]"							,comment="数据信息"		,type=String.class),
		@Return2Desc(name="data[n].userId"					,comment="用户标识"		,type=String.class),
		@Return2Desc(name="data[n].userMobile"				,comment="用户手机号"		,type=String.class),
		@Return2Desc(name="data[n].brandId"					,comment="用户所属品牌"	,type=String.class),
	}))
	public Map regist_batch(
		@ParamDesc(comment="用户信息"			,validate=Validate.M_SAFE ,unnull= true) 		List userList,
		@ParamDesc(comment="来源类型"			,validate=Validate.M_SAFE ,unnull= true) 		String userSourcetype,
		@ParamDesc(comment="是否必须"			,validate="R:[YN]" ,	defVal="N") 				String isMust,
		Map $params
	) throws Exception {
		if(userList == null || userList.size() < 1) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"用户信息不能为空!",null);
		}
		//crm传递过来的所有用户手机号
		HashMap<String,ArrayList<String>> brandMobilesMap = new HashMap<>();
		//crm传递过来的所有用户信息
		ArrayList<Map> allUser = new ArrayList<>();
		//已经存在的用户信息
		HashMap<String,UcUser> existsUser = new HashMap<>();
		String errorPhone = "";
		LinkedHashSet<String> brandIdSet = new LinkedHashSet<>();
		//过滤没有手机号的用户信息
		HashMap<String,String> mobileName = new HashMap<>();
		for (Object user : userList) {
			Map userMap= (Map) user;
			String userMobile = (String) userMap.get("userMobile");
			String userName = (String) userMap.get("userName");
			mobileName.put(userMobile, userName);
			if(!StringUtils.isEmpty(userMobile)) {
				String brandId = (String) userMap.get("brandId");
				if(StringUtils.isEmpty(brandId)) {
					errorPhone = errorPhone+userMobile+",";
				}
				brandIdSet.add(brandId);
				ArrayList<String> moblieList = brandMobilesMap.get(brandId);
				if(moblieList == null) {
					moblieList = new ArrayList<>();
					brandMobilesMap.put(brandId, moblieList);
				}
				moblieList.add(userMobile);
				allUser.add(userMap);
			}
		}
		if(!StringUtils.isEmpty(errorPhone)) {
			throw new ApplicationException(ErrorCode.argument_invalide, "品牌标识不能为空!", MapUtils.toMap(new Object[][] {
				{"userMobile",errorPhone.substring(0, errorPhone.length()-1)}
			}));
		}
		//判断brandId是否存在
		if(brandIdSet.size()==0) {
			throw new ApplicationException(ErrorCode.argument_invalide,"没有可添加的用户!",null);
		}
		validateBrandIds(brandIdSet);
		//查询已经存在的用户
		List<UcUser> exitsUserList = ucUserService.findByUserMoblies(brandMobilesMap);
		if(exitsUserList != null && exitsUserList.size() > 0) {
			for (UcUser ucUser : exitsUserList) {
				String key = (String) ucUser.getUserMobile() + (String) ucUser.getBrandId();
				UcUser ucUser2 = existsUser.get(key);
				if(ucUser2 == null) {
					existsUser.put(key , ucUser);
				}else {
					existsUser.put(key, compareLastLoginTime(ucUser2, ucUser));
				}
			}
		}
		//返回给crm的用户信息
		ArrayList<Map> resoutList = new ArrayList<>();
		executeUser(allUser, existsUser, resoutList,userSourcetype,mobileName,isMust);
		return MapUtils.toMap(new Object[][] {
			{"data",resoutList}
		});
	}


	/**
	 * 处理CRM注册的用户数据
	 * @param allUser		全部用户
	 * @param existsUser	存在的用户信息
	 * @param resoutList  	返回给crm的用户信息
	 * @param userSourcetype
	 * @param mobileName 
	 * @param isMust 
	 * @throws Exception
	 */
	public void executeUser(ArrayList<Map> allUser, HashMap<String, UcUser> existsUser, ArrayList<Map> resoutList, String userSourcetype, HashMap<String, String> mobileName, String isMust)
			throws Exception {
		for (Map mapUser : allUser) {
			String userMobile = (String) mapUser.get("userMobile");
			String brandId = (String) mapUser.get("brandId");
			UcUser existUser = existsUser.get(userMobile + brandId);
			if(existUser != null) {
				HashMap<Object,Object> userMap = new HashMap<>();
				userMap.put("userId", existUser.getUserId());
				String exisuserMobile = (String) existUser.getUserMobile();
				userMap.put("userMobile", exisuserMobile);
				String userLoginname = (String) existUser.getUserLoginname();
				userMap.put("brandId", existUser.getBrandId());
				if(StringUtils.isEmpty(userLoginname)) {
					userMap.put("userLoginname", "yl"+exisuserMobile);
				}else {
					userMap.put("userLoginname", userLoginname);
				}
				//更新用户姓名
				//如果是网站来的 就不需要更新如果是其他来的就需要更新用户姓名
				if(!"YOULU.WEB".equals(userSourcetype)&& "Y".equals(isMust)&&!UcConst.UserIDcardStatus.SUCCESS.equals(existUser.getUserIdcardstatus())) {
					String userName = mobileName.get(userMobile);
					existUser.setUserName(userName);
					if(!StringUtils.isEmpty(userName)) {
						ucUserService.updateName(existUser);
					}
				}
				userMap.put("userName", existUser.getUserName());
				//isNew   true 是新的  false  是老用户
				userMap.put("isNew", false);
				resoutList.add(userMap);
			}else {
				//需要添加用户信息
				UcUser ucUser = new UcUser();
				ucUser.setUserMobile(userMobile);
				ucUser.setUserMobilestatus("Y");
				ucUser.setUserFlag(UserFlag.student);
				ucUser.setUserIdcardstatus(UserIDcardStatus.NO);
				ucUser.setBrandId(brandId);
				String userName = (String) mapUser.get("userName");
				ucUser.setUserName(userName);
				ucUser.setUserSourcetype(userSourcetype);
				String userLoginpwd = (String) mapUser.get("userLoginpwd");
				ucUser.setUserLoginpwd(userLoginpwd);
				ucUser = ucUserService.crm_add(ucUser,isMust);
				HashMap<Object,Object> userMap = new HashMap<>();
				userMap.put("userId", ucUser.getUserId());
				userMap.put("userMobile", ucUser.getUserMobile());
				userMap.put("brandId", brandId);
				userMap.put("userName", ucUser.getUserName());
				String userLoginname = ucUser.getUserLoginname();
				if(StringUtils.isEmpty(userLoginname)) {
					userMap.put("userLoginname", "yl"+ucUser.getUserMobile());
					ucUser.setUserLoginname("yl"+ucUser.getUserMobile());
				}else {
					userMap.put("userLoginname", userLoginname);
				}
				userMap.put("isNew", true);
				recordRegistLog(ucUser , "CRM");
				resoutList.add(userMap);
			}
		}
	}

	/**
	 * 校验brandId是否正确
	 * @param brandIdSet
	 * @throws DBException
	 * @throws ApplicationException
	 */
	private void validateBrandIds(LinkedHashSet<String> brandIdSet) throws DBException, ApplicationException {
		List<UcBrand> brandList= ucBrandService.findByBrandNames(SQLUtils.getIn(brandIdSet));
		if(brandList == null || brandList.size()==0) {
			throw new ApplicationException(ErrorCode.argument_invalide,"品牌标识错误!",MapUtils.toMap(new Object[][] {
				{"msg","品牌不存在!"},
				{"brandIds",SQLUtils.getIn(brandIdSet)}
			}));
		}
		String brandNameAll = "";
		for (UcBrand ucBrand : brandList) {
			brandNameAll = brandNameAll + ucBrand.getBrandName();
		}
		if(brandIdSet.size() == brandList.size()) {
			return;
		}
		ArrayList<String> brandIds = new ArrayList<>();
		for (UcBrand brand : brandList) {
			brandIds.add(brand.getBrandId());
		}
		String errorBrandName = "";
		if(brandList == null || brandList.size() != brandIdSet.size()) {
			for (String brandId : brandIdSet) {
				if(!brandNameAll.contains(brandId)) {
					errorBrandName = errorBrandName + brandId + ",";
				}
			}
		}
		if(!StringUtils.isEmpty(errorBrandName)) {
			throw new ApplicationException(ErrorCode.argument_invalide,"品牌标识错误!",MapUtils.toMap(new Object[][] {
				{"brandIds",errorBrandName.substring(0, errorBrandName.length()-1)}
			}));
		}
	}

	/***
	 * 根据最后登录时间判断返回用户
	 * @return
	 */
	public UcUser compareLastLoginTime(UcUser ucUser2,UcUser ucUser) {
		String beforeDate = ucUser2.getUserLastlogindate();
		String afterDate =  ucUser.getUserLastlogindate();
		if(StringUtils.isEmpty(beforeDate) && StringUtils.isEmpty(afterDate)) {
			return ucUser2;
		}else if(!StringUtils.isEmpty(beforeDate) && !StringUtils.isEmpty(afterDate)) {
			if(DateUtils.compare(beforeDate, afterDate) > 0) {
				return ucUser2;
			}else {
				return ucUser;
			}
		}else if(!StringUtils.isEmpty(beforeDate)) {
			return ucUser2;
		}else {
			return ucUser;
		}
	}

	/**
	 * 获取手机号归属地
	 * @param mobile
	 * @return
	 * @throws Exception
	 */
	public String getMobileArea(String mobile) throws Exception {
		Map data = (Map) CoreUtils.request(Ed.address, Ed.mobile, MapUtils.toMap(new Object[][] {
			{"phoneNo",mobile}
		}), "UC", "ED");
		return (String) data.get("mobileAreaid");
	}

	/**
	 * 根据用户标识list获取用户信息 - 调用服务:消息中心
	 * @date 2019-06-11
	 * @return
	 * @throws DBException
	 */
	@CoreRule(protocol = "sourceId/list", needLogin = false)
    @MethodDesc(comment = "用户来源标识获取用户列表", returns = @ReturnDesc(subs = {
        @Return2Desc(name = "data", comment = "数据", type = String.class),
        @Return2Desc(name = "data[n].userId", comment = "用户标识", type = String.class),
        @Return2Desc(name = "data[n].userName", comment = "用户姓名", type = String.class),
        @Return2Desc(name = "data[n].userFlag", comment = "用户类型", type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师"),
        @Return2Desc(name = "data[n].userLoginname", comment = "用户登录帐号", type = String.class),
        @Return2Desc(name = "data[n].userLoginpwdstatus", comment = "用户登录密码状态", type = String.class, memo = "[枚举]S:自设定;I:初始化"),
        @Return2Desc(name = "data[n].userLockstatus", comment = "用户锁定状态", type = String.class, memo = "[枚举]Y:锁定;N:未锁定"),
        @Return2Desc(name = "data[n].userLastloginip", comment = "用户上次登录IP", type = String.class),
        @Return2Desc(name = "data[n].userLastlogindate", comment = "用户上次登录时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
        @Return2Desc(name = "data[n].userCreateddate", comment = "用户创建时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
        @Return2Desc(name = "data[n].userCreatesrc", comment = "用户创建来源", type = String.class),
        @Return2Desc(name = "data[n].userCreatetype", comment = "用户创建类型", type = String.class),
        @Return2Desc(name = "data[n].userMobile", comment = "用户手机", type = String.class),
        @Return2Desc(name = "data[n].userMobilestatus", comment = "用户手机状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证"),
        @Return2Desc(name = "data[n].userEmail", comment = "用户邮箱", type = String.class),
        @Return2Desc(name = "data[n].userEmailstatus", comment = "用户邮箱状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证"),
        @Return2Desc(name = "data[n].userIdcard", comment = "用户身份证号", type = String.class),
        @Return2Desc(name = "data[n].userGender", comment = "用户性别", type = String.class, memo = "[枚举]M:男性;F:女性"),
        @Return2Desc(name = "data[n].userAreacode", comment = "地区编号", type = String.class),
        @Return2Desc(name = "data[n].userNickname", comment = "用户昵称", type = String.class),
        @Return2Desc(name = "data[n].userMarrystatus", comment = "婚姻状态", type = String.class, memo = "[枚举]Y:已婚;N:未婚"),
        @Return2Desc(name = "data[n].userPostcode", comment = "用户邮编", type = String.class),
        @Return2Desc(name = "data[n].userAddress", comment = "用户居住地址", type = String.class),
        @Return2Desc(name = "data[n].userTel", comment = "用户电话", type = String.class),
        @Return2Desc(name = "data[n].userIdcardtype", comment = "用户证件类型", type = String.class),
        @Return2Desc(name = "data[n].userAvatar", comment = "用户头像", type = String.class),
        @Return2Desc(name = "data[n].userMemo", comment = "用户备注", type = String.class),
        @Return2Desc(name = "data[n].userBirthday", comment = "用户出生日期", type = String.class),
        @Return2Desc(name = "data[n].userSignature", comment = "用户签名", type = String.class),
    }))
	public List sourceId_list(@ParamDesc(comment = "用户id集合"     , validate = Validate.M_SAFE              , memo = "[说明]用户sourceId集合"        ) List<String> userSourceids, Map $params) throws DBException {
	   return this.ucUserService.findByuserSourceids(userSourceids);
	}

	/**
	 * 根据用户标识list获取用户信息 - 调用服务:消息中心
	 * @date 2019-06-11
	 * @return
	 * @throws DBException
	 * @throws ApplicationException
	 */
	@CoreRule(protocol = "list/moblies", needLogin = false)
    @MethodDesc(comment = "用户手机号获取用户列表", returns = @ReturnDesc(subs = {
        @Return2Desc(name = "data", comment = "数据", type = String.class),
        @Return2Desc(name = "data[n].userId", comment = "用户标识", type = String.class),
        @Return2Desc(name = "data[n].userMobile", comment = "用户手机号", type = String.class),
        @Return2Desc(name = "data[n].userFlag", comment = "用户类型", type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师"),
        @Return2Desc(name = "data[n].userLoginname", comment = "用户登录帐号", type = String.class)
    }))
	public Map list_mobiles(
			@ParamDesc(comment = "用户手机号集合"     	, validate = Validate.M_SAFE          , memo = "[说明]用户手机号集合List格式",        			unnull=true	) List<String> userMobiles,
			@ParamDesc(comment = "用户类型"     		, validate = "R:[SAIT]"              , memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师;",     	unnull=true) String userFlag ,
			@ParamDesc(comment = "是否需要用户名，身份证等信息"     		, validate = "R:[YN]"              , memo = "需要用户名，身份证等更多信息" ,defVal = "N") String moreInfo ,
			@ParamDesc(comment = "品牌标识"     		, validate = Validate.M_SAFE         , memo = "品牌标识" ) String brandId ,
			Map $params) throws DBException, ApplicationException {
		if(userMobiles == null || userMobiles.size() < 1) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"用户手机号集合不能为空",null);
		}
		return MapUtils.toMap(new Object[][] {
			{"data","Y".equals(moreInfo)?this.ucUserService.findByuserMobilesMore(userMobiles,userFlag,brandId):this.ucUserService.findByuserMobiles(userMobiles,userFlag,brandId)}
		});
	}

	/**
	 * 根据用户邮箱集合获取用户信息列表 - 调用服务:消息中心
	 *
	 * @return
	 * @throws DBException
	 * @throws ApplicationException
	 * @date 2020-10-16
	 */
	@CoreRule(protocol = "list/emails", needLogin = false)
	@MethodDesc(comment = "用户邮箱获取用户列表", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据", type = String.class),
			@Return2Desc(name = "data[n].userId", comment = "用户标识", type = String.class),
			@Return2Desc(name = "data[n].userMobile", comment = "用户手机号", type = String.class),
			@Return2Desc(name = "data[n].userEmail", comment = "用户邮箱", type = String.class),
			@Return2Desc(name = "data[n].userFlag", comment = "用户类型", type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师"),
			@Return2Desc(name = "data[n].userLoginname", comment = "用户登录帐号", type = String.class),
			@Return2Desc(name = "data[n].userLastlogindate", comment = "用户上次登录时间", type = String.class),
			@Return2Desc(name = "data[n].userName", comment = "用户名", memo = "入参moreInfo=Y时结果中存在该值", type = String.class),
			@Return2Desc(name = "data[n].userIdcard", comment = "用户身份证号", memo = "入参moreInfo=Y时结果中存在该值", type = String.class)
	}))
	public Map listEmails(
			@ParamDesc(comment = "用户邮箱集合", validate = Validate.M_SAFE, memo = "[说明]用户邮箱集合List格式", unnull = true) List<String> userEmails,
			@ParamDesc(comment = "用户类型", validate = "R:[SAIT]", memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师;", unnull = true) String userFlag,
			@ParamDesc(comment = "是否需要用户名，身份证等信息", validate = "R:[YN]", memo = "需要用户名，身份证等更多信息", defVal = "N") String moreInfo,
			Map $params) throws DBException, ApplicationException {
		if (ObjectUtils.isEmpty(userEmails)) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "用户邮箱集合不能为空", null);
		}
		return MapUtils.toMap(new Object[][]{
				{"data", this.ucUserService.findByUserEmails(userEmails, userFlag, moreInfo)}
		});
	}

	/**
	 * 禁用账号
	 * @throws DBException
	 * @throws ApplicationException
	 */
	@CoreRule(protocol = "avl", needLogin = false)
    @MethodDesc(comment = "禁用启用账号", returns = @ReturnDesc(subs = {}))
	public void avl(
			@ParamDesc(comment = "用户标识"     	, validate = Validate.M_SAFE          , memo = "用户标识集合",        			unnull=true	) List<String> userIds,
			@ParamDesc(comment = "禁用状态"     	, validate = "R:[YN]"          		  , memo = "枚举:[Y:启用;N:禁用]",        	unnull=true	) String avlstatus,
			Map $params) throws DBException, ApplicationException {
		List<UcUser> userList = ucUserService.findByUserIds(userIds,UcConst.Delstatus.NO,false);//查询未删除的用户信息
		if(userList.size() != userIds.size()) {
			ArrayList<String> existUserId = new ArrayList<>();
			ArrayList<String> notExistUserId = new ArrayList<>();
			for (UcUser ucUser : userList) {
				existUserId.add(ucUser.getUserId());
			}
			for (String userId : userIds) {
				if(!existUserId.contains(userId)) {
					notExistUserId.add(userId);
				}
			}
			if(notExistUserId.size()>0) {
				throw new ApplicationException(UcConst.Errorcode.USER_ARGUMENT_INVALIDE, "用户不存在", MapUtils.toMap(new Object[][] {
					{"userIds",notExistUserId}
				}));
			}
		}
		ucUserService.edituserAvlstatus(userIds, avlstatus);

	}

	/**
	 * 根据用户标识list获取用户信息 - 调用服务:消息中心
	 * @date 2019-06-11
	 * @return
	 * @throws DBException
	 * @throws ApplicationException
	 */
	@CoreRule(protocol = "list/loginname", needLogin = false)
    @MethodDesc(comment = "用户登陆名获取用户列表", returns = @ReturnDesc(subs = {
        @Return2Desc(name = "data", comment = "数据", type = String.class),
        @Return2Desc(name = "data[n].userId", comment = "用户标识", type = String.class),
        @Return2Desc(name = "data[n].userMobile", comment = "用户手机号", type = String.class),
        @Return2Desc(name = "data[n].userFlag", comment = "用户类型", type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师"),
        @Return2Desc(name = "data[n].userLoginname", comment = "用户登录帐号", type = String.class)
    }))
	public Map list_loginname(
			@ParamDesc(comment = "用户登陆名集合"     	, validate = Validate.M_SAFE          , memo = "[说明]用户登陆名集合List格式",        			unnull=true	) List<String> userLoginname,
			@ParamDesc(comment = "用户类型"     		, validate = "R:[SAIT]"              , memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师;",     	unnull=true) String userFlag ,
			@ParamDesc(comment = "不缓存"     		, validate = Validate.M_SAFE      			,defVal="false"        , memo = "不缓存"        ) boolean noDelay,
			Map $params) throws DBException, ApplicationException {
		if(userLoginname == null || userLoginname.size() < 1) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"用户登陆名集合不能为空",null);
		}
		return MapUtils.toMap(new Object[][] {
			{"data",this.ucUserService.findByuserLoginName(userLoginname,userFlag,noDelay)}
		});
	}

	/**
	 * 登录密码修改
	 * @param userId
	 * @param userLoginpwd
	 * @param $params
	 * @throws Exception
	 */
	@CoreRule(protocol = "loginpwd/edit", needLogin = false)
	@MethodDesc(comment = "登陆密码重置", returns = @ReturnDesc(subs = {

	}))
	public void loginpwd_edit(@ParamDesc(comment = "用户标识", validate = Validate.R_ID, unnull = true) String userId,
			@ParamDesc(comment = "用户登录密码", validate = Validate.R_PASSWORD, unnull = true) String userLoginpwd, Map $params)
			throws Exception {
		UcUser user = ucUserService.queryById(userId);
		if(user == null) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "用户不存在!", null, null);
		}
		ucUserService.editLoginpwd(user, userLoginpwd);
	}

	/**
	 * 手机号和登录名查询用户信息
	 * @return
	 * @throws DBException
	 */
	@CoreRule(protocol = "single/query", needLogin = false)
	@MethodDesc(comment = "登录名或手机号查询用户信息", returns = @ReturnDesc(subs = {

	}))
	public Map single_query(
			@ParamDesc(comment = "用户登录名", validate = Validate.M_SAFE, unnull = false) String userLoginname,
			@ParamDesc(comment = "用户手机号", validate = Validate.M_SAFE, unnull = false) String userMobile,
			@ParamDesc(comment = "用户类型", validate = Validate.M_SAFE, unnull = false) String userFlag) throws DBException {
		HashMap<String,Object> params = new HashMap<>();

		params.put("userFlag", userFlag);
		params.put("userDelstatus", UcConst.Delstatus.NO);
		if(!StringUtils.isEmpty(userLoginname)) {
			params.put("userLoginname", userLoginname);
		}else if(!StringUtils.isEmpty(userMobile)) {
			params.put("userMobile", userMobile);
		}
		if(params.size()<3) {
			return null;
		}
		UcUser user = ucUserService.findByCols(params);
		if(user == null) {
			return null;
		}
		return MapUtils.toMap(new Object[][] {
			{"userId",user.getUserId()},
			{"userName",user.getUserName()},
			{"userMobile",user.getUserMobile()}
		});
	}

	/**
	 * 不校验注册用户
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "nocheck/register", needLogin = false)
	@MethodDesc(comment = "不校验注册学生用户", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "userId", comment = "用户标识", type = String.class),
			@Return2Desc(name = "userName", comment = "用户姓名", type = String.class),
			@Return2Desc(name = "userFlag", comment = "用户类型", type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师"),
			@Return2Desc(name = "userLoginname", comment = "用户登录帐号", type = String.class),
			@Return2Desc(name = "userLoginpwdstatus", comment = "用户登录密码状态", type = String.class, memo = "[枚举]S:自设定;I:初始化"),
			@Return2Desc(name = "userLockstatus", comment = "用户锁定状态", type = String.class, memo = "[枚举]Y:锁定;N:未锁定"),
			@Return2Desc(name = "userLastloginip", comment = "用户上次登录IP", type = String.class),
			@Return2Desc(name = "userLastlogindate", comment = "用户上次登录时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
			@Return2Desc(name = "userCreateddate", comment = "用户创建时间", type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"),
			@Return2Desc(name = "userCreatesrc", comment = "用户创建来源", type = String.class),
			@Return2Desc(name = "userCreatetype", comment = "用户创建类型", type = String.class),
			@Return2Desc(name = "userMobile", comment = "用户手机", type = String.class),
			@Return2Desc(name = "userMobilestatus", comment = "用户手机状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证"),
			@Return2Desc(name = "userEmail", comment = "用户邮箱", type = String.class),
			@Return2Desc(name = "userEmailstatus", comment = "用户邮箱状态", type = String.class, memo = "[枚举]Y:已验证;N:未验证"),
			@Return2Desc(name = "userIdcard", comment = "用户身份证号", type = String.class),
			@Return2Desc(name = "userGender", comment = "用户性别", type = String.class, memo = "[枚举]M:男性;F:女性"),
			@Return2Desc(name = "registStatus", comment = "注册状态", type = String.class, memo = "[枚举]Y:注册;N:不注册"),
	}))
	public Map nocheck_register(
			@ParamDesc(comment = "用户手机号"		,validate = Validate.M_SAFE, unnull = false) String userMobile,
			@ParamDesc(comment = "品牌标识"		, validate = Validate.M_SAFE		, memo = "[枚举]YOULU:优路"	,defVal="YOULU"			) String brandId,
			@ParamDesc(comment = "渠道来源"		, validate = Validate.M_SAFE		) String sourceChannel,
			@ParamDesc(comment = "ip"			, validate = Validate.M_SAFE		) String ip,
			HttpServletRequest request
			) throws Exception {
		Map ucUser = ucUserService.findByuserMobile(userMobile, UcConst.UserFlag.student);
		UcUser ucUserLog = new UcUser();

		if(ucUser != null) {
			BeanUtils.setBean(ucUserLog, ucUser);
			this.recordLoginLog(ucUser);
			ucUser.put("registStatus", "N");
			return ucUser;
		}else {
			if(lockService==null){
	 			lockService = MVCUtils.getBean(LockService.class);
	 		}
	 		String selfMark = UUID.randomUUID().toString();

	 		String key = userMobile + brandId + UcConst.UserFlag.student;
	 		try{
				Boolean flag = lockService.tryLock(key, selfMark, 100000L, 10000, 300L);
				if(flag){
					ucUser = ucUserService.findByuserMobile(userMobile, UcConst.UserFlag.student);
					if(ucUser == null) {
						ucUser = ucUserService.nocheck_register(userMobile,brandId,UcConst.UserFlag.student,ip,sourceChannel);
						BeanUtils.setBean(ucUserLog, ucUser);
						this.recordRegistLog(ucUserLog,request.getParameter("cType"));
						this.recordLoginLog(ucUser);
						ucUser.put("registStatus", "Y");
						return ucUser;
					}else {
						this.recordLoginLog(ucUser);
						ucUser.put("registStatus", "N");
						return ucUser;
					}
				}else {
					ucUser = ucUserService.findByuserMobile(userMobile, UcConst.UserFlag.student);
					if(ucUser == null) {
						throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT, "用户不存在", null);
					}else {
						this.recordLoginLog(ucUser);
						ucUser.put("registStatus", "N");
						return ucUser;
					}
				}
			}finally{
				lockService.unlock(key, selfMark);
			}
		}

	}

	/**
	 * 记录登录日志
	 */
	private void recordLoginLog(Map user) {
		UcUserloginlog loginLog = new UcUserloginlog();
		loginLog.setLogLogintype(UcLoginlog.LOGINTYPE_PHONE_PASSWORD);
		loginLog.setLogLoginstatus(UcLoginlog.LOGINSTATUS_SUCCESS);
		loginLog.setUserId((String) user.get("userId"));
		loginLog.setUserLoginname((String) user.get("userLoginname"));
		loginLog.setUserMobile((String) user.get("userMobile"));
		loginLog.setUserName((String) user.get("userName"));
		this.coreLogService.send(loginLog);
	}

	/**
	 * 手机号和登录名查询用户信息
	 * @return
	 * @throws DBException
	 */
	@CoreRule(protocol = "like/query", needLogin = false)
	@MethodDesc(comment = "模糊查询用户信息", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data"								, comment = "数据"			, type = String.class										),
			@Return2Desc(name = "data[n].userId"					, comment = "用户标识"		, type = String.class										),
			@Return2Desc(name = "data[n].userName"					, comment = "用户姓名"		, type = String.class										),
			@Return2Desc(name = "data[n].userFlag"					, comment = "用户类型"		, type = String.class, memo = "[枚举]S:学生;A:代理商;I:内部用户;T:外聘教师"	),
			@Return2Desc(name = "data[n].userLoginname"				, comment = "用户登录帐号"		, type = String.class										),
			@Return2Desc(name = "data[n].userLoginpwdstatus"		, comment = "用户登录密码状态"	, type = String.class, memo = "[枚举]S:自设定;I:初始化"			),
			@Return2Desc(name = "data[n].userLockstatus"			, comment = "用户锁定状态"		, type = String.class, memo = "[枚举]Y:锁定;N:未锁定"			),
			@Return2Desc(name = "data[n].userLastloginip"			, comment = "用户上次登录IP"	, type = String.class										),
			@Return2Desc(name = "data[n].userLastlogindate"			, comment = "用户上次登录时间"	, type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"	),
			@Return2Desc(name = "data[n].userCreateddate"			, comment = "用户创建时间"		, type = String.class, memo = "[格式]yyyy-MM-dd HH:mm:ss"	),
			@Return2Desc(name = "data[n].userCreatesrc"				, comment = "用户创建来源"		, type = String.class										),
			@Return2Desc(name = "data[n].userCreatetype"			, comment = "用户创建类型"		, type = String.class										),
			@Return2Desc(name = "data[n].userMobile"				, comment = "用户手机"		, type = String.class										),
			@Return2Desc(name = "data[n].userMobilestatus"			, comment = "用户手机状态"		, type = String.class, memo = "[枚举]Y:已验证;N:未验证"			),
			@Return2Desc(name = "data[n].userEmail"					, comment = "用户邮箱"		, type = String.class										),
			@Return2Desc(name = "data[n].userEmailstatus"			, comment = "用户邮箱状态"		, type = String.class, memo = "[枚举]Y:已验证;N:未验证"			),
			@Return2Desc(name = "data[n].userIdcard"				, comment = "用户身份证号"		, type = String.class										),
			@Return2Desc(name = "data[n].userGender"				, comment = "用户性别"		, type = String.class, memo = "[枚举]M:男性;F:女性"			),
			@Return2Desc(name = "data[n].studentDeposittype"		, comment = "用户学员类型"		, type = String.class, memo = "[枚举]C:会员;T:学员"			)
	}))
	public Map like_query(
			@ParamDesc(comment = "用户姓名", validate = Validate.M_SAFE, unnull = false) String userName,
			@ParamDesc(comment = "用户登录名", validate = Validate.M_SAFE, unnull = false) String userLoginname,
			@ParamDesc(comment = "用户手机号", validate = Validate.M_SAFE, unnull = false) String userMobile,
			@ParamDesc(comment = "用户类型", validate = Validate.M_SAFE, unnull = true) String userFlag
			) throws DBException {
		HashMap<String,String> params = new HashMap<>();
		params.put("userName", userName);
		params.put("userLoginname", userLoginname);
		params.put("userMobile", userMobile);
		params.put("userFlag", userFlag);
		List data = ucUserService.likeQuery(params);
		return MapUtils.toMap(new Object[][] {
			{"data",data}
		});
	}

	/**
	 * 此接口更改，不再根据用户id去查询，而是由网关进行解密
	 * 限制访问次数，也放在网关，这里其实只是为了记录日志
	 * 查看手机号
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "hide/query", needLogin = false)
	@MethodDesc(comment = "用户隐藏信息查看", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "userMoblie"			, comment = "用户手机号"		, type = String.class	),
	}))
	public Map hide_query(
			@ParamDesc(comment = "用户标识", validate = Validate.M_SAFE, unnull = false) String userId,
			@ParamDesc(comment = "客户手机号标识", validate = Validate.M_SAFE, unnull = false) String userMobile,
			@ParamDesc(comment = "客户姓名", validate = Validate.M_SAFE, unnull = false) String userName,
			@ParamDesc(comment = "当前人id", validate = Validate.M_SAFE, unnull = false) String currUserId,
			@ParamDesc(comment = "当前人姓名", validate = Validate.M_SAFE, unnull = false) String currUsername,
			@ParamDesc(comment = "查看模块", validate = Validate.M_SAFE, unnull = false) String module,
			@ParamDesc(comment = "查看菜单名称", validate = Validate.M_SAFE, unnull = false) String menu,
			@ParamDesc(comment = "查看来源IP", validate = Validate.M_SAFE, unnull = false) String ip,
			@ParamDesc(comment = "浏览器", validate = Validate.M_SAFE, unnull = false) String browser,
			HttpServletRequest request
			) throws Exception {
//		if(userId!=null){
//			UcUser ucUser = ucUserService.queryById(userId);
//			if(ucUser ==null){
//				throw new ApplicationException(ErrorCode.argument_invalide,"用户不存在",null);
//			}
//			userMobile = ucUser.getUserMobile();
//		}


		UcMobileviewlog moblieviewlog = new UcMobileviewlog();
		moblieviewlog.setLogBrowser(browser);
		moblieviewlog.setLogCreatername(currUsername);
		moblieviewlog.setLogCreater(currUserId);
		moblieviewlog.setLogIp(ip);
		moblieviewlog.setLogMenu(menu);
		moblieviewlog.setLogModule(module);
		moblieviewlog.setUserId(userId);
		moblieviewlog.setUserMobile(userMobile);
		moblieviewlog.setUserName(userName);
		this.coreLogService.send(moblieviewlog);

		return MapUtils.toMap(new Object[][] {
			{"userMoblie",userMobile}
		});
	}

	@CoreRule(protocol = "regist/byagent", needLogin = false)
	@MethodDesc(comment = "代理商注册学员信息", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "userId"			, comment = "用户标识"		, type = String.class	),
	}))
	public Map registByAgent(
			@ParamDesc(comment = "用户手机号", validate = Validate.M_SAFE, unnull = true) String userMobile,
			HttpServletRequest request,
			Map $params
			) throws Exception {
		HashMap<String,Object> params = new HashMap<>();
		params.put("userFlag", "S");
		params.put("userDelstatus", UcConst.Delstatus.NO);
		params.put("userMobile", userMobile);

		UcUser user = ucUserService.findByCols(params);
		if(user == null) {
			params.put("cType","YOULU.AGENT");
			return this.add("","yl"+userMobile,
						null,"I","N","S",userMobile,null,
					null,null,null,null,"N","Y","YOULU","YOULU.AGENT",null,request,params
					);
		}else{
			return MapUtils.toMap(new Object[][] {
					{"userId",user.getUserId()}
			});
		}
	}

	/**
	 * 修改性别
	 */
	@CoreRule(protocol = "gender/edit", needLogin = false)
	@MethodDesc(comment = "用户修改性别", returns = @ReturnDesc(subs = {
	}))
	public void gender_edit(
			@ParamDesc(comment = "用户标识", 		validate = Validate.M_SAFE,	unnull = true) 													String userId,
			@ParamDesc(comment = "用户性别", 		validate = "R:[MF]"	,					  memo="[枚举]M:男性;F:女性") 							String userGender,
			Map $params_desc
			)throws Exception {
		UcUser ucUser = ucUserService.queryById(userId);
		if(ucUser == null) {
			throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT,"用户不存在",null);
		}
		ucUser.setUserGender(userGender);
		ucUserService.edit(ucUser);
	}

	/**
	 * 修改性别
	 * crm使用
	 */
	@CoreRule(protocol = "name/edit", needLogin = false)
	@MethodDesc(comment = "用户姓名修改", returns = @ReturnDesc(subs = {
	}))
	public void name_edit(
			@ParamDesc(comment = "用户标识", 		validate = Validate.M_SAFE,	unnull = true) 													String userId,
			@ParamDesc(comment = "用户姓名", 		validate = Validate.M_SAFE	,	unnull = true) 												String userName,
			Map $params_desc
			)throws Exception {
		ucUserService.nameEdit(userId,userName);
	}
	
	/**
	 * 用户推送信息获取
	 * crm使用
	 */
	@CoreRule(protocol = "push/list", needLogin = false)
	@MethodDesc(comment = "用户推送信息获取", returns = @ReturnDesc(subs = {
	}))
	public Map name_edit(
			@ParamDesc(comment = "用户标识", 		validate = Validate.M_SAFE,	unnull = true) 													String[] userIds,
			Map $params_desc
			)throws Exception {
		return ucUserService.push_list(userIds);
	}

	/**
	 * 提供给 消息中心 用
	 * <pre>
	 *     tapd: https://www.tapd.cn/42228080/prong/stories/view/1142228080001025129?from=letter
	 *     对接人: 王振明
	 * </pre>
	 */
	@CoreRule(protocol = "list/userids", needLogin = false)
	@MethodDesc(comment = "根据用户标识获取用户名称", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据", type = String.class),
			@Return2Desc(name = "data[n].userId", comment = "用户标识", type = String.class),
			@Return2Desc(name = "data[n].userName", comment = "用户姓名", type = String.class)
	}))
	public List getUserNameListByUserIds(
			@ParamDesc(comment = "用户标识", validate = Validate.R_IDS, unnull = true) String userIds,
			@ParamDesc(comment = "用户可用状态", validate = "R:[YN]", memo = "[枚举]Y:可用;N:不可用") String userAvlstatus
	) throws Exception {
		return ucUserService.getUserNameListByUserIds(userIds.split(","), userAvlstatus);
	}

	@CoreRule(protocol = "syncuser", needLogin = false)
	@MethodDesc(comment = "获取用户信息.用于企业学习平台同步数据", returns = @ReturnDesc(subs = {}))
	public List<Map<String, Object>> syncuser(
			@ParamDesc(comment = "用户标识列表", validate = Validate.M_SAFE, unnull = true) List<String> userIds
	) throws Exception {
		return ucUserService.queryUcUser4CorpSync(userIds);
	}

	@CoreRule(protocol = "unsubscribe", needLogin = false)
	@MethodDesc(comment = "永久注销账号", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据", type = String.class),
			@Return2Desc(name = "data[n].userId", comment = "用户标识", type = String.class),
			@Return2Desc(name = "data[n].userName", comment = "用户姓名", type = String.class)
	}))
	public Map unsubscribe(
			@ParamDesc(comment = "用户标识", validate = Validate.R_ID, unnull = true) String userId
	) throws Exception {
		UcUser user = ucUserService.queryById(userId);
		if (ObjectUtils.isEmpty(user)) {
			throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT, "账号注销异常, 用户信息不存在", null, null);
		}
		if (!(UserFlag.student.equals(user.getUserFlag()) || UserFlag.corp.equals(user.getUserFlag()))){
			throw new ApplicationException(UcConst.Errorcode.USER_ARGUMENT_INVALIDE, "账号注销异常, 用户身份信息异常", null, null);
		}

		ucUserService.unsubscribe(user);

		Map<String, String> dataMap = new LinkedHashMap<>();
		dataMap.put("userId", userId);
		dataMap.put("userName", user.getUserName());
		Map<String, Map> r = new HashMap<>();
		r.put("data", dataMap);
		return r;
	}

	/**
	 * 根据用户ID和禁用状态修改用户
	 * @param userId
	 * @param userAvlstatus
	 * @throws Exception
	 */
	@CoreRule(protocol = "modify/avlstatus", needLogin = false)
	@MethodDesc(comment = "变更user的可用状态为客户端传递的状态", returns = @ReturnDesc(subs = {}))
	public void modifyUserAvailabilityStatus(
			@ParamDesc(comment = "用户ID", validate = Validate.M_SAFE, unnull = true) String userId,
			@ParamDesc(comment = "用户禁用状态",validate = "R:[YN]",memo = "[枚举]Y:可用;N:不可用") String userAvlstatus
	) throws Exception {
	    //判断用户ID和可用状态是否为空
        if(isEmpty(userId)){
            throw new ApplicationException(UcConst.Errorcode.USER_ARGUMENT_INVALIDE, "用户ID为空", null, null);
        } else if(isEmpty(userAvlstatus)){
            throw new ApplicationException(UcConst.Errorcode.USER_ARGUMENT_INVALIDE, "用户禁用状态为空", null, null);
        }
        UcUser user = ucUserService.queryById(userId);
        if (ObjectUtils.isEmpty(user)) {
            throw new ApplicationException(UcConst.Errorcode.USER_NON_EXISTENT, "用户信息不存在", null, null);
        }
        if(user.getUserAvlstatus().equals(userAvlstatus)){
            throw new ApplicationException(UcConst.Errorcode.USER_AVL_ERROR, "用户状态已更改", null, null);
        }
        //修改状态
		ucUserService.modifyUserAvailabilityStatus(user,userAvlstatus);
	}


	/**
	 * 根据用户信息获取用户ID
	 * @param userIdcard 用户身份证号
	 * @param userName 用户姓名
	 * @param userMobile 用户手机
	 * @param userAvlstatus 用户禁用状态
	 * @param userDelstatus 用户删除状态
	 * @throws Exception 查询条件为空,用户不存在
	 */
	@CoreRule(protocol = "findidsbyparam")
	@MethodDesc(comment = "根据用户信息获取用户ID", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "userIds", comment = "用户标识列表", type = String.class),
	}))
	public Map getIdsByParam(
			@ParamDesc(comment = "用户身份证号", validate = Validate.M_SAFE) String userIdcard,
			@ParamDesc(comment = "用户姓名", validate = Validate.M_SAFE) String userName,
			@ParamDesc(comment = "用户手机", validate = Validate.M_SAFE) String userMobile,
			@ParamDesc(comment = "用户禁用状态",validate = "R:[YN]",memo = "[枚举]Y:可用;N:不可用") String userAvlstatus,
			@ParamDesc(comment = "用户删除状态",validate = "R:[YN]",memo = "[枚举]Y:删除;N:未删除",defVal = "N") String userDelstatus
	) throws Exception {
		if(isEmpty(userIdcard) && isEmpty(userName) && isEmpty(userMobile)){
			throw new ApplicationException(UcConst.Errorcode.USER_ARGUMENT_INVALIDE, "查询条件为空", null, null);
		}
		return ucUserService.getIdsByParam(userIdcard,userName,userMobile,userAvlstatus,userDelstatus);
	}

}
