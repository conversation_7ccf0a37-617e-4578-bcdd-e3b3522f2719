package com.niceloo.uc.handler;

import static org.nobject.common.lang.StringUtils.isEmpty;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.niceloo.uc.model.UcRole;
import com.niceloo.uc.service.UcRoleService;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.MethodDesc;
import org.nobject.common.code.describer.ParamDesc;
import org.nobject.common.code.describer.Return2Desc;
import org.nobject.common.code.describer.ReturnDesc;
import org.nobject.common.code.describer.Validate;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.js.JSONUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;

import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.uc.common.UcConst.Errorcode;
import com.niceloo.uc.model.UcConfig;
import com.niceloo.uc.service.UcConfigService;

/**
 * 配置-业务处理类
 * <AUTHOR>
 * @Date 2019年1月7日 15:06:22
 */
@Service
@CoreRule(protocol = "uc/config")
//@Deprecated
public class UcConfigHandler {

	@Autowired
	private UcConfigService ucConfigService;
	@Autowired
	private UcRoleService ucRoleService;
	/**
	 * 配置-添加
	 * 这里是给管理员用的只能添加角色的公共的参数
	 * @param configType 配置类型
	 * @param configCode 配置编码
	 * @param configValue 配置数值
	 * @param configScopetype 配置范围类型
	 * @param configScopeobj 配置范围对象
	 * @return
	 * @throws Exception
	 */
	
	@CoreRule(protocol = "add", needLogin = false)
	@MethodDesc(comment = "配置添加", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "configId", comment = "配置标识", type = String.class)
	}))
	public Map add(
		@ParamDesc(comment="配置类型",validate=Validate.M_SAFE,length = 100 ,unnull=true)	String configType,	
		@ParamDesc(comment="配置编码",validate=Validate.M_SAFE,length = 100 ,unnull=true)	String configCode,	
		@ParamDesc(comment="配置数值",validate=Validate.M_SAFE,length = 2000 ,unnull=true)	String configValue,	
		@ParamDesc(comment="配置范围类型",validate=Validate.M_INRANGE+"[['G','R'] ,'$value']",length = 10 ,unnull=true)	String configScopetype,	
		@ParamDesc(comment="配置范围对象",validate=Validate.M_SAFE,length = 36 ,unnull=false)	String configScopeobj,	
//		@ParamDesc(comment="配置数据类型",validate=Validate.M_SAFE,length = 10 ,unnull=true)	String configUnittype,	
//		@ParamDesc(comment="配置数据对象",validate=Validate.M_SAFE,length = 36 ,unnull=true)	String configUnitobj,	
		Map $params
	)throws Exception {
		
		UcConfig ucConfig = new UcConfig();
		BeanUtils.setBean(ucConfig, $params);
		//ucConfig.setGmtCreate(new Date());
		addAndEditBeforeValidate(ucConfig);
		ucConfigService.save(ucConfig);
		return MapUtils.toMap(new Object[][] { { "configId", ucConfig.getConfigId() } });
	}

	/**
	 * 配置-删除
	 * 给管理员用的只能删除公共配置暂时不支持删除用户自己的配置
	 * @param configId 配置标识
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "delete", needLogin = false)
	@MethodDesc(comment = "配置删除", returns = @ReturnDesc(subs = {}))
	public void delete(
		@ParamDesc(comment="配置标识",validate=Validate.R_ID,unnull = true, length = 32)	String configId,	
		Map $params
	)throws Exception { 
		UcConfig ucConfig = ucConfigService.findById(configId);
		if(ucConfig != null){
			//只能删除全局的和角色的配置
			String configScopetype = ucConfig.getConfigScopetype();
			if("G".equals(configScopetype)||"R".equals(configScopetype)) {
				ucConfigService.delete(ucConfig);
			}else {
				throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "只能删除角色和全局配置", null, null);
			}
		}
	}

	/**
	 * 配置-修改
	 * 管理员用的只能修改公共配置不允许修改用户自己的配置
	 * @param configId 配置标识
	 * @param configType 配置类型
	 * @param configCode 配置编码
	 * @param configValue 配置数值
	 * @param configScopetype 配置范围类型
	 * @param configScopeobj 配置范围对象
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "edit", needLogin = false)
	@MethodDesc(comment = "配置-编辑", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "configId", comment = "配置标识", type = String.class)
	}))
	public void edit(
		@ParamDesc(comment="配置标识",validate=Validate.R_ID,length = 36 ,unnull=true)	String configId,	
		@ParamDesc(comment="配置类型",validate=Validate.M_SAFE,length = 100 ,unnull=true)	String configType,	
		@ParamDesc(comment="配置编码",validate=Validate.M_SAFE,length = 100 ,unnull=true)	String configCode,	
		@ParamDesc(comment="配置数值",validate=Validate.M_SAFE,length = 2000 ,unnull=true)	String configValue,	
		@ParamDesc(comment="配置范围类型",validate=Validate.M_INRANGE+"[['G','R'] ,'$value']",length = 10 ,unnull=true)	String configScopetype,	
		@ParamDesc(comment="配置范围对象",validate=Validate.M_SAFE,length = 36 ,unnull=false)	String configScopeobj,	
//		@ParamDesc(comment="配置数据类型",validate=Validate.M_SAFE,length = 10 ,unnull=true)	String configUnittype,	
//		@ParamDesc(comment="配置数据对象",validate=Validate.M_SAFE,length = 36 ,unnull=true)	String configUnitobj,	
		Map $params
	)throws Exception {
		UcConfig ucConfig = ucConfigService.findById(configId);
		if (ucConfig == null) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "配置不存在", null, null);
		}
		String scopetype = ucConfig.getConfigScopetype();
		if("G".equals(scopetype)||"R".equals(scopetype)) {
		}else {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "只能修改角色和公共配置", null, null);
		}
		
 		BeanUtils.setBean(ucConfig, MapUtils.removeEmpty($params));
//		ucConfig.setConfigUnittype(configUnittype);
//		ucConfig.setConfigUnitobj(configUnitobj);
		addAndEditBeforeValidate(ucConfig);
		ucConfigService.update(ucConfig);
	}


	private void addAndEditBeforeValidate(UcConfig ucConfig) throws ApplicationException, DBException {
		String configScopetype = ucConfig.getConfigScopetype();
		String configScopeobj = ucConfig.getConfigScopeobj();
		if(!"U".equals(configScopetype)&&!"R".equals(configScopetype)&&!"G".equals(configScopetype)) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "配置范围错误!", null, null);
		}
		if("U".equals(configScopetype)||"R".equals(configScopetype)) {
			if(StringUtils.isEmpty(configScopeobj)) {
				throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "配置范围对象不能为空!", null, null);
			}
		}else {
			if(!StringUtils.isEmpty(configScopeobj)) {
				throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "配置范围对象需为空", null, null);
			}
		}
		if("R".equals(configScopetype)) {
			//查询角色是否存在
			UcRole findById = ucRoleService.findById(ucConfig.getConfigScopeobj());
			if(findById == null) {
				throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "角色不存在", null, null);
			}
		}
	}
	/**
	 * 配置-详情
	 * @param configId 配置标识
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "info", needLogin = false)
	@MethodDesc(comment = "配置-详情", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "configId", comment = "配置标识", type = String.class),
			@Return2Desc(name = "configType", comment = "配置类型", type = String.class),
			@Return2Desc(name = "configCode", comment = "配置编码", type = String.class),
			@Return2Desc(name = "configValue", comment = "配置数值", type = String.class),
			@Return2Desc(name = "configScopetype", comment = "配置范围类型", type = String.class),
			@Return2Desc(name = "configScopeobj", comment = "配置范围对象", type = String.class),
			@Return2Desc(name = "configUnittype", comment = "配置数据类型", type = String.class),
			@Return2Desc(name = "configUnitobj", comment = "配置数据对象", type = String.class),
	}))
	public Map info(
		@ParamDesc(comment="配置标识",validate=Validate.R_ID,unnull=true,length=32)	String configId,	
		Map $params
	)throws Exception {
		UcConfig ucConfig = ucConfigService.findById(configId);
		if (ucConfig == null) {
			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE, "配置不存在", null, null);
		}
		//清除掉用户隐私信息
		Map rtn=BeanUtils.bean2Map(ucConfig);
		return rtn;
	}	


	/**
	 * 配置-分页
	 * @param pageIndex 分页起始
	 * @param pageSize 分页数量
	 * @params orderKey 排序字段
	 * @params orderVal 排序字段
	 * @params configId 配置标识
	 * @params configType 配置类型
	 * @params configCode 配置编码
	 * @params configValue 配置数值
	 * @params configScopetype 配置范围类型
	 * @params configScopeobj 配置范围对象
	 * @params configUnittype 配置数据类型
	 * @params configUnitobj 配置数据对象
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "list", needLogin = false)
	@MethodDesc(comment = "配置-分页", returns = @ReturnDesc(subs = {
		@Return2Desc(name="count",comment="数量",type=Integer.class),
		@Return2Desc(name="data",comment="数据",type=List.class),
		@Return2Desc(name = "data[n].configId", comment = "配置标识", type = String.class),
		@Return2Desc(name = "data[n].configType", comment = "配置类型", type = String.class),
		@Return2Desc(name = "data[n].configCode", comment = "配置编码", type = String.class),
		@Return2Desc(name = "data[n].configValue", comment = "配置数值", type = String.class),
		@Return2Desc(name = "data[n].configScopetype", comment = "配置范围类型", type = String.class),
		@Return2Desc(name = "data[n].configScopeobj", comment = "配置范围对象", type = String.class),
		@Return2Desc(name = "data[n].configUnittype", comment = "配置数据类型", type = String.class),
		@Return2Desc(name = "data[n].configUnitobj", comment = "配置数据对象", type = String.class),
	}))
	public Map list(
		@ParamDesc(comment="分页起始",validate=Validate.M_STARTINDEX,defVal = "0")	Integer pageIndex,
		@ParamDesc(comment="分页数量",validate=Validate.M_COUNT,defVal = "10")	Integer pageSize,
		@ParamDesc(comment="排序字段")	String orderKey,
		@ParamDesc(comment="排序字段",validate="R:[YN]",memo="[枚举]Y:正序;N:倒序")	String orderVal,
		@ParamDesc(comment="配置标识",validate=Validate.R_ID,memo="")	String configId,
		@ParamDesc(comment="配置类型",validate=Validate.M_SAFE,memo="")	String configType,
		@ParamDesc(comment="配置编码",validate=Validate.M_SAFE,memo="")	String configCode,
		@ParamDesc(comment="配置数值",validate=Validate.M_SAFE,memo="")	String configValue,
		@ParamDesc(comment="配置范围类型",validate=Validate.M_INRANGE+"[['G','R','U'] ,'$value']",memo="[枚举]G:全局;R:角色;U:用户")	String configScopetype,
		@ParamDesc(comment="配置范围对象",validate=Validate.M_SAFE,memo="")	String configScopeobj,
		@ParamDesc(comment="配置数据类型",validate=Validate.M_SAFE,memo="")	String configUnittype,
		@ParamDesc(comment="配置数据对象",validate=Validate.M_SAFE,memo="")	String configUnitobj,
		Map $params
	)throws Exception {
		Map where=MapUtils.toMap(new Object[][]{
			{"configId",configId},
			{"configType",configType},
			{"configCode",configCode},
			{"configValue",configValue},
			{"configScopetype",configScopetype},
			{"configScopeobj",configScopeobj},
			{"configUnittype",configUnittype},
			{"configUnitobj",configUnitobj},
		});	
		Map order=isEmpty(orderKey)?null:MapUtils.toMap(new Object[][]{
			{orderKey	,orderVal},
		});
		QueryResult qr=ucConfigService.queryMapsCount(where, null, order, pageIndex, pageSize);
		return BeanUtils.bean2Map(qr);
	}
	/**
	 * 保存多个config
	 * @param configs list集合
	 * @param $params
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "saves", needLogin = false)
	@MethodDesc(comment = "配置保存", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "configId", comment = "配置标识", type = String.class)
	}))
	public Map saves(
		@ParamDesc(comment="配置对象集合" ,unnull=true)	List<Map> configs,	
		Map $params
	)throws Exception {
		ArrayList<UcConfig> list = new ArrayList<>();
		for (Map map : configs) {
			UcConfig ucConfig = new UcConfig();
			BeanUtils.setBean(ucConfig, map);
			addAndEditBeforeValidate(ucConfig);
			list.add(ucConfig);
		}
		String ids=ucConfigService.saves(list);
		return MapUtils.toMap(new Object[][] { { "configIds", ids } });
	}
	
	
	@CoreRule(protocol = "find", needLogin = false)
	@MethodDesc(comment = "配置查询", returns = @ReturnDesc(subs = {
			@Return2Desc(name="data",comment="数据",type=List.class),
			@Return2Desc(name = "data[n].configId", comment = "配置标识", type = String.class),
			@Return2Desc(name = "data[n].configType", comment = "配置类型", type = String.class),
			@Return2Desc(name = "data[n].configCode", comment = "配置编码", type = String.class),
			@Return2Desc(name = "data[n].configValue", comment = "配置数值", type = String.class),
			@Return2Desc(name = "data[n].configScopetype", comment = "配置范围类型", type = String.class),
			@Return2Desc(name = "data[n].configScopeobj", comment = "配置范围对象", type = String.class),
			@Return2Desc(name = "data[n].configUnittype", comment = "配置数据类型", type = String.class),
			@Return2Desc(name = "data[n].configUnitobj", comment = "配置数据对象", type = String.class),
	}))
	public Map find(
		@ParamDesc(comment="配置类型",validate=Validate.M_SAFE,length = 100 ,unnull=true)	String configType,	
		@ParamDesc(comment="配置编码",validate=Validate.M_SAFE,length = 100 ,unnull=false)	String configCode,	
		@ParamDesc(comment="配置范围类型",validate=Validate.M_INRANGE+"[['G','R','U'] ,'$value']",length = 10 ,unnull=true)	String configScopetype,	
		@ParamDesc(comment="配置范围对象",validate=Validate.M_SAFE,length = 36 ,unnull=false)	String configScopeobj,	
		@ParamDesc(comment="查询类型",validate=Validate.M_SAFE,length = 36 ,unnull=true)	String queryType,	
		Map $params
	)throws Exception {
		List data=ucConfigService.find($params);
		return MapUtils.toMap(new Object[][] { { "datas", data } });
	}
	
	/**
	 * 批量保存配置信息
	 * @param configs list集合
	 * @param $params
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "batch/save", needLogin = false)
	@MethodDesc(comment = "配置批量保存", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "configId", comment = "配置标识", type = String.class)
	}))
	public void batch_save(
		@ParamDesc(comment="配置对象集合" ,unnull=true)	List<Map> configs,	
		Map $params
	)throws Exception {
		ArrayList<UcConfig> existConfigList = new ArrayList<>();
		ArrayList<UcConfig> notExistConfigList = new ArrayList<>();
		
		for (Map map : configs) {
			UcConfig ucConfig = new UcConfig();
			BeanUtils.setBean(ucConfig, map);
			addAndEditBeforeValidate(ucConfig);
			if(StringUtils.isEmpty(ucConfig.getConfigId())) {
				notExistConfigList.add(ucConfig);
			}else {
				existConfigList.add(ucConfig);
			}
		}
		ucConfigService.batch_save(existConfigList,notExistConfigList);
	}
	
}