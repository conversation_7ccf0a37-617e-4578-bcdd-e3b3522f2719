package com.niceloo.uc.handler;

import java.util.Map;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.bytecode.Cafebabe.Constant;
import org.nobject.common.code.describer.MethodDesc;
import org.nobject.common.code.describer.ParamDesc;
import org.nobject.common.code.describer.Return2Desc;
import org.nobject.common.code.describer.ReturnDesc;
import org.nobject.common.code.describer.Validate;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.log.Logger;

import com.niceloo.core.bean.CoreConst.ErrorCode;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.uc.common.UcConst.Errorcode;
import com.niceloo.uc.model.UcBrand;
import com.niceloo.uc.service.UcBrandService;

/**
 * UcBrandHandler
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@CoreRule(protocol="uc/brand")
public class UcBrandHandler {
	
	/** logger */
	private Logger logger=Logger.getLogger(UcBrandHandler.class);
	
	@Autowired
	private UcBrandService ucBrandService;
	
	/** 
	 * 品牌列表
	 */
	@CoreRule(protocol="list",needLogin=false)
	@MethodDesc(comment="品牌列表",returns=@ReturnDesc(subs={
			@Return2Desc(name="data"					 ,comment="数据"				,type=String.class),
			@Return2Desc(name="	data[n].brandId"		 ,comment="品牌标识"			,type=String.class),
			@Return2Desc(name="data[n].brandName"		 ,comment="品牌名称"			,type=String.class),
			@Return2Desc(name="data[n].brandAvlstatus"	 ,comment="品牌可用状态"		,type=String.class		,memo="枚举:[Y:可用;N:不可用]"),
			@Return2Desc(name="data[n].brandSeq"		 ,comment="品牌排序"		    ,type=int.class),
			@Return2Desc(name="data[n].brandCreater"     ,comment="品牌创建者"			,type=String.class),
			@Return2Desc(name="data[n].brandCreatedate"  ,comment="品牌创建日期"		,type=String.class),
			@Return2Desc(name="data[n].brandModifier"    ,comment="品牌修改者"			,type=String.class),
			@Return2Desc(name="data[n].brandModifieddate",comment="品牌修改日期"		,type=String.class),
			@Return2Desc(name="data[n].brandMemo"        ,comment="品牌描述"			,type=String.class),

	
	}))
	public Map list(
			@ParamDesc(comment="排序字段"		,validate=Validate.M_SAFE) 								String orderKey,
			@ParamDesc(comment="排序数值"		,validate="R:[YN]",memo="[枚举]Y:正序;N:倒序") 						String orderVal,
			@ParamDesc(comment="分页起始"		,validate=Validate.M_STARTINDEX	,defVal="0") 			Integer pageIndex,
			@ParamDesc(comment="分页数量"		,validate=Validate.M_COUNT		,defVal="10") 			Integer pageSize,
			@ParamDesc(comment="品牌名称"		,validate=Validate.M_SAFE) 								String brandName,
			@ParamDesc(comment="品牌创建者"		,validate=Validate.R_ID) 						String brandCreater,
			@ParamDesc(comment="品牌创建开始日期"		,validate=Validate.M_YMDHMS) 								String brandCreatedate_begin,
			@ParamDesc(comment="品牌创建结束日期"		,validate=Validate.M_YMDHMS) 						String brandCreatedate_end,
			@ParamDesc(comment="品牌修改者"		,validate=Validate.R_ID) 						String brandModifier,
			@ParamDesc(comment="品牌修改开始日期"		,validate=Validate.M_YMDHMS) 								String brandModifieddate_begin,
			@ParamDesc(comment="品牌修改结束日期"		,validate=Validate.M_YMDHMS) 						String brandModifieddate_end,
			@ParamDesc(comment="品牌可用状态"		,validate="R:[YN]",memo="枚举:[Y:可用;N:不可用]") 						String brandAvlstatus
			)throws Exception{
			Map params=MapUtils.toMap(new Object[][]{
				{"brandName"				,brandName},
				{"brandCreater"				,brandCreater},
				{"brandCreatedate_begin"	,brandCreatedate_begin},
				{"brandCreatedate_end"		,brandCreatedate_end},
				{"brandModifier"			,brandModifier},
				{"brandModifieddate_begin"	,brandModifieddate_begin},
				{"brandModifieddate_end"	,brandModifieddate_end},
				{"brandAvlstatus"			,brandAvlstatus},
			});
		
			QueryResult qr	=ucBrandService.queryBrandList(pageIndex, pageSize,params,orderKey,orderVal);
			return BeanUtils.bean2Map(qr);
		}
	
	/** 
	 * 品牌添加
	 * @return 
	 */
	@CoreRule(protocol="add",needLogin=false)
	@MethodDesc(comment="品牌添加",returns=@ReturnDesc(subs={
		
	}))
	
	public Map add(
			@ParamDesc(comment="品牌名称",validate=Validate.M_SAFE)String brandName,
			@ParamDesc(comment="品牌排序",validate=Validate.R_NUM)String brandSeq,
			@ParamDesc(comment="品牌描述",validate=Validate.M_SAFE)String brandMemo,
			@ParamDesc(comment="创建者标识",validate=Validate.R_ID)String brandCreater,
			@ParamDesc(comment="品牌可用状态",validate="R:[YN]",memo="[枚举]：Y:可用;N:不可用")String brandAvlstatus,
			Map $params
	)throws Exception{
		UcBrand brand = new UcBrand();
		BeanUtils.setBean(brand, $params);
		ucBrandService.add(brand);
		return MapUtils.toMap(new Object[][]{
			{"brandId",brand.getBrandId()}
		});
	}
	
	
	/** 
	 *品牌详情
	 */
	@CoreRule(protocol="info",needLogin=false)
	@MethodDesc(comment="品牌详情",returns=@ReturnDesc(subs={
			@Return2Desc(name="data"				     ,comment="数据"				,type=String.class),
			@Return2Desc(name="	data[n].brandName"		 ,comment="品牌名称"			,type=String.class),
			@Return2Desc(name="data[n].brandMemo"		 ,comment="品牌描述"			,type=String.class),
			@Return2Desc(name="data[n].brandCreater"	 ,comment="品牌创建者"		    ,type=String.class),
			@Return2Desc(name="data[n].brandCreatedate"  ,comment="品牌创建日期"		,type=String.class),
			@Return2Desc(name="data[n].brandModifier"	 ,comment="品牌修改者"			,type=String.class),
			@Return2Desc(name="data[n].brandModifieddate",comment="品牌修改日期"		,type=String.class),
			@Return2Desc(name="data[n].brandAvlstatus"   ,comment="品牌可用状态"		,type=String.class  	,memo="	[枚举]:Y:可用;N:不可用"),
			@Return2Desc(name="data[n].brandId" 		 ,comment="品牌标识"	   		,type=String.class),
			@Return2Desc(name="data[n].brandSeq	"        ,comment="品牌排序"			,type=int.class   ),
	}))
	public Map info(
			@ParamDesc(comment="品牌标识"		,validate=Validate.R_ID) 						String brandId
			)throws Exception{
		
		UcBrand brand = ucBrandService.queryBrandById(brandId);
			
			return BeanUtils.bean2Map(brand);
		}
	
	
	/** 
	 * 修改
	 */
	@CoreRule(protocol="edit",needLogin=false)
	@MethodDesc(comment="品牌修改",returns=@ReturnDesc(subs={
			
	}))
	public void edit(
			@ParamDesc(comment="品牌标识",validate=Validate.R_ID)String brandId,
			@ParamDesc(comment="品牌名称",validate=Validate.M_SAFE)String brandName,
			@ParamDesc(comment="品牌描述",validate=Validate.M_SAFE)String brandMemo,
			@ParamDesc(comment="品牌可用状态",validate="R:[YN]",memo="[枚举]Y:可用;N:不可用")String brandAvlstatus,
			@ParamDesc(comment="品牌排序",validate=Validate.R_NUM)String brandSeq,
			@ParamDesc(comment="修改者标识",validate=Validate.R_ID)String brandModifier,
			Map $params
	)throws Exception{
		
		UcBrand brand = ucBrandService.queryBrandById(brandId);
		
 		if(brand==null){
 			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"页面不存在",null,null);
 		}
 		
		BeanUtils.setBean(brand, MapUtils.removeEmpty($params));
 		
 		ucBrandService.edit(brand);
	}
	
	
	/** 
	 * delete
	 */
	@CoreRule(protocol="delete",needLogin=false)
	@MethodDesc(comment="删除品牌",returns=@ReturnDesc(subs={
  	}))
  	public void delete(
  		@ParamDesc(comment="品牌标识",validate=Validate.R_ID) String brandId,
  		Map $params
  	)throws Exception{
  		
		UcBrand brand = new UcBrand();
		brand.setBrandId(brandId);
		ucBrandService.del(brand);
  	}
	
}
