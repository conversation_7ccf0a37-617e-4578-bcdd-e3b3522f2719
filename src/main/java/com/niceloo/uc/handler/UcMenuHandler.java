package com.niceloo.uc.handler;

import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.bean.CoreConst.ErrorCode;
import com.niceloo.core.mvc.MVCUtils;
import com.niceloo.core.service.CoreCacheService;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.uc.model.UcDatapolicy;
import com.niceloo.uc.model.UcMenu;
import com.niceloo.uc.service.UcDatapolicyService;
import com.niceloo.uc.service.UcMenuService;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.buffer.ByteBuffer;
import org.nobject.common.code.describer.*;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.file.GZIPUtils;
import org.nobject.common.http.HttpClient;
import org.nobject.common.js.JSONObject;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;

/**
 * 菜单-业务处理类
 *
 * <AUTHOR>
 * @Date 2019-11-18 10:35:12
 */
@Service
@CoreRule(protocol = "uc/menu")
public class UcMenuHandler {

	@Autowired
	private UcMenuService ucMenuService;
	@Autowired
	private UcDatapolicyService ucDatapolicyService;
	@Autowired
	private CoreCacheService coreCacheService;

	/**
	 * 菜单-添加
	 *
	 * @param menuName          菜单名称
	 * @param menuType          菜单名称
	 * @param menuUrl           菜单地址
	 * @param menuCode          菜单编码
	 *                          //	 * @param menuLevelcode     菜单层级编码
	 * @param menuMemo          菜单备注
	 * @param menuSeq           菜单顺序
	 * @param menuDatapolicy    功能数据权限
	 * @param menuDisablestatus 菜单禁用状态
	 * @param menuIcon          菜单图标
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "add", auth = "uc/menu/add")
	@MethodDesc(comment = "添加菜单", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "menuId", comment = "menuId", type = String.class)
	}))
	public Map add(
			@ParamDesc(comment = "菜单名称", validate = Validate.M_SAFE, length = 50, unnull = true) String menuName,
			@ParamDesc(comment = "菜单类型", validate = "R:(desktop)|(app)|(model)|(page)|(funcGroup)|(func)", memo = "枚举 工作台:desktop 应用:app 模块:model 页面:page 功能组合:funcGroup 功能:func",
					length = 40, unnull = true) String menuType,
			@ParamDesc(comment = "菜单地址", validate = Validate.M_SAFE, length = 500) String menuUrl,
			@ParamDesc(comment = "上级菜单id", validate = Validate.R_ID, length = 50, unnull = true) String pid,
			@ParamDesc(comment = "权限编码", validate = Validate.M_SAFE, length = 100, memo = "唯一值，若为功能，需与protocol一致") String menuCode,
			@ParamDesc(comment = "菜单参数", validate = Validate.M_SAFE, length = 200) String menuParam,
			@ParamDesc(comment = "关联权限", validate = Validate.M_SAFE, length = 200) String menuExtcode,
			@ParamDesc(comment = "菜单备注", validate = Validate.M_SAFE, length = 500) String menuMemo,
			@ParamDesc(comment = "菜单顺序", validate = Validate.R_NUM, length = 5, defVal = "1", memo = "越小越靠前") Integer menuSeq,
			@ParamDesc(comment = "功能数据权限", validate = Validate.M_SAFE) List menuDatapolicy,
			@ParamDesc(comment = "菜单禁用状态", validate = "R:[ED]", length = 1, defVal = "E", memo = "枚举：E启用，D禁用") String menuDisablestatus,
			@ParamDesc(comment = "菜单图标", validate = Validate.M_SAFE, length = 200) String menuIcon,
			Map $params
	) throws Exception {
		UcMenu ucMenu = new UcMenu();
		$params.remove("menuDatapolicy");
		BeanUtils.setBean(ucMenu, $params);
		if (menuDatapolicy != null && menuDatapolicy.size() > 0) {
			String s = menuDatapolicy.toString();
			ucMenu.setMenuDatapolicy(s.substring(1, s.length() - 1).replaceAll("\\s", ""));
		}
		ucMenuService.save(ucMenu, pid);
		return MapUtils.toMap(new Object[][]{{"menuId", ucMenu.getMenuId()}});
	}

	/**
	 * 菜单-删除
	 *
	 * @param menuId menuId
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "delete", auth = "uc/menu/delete")
	@MethodDesc(comment = "删除菜单", returns = @ReturnDesc(subs = {}))
	public void delete(
			@ParamDesc(comment = "menuId", validate = Validate.R_ID, unnull = true, length = 32) String menuId,
			Map $params
	) throws Exception {
		UcMenu ucMenu = ucMenuService.findById(menuId);
		if (ucMenu != null) {
			ucMenuService.delete(ucMenu);
		}
	}

	/**
	 * 菜单-修改
	 *
	 * @param menuId            菜单标识
	 * @param menuName          菜单名称
	 * @param menuType          菜单名称
	 * @param menuUrl           菜单地址
	 * @param menuCode          菜单编码
	 * @param menuMemo          菜单备注
	 * @param menuSeq           菜单顺序
	 * @param menuDatapolicy    功能数据权限
	 * @param menuDisablestatus 菜单禁用状态
	 * @param menuIcon          菜单图标
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "edit", auth = "uc/menu/edit")
	@MethodDesc(comment = "编辑菜单", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "menuId", comment = "menuId", type = String.class)
	}))
	public void edit(
			@ParamDesc(comment = "菜单标识", validate = Validate.R_ID, length = 36, unnull = true) String menuId,
			@ParamDesc(comment = "菜单名称", validate = Validate.M_SAFE, length = 50, unnull = true) String menuName,
			@ParamDesc(comment = "菜单类型", validate = "R:(desktop)|(app)|(model)|(page)|(funcGroup)|(func)", memo = "枚举 工作台:desktop 应用:app 模块:model 页面:page 功能组合:funcGroup 功能:func",
					length = 40, unnull = true) String menuType,
			@ParamDesc(comment = "菜单地址", validate = Validate.M_SAFE, length = 500) String menuUrl,
			@ParamDesc(comment = "权限编码", validate = Validate.M_SAFE, length = 100, memo = "唯一值，若为功能，需与protocol一致") String menuCode,
			@ParamDesc(comment = "菜单参数", validate = Validate.M_SAFE, length = 200) String menuParam,
			@ParamDesc(comment = "关联权限", validate = Validate.M_SAFE, length = 200) String menuExtcode,
			@ParamDesc(comment = "菜单备注", validate = Validate.M_SAFE, length = 500) String menuMemo,
			@ParamDesc(comment = "菜单顺序", validate = Validate.R_NUM, defVal = "1", memo = "越小越靠前") Integer menuSeq,
			@ParamDesc(comment = "功能数据权限", validate = Validate.M_SAFE) List menuDatapolicy,
			@ParamDesc(comment = "菜单禁用状态", validate = "R:[ED]", length = 1, defVal = "E", memo = "枚举：E启用，D禁用") String menuDisablestatus,
			@ParamDesc(comment = "菜单图标", validate = Validate.M_SAFE, length = 200) String menuIcon,
			Map $params
	) throws Exception {
		UcMenu ucMenu = ucMenuService.findById(menuId);
		if (ucMenu == null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "菜单不存在", null, null);
		}
		ucMenu.setMenuName(menuName);
		ucMenu.setMenuType(menuType);
		ucMenu.setMenuUrl(menuUrl);
		ucMenu.setMenuCode(menuCode);
		ucMenu.setMenuMemo(menuMemo);
		ucMenu.setMenuSeq(menuSeq);
		ucMenu.setMenuExtcode(menuExtcode);
		ucMenu.setMenuParam(menuParam);
		if (menuDatapolicy != null && menuDatapolicy.size() > 0) {
			String s = menuDatapolicy.toString();
			ucMenu.setMenuDatapolicy(s.substring(1, s.length() - 1).replaceAll("\\s", ""));
		}else{
			ucMenu.setMenuDatapolicy(null);
		}
		ucMenu.setMenuDisablestatus(menuDisablestatus);
		ucMenu.setMenuIcon(menuIcon);
		ucMenuService.update(ucMenu);
	}

	@CoreRule(protocol = "move", auth = "uc/menu/edit")
	@MethodDesc(comment = "移动菜单", returns = @ReturnDesc(subs = {
	}))
	public void move(
			@ParamDesc(comment = "移动的菜单id", validate = Validate.R_ID, length = 36, unnull = true) String sourceId,
			@ParamDesc(comment = "目标菜单id", validate = Validate.R_ID, length = 36, unnull = true) String destId,
			@ParamDesc(comment = "前后", validate = "R:[PAI]", length = 1, unnull = true,memo = "枚举： P 前面 A 后面 I 移入") String mode,
			Map $params
	) throws Exception {
		ucMenuService.move(sourceId,destId,mode);
	}


	/**
	 * 菜单-详情
	 *
	 * @param menuId  menuId
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "info", needLogin = false)
	@MethodDesc(comment = "菜单详情", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "menuId", comment = "菜单标识", type = String.class),
			@Return2Desc(name = "menuName", comment = "菜单名称", type = String.class),
			@Return2Desc(name = "menuType", comment = "菜单类型", type = String.class, memo = "枚举 工作台:desktop 应用:app 模块:model 页面:page 功能组合:funcGroup 功能:func"),
			@Return2Desc(name = "menuUrl", comment = "菜单地址", type = String.class),
			@Return2Desc(name = "menuCode", comment = "权限编码", type = String.class, memo = "唯一值，若为功能，需与protocol一致"),
			@Return2Desc(name = "menuParam", comment = "菜单参数", type = String.class),
			@Return2Desc(name = "menuExtcode", comment = "关联权限", type = String.class),
			@Return2Desc(name = "menuLevelcode", comment = "菜单层级编码", type = String.class),
			@Return2Desc(name = "menuMemo", comment = "菜单备注", type = String.class),
			@Return2Desc(name = "menuSeq", comment = "菜单顺序", type = Integer.class, memo = "越小越靠前"),
			@Return2Desc(name = "menuDatapolicy", comment = "功能数据权限", type = List.class),
			@Return2Desc(name = "menuDisablestatus", comment = "菜单禁用状态", type = String.class, memo = "枚举：E启用，D禁用"),
			@Return2Desc(name = "menuIcon", comment = "菜单图标", type = String.class),
	}))
	public Map info(
			@ParamDesc(comment = "menuId", validate = Validate.R_ID, unnull = true, length = 32) String menuId,
			Map $params
	) throws Exception {
		UcMenu ucMenu = ucMenuService.findById(menuId);
		if (ucMenu == null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "菜单不存在", null, null);
		}
		//清除掉用户隐私信息
		Map rtn = BeanUtils.bean2Map(ucMenu);
		String menuDatapolicy = ucMenu.getMenuDatapolicy();
		if (!StringUtils.isEmpty(menuDatapolicy)) {
			rtn.put("menuDatapolicy", menuDatapolicy.split(","));
		}
		rtn.remove("__updateProps__");
		return rtn;
	}

	/**
	 * 字典树
	 */
	@CoreRule(protocol = "tree", needLogin = false)
	@MethodDesc(comment = "菜单树", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据集合", type = String.class),
			@Return2Desc(name = "data[n].menuId", comment = "菜单标识", type = String.class),
			@Return2Desc(name = "data[n].menuName", comment = "菜单名称", type = String.class),
			@Return2Desc(name = "data[n].menuType", comment = "菜单类型", type = String.class, memo = "枚举 工作台:desktop 应用:app 模块:model 页面:page 功能组合: funcGroup 功能:func"),
			@Return2Desc(name = "data[n].menuUrl", comment = "菜单地址", type = String.class),
			@Return2Desc(name = "data[n].menuCode", comment = "权限编码", type = String.class, memo = "唯一值，若为功能，需与protocol一致"),
			@Return2Desc(name = "data[n].menuParam", comment = "菜单参数", type = String.class),
			@Return2Desc(name = "data[n].menuExtcode", comment = "关联权限", type = String.class),
			@Return2Desc(name = "data[n].menuSeq", comment = "菜单顺序", type = Integer.class, memo = "越小越靠前"),
			@Return2Desc(name = "data[n].menuDisablestatus", comment = "菜单禁用状态", type = String.class, memo = "枚举：E启用，D禁用"),
			@Return2Desc(name = "data[n].menuIcon", comment = "菜单图标", type = String.class),
			@Return2Desc(name = "data[n].menuDatapolicy", comment = "功能数据权限", type = List.class),
			@Return2Desc(name = "data[n].children", comment = "子集合", type = List.class),
	}))
	public Map tree(
			@ParamDesc(comment = "是否包含功能", validate = "R:[YN]", defVal = "N", memo = "枚举:YN,默认N") String containsFunc,
			@ParamDesc(comment = "是否包含禁用的", validate = "R:[YN]", defVal = "N", memo = "枚举:YN,默认N") String containsDisabled,
			@ParamDesc(comment = "是否包含功能组合", validate = "R:[YN]", defVal = "Y", memo = "枚举:YN,默认Y") String containsFuncGruop
	) throws Exception {

		boolean containsF = "Y".equalsIgnoreCase(containsFunc);
		boolean containsD = "Y".equalsIgnoreCase(containsDisabled);
		boolean containsFG = "Y".equalsIgnoreCase(containsFuncGruop);
		List list = ucMenuService.queryMenuTree(containsF, containsD,null, containsFG);
		return MapUtils.toMap(new Object[][]{{"data", list}});
	}


	/**
	 * 字典树
	 */
	@CoreRule(protocol = "children", needLogin = false)
	@MethodDesc(comment = "子菜单列表", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据集合", type = String.class),
			@Return2Desc(name = "data[n].menuId", comment = "菜单标识", type = String.class),
			@Return2Desc(name = "data[n].menuName", comment = "菜单名称", type = String.class),
			@Return2Desc(name = "data[n].menuType", comment = "菜单类型", type = String.class, memo = "枚举 工作台:desktop 应用:app 模块:model 页面:page 功能组合:funcGroup 功能:func"),
			@Return2Desc(name = "data[n].menuUrl", comment = "菜单地址", type = String.class),
			@Return2Desc(name = "data[n].menuCode", comment = "菜单编码", type = String.class, memo = "唯一值，若为功能，需与protocol一致"),
			@Return2Desc(name = "data[n].menuParam", comment = "菜单参数", type = String.class),
			@Return2Desc(name = "data[n].menuSeq", comment = "菜单顺序", type = Integer.class, memo = "越小越靠前"),
			@Return2Desc(name = "data[n].menuDisablestatus", comment = "菜单禁用状态", type = String.class, memo = "枚举：E启用，D禁用"),
			@Return2Desc(name = "data[n].menuIcon", comment = "菜单图标", type = String.class),
			@Return2Desc(name = "data[n].menuDatapolicy", comment = "功能数据权限", type = List.class),

	}))
	public Map children(
			@ParamDesc(comment = "父节点id", validate = Validate.R_ID, unnull = true) String pid,
			@ParamDesc(comment = "名称", validate = Validate.M_SAFE, memo = "模糊查询") String menuName,
			Map $params
	) throws Exception {
		List<UcMenu> menus = ucMenuService.queryChildren(pid, menuName);
		List<Map> list = new LinkedList<>();
		for (UcMenu menu : menus) {
			Map map = BeanUtils.bean2Map(menu);
			map.remove("__updateProps__");
			if (!StringUtils.isEmpty(menu.getMenuDatapolicy())) {
				map.put("menuDatapolicy", menu.getMenuDatapolicy().split(","));
			}
			list.add(map);
		}
		return MapUtils.toMap(new Object[][]{{"data", list}});
	}

	/**
	 * 备份
	 */
	@CoreRule(protocol = "backup", auth = "uc/menu/backup")
	@MethodDesc(comment = "备份菜单", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "filePath", comment = "文件路径", type = String.class),
	}))
	public Map backup(
			@ParamDesc(comment = "父节点id", validate = Validate.R_ID, memo = "根节点pid传0", defVal = "0") String pid,
			Map $params
	) throws Exception {
		List list = ucMenuService.queryBackup(pid);
		if(list == null){
			throw new ApplicationException(ErrorCode.argument_invalide,"父节点不存在",null);
		}
		List<UcDatapolicy> ucDatapolicies = ucDatapolicyService.queryList(null, null);
		Map map = MapUtils.toMap(new Object[][]{{pid, list}, {"datapolicy", ucDatapolicies}});
		//转成json字符串
		String jsonString = JSONObject.toJSONObject(map).toString();
		byte[] compress = GZIPUtils.compress(jsonString.getBytes("UTF-8"));
		ByteArrayInputStream in = new ByteArrayInputStream(compress);
		Map upload = (Map) CoreUtils.upload(in, "menu.bak", null, "usercenter", "fileservice", true);
		in.close();
		return upload;
	}

	/**
	 * 还原
	 */
	@CoreRule(protocol = "rollback", auth = "uc/menu/rollback")
	@MethodDesc(comment = "还原菜单", returns = @ReturnDesc(subs = {
//			@Return2Desc(name = "cacheId", comment = "缓存id", type = String.class, memo = "确认还原时回传"),
			@Return2Desc(name = "add", comment = "新增菜单列表", type = List.class),
			@Return2Desc(name = "del", comment = "删除菜单列表", type = List.class),
			@Return2Desc(name = "update", comment = "更新菜单列表", type = List.class),
	}))
	public Map rollback(
			@ParamDesc(comment = "文件路径", validate = Validate.M_SAFE) String filePath
	) throws Exception {
		HttpClient client = new HttpClient();
		byte[] bytes;
		try{
			InputStream inputStream = client.getInputStream(MVCUtils.getProperty("fs.url") + "/api/file/download?path=" + filePath, 6000, 6000);
			ByteBuffer byteBuffer = new ByteBuffer(10240);
			while (true) {
				byte[] bytes1 = new byte[10240];
				int read = inputStream.read(bytes1);
				if (read == -1) {
					break;
				}
				byteBuffer.append(bytes1, 0, read);
			}
			bytes = byteBuffer.getBytes();
		}catch (Exception e){
			throw new ApplicationException(ErrorCode.execute_faild, "文件获取失败！", null, e);
		}
		String str;
		try{
			byte[] uncompress = GZIPUtils.uncompress(bytes);
			str = new String(uncompress,"UTF-8");
		}catch (Exception e){
			throw new ApplicationException(ErrorCode.execute_faild, "文件解析错误！", null, e);
		}

		Map map = MapUtils.toMap(str);
		Set set = map.keySet();
		List addList = new ArrayList();
		List delList = new ArrayList();
		List updateList = new ArrayList();
		List datapolicy = new ArrayList();
		for (Object o : set) {
			String key = (String) o;
			if (!key.equals("datapolicy")) {
				String pid = key;
				List data = (List) map.get(pid);
				Map menuIdMap = new HashMap();
				for (Object datum : data) {
					Map menuMap = (Map) datum;
					String menuId = (String) menuMap.get("menuId");
					menuIdMap.put(menuId, "");
					String createDate = (String) menuMap.get("menuCreatedate");
					String menuModifieddate = (String) menuMap.get("menuModifieddate");
					UcMenu menu = ucMenuService.findById(menuId);
					if (menu == null) {
						//新增
						menu = new UcMenu();
						BeanUtils.setBean(menu, menuMap);
						addList.add(menu);
					} else if (menu.getMenuCreatedate().equals(createDate) && menuModifieddate != null && !menuModifieddate.equals(menu.getMenuModifieddate())) {
						//修改
						menu = new UcMenu();
						BeanUtils.setBean(menu, menuMap);
						updateList.add(menu);
					} else if (!menu.getMenuCreatedate().equals(createDate)) {
						//冲突
						throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, menuId + "存在冲突", null);
					}
				}
				//删除
				List list = ucMenuService.queryBackup(pid);
				if(list != null && list.size()>0){
					for (Object o1 : list) {
						UcMenu menu = (UcMenu) o1;
						String menuId = menu.getMenuId();
						if (!menuIdMap.containsKey(menuId)) {
							delList.add(menu);
						}
					}
				}

			} else {
				datapolicy = (List) map.get("datapolicy");
			}
		}

		Map map1 = MapUtils.toMap(new Object[][]{
				{"add", addList},
				{"del", delList},
				{"update", updateList}
		});
		HashMap<Object, Object> result = new HashMap<>();
		result.put("menu", map1);
		result.put("datapolicy", datapolicy);
//		//缓存  5min
//		String cacheId = UUID.randomUUID().toString();
//		coreCacheService.put("mapForConfirm" + cacheId, result, 5 * 60 * 1000);
//		map1.put("cacheId", cacheId);

		// 暂不缓存，直接执行
		ucMenuService.executeRollback(result);
		return map1;
	}

//	/**
//	 * 确认还原
//	 *
//	 * @throws Exception
//	 */
//	@CoreRule(protocol = "confirmRollback", needLogin = false)
//	@MethodDesc(comment = "确认还原", returns = @ReturnDesc(subs = {}))
//	public void confirm(
//			@ParamDesc(comment = "缓存id", validate = Validate.M_SAFE) String cacheId
//	) throws Exception {
//		Map result = (Map) coreCacheService.get("mapForConfirm" + cacheId);
//		if (result != null) {
//			ucMenuService.executeRollback(result);
//		} else {
//			throw new Exception("没有找到还原文件，可能已过期");
//		}
//	}


	@CoreRule(protocol = "roles", auth = "uc/menu/roles")
	@MethodDesc(comment = "根据菜单查询所属角色", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据", type = List.class),
			@Return2Desc(name = "data[n].roleId", comment = "角色标识", type = String.class),
			@Return2Desc(name = "data[n].roleName", comment = "角色名称", type = String.class),
			@Return2Desc(name = "data[n].roleMemo", comment = "角色描述", type = String.class),
			@Return2Desc(name = "data[n].roleSeq", comment = "角色排序", type = Integer.class),
			@Return2Desc(name = "data[n].roleCreatedate", comment = "角色创建时间", type = String.class),
			@Return2Desc(name = "data[n].roleCreaterid", comment = "角色创建人id", type = String.class),
			@Return2Desc(name = "data[n].roleCreatername", comment = "角色创建人名称", type = String.class),
	}))
	public Map queryRolesByMenu(
			@ParamDesc(comment = "菜单id", validate = Validate.M_SAFE, memo = "菜单id") String menuId,
			@ParamDesc(comment = "角色名称", validate = Validate.M_SAFE, memo = "模糊查询") String roleName,
			Map $params
	) throws Exception {

		List<Map> list = ucMenuService.queryRolesByMenu(menuId,roleName);
		return MapUtils.toMap(new Object[][]{
				{"data", list}
		});
	}
}

