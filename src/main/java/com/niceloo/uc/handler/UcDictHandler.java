package com.niceloo.uc.handler;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.MethodDesc;
import org.nobject.common.code.describer.ParamDesc;
import org.nobject.common.code.describer.Return2Desc;
import org.nobject.common.code.describer.ReturnDesc;
import org.nobject.common.code.describer.Validate;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;
import org.nobject.common.log.Logger;

import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.uc.common.UcConst.Errorcode;
import com.niceloo.uc.model.UcDict;
import com.niceloo.uc.service.UcDictService;
import com.niceloo.uc.utils.DictUtils;

/**
 * UcDictHandler
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@CoreRule(protocol="uc/dict")
public class UcDictHandler {
	/** logger */
	private Logger logger=Logger.getLogger(UcDictHandler.class);
	
	@Autowired
	private UcDictService ucDictService;
	
	/** 
	 * 字典树
	 */
	@CoreRule(protocol="tree",needLogin=false)
	@MethodDesc(comment="字典树",returns=@ReturnDesc(subs={
		@Return2Desc(name="data"					 ,comment="数据集合"			,type=String.class),
		@Return2Desc(name="data[n].dictId"		 	 ,comment="字典标识"			,type=String.class),
		@Return2Desc(name="data[n].dictName"		 ,comment="字典名称"			,type=String.class),
		@Return2Desc(name="data[n].dictLevelcode"	 ,comment="字典层级编码"		,type=String.class	),
		@Return2Desc(name="data[n].dictSeq"		     ,comment="字典顺序"		    ,type=int.class),
		@Return2Desc(name="data[n].dictVal"	 		 ,comment="字典数值"			,type=String.class	),
		@Return2Desc(name="data[n].dictMemo"		 ,comment="字典描述"		    ,type=int.class),
		@Return2Desc(name="data[n].children"         ,comment="子集合"			,type=String.class),
	}))
	public Map tree(
		@ParamDesc(comment="字典类型"		,validate=Validate.M_SAFE) 								String dicttype
		)throws Exception{
		
	 	Map where=MapUtils.toMap(new Object[][]{
			{"dicttype"				,dicttype},
		});
	
		return ucDictService.queryDictTree(where);
	}
	
	
	/** 
	 * 字典类型
	 */
	@CoreRule(protocol="type",needLogin=false)
	@MethodDesc(comment="字典类型",returns=@ReturnDesc(subs={
		@Return2Desc(name="data"					 ,comment="数据集合"			,type=String.class),
		@Return2Desc(name="data[n].dictType"		 	 ,comment="字典类型"			,type=String.class),
	}))
	public Map type()throws Exception{
		QueryResult qr =  ucDictService.queryDictType();      
		return BeanUtils.bean2Map(qr);
	}
	
	
	/** 
	 * 修改
	 */
	@CoreRule(protocol="edit",needLogin=false)
	@MethodDesc(comment="字典修改",returns=@ReturnDesc(subs={
	}))
	public void edit(
		@ParamDesc(comment="字典标识",validate=Validate.R_ID)String dictId,
		@ParamDesc(comment="字典名称",validate=Validate.M_SAFE)String 	dictName,
		@ParamDesc(comment="字典数值",validate=Validate.M_SAFE)String dictVal,
		@ParamDesc(comment="字典描述",validate=Validate.M_SAFE)String 	dictMemo,
		@ParamDesc(comment="字典编码",validate=Validate.M_SAFE)String 	dictCode,
		@ParamDesc(comment="字典类型",validate=Validate.M_SAFE)String dicttype,
		Map $params
	)throws Exception{
		
		UcDict dict = ucDictService.queryDictById(dictId);
		
 		if(dict==null){
 			throw new ApplicationException(Errorcode.USER_ARGUMENT_INVALIDE,"菜单不存在",null,null);
 		}
 		
 		BeanUtils.setBean(dict, MapUtils.removeEmpty($params));
 		
 		ucDictService.edit(dict);
	}
	
	

	/** 
	 * 字典添加
	 * @return 
	 */
	@CoreRule(protocol="add",needLogin=false)
	@MethodDesc(comment="字典添加",returns=@ReturnDesc(subs={
	}))
	public Map add(
		@ParamDesc(comment="父级级层编码",validate=Validate.M_SAFE)String pId,
		@ParamDesc(comment="字典名称",validate=Validate.M_SAFE)String 	dictName,
		@ParamDesc(comment="字典数值",validate=Validate.M_SAFE)String dictVal,
		@ParamDesc(comment="字典描述",validate=Validate.M_SAFE)String 	dictMemo,
		@ParamDesc(comment="字典编码",validate=Validate.M_SAFE)String 	dictCode,
		@ParamDesc(comment="字典类型",validate=Validate.M_SAFE)String dicttype,
		Map $params
	)throws Exception{
		UcDict dict = new UcDict();
		BeanUtils.setBean(dict, $params);
		ucDictService.add(dict,pId);
		return MapUtils.toMap(new Object[][]{
			{"dictId",dict.getDictId()}
		});
	}
	
	/** 
	 * delete
	 */
	@CoreRule(protocol="delete",needLogin=false)
	@MethodDesc(comment="字典移除",returns=@ReturnDesc(subs={
  	}))
  	public void delete(
  		@ParamDesc(comment="字典标识",validate=Validate.R_ID) String dictId,
  		Map $params
  	)throws Exception{
		ucDictService.del(dictId);
  	}
	
	@CoreRule(protocol="rank",needLogin=false)
	@MethodDesc(comment = "字典层级",returns=@ReturnDesc(subs={
	}))
	public Map rank(
	@ParamDesc(comment="父级级层编码",	validate=Validate.R_ID)		String 		pId, //如果 Pid和 dictId都不传入的话默认查询第一层的
	@ParamDesc(comment="字典标识",	validate=Validate.R_ID) 	String 		dictId,
	@ParamDesc(comment="层数",		validate=Validate.R_NUM)	int 		rank,//不包含本层的几层
	@ParamDesc(comment="是否是树结构",	validate=Validate.M_SAFE) 	boolean 	isTree,
	@ParamDesc(comment="字典类型",validate=Validate.M_SAFE)String dicttype,
	Map $params
	) throws Exception{
		return ucDictService.rank($params);
	}
	
	/** 
	 * 字典列表模糊查询
	 */
	@CoreRule(protocol="list/seem",needLogin=false)
	@MethodDesc(comment="字典列表模糊查询",returns=@ReturnDesc(subs={
		@Return2Desc(name="data"					 ,comment="数据集合"			,type=String.class),
		@Return2Desc(name="data[n].dictName"		 ,comment="字典名称"			,type=String.class),
		@Return2Desc(name="data[n].dictCode"	 	 ,comment="字典编码"			,type=String.class),
	}))
	public Map list_like(
		@ParamDesc(comment="字典类型"		,validate=Validate.M_SAFE,unnull = true) 				String dictType,
		@ParamDesc(comment="字典名称"		,validate=Validate.M_SAFE) 								String dictName
		)throws Exception{
		String[] split = dictType.split(",");
		if(split.length == 1) {
			ArrayList<Map> likeDictsByName = DictUtils.likeDictsByName(dictType, dictName);
			return MapUtils.toMap(new Object[][] {
				{"data",likeDictsByName}
			});
		}else {
			HashMap<Object,Object> resoutMap = new HashMap<>();
			for (String string : split) {
				List dicts = DictUtils.getDicts(string);
				if(dicts == null) {
					dicts = new ArrayList<>();
				}
				resoutMap.put(string, dicts);
			}
			return resoutMap;
		}
	}
	
	/**
	 * 获取字典树
	 * @param dictCode  字典标识
	 * @param totalRank 
	 * @param $params
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol="area/tree",needLogin=false)
	@MethodDesc(comment = "地区树",returns=@ReturnDesc(subs={
	}))
	public Map area_tree(
	@ParamDesc(comment="地区编码",		validate=Validate.R_ID) 	String 		areaCode,
	@ParamDesc(comment="层数",			validate=Validate.R_NUM, 	memo="不包含本层的层数")	int 		rank,
	@ParamDesc(comment="需要市辖区",		validate=Validate.M_SAFE, 	memo="是否需要市辖区")		String 		needArea,
	Map $params
	) throws Exception{
		return MapUtils.toMap(new Object[][] {
			{"data",ucDictService.areaTree(areaCode,rank,needArea)}
		});
	}
	
	@CoreRule(protocol="area/list",needLogin=false)
	@MethodDesc(comment = "地区列表",returns=@ReturnDesc(subs={
			@Return2Desc(name="data"					 ,comment="数据集合"			,type=String.class),
			@Return2Desc(name="data[n].dictId"		 	 ,comment="字典标识"			,type=String.class),
			@Return2Desc(name="data[n].dictName"		 ,comment="字典名称"			,type=String.class),
			@Return2Desc(name="data[n].dictLevelcode"	 ,comment="字典层级编码"		,type=String.class),
			@Return2Desc(name="data[n].dictSeq"		     ,comment="字典顺序"		    ,type=int.class),
			@Return2Desc(name="data[n].dictVal"	 		 ,comment="字典数值"			,type=String.class),
			@Return2Desc(name="data[n].dictMemo"		 ,comment="字典描述"		    ,type=int.class),
			@Return2Desc(name="data[n].dictCode"         ,comment="字典编码"			,type=String.class),
	}))
	public Map area_list(
			@ParamDesc(comment="字典编码",		validate=Validate.M_SAFE,unnull = true) 		List 		dictCodes,
			@ParamDesc(comment="字典类型",		validate=Validate.M_SAFE, unnull = true) 	    String 		dictType
			) throws Exception {
		
		return MapUtils.toMap(new Object[][] {
			{"data",DictUtils.getDictsByCodes(dictCodes, dictType, false)}
		});
	}
	
	/**
	 * 获取字典树
	 * @param dictCode  字典标识
	 * @param totalRank 
	 * @param $params
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol="area/analyze",needLogin=false)
	@MethodDesc(comment = "地区解码",returns=@ReturnDesc(subs={
			@Return2Desc(name="areaName"					 ,comment="地区名称"			,type=String.class),
	}))
	public Map area_analyze(
	@ParamDesc(comment="地区编码",		validate=Validate.R_ID) 	String 		areaCode,
	Map $params
	) throws Exception{
		return MapUtils.toMap(new Object[][] {
			{"areaName",DictUtils.analyzeCode(areaCode)}
		});
	}
	
	/**
	 * 获取意向
	 * @return
	 * @throws Exception 
	 */
	@CoreRule(protocol="intention/list",needLogin=false)
	@MethodDesc(comment = "获取意向集合",returns=@ReturnDesc(subs={
			@Return2Desc(name="areaName"					 ,comment="地区名称"			,type=String.class),
	}))
	public Map intention_list(
			@ParamDesc(comment="意向类型",		validate=Validate.M_SAFE ,memo = "C:客户营销;S:学员服务") 	String 		type,
			Map $params
			) throws Exception {
		if("C".equals(type)) {
			return getYx("CTYX");
		}else if("S".equals(type)) {
			return getYx("SSYX");
		}else {
			return getYx("");
		}
	}
	
	/**
	 * 获取意向
	 * @param code
	 * @return
	 * @throws Exception
	 */
	public Map getYx(String code) throws Exception {
		List<Object> resoutList = new ArrayList<>();
		List<Map> dicts = DictUtils.getDicts("intention");
		for (Map map : dicts) {
			String dictCode = (String) map.get("dictCode");
			List list=(List)map.get("children");
			if(list != null && list.size() > 0) {
				if(StringUtils.isEmpty(code)) {
					resoutList.addAll(list);
				}else if(code.equals(dictCode)) {
					resoutList.addAll(list);
					break;
				}
			}
		}
		return MapUtils.toMap(new Object[][] {
			{"data",resoutList}
		});
	}
	
	/** 
	 * 字典列表模糊查询
	 */
	@CoreRule(protocol="list/dictIds",needLogin=false)
	@MethodDesc(comment="根据字典标识集合查询字典",returns=@ReturnDesc(subs={
		@Return2Desc(name="data"					 ,comment="数据集合"			,type=String.class),
		@Return2Desc(name="data[n].dictId"		 	 ,comment="字典标识"			,type=String.class),
		@Return2Desc(name="data[n].dictName"		 ,comment="字典名称"			,type=String.class),
		@Return2Desc(name="data[n].dictLevelcode"	 ,comment="字典层级编码"		,type=String.class),
		@Return2Desc(name="data[n].dictSeq"		     ,comment="字典顺序"		    ,type=int.class),
		@Return2Desc(name="data[n].dictVal"	 		 ,comment="字典数值"			,type=String.class),
		@Return2Desc(name="data[n].dictMemo"		 ,comment="字典描述"		    ,type=int.class),
		@Return2Desc(name="data[n].dictCode"         ,comment="字典编码"			,type=String.class),
	}))
	public Map list_ids(
		@ParamDesc(comment="字典标识集合"		,validate=Validate.M_SAFE) 								List<String> dictIds
		)throws Exception{
		List dictList = ucDictService.queryListIds(dictIds);
		return MapUtils.toMap(new Object[][] {
			{"data",dictList}
		});
	}
	
}
