package com.niceloo.uc.handler;

import com.niceloo.core.bean.CoreConst.ErrorCode;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.uc.model.UcLoginip;
import com.niceloo.uc.service.UcLoginipService;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.*;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;

import java.util.List;
import java.util.Map;

import static org.nobject.common.lang.StringUtils.isEmpty;

/**
 * ip名单-业务处理类
 *
 * <AUTHOR>
 * @Date 2020-03-14 14:03:54
 */
@Service
@CoreRule(protocol = "uc/loginip")
public class UcLoginipHandler {

    @Autowired
    private UcLoginipService ucLoginipService;

    /**
     * ip名单-添加
     *
     * @param loginip          ip
     * @param loginipType      ip类型
     * @param loginipCreator   创建人
     * @param loginipChecktype 校验方式
     * @param loginipMemo      备注
     * @return
     * @throws Exception
     */
    @CoreRule(protocol = "add", needLogin = false)
    @MethodDesc(comment = "ip名单-添加", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "loginipId", comment = "loginipId", type = String.class)
    }))
    public Map add(
            @ParamDesc(comment = "ip", validate = Validate.M_SAFE, length = 50, unnull = true) String loginip,
            @ParamDesc(comment = "ip类型", validate = "R:[WB]", length = 1, memo = "[枚举]W:白名单;B:黑名单", defVal = "W") String loginipType,
            @ParamDesc(comment = "创建人", validate = Validate.M_SAFE, length = 36) String loginipCreator,
            @ParamDesc(comment = "ip校验方式", validate = "R:[PT]", length = 1, memo = "[枚举]P:永久;T:临时", unnull = true) String loginipChecktype,
            @ParamDesc(comment = "备注", validate = Validate.M_SAFE) String loginipMemo,
            @ParamDesc(comment = "创建人姓名", validate = Validate.M_SAFE) String loginipCreatorname,
            Map $params
    ) throws Exception {
        if (ucLoginipService.isExist(loginip, !"P".equals(loginipChecktype), null)) {
            throw new ApplicationException(ErrorCode.argument_invalide, "ip/ip范围已存在", null, null);
        }
        UcLoginip ucLoginip = new UcLoginip();
        BeanUtils.setBean(ucLoginip, $params);
        ucLoginipService.save(ucLoginip);
        return MapUtils.toMap(new Object[][]{{"loginipId", ucLoginip.getLoginipId()}});
    }

    /**
     * ip名单-删除
     *
     * @param loginipId loginipId
     * @return
     * @throws Exception
     */
    @CoreRule(protocol = "delete", needLogin = false)
    @MethodDesc(comment = "ip名单-删除", returns = @ReturnDesc(subs = {}))
    public void delete(
            @ParamDesc(comment = "loginipId", validate = Validate.R_ID, unnull = true, length = 32) String loginipId,
            Map $params
    ) throws Exception {
        UcLoginip ucLoginip = ucLoginipService.findById(loginipId);
        if (ucLoginip != null) {
            ucLoginipService.delete(ucLoginip);
        }
    }

    /**
     * ip名单-修改
     *
     * @param loginipId        ip名单标识
     * @param loginip          ip
     * @param loginipChecktype 校验方式
     * @param loginipMemo      备注
     * @return
     * @throws Exception
     */
    @CoreRule(protocol = "edit", needLogin = false)
    @MethodDesc(comment = "ip名单-编辑", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "loginipId", comment = "loginipId", type = String.class)
    }))
    public void edit(
            @ParamDesc(comment = "ip名单标识", validate = Validate.R_ID, length = 36, unnull = true) String loginipId,
            @ParamDesc(comment = "ip", validate = Validate.M_SAFE, length = 50) String loginip,
            //@ParamDesc(comment = "ip类型", validate = "R:[WB]", length = 1, memo = "[枚举]W:白名单;B:黑名单", unnull = true) String loginipType,
            @ParamDesc(comment = "校验方式", validate = "R:[PT]", length = 1, unnull = true, memo = "[枚举]P:永久;T:临时") String loginipChecktype,
            @ParamDesc(comment = "备注", validate = Validate.M_SAFE) String loginipMemo,
            Map $params
    ) throws Exception {
        UcLoginip ucLoginip = ucLoginipService.findById(loginipId);
        if (ucLoginip == null) {
            throw new ApplicationException(ErrorCode.argument_invalide, "ip名单不存在", null, null);
        }
        if (!StringUtils.isEmpty(loginip) && !loginip.equals(ucLoginip.getLoginip())) {
            Boolean exist = ucLoginipService.isExist(loginip, !"P".equals(loginipChecktype), loginipId);
            if (exist) {
                throw new ApplicationException(ErrorCode.argument_invalide, "ip已经存在", null, null);
            }
        }
        BeanUtils.setBean(ucLoginip, $params);
        ucLoginipService.update(ucLoginip);
    }

    /**
     * ip名单-详情
     *
     * @param loginipId loginipId
     * @param $params   附加参数
     * @return
     * @throws Exception
     */
    @CoreRule(protocol = "info", needLogin = false)
    @MethodDesc(comment = "ip名单-详情", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "loginipId", comment = "ip名单标识", type = String.class),
            @Return2Desc(name = "loginip", comment = "ip", type = String.class),
            @Return2Desc(name = "loginipType", comment = "ip类型", memo = "[枚举]W:白名单;B:黑名单", type = String.class),
            @Return2Desc(name = "loginipCreator", comment = "创建人", type = String.class),
            @Return2Desc(name = "loginipCreateddate", comment = "创建时间", type = String.class),
            @Return2Desc(name = "loginipChecktype", comment = "校验方式", memo = "[枚举]P:永久;T:临时", type = String.class),
            @Return2Desc(name = "loginipAccuracy", comment = "精确度", memo = "[枚举]A:精确;R:范围", type = String.class),
            @Return2Desc(name = "loginipMemo", comment = "备注", type = String.class),
            @Return2Desc(name = "loginipCreatorname", comment = "创建人姓名", type = String.class),
    }))
    public Map info(
            @ParamDesc(comment = "loginipId", validate = Validate.R_ID, unnull = true, length = 32) String loginipId,
            Map $params
    ) throws Exception {
        UcLoginip ucLoginip = ucLoginipService.findById(loginipId);
        if (ucLoginip == null) {
            throw new ApplicationException(ErrorCode.argument_invalide, "ip名单不存在", null, null);
        }
        //清除掉用户隐私信息
        Map rtn = BeanUtils.bean2Map(ucLoginip);
        return rtn;
    }


    /**
     * ip名单-分页
     *
     * @param pageIndex 分页起始
     * @param pageSize  分页数量
     * @param $params   附加参数
     * @return
     * @throws Exception
     * @params orderKey 排序字段
     * @params orderVal 排序字段
     * @params loginipId ip名单标识
     * @params loginip ip
     * @params loginipType ip类型
     * @params loginipCreator 创建人
     * @params loginipCreateddate 创建时间
     * @params loginipChecktype 校验方式
     * @params loginipMemo 备注
     */
    @CoreRule(protocol = "list", needLogin = false)
    @MethodDesc(comment = "ip名单-分页", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "count", comment = "数量", type = Integer.class),
            @Return2Desc(name = "data", comment = "数据", type = List.class),
            @Return2Desc(name = "data[n].loginipId", comment = "ip名单标识", type = String.class),
            @Return2Desc(name = "data[n].loginip", comment = "ip", type = String.class),
            @Return2Desc(name = "data[n].loginipType", comment = "ip类型", memo = "[枚举]W:白名单;B:黑名单", type = String.class),
            @Return2Desc(name = "data[n].loginipCreator", comment = "创建人", type = String.class),
            @Return2Desc(name = "data[n].loginipCreateddate", comment = "创建时间", type = String.class),
            @Return2Desc(name = "data[n].loginipChecktype", comment = "校验方式", memo = "[枚举]P:永久;T:临时", type = String.class),
            @Return2Desc(name = "data[n].loginipMemo", comment = "备注", type = String.class),
            @Return2Desc(name = "data[n].loginipAccuracy", comment = "精确度", memo = "[枚举]A:精确;R:范围", type = String.class),
    }))
    public Map list(
            @ParamDesc(comment = "分页起始", validate = Validate.M_STARTINDEX, defVal = "0") Integer pageIndex,
            @ParamDesc(comment = "分页数量", validate = Validate.M_COUNT, defVal = "10") Integer pageSize,
            @ParamDesc(comment = "排序字段") String orderKey,
            @ParamDesc(comment = "排序字段", validate = "R:[YN]", memo = "[枚举]Y:正序;N:倒序") String orderVal,
            @ParamDesc(comment = "ip", validate = Validate.M_SAFE) String loginip,
            @ParamDesc(comment = "ip类型", validate = "R:[WB]", memo = "[枚举]W:白名单;B:黑名单", defVal = "W") String loginipType,
            @ParamDesc(comment = "创建人", validate = Validate.M_SAFE) String loginipCreator,
            @ParamDesc(comment = "创建时间(开始)", validate = Validate.M_SAFE) String startDate,
            @ParamDesc(comment = "创建时间(结束)", validate = Validate.M_SAFE) String endDate,
            @ParamDesc(comment = "创建人姓名", validate = Validate.M_SAFE) String loginipCreatorname,
            @ParamDesc(comment = "校验方式", validate = "R:[PT]", memo = "[枚举]P:永久;T:临时") String loginipChecktype,
            @ParamDesc(comment = "精确度", validate = "R:[AR]", memo = "[枚举]A:精确;R:范围") String loginipAccuracy,
            Map $params
    ) throws Exception {
        Map where = MapUtils.toMap(new Object[][]{
                {"loginip", loginip},
                {"loginipType", loginipType},
                {"loginipCreator", loginipCreator},
                {"startDate", startDate},
                {"endDate", endDate},
                {"loginipChecktype", loginipChecktype},
                {"loginipAccuracy", loginipAccuracy},
        });
        if (!isEmpty(loginipCreatorname)) {
            where.put("loginipCreatorname", loginipCreatorname);
        }
        Map order = isEmpty(orderKey) ? null : MapUtils.toMap(new Object[][]{
                {orderKey, orderVal},
        });
        QueryResult qr = ucLoginipService.queryMapsCount(where, null, order, pageIndex, pageSize);
        return BeanUtils.bean2Map(qr);
    }
}