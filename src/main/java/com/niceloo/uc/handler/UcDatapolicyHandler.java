package com.niceloo.uc.handler;

import com.niceloo.core.bean.CoreConst.ErrorCode;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.uc.model.UcDatapolicy;
import com.niceloo.uc.service.UcDatapolicyService;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.*;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;

import java.util.*;

/**
 * 资源策略-业务处理类
 *
 * <AUTHOR>
 * @Date 2019-11-18 15:26:54
 */
@Service
@CoreRule(protocol = "uc/datapolicy")
public class UcDatapolicyHandler {

	@Autowired
	private UcDatapolicyService ucDatapolicyService;

	/**
	 * 资源策略-添加
	 * @param datapolicyCode 资源策略名称
	 * @param datapolicyName 资源策略类型
	 * @param datapolicyGroup 资源策略分组
	 * @param datapolicySeq 资源策略排序
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "add", needLogin = false)
	@MethodDesc(comment = "资源策略-添加", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "datapolicyId", comment = "datapolicyId", type = String.class)
	}))
	public Map add(
			@ParamDesc(comment="资源策略code",validate=Validate.M_SAFE,length = 40 ,unnull=true)	String datapolicyCode,
			@ParamDesc(comment="资源策略名称",validate=Validate.M_SAFE,length = 50 ,unnull=true)	String datapolicyName,
			@ParamDesc(comment="资源策略分组",validate=Validate.M_SAFE,length = 50 ,defVal = "通用")	String datapolicyGroup,
			@ParamDesc(comment="资源策略排序",validate=Validate.M_SAFE,length = 50 ,defVal = "0")	Integer datapolicySeq,
			Map $params
	)throws Exception {
		UcDatapolicy ucDatapolicy = new UcDatapolicy();
		BeanUtils.setBean(ucDatapolicy, $params);
		ucDatapolicyService.save(ucDatapolicy);
		return MapUtils.toMap(new Object[][] { { "datapolicyId", ucDatapolicy.getDatapolicyId() } });
	}

	/**
	 * 资源策略-删除
	 * @param datapolicyId datapolicyId
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "delete", needLogin = false)
	@MethodDesc(comment = "资源策略-删除", returns = @ReturnDesc(subs = {}))
	public void delete(
			@ParamDesc(comment="datapolicyId",validate=Validate.R_ID,unnull = true, length = 32)	String datapolicyId,
			Map $params
	)throws Exception {
		UcDatapolicy ucDatapolicy = ucDatapolicyService.findById(datapolicyId);
		if(ucDatapolicy != null){
			ucDatapolicyService.delete(ucDatapolicy);
		}
	}

	/**
	 * 资源策略-修改
	 * @param datapolicyId 资源策略标识
	 * @param datapolicyCode 资源策略名称
	 * @param datapolicyName 资源策略类型
	 * @param datapolicyGroup 资源策略分组
	 * @param datapolicySeq 资源策略排序
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "edit", needLogin = false)
	@MethodDesc(comment = "编辑资源策略", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "datapolicyId", comment = "datapolicyId", type = String.class)
	}))
	public void edit(
			@ParamDesc(comment="资源策略标识",validate=Validate.R_ID,length = 36 ,unnull=true)	String datapolicyId,
			@ParamDesc(comment="资源策略code",validate=Validate.M_SAFE,length = 40 ,unnull=true)	String datapolicyCode,
			@ParamDesc(comment="资源策略名称",validate=Validate.M_SAFE,length = 50 ,unnull=true)	String datapolicyName,
			@ParamDesc(comment="资源策略分组",validate=Validate.M_SAFE,length = 50 ,defVal = "通用")	String datapolicyGroup,
			@ParamDesc(comment="资源策略排序",validate=Validate.M_SAFE,length = 50 ,defVal = "0")	Integer datapolicySeq,
			Map $params
	)throws Exception {
		UcDatapolicy ucDatapolicy = ucDatapolicyService.findById(datapolicyId);
		if (ucDatapolicy == null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "资源策略不存在", null, null);
		}
		ucDatapolicy.setDatapolicyCode(datapolicyCode);
		ucDatapolicy.setDatapolicyName(datapolicyName);
		ucDatapolicy.setDatapolicyGroup(datapolicyGroup);
		ucDatapolicy.setDatapolicySeq(datapolicySeq);
		ucDatapolicyService.update(ucDatapolicy);
	}

	/**
	 * 资源策略-详情
	 * @param datapolicyId datapolicyId
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "info", needLogin = false)
	@MethodDesc(comment = "资源策略详情", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "datapolicyId", comment = "资源策略标识", type = String.class),
			@Return2Desc(name = "datapolicyCode", comment = "资源策略code", type = String.class),
			@Return2Desc(name = "datapolicyName", comment = "资源策略名称", type = String.class),
			@Return2Desc(name = "datapolicyGroup", comment = "资源策略分组", type = String.class),
			@Return2Desc(name = "datapolicySeq", comment = "资源策略排序", type = Integer.class),
	}))
	public Map info(
			@ParamDesc(comment="datapolicyId",validate=Validate.R_ID,unnull=true,length=32)	String datapolicyId,
			Map $params
	)throws Exception {
		UcDatapolicy ucDatapolicy = ucDatapolicyService.findById(datapolicyId);
		if (ucDatapolicy == null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "资源策略不存在", null, null);
		}
		//清除掉用户隐私信息
		Map rtn=BeanUtils.bean2Map(ucDatapolicy);
		rtn.remove("__updateProps__");
		return rtn;
	}

	/**
	 * 资源策略-分页
	 *
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 * @params datapolicyId 资源策略标识
	 * @params datapolicyCode 资源策略code
	 * @params datapolicyName 资源策略名称
	 */
	@CoreRule(protocol = "list", needLogin = false)
	@MethodDesc(comment = "资源策略列表(维护用)", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据", type = List.class),
			@Return2Desc(name = "data[n].datapolicyId", comment = "资源策略标识", type = String.class),
			@Return2Desc(name = "data[n].datapolicyCode", comment = "资源策略code", type = String.class),
			@Return2Desc(name = "data[n].datapolicyName", comment = "资源策略名称", type = String.class),
			@Return2Desc(name = "data[n].datapolicyGroup", comment = "资源策略分组", type = String.class),
			@Return2Desc(name = "data[n].datapolicySeq", comment = "资源策略排序", type = Integer.class),

	}))
	public Map list(
			@ParamDesc(comment = "资源策略名称", validate = Validate.M_SAFE) String datapolicyName,
			@ParamDesc(comment = "资源策略分组", validate = Validate.M_SAFE) String datapolicyGroup,
			Map $params
	) throws Exception {
		List<UcDatapolicy> list = ucDatapolicyService.queryList(datapolicyName,datapolicyGroup);
		List<Map> mapList= new LinkedList<>();
		for (UcDatapolicy p : list) {
			Map map = new HashMap();
			map.put("datapolicyId",p.getDatapolicyId());
			map.put("datapolicyCode",p.getDatapolicyCode());
			map.put("datapolicyName",p.getDatapolicyName());
			map.put("datapolicyGroup",p.getDatapolicyGroup());
			map.put("datapolicySeq",p.getDatapolicySeq());
			mapList.add(map);
		}
		return MapUtils.toMap(new Object[][]{
				{"data",mapList}
		});
	}

	/**
	 * 资源策略-分页
	 *
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 * @params datapolicyId 资源策略标识
	 * @params datapolicyCode 资源策略code
	 * @params datapolicyName 资源策略名称
	 */
	@CoreRule(protocol = "tree", needLogin = false)
	@MethodDesc(comment = "资源策略树", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据", type = List.class),
			@Return2Desc(name = "data[n].datapolicyCode", comment = "资源策略code", type = String.class),
			@Return2Desc(name = "data[n].datapolicyName", comment = "资源策略名称", type = String.class),
			@Return2Desc(name = "data[n].isGroup", comment = "是否是组", type = String.class,memo = "组仅用于展示时方便，code无意义，不可选"),
			@Return2Desc(name = "data[n].children", comment = "子节点", type = List.class),

	}))
	public Map tree(
			@ParamDesc(comment = "资源策略名称", validate = Validate.M_SAFE) String datapolicyName,
			@ParamDesc(comment = "资源策略分组", validate = Validate.M_SAFE) String datapolicyGroup,
			Map $params
	) throws Exception {
		List<UcDatapolicy> list = ucDatapolicyService.queryList(datapolicyName,datapolicyGroup);

		Map<String,LinkedList> map = new LinkedHashMap<>();
		map.put("通用",new LinkedList());
		for (UcDatapolicy ucDatapolicy : list) {
			String group = ucDatapolicy.getDatapolicyGroup();
			if(StringUtils.isEmpty(group)){
				group = "通用";
			}
			LinkedList groupList = map.get(group);
			if(groupList == null){
				groupList = new LinkedList();
				map.put(group,groupList);
			}
			Map m = new LinkedHashMap();
			m.put("datapolicyCode",ucDatapolicy.getDatapolicyCode());
			m.put("datapolicyName",ucDatapolicy.getDatapolicyName());
			groupList.add(m);
		}

		int i=0;
		List<Map> rootList = new ArrayList<>();
		for (Map.Entry<String, LinkedList> entry : map.entrySet()) {
			Map m = new LinkedHashMap();
			m.put("datapolicyCode","group"+ i++);
			m.put("datapolicyName",entry.getKey());
			m.put("children",entry.getValue());
			rootList.add(m);
		}

		return MapUtils.toMap(new Object[][]{
				{"data",rootList}
		});
	}


	/**
	 * 资源策略-分页
	 *
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 * @params datapolicyId 资源策略标识
	 * @params datapolicyCode 资源策略code
	 * @params datapolicyName 资源策略名称
	 */
	@CoreRule(protocol = "all", needLogin = false)
	@MethodDesc(comment = "全部资源策略(菜单编辑/角色编辑页面用)", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据", type = List.class),
			@Return2Desc(name = "data[n].datapolicyCode", comment = "资源策略code", type = String.class),
			@Return2Desc(name = "data[n].datapolicyName", comment = "资源策略名称", type = String.class),

	}))
	public Map all(
			@ParamDesc(comment = "资源策略名称", validate = Validate.M_SAFE,memo = "模糊搜索") String datapolicyName,
			Map $params
	) throws Exception {
		List<UcDatapolicy> list = ucDatapolicyService.queryList(datapolicyName,null);

		List<Map> rootList = new ArrayList<>();
		for (UcDatapolicy ucDatapolicy : list) {
			Map m = new LinkedHashMap();
			m.put("datapolicyCode",ucDatapolicy.getDatapolicyCode());
			m.put("datapolicyName",ucDatapolicy.getDatapolicyName());
			rootList.add(m);
		}

		return MapUtils.toMap(new Object[][]{
				{"data",rootList}
		});
	}
}