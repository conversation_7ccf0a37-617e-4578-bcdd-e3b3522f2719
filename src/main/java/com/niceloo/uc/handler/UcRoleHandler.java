package com.niceloo.uc.handler;

import com.niceloo.core.bean.CoreConst.ErrorCode;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.uc.model.UcRole;
import com.niceloo.uc.model.UcRolemenu;
import com.niceloo.uc.service.*;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.*;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.SetUtils;
import org.nobject.common.lang.StringUtils;

import java.util.*;

/**
 * 角色-业务处理类
 *
 * <AUTHOR>
 * @Date 2019-11-18 10:35:12
 */
@Service
@CoreRule(protocol = "uc/role")
public class UcRoleHandler {

	@Autowired
	private UcRoleService ucRoleService;

	@Autowired
	private UcRolemenuService ucRolemenuService;

	@Autowired
	private UcMenuService ucMenuService;

	@Autowired
	private UcUserbrandService ucUserbrandService;

	@Autowired
	private UcUserroleService ucUserroleService;

	private final String[] EMPTYSTRINGARRAY = new String[0];

	/**
	 * 用户隐私信息当日查看次数最大值
	 */
	private final Integer MAX_COUNT = 999999;

	/**
	 * 角色-添加
	 *
	 * @param roleName       角色名称
	 * @param roleMemo       角色描述
	 * @param roleManagerole 可授权角色
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "add", auth = "uc/role/edit")
	@MethodDesc(comment = "添加角色", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "roleId", comment = "roleId", type = String.class)
	}))
	public Map add(
			@ParamDesc(comment = "角色名称", validate = Validate.M_SAFE, length = 50, unnull = true) String roleName,
			@ParamDesc(comment = "角色类型", validate = Validate.M_SAFE, length = 50) String roleType,
			@ParamDesc(comment = "允许查看手机号次数", validate = Validate.M_GTE0,defVal = "0") Integer roleMobilecount,
			@ParamDesc(comment = "允许查看身份证号次数", validate = Validate.M_GTE0,defVal = "0") Integer roleIdcardcount,
			@ParamDesc(comment = "允许查看微信次数", validate = Validate.M_GTE0,defVal = "0") Integer roleWeixincount,
			@ParamDesc(comment = "允许查看代理商次数", validate = Validate.M_GTE0,defVal = "0") Integer roleAgentcount,
			@ParamDesc(comment = "是否允许授权ip", validate = "R:[YN]",defVal = "N",memo = "枚举： YN") String roleIpstatus,
			@ParamDesc(comment = "是否授权ip-guard认证", validate = "R:[YN]",defVal = "N",memo = "枚举： YN") String roleIpguardstatus,
			@ParamDesc(comment = "角色描述", validate = Validate.M_SAFE, length = 500) String roleMemo,
			@ParamDesc(comment = "角色排序", validate = Validate.R_NUM, defVal = "0", memo = "越小越靠前") Integer roleSeq,
			@ParamDesc(comment = "创建人id", validate = Validate.R_ID, memo = "网关自动填写，不传") String roleCreaterid,
			@ParamDesc(comment = "创建人名称", validate = Validate.M_SAFE, memo = "网关自动填写，不传") String roleCreatername,
			@ParamDesc(comment = "管理范围", validate = Validate.M_SAFE, memo = "枚举： C 自定义  B 当前系统", defVal = "C") String roleManagescope,
			@ParamDesc(comment = "管理角色", validate = Validate.M_SAFE, memo = "[\"id1\",\"id2\"]") List roleManagerole,
			@ParamDesc(comment = "用户Id", validate = Validate.R_ID, memo = "不传，网关自动填写", unnull = true) String userId,
			@ParamDesc(comment = "品牌标识", validate = Validate.R_ID, memo = "不传，网关自动填写", unnull = true) String brandId,
			Map $params
	) throws Exception {
		checkMaximumOfCount(roleMobilecount, roleIdcardcount, roleWeixincount,roleAgentcount);
		UcRole ucRole = new UcRole();
		ucRole.setRoleName(roleName);
		ucRole.setRoleMemo(roleMemo);
		ucRole.setRoleSeq(roleSeq);
		ucRole.setRoleCreatedate(DateUtils.getNowDString());
		ucRole.setRoleCreaterid(roleCreaterid);
		ucRole.setRoleCreatername(roleCreatername);
		ucRole.setRoleManagescope(roleManagescope);
		ucRole.setBrandId(brandId);
		ucRole.setRoleType(roleType);
		ucRole.setRoleMobilecount(roleMobilecount);
		ucRole.setRoleIdcardcount(roleIdcardcount);
		ucRole.setRoleWeixincount(roleWeixincount);
		ucRole.setRoleAgentcount(roleAgentcount);
		ucRole.setRoleIpstatus(roleIpstatus);
		ucRole.setRoleIpguardstatus(roleIpguardstatus);
		if (roleManagerole != null && roleManagerole.size() > 0) {
			String s = roleManagerole.toString();
			ucRole.setRoleManagerole(s.substring(1, s.length() - 1).replaceAll("\\s", ""));
		} else {
			ucRole.setRoleManagerole(null);
		}
		ucRoleService.save(ucRole, userId, brandId);
		return MapUtils.toMap(new Object[][]{{"roleId", ucRole.getRoleId()}});
	}

	/**
	 * 角色-删除
	 *
	 * @param roleId roleId
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "delete", auth = "uc/role/delete")
	@MethodDesc(comment = "删除角色", returns = @ReturnDesc(subs = {}))
	public void delete(
			@ParamDesc(comment = "roleId", validate = Validate.R_ID, unnull = true, length = 36) String roleId,
			Map $params
	) throws Exception {
		UcRole ucRole = ucRoleService.findById(roleId);
		if (ucRole != null) {
			ucRoleService.delete(ucRole);
		}
	}


	@CoreRule(protocol = "move", auth = "uc/role/edit")
	@MethodDesc(comment = "移动角色", returns = @ReturnDesc(subs = {
	}))
	public void move(
			@ParamDesc(comment = "移动的角色id", validate = Validate.R_ID, length = 36, unnull = true) String sourceId,
			@ParamDesc(comment = "目标角色id", validate = Validate.R_ID, length = 36, unnull = true) String destId,
			@ParamDesc(comment = "前后", validate = "R:[PA]", length = 1, unnull = true,memo = "枚举： P 前面 A 后面") String mode,
			Map $params
	) throws Exception {
		ucRoleService.move(sourceId,destId,mode);
	}

	/**
	 * 角色-修改
	 *
	 * @param roleId         角色标识
	 * @param roleName       角色名称
	 * @param roleMemo       角色描述
	 * @param roleManagerole 可授权角色
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "edit", auth = "uc/role/edit")
	@MethodDesc(comment = "编辑角色", returns = @ReturnDesc(subs = {
	}))
	public void edit(
			@ParamDesc(comment = "角色标识", validate = Validate.R_ID, length = 36, unnull = true) String roleId,
			@ParamDesc(comment = "角色类型", validate = Validate.M_SAFE, length = 50) String roleType,
			@ParamDesc(comment = "允许查看手机号次数", validate = Validate.M_GTE0,defVal = "0") Integer roleMobilecount,
			@ParamDesc(comment = "允许查看身份证号次数", validate = Validate.M_GTE0,defVal = "0") Integer roleIdcardcount,
			@ParamDesc(comment = "允许查看微信次数", validate = Validate.M_GTE0,defVal = "0") Integer roleWeixincount,
			@ParamDesc(comment = "允许查看代理商次数", validate = Validate.M_GTE0,defVal = "0") Integer roleAgentcount,
			@ParamDesc(comment = "是否允许授权ip", validate = "R:[YN]",defVal = "N",memo = "枚举： YN") String roleIpstatus,
			@ParamDesc(comment = "是否授权ip-guard认证", validate = "R:[YN]",defVal = "N",memo = "枚举： YN") String roleIpguardstatus,
			@ParamDesc(comment = "角色名称", validate = Validate.M_SAFE, length = 50) String roleName,
			@ParamDesc(comment = "角色描述", validate = Validate.M_SAFE, length = 500) String roleMemo,
			@ParamDesc(comment = "角色排序", validate = Validate.R_NUM, memo = "越小越靠前") Integer roleSeq,
			@ParamDesc(comment = "管理范围", validate = Validate.M_SAFE, memo = "枚举： C 自定义  B 当前系统", defVal = "C") String roleManagescope,
			@ParamDesc(comment = "管理角色", validate = Validate.M_SAFE, memo = "[\"id1\",\"id2\"]") List roleManagerole,
			Map $params
	) throws Exception {
		checkMaximumOfCount(roleMobilecount, roleIdcardcount, roleWeixincount,roleAgentcount);
		UcRole ucRole = ucRoleService.findById(roleId);
		if (ucRole == null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "角色不存在", null, null);
		}
		ucRole.setRoleName(roleName);
		ucRole.setRoleMemo(roleMemo);
		ucRole.setRoleSeq(roleSeq);
		ucRole.setRoleManagescope(roleManagescope);
		ucRole.setRoleType(roleType);
		ucRole.setRoleMobilecount(roleMobilecount);
		ucRole.setRoleIdcardcount(roleIdcardcount);
		ucRole.setRoleWeixincount(roleWeixincount);
		ucRole.setRoleAgentcount(roleAgentcount);
		ucRole.setRoleIpstatus(roleIpstatus);
		ucRole.setRoleIpguardstatus(roleIpguardstatus);
		if (roleManagerole != null && roleManagerole.size() > 0) {
			String s = roleManagerole.toString();
			ucRole.setRoleManagerole(s.substring(1, s.length() - 1).replaceAll("\\s", ""));
		} else {
			ucRole.setRoleManagerole(null);
		}
		ucRoleService.update(ucRole);
	}

	/**
	 * 角色-修改
	 *
	 * @param copyRoleId 角色标识
	 * @param roleName   新角色名称
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "copy", auth = "uc/role/edit")
	@MethodDesc(comment = "复制角色", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "roleId", comment = "roleId", type = String.class, memo = "复制出来的角色id")
	}))
	public Map copy(
			@ParamDesc(comment = "角色标识", validate = Validate.R_ID, length = 36, unnull = true) String copyRoleId,
			@ParamDesc(comment = "角色名称", validate = Validate.M_SAFE, length = 50, unnull = true) String roleName,
			@ParamDesc(comment = "创建人id", validate = Validate.R_ID, memo = "网关自动填写，不传") String roleCreaterid,
			@ParamDesc(comment = "创建人名称", validate = Validate.M_SAFE, memo = "网关自动填写，不传") String roleCreatername,
			@ParamDesc(comment = "品牌id", validate = Validate.M_SAFE, memo = "当页面上显示多个品牌的角色时，需传") String brandId,
			Map $params
	) throws Exception {
		UcRole role = ucRoleService.copyRole(copyRoleId, roleName, roleCreaterid, roleCreatername, brandId);
		return MapUtils.toMap(new Object[][]{{"roleId", role.getRoleId()}});
	}


	/**
	 * 角色-详情
	 *
	 * @param roleId  roleId
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "info", needLogin = false)
	@MethodDesc(comment = "角色详情", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "roleId", comment = "角色标识", type = String.class),
			@Return2Desc(name = "roleName", comment = "角色名称", type = String.class),
			@Return2Desc(name = "roleType", comment = "角色类型", type = String.class),
			@Return2Desc(name = "roleMobilecount", comment = "允许查看手机号次数", type = String.class),
			@Return2Desc(name = "roleAgentcount", comment = "允许查看代理商次数", type = String.class),
			@Return2Desc(name = "roleIpstatus", comment = "是否允许授权ip", type = String.class),
			@Return2Desc(name = "roleIpguardstatus", comment = "是否授权ip-guard认证", type = String.class),
			@Return2Desc(name = "roleMemo", comment = "角色描述", type = String.class),
			@Return2Desc(name = "roleCreatedate", comment = "角色创建时间", type = String.class),
			@Return2Desc(name = "roleCreaterid", comment = "角色创建人id", type = String.class),
			@Return2Desc(name = "roleCreatername", comment = "角色创建人名称", type = String.class),
			@Return2Desc(name = "roleManagescope", comment = "角色管理范围", type = String.class, memo = "C 自定义  B  当前系统"),
			@Return2Desc(name = "roleManagerole", comment = "管理角色列表", type = List.class, memo = "[\"id1\",\"id2\"]"),
	}))
	public Map info(
			@ParamDesc(comment = "roleId", validate = Validate.R_ID, unnull = true, length = 36) String roleId,
			Map $params
	) throws Exception {
		UcRole ucRole = ucRoleService.findById(roleId);
		if (ucRole == null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "角色不存在", null, null);
		}
		//清除掉用户隐私信息
		Map map1 = BeanUtils.bean2Map(ucRole);
		String roleManagerole = ucRole.getRoleManagerole();
		if (!StringUtils.isEmpty(roleManagerole)) {
			String[] split = roleManagerole.split(",");
			map1.put("roleManagerole", split);
		} else {
			map1.put("roleManagerole", EMPTYSTRINGARRAY);
		}
		map1.remove("__updateProps__");
		return map1;
	}


	@CoreRule(protocol = "menu/tree", needLogin = false)
	@MethodDesc(comment = "根据角色获取菜单树", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据集合", type = String.class),
			@Return2Desc(name = "data[n].menuId", comment = "菜单标识", type = String.class),
			@Return2Desc(name = "data[n].menuName", comment = "菜单名称", type = String.class),
			@Return2Desc(name = "data[n].menuType", comment = "菜单类型", type = String.class, memo = "枚举 工作台:desktop 应用:app 模块:model 页面:page 功能组合:funcGroup 功能:func"),
			@Return2Desc(name = "data[n].menuUrl", comment = "菜单地址", type = String.class),
			@Return2Desc(name = "data[n].menuCode", comment = "菜单编码", type = String.class, memo = "唯一值，若为功能，需与protocol一致"),
			@Return2Desc(name = "data[n].menuParam", comment = "菜单参数", type = String.class),
			@Return2Desc(name = "data[n].menuSeq", comment = "菜单顺序", type = Integer.class, memo = "越小越靠前"),
			@Return2Desc(name = "data[n].menuDisablestatus", comment = "菜单禁用状态", type = String.class, memo = "枚举：E启用，D禁用"),
			@Return2Desc(name = "data[n].menuIcon", comment = "菜单图标", type = String.class),
			@Return2Desc(name = "data[n].menuDatapolicy", comment = "功能数据权限", type = List.class),
			@Return2Desc(name = "data[n].children", comment = "子集合", type = List.class),

			@Return2Desc(name = "checkedata", comment = "checked数据集合", type = String.class),
			@Return2Desc(name = "checkedata[n].menuId", comment = "菜单id", type = String.class),
			@Return2Desc(name = "checkedata[n].roleDatapolicy", comment = "资源权限code集合", type = List.class),
	}))
	public Map getMenuTreeByRole(
			@ParamDesc(comment = "roleId", validate = Validate.R_ID, unnull = true, length = 36) String roleId,
			@ParamDesc(comment = "用户Id", validate = Validate.R_ID, unnull = true, length = 36, memo = "网关传入") String userId,
			@ParamDesc(comment = "品牌标识", validate = Validate.R_ID, unnull = true, length = 36) String brandId,
			Map $params) throws Exception {
		List<Map> checkedList = new ArrayList<>();
		List<Map> list = null;

		boolean containsF = true;
		boolean containsD = false;
		boolean containsFG = true;
		UcRole byId = this.ucRoleService.findById(roleId);
		if (byId == null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "角色不存在", "");
		}
		if (ucUserroleService.isSuperAdmin(userId)) {
			list = ucMenuService.queryMenuTree(containsF, containsD, null, containsFG);
		} else {
			list = ucMenuService.queryMenuTreeByUser(userId, brandId, containsF, containsD,null, containsFG);
		}
		if ("Y".equals(byId.getRoleAdminstatus())) {
			fillCheckedListByMenuList(checkedList, list);
		} else {
			List<UcRolemenu> byRoleId = ucRolemenuService.queryByRoleId(roleId);
			Map<String, Set<String>> map = new HashMap<>();
			Set emptyset = new HashSet();
			for (UcRolemenu ucRolemenu : byRoleId) {
				String roleDatapolicy = ucRolemenu.getRoleDatapolicy();
				if (!StringUtils.isEmpty(roleDatapolicy)) {
					String[] split = roleDatapolicy.split(",");
					Set set = SetUtils.toSet(split);
					map.put(ucRolemenu.getMenuId(), set);
				} else {
					map.put(ucRolemenu.getMenuId(), emptyset);
				}
			}
			this.filterCheckedMenus(list, map);
			for (Map.Entry<String, Set<String>> entry : map.entrySet()) {
				Map m = new HashMap();
				m.put("menuId", entry.getKey());
				m.put("roleDatapolicy", entry.getValue());
				checkedList.add(m);
			}
		}

		return MapUtils.toMap(new Object[][]{
				{"data", list},
				{"checkedList", checkedList}
		});
	}

	private void fillCheckedListByMenuList(List<Map> checkedList, List<Map> list) {
		for (Map map : list) {
			if (map.get("menuDatapolicy") != null) {
				checkedList.add(MapUtils.toMap(new Object[][]{
						{"menuId", map.get("menuId")},
						{"roleDatapolicy", map.get("menuDatapolicy")}
				}));
			} else {
				checkedList.add(MapUtils.toMap(new Object[][]{
						{"menuId", map.get("menuId")},
						{"roleDatapolicy", EMPTYSTRINGARRAY}
				}));
			}
			Object children = map.get("children");
			if (children != null && children instanceof List) {
				fillCheckedListByMenuList(checkedList, (List<Map>) children);
			}
		}
	}

	/**
	 * 过滤选中的menu，如果子没有全部选中，则父不全部选中
	 *
	 * @param menuList
	 * @param checkMenus
	 * @return
	 */
	private boolean filterCheckedMenus(List<Map> menuList, Map checkMenus) {
		Iterator<Map> iterator = menuList.iterator();
		boolean allChecked = true;
		while (iterator.hasNext()) {
			Map map = iterator.next();
			Object menuId = map.get("menuId");
			Object children = map.get("children");
			if (children != null) {
				List<Map> ccc = (List) children;
				if (!filterCheckedMenus(ccc, checkMenus)) {
					checkMenus.remove(menuId);
					allChecked = false;
				}
			} else {
				if (checkMenus.get(menuId) == null) {
					allChecked = false;
				}
			}
		}
		return allChecked;
	}



	/**
	 * 根据角色获取对应的权限信息
	 *
	 * @param roleId  roleId
	 * @param menus   [{menuId:"xxx",roleDatapolicy:["aaa","bbb"]}]
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "rolemenu/set", auth = "uc/role/edit")
	@MethodDesc(comment = "设置角色的菜单权限", returns = @ReturnDesc(subs = {}))
	public void setRolemenu(
			@ParamDesc(comment = "roleId", validate = Validate.R_ID, unnull = true, length = 36) String roleId,
			@ParamDesc(comment = "userId", validate = Validate.R_ID, unnull = true, memo = "当前用户的id，网关传入", length = 36) String userId,
			@ParamDesc(comment = "menus", validate = Validate.M_SAFE, unnull = true, memo = "[{menuId:xxx,roleDatapolicy:[\"policyCode1\",\"policyCode2\"]},{……}]") List<Map> menus,
			Map $params
	) throws Exception {
		UcRole ucRole = ucRoleService.findById(roleId);
		if (ucRole == null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "角色不存在", null, null);
		}
		ucRolemenuService.setMenusbyRoleId(ucRole, userId, menus);
	}


	/**
	 * 角色列表
	 *
	 * @param roleName 角色名称
	 * @param userId 用户Id
	 * @param brandId 品牌标识
	 * @param group 是否分组
	 * @param $params 附加参数
	 * @return Map
	 * @throws Exception
	 */
	@CoreRule(protocol = "list", needLogin = false)
	@MethodDesc(comment = "角色列表", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据", type = List.class),
			@Return2Desc(name = "dataType", comment = "数据类型，多品牌下返回tree，单品牌返回list", memo = "tree list", type = String.class),
			@Return2Desc(name = "brandId", comment = "单品牌时，返回品牌id", type = String.class),
			@Return2Desc(name = "brandName", comment = "单品牌时，返回品牌名称", type = String.class),
			@Return2Desc(name = "data[n].roleId", comment = "角色标识", type = String.class),
			@Return2Desc(name = "data[n].flag", comment = "标记", memo = "超管 admin 品牌brand  角色 role ", type = String.class),
			@Return2Desc(name = "data[n].roleName", comment = "角色名称", type = String.class),
			@Return2Desc(name = "data[n].roleType", comment = "角色类型", type = String.class),
			@Return2Desc(name = "data[n].roleMemo", comment = "角色描述", type = String.class),
			@Return2Desc(name = "data[n].roleSeq", comment = "角色排序", type = Integer.class),
			@Return2Desc(name = "data[n].roleMobilecount", comment = "允许查看手机号次数", type = String.class),
			@Return2Desc(name = "data[n].roleIpstatus", comment = "是否允许授权ip", type = String.class),
			@Return2Desc(name = "data[n].roleCreatedate", comment = "角色创建时间", type = String.class),
			@Return2Desc(name = "data[n].roleCreaterid", comment = "角色创建人id", type = String.class),
			@Return2Desc(name = "data[n].roleCreatername", comment = "角色创建人名称", type = String.class),
			@Return2Desc(name = "data[n].roleManagerole", comment = "可授权角色", type = List.class),
			@Return2Desc(name = "data[n].brandId", comment = "角色品牌", type = String.class),
			@Return2Desc(name = "data[n].roleAdminstatus", comment = "是否超级管理员", type = String.class),
			@Return2Desc(name = "data[n].roleManagescope", comment = "管理范围", type = String.class),
			@Return2Desc(name = "data[n].children", comment = "子节点", memo = "当有多个品牌时，处理成二级树", type = List.class),
	}))
	public Map list(
			@ParamDesc(comment = "角色名称", validate = Validate.M_SAFE, memo = "角色名称") String roleName,
			@ParamDesc(comment = "用户Id", validate = Validate.R_ID, memo = "不传，网关自动填写", unnull = true) String userId,
			@ParamDesc(comment = "品牌标识", validate = Validate.R_ID, memo = "不传，网关自动填写", unnull = true) String brandId,
			@ParamDesc(comment = "是否分组", validate = Validate.M_SAFE, memo = "是否根据品牌分组  Y  N ", defVal = "N") String group,
			Map $params
	) throws Exception {

		boolean superAdmin = ucUserroleService.isSuperAdmin(userId);
		List<UcRole> ucRoles;
		if (superAdmin) {
			ucRoles = ucRoleService.queryRoleListByName(roleName, null);
		} else {
			//获取用户角色
			ucRoles = ucRoleService.queryRoleListByUser(userId, brandId, roleName);
		}
		List<Map> managerRoleList = new ArrayList();
		for (UcRole managerRole : ucRoles) {
			Map map = BeanUtils.bean2Map(managerRole);
			map.remove("__updateProps__");
			String roleManagerole = managerRole.getRoleManagerole();
			if (roleManagerole != null && !"".equals(roleManagerole)) {
				map.put("roleManagerole", roleManagerole.split(","));
			} else {
				map.put("roleManagerole", EMPTYSTRINGARRAY);
			}
			map.put("flag", "role");
			managerRoleList.add(map);
		}
		Map<String, String> brandMap = ucUserbrandService.queryBranMap();
		if (superAdmin && "Y".equals(group)) {
			List<Map> rt = new ArrayList<>();
			boolean addedAdmin = false;
			for (Map.Entry<String, String> entry : brandMap.entrySet()) {
				Map m = new LinkedHashMap();
				rt.add(m);
				m.put("roleId", entry.getKey());
				m.put("roleName", entry.getValue());
				ArrayList<Map> ll = new ArrayList<>();
				m.put("children", ll);
				m.put("flag", "brand");
				for (Map o : managerRoleList) {
					if (!addedAdmin && "Y".equals(o.get("roleAdminstatus"))) {
						rt.add(0, o);
						o.put("flag", "admin");
						addedAdmin = true;
						continue;
					}
					if (entry.getKey().equals(o.get("brandId"))) {
						ll.add(o);
					}
				}
			}
			return MapUtils.toMap(new Object[][]{
					{"data", rt},
					{"dataType", "tree"}
			});
		} else {
			return MapUtils.toMap(new Object[][]{
					{"data", managerRoleList},
					{"dataType", "list"},
					{"brandName", brandMap.get(brandId)},
					{"brandId", brandId}
			});
		}

	}

	@CoreRule(protocol = "roletypes", needLogin = false)
	@MethodDesc(comment = "角色类型列表", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据", type = List.class),
			@Return2Desc(name = "data[n].roletypeCode", comment = "角色类型code", type = String.class),
			@Return2Desc(name = "data[n].roletypeName", comment = "角色类型名称",type=String.class),
			@Return2Desc(name = "data[n].roletypeSeq", comment = "排序号", type = Integer.class),
	}))
	public Map list(
			Map $params
	) throws Exception {
		List<Map> roleTypes = ucRoleService.findRoleTypes();
		return MapUtils.toMap(new Object[][]{
				{"data",roleTypes}
		});
	}


	@CoreRule(protocol = "roles/bytype", needLogin = false)
	@MethodDesc(comment = "根据角色类型查询角色", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据", type = List.class),
			@Return2Desc(name = "data[n].roleId", comment = "角色标识", type = String.class),
			@Return2Desc(name = "data[n].roleName", comment = "角色名称", type = String.class),
			@Return2Desc(name = "data[n].roleType", comment = "角色类型", type = String.class),
			@Return2Desc(name = "data[n].brandId", comment = "角色品牌", type = String.class),
	}))
	public Map rolesBytype(
			@ParamDesc(comment = "类型code", validate = Validate.M_SAFE, memo = "角色名称") String roleType,
			@ParamDesc(comment = "品牌Id", validate = Validate.M_SAFE, memo = "角色名称") String brandId
			) throws Exception {
		List<UcRole> roleTypes = ucRoleService.findByRoleTypes(roleType,brandId);
		return MapUtils.toMap(new Object[][]{
				{"data",roleTypes}
		});
	}


	@CoreRule(protocol = "users", needLogin = false)
	@MethodDesc(comment = "根据角色获取用户id集合", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "data", comment = "数据", type = List.class),
			@Return2Desc(name = "data[n]", comment = "userId", type = String.class),
	}))
	public Map rolesBytype(
			@ParamDesc(comment = "角色id", validate = Validate.M_SAFE, memo = "角色id",unnull = true) String roleId
	) throws Exception {
		String []userIds = ucRoleService.findUsersByrole(roleId);
		return MapUtils.toMap(new Object[][]{
				{"data",userIds}
		});
	}

	/**
	 * @Description: 校验用户隐私信息当日查看次数最大值
	 * @param counts
	 * @return void
	 * @throws ApplicationException
	 * <AUTHOR>
	 * @Date 2020/11/26 9:02
	 */
	private void checkMaximumOfCount(Integer... counts) throws ApplicationException {
		for (Integer count : counts) {
			if (count.compareTo(MAX_COUNT) > 0) {
				throw new ApplicationException(ErrorCode.argument_invalide, "查看次数最大为: " + MAX_COUNT.toString(), null, null);
			}
		}
	}

}