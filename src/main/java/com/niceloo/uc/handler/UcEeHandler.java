package com.niceloo.uc.handler;

import com.niceloo.core.bean.CoreConst.ErrorCode;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.lock.annotation.GlobalLock;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.common.UcStringUtil;
import com.niceloo.uc.model.*;
import com.niceloo.uc.service.*;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.*;
import org.nobject.common.db.Transactional;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.lang.CollectionUtils;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.ObjectUtils;
import org.nobject.common.lang.StringUtils;
import org.nobject.common.log.Logger;

import java.util.*;
import java.util.stream.Collectors;

import static org.nobject.common.lang.StringUtils.isEmpty;

/**
 * 员工-业务处理类
 * maning 20201205
 */
@SuppressWarnings({"unused", "Duplicates"})
@Service
@CoreRule(protocol = "uc/ee")
public class UcEeHandler {

    @Autowired
    private UcEeService ucEeService;
    @Autowired
    private UcDpteeService ucDpteeService;
    @Autowired
    private UcSchoolService ucSchoolService;
    @Autowired
    private UcDptService ucDptService;
    @Autowired
    private UcUserrelationService ucUserrelationService;
    @Autowired
    private UcTagService ucTagService;

    private Logger logger = Logger.getLogger(UcEeHandler.class);


    /**
     * 员工-详情-用于基础数据，用户中心，企业营销，系统设置
     *
     * @param eeId 员工标识
     */
    @SuppressWarnings("unchecked")
    @CoreRule(protocol = "info")
    @MethodDesc(comment = "员工-详情", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "eeId", comment = "员工标识", type = String.class),
            @Return2Desc(name = "userId", comment = "用户标识", type = String.class),
            @Return2Desc(name = "userName", comment = "用户名", type = String.class),
            @Return2Desc(name = "eeNo", comment = "员工编号", type = String.class),
            @Return2Desc(name = "dptId", comment = "员工部门标识", type = String.class),
            @Return2Desc(name = "posId", comment = "员工岗位标识", type = String.class),
            @Return2Desc(name = "posName", comment = "员工岗位", type = String.class),
            @Return2Desc(name = "dptName", comment = "部门名称", type = String.class),
            @Return2Desc(name = "dptFullname", comment = "部门组织名称", type = String.class),
            @Return2Desc(name = "eeOfficialstatus", comment = "员工正式状态(O:正式;P:试用)", type = String.class),
            @Return2Desc(name = "eeWorkstatus", comment = "员工在职状态(O:在职;L:离职;W:预入职)", type = String.class),
            @Return2Desc(name = "eeHiredate", comment = "员工入职日期", type = String.class),
            @Return2Desc(name = "eeTermdate", comment = "员工离职日期", type = String.class),
            @Return2Desc(name = "eePhone", comment = "员工联系电话", type = String.class),
            @Return2Desc(name = "eeInnerphone", comment = "员工联系电话(销售使用)", type = String.class),
            @Return2Desc(name = "eeEdutype", comment = "员工教学身份(C:班主任;T:教研老师)", type = String.class),
            @Return2Desc(name = "eeCompanyemail", comment = "员工企业邮箱", type = String.class),
            @Return2Desc(name = "eeCompanywechat", comment = "员工企业微信", type = String.class),
            @Return2Desc(name = "eeSysaccount", comment = "员工系统账号", type = String.class),
            @Return2Desc(name = "eeAdress", comment = "员工现住地址", type = String.class),
            @Return2Desc(name = "eeRoles", comment = "员工角色", type = List.class),
            @Return2Desc(name = "eeBrands", comment = "员工品牌", type = List.class),
            @Return2Desc(name = "eeWeightgrade", comment = "员工权重等级", type = List.class),
            @Return2Desc(name = "eeTqaccount", comment = "员工TQ账号", type = String.class),
            @Return2Desc(name = "eeTqpassword", comment = "员工TQ密码", type = String.class),
            @Return2Desc(name = "eeFyaccount", comment = "员工风云账号", type = String.class),
            @Return2Desc(name = "eeFypassword", comment = "员工风云密码", type = String.class),
            @Return2Desc(name = "eeCallouttype", comment = "员工外呼方式(T:TQ;F:风云)", type = String.class),
            @Return2Desc(name = "eeYkphonestatus", comment = "员工云客手机外呼可用状态(Y:可用;N:不可用)", type = String.class),
            @Return2Desc(name = "eeYksoftphonestatus", comment = "员工云客软电话外呼可用状态(Y:可用;N:不可用)", type = String.class),
            @Return2Desc(name = "eeByavlstatus", comment = "员工百应账号可用状态(Y:可用;N:不可用)", type = String.class),
            @Return2Desc(name = "eeByaccount", comment = "员工百应账号", type = String.class),
            @Return2Desc(name = "schoolId", comment = "分校标识", type = String.class),
            @Return2Desc(name = "schoolName", comment = "分校名称", type = String.class),
            @Return2Desc(name = "principalName", comment = "部门负责人", type = String.class),
            @Return2Desc(name = "principalNo", comment = "部门负责人编号", type = String.class),
            @Return2Desc(name = "eeSourceid", comment = "员工来源标识", type = String.class),
            @Return2Desc(name = "eePostype", comment = "员工职级", type = String.class),
            @Return2Desc(name = "eeParentno", comment = "汇报上级编号", type = String.class),
            @Return2Desc(name = "eeParentname", comment = "汇报上级名称", type = String.class),
            @Return2Desc(name = "tagType",comment = "标签类型 A答疑老师", memo = "R:[A]", length = 1, type = String.class),
            @Return2Desc(name = "tagAvlstatus",comment = "标签启用状态(Y:可用;N:不可用)", memo = "R:[YN]", length = 1, type = String.class),
    }))
    public Map info(
            @ParamDesc(comment = "员工标识", validate = Validate.R_ID, length = 36) String eeId,
            @ParamDesc(comment = "用户标识", validate = Validate.R_ID, length = 36) String userId
    ) throws Exception {
        if (isEmpty(eeId) && isEmpty(userId)) {
            throw new ApplicationException(ErrorCode.argument_invalide, "参数不能为空", null, null);
        }
        BdEe bdEe = null;
        // 优先查询 userId
        if (!isEmpty(userId)) {
            List<BdEe> list = ucEeService.findByUserIds(userId);
            if (list.size() > 0) {
                bdEe = list.get(0);
            }
        } else {
            bdEe = ucEeService.findById(eeId);
        }
        if (bdEe == null) {
            throw new ApplicationException(ErrorCode.argument_invalide, "员工不存在", null, null);
        }

        //清除掉用户隐私信息
        Map rtn = BeanUtils.bean2Map(bdEe);

        //获取部门名称，岗位名称，学校名称
        List<Map> names = ucDpteeService.findOtherNameByeeId((String) rtn.get("eeId"));
        if (names != null && names.size() > 0) {
            rtn.putAll(names.get(0));
            String fullDptnameByLevelcode;
            try {
                fullDptnameByLevelcode = ucDptService.getFullDptnameByLevelcode(names.get(0).get("dptLevelcode").toString());
            } catch (Exception e) {
                fullDptnameByLevelcode = "";
                logger.error("ucDptService.getFullDptnameByLevelcode 异常.", e);
            }
            rtn.put("dptFullname", fullDptnameByLevelcode);
        } else {
            rtn.put("dptId", "");
            rtn.put("dptName", "");
            rtn.put("dptFullname", "");
            rtn.put("posId", "");
            rtn.put("posName", "");
        }
        BdSchool school = ucSchoolService.findById((String) rtn.get("schoolId"));
        if (school != null) {
            rtn.put("schoolName", school.getSchoolName());
        } else {
            rtn.put("schoolName", "");
        }

        //获取部门负责人信息
        BdDptee dptee = ucDpteeService.findSuperByeeId(bdEe.getEeId());
        if (null == dptee) {
            rtn.put("principalName", "");
            rtn.put("principalNo", "");
        } else {
            BdEe superEe = ucEeService.findById(dptee.getEeId());
            if (superEe == null) {
                rtn.put("principalName", "");
                rtn.put("principalNo", "");
            } else {
                rtn.put("principalName", superEe.getUserName());
                rtn.put("principalNo", superEe.getEeNo());
            }
        }

        // 员工汇报上级
        if (StringUtils.isEmpty(bdEe.getEeParentno())) {
            rtn.put("eeParentno", "");
            rtn.put("eeParentname", "");
        } else {
            BdEe parent = ucEeService.findByNo(bdEe.getEeParentno());
            if (!ObjectUtils.isEmpty(parent)) {
                rtn.put("eeParentno", parent.getEeNo());
                rtn.put("eeParentname", parent.getUserName());
            } else {
                rtn.put("eeParentno", "");
                rtn.put("eeParentname", "");
            }
        }

        if (!StringUtils.isEmpty((String) rtn.get("eeRoles"))) {
            String eeRole = (String) rtn.get("eeRoles");
            if (StringUtils.isEmpty(eeRole)) {
                rtn.put("eeRoles", new String[0]);
            } else {
                rtn.put("eeRoles", eeRole.split(","));
            }
        } else {
            rtn.put("eeRoles", new String[0]);
        }
        if (!StringUtils.isEmpty((String) rtn.get("eeBrands"))) {
            String eeBrands = (String) rtn.get("eeBrands");
            if (StringUtils.isEmpty(eeBrands)) {
                rtn.put("eeBrands", new String[0]);
            } else {
                rtn.put("eeBrands", eeBrands.split(","));
            }
        } else {
            rtn.put("eeBrands", new String[0]);
        }
        //答疑老师相关信息
        UcUserTag tagById = ucTagService.findTagById(bdEe.getUserId(), UcConst.userTagType.type_A.code);
        if(!ObjectUtils.isEmpty(tagById)){
            rtn.put("tagType",UcConst.userTagType.type_A.code);
            rtn.put("tagAvlstatus",tagById.getTagAvlstatus());
        }
        return rtn;
    }

    /**
     * 员工-根据用户标识获取员工部门分校信息-用于客户营销的相关页面
     *
     * @param userId       用户标识
     * @param $params_desc 附加参数
     */
    @SuppressWarnings("unchecked")
    @CoreRule(protocol = "orgInfo")
    @MethodDesc(comment = "员工-部门分校信息", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "eeId", comment = "员工标识", type = String.class),
            @Return2Desc(name = "eeNo", comment = "员工编号", type = String.class),
            @Return2Desc(name = "dptId", comment = "部门标识", type = String.class),
            @Return2Desc(name = "dptName", comment = "部门名称", type = String.class),
            @Return2Desc(name = "dptCode", comment = "部门编码", type = String.class),
            @Return2Desc(name = "schoolId", comment = "学校标识", type = String.class),
            @Return2Desc(name = "schoolName", comment = "学校名称", type = String.class),
            @Return2Desc(name = "relationId", comment = "学校关联的部门标识", type = String.class),
            @Return2Desc(name = "relationName", comment = "学校关联的部门名称", type = String.class),
            @Return2Desc(name = "pareadptId", comment = "大区标识", type = String.class),
    }))
    public Map orgInfo(
            @ParamDesc(comment = "用户标识", validate = Validate.R_ID, length = 36) String userId,
            @ParamDesc(comment = "员工标识", validate = Validate.R_ID, length = 36) String eeId,
            Map $params_desc
    ) throws Exception {
        if (isEmpty(eeId) && isEmpty(userId)) {
            throw new ApplicationException(ErrorCode.argument_invalide, "参数不能为空", null, null);
        }
        BdEe bdEe = null;
        // 优先查询 userId
        if (!isEmpty(userId)) {
            List<BdEe> list = ucEeService.findByUserIds(userId);
            if (list.size() > 0) {
                bdEe = list.get(0);
            }
        } else {
            bdEe = ucEeService.findById(eeId);
        }
        if (bdEe == null) {
            throw new ApplicationException(ErrorCode.argument_invalide, "员工不存在", null, null);
        }

        Map rtn = ucEeService.findOrgInfoByUserId(bdEe.getEeId());
        if (rtn.get("dptId") != null) {
            String pareadptId = ucDptService.findAreaByDptId((String) rtn.get("dptId"));
            if (!isEmpty(pareadptId)) {
                rtn.put("pareadptId", pareadptId);
            }
        } else {
            rtn.put("eeId", bdEe.getEeId());
            rtn.put("eeNo", bdEe.getEeNo());
        }
        return rtn;
    }

    /**
     * 模糊查询-用于员工搜索下拉框 - 用于客户营销，推广中心
     * @return map
     * @throws Exception e
     */
    @CoreRule(protocol = "list/forSearch")
    @MethodDesc(comment = "员工-模糊查询-用于员工搜索下拉框", returns = @ReturnDesc(subs = {
            @Return2Desc(name="data",comment="员工列表",type=List.class),
            @Return2Desc(name = "data[n].eeId", comment = "员工标识", type = String.class),
            @Return2Desc(name = "data[n].userId", comment = "用户标识", type = String.class),
            @Return2Desc(name = "data[n].userName", comment = "用户姓名", type = String.class),
            @Return2Desc(name = "data[n].eeWorkstatus", comment = "员工在职状态(O:在职;L:离职;W:预入职)", type = String.class),
            @Return2Desc(name = "data[n].schoolId", comment = "学校标识", type = String.class),
            @Return2Desc(name = "data[n].schoolName", comment = "学校名称", type = String.class),
            @Return2Desc(name = "data[n].dptId", comment = "所属部门标识", type = String.class),
            @Return2Desc(name = "data[n].dptName", comment = "所属部门名称", type = String.class),
    }))
    public Map listforSearch(
            @ParamDesc(comment="分页起始",validate=Validate.M_STARTINDEX,defVal = "0")	Integer pageIndex,
            @ParamDesc(comment="分页数量",validate=Validate.M_COUNT,defVal = "15")	Integer pageSize,
            @ParamDesc(comment="员工在职状态",validate="R:[OLW]",memo="[枚举值]O:在职;L:离职;W:预入职") String eeWorkstatus,
            @ParamDesc(comment="员工教学身份",validate="R:[CT]",memo="[枚举]C:班主任;T:教研老师")	String eeEdutype,
            @ParamDesc(comment="员工可用状态",validate="R:[YN]",memo="[枚举值]Y:可用;N:不可用")	String eeAvlstatus,
            @ParamDesc(comment="用户名称",validate=Validate.M_SAFE,memo="用户名称",unnull = true)	String userName,
            @ParamDesc(comment="分校标识",validate=Validate.R_ID,length = 32 )	String schoolId,
            Map $params_desc
    )throws Exception {
        List<Map> list = ucEeService.listforSearch(userName, eeAvlstatus, eeEdutype, eeWorkstatus, schoolId, pageIndex, pageSize);
        List<String> userIds = list.stream().map(doc -> (String)doc.get("userId")).collect(Collectors.toList());
        List<UcUserrelation> urs =ucUserrelationService.queryRelationsByUserId2(userIds, Collections.singletonList("project"), "YOULU");
        Map<String, List<UcUserrelation>> urDepot = urs.stream().collect(Collectors.groupingBy(UcUserrelation::getUserId));
        list.forEach(doc -> {
            String userId = (String) doc.get("userId");
            Map<String, List<String>> map1 = new LinkedHashMap<>();
            //noinspection unchecked
            doc.put("dataScopo", map1);
            List<UcUserrelation> _urs = urDepot.get(userId);
            List<String> projectIds = CollectionUtils.isEmpty(_urs)
                    ? new ArrayList<>()
                    : _urs.stream().map(UcUserrelation::getUserreRelationid).collect(Collectors.toList());
            map1.put("project", projectIds);
        });


        Map r = new LinkedHashMap();
        //noinspection unchecked
        r.put("data",list);
        return r;
    }

    /**
     * 根据部门，查询直属员工及子部门 - 用于客户营销，推广中心
     * @param dptId 部门标识
     */
    @CoreRule(protocol = "list/eeAndDpt")
    @MethodDesc(comment = "员工-部门查询直属员工及子部门", returns = @ReturnDesc(subs = {
            @Return2Desc(name="ees",comment="员工列表",type=List.class),
            @Return2Desc(name = "ees[n].eeId", comment = "员工标识", type = String.class),
            @Return2Desc(name = "ees[n].userId", comment = "员工用户标识", type = String.class),
            @Return2Desc(name = "ees[n].userName", comment = "用户姓名", type = String.class),
            @Return2Desc(name = "ees[n].dptId", comment = "员工所属部门标识", type = String.class),
            @Return2Desc(name="dpts",comment="部门列表",type=List.class),
            @Return2Desc(name = "dpts[n].dptId", comment = "部门标识", type = String.class),
            @Return2Desc(name = "dpts[n].dptName", comment = "部门名称", type = String.class),
    }))
    public Map listAll(
            @ParamDesc(comment="部门标识",validate=Validate.R_ID,memo="部门标识",unnull = true)	String dptId,
            @ParamDesc(comment="查询员工类型",validate="R:[AD]",memo="[枚举值]A:全部员工;D:直属员工",defVal = "D")	String eeSearchtype,
            @ParamDesc(comment="用户名称",validate=Validate.M_SAFE,memo="用户名称")	String userName,
            @ParamDesc(comment="员工教学身份(C:班主任;T:教研老师)",validate="R:[CT]",length = 1)	String eeEdutype,
            Map $params_desc
    )throws Exception {
        Map<String, List<Map>> r = ucEeService.findDptAndEeByDptId(dptId, eeSearchtype, userName, eeEdutype);
        List<String> userIds = r.get("ees").stream().map(doc -> (String)doc.get("userId")).collect(Collectors.toList());
        List<UcUserrelation> urs =ucUserrelationService.queryRelationsByUserId2(userIds, Collections.singletonList("project"), "YOULU");
        Map<String, List<UcUserrelation>> urDepot = urs.stream().collect(Collectors.groupingBy(UcUserrelation::getUserId));
        r.get("ees").forEach(doc -> {
            String userId = (String) doc.get("userId");
            Map<String, List<String>> map1 = new LinkedHashMap<>();
            //noinspection unchecked
            doc.put("dataScopo", map1);
            List<UcUserrelation> _urs = urDepot.get(userId);
            List<String> projectIds = CollectionUtils.isEmpty(_urs)
                    ? new ArrayList<>()
                    : _urs.stream().map(UcUserrelation::getUserreRelationid).collect(Collectors.toList());
            map1.put("project", projectIds);
        });

        return r;
    }

    /**
     * 员工-分页带权限查询
     *
     * @param pageIndex    分页起始
     * @param pageSize     分页数量
     * @param userName     用户名
     * @param eeNo         员工编号
     * @param eeWorkstatus 员工在职状态(O:在职;L:离职;W:预入职)
     * @param eeEdutype    员工教学身份(C:班主任;T:教研老师)
     * @param eePhone      员工联系电话
     * @param $params_desc 附加参数
     * @return map
     * @throws Exception ex
     */
    @CoreRule(protocol = "list/auth")
    @MethodDesc(comment = "员工-分页", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "count", comment = "数量", type = Integer.class),
            @Return2Desc(name = "data", comment = "数据", type = List.class),
            @Return2Desc(name = "data[n].eeId", comment = "员工标识", type = String.class),
            @Return2Desc(name = "data[n].userName", comment = "用户名", type = String.class),
            @Return2Desc(name = "data[n].userId", comment = "用户标识", type = String.class),
            @Return2Desc(name = "data[n].eePhone", comment = "员工联系电话", type = String.class),
            @Return2Desc(name = "data[n].eeInnerphone", comment = "员工联系电话（销售使用）", type = String.class),
            @Return2Desc(name = "data[n].eeNo", comment = "员工编号", type = String.class),
            @Return2Desc(name = "data[n].eeOfficialstatus", comment = "员工正式状态(O:正式;P:试用)", type = String.class),
            @Return2Desc(name = "data[n].eeWorkstatus", comment = "员工在职状态(O:在职;L:离职;W:预入职)", type = String.class),
            @Return2Desc(name = "data[n].dptId", comment = "员工部门标识", type = String.class),
            @Return2Desc(name = "data[n].dptName", comment = "员工部门", type = String.class),
            @Return2Desc(name = "data[n].posId", comment = "员工岗位标识", type = String.class),
            @Return2Desc(name = "data[n].posName", comment = "员工岗位", type = String.class),
            @Return2Desc(name = "data[n].eeRoles", comment = "员工角色", type = String.class),
            @Return2Desc(name = "data[n].eeBrands", comment = "员工品牌", type = String.class),
            @Return2Desc(name = "data[n].eeEdutype", comment = "员工教学身份(C:班主任;T:教研老师)", type = String.class),
            @Return2Desc(name = "data[n].eeCreateddate", comment = "员工创建时间", type = String.class),
            @Return2Desc(name = "data[n].eeCreater", comment = "员工创建人", type = String.class),
            @Return2Desc(name = "data[n].eeModifier", comment = "员工修改人", type = String.class),
            @Return2Desc(name = "data[n].eeModifieddate", comment = "员工修改修改时间", type = String.class),
            @Return2Desc(name = "data[n].schoolId", comment = "学校标识", type = String.class),
            @Return2Desc(name = "data[n].schoolName", comment = "学校名称", type = String.class),
            @Return2Desc(name = "data[n].userAvlstatus", comment = "用户可用状态(Y:启用;N:禁用)", type = String.class),
    }))
    public Map listAuth(
            @ParamDesc(comment = "分页起始", validate = Validate.M_STARTINDEX, defVal = "0") Integer pageIndex,
            @ParamDesc(comment = "分页数量", validate = Validate.R_NUM, defVal = "10") Integer pageSize,
            @ParamDesc(comment = "用户名", validate = Validate.M_SAFE, memo = "用户名") String userName,
            @ParamDesc(comment = "员工联系电话", validate = Validate.R_PHONE, memo = "员工联系电话") String eePhone,
            @ParamDesc(comment = "员工联系电话", validate = Validate.R_PHONE, memo = "员工联系电话（销售使用）") String eeInnerphone,
            @ParamDesc(comment = "员工编号", validate = Validate.M_SAFE, memo = "员工编号") String eeNo,
            @ParamDesc(comment = "员工正式状态", validate = "R:[OP]", memo = "[枚举值]O:正式;P:试用") String eeOfficialstatus,
            @ParamDesc(comment = "员工在职状态", validate = "R:[OLW]", memo = "[枚举值]O:在职;L:离职;W:预入职") String eeWorkstatus,
            @ParamDesc(comment = "员工教学身份(C:班主任;T:教研老师)", validate = "R:[CT]", memo = "[枚举]C:班主任;T:教研老师") String eeEdutype,
            @ParamDesc(comment = "部门标识", validate = Validate.R_IDS, memo = "部门标识") String dptId,
            @ParamDesc(comment = "角色id", validate = Validate.M_SAFE, memo = "角色id") String eeRole,
            @ParamDesc(comment = "品牌id", validate = Validate.M_SAFE, memo = "品牌id") String eeBrand,
            @ParamDesc(comment = "员工创建开始时间", validate = Validate.M_YMD, memo = "员工创建时间") String startDate,
            @ParamDesc(comment = "员工创建结束时间", validate = Validate.M_YMD, memo = "员工创建时间") String endDate,
            @ParamDesc(comment = "分校标识", validate = Validate.R_ID, memo = "分校标识") String schoolId,
            Map $params_desc
    ) throws Exception {
        if (pageSize < 1 || pageSize > 500) {
            throw new ApplicationException(UcConst.Errorcode.USER_ARGUMENT_INVALIDE, "pageSize范围:(0,500]", null);
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> where = CoreUtils.getWhere($params_desc);

        // 员工可用状态，删除状态
        where.put("eeAvlstatus", "Y");
        where.put("eeDelstatus", "N");
        if (!isEmpty(startDate)) {
            where.put("startDate", UcStringUtil.format2YMDHms(startDate, "MIN"));
        }
        if (!isEmpty(endDate)) {
            where.put("endDate", UcStringUtil.format2YMDHms(endDate, "MAX"));
        }

        QueryResult qr = ucEeService.queryMapsCount(where, null, null, pageIndex, pageSize, true);
        return BeanUtils.bean2Map(qr);
    }



    /**
     * 员工-修改
     */
    @GlobalLock(expireTime = 60000, key = "ee:+#args[0]")
    @CoreRule(protocol = "edit")
    @MethodDesc(comment = "员工-编辑", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "eeId", comment = "员工标识", type = String.class)
    }))
    @Transactional(rollbackFor = Exception.class)
    public void edit(
            @ParamDesc(comment = "员工标识", validate = Validate.R_ID, length = 36) String eeId,
            @ParamDesc(comment = "员工教学身份(C:班主任;T:教研老师)", validate = "R:[CT]", length = 1) String eeEdutype,
            @ParamDesc(comment = "员工企业微信", validate = Validate.M_SAFE, length = 255) String eeCompanywechat,
            @ParamDesc(comment = "员工权重等级", validate = Validate.M_SAFE, length = 6) String eeWeightgrade,
            @ParamDesc(comment = "当前品牌", validate = Validate.M_SAFE, length = 36) String brandId,
            @ParamDesc(comment = "员工品牌", validate = Validate.M_SAFE, length = 36, unnull = true) List eeBrands,
            @ParamDesc(comment = "员工现住地址", validate = Validate.M_SAFE, length = 255) String eeAdress,
            @ParamDesc(comment = "员工角色", validate = Validate.M_SAFE, memo = "传角色id集合") List eeRoles,
            @ParamDesc(comment = "员工修改者", validate = Validate.M_SAFE) String eeModifier,
            @ParamDesc(comment = "员工百应账号", validate = Validate.R_PHONE) String eeByaccount,
            @ParamDesc(comment = "员工百应账号可用状态(Y:可用;N:不可用)", validate = "R:[YN]", length = 1) String eeByavlstatus,
            @ParamDesc(comment = "标签类型 A答疑老师", validate = "R:[A]", length = 1) String tagType,
            @ParamDesc(comment = "标签启用状态(Y:可用;N:不可用)", validate = "R:[YN]", length = 1) String tagAvlstatus,
            Map $params_desc
    ) throws Exception {
        if ("Y".equals(eeByavlstatus) && StringUtils.isEmpty(eeByaccount)) {
            throw new ApplicationException(ErrorCode.argument_invalide, "启用百应账号时,百应账号必填", null, null);
        }
        BdEe bdEe = ucEeService.findById(eeId);
        if (bdEe == null) {
            throw new ApplicationException(ErrorCode.argument_invalide, "员工不存在", null, null);
        }
        BeanUtils.setBean(bdEe, $params_desc);
        bdEe.setEeModifieddate(DateUtils.getNowDString());

        ucEeService.edit(bdEe, brandId, eeModifier, eeRoles, eeBrands, tagType, tagAvlstatus);
    }
}