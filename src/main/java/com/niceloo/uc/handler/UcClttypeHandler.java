package com.niceloo.uc.handler;

import static org.nobject.common.lang.StringUtils.isEmpty;

import java.util.List;
import java.util.Map;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.MethodDesc;
import org.nobject.common.code.describer.ParamDesc;
import org.nobject.common.code.describer.Return2Desc;
import org.nobject.common.code.describer.ReturnDesc;
import org.nobject.common.code.describer.Validate;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.lang.MapUtils;

import com.niceloo.core.bean.CoreConst.ErrorCode;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.uc.common.UcConst.Avlstatus;
import com.niceloo.uc.model.UcClttype;
import com.niceloo.uc.service.UcClttypeService;

/**
 * 客户端类型-业务处理类
 * <AUTHOR>
 * @Date 2019-09-25
 */
@Service
@CoreRule(protocol = "uc/clttype")
public class UcClttypeHandler {

	@Autowired
	private UcClttypeService ucClttypeService;

	/**
	 * 客户端类型-添加
	 * @param clttypeCode 客户端类型编码
	 * @param clttypeName 客户端类型名称
	 * @param clttypeTerminal 客户端类型终端
	 * @param clttypeDelstatus 客户端类型删除状态
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "add", needLogin = false)
	@MethodDesc(comment = "客户端类型-添加", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "clttypeId", comment = "客户端类型标识", type = String.class)
	}))
	public Map add(
		@ParamDesc(comment="客户端版本类型编码",validate=Validate.M_SAFE,length = 50 ,unnull=true)	String clttypeCode,	
		@ParamDesc(comment="客户端版本类型名称",validate=Validate.M_SAFE,length = 100 ,unnull=true)	String clttypeName,	
		@ParamDesc(comment="客户端版本类型终端",validate=Validate.M_SAFE,length = 20 ,unnull=true)	String clttypeTerminal,	
		Map $params
	)throws Exception {
		UcClttype clttype = ucClttypeService.findByCols(MapUtils.toMap(new Object[][] {
			{"clttypeCode",clttypeCode},
			{"clttypeTerminal",clttypeTerminal},
			{"clttypeDelstatus",Avlstatus.NO}
		}));
		if(clttype != null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "客户端类型已存在!", null, null);
		}
		UcClttype ucClttype = new UcClttype();
		BeanUtils.setBean(ucClttype, $params);
		ucClttype.setClttypeDelstatus(Avlstatus.NO);
		ucClttypeService.add(ucClttype);
		return MapUtils.toMap(new Object[][] { { "clttypeId", ucClttype.getClttypeId() } });
	}

	/**
	 * 客户端类型-删除
	 * @param clttypeId 客户端类型标识
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "delete", needLogin = false)
	@MethodDesc(comment = "客户端类型-删除", returns = @ReturnDesc(subs = {}))
	public void delete(
		@ParamDesc(comment="客户端类型标识",validate=Validate.R_ID,unnull = true, length = 36)	String clttypeId,	
		Map $params
	)throws Exception { 
		UcClttype ucClttype = ucClttypeService.findById(clttypeId);
		if(ucClttype != null){
			ucClttypeService.delete(ucClttype);
		}
	}

	/**
	 * 客户端类型-修改
	 * @param clttypeId 客户端类型标识
	 * @param clttypeCode 客户端类型编码
	 * @param clttypeName 客户端类型名称
	 * @param clttypeTerminal 客户端类型终端
	 * @param clttypeDelstatus 客户端类型删除状态
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "edit", needLogin = false)
	@MethodDesc(comment = "客户端类型-编辑", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "clttypeId", comment = "客户端类型标识", type = String.class)
	}))
	public void edit(
		@ParamDesc(comment="客户端类型标识",validate=Validate.R_ID,length = 36 ,unnull=true)	String clttypeId,	
		@ParamDesc(comment="客户端类型编码",validate=Validate.M_SAFE,length = 50 ,unnull=true)	String clttypeCode,	
		@ParamDesc(comment="客户端类型名称",validate=Validate.M_SAFE,length = 100 ,unnull=true)	String clttypeName,	
		@ParamDesc(comment="客户端类型终端",validate=Validate.M_SAFE,length = 20 ,unnull=true)	String clttypeTerminal,	
		Map $params
	)throws Exception {
		//判重
		UcClttype ucClttype = ucClttypeService.exist(clttypeCode,clttypeTerminal,clttypeId);
		if(ucClttype != null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "客户端编码和终端不能重复!", null, null);
		}
		ucClttype = ucClttypeService.findById(clttypeId);
		if (ucClttype == null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "客户端类型不存在", null, null);
		}
		if(Avlstatus.YES.equals(ucClttype.getClttypeDelstatus())) {
			throw new ApplicationException(ErrorCode.argument_invalide, "已删除的不允许修改", null, null);
		}
		ucClttype.setClttypeCode(clttypeCode);
		ucClttype.setClttypeName(clttypeName);
		ucClttype.setClttypeTerminal(clttypeTerminal);
		ucClttypeService.edit(ucClttype);
	}

	/**
	 * 客户端类型-详情
	 * @param clttypeId 客户端类型标识
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "info", needLogin = false)
	@MethodDesc(comment = "客户端类型-详情", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "clttypeId", comment = "客户端类型标识", type = String.class),
			@Return2Desc(name = "clttypeCode", comment = "客户端类型编码", type = String.class),
			@Return2Desc(name = "clttypeName", comment = "客户端类型名称", type = String.class),
			@Return2Desc(name = "clttypeTerminal", comment = "客户端类型终端", type = String.class),
			@Return2Desc(name = "clttypeDelstatus", comment = "客户端类型删除状态", type = String.class),
	}))
	public Map info(
		@ParamDesc(comment="客户端类型标识",validate=Validate.R_ID,unnull=true,length=36)	String clttypeId,	
		Map $params
	)throws Exception {
		UcClttype ucClttype = ucClttypeService.findById(clttypeId);
		if (ucClttype == null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "客户端类型不存在", null, null);
		}
		//清除掉用户隐私信息
		Map rtn=BeanUtils.bean2Map(ucClttype);
		return rtn;
	}	


	/**
	 * 客户端类型-分页
	 * @param pageIndex 分页起始
	 * @param pageSize 分页数量
	 * @params orderKey 排序字段
	 * @params orderVal 排序字段
	 * @params clttypeId 客户端类型标识
	 * @params clttypeCode 客户端类型编码
	 * @params clttypeName 客户端类型名称
	 * @params clttypeTerminal 客户端类型终端
	 * @params clttypeDelstatus 客户端类型删除状态
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "list", needLogin = false)
	@MethodDesc(comment = "客户端类型-分页", returns = @ReturnDesc(subs = {
		@Return2Desc(name="count",comment="数量",type=Integer.class),
		@Return2Desc(name="data",comment="数据",type=List.class),
		@Return2Desc(name = "data[n].clttypeId", comment = "客户端类型标识", type = String.class),
		@Return2Desc(name = "data[n].clttypeCode", comment = "客户端类型编码", type = String.class),
		@Return2Desc(name = "data[n].clttypeName", comment = "客户端类型名称", type = String.class),
		@Return2Desc(name = "data[n].clttypeTerminal", comment = "客户端类型终端", type = String.class),
		@Return2Desc(name = "data[n].clttypeDelstatus", comment = "客户端类型删除状态", type = String.class),
	}))
	public Map list(
		@ParamDesc(comment="分页起始",validate=Validate.M_STARTINDEX,defVal = "0")	Integer pageIndex,
		@ParamDesc(comment="分页数量",validate=Validate.M_COUNT,defVal = "10")	Integer pageSize,
		@ParamDesc(comment="排序字段")	String orderKey,
		@ParamDesc(comment="排序字段",validate="R:[YN]",memo="[枚举]Y:正序;N:倒序")	String orderVal,
		@ParamDesc(comment="组合条件")	Map searchCombination,
		@ParamDesc(comment="客户端类型标识",validate=Validate.R_ID,memo="客户端版本类型标识")	String clttypeId,
		@ParamDesc(comment="客户端类型编码",validate=Validate.M_SAFE,memo="客户端版本类型编码")	String clttypeCode,
		@ParamDesc(comment="客户端类型名称",validate=Validate.M_SAFE,memo="客户端版本类型名称")	String clttypeName,
		@ParamDesc(comment="客户端类型终端",validate=Validate.M_SAFE,memo="[枚举]PC:PC;ANDROID:安卓;H5:H5;IOS:IOS")	String clttypeTerminal,
		@ParamDesc(comment="客户端类型删除状态",validate=Validate.M_SAFE,memo="客户端类型删除状态")	String clttypeDelstatus,
		Map $params
	)throws Exception {
		
		Map where=CoreUtils.getWhere($params);
		Map order=isEmpty(orderKey)?null:MapUtils.toMap(new Object[][]{
			{orderKey	,orderVal},
		});
		QueryResult qr=ucClttypeService.findMapsCount(where, null, order, pageIndex, pageSize);
		return BeanUtils.bean2Map(qr);
	}
	
	/**
	 * 客户端类型-全部
	 * @params orderKey 排序字段
	 * @params orderVal 排序字段
	 * @params clttypeId 客户端类型标识
	 * @params clttypeCode 客户端类型编码
	 * @params clttypeName 客户端类型名称
	 * @params clttypeTerminal 客户端类型终端
	 * @params clttypeDelstatus 客户端类型删除状态
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "all", needLogin = false)
	@MethodDesc(comment = "客户端类型-全部", returns = @ReturnDesc(subs = {
		@Return2Desc(name="data",comment="数据",type=List.class),
		@Return2Desc(name = "data[n].clttypeId", comment = "客户端类型标识", type = String.class),
		@Return2Desc(name = "data[n].clttypeCode", comment = "客户端类型编码", type = String.class),
		@Return2Desc(name = "data[n].clttypeName", comment = "客户端类型名称", type = String.class),
		@Return2Desc(name = "data[n].clttypeTerminal", comment = "客户端类型终端", type = String.class),
		@Return2Desc(name = "data[n].clttypeDelstatus", comment = "客户端类型删除状态", type = String.class),
	}))
	public Map all(
		@ParamDesc(comment="排序字段")	String orderKey,
		@ParamDesc(comment="排序字段",validate="R:[YN]",memo="[枚举]Y:正序;N:倒序")	String orderVal,
		@ParamDesc(comment="客户端类型标识",validate=Validate.R_IDS,memo="客户端版本类型标识")	String clttypeIds,
		@ParamDesc(comment="客户端类型编码",validate=Validate.M_SAFE,memo="客户端版本类型编码")	String clttypeCode,
		@ParamDesc(comment="客户端类型名称",validate=Validate.M_SAFE,memo="客户端版本类型名称")	String clttypeName,
		@ParamDesc(comment="客户端类型终端",validate=Validate.M_SAFE,memo="[枚举]PC:PC;ANDROID:安卓;H5:H5;IOS:IOS")	String clttypeTerminal,
		@ParamDesc(comment="客户端类型删除状态",validate=Validate.M_SAFE,memo="客户端类型删除状态")	String clttypeDelstatus,
		Map $params
	)throws Exception {
		return ucClttypeService.findAll($params);
	}
}