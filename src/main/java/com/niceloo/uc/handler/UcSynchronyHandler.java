package com.niceloo.uc.handler;

import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.uc.service.UcSynchronyService;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.*;
import org.nobject.common.log.Logger;

import java.util.List;
import java.util.Map;

/**
 * 同步数据-处理类
 *
 * zzz
 * 2020-12-24
 */
@SuppressWarnings({"unused", "Duplicates"})
@Service
@CoreRule(protocol = "uc/synchrony")
public class UcSynchronyHandler {

    /**
     * logger
     */
    private final Logger logger = Logger.getLogger(UcUserAuthHandler.class);

    @Autowired
    private UcSynchronyService ucSynchronyService;

    /**
     * `分校`数据同步
     */
    @CoreRule(protocol = "getschoolbydate/list")
    @MethodDesc(comment = "`分校`数据同步", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "data", comment = "数据", type = List.class),
            @Return2Desc(name = "count", comment = "数量", type = Integer.class),
            @Return2Desc(name = "data[n].schoolId", comment = "分校id", type = String.class),
            @Return2Desc(name = "data[n].schoolName", comment = "分校名称", type = String.class),
            @Return2Desc(name = "data[n].schoolAreacode", comment = "地区编码", type = String.class),
            @Return2Desc(name = "data[n].schoolAvlstatus", comment = "学校禁用状态(Y:可用;N:禁用)", type = String.class),
            @Return2Desc(name = "data[n].schoolDelstatus", comment = "学校删除状态(Y:删除;N:未删除)", type = String.class),
            @Return2Desc(name = "data[n].schoolCreateddate", comment = "创建时间", type = String.class),
            @Return2Desc(name = "data[n].schoolModifieddate", comment = "修改时间", type = String.class),
    }))
    public Map getSchoolModifyDataList(
            @ParamDesc(comment = "起始时间", validate = Validate.M_YMDHMS, unnull = true) String modifiedDate,
            Map $params
    ) throws Exception {
        logger.info("getSchoolModifyDataList开始, 起始时间=" + modifiedDate);
        QueryResult qr = ucSynchronyService.getSchoolModifyDataList(modifiedDate);
        return BeanUtils.bean2Map(qr);
    }

    /**
     * `员工`数据同步
     */
    @CoreRule(protocol = "geteebydate/list")
    @MethodDesc(comment = "`员工`数据同步", returns = @ReturnDesc(subs = {
            @Return2Desc(name = "data", comment = "数据", type = List.class),
            @Return2Desc(name = "count", comment = "数量", type = Integer.class),
            @Return2Desc(name = "data[n].eeId", comment = "员工ID", type = String.class),
            @Return2Desc(name = "data[n].userId", comment = "用户ID", type = String.class),
            @Return2Desc(name = "data[n].eeNo", comment = "员工编号", type = String.class),
            @Return2Desc(name = "data[n].schoolId", comment = "分校标识", type = String.class),
            @Return2Desc(name = "data[n].userName", comment = "用户名", type = String.class),
            @Return2Desc(name = "data[n].eeWorkstatus", comment = "在职状态(O:在职;L:离职;W:预入职;P:试用)", type = String.class),
            @Return2Desc(name = "data[n].eeAvlstatus", comment = "员工可用状态(Y:可用;N:不可用)", type = String.class),
            @Return2Desc(name = "data[n].eeDelstatus", comment = "员工删除状态(Y:删除;N:未删除)", type = String.class),
            @Return2Desc(name = "data[n].eeCreateddate", comment = "创建时间", type = String.class),
            @Return2Desc(name = "data[n].eeModifieddate", comment = "修改时间", type = String.class),
    }))
    public Map getEeModifyDataList(
            @ParamDesc(comment = "起始时间", validate = Validate.M_YMDHMS, unnull = true) String modifiedDate,
            Map $params
    ) throws Exception {
        logger.info("getEeModifyDataList开始, 起始时间=" + modifiedDate);
        QueryResult qr = ucSynchronyService.getEeModifyDataList(modifiedDate);
        return BeanUtils.bean2Map(qr);
    }

}
