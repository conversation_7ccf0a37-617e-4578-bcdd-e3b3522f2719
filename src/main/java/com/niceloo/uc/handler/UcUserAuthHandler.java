package com.niceloo.uc.handler;

import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.uc.common.UcConst.IdAuthType;
import com.niceloo.uc.service.UcUserService;
import com.niceloo.uc.service.UcUserauthService;
import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.MethodDesc;
import org.nobject.common.code.describer.ParamDesc;
import org.nobject.common.code.describer.ReturnDesc;
import org.nobject.common.code.describer.Validate;
import org.nobject.common.lang.ListUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;
import org.nobject.common.log.Logger;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * UcUserHandler
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@CoreRule(protocol = "uc/auth")
public class UcUserAuthHandler {

	/**
	 * logger
	 */
	private Logger logger = Logger.getLogger(UcUserAuthHandler.class);

	/**
	 * UcUserService
	 */
	@Autowired
	private UcUserService ucUserService;

	@Autowired
	private UcUserauthService ucUserauthService;


	/**
	 * 用户列表
	 */
	@CoreRule(protocol = "type/list", needLogin = false)
	@MethodDesc(comment = "认证策略列表", returns = @ReturnDesc(subs = {}))
	public Map listtype() throws Exception {

		/**
		 * 当前仅支持短信认证，后期增加新的认证
		 */
//		QueryResult queryResult = new QueryResult();
//		queryResult.setCount(1);
//		queryResult.setData(ListUtils.toList(new Object[]{
//				MapUtils.toMap(new Object[][]{
//						{IdAuthType.sms.code, IdAuthType.sms.name}
//				})
//		}));

		return MapUtils.toMap(new Object[][]{
				{"data",ListUtils.toList(new Object[]{
						MapUtils.toMap(new Object[][]{
								{"code",IdAuthType.sms.code},
								{"name",IdAuthType.sms.name}
						})
				})}
		});
	}

	/**
	 * 用户列表
	 */
	@CoreRule(protocol = "type/user", needLogin = false)
	@MethodDesc(comment = "用户认证策略", returns = @ReturnDesc(subs = {}))
	public Map list_i(
			@ParamDesc(comment = "用户id", validate = Validate.R_ID,unnull = true) String userId)
			throws Exception {

		String[] authTypeByUser = ucUserauthService.getAuthTypeByUser(userId);

//		QueryResult queryResult = new QueryResult();
//		queryResult.setCount(authTypeByUser.length);
//		queryResult.setData(ListUtils.toList(authTypeByUser));

		return  MapUtils.toMap(new Object[][]{
				{"data",ListUtils.toList(authTypeByUser)}
		});
	}

	/**
	 * 设置用户的认证策略
	 *
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "type/user/set", needLogin = false)
	@MethodDesc(comment = "设置用户的认证策略", returns = @ReturnDesc(subs = {}))
	public void setUserBrand(
			@ParamDesc(comment = "用户id", validate = Validate.R_ID, length = 36, unnull = true) String userId,
			@ParamDesc(comment = "认证策略集合", validate = Validate.M_SAFE, memo = "wx,msg(如果没有，传空或none)") String typeIds,
			Map $params
	) throws Exception {
		Set<String> authtypes = new HashSet<>();
		if (typeIds != null && !"".equals(typeIds) && !"none".equalsIgnoreCase(typeIds)) {
			String[] split = typeIds.split(",");
			for (String s : split) {
				if (!StringUtils.isEmpty(s.trim())) {
					authtypes.add(s.trim().toLowerCase());
				}
			}
		}
		ucUserauthService.saveAuthTypeByUser(userId, authtypes);
		return;
	}

}
