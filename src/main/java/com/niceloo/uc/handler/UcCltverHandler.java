package com.niceloo.uc.handler;

import static org.nobject.common.lang.StringUtils.isEmpty;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.BeanUtils;
import org.nobject.common.bean.Service;
import org.nobject.common.code.describer.MethodDesc;
import org.nobject.common.code.describer.ParamDesc;
import org.nobject.common.code.describer.Return2Desc;
import org.nobject.common.code.describer.ReturnDesc;
import org.nobject.common.code.describer.Validate;
import org.nobject.common.db.Transactional;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.ObjectUtils;
import org.nobject.common.lang.StringUtils;

import com.niceloo.core.bean.CoreConst;
import com.niceloo.core.bean.CoreConst.ErrorCode;
import com.niceloo.core.dao.QueryResult;
import com.niceloo.core.utils.CoreRule;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.uc.common.UcConfig.Uk;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.common.UcConst.Avlstatus;
import com.niceloo.uc.common.UcConst.Delstatus;
import com.niceloo.uc.common.UcConst.IterType;
import com.niceloo.uc.common.UcConst.PublishType;
import com.niceloo.uc.model.UcClttype;
import com.niceloo.uc.model.UcCltver;
import com.niceloo.uc.model.UcUser;
import com.niceloo.uc.service.UcCltgrayService;
import com.niceloo.uc.service.UcClttypeService;
import com.niceloo.uc.service.UcCltverService;
import com.niceloo.uc.service.UcUserService;
import com.niceloo.uc.utils.AESUtils;

/**
 * 客户端版本-业务处理类
 * <AUTHOR>
 * @Date 2019-09-25
 */
@Service
@CoreRule(protocol = "uc/cltver")
public class UcCltverHandler {

	@Autowired
	private UcCltverService ucCltverService;
	@Autowired
	private UcClttypeService ucClttypeService;
	@Autowired
	private UcCltgrayService ucCltgrayService;
	@Autowired
	private UcUserService ucUserService;
	/**
	 * 客户端版本-添加
	 * @param clttypeId 客户端类型标识
	 * @param cltverMemo 客户端版本描述
	 * @param cltverAddress 客户端版本地址
	 * @param cltverCode 客户端版本编号
	 * @param cltverPublishtype 客户端版本发布类型
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "add", needLogin = false)
	@MethodDesc(comment = "客户端版本-添加", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "cltverId", comment = "客户端版本标识", type = String.class)
	}))
	public Map add(
		@ParamDesc(comment="客户端版本类型标识",validate=Validate.R_ID,length = 36 ,unnull=true)	String clttypeId,	
		@ParamDesc(comment="客户端版本描述",validate=Validate.M_SAFE,length = 2000 ,unnull=false)	String cltverMemo,	
		@ParamDesc(comment="客户端版本地址",validate=Validate.M_SAFE,length = 200 ,unnull=false)	String cltverAddress,	
		@ParamDesc(comment="客户端版本号",validate=Validate.M_SAFE,length = 20 ,unnull=true)	String cltverCode,	
		@ParamDesc(comment="用户标识",validate=Validate.R_ID,length = 36 ,unnull=true)	String userId,	
		@ParamDesc(comment="用户姓名",validate=Validate.M_SAFE,length = 200 ,unnull=true)	String userName,
		@ParamDesc(comment="客户端版本市场审核状态",validate=Validate.M_SAFE,length = 1 ,defVal = "N",unnull=false)	String cltverMarkcheckstatus,
		Map $params
	)throws Exception {
		UcClttype clttype = ucClttypeService.findById(clttypeId);
		if(clttype == null || Delstatus.YES.equals(clttype.getClttypeDelstatus())) {
			throw new ApplicationException(CoreConst.ErrorCode.argument_invalide,"客户端类型不存在!",null);
		}
		if(ucCltverService.valdateClverSeq(clttypeId,cltverCode)) {
			throw new ApplicationException(ErrorCode.argument_invalide, "该版本号过小!", null, null);
		}
		UcCltver ucCltver = new UcCltver();
		BeanUtils.setBean(ucCltver, $params);
		ucCltver.setCltverCreater(userId);
		ucCltver.setCltverCreatername(userName);
		ucCltver.setCltverCreatedate(DateUtils.getNowDString());
		ucCltver.setCltverPublishtype(PublishType.NOT);//未发布
		ucCltver.setCltverDelstatus(UcConst.Delstatus.NO);//未删除
		ucCltver.setCltverAvlstatus(UcConst.Avlstatus.YES);//可用
		ucCltver.setCltverDelaymaxday(0);//设置默认延迟天数为0天
		ucCltverService.add(ucCltver);
		return MapUtils.toMap(new Object[][] { { "cltverId", ucCltver.getCltverId() } });
	}

	/**
	 * 客户端版本-删除
	 * @param cltverId 客户端版本标识
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "delete", needLogin = false)
	@MethodDesc(comment = "客户端版本-删除", returns = @ReturnDesc(subs = {}))
	public void delete(
		@ParamDesc(comment="客户端版本标识",validate=Validate.R_ID,unnull = true, length = 36)	String cltverId,
		@ParamDesc(comment="用户标识",validate=Validate.R_ID,length = 36 ,unnull=true)	String userId,	
		@ParamDesc(comment="用户姓名",validate=Validate.M_SAFE,length = 200 ,unnull=true)	String userName,
		Map $params
	)throws Exception { 
		UcCltver ucCltver = ucCltverService.findById(cltverId);
		if(ucCltver != null){
			ucCltver.setCltverModifieddate(DateUtils.getNowDString());
			ucCltver.setCltverModifier(userId);
			ucCltver.setCltverModifiername(userName);
			ucCltver.setCltverDelstatus(Avlstatus.YES);
			ucCltverService.edit(ucCltver);
		}
	}

	/**
	 * 客户端版本-修改
	 * @param cltverId 客户端版本标识
	 * @param clttypeId 客户端类型标识
	 * @param cltverMemo 客户端版本描述
	 * @param cltverAddress 客户端版本地址
	 * @param cltverCode 客户端版本编号
	 * @param cltverPublishtype 客户端版本发布类型
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "edit", needLogin = false)
	@MethodDesc(comment = "客户端版本-编辑", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "cltverId", comment = "客户端版本标识", type = String.class)
	}))
	@Transactional
	public void edit(
		@ParamDesc(comment="客户端版本标识",validate=Validate.R_ID,length = 36 ,unnull=true)	String cltverId,
		@ParamDesc(comment="客户端版本描述",validate=Validate.M_SAFE,length = 2000 ,unnull=false)	String cltverMemo,	
		@ParamDesc(comment="客户端版本地址",validate=Validate.M_SAFE,length = 200 ,unnull=false)	String cltverAddress,
		@ParamDesc(comment="客户端版本编号",validate=Validate.M_SAFE,length = 200 ,unnull=true)	String cltverCode,
		@ParamDesc(comment="客户端版本发布类型",validate="R:[NGC]",length = 1 ,unnull=true)	String cltverPublishtype,
		@ParamDesc(comment="客户端版本迭代类型",validate="R:[FCDW]",length = 1 ,unnull=false)	String cltverItertype,
		@ParamDesc(comment="客户端版本包类型",validate="R:[AP]",length = 1 ,unnull=false)	String cltverPackagetype,
		@ParamDesc(comment="用户标识",validate=Validate.R_ID,length = 36 ,unnull=true)	String userId,	
		@ParamDesc(comment="用户姓名",validate=Validate.M_SAFE,length = 200 ,unnull=true)	String userName,	
		@ParamDesc(comment="客户端版本市场审核状态",validate="R:[YN]",length = 1 ,unnull=true)	String cltverMarkcheckstatus,
		@ParamDesc(comment="版本延迟天数",validate=Validate.R_NUM,unnull = false,defVal="0", length = 2,memo= "延迟选择迭代最大延迟天数(默认值0天)")				int cltverDelaymaxday,
		Map $params
	)throws Exception {
		if(!UcConst.IterType.DELAY.equals(cltverItertype)) {
			cltverDelaymaxday = 0;
		}
		UcCltver ucCltver = ucCltverService.findById(cltverId);
		if (ucCltver == null || Delstatus.YES.equals(ucCltver.getCltverDelstatus())) {
			throw new ApplicationException(ErrorCode.argument_invalide, "客户端版本不存在", null, null);
		}
		//判断是否已发布
		String cltverPublishtypeHis = ucCltver.getCltverPublishtype();
		//未发布
		if(UcConst.PublishType.NOT.equals(cltverPublishtypeHis)) {
			if(!cltverCode.equals(ucCltver.getCltverCode())) {
				//比较两个版本号大小
				if(UcCltverService.compareVersion(ucCltver.getCltverCode(),cltverCode)) {
					throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "版本号必须大于或等于当前版本", null);
				}
			}
			String cltverPublishtypehis = ucCltver.getCltverPublishtype();
			if(!cltverPublishtypehis.equals(cltverPublishtype)) {
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "未发布的版本不允许修改发布类型", null);
			}
		}else {
			if(!cltverCode.equals(ucCltver.getCltverCode())) {
				throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "已发布的版本不允许修改版本号", null);
			}else {
				String cltverItertype2 = ucCltver.getCltverItertype();
				if(UcConst.IterType.FORCE.equals(cltverItertype2) && !cltverItertype2.equals(cltverItertype)) {
					throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "强制迭代不允许修改为其他迭代类型", null);
				}
				//未修改发布类型
				if(cltverPublishtypeHis.equals(cltverPublishtype)) {
					//判断当前发布类型是否是灰度发布
					if(!cltverPublishtypeHis.equals(UcConst.PublishType.GRAY)) {
						//获取非灰度最大升级版本信息
						valMaxCode(cltverCode, cltverItertype, ucCltver);
					}
					//修改发布类型
				}else {
					if(UcConst.PublishType.NOT.equals(cltverPublishtype)) {
						throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "已发布的版本不允许修改为未发布", null);
					}else if(UcConst.PublishType.GRAY.equals(cltverPublishtype)) {
						throw new ApplicationException(CoreConst.ErrorCode.argument_invalide, "普通发布不允许修改灰度发布", null);
					}else {
						valMaxCode(cltverCode, cltverItertype, ucCltver);
					}
				}
			}
		}
		ucCltver.setCltverItertype(cltverItertype);
		ucCltver.setCltverPackagetype(cltverPackagetype);
		ucCltver.setCltverPublishtype(cltverPublishtype);
		ucCltver.setCltverCode(cltverCode);
		ucCltver.setCltverMemo(cltverMemo);
		ucCltver.setCltverAddress(cltverAddress);
		ucCltver.setCltverModifieddate(DateUtils.getNowDString());
		ucCltver.setCltverModifier(userId);
		ucCltver.setCltverModifiername(userName);
		ucCltver.setCltverMarkcheckstatus(cltverMarkcheckstatus);
		ucCltver.setCltverDelaymaxday(cltverDelaymaxday);
		ucCltverService.edit(ucCltver);
	}

	/**
	 * 判断普通发布最大版本
	 * @param cltverCode
	 * @param cltverItertype
	 * @param ucCltver
	 * @throws DBException
	 */
	public void valMaxCode(String cltverCode, String cltverItertype, UcCltver ucCltver) throws DBException {
		UcCltver findMaxCltverNotGary = ucCltverService.createMaxCltverNoGaryCacha(ucCltver.getClttypeId());
		if(findMaxCltverNotGary == null) {
			return;
		}
		//获得非灰度版本最大版本号
		String maxCode = findMaxCltverNotGary.getCltverCode();
		//判断当前版本号是否是最大
		
		//判断是否是强制升级
		if(cltverItertype.equals(UcConst.IterType.FORCE)) {
			//判断是否是当前最大版本号
			if(!UcCltverService.compareVersion(maxCode,cltverCode)) {
				ucCltverService.editHisAvlNo(ucCltver);
			}else {
				List<UcCltver> cltverList = ucCltverService.findCltverByClttypeId(ucCltver.getClttypeId());
				if(cltverList != null && cltverList.size() > 0) {
					ArrayList<UcCltver> updateCltver = new ArrayList<>();
					for (UcCltver ucCltver2 : cltverList) {
						if(UcCltverService.compareVersion(cltverCode,ucCltver2.getCltverCode())) {
							ucCltver2.setCltverAvlstatus(UcConst.Avlstatus.NO);
							updateCltver.add(ucCltver2);
						}
					}
					ucCltverService.editCltvers(cltverList);
				}
			}
		}
		
		
		if(!UcCltverService.compareVersion(maxCode,cltverCode)) {
			if(cltverItertype.equals(UcConst.IterType.FORCE)) {
				//修改历史所有版本都为不可用
				ucCltverService.editHisAvlNo(ucCltver);
			}
		}
	}

	/**
	 * 客户端版本-详情
	 * @param cltverId 客户端版本标识
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "info", needLogin = false)
	@MethodDesc(comment = "客户端版本-详情", returns = @ReturnDesc(subs = {
			@Return2Desc(name = "cltverId", comment = "客户端版本标识", type = String.class),
			@Return2Desc(name = "clttypeId", comment = "客户端类型标识", type = String.class),
			@Return2Desc(name = "cltverMemo", comment = "客户端版本描述", type = String.class),
			@Return2Desc(name = "cltverAddress", comment = "客户端版本地址", type = String.class),
			@Return2Desc(name = "cltverCode", comment = "客户端版本编号", type = String.class),
			@Return2Desc(name = "cltverPackagetype", comment = "客户端版本包类型", type = String.class),
			@Return2Desc(name = "cltverPublishtype", comment = "客户端版本发布类型", type = String.class),
			@Return2Desc(name = "cltverPublishdate", comment = "客户端版本发布时间", type = String.class),
			@Return2Desc(name = "cltverItertype", comment = "客户端版本迭代类型",memo="[枚举]C:选择迭代;F:强制迭代", type = String.class),
			@Return2Desc(name = "cltverDelstatus", comment = "客户端版本删除状态", type = String.class),
			@Return2Desc(name = "cltverAvlstatus", comment = "客户端版本可用状态", type = String.class),
			@Return2Desc(name = "cltverCreatedate", comment = "客户端版本创建时间", type = String.class),
			@Return2Desc(name = "cltverCreater", comment = "客户端版本创建人", type = String.class),
			@Return2Desc(name = "cltverCreatername", comment = "客户端版本创建人姓名", type = String.class),
			@Return2Desc(name = "cltverModifieddate", comment = "客户端版本修改时间", type = String.class),
			@Return2Desc(name = "cltverModifier", comment = "客户端版本修改人", type = String.class),
			@Return2Desc(name = "cltverModifiername", comment = "客户端版本修改人姓名", type = String.class),
			@Return2Desc(name = "cltverMarkcheckstatus", comment = "客户端版本市场审核状态", type = String.class),
			@Return2Desc(name = "cltverDelaymaxday", comment = "客户端版本延迟最大天数", type = String.class),
	}))
	public Map info(
		@ParamDesc(comment="客户端版本标识",validate=Validate.R_ID,unnull=true,length=36)	String cltverId,	
		Map $params
	)throws Exception {
		UcCltver ucCltver = ucCltverService.findById(cltverId);
		if (ucCltver == null) {
			throw new ApplicationException(ErrorCode.argument_invalide, "客户端版本不存在", null, null);
		}
		String clttypeId = ucCltver.getClttypeId();
		UcClttype clttype = ucClttypeService.findById(clttypeId);
		Map map0 = MapUtils.toMap0(ucCltver);
		map0.put("clttypeName", clttype.getClttypeName());
		return map0;
	}	

	/**
	 * 客户端版本-分页
	 * @param pageIndex 分页起始
	 * @param pageSize 分页数量
	 * @params orderKey 排序字段
	 * @params orderVal 排序字段
	 * @params cltverId 客户端版本标识
	 * @params clttypeId 客户端类型标识
	 * @params cltverMemo 客户端版本描述
	 * @params cltverAddress 客户端版本地址
	 * @params cltverCode 客户端版本编号
	 * @params cltverPublishtype 客户端版本发布类型
	 * @params cltverDelstatus 客户端版本删除状态
	 * @params cltverCreatedate 客户端版本创建时间
	 * @params cltverCreater 客户端版本创建人
	 * @params cltverCreatername 客户端版本创建人姓名
	 * @params cltverModifieddate 客户端版本修改时间
	 * @params cltverModifier 客户端版本修改人
	 * @params cltverModifiername 客户端版本修改人姓名
	 * @param $params 附加参数
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "list", needLogin = false)
	@MethodDesc(comment = "客户端版本-分页", returns = @ReturnDesc(subs = {
		@Return2Desc(name="count",comment="数量",type=Integer.class),
		@Return2Desc(name="data",comment="数据",type=List.class),
		@Return2Desc(name = "data[n].cltverId", comment = "客户端版本标识", type = String.class),
		@Return2Desc(name = "data[n].clttypeId", comment = "客户端类型标识", type = String.class),
		@Return2Desc(name = "data[n].cltverMemo", comment = "客户端版本描述", type = String.class),
		@Return2Desc(name = "data[n].cltverAddress", comment = "客户端版本地址", type = String.class),
		@Return2Desc(name = "data[n].cltverCode", comment = "客户端版本编号", type = String.class),
		@Return2Desc(name = "data[n].cltverPackagetype", comment = "客户端版本包类型" ,type = String.class),
		@Return2Desc(name = "data[n].cltverPublishtype", comment = "客户端版本发布类型",memo="[枚举]N:未发布;G:灰度发布;C:普通发布", type = String.class),
		@Return2Desc(name = "data[n].cltverItertype", comment = "客户端版本迭代类型",memo="[枚举]C:选择迭代;F:强制迭代", type = String.class),
		@Return2Desc(name = "data[n].cltverPublishdate", comment = "客户端版本发布时间", type = String.class),
		@Return2Desc(name = "data[n].cltverDelstatus", comment = "客户端版本删除状态", type = String.class),
		@Return2Desc(name = "data[n].cltverAvlstatus", comment = "客户端版本可用状态", type = String.class),
		@Return2Desc(name = "data[n].cltverCreatedate", comment = "客户端版本创建时间", type = String.class),
		@Return2Desc(name = "data[n].cltverCreater", comment = "客户端版本创建人", type = String.class),
		@Return2Desc(name = "data[n].cltverCreatername", comment = "客户端版本创建人姓名", type = String.class),
		@Return2Desc(name = "data[n].cltverModifieddate", comment = "客户端版本修改时间", type = String.class),
		@Return2Desc(name = "data[n].cltverModifier", comment = "客户端版本修改人", type = String.class),
		@Return2Desc(name = "data[n].cltverModifiername", comment = "客户端版本修改人姓名", type = String.class),
		@Return2Desc(name = "data[n].cltverMarkcheckstatus", comment = "客户端版本市场审核状态", type = String.class),
		@Return2Desc(name = "data[n].cltverDelaymaxday", comment = "客户端版本延迟最大天数", type = String.class),
	}))
	public Map list(
		@ParamDesc(comment="分页起始",validate=Validate.M_STARTINDEX,defVal = "0")	Integer pageIndex,
		@ParamDesc(comment="分页数量",validate=Validate.M_COUNT,defVal = "10")	Integer pageSize,
		@ParamDesc(comment="排序字段" ,defVal = "cltverCreatedate")	String orderKey,
		@ParamDesc(comment="排序字段",validate="R:[YN]",memo="[枚举]Y:正序;N:倒序" ,defVal="N")	String orderVal,
		@ParamDesc(comment="组合条件")	Map searchCombination,
		@ParamDesc(comment="客户端版本标识",validate=Validate.R_ID,memo="客户端版本标识")	String cltverId,
		@ParamDesc(comment="客户端类型标识",validate=Validate.R_ID,memo="客户端版本类型标识")	String clttypeId,
		@ParamDesc(comment="客户端版本描述",validate=Validate.M_SAFE,memo="客户端版本描述")	String cltverMemo,
		@ParamDesc(comment="客户端版本地址",validate=Validate.M_SAFE,memo="客户端版本地址")	String cltverAddress,
		@ParamDesc(comment="客户端版本编号",validate=Validate.M_SAFE,memo="客户端版本编号")	String cltverCode,
		@ParamDesc(comment="客户端版本升级类型",validate="R:[AP]",memo="[枚举]A:全部;P:部分")	String cltverPackagetype,
		@ParamDesc(comment="客户端版本发布类型",validate=Validate.M_SAFE,memo="客户端版本发布类型")	String cltverPublishType,
		Map $params
	)throws Exception {
		Map where=CoreUtils.getWhere($params);
		Map order=isEmpty(orderKey)?null:MapUtils.toMap(new Object[][]{
			{orderKey	,orderVal},
		});
		QueryResult qr=ucCltverService.findMapsCount(where, null, order, pageIndex, pageSize);
		return BeanUtils.bean2Map(qr);
	}
	
	/**
	 * 客户端版本-删除
	 * @param cltverId 客户端版本标识
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "publish", needLogin = false)
	@MethodDesc(comment = "客户端版本-发布", returns = @ReturnDesc(subs = {}))
	public void publish(
		@ParamDesc(comment="客户端版本标识",validate=Validate.R_ID,unnull = true, length = 36)	String cltverId,
		@ParamDesc(comment="客户端版本包类型",validate="R:[AP]",unnull = true, length = 1,memo="[枚举]A:全部;P:部分")											String cltverPackagetype,
		@ParamDesc(comment="客户端版本迭代类型",validate=Validate.M_SAFE,unnull = true, length = 1,memo= "[枚举]F:强制迭代;C:选择迭代;D:延迟选择迭代;W:不提示选择迭代")	String cltverItertype,
		@ParamDesc(comment="发布类型",validate=Validate.M_SAFE,unnull = true, length = 1,memo= "[枚举]N:未发布;C:普通发布;G:灰度发布")								String publishType,
		@ParamDesc(comment="版本延迟天数",validate=Validate.R_NUM,unnull = false,defVal="0", length = 2,memo= "延迟选择迭代最大延迟天数(默认值0天)")				int cltverDelaymaxday,
		@ParamDesc(comment="用户标识",validate=Validate.R_ID,length = 36 ,unnull=true)	String userId,
		@ParamDesc(comment="用户姓名",validate=Validate.M_SAFE,length = 200 ,unnull=true)	String userName,
		Map $params
	)throws Exception {
		UcCltver ucCltver = ucCltverService.findById(cltverId);
		if(ucCltver != null){
			if(Avlstatus.YES.equals(ucCltver.getCltverDelstatus())) {
				throw new ApplicationException(ErrorCode.argument_invalide, "已删除的版本不允许发布!", null, null);
			}
			if(!PublishType.NOT.equals(ucCltver.getCltverPublishtype())) {
				throw new ApplicationException(ErrorCode.argument_invalide, "已经发布的版本不允许重新发布!", null, null);
			}
			ucCltver.setCltverPackagetype(cltverPackagetype);
			ucCltver.setCltverPublishtype(publishType);
			ucCltver.setCltverPublishdate(DateUtils.getNowDString());
			ucCltver.setCltverModifieddate(DateUtils.getNowDString());
			ucCltver.setCltverModifier(userId);
			ucCltver.setCltverModifiername(userName);
			ucCltver.setCltverItertype(cltverItertype);
			ucCltver.setCltverDelaymaxday(cltverDelaymaxday);
			ucCltverService.publish(ucCltver);
		}else {
			throw new ApplicationException(ErrorCode.argument_invalide, "客户端版本不存在!", null, null);
		}
	}
	
	/**
	 * 客户端版本-校验
	 * @param cltverId 客户端版本标识
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "check", needLogin = false)
	@MethodDesc(comment = "客户端版本-检查", returns = @ReturnDesc(subs = {
			@Return2Desc(name="updateType",				comment="客户端升级类型",			type=String.class),
			@Return2Desc(name="cltverCode",				comment="客户端版本号",			type=String.class),
			@Return2Desc(name="cltverPackagetype",		comment="客户端版本包类型",			type=String.class),
			@Return2Desc(name="cltverAddress",			comment="客户端版本包地址",			type=String.class),
			@Return2Desc(name="cltverMemo",				comment="客户端版本描述",			type=String.class),
			@Return2Desc(name="cltverMarkcheckstatus", 	comment = "客户端版本市场审核状态", 	type = String.class),
			@Return2Desc(name="cltverDelaymaxday", 		comment = "客户端版本延迟最大天", 	type = String.class),
	}))
	public Map check(
		@ParamDesc(comment="客户端类型编码",validate=Validate.M_SAFE,length = 50 ,unnull=true)	String clttypeCode,	
		@ParamDesc(comment="客户端类型终端",validate=Validate.M_SAFE,length = 50 ,unnull=true)	String clttypeTerminal,
		@ParamDesc(comment="客户端版本号",validate=Validate.M_SAFE,length = 20 ,unnull=true)	String cltverCode,
		@ParamDesc(comment="用户标识",validate=Validate.R_ID,length = 36 ,unnull=false)		String userId,	
		@ParamDesc(comment="终端标识",validate=Validate.M_SAFE,length = 200 ,unnull=false)	String terminalId
	)throws Exception {
		Map resoutMap = new HashMap<>();
		UcClttype ucClttype = ucClttypeService.findByCols(MapUtils.toMap(new Object[][] {
			{"clttypeCode",clttypeCode},
			{"clttypeTerminal",clttypeTerminal},
			{"clttypeDelstatus",Avlstatus.NO}
		}));
		if(ucClttype == null) {
			resoutMap.put("updateType", PublishType.NOT);
			resoutMap.put("cltverMarkcheckstatus", "N");
			resoutMap.put("cltverDelaymaxday", 0);
		}else {
			resoutMap = ucCltverService.check(ucClttype,cltverCode,userId,terminalId);
			//查询当前正在运行的版本的版本信息       这里需要判断审核中     如果是需要升级的话还需要判断当前版本是否是可用,如果不可用升级提醒需要改变为强制升级
			UcCltver ucCltver = ucCltverService.queryCltverByTypeidCodeDel(ucClttype.getClttypeId(),cltverCode,Delstatus.NO);
			String updateType = (String) resoutMap.get("updateType");
			if(ObjectUtils.isEmpty(ucCltver)) {
				resoutMap.put("cltverMarkcheckstatus", "N");
				if(!PublishType.NOT.equals(updateType)) {
					resoutMap.put("updateType",IterType.FORCE);
				}
			}else {
				resoutMap.put("cltverMarkcheckstatus", StringUtils.isEmpty(ucCltver.getCltverMarkcheckstatus())?"N":ucCltver.getCltverMarkcheckstatus());
				//升级类型重定义 如果上个版本是不可用(不包括不存在)这次的升级版本必须是强制迭代
				//判断本次是否是要升级  如果需要升级的话就根据上个版本的可用状态判,如果是不可用  当前版本需要修改为强制迭代 
				if(!PublishType.NOT.equals(updateType) && UcConst.Avlstatus.NO.equals(ucCltver.getCltverAvlstatus())) {
					resoutMap.put("updateType",IterType.FORCE);
				}
			}
		}
		return resoutMap;
	}
	
	/**
	 * 客户端版本-校验
	 * @param cltverId 客户端版本标识
	 * @return
	 * @throws Exception
	 */
	@CoreRule(protocol = "uk/check", needLogin = false)
	@MethodDesc(comment = "客户端版本-检查", returns = @ReturnDesc(subs = {
			@Return2Desc(name="updateType",			comment="客户端升级类型",		type=String.class),
			@Return2Desc(name="cltverCode",			comment="客户端版本号",		type=String.class),
			@Return2Desc(name="cltverPackagetype",	comment="客户端版本包类型",		type=String.class),
			@Return2Desc(name="cltverAddress",		comment="客户端版本包地址",		type=String.class),
			@Return2Desc(name="cltverMemo",			comment="客户端版本描述",		type=String.class),
			@Return2Desc(name = "cltverMarkcheckstatus", comment = "客户端版本市场审核状态", type = String.class),
	}))
	public Map uk_check(
		@ParamDesc(comment="客户端类型编码",validate=Validate.M_SAFE,length = 50 ,unnull=true)	String clttypeCode,	
		@ParamDesc(comment="客户端类型终端",validate=Validate.M_SAFE,length = 50 ,unnull=true)	String clttypeTerminal,
		@ParamDesc(comment="客户端版本号",validate=Validate.M_SAFE,length = 20 ,unnull=true)	String cltverCode,
		@ParamDesc(comment="终端标识",validate=Validate.M_SAFE,length = 200 ,unnull=false)	String terminalId,
		@ParamDesc(comment="ukey",validate=Validate.R_ID ,unnull=false)		String uk,	
		@ParamDesc(comment = "请求端", validate = Validate.M_SAFE) 			String type,
		@ParamDesc(comment = "用户类型", validate = "R:[SAI]", memo = "[枚举]S:学生;A:代理商;I:内部用户") 	String userFlag
	)throws Exception {
		String userId = "";
		if(!StringUtils.isEmpty(uk)) {
			userId = getUserIdByuk(uk,type,userFlag);
		}
		return check(clttypeCode, clttypeTerminal, cltverCode, userId, terminalId);
	}

	/**
	 * 根据UK获取用户标识
	 * @param uk
	 * @param type
	 * @param userFlag
	 * @return
	 * @throws Exception
	 */
	private String getUserIdByuk(String uk, String type,String userFlag) throws Exception {
		//解密的时候注意是Android ios 和网站端的key salt 和迭代次数
		String userSourceid = "";
		if("A".equals(type)) {
			userSourceid = AESUtils.ukAesDecrypt(uk, Uk.android_key, Uk.android_salt, Uk.android_iteration);
		}else if("I".equals(type)) {
			userSourceid=AESUtils.ukAesDecrypt(uk, Uk.ios_key, Uk.ios_salt, Uk.ios_iteration);
		}else if("C".equals(type)){
			userSourceid=AESUtils.ukAesDecrypt(uk, Uk.client_key, Uk.client_salt, Uk.client_iteration);
			String[] split = userSourceid.split("&");
			userSourceid = split[0];
		}else{
			userSourceid=UcLoginHandler.webEncrypt(uk);
		}
		UcUser user = ucUserService.queryBySourceid$Flag$Avlstatus(userSourceid, userFlag, null);
		if (user == null) {
			user=ucUserService.syncUserToNet(userSourceid);
		}
		if(user != null) {
			return user.getUserId();
		}
		return "";
	}
	
}