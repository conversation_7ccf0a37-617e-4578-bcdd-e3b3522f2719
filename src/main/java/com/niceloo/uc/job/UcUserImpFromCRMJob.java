package com.niceloo.uc.job;

import static org.nobject.common.lang.ObjectUtils.isEmpty;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

import org.nobject.common.bean.Autowired;
import org.nobject.common.bean.Service;
import org.nobject.common.log.Logger;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.utils.CoreUtils;
import com.niceloo.uc.common.UcConst.Datatype;
import com.niceloo.uc.model.UcUser;
import com.niceloo.uc.service.UcUserService;

/**
 * UcUserImpFromCRMJob
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class UcUserImpFromCRMJob implements Runnable{
	
	/** commonDao_crm */
	@Autowired
	private CommonDao commonDao_crm;
	
	/** commonDao */
	@Autowired
	private CommonDao commonDao;
	
	/** logger */
	private Logger logger=Logger.getLogger(UcUserImpFromCRMJob.class);
	
	private String lastSyncDate=null;
	
	/** ucUserService */
	@Autowired
	private UcUserService ucUserService;
	
	/** 下次轮询时常 */
	long interval_next=0;
	
	/** 轮询最大时常 */
	long interval_max=60*1000;
	
	/** count */
	public CountDownLatch count;
	
	/** 
	 * suspend
	 */
	public void suspend(){
		if(count==null){
			count=new CountDownLatch(1);
		}
	}
	
	/** 
	 * resume
	 */
	public void resume(){
		if(count!=null){
			count.countDown();
		}
	}
	
	/**
	 * @see java.lang.Runnable#run()
	 */
	@Override
	public void run() {
		
		while(true){
			
			//
			if(count!=null){
				try {
					count.await();
					count=null;
				} catch (InterruptedException e) {
					logger.error("未预期异常："+e.getMessage(),e);
				}
			}
			
			//首次更新时间
			if(lastSyncDate==null){
				try {
					lastSyncDate=commonDao.queryString("SELECT MAX( userModifieddate) FROM UcUser WHERE userModifiedtype=? ",new Object[]{Datatype.CRM_IMPORT});
				} catch (Exception e) {
					CoreUtils.setRunningStatus(e);
					logger.error("获取更新时间失败："+e.getMessage(),e);
					return;
				}
			}
			
			String sql_where="";
			if(!isEmpty(lastSyncDate)){
				sql_where	=" AND ModifiedOn> CONVERT(datetime,'"+lastSyncDate+"', 120)";
			}
			
			try {
				List<Map> users=commonDao_crm.queryMaps("SELECT * from SYS_User WHERE 1=1 "+sql_where+" ORDER BY ModifiedOn ASC ", 0, 100, new Object[]{}, null);
				for(Map mUser:users){
					UcUser user=ucUserService.impFromCRM(mUser, null);
					lastSyncDate=user.getUserModifieddate();
				}
				
				if(users.size()>0){
					interval_next=50;
				}else{
					interval_next=interval_max;
				}
			} catch (Exception e) {
				logger.error("[CRM用户定时同步]"+e.getMessage(),e);
				interval_next=Math.min(interval_max, interval_next+1000);
			}
			
			try {
				Thread.sleep(interval_next);
			} catch (Exception e) {
				// TODO: handle exception
			}
		}
	}
	
	
	
	public static void main(String[] args) throws Exception {
		
	}
	
}
