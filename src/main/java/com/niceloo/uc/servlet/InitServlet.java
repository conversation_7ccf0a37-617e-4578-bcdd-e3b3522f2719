package com.niceloo.uc.servlet;

import javax.servlet.ServletContext;

import com.niceloo.core.job.utils.JobUtils;
import com.niceloo.uc.service.UcLoginipService;
import com.niceloo.uc.utils.MqUtils;

import org.nobject.common.db.DBFactory;
import org.nobject.common.exception.DBException;
import org.nobject.common.file.PropertiesUtils;
import org.nobject.common.lang.ClassUtils;
import org.nobject.common.lang.ListUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;
import org.nobject.common.mvc.MVCServlet;

import com.niceloo.core.bean.CoreConfig.Api;
import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.lock.service.LockService;
import com.niceloo.core.mq.MQServiceFactory;
import com.niceloo.core.mq.MQUtils;
import com.niceloo.core.mvc.MVCUtils;
import com.niceloo.core.redis.RedisClient;
import com.niceloo.core.servlet.CoreServlet;
import com.niceloo.uc.common.UcConfig;
import com.niceloo.uc.common.UcConst;
import com.niceloo.uc.job.UcUserImpFromCRMJob;

/**
 * InitServlet
 *
 * <AUTHOR>
 * @version 1.0
 */
public class InitServlet extends CoreServlet {

	/**
	 * @see com.niceloo.core.servlet.CoreServlet#onInit(javax.servlet.ServletContext)
	 */
	@Override
	protected void onInit(ServletContext servletContext) throws Exception {
		super.onInit(servletContext);
	}

	/**
	 * @see com.niceloo.core.servlet.CoreServlet#onPropsReaded()
	 */
	@Override
	public void onPropsReaded() throws Exception {
		super.onPropsReaded();

		//
		ClassUtils.mappingStaticFields(UcConfig.class,
				PropertiesUtils.toMap(MVCServlet.context.beanFactory.properties));
/*
		//
		buildCommonDao("uc_read");
		//
		buildCommonDao("uc_write");*/
		buildCoreDao("uc");

		//
		buildCommonDao("bd_write");
		//
		buildCommonDao("crm_write");
		//
		buildCommonDao("crm_read");
		//
		buildCommonDao("fd_read");

		buildCommonDao("ipg_read");

		initRedis();
		
		buildBean("mqService", MQServiceFactory.getService(null));
		
		MqUtils.userNameEditMqMessage(MQServiceFactory.getService("crm"));
		MQUtils.bindSubscriber();
	}

	private void initRedis() throws Exception {
		
		String host = MVCUtils.getProperty("redis.uk.host");//地址
		String port = MVCUtils.getProperty("redis.uk.port");//端口号
		String password = MVCUtils.getProperty("redis.uk.password");//密码
		String maxTotal = MVCUtils.getProperty("redis.uk.maxTotal","10");//最大连接数
		if(StringUtils.isEmpty(host)||StringUtils.isEmpty(port)||StringUtils.isEmpty(password)) {
			throw new Exception("redis未配置");
		}
		UcConst.redis=new RedisClient(host, Integer.parseInt(port), password);
		UcConst.redis.setMaxTotal(Integer.parseInt(maxTotal));
		UcConst.redis.init();
	}

	/**
	 * @see com.niceloo.core.servlet.CoreServlet#onLoaded()
	 */
	@Override
	protected void onLoaded() throws Exception {
		super.onLoaded();

		//
		initApis();

		Api.modus = ListUtils
				.toList(new Object[] { MapUtils.toMap(new Object[][] {
						{ "name", "uc" },
						{ "comment", "用户中心" }
				}),
						MapUtils.toMap(new Object[][] {
								{ "name", "hy" },
								{ "comment", "会员中心" }
						})
				});



		// ORM
		CommonDao commonDao_uc_write = (CommonDao)MVCUtils.getBean("commonDao_uc_write");
		DBFactory dbFactory_uc_write = commonDao_uc_write.getDbFactory();
		try {
			dbFactory_uc_write.setPath(ListUtils.toList(new Object[] { "com/niceloo/uc/**/model/*" }));
		} catch (Exception e) {
			throw new Exception(e);
		}
	
		
		
		CommonDao commonDao_uc_read = (CommonDao)MVCUtils.getBean("commonDao_uc_read");
		DBFactory dbFactory_uc_read  = commonDao_uc_read.getDbFactory();
		try {
			dbFactory_uc_read.setPath(ListUtils.toList(new Object[] { "com/niceloo/uc/**/model/*" }));
		} catch (Exception e) {
			throw new Exception(e);
		}
		
		CommonDao commonDao_fd_read = (CommonDao)MVCUtils.getBean("commonDao_fd_read");
		DBFactory dbFactory_fd_read  = commonDao_fd_read.getDbFactory();
		try {
			dbFactory_fd_read.setPath(ListUtils.toList(new Object[] { "com/niceloo/*/model/*" }));
		} catch (Exception e) {
			throw new Exception(e);
		}

		CommonDao commonDao_bd_write = (CommonDao)MVCUtils.getBean("commonDao_bd_write");
		DBFactory dbFactory_bd_write  = commonDao_bd_write.getDbFactory();
		try {
			dbFactory_bd_write.setPath(ListUtils.toList(new Object[] { "com/niceloo/*/model/*" }));
		} catch (Exception e) {
			throw new Exception(e);
		}

		CommonDao commonDao_ipg_read = (CommonDao)MVCUtils.getBean("commonDao_bd_write");
		DBFactory dbFactory_ipg_read  = commonDao_ipg_read.getDbFactory();
		try {
			dbFactory_ipg_read.setPath(ListUtils.toList(new Object[] { "com/niceloo/*/model/*" }));
		} catch (Exception e) {
			throw new Exception(e);
		}

		//
		if ("true".equals(MVCUtils.getProperty("job.impFromCRM.enable"))) {
			new Thread(MVCUtils.getBean(UcUserImpFromCRMJob.class), "用户定时同步CRM").start();
		}

		//定时任务 清理临时ip
		JobUtils.scanJobs();

	}
}
