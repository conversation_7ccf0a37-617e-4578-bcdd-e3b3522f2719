package com.niceloo.uc.utils;
import java.net.URLEncoder;
import java.util.Map;

import org.nobject.common.exception.ApplicationException;
import org.nobject.common.http.HttpClient;
import org.nobject.common.js.JSONUtils;
import org.nobject.common.log.Logger;

/**      
* 类名称：HttpClientUtil   
* 类描述： 远程调用接口
* 创建人：haoyang   
* 创建时间：2019年2月14日 上午10:00:17   
* @version        
*/
public class HttpClientUtils {

	/** logger */
	private static Logger logger=Logger.getLogger(HttpClientUtils.class);
	
	/**
	 * @param url
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public static Object post(String url,Object obj) throws Exception{
		return post(url, obj, "","");
	}
	
	/**
	 * 请求
	 * @param url
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public static Object post(String url,Object obj,String prelog,String eventId) throws Exception {
		 HttpClient client	=	new HttpClient();
		 String str_req		=	JSONUtils.toString(obj);
		 str_req	 		=	"params="+URLEncoder.encode(""+str_req, "UTF-8")+"&ctype=MSA";
		 client.postHTML(url, str_req, "UTF-8");
		 String str_res		=	client.getReponseText();

		 if(str_res==null){
			 throw new ApplicationException("系统访问失败:"+url);
		 }
		 
		 Map res			=	JSONUtils.toMap(str_res);
		 String code		=	(String)res.get("code");
		 Object data		=	res.get("data");
		
		 return data;
	}
}
