package com.niceloo.uc.utils;

import java.math.BigDecimal;
import java.util.Iterator;
import java.util.Map;
import java.util.UUID;

import org.nobject.common.encrypt.MD5Utils;
import org.nobject.common.encrypt.SHA1Utils;
import org.nobject.common.exception.ApplicationException;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;

import com.niceloo.uc.common.UcConst;

/**
 * 用户工具类
 * <AUTHOR>
 *
 */
public class UserUtils {
	/** 
 	 * 获取加密密码
 	 * @param pwd
 	 * @param pwdtype
 	 * @return
 	 */
 	public static String getEncryptPwd(String pwd,String pwdtype) {
 		
 		
 		if(UcConst.UserLoginpwdtype.SHA1.equals(pwdtype)){
 			return SHA1Utils.encrypt(pwd);
 		}else if(UcConst.UserLoginpwdtype.MD5.equals(pwdtype)){
 			return MD5Utils.encrypt(pwd);
 		}
 		
 		throw new RuntimeException("密码格式不支持:"+pwdtype);
 	}
 	
 	/** formData分隔符 */
 	public static String BOUNDARY = "---------------------------123821742118716"; 

 	/**
 	 * 拼接formData请求参数
 	 * @param params
 	 * @return
 	 */
 	public static String joinFormdata(Map<String, String> params) {
 		String req= "";
 		if (params != null) {
 			StringBuffer strBuf = new StringBuffer();
 			Iterator iter = params.entrySet().iterator();
 			while (iter.hasNext()) {
 				Map.Entry entry = (Map.Entry) iter.next();
 				String inputName = (String) entry.getKey();
 				String inputValue = (String) entry.getValue();
 				if (inputValue == null) {
 					continue;
 				}
 				strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
 				strBuf.append("Content-Disposition: form-data; name=\"" + inputName + "\"\r\n\r\n");
 				strBuf.append(inputValue);
 			}
 			strBuf.append("\r\n--" + BOUNDARY + "--\r\n");
 			req =strBuf.toString();
 		}
 		return req;
 	}
 	
 	//(?![0-9]+$)表示排除掉只有数字的组合，
 	//(?![a-zA-Z]+$)表示排除掉只有字母的组合
 	//(?![\\W]+$)排除数字特殊符号
 	//(.{8,})8位以上密码
	/**密码正则表达式*/
	public static final String R_PWD = "^(?![0-9]+$)(?![a-zA-Z]+$)(?![\\W]+$)(.{8,})";
	
	/**
	 * 登陆密码安全校验
	 * @param pwd
	 * @return
	 * @throws ApplicationException 
	 */
	public static void pwdVerify(String pwd) throws ApplicationException {
		if(StringUtils.isEmpty(pwd)&&!pwd.matches(R_PWD)) {
			throw new ApplicationException(UcConst.Errorcode.USER_PWD_INVALIDE,"密码必须由字母、数字和特殊字符组成，且长度不小于8位",null);
		}
	}
	
	/**
	 * 获取认证信息MQ头
	 * @return
	 */
	public static Map getMqHeard(String topical) {
		String guId = UUID.randomUUID().toString();
		return MapUtils.toMap(new Object[][] {
			{"cap-msg-id",guId},
			{"cap-msg-name",topical},
			{"cap-msg-type",""},
			{"cap-corr-id",guId},
			{"cap-corr-seq","0"},
			{"cap-senttime",DateUtils.getNowDString()},
			{"x-max-length","1000000"},
			{"x-max-length-bytes","1073741824"}
		});
	}
	
	
	public static String arr = "lpymkz2tsrxd58ahb94n61vgjw3i7uqo0fce";

	public static BigDecimal pow = new BigDecimal(arr.length());
	
	/**
	 * 10进制转换成62进制
	 * @param str
	 * @return
	 */
	public static String change10TO62(String str) {
		str = str.replaceAll("[a-zA-Z]", "");
		str = str.substring(10, str.length())+str.substring(0,10);
		BigDecimal bigDecimal = new BigDecimal(str);
		StringBuilder sb = new StringBuilder("");
		while (bigDecimal.compareTo(pow) >= 0) {
			BigDecimal[] bigDecimals = bigDecimal.divideAndRemainder(pow);
			bigDecimal = bigDecimals[0];
			int intValue = bigDecimals[1].intValue();
			sb.insert(0, arr.charAt(intValue));
		}
		sb.insert(0, arr.charAt(bigDecimal.intValue()));
		return sb.toString();
	}

	/**
	 * 62进制转换成10进制
	 * @param str
	 * @return
	 */
	public static String change62To10(String str) {
		BigDecimal bigDecimal1 = new BigDecimal(0);
		for (int i = 0; i < str.length(); i++) {
			char c = str.charAt(i);
			int i1 = arr.indexOf(c);
			BigDecimal pow1 = pow.pow(str.length() - i - 1);
			BigDecimal multiply = BigDecimal.valueOf(i1).multiply(pow1);
			bigDecimal1 = bigDecimal1.add(multiply);
		}
		String string = bigDecimal1.toString();
		string = string.substring(string.length()-10)+StringUtils.fillEmptyWithStr(Long.parseLong(string.substring(0,string.length()-10)), 10, "0");
		return string;
	}
}
