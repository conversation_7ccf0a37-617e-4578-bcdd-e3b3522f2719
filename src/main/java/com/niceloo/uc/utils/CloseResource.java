package com.niceloo.uc.utils;

/**
 * 关闭资源工具
 *
 * <AUTHOR> Created in 18:02:13 2018-11-1 0001
 */
public final class CloseResource {

    /**
     * 安静地关闭
     *
     * @param arg0 继承自AuthCloseable的实例
     */
    public static void closeQuietly(AutoCloseable... arg0) {
        for (AutoCloseable node : arg0) {
            try {
                node.close();
            } catch (Exception e) {
                // No-op
            }
        }
    }

}
