package com.niceloo.uc.utils;

import java.util.Map;

import org.nobject.common.exception.ApplicationException;
import org.nobject.common.lang.MapUtils;

import com.niceloo.core.utils.CoreUtils;
import com.niceloo.uc.common.UcConst.Errorcode;

/**
 * 证件认证工具类
 *
 */
public class IDCardUtils {

	/**
	 * 身份证认证校验
	 * @param cardNo  	证件号
	 * @param realName 	真实姓名
	 * @param url		认证服务地址
	 * @param api		认证API
	 */
	public static void idcardValidate(String cardNo,String realName,String url,String api) throws Exception {
		Map data = (Map) CoreUtils.request(url, api, MapUtils.toMap(new Object[][] {
			{"cardNo",cardNo},
			{"realName",realName}
		}), "UC", "ED");
		int code = (int) data.get("code");
		if( 0 != code) {
			throw new ApplicationException(Errorcode.USER_IDCARD_ERROR,"证件号码错误,请重新填写!",null);
		}
	}
	
	/**
	 * 港澳通行证校验
	 * @throws ApplicationException 
	 */
	public static void hongKongValidate(String cardNo) throws ApplicationException {
		//
		if(!cardNo.matches("^[HMhm]{1}([0-9]{9,10})$")) {
			throw new ApplicationException(Errorcode.USER_IDCARD_ERROR,"证件号码错误,请重新填写!",null);
		}
	}
	
	/**
	 * 台胞证校验
	 * @throws ApplicationException 
	 */
	public static void taiWanValidate(String cardNo) throws ApplicationException {
		if(!cardNo.matches("^[0-9]{8}$")) {
			throw new ApplicationException(Errorcode.USER_IDCARD_ERROR,"证件号码错误,请重新填写!",null);
		}
	}
	
	/**
	 * 军官证校验
	 * @param userIdcard
	 * @throws ApplicationException 
	 */
	public static void officerValidate(String cardNo) throws ApplicationException {
		if(!cardNo.matches("([0-9|X]){18}")) {
			throw new ApplicationException(Errorcode.USER_IDCARD_ERROR,"证件号码错误,请重新填写!",null);
		}
	}
	
}
