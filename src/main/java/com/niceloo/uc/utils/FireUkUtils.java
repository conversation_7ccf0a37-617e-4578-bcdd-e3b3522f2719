package com.niceloo.uc.utils;

import org.nobject.common.encrypt.DesUtils;

public class FireUkUtils {

	/**
	 * 解密
	 * @param key 		秘钥
	 * @param content	加密字符串
	 * @return
	 * @throws Exception
	 */
	public static String decryptString(String key, String content) throws Exception{
    	byte[] bytes = new byte[content.length()/2];
    	for (int i = 0; i < content.length()/2; i++) {
    		int parseInt = Integer.parseInt(content.substring(i*2, 2*(i+1)),16);
    		bytes[i] = (byte) parseInt;
		}
    	byte[] decrypt = DesUtils.decrypt(bytes,key);
        return new String(decrypt,"GB2312");
     }
   
}
