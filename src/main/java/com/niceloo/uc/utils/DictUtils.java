package com.niceloo.uc.utils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.nobject.common.lang.CollectionUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.ObjectUtils;
import org.nobject.common.lang.StringUtils;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.mq.MQPackage;
import com.niceloo.core.mq.MQService;
import com.niceloo.core.mq.MQTopicContext;
import com.niceloo.core.mq.MQTopicContext.Ack;
import com.niceloo.core.mq.MQTopicHandler;
import com.niceloo.core.mvc.MVCUtils;
import com.niceloo.uc.service.UcDictService;

/** 用户字典工具 */
public class DictUtils {
	
	/** MQService标识 */
	public static String mqServiceId		="mqService";
	
	/** 字典缓存 */
	private static Map<String,List> dicts	=new HashMap();
	
	/** 订阅 */
	private static boolean subscribed		=false;
	/** 地区 */
	private static String AREA="area";
	/**
	 * 获取字典
	 * @param dictType 字典类型
	 * @param dictCode 字典编码 
	 */
	public static Map getDict(String dictType,String dictCode) throws Exception{
		return getDict(dictCode, getDicts(dictType));
	}
	
	private static Map getDict(String dictCode,List<Map> dicts) throws Exception{
		for(Map dict:dicts) {
			if(dict.get("dictCode").equals(dictCode)) {
				return dict;
			}
			
			List children=(List)dict.get("children");
			if(CollectionUtils.isEmpty(children)) {
				continue;
			}
			
			Map rtn=getDict(dictCode, children);
			if(rtn!=null) {
				return rtn;
			}
		}
		return null;
	}
	
	
	/**
	 * 获取字典列表
	 * @throws Exception
	 */
	public static List getDicts(String dictType) throws Exception {

		//记载
		if(!dicts.containsKey(dictType)) {
			synchronized (dicts) {
				//加载Dict
				UcDictService dictService = MVCUtils.getBean(UcDictService.class);
				Map queryDictTree = dictService.queryDictTree(MapUtils.toMap(new Object[][] {
					{"dicttype",dictType}
				}));
				List dictList=(List)queryDictTree.get("data");
				//缓存
				dicts.put(dictType, dictList);
				//订阅事件
				if(!subscribed) {
					MQService mqService=(MQService)MVCUtils.getBean(mqServiceId);
					String group="niceloo_"+StringUtils.removeStartEnd(CoreConfig.Uri.context, "/","/");
					String clusterSeq=CoreConfig.Sys.clusterSeq;
					mqService.subscribe("youlu_uc_dict_edit","dictquery"+group+clusterSeq, new DictMQTopicHandler());
					subscribed=true;
				}
			}
		}
		
		return dicts.get(dictType);
	}
	//字典对象中的地区编码
	private static final String areaCode="dictCode";
	//字段对象中的地区名称
	private static final String areaName="dictName";
	//字典对象中的子类
	private static final String dictClildren="children";
	/**;
	  *  分析地址，生成国家编码、地区编码、村庄编码
	 * @param address 地区
	 */

	public static Map analyzeAddress(String address) throws Exception{
		
		String areacode		=null;//地区编码
		//分析
		List provinceList = getDicts(AREA);
		//拿到省
		for (Object province : provinceList) {
			Map provinceMap=(Map) province;
			String dictMemo=(String) provinceMap.get(areaName);
			if(-1 != address.indexOf(dictMemo)|| -1 != address.indexOf(dictMemo.replace("省", ""))||-1 != address.indexOf(dictMemo.replace("市", ""))) {
				areacode = (String) provinceMap.get(areaCode);
				List cityList = (List) provinceMap.get(dictClildren);
				if(cityList==null) {
					break;
				}
				if(cityList.size()==1) {
					//是市辖区类似于北京
					Map cityMap= (Map) cityList.get(0);
					List areaList = (List) cityMap.get(dictClildren);
					if(areaList == null) {
						break;
					}
					for (Object area : areaList) {
						Map areaMap=(Map) area;
						String cityName=(String) areaMap.get(areaName);
						if(-1 != address.indexOf(cityName)|| -1 != address.indexOf(cityName.replace("市", ""))||-1 != address.indexOf(cityName.replace("区", ""))) {
							areacode = (String) areaMap.get(areaCode);
							break;
						}
						
					}
					break;
				}else {
					for (Object city : cityList) {
						Map cityMap=(Map) city;
						dictMemo=(String) cityMap.get(areaName);
						if(-1 != address.indexOf(dictMemo)|| -1 != address.indexOf(dictMemo.replace("市", ""))) {
							areacode = (String) cityMap.get(areaCode);
							List areaList = (List) cityMap.get(dictClildren);
							if(areaList == null) {
								break;
							}
							for (Object area : areaList) {
								Map areaMap=(Map) area;
								if(-1 != address.indexOf((String) areaMap.get(areaName))) {
									areacode = (String) areaMap.get(areaCode);
									break;
								}
							}
							break;
						}
					}
				}
				break;
			}
		}
		//返回一个地区编码
		return MapUtils.toMap(new Object[][] {
			{"areacode"		,areacode},
		});
	}
	
	/**
	 * 根据地区编码解析地址信息
	 * @param code 地区编码
	 * @return
	 * @throws Exception
	 */
	public static String analyzeCode(String code) throws Exception {
		if(StringUtils.isEmpty(code)) {
			return "";
		}
		return analyze(getDicts(AREA), code);
	}
	
	private static String analyze(List dicts,String code) {
		for (Object object : dicts) {
			Map dict=(Map) object;
			String dictCode = (String) dict.get(areaCode);
			String name = (String) dict.get(areaName);
			if(code.equals(dictCode)) {
				return name;
			}else {
				List clildren = (List)dict.get(dictClildren);
				if(ObjectUtils.isEmpty(clildren)) {
					continue;
				}else {
					String analyze = analyze(clildren, code);
					if(StringUtils.isEmpty(analyze)) {
						continue;
					}else {
						return name + analyze;
					}
				}
			}
		}
		return "";
	}
	
	/**
	 * 替换最后字符串为0
	 * @param str
	 * @param length
	 * @return
	 */
	private static String replacelastlength(String str , int length) {
		if(str.length()<length+2) {
			return "";
		}
		String substring = str.substring(0, str.length()-length);
		return leftAdd(substring,length,"0");
	}
	
	/**
	 * 右侧补齐
	 * @param str
	 * @param i
	 * @param addStr
	 * @return
	 */
	private static String leftAdd(String str,int i,String addStr) {
		for (int j = 0; j < i; j++) {
			str+=addStr;
		}
		return str;
	}
	
	/** 字典订阅 */
	static class DictMQTopicHandler implements MQTopicHandler{

		/* (non-Javadoc)
		 * @see com.niceloo.core.mq.MQTopicHandler#handle(com.niceloo.core.mq.MQTopicContext)
		 */
		@Override
		public Ack handle(MQTopicContext context) throws Exception {
			MQPackage mqPackage	=context.getContentPackage();
			Map data			=mqPackage.getDataMap();
			String dictType		=(String)data.get("dictType");
			dicts.remove(dictType);
			//重新加载
			getDicts(dictType);
			return null;
		}
	}
	/**
	 * 
	 * @param codeList  字典编码code的list集合
	 * @param dictType  字典类型
	 * @param hasChildren  是否要子
	 * @return  字典集合
	 * @throws Exception
	 */
	public static ArrayList<Map> getDictsByCodes(List<String> codeList,String dictType,boolean hasChildren) throws Exception {
		ArrayList<Map> arrayList = new ArrayList<>();
		
		List<Map> dicts = getDicts(dictType);
		getDictsByCodes(arrayList, codeList, dicts,hasChildren);
		return arrayList;
	}
	//递归查询字典
	public static void getDictsByCodes(ArrayList<Map> arrayList,List<String> codeList,List<Map> dicts, boolean hasChildren) {
		for (Map dict : dicts) {
			String dictCode = (String)dict.get("dictCode");
			boolean contains = codeList.contains(dictCode);
			if(contains) {
				if(hasChildren) {
					arrayList.add(dict);
				}else {
					Map newMap = new HashMap<>();
					MapUtils.copy(newMap, dict, true);
					newMap.remove("children");
					arrayList.add(newMap);
				}
			}
			List children=(List)dict.get("children");
			if(!CollectionUtils.isEmpty(children)) {
				getDictsByCodes(arrayList, codeList, children,hasChildren);
			}
			if(arrayList.size()==codeList.size()) {
				continue;
			}
		}
	}
	
	/**
	 * 根据名称模糊查询字典数据
	 * @param dictType  字典类型
	 * @param dictName	字典名称
	 * @return
	 * @throws Exception
	 */
	public static ArrayList<Map> likeDictsByName(String dictType,String dictName) throws Exception {
		ArrayList<Map> resoutList = new ArrayList<>();
		if(StringUtils.isEmpty(dictType)) {
			return resoutList;
		}else {
			likeDict(dictName,getDicts(dictType),resoutList);
			return resoutList;
		}
	}
	
	/**
	 * 模糊查询子类的字典数据
	 * @param dictName
	 * @param dicts
	 * @param resoutList
	 * @throws Exception
	 */
	private static void likeDict(String dictName,List<Map> dicts,List resoutList) throws Exception{
		for(Map dict:dicts) {
			String name = (String)dict.get("dictName");
			if(StringUtils.isEmpty(dictName) || name.indexOf(dictName)!= -1) {
				resoutList.add(dict);
			}
			List children=(List)dict.get("children");
			if(CollectionUtils.isEmpty(children)) {
				continue;
			}
			likeDict(dictName, children,resoutList);
		}
	}
	
}
