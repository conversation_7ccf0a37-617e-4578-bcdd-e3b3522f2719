package com.niceloo.uc.utils;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * http请求工具类
 *
 * <AUTHOR>
 * @version 1.0
 */
public class HttpUtils {

	/**
	 * 发起http post请求
	 * 
	 * @param uri   请求路径
	 * @param parmert   参数
	 * @param charset  编码
	 * @return 字符串
	 * @throws Exception
	 */
	public static String post(String uri, String parmert, String charset) throws Exception {
		HttpURLConnection connection=null;
		OutputStream outputStream=null;
		BufferedOutputStream bufferedOutputStream=null;
		PrintWriter pw=null;
		InputStream inputStream=null;
		InputStreamReader inputStreamReader=null;
		BufferedReader br=null;
		try {
			URL url = new URL(uri);
			connection = (HttpURLConnection) url.openConnection();
			
			connection.setDoInput(true); // 设置可输入
			connection.setDoOutput(true); // 设置该连接是可以输出的
			connection.setRequestMethod("POST"); // 设置请求方式
			connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=" + charset);
			outputStream = connection.getOutputStream();
			bufferedOutputStream = new BufferedOutputStream(outputStream);
			pw = new PrintWriter(bufferedOutputStream);
			// 编码
			pw.write(parmert);
			pw.flush();
			inputStream= connection.getInputStream();
			inputStreamReader = new InputStreamReader(inputStream, charset);
			br = new BufferedReader(inputStreamReader);
			String line = null;
			StringBuilder result = new StringBuilder();
			while ((line = br.readLine()) != null) { // 读取数据
				result.append(line + "\n");
			}
			return result.toString();
		} finally {
			if(pw != null) {
				pw.close();
			}
			if(bufferedOutputStream != null) {
				bufferedOutputStream.close();
			}
			if(outputStream != null) {
				outputStream.close();
			}
			if(br != null) {
				br.close();
			}
			if(inputStreamReader != null) {
				inputStreamReader.close();
			}
			if(inputStream != null) {
				inputStream.close();
			}
			if(connection != null) {
				connection.disconnect();
			}
		}
	}

	/**
	 * 发起 http get请求
	 * 
	 * @param uri  请求路径
	 * @param parmert 请求参数 就是url ?后的
	 * @param charset 编码
	 * @return 请求的返回值
	 * @throws Exception
	 */
	public static String get(String uri, String parmert, String charset) throws Exception {
		HttpURLConnection connection=null;
		InputStream inputStream=null;
		InputStreamReader inputStreamReader=null;
		BufferedReader br=null;
		try {
			URL url = new URL(uri + "?" + parmert);
			connection = (HttpURLConnection) url.openConnection();
			
			connection.setDoOutput(true); // 设置该连接是可以输出的
			connection.setRequestMethod("GET"); // 设置请求方式
			connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=" + charset);
			inputStream = connection.getInputStream();
			inputStreamReader = new InputStreamReader(inputStream, charset);
			br = new BufferedReader(inputStreamReader);
			String line = null;
			StringBuilder result = new StringBuilder();
			while ((line = br.readLine()) != null) { // 读取数据
				result.append(line + "\n");
			}
			return result.toString();
		} finally {
			if(br != null) {
				br.close();
			}
			if(inputStreamReader != null) {
				inputStreamReader.close();
			}
			if(inputStream != null) {
				inputStream.close();
			}
			if(connection != null) {
				connection.disconnect();
			}
		}
	}

	public static void main(String[] args) throws Exception {
		String string = new String("[{\"api\":\"SC0101\",\"params\":{}}]".getBytes(),"utf-8");
		String post = get("http://192.168.11.206:8082/midservice/api",
				"directives="+string, "utf-8");
		System.out.println(post);
	}
}
