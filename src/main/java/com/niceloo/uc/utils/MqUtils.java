package com.niceloo.uc.utils;

import java.util.Map;

import org.nobject.common.exception.ApplicationException;
import org.nobject.common.js.JSONUtils;
import org.nobject.common.lang.StringUtils;

import com.niceloo.core.mq.MQPackage;
import com.niceloo.core.mq.MQService;
import com.niceloo.core.mq.MQTopicContext;
import com.niceloo.core.mq.MQTopicHandler;
import com.niceloo.core.mq.MQTopicContext.Ack;
import com.niceloo.core.mvc.MVCUtils;
import com.niceloo.uc.service.UcUserService;

public class MqUtils {

	public static void userNameEditMqMessage(MQService mqService) throws Exception {
		UserNameMQTopicHandler handler = new UserNameMQTopicHandler();
		mqService.subscribe("ChangeNameIntegrationEvent", "userEdit", handler);
	}
	/** 字典订阅 */
	static class UserNameMQTopicHandler implements MQTopicHandler{

		/* (non-Javadoc)
		 * @see com.niceloo.core.mq.MQTopicHandler#handle(com.niceloo.core.mq.MQTopicContext)
		 */
		@Override
		public Ack handle(MQTopicContext context) throws Exception {
			String contentString = context.getContentString();
			if(!StringUtils.isEmpty(contentString)) {
				Map data = JSONUtils.toMap(contentString);
				String userId		=(String)data.get("UserId");
				String userName		=(String)data.get("Name");
				//重新加载
				UcUserService ucUserService = MVCUtils.getBean(UcUserService.class);
				try {
					ucUserService.nameEdit(userId, userName);
				} catch (ApplicationException e) {
					e.printStackTrace();
				}
			}
			return null;
			
		}
	}
}
