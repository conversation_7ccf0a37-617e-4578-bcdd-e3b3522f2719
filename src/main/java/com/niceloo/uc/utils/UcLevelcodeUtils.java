package com.niceloo.uc.utils;

public class UcLevelcodeUtils {
	
	/**
	 * 是否是父节点
	 * @param parent_levelcode
	 * @param child_levelcode
	 */
	public static boolean isParent(String parent_levelcode,String child_levelcode){
		return child_levelcode.substring(0, child_levelcode.length()-10).equals(parent_levelcode);
	}
	
	/**
	 * 是否同父
	 * @param levelcode0
	 * @param levelcode1
	 */
	public static boolean isSameParent(String levelcode0,String levelcode1){
		return levelcode0.substring(0, levelcode0.length()-10).equals(levelcode1.substring(0, levelcode1.length()-10));
	}
	
	/**
	 * 获取父基层编码
	 * @param levelcode
	 * @return
	 */
	public static String getParentLevelcode(String levelcode){
		return levelcode.substring(0, levelcode.length()-10);
	}
	
}
