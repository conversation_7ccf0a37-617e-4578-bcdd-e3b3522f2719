package com.niceloo.uc.common;

import org.nobject.common.exception.ApplicationException;
import org.nobject.common.exception.UnSupportException;
import org.nobject.common.lang.StringUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/4/14 20:36
 */
public final class UcStringUtil {

    /**
     * 清除字符串中的空字符串
     * eg: ,A,B,C,,,, D ,,  ->A,B,C,D
     *
     * @param target 源数据
     * @param delimiter 分割符
     * @return String 分割后的字符串
     */
    public static String removeEmptyString(String target, String delimiter) throws ApplicationException {
        List<String> result = new LinkedList<>();
        if (Objects.isNull(delimiter)) {
            return target;
        }
        if (isEmpty(target)) {
            throw new ApplicationException("源数据不能为空");
        } else {
            String[] eles = target.split(delimiter);
            for (String ele : eles) {
                String reviseEle = ele.trim();
                if (!isEmpty(reviseEle)) {
                    result.add(reviseEle);
                }
            }
        }
        return String.join(delimiter, result);
    }

    /**
     * 判断字符串是否为空
     *
     * @param target 源数据
     * @return true/false
     */
    public static boolean isEmpty(String target) {
        int strLen;
        if (target == null || (strLen = target.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if ((!Character.isWhitespace(target.charAt(i)))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 根据类型获取时间最小值和最大值
     * @param strDate
     * @param type MIN/MAX
     * @return
     * @throws UnSupportException
     */
    public static String format2YMDHms(String strDate,String type) throws UnSupportException {

        if (StringUtils.isEmpty(strDate)) {
            throw new RuntimeException("时间字符串不能为空");
        } else {
            strDate = strDate.trim();

            if (strDate.matches("\\d{4}")) {
                if(type.equals("MIN")){
                    strDate = strDate + "-01-01 00:00:00";
                }else if(type.equals("MAX")){
                    strDate = strDate + "-01-01 23:59:59";
                }
            } else if (strDate.matches("\\d{4}-\\d{1,2}")) {
                if(type.equals("MIN")) {
                    strDate = strDate + "-01 00:00:00";
                }else if(type.equals("MAX")){
                    strDate = strDate + "-01 23:59:59";
                }
            } else if (strDate.matches("\\d{4}-\\d{1,2}-\\d{1,2}")) {
                if(type.equals("MIN")) {
                    strDate = strDate + " 00:00:00";
                }else if(type.equals("MAX")){
                    strDate = strDate + " 23:59:59";
                }
            } else if (strDate.matches("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}")) {
                if(type.equals("MIN")) {
                    strDate = strDate + ":00";
                }else if(type.equals("MAX")){
                    strDate = strDate + ":59";
                }
            } else if (strDate.matches("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}:\\d{1,2}")) {
            }
            return strDate;
        }
    }

}
