package com.niceloo.uc.common;

import org.nobject.common.lang.MapUtils;

import java.util.Map;

public class UcConfig {

	/**
	 * 用户
	 */
	public static class User {

		/** 密码加密方式_默认 */
		public static String loginpwdtype_default = "SHA1";
		
		/**默认密码 */
		public static String loginpwd_default	= "123456";

	}

	/**
	 * 网站配置
	 *
	 */
	public static class Net{
		/**地址*/
		public static String address;
		/**用户API*/
		public static class User{
			/**用户信息*/
			public static String info;
			/**Ukey*/
			public static String ukey;
			/**注册*/
			public static String regist;
			/**修改密码*/
			public static String pwd_edit;
		}
	}
	public static class Uk {
		/**加密key*/
		public static String key;
		/**盐*/
		public static String salt;
		/**迭代次数 */
		public static int iteration;
		/**ANDROID加密KEY*/
		public static String android_key;
		/**ANDROID盐*/
		public static String android_salt;
		/**ANDROID迭代次数*/
		public static int android_iteration;
		/**IOS加密KEY*/
		public static String ios_key;
		/**IOS盐*/
		public static String ios_salt;
		/**IOS迭代次数*/
		public static int ios_iteration;
		/**redis中存储用户登录信息的key*/
		public static String rediskey;
		/**IOS加密KEY*/
		public static String client_key;
		/**IOS盐*/
		public static String client_salt;
		/**IOS迭代次数*/
		public static int client_iteration;
		/**消防解密key*/
		public static String fire_key;
	}
	
	public static class Area{
		/**市辖区编码*/
		public static String mdCode = "110100,120100,310100,500100,500200";
	}
	
	public static class Ed{
		/**地址*/
		public static String address;
		/**身份认证接口路径*/
		public static String identify;
		/** 手机号归属地接口路径 */
		public static String mobile;
	}
	
	/**
	 * CRM配置相关
	 */
	public static class Crm{
		/** 地址 */
		public static String address;
		
		public static boolean old_identity = true;
		/**
		 * RSA加密 
		 */
		public static class Rsa{
			/** 公钥 */
			public static String public_key;
			/** 私钥 */
			public static String private_key;
		}
		
		/**
		 * API路径
		 */
		public static class Api{
			/** 修改用户信息 */
			public static String modify_Information;
		}
		
	}
	
	/**
	 * 人才库配置
	 *
	 */
	public static class Tb{
		/** 人才项目URL路径 */
		public static String url;
	}
	
	/**
	 * 企业配置
	 *
	 */
	public static class Cr{
		/** 企业项目URL路径 */
		public static String url;
		/** 添加企业用户信息接口 */
		public static String corpuser_add;
		/** 修改企业用户信息接口 */
		public static String corpuser_edit;
	}

	/**
	 * 菜单所属系统标识对应的menuId
	 */
	public static class Sys{
		public static Map<String,String> sysId= MapUtils.toMap(new Object[][]{
				{"ADMIN_WEB","MENU2020032410000000001"},
				{"ADMIN_APP","MENU2020032410000000003"}
		});
	}
	
	/**
	 * 客户端版本配置
	 *
	 */
	public static class Cltver{
		/**版本缓存时间 */
		public static long cachaTime = 60*1000; 
	}
}
