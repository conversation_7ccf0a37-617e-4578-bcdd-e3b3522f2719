package com.niceloo.uc.common;

import com.niceloo.core.redis.RedisClient;

public class UcConst {

	/** REDIS */
	public static RedisClient redis = null;

	/**
	 * 部门一个层级编码的长度
	 */
	public static final Integer LEVELCODE_LENGTH = 10;

	/**
	 * 编码
	 */
	public static class Code{
		
		/**
		 * 锁定状态
		 */
		public static class Lockstatus{
			/** 已锁定 */
			public static String YES="Y";
			/** 未锁定 */
			public static String NO	="N";
		}
	}
	
	/**
	 * 用户类型
	 */
	public static class UserFlag{
		/** 代理商 */
		public static String agent		=	"A";
		/** 学生 */
		public static String student	=	"S";
		/** 内部人员 */
		public static String inner		=	"I";
		/** 教师 */
		public static String teacter 	= 	"T";
		/**企业用户*/
		public static String corp		=	"C";
	}
	
	/**
	 * 可用状态
	 */
	public static class Avlstatus{
		/** 是 */
		public static final String YES	="Y";
		/** 否 */
		public static final String NO	="N";
	}
	
	/**
	 * 删除状态
	 */
	public static class Delstatus{
		/** 是 */
		public static final String YES	="Y";
		/** 否 */
		public static final String NO	="N";
	}
	
	/**
	 *加密方式 
	 *
	 */
    public static class UserLoginpwdtype {
        /** sha1 */
        public static final String SHA1 = "SHA1";
        /** md5 */
        public static final String MD5 = "MD5";
    }

    /**
     * 创建类型
     */
    public static class Datatype {
        /** crm_import */
        public static final String CRM_IMPORT = "CRM_IMP";
        /** uc_add */
        public static final String UC = "UC";
    }

    /**
     * MQ推送
     */
    public static class Mqtype {
        /** 绑定手机号 */
        public static final String USER_MQ_SUBCREABER_BUIND_MOBILE 	= 	"youlu_uc_user_bindmobile";
        /** 用户资料修改 */
        public static final String USER_MQ_SUBCREABER_PROFILE_EDIT 	= 	"youlu_uc_user_profile_edit";
        /** 字典修改 */
        public static final String DICT_MQ_SUBCREABER_EDIT			=	"youlu_uc_dict_edit";
        /**身份认证信息修改*/
        public static final String USER_MQ_IDENTITY_EDIT			=	"youlu_uc_user_identity_edit";
        /** 学员账号注销 */
        public static final String USER_MQ_UNSUBSCRIBE              =    "youlu_uc_unsubscribe";
		/** 员工修改标签状态 */
		public static final String USER_MQ_TAG_EDIT                 =    "youlu_uc_tag_edit";
    }

    public static class Errorcode{
    	/** 参数错误 */
    	public static final String USER_ARGUMENT_INVALIDE		=	"3000";
    	/** 手机号已存在 */
    	public static final String USER_PHONE_INVALIDE			=	"3001";
    	/** 网站平台开户异常 */
    	public static final String USER_NET_INVALIDE			=	"3002";
    	/** 证件已存在 */
    	public static final String USER_IDCARD_EXIST			=	"3003";
    	/** 证件错误  */
    	public static final String USER_IDCARD_ERROR			= 	"3004";
    	/** 手机号已绑定  */
    	public static final String USER_PHONE_EXIST				= 	"3005";
    	/** 用户名已存在  */
    	public static final String USER_LOGIN_EXIST				= 	"3006";
    	/** 邮箱已存在  */
    	public static final String USER_EMAIL_EXIST				= 	"3007";
    	/** 密码不安全 */
    	public static final String USER_PWD_INVALIDE			=	"4001";
    	/** 登陆账号密码错误*/
    	public static final String USER_LOGIN_ERROR				=	"4002";
    	/**用户不存在*/
    	public static final String USER_NON_EXISTENT			=	"4003";
    	/**用户手机号不存在*/
    	public static final String USER_NON_PHONE				=	"4004";
    	/**注册失败*/
    	public static final String USER_ADD_ERROR				=	"4005";
    	/**用户来源标识已存在*/
    	public static final String USER_SOURCEID_EXIST			=	"4006";
    	/**用户来源标识已存在*/
    	public static final String USER_MOBILE_BIND_ERROR		=	"4007";
    	/**用户被禁用*/
    	public static final String USER_AVL_ERROR				=	"4008";
    	/**用户被锁定*/
    	public static final String USER_LOCK_ERROR				=	"4009";
    	/**父节点不存在*/
    	public static final String PARENT_EMPTY					=	"5005";
    	/**解密失败*/
    	public static final String DECRYPT_ERROR				=	"5006";
    	/**IP不正确*/
    	public static final String IP_ERROR						=	"5007";
    	/**ip-guard校验失败*/
    	public static final String IP_GUARD_ERR                 =   "5008";
    	/**员工已离职*/
    	public static final String EE_HAS_LEFT                  =   "6001";
    }
    
    
    public static class RcdType{
    	/** 登录 */
    	public static final String LOGIN = "LOGIN";
    	/** 注册 */
    	public static final String REGIST = "REGIST";
    	/** 其他 */
    	public static final String OTHER = "OTHER";
    }
    
    /**
     * 
     * UK登陆TYPE
     *
     */
    public static class UkType{
    	/** Wap站 */
    	public static final String WAP			=	"W";
    	/** PC */
    	public static final String PC			=	"P";
    	/** APP */
    	public static final String APP			=	"A";
    	/** PC客户端 */
    	public static final String PC_CLIENT 	= 	"C";
    }
    
   
   /**
    * 发布方式
    *
    */
   public static class PublishType{
	   /**未发布*/
	   public static final String NOT	= "N";
	   /**灰度升级*/
	   public static final String GRAY	= "G";
	   /**普通发布*/
	   public static final String COMMON = "C";
   }
   
   /**
    * 迭代方式
    *
    */
   public static class IterType{
	   /**强制迭代*/
	   public static final String FORCE  	=	"F";
	   /**选择迭代*/
	   public static final String CUSTOM 	=	"C";
	   /**延迟选择迭代*/
	   public static final String DELAY		=	"D";
	   /**弱提示选择迭代*/
	   public static final String WEAK		=	"W";
   }
   
   /**
    * 包类型
    *
    */
   public static class Packagetype{
	   /**全部*/
	   public static final String ALL	=	"A";
	   /**部分*/
	   public static final String PORTION	=	"P";
   }
   
   /**
    * 灰度类型
    */
   public static class GrayType{
	   /** 用户 */
	   public static final String USER 		= "U";
	   /**终端*/
	   public static final String Terminal	= "T";
   }
   
   /**
    * 身份认证状态
    *
    */
   public static class UserIDcardStatus{
	   /** 未填写 */
	   public static final String NO		=	"N";
	   /** 待审核 */
	   public static final String WAIT		=	"W";
	   /** 审核失败 */
	   public static final String FAIL		=	"F";
	   /** 审核成功*/
	   public static final String SUCCESS	=	"S";
   }
   
   /**
    * 证件类型
    *
    */
   public static class IDcardType{
	   /** 身份证 */
	   public static final String IDCARD 	= 	"I";
	   /** 港澳通行证 */
	   public static final String HGJMLW   	=	"H";
	   /** 台胞证 */
	   public static final String TWJMLW   	=	"T";
	   /** 军官证 */
	   public static final String OFFICER	=	"O";
   }
   
   
   /**
    * 品牌
    *
    */
   public static class Brand{
	   /** 优路*/
	   public static final String YOULU 	= 	"bfb5ca6a-6d18-4aac-8571-ec38fd2d3bdc";
	   /** GEEDU */
	   public static final String GEEDU   	=	"dbc475ed-3257-4df4-a6b3-ddea6be9ed32";
	   /** 链学 */
	   public static final String LIANXUE   =	"d5a8cb67-54ba-448c-bab9-4044d74081a7";
   }
   
   /**
    * 用户来源
    *
    */
   public static class SourceType{
	   /**网站*/
	   public static final String YOULU_WEB = "YOULU.WEB";
	   /**CRM*/
	   public static final String YOULU_CRM = "YOULU.CRM";
	   /**微信*/
	   public static final String YOULU_WX 	= "YOULU.WX";
   }

	/**
	 * 身份认证类型
	 *
	 */
	public enum  IdAuthType{

		wx("wx","微信"),
		workwx("workwx","企业微信"),
		sms("sms","短信验证码"),
		email("workwx","邮箱");

		public String code;
		public String name;

		IdAuthType(String code, String name) {
			this.code = code;
			this.name = name;
		}
   }

	/**
	 * 用户标签枚举
	 */
	public enum userTagType {
		type_A("A","答疑老师");

		public String code;
		public String name;

		userTagType(String code, String name) {
			this.code = code;
			this.name = name;
		}
   }
}
