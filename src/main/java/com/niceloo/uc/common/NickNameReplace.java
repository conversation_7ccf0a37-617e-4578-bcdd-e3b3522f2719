package com.niceloo.uc.common;

import static org.nobject.common.lang.StringUtils.isEmpty;

public class NickNameReplace {
	
	private static String replaceStr1 = "§№≡";
	private static String replaceStr2 = "↑№↓";
	
	public static String InsertReplace(String text) {
		
		if(!isEmpty(text)){
			String s = text;
			if ((text.indexOf("'") >= 0) ) {
				s = s.replaceAll("'", replaceStr1);
			}
			if(text.indexOf("<") >= 0){
				s = s.replaceAll("<", replaceStr2);
			}
			return s;
		}
		return text;
	}
	
	
	public static String QueryReplace(String text) {
		if(!isEmpty(text)){
			String s = text;
			if ((text.indexOf(replaceStr1) >= 0) ) {
				s = s.replaceAll( replaceStr1,"'");
			}
			if(text.indexOf(replaceStr2) >= 0){
				s = s.replaceAll(replaceStr2,"<");
			}
			return s;
		}
		return text;
	}
}
	