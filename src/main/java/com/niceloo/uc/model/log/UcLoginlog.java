package com.niceloo.uc.model.log;

import com.niceloo.core.log.CoreLog;

/**
 * 登录日志，包含用户端登录日志和管理端登录日志
 * @Auther hepangui
 * @Date 2020/2/8 0008
 */
public abstract class UcLoginlog extends CoreLog {


	public static final String LOGINTYPE_ACCOUNT_PASSWORD = "AP";
	public static final String LOGINTYPE_PHONE_PASSWORD = "PP";
	public static final String LOGINTYPE_MAIL_PASSWORD = "MP";
	public static final String LOGINTYPE_WORKWX = "WX";
	public static final String LOGINTYPE_UK = "UK";

	public static final String LOGINSTATUS_SUCCESS = "S";
	public static final String LOGINSTATUS_ERROR = "E";

	/**
	 * 登录类型：账户密码
	 */
	String logLogintype;

	/**
	 * 登录ip
	 */
	String logLoginip;

	/**
	 * 账户id
	 */
	String userId;

	/**
	 * 账户名
	 */
	String userName;

	/**
	 * 员工手机号
	 */
	String userMobile;

	/**
	 * 员工登录账号
	 */
	String userLoginname;

	/**
	 * 登录状态  成功，失败
	 */
	String logLoginstatus;

	/**
	 * 错误信息，如密码错误
	 */
	String logLoginerrormsg;


	public String getLogLogintype() {
		return logLogintype;
	}

	public void setLogLogintype(String logLogintype) {
		this.logLogintype = logLogintype;
	}

	public String getLogLoginip() {
		return logLoginip;
	}

	public void setLogLoginip(String logLoginip) {
		this.logLoginip = logLoginip;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserMobile() {
		return userMobile;
	}

	public void setUserMobile(String userMobile) {
		this.userMobile = userMobile;
	}

	public String getUserLoginname() {
		return userLoginname;
	}

	public void setUserLoginname(String userLoginname) {
		this.userLoginname = userLoginname;
	}

	public String getLogLoginstatus() {
		return logLoginstatus;
	}

	public void setLogLoginstatus(String logLoginstatus) {
		this.logLoginstatus = logLoginstatus;
	}

	public String getLogLoginerrormsg() {
		return logLoginerrormsg;
	}

	public void setLogLoginerrormsg(String logLoginerrormsg) {
		this.logLoginstatus = LOGINSTATUS_ERROR;
		this.logLoginerrormsg = logLoginerrormsg;
	}
}
