package com.niceloo.uc.model.log;

import com.niceloo.core.log.CoreLog;

/**
 * 注册日志
 * @Auther hepangui
 * @Date 2020/2/9 0009
 */
public class UcRegistlog extends CoreLog {

	public static final String STATUS_SUCCESS = "S";
	public static final String STATUS_ERROR = "E";
	/**
	 * 账户id
	 */
	String userId;

	/**
	 * 账户名称
	 */
	String userName;

	/**
	 * 手机号
	 */
	String userMobile;

	/**
	 * 登录账号
	 */
	String userLoginname;

	/**
	 * 成功   失败
	 */
	String logRegiststatus;

	/**
	 * 错误信息，如用户名已存在等
	 */
	String logRegisterrormsg;

	/**
	 * 注册方式
	 */
	String logRegisttype;

	/**
	 * 注册详细信息
	 */
	String logRegistdetail;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserMobile() {
		return userMobile;
	}

	public void setUserMobile(String userMobile) {
		this.userMobile = userMobile;
	}

	public String getUserLoginname() {
		return userLoginname;
	}

	public void setUserLoginname(String userLoginname) {
		this.userLoginname = userLoginname;
	}

	public String getLogRegiststatus() {
		return logRegiststatus;
	}

	public void setLogRegiststatus(String logRegiststatus) {
		this.logRegiststatus = logRegiststatus;
	}

	public String getLogRegisterrormsg() {
		return logRegisterrormsg;
	}

	public void setLogRegisterrormsg(String logRegisterrormsg) {
		this.logRegisterrormsg = logRegisterrormsg;
	}


	public String getLogRegisttype() {
		return logRegisttype;
	}

	public void setLogRegisttype(String logRegisttype) {
		this.logRegisttype = logRegisttype;
	}

	public String getLogRegistdetail() {
		return logRegistdetail;
	}

	public void setLogRegistdetail(String logRegistdetail) {
		this.logRegistdetail = logRegistdetail;
	}
}
