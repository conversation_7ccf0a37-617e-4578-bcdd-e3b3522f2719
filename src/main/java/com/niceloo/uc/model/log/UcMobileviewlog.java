package com.niceloo.uc.model.log;

import com.niceloo.core.log.CoreLog;

/**
 * 用户手机号查看日志
 * <AUTHOR>
 *
 */
public class UcMobileviewlog extends CoreLog {

	/**用户标识*/
	private String userId;
	/**用户姓名*/
	private String userName;
	/**用户手机号*/
	private String userMobile;
	/**用户登录帐号*/
	private String userLoginname;
	/**创建人*/
	private String logCreater;
	/**创建人姓名*/
	private String logCreatername;
	/**手机号查看模块*/
	private String logModule;
	/**菜单名称*/
	private String logMenu;
	/**IP*/
	private String logIp;
	/**浏览器*/
	private String logBrowser;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserMobile() {
		return userMobile;
	}

	public void setUserMobile(String userMobile) {
		this.userMobile = userMobile;
	}

	public String getUserLoginname() {
		return userLoginname;
	}

	public void setUserLoginname(String userLoginname) {
		this.userLoginname = userLoginname;
	}

	public String getLogCreater() {
		return logCreater;
	}

	public void setLogCreater(String logCreater) {
		this.logCreater = logCreater;
	}

	public String getLogCreatername() {
		return logCreatername;
	}

	public void setLogCreatername(String logCreatername) {
		this.logCreatername = logCreatername;
	}

	public String getLogModule() {
		return logModule;
	}

	public void setLogModule(String logModule) {
		this.logModule = logModule;
	}

	public String getLogMenu() {
		return logMenu;
	}

	public void setLogMenu(String logMenu) {
		this.logMenu = logMenu;
	}

	public String getLogIp() {
		return logIp;
	}

	public void setLogIp(String logIp) {
		this.logIp = logIp;
	}

	public String getLogBrowser() {
		return logBrowser;
	}

	public void setLogBrowser(String logBrowser) {
		this.logBrowser = logBrowser;
	}
}
