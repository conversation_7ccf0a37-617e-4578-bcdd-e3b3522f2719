package com.niceloo.uc.model;
import com.niceloo.core.mongo.annonation.Field;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 角色POJO类
 * <AUTHOR>
 * @Date 2019-11-18 10:35:12
 */
@POJO
@ClassDesc(comment = "角色")
public class UcRole{

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "角色标识")
	private String roleId;
	@POJO
	@FieldDesc(length = 100 ,comment = "角色名称")
	private String roleName;
	@POJO
	@FieldDesc(length = 100 ,comment = "角色排序")
	private Integer roleSeq;
	@POJO
	@FieldDesc(length = 4000 ,comment = "角色描述")
	private String roleMemo;
	@POJO
	@FieldDesc(length = 1000 ,comment = "自定义管理范围")
	private String roleManagerole;
	@POJO
	@FieldDesc(length = 20 ,comment = "创建时间")
	private String roleCreatedate;
	@POJO
	@FieldDesc(length = 1000 ,comment = "创建人id")
	private String roleCreaterid;
	@POJO
	@FieldDesc(length = 1000 ,comment = "创建人名称")
	private String roleCreatername;
	@POJO
	@FieldDesc(length = 36 ,comment = "品牌标识")
	private String brandId;
	@POJO
	@FieldDesc(length = 1 ,comment = "超级管理员")
	private String roleAdminstatus;

	@POJO
	@FieldDesc(length = 1 , comment = "管理范围策略")
	private String roleManagescope;

	@POJO
	@FieldDesc(comment = "角色类型")
	private String roleType;

	@POJO
	@FieldDesc(comment = "是否授权登录IP")
	private String roleIpstatus;

	@POJO
	@FieldDesc(comment = "是否授权ip-guard认证")
	private String roleIpguardstatus;

	@POJO
	@FieldDesc(comment = "允许查看手机号次数")
	private Integer roleMobilecount;

	@POJO
	@FieldDesc(comment = "允许查看身份证号次数")
	private Integer roleIdcardcount;

	@POJO
	@FieldDesc(comment = "允许查看微信次数")
	private Integer roleWeixincount;

	@POJO
	@FieldDesc(comment = "允许查看代理商次数")
	private Integer roleAgentcount;

	/**
	 * 获取角色标识
	 * 
	 * @return 角色标识
	 */
	public String getRoleId() {
		return this.roleId;
	}
	/**
	 * 设置角色标识
	 * 
	 * @param  roleId 角色标识
	 */
	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
	/**
	 * 获取角色名称
	 * 
	 * @return 角色名称
	 */
	public String getRoleName() {
		return this.roleName;
	}
	/**
	 * 设置角色名称
	 * 
	 * @param  roleName 角色名称
	 */
	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
	/**
	 * 获取角色描述
	 * 
	 * @return 角色描述
	 */
	public String getRoleMemo() {
		return this.roleMemo;
	}
	/**
	 * 设置角色描述
	 * 
	 * @param  roleMemo 角色描述
	 */
	public void setRoleMemo(String roleMemo) {
		this.roleMemo = roleMemo;
	}
	/**
	 * 获取可授权角色
	 * 
	 * @return 可授权角色
	 */
	public String getRoleManagerole() {
		return this.roleManagerole;
	}
	/**
	 * 设置可授权角色
	 * 
	 * @param  roleManagerole 可授权角色
	 */
	public void setRoleManagerole(String roleManagerole) {
		this.roleManagerole = roleManagerole;
	}

	public Integer getRoleSeq() {
		return roleSeq;
	}

	public void setRoleSeq(Integer roleSeq) {
		this.roleSeq = roleSeq;
	}

	public String getRoleCreatedate() {
		return roleCreatedate;
	}

	public void setRoleCreatedate(String roleCreatedate) {
		this.roleCreatedate = roleCreatedate;
	}

	public String getRoleCreaterid() {
		return roleCreaterid;
	}

	public void setRoleCreaterid(String roleCreaterid) {
		this.roleCreaterid = roleCreaterid;
	}

	public String getRoleCreatername() {
		return roleCreatername;
	}

	public void setRoleCreatername(String roleCreatername) {
		this.roleCreatername = roleCreatername;
	}

	public String getBrandId() {
		return brandId;
	}

	public void setBrandId(String brandId) {
		this.brandId = brandId;
	}

	public String getRoleAdminstatus() {
		return roleAdminstatus;
	}

	public void setRoleAdminstatus(String roleAdminstatus) {
		this.roleAdminstatus = roleAdminstatus;
	}

	public String getRoleManagescope() {
		return roleManagescope;
	}

	public void setRoleManagescope(String roleManagescope) {
		this.roleManagescope = roleManagescope;
	}

	public String getRoleType() {
		return roleType;
	}

	public void setRoleType(String roleType) {
		this.roleType = roleType;
	}

	public String getRoleIpstatus() {
		return roleIpstatus;
	}

	public void setRoleIpstatus(String roleIpstatus) {
		this.roleIpstatus = roleIpstatus;
	}

	public String getRoleIpguardstatus() {
		return roleIpguardstatus;
	}

	public void setRoleIpguardstatus(String roleIpguardstatus) {
		this.roleIpguardstatus = roleIpguardstatus;
	}

	public Integer getRoleMobilecount() {
		return roleMobilecount;
	}

	public void setRoleMobilecount(Integer roleMobilecount) {
		this.roleMobilecount = roleMobilecount;
	}

	public Integer getRoleIdcardcount() {
		return roleIdcardcount;
	}

	public void setRoleIdcardcount(Integer roleIdcardcount) {
		this.roleIdcardcount = roleIdcardcount;
	}

	public Integer getRoleWeixincount() {
		return roleWeixincount;
	}

	public void setRoleWeixincount(Integer roleWeixincount) {
		this.roleWeixincount = roleWeixincount;
	}

	public Integer getRoleAgentcount() {
		return roleAgentcount;
	}

	public void setRoleAgentcount(Integer roleAgentcount) {
		this.roleAgentcount = roleAgentcount;
	}
}