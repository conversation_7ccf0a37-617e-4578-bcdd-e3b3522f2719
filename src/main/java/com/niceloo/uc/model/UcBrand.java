package com.niceloo.uc.model;

import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 品牌类
 * @version 1.0
 */
@POJO
@ClassDesc(comment="品牌")
public class UcBrand {
	/** 品牌标识 */
	@POJO(id=true,generator=ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length=36,comment="品牌标识")
	private String brandId;
	/** 品牌名称*/
	@POJO
	@FieldDesc(length=100,comment="品牌名称")
	private String brandName;
	/** 品牌排序*/
	@POJO
	@FieldDesc(length=5,comment="品牌排序")
	private int brandSeq;
	/** 品牌描述 */
	@POJO
	@FieldDesc(length=200,comment="品牌描述")
	private String brandMemo;
	/**品牌创建者*/
	@POJO
	@FieldDesc(length=36,comment="品牌创建者")
	private String brandCreater;
	/** 品牌创建日期 */
	@POJO
	@FieldDesc(length=19,comment="品牌创建日期")
	private String brandCreatedate;
	/**品牌修改者 */
	@POJO
	@FieldDesc(length=36,comment="品牌修改者")
	private String brandModifier;
	/**品牌修改日期 */
	@POJO
	@FieldDesc(length=19,comment="品牌修改日期")
	private String brandModifieddate;
	/**品牌可用状态 */
	@POJO
	@FieldDesc(length=1,comment="品牌可用状态")
	private String brandAvlstatus;
	/**品牌删除状态*/
	@POJO
	@FieldDesc(length=1,comment="品牌删除状态")
	private String brandDelstatus;
	public String getBrandId() {
		return brandId;
	}
	public void setBrandId(String brandId) {
		this.brandId = brandId;
	}
	public String getBrandName() {
		return brandName;
	}
	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}
	public int getBrandSeq() {
		return brandSeq;
	}
	public void setBrandSeq(int brandSeq) {
		this.brandSeq = brandSeq;
	}
	public String getBrandMemo() {
		return brandMemo;
	}
	public void setBrandMemo(String brandMemo) {
		this.brandMemo = brandMemo;
	}
	public String getBrandCreater() {
		return brandCreater;
	}
	public void setBrandCreater(String brandCreater) {
		this.brandCreater = brandCreater;
	}
	public String getBrandCreatedate() {
		return brandCreatedate;
	}
	public void setBrandCreatedate(String brandCreatedate) {
		this.brandCreatedate = brandCreatedate;
	}
	public String getBrandModifier() {
		return brandModifier;
	}
	public void setBrandModifier(String brandModifier) {
		this.brandModifier = brandModifier;
	}
	public String getBrandModifieddate() {
		return brandModifieddate;
	}
	public void setBrandModifieddate(String brandModifieddate) {
		this.brandModifieddate = brandModifieddate;
	}
	public String getBrandAvlstatus() {
		return brandAvlstatus;
	}
	public void setBrandAvlstatus(String brandAvlstatus) {
		this.brandAvlstatus = brandAvlstatus;
	}
	public String getBrandDelstatus() {
		return brandDelstatus;
	}
	public void setBrandDelstatus(String brandDelstatus) {
		this.brandDelstatus = brandDelstatus;
	}

}
