package com.niceloo.uc.model;

import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 用户标签表
 */
@POJO
@ClassDesc(comment = "用户标签表")
public class UcUserTag {

    @POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
    @FieldDesc(length = 36 ,comment = "用户标签Id")
    private String tagId;

    @POJO
    @FieldDesc(length = 36 ,comment = "用户id")
    private String userId;
    @POJO
    @FieldDesc(length = 1 ,comment = "标签类型 A:答疑老师")
    private String tagType;
    @POJO
    @FieldDesc(length = 1 ,comment = "标签启用状态(Y:可用;N:不可用)")
    private String tagAvlstatus;
    @POJO
    @FieldDesc(length = 1 ,comment = "标签删除状态(Y:已删除;N:未删除)")
    private String tagDelstatus;
    @POJO
    @FieldDesc(length = 19 ,comment = "标签修改时间")
    private String tagModifieddate;
    @POJO
    @FieldDesc(length = 19 ,comment = "标签创建时间")
    private String tagCreatedate;
    @POJO
    @FieldDesc(length = 36 ,comment = "标签创建人")
    private String tagCreator;
    @POJO
    @FieldDesc(length = 36 ,comment = "标签修改人")
    private String tagModifier;

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTagType() {
        return tagType;
    }

    public void setTagType(String tagType) {
        this.tagType = tagType;
    }

    public String getTagAvlstatus() {
        return tagAvlstatus;
    }

    public void setTagAvlstatus(String tagAvlstatus) {
        this.tagAvlstatus = tagAvlstatus;
    }

    public String getTagDelstatus() {
        return tagDelstatus;
    }

    public void setTagDelstatus(String tagDelstatus) {
        this.tagDelstatus = tagDelstatus;
    }

    public String getTagModifieddate() {
        return tagModifieddate;
    }

    public void setTagModifieddate(String tagModifieddate) {
        this.tagModifieddate = tagModifieddate;
    }

    public String getTagCreatedate() {
        return tagCreatedate;
    }

    public void setTagCreatedate(String tagCreatedate) {
        this.tagCreatedate = tagCreatedate;
    }

    public String getTagCreator() {
        return tagCreator;
    }

    public void setTagCreator(String tagCreator) {
        this.tagCreator = tagCreator;
    }

    public String getTagModifier() {
        return tagModifier;
    }

    public void setTagModifier(String tagModifier) {
        this.tagModifier = tagModifier;
    }
}
