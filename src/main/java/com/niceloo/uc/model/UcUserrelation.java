package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 用户关系POJO类
 * <AUTHOR>
 * @Date 2019-11-18 10:35:12
 */
@POJO
@ClassDesc(comment = "用户关系")
public class UcUserrelation {

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "用户关系id")
	private String userreId;
	@POJO
	@FieldDesc(length = 36 ,comment = "关系类型")
	private String userreType;
	@POJO
	@FieldDesc(length = 255 ,comment = "用户id")
	private String userId;
	@POJO
	@FieldDesc(length = 255 ,comment = "关系id")
	private String userreRelationid;
	@POJO
	@FieldDesc(length = 1000 ,comment = "关系id")
	private String userreRelationlevelcode;
	@POJO
	@FieldDesc(length = 36 ,comment = "品牌标识")
	private String brandId;

	public UcUserrelation() {
	}

	public UcUserrelation(String userreId, String userreType, String userId, String userreRelationid) {
		this.userreId = userreId;
		this.userreType = userreType;
		this.userId = userId;
		this.userreRelationid = userreRelationid;
	}

	public UcUserrelation(String userreId, String userreType, String userId,
						  String userreRelationid, String userreRelationlevelcode, String brandId) {
		this.userreId = userreId;
		this.userreType = userreType;
		this.userId = userId;
		this.userreRelationid = userreRelationid;
		this.userreRelationlevelcode = userreRelationlevelcode;
		this.brandId = brandId;
	}

	/**
	 * 获取用户关系id
	 * 
	 * @return 用户关系id
	 */
	public String getUserreId() {
		return this.userreId;
	}
	/**
	 * 设置用户关系id
	 * 
	 * @param  userreId 用户关系id
	 */
	public void setUserreId(String userreId) {
		this.userreId = userreId;
	}
	/**
	 * 获取关系类型
	 * 
	 * @return 关系类型
	 */
	public String getUserreType() {
		return this.userreType;
	}
	/**
	 * 设置关系类型
	 * 
	 * @param  userreType 关系类型
	 */
	public void setUserreType(String userreType) {
		this.userreType = userreType;
	}
	/**
	 * 获取用户id
	 * 
	 * @return 用户id
	 */
	public String getUserId() {
		return this.userId;
	}
	/**
	 * 设置用户id
	 * 
	 * @param  userId 用户id
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}
	/**
	 * 获取关系id
	 * 
	 * @return 关系id
	 */
	public String getUserreRelationid() {
		return this.userreRelationid;
	}
	/**
	 * 设置关系id
	 * 
	 * @param  userreRelationid 关系id
	 */
	public void setUserreRelationid(String userreRelationid) {
		this.userreRelationid = userreRelationid;
	}

	public String getBrandId() {
		return brandId;
	}

	public void setBrandId(String brandId) {
		this.brandId = brandId;
	}

	public String getUserreRelationlevelcode() {
		return userreRelationlevelcode;
	}

	public void setUserreRelationlevelcode(String userreRelationlevelcode) {
		this.userreRelationlevelcode = userreRelationlevelcode;
	}
}