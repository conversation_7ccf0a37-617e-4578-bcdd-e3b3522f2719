package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 基础用户POJO类
 * @version 1.0
 */
@POJO
@ClassDesc(comment="基础用户")
public class UcUser{

	/** 用户标识 */
	@POJO(id=true,generator=ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length=36,comment="用户标识")
	private String userId;
	/** 用户姓名 */
	@POJO
	@FieldDesc(length=50,comment="用户姓名")
	private String userName;
	/** 用户登录帐号 */
	@POJO
	@FieldDesc(length=20,comment="用户登录帐号")
	private String userLoginname;
	/** 用户登录密码 */
	@POJO
	@FieldDesc(length=100,comment="用户登录密码")
	private String userLoginpwd;
	/**用户密码最后修改时间*/
	@POJO
	@FieldDesc(length=100,comment="用户登录密码最后修改时间")
	private String userLastloginpwddate;
	/** 用户登录密码状态 */
	@POJO
	@FieldDesc(length=1,comment="用户登录密码状态")
	private String userLoginpwdstatus;
	/** 用户登录密码类型 */
	@POJO
	@FieldDesc(length=20,comment="用户登录密码类型")
	private String userLoginpwdtype;
	/** 用户类型 */
	@POJO
	@FieldDesc(length=1,comment="用户类型")
	private String userFlag;
	/** 用户锁定状态 */
	@POJO
	@FieldDesc(length=1,comment="用户锁定状态")
	private String userLockstatus;
	/** 用户上次登录IP */
	@POJO
	@FieldDesc(length=50,comment="用户上次登录IP")
	private String userLastloginip;
	/** 用户上次登录时间 */
	@POJO
	@FieldDesc(length=19,comment="用户上次登录时间")
	private String userLastlogindate;
	/** 用户上次登录品牌 */
	@POJO
	@FieldDesc(length=19,comment="用户上次登录品牌")
	private String userLastloginbrand;
	/** 用户创建时间 */
	@POJO
	@FieldDesc(length=19,comment="用户创建时间")
	private String userCreateddate;
	/** 用户创建方式 */
	@POJO
	@FieldDesc(length=20,comment="用户创建方式")
	private String userCreatetype;
	/** 用户修改时间 */
	@POJO
	@FieldDesc(length=19,comment="用户修改时间")
	private String userModifieddate;
	/** 用户修改方式 */
	@POJO
	@FieldDesc(length=20,comment="用户修改方式")
	private String userModifiedtype;
	/** 用户手机 */
	@POJO
	@FieldDesc(length=20,comment="用户手机")
	private String userMobile;
	/** 用户手机状态 */
	@POJO
	@FieldDesc(length=1,comment="用户手机状态")
	private String userMobilestatus;
	/** 用户邮箱 */
	@POJO
	@FieldDesc(length=50,comment="用户邮箱")
	private String userEmail;
	/** 用户邮箱状态 */
	@POJO
	@FieldDesc(length=1,comment="用户邮箱状态")
	private String userEmailstatus;
	/** 用户身份证号 */
	@POJO
	@FieldDesc(length=20,comment="用户身份证号")
	private String userIdcard;
	/** 用户性别 */
	@POJO
	@FieldDesc(length=1,comment="用户性别")
	private String userGender;
	/** 用户OPENID */
	@POJO
	@FieldDesc(length=50,comment="用户OPENID")
	private String userOpenid;
	/** 用户QQ */
	@POJO
	@FieldDesc(length=50,comment="用户QQ")
	private String userQq;
	/** 用户微信 */
	@POJO
	@FieldDesc(length=50,comment="用户微信")
	private String userWeixin;
	/** 用户删除状态 */
	@POJO
	@FieldDesc(length=1,comment="用户删除状态")
	private String userDelstatus;
	/** 用户可用状态 */
	@POJO
	@FieldDesc(length=1,comment="用户可用状态")
	private String userAvlstatus;
	/** 用户来源标识 */
	@POJO
	@FieldDesc(length=50,comment="用户来源标识")
	private String userSourceid;
	/** 用户来源类型 */
	@POJO
	@FieldDesc(length=50,comment="用户来源类型")
	private String userSourcetype;
	/** 地区编号 */
	@POJO
	@FieldDesc(length=6,comment="地区编号 ")
	private String userAreacode;
	/** 用户昵称 */
	@POJO
	@FieldDesc(length=50,comment="用户昵称")
	private String userNickname;
	/**婚姻状态*/
	@POJO
	@FieldDesc(length=1,comment="婚姻状态")
	private String userMarrystatus;
	/**用户邮编*/
	@POJO
	@FieldDesc(length=6,comment="用户邮编")
	private String userPostcode;
	/**用户居住地址*/
	@POJO
	@FieldDesc(length=200,comment="用户居住地址")
	private String userAddress;
	/**用户电话*/
	@POJO
	@FieldDesc(length=200,comment="用户电话")
	private String userTel;
	/**用户证件类型*/
	@POJO
	@FieldDesc(length=36,comment="用户证件类型")
	private String userIdcardtype;
	/**用户头像*/
	@POJO
	@FieldDesc(length=200,comment="用户头像")
	private String userAvatar;
	/**用户备注*/
	@POJO
	@FieldDesc(comment="用户备注")
	private String userMemo;
	/**用户出生日期*/
	@POJO
	@FieldDesc(length=50,comment="用户出生日期")
	private String userBirthday;
	/**用户签名*/
	@POJO
	@FieldDesc(length=500,comment="用户签名")
	private String userSignature;
	/**登陆手机校验*/
	@POJO
	@FieldDesc(length=500,comment="登陆手机校验")
	private String userCheckmobile;
	/**登陆邮箱校验*/
	@POJO
	@FieldDesc(length=500,comment="登陆邮箱校验")
	private String userCheckemail;
	@POJO
	@FieldDesc(length=1,comment="身份证状态")
	private String userIdcardstatus;
	/**用户工作单位名称*/
	@POJO
	@FieldDesc(length=500,comment="用户工作单位名称")
	private String userWorkunit;
	/**用户参加工作年份*/
	@POJO
	@FieldDesc(length=4,comment="用户参加工作年份")
	private String userWorkyear;
	/**用户毕业院校*/
	@POJO
	@FieldDesc(length=255,comment="用户毕业院校")
	private String userEduschool;
	/**用户学历*/
	@POJO
	@FieldDesc(length=2,comment="用户学历")
	private String userEdulevel;
	/**用户专业*/
	@POJO
	@FieldDesc(length=255,comment="用户专业")
	private String userEdumajor;
	/**品牌标识*/
	@POJO
	@FieldDesc(length=255,comment="品牌标识")
	private String brandId;
	/**阿里推送标识*/
	@POJO
	@FieldDesc(length=255,comment="阿里推送标识")
	private String userApushid;
	
	/** 设置用户标识 */
	public void setUserId(String userId){this.userId=userId;}
	/** 设置用户姓名 */
	public void setUserName(String userName){this.userName=userName;}
	/** 设置用户登录帐号 */
	public void setUserLoginname(String userLoginname){this.userLoginname=userLoginname;}
	/** 设置用户登录密码 */
	public void setUserLoginpwd(String userLoginpwd){this.userLoginpwd=userLoginpwd;}
	/** 设置用户登录密码状态 */
	public void setUserLoginpwdstatus(String userLoginpwdstatus){this.userLoginpwdstatus=userLoginpwdstatus;}
	/** 设置用户登录密码类型 */
	public void setUserLoginpwdtype(String userLoginpwdtype){this.userLoginpwdtype=userLoginpwdtype;}
	/** 设置用户类型 */
	public void setUserFlag(String userFlag){this.userFlag=userFlag;}
	/** 设置用户锁定状态 */
	public void setUserLockstatus(String userLockstatus){this.userLockstatus=userLockstatus;}
	/** 设置用户上次登录IP */
	public void setUserLastloginip(String userLastloginip){this.userLastloginip=userLastloginip;}
	/** 设置用户上次登录时间 */
	public void setUserLastlogindate(String userLastlogindate){this.userLastlogindate=userLastlogindate;}
	/** 设置用户创建方式 */
	public void setUserCreatetype(String userCreatetype){this.userCreatetype=userCreatetype;}
	/** 设置用户修改时间 */
	public void setUserModifieddate(String userModifieddate){this.userModifieddate=userModifieddate;}
	/** 设置用户修改方式 */
	public void setUserModifiedtype(String userModifiedtype){this.userModifiedtype=userModifiedtype;}
	/** 设置用户手机 */
	public void setUserMobile(String userMobile){this.userMobile=userMobile;}
	/** 设置用户手机状态 */
	public void setUserMobilestatus(String userMobilestatus){this.userMobilestatus=userMobilestatus;}
	/** 设置用户邮箱 */
	public void setUserEmail(String userEmail){this.userEmail=userEmail;}
	/** 设置用户邮箱状态 */
	public void setUserEmailstatus(String userEmailstatus){this.userEmailstatus=userEmailstatus;}
	/** 设置用户身份证号 */
	public void setUserIdcard(String userIdcard){this.userIdcard=userIdcard;}
	/** 设置用户性别 */
	public void setUserGender(String userGender){this.userGender=userGender;}
	/** 设置用户OPENID */
	public void setUserOpenid(String userOpenid){this.userOpenid=userOpenid;}
	/** 设置用户QQ */
	public void setUserQq(String userQq){this.userQq=userQq;}
	/** 设置用户微信 */
	public void setUserWeixin(String userWeixin){this.userWeixin=userWeixin;}
	/** 设置用户删除状态 */
	public void setUserDelstatus(String userDelstatus){this.userDelstatus=userDelstatus;}
	/** 设置用户可用状态 */
	public void setUserAvlstatus(String userAvlstatus){this.userAvlstatus=userAvlstatus;}
	/** 设置用户来源标识 */
	public void setUserSourceid(String userSourceid){this.userSourceid=userSourceid;}
	/** 设置用户来源类型 */
	public void setUserSourcetype(String userSourcetype){this.userSourcetype=userSourcetype;}
	/** 获取用户标识 */
	public String getUserId(){return this.userId;}
	/** 获取用户姓名 */
	public String getUserName(){return this.userName;}
	/** 获取用户登录帐号 */
	public String getUserLoginname(){return this.userLoginname;}
	/** 获取用户登录密码 */
	public String getUserLoginpwd(){return this.userLoginpwd;}
	/** 获取用户登录密码状态 */
	public String getUserLoginpwdstatus(){return this.userLoginpwdstatus;}
	/** 获取用户登录密码类型 */
	public String getUserLoginpwdtype(){return this.userLoginpwdtype;}
	/** 获取用户类型 */
	public String getUserFlag(){return this.userFlag;}
	/** 获取用户锁定状态 */
	public String getUserLockstatus(){return this.userLockstatus;}
	/** 获取用户上次登录IP */
	public String getUserLastloginip(){return this.userLastloginip;}
	/** 获取用户上次登录时间 */
	public String getUserLastlogindate(){return this.userLastlogindate;}
	/** 获取用户创建方式 */
	public String getUserCreatetype(){return this.userCreatetype;}
	/** 获取用户修改时间 */
	public String getUserModifieddate(){return this.userModifieddate;}
	/** 获取用户修改方式 */
	public String getUserModifiedtype(){return this.userModifiedtype;}
	/** 获取用户手机 */
	public String getUserMobile(){return this.userMobile;}
	/** 获取用户手机状态 */
	public String getUserMobilestatus(){return this.userMobilestatus;}
	/** 获取用户邮箱 */
	public String getUserEmail(){return this.userEmail;}
	/** 获取用户邮箱状态 */
	public String getUserEmailstatus(){return this.userEmailstatus;}
	/** 获取用户身份证号 */
	public String getUserIdcard(){return this.userIdcard;}
	/** 获取用户性别 */
	public String getUserGender(){return this.userGender;}
	/** 获取用户OPENID */
	public String getUserOpenid(){return this.userOpenid;}
	/** 获取用户QQ */
	public String getUserQq(){return this.userQq;}
	/** 获取用户微信 */
	public String getUserWeixin(){return this.userWeixin;}
	/** 获取用户删除状态 */
	public String getUserDelstatus(){return this.userDelstatus;}
	/** 获取用户可用状态 */
	public String getUserAvlstatus(){return this.userAvlstatus;}
	/** 获取用户来源标识 */
	public String getUserSourceid(){return this.userSourceid;}
	/** 获取用户来源类型 */
	public String getUserSourcetype(){return this.userSourcetype;}
	/** 获取身份证状态 */
	public String getUserIdcardstatus() {return userIdcardstatus;}
	/** 设置身份证状态 */
	public void setUserIdcardstatus(String userIdcardstatus) {this.userIdcardstatus = userIdcardstatus;}
	/** 获取地区编号 */
	public String getUserAreacode() {return userAreacode;}
	/** 设置地区编号 */
	public void setUserAreacode(String userAreacode) {this.userAreacode = userAreacode;}
	/** 获取用户昵称 */
	public String getUserNickname() {return userNickname;}
	/** 设置用户昵称 */
	public void setUserNickname(String userNickname) {this.userNickname = userNickname;}
	/** 设置婚姻状态 */
	public String getUserMarrystatus() {return userMarrystatus;}
	/** 获取婚姻状态 */
	public void setUserMarrystatus(String userMarrystatus) {this.userMarrystatus = userMarrystatus;}
	/** 获取用户邮编 */
	public String getUserPostcode() {return userPostcode;}
	/** 设置用户邮编 */
	public void setUserPostcode(String userPostcode) {this.userPostcode = userPostcode;}
	/**获取用户地址*/
	public String getUserAddress() {return userAddress;}
	/**设置用户地址*/
	public void setUserAddress(String userAddress) {this.userAddress = userAddress;}
	/**获取用户电话*/
	public String getUserTel() {return userTel;}
	/**设置用户电话*/
	public void setUserTel(String userTel) {this.userTel = userTel;}
	/**获取用户证件类型*/
	public String getUserIdcardtype() {return userIdcardtype;}
	/**设置用户证件类型*/
	public void setUserIdcardtype(String userIdcardtype) {this.userIdcardtype = userIdcardtype;}
	/**获取用户头像*/
	public String getUserAvatar() {return userAvatar;}
	/**设置用户头像*/
	public void setUserAvatar(String userAvatar) {this.userAvatar = userAvatar;}
	/**获取用户备注*/
	public String getUserMemo() {return userMemo;}
	/**设置用户备注*/
	public void setUserMemo(String userMemo) {this.userMemo = userMemo;}
	/**获取用户生日*/
	public String getUserBirthday() {return userBirthday;}
	/**设置用户生日*/
	public void setUserBirthday(String userBirthday) {this.userBirthday = userBirthday;}
	/**获取用户签名*/
	public String getUserSignature() {return userSignature;}
	/**设置用户签名*/
	public void setUserSignature(String userSignature) {this.userSignature = userSignature;}
	/**获取用户登陆邮箱校验*/
	public String getUserCheckemail() {return userCheckemail;}
	/**设置用户登陆邮箱校验*/
	public void setUserCheckemail(String userCheckemail) {this.userCheckemail = userCheckemail;}
	/**获取用户登陆手机校验*/
	public String getUserCheckmobile() {return userCheckmobile;}
	/**设置用户登陆手机校验*/
	public void setUserCheckmobile(String userCheckmobile) {this.userCheckmobile = userCheckmobile;}
	/**获取用户创建时间*/
	public String getUserCreateddate() {return userCreateddate;}
	/**设置用户创建时间*/
	public void setUserCreateddate(String userCreateddate) {this.userCreateddate = userCreateddate;}
	/**获取用户工作单位名称*/
	public String getUserWorkunit() {return userWorkunit;}
	/**设置用户工作单位名称*/
	public void setUserWorkunit(String userWorkunit) {this.userWorkunit = userWorkunit;}
	/**获取用户参加工作年份*/
	public String getUserWorkyear() {return userWorkyear;}
	/**设置用户参加工作年份*/
	public void setUserWorkyear(String userWorkyear) {this.userWorkyear = userWorkyear;}
	/**获取用户毕业院校*/
	public String getUserEduschool() {return userEduschool;}
	/**设置用户毕业院校*/
	public void setUserEduschool(String userEduschool) {this.userEduschool = userEduschool;}
	/**获取用户学历*/
	public String getUserEdulevel() {return userEdulevel;}
	/**设置用户学历*/
	public void setUserEdulevel(String userEdulevel) {this.userEdulevel = userEdulevel;}
	/**获取用户专业*/
	public String getUserEdumajor() {return userEdumajor;}
	/**设置用户专业*/
	public void setUserEdumajor(String userEdumajor) {this.userEdumajor = userEdumajor;}
	/** 获取品牌标识 */
	public String getBrandId() {return brandId;}
	/** 设置品牌标识 */
	public void setBrandId(String brandId) {this.brandId = brandId;}
	/**获取最后登录品牌*/
	public String getUserLastloginbrand() {return userLastloginbrand;}
	/**设置最后登录品牌*/
	public void setUserLastloginbrand(String userLastloginbrand) {this.userLastloginbrand = userLastloginbrand;}
	/**获取用户密码最后修改时间*/
	public String getUserLastloginpwddate() {return userLastloginpwddate;}
	/**设置用户密码最后修改时间*/
	public void setUserLastloginpwddate(String userLastloginpwddate) {this.userLastloginpwddate = userLastloginpwddate;}
	/**获取阿里推送标识*/
	public String getUserApushid() {return userApushid;}
	/**设置阿里推送标识*/
	public void setUserApushid(String userApushid) {this.userApushid = userApushid;}

	public boolean isEe() {
		return "I".equalsIgnoreCase(userFlag);
	}
}