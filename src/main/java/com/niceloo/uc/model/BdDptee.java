package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 部门员工关系POJO类
 * @version 1.0
 */
@POJO
@ClassDesc(comment="部门员工关系")
public class BdDptee {

	/** 部门员工标识 */
	@POJO(id=true,generator= ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length=36,comment="部门员工标识")
	private String dpteeId;
	/** 部门标识 */
	@POJO
	@FieldDesc(length=36,comment="部门标识")
	private String dptId;
	/** 岗位标识 */
	@POJO
	@FieldDesc(length=36,comment="岗位标识")
	private String posId;
	/** 部门员工关系(L:负责人;P:所属) */
	@POJO
	@FieldDesc(length=1,comment="部门员工关系(L:负责人;P:所属)")
	private String dpteeRelation;
	/** 员工标识 */
	@POJO
	@FieldDesc(length=36,comment="员工标识")
	private String eeId;
	/** 部门员工来源标识 */
	@POJO
	@FieldDesc(length=36,comment="部门员工来源标识")
	private String dpteeSourceid;
	/** 设置部门员工标识 */
	public void setDpteeId(String dpteeId){this.dpteeId=dpteeId;}
	/** 设置部门标识 */
	public void setDptId(String dptId){this.dptId=dptId;}
	/** 设置岗位标识 */
	public void setPosId(String posId){this.posId=posId;}
	/** 设置部门员工关系(L:负责人;P:所属) */
	public void setDpteeRelation(String dpteeRelation){this.dpteeRelation=dpteeRelation;}
	/** 设置员工标识 */
	public void setEeId(String eeId){this.eeId=eeId;}
	/** 获取部门员工标识 */
	public String getDpteeId(){return this.dpteeId;}
	/** 获取部门标识 */
	public String getDptId(){return this.dptId;}
	/** 获取岗位标识 */
	public String getPosId(){return this.posId;}
	/** 获取部门员工关系(L:负责人;P:所属) */
	public String getDpteeRelation(){return this.dpteeRelation;}
	/** 获取员工标识 */
	public String getEeId(){return this.eeId;}

	
	
	public String getDpteeSourceid() {
		return dpteeSourceid;
	}
	public void setDpteeSourceid(String dpteeSourceid) {
		this.dpteeSourceid = dpteeSourceid;
	}



	/**
	 * 部门员工关系(L:负责人;P:所属)
	 */
	public static class DpteeRelation{
	
	
		/** 负责人 */
		public static String L="L";
	
		/** 所属 */
		public static String P="P";
	
	
	}



}