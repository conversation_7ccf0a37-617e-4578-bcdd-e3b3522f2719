package com.niceloo.uc.model;

import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 字典类型类
 * @version 1.0
 */
@POJO
@ClassDesc(comment="字典类型")
public class UcDicttype {
	/** 字典类型标识 */
	@POJO(id=true,generator=ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length=36,comment="字典类型标识")
	private String dicttypeId;
	/** 字典类型名称 */
	@POJO
	@FieldDesc(length=100,comment="字典类型名称")
	private String dicttypeName;
	/**字典类型编码*/
	@POJO
	@FieldDesc(length=100,comment="字典类型编码")
	private String dicttypeCode;
	/** 字典类型描述 */
	@POJO
	@FieldDesc(length=2000,comment="字典类型描述")
	private String dicttypeMemo;
	public String getDicttypeId() {
		return dicttypeId;
	}
	public void setDicttypeId(String dicttypeId) {
		this.dicttypeId = dicttypeId;
	}
	public String getDicttypeName() {
		return dicttypeName;
	}
	public void setDicttypeName(String dicttypeName) {
		this.dicttypeName = dicttypeName;
	}
	public String getDicttypeCode() {
		return dicttypeCode;
	}
	public void setDicttypeCode(String dicttypeCode) {
		this.dicttypeCode = dicttypeCode;
	}
	public String getDicttypeMemo() {
		return dicttypeMemo;
	}
	public void setDicttypeMemo(String dicttypeMemo) {
		this.dicttypeMemo = dicttypeMemo;
	}
	
}
