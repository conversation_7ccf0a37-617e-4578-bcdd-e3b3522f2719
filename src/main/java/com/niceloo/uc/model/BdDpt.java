package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 部门POJO类
 * @version 1.0
 */
@POJO
@ClassDesc(comment="部门")
public class BdDpt {

	/** 部门标识 */
	@POJO(id=true,generator= ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length=36,comment="部门标识")
	private String dptId;
	/** 部门名称 */
	@POJO
	@FieldDesc(length=50,comment="部门名称")
	private String dptName;
	/** 部门编码 */
	@POJO
	@FieldDesc(length=50,comment="部门编码")
	private String dptCode;
	/** 部门层级编码 */
	@POJO
	@FieldDesc(length=200,comment="部门层级编码")
	private String dptLevelcode;
	/** 部门顺序 */
	@POJO
	@FieldDesc(comment="部门顺序")
	private Integer dptSeq;
	@POJO
	@FieldDesc(length=1,comment="部门类型([枚举]D:部门;S:分校;A:大区;C:全国)")
	private String dptType;
	/** 部门创建时间 */
	@POJO
	@FieldDesc(length=19,comment="部门创建时间")
	private String dptCreateddate;
	/** 部门创建人 */
	@POJO
	@FieldDesc(length=36,comment="部门创建人")
	private String dptCreater;
	/** 部门修改人 */
	@POJO
	@FieldDesc(length=36,comment="部门修改人")
	private String dptModifier;
	/** 部门修改时间 */
	@POJO
	@FieldDesc(length=19,comment="部门修改时间")
	private String dptModifieddate;
	/** 部门启用状态(Y:可用;N:不可用) */
	@POJO
	@FieldDesc(length=1,comment="部门启用状态(Y:可用;N:不可用)")
	private String dptAvlstatus;
	/** 部门删除状态(Y:已删除;N:未删除) */
	@POJO
	@FieldDesc(length=1,comment="部门删除状态(Y:已删除;N:未删除)")
	private String dptDelstatus;
	/** 部门来源标识 */
	@POJO
	@FieldDesc(length=36,comment="部门来源标识")
	private String dptSourceid;
	/** 部门关联标识 */
	@POJO
	@FieldDesc(length=36,comment="部门关联标识")
	private String dptRelationid;
	/**
	 * 部门标识(人事) 20200903 添加了该属性
	 */
	@POJO
	@FieldDesc(length = 50 ,comment = "部门标识(人事)")
	private String dptHrid;
	/** 设置部门标识 */
	public void setDptId(String dptId){this.dptId=dptId;}
	/** 设置部门名称 */
	public void setDptName(String dptName){this.dptName=dptName;}
	/** 设置部门编码 */
	public void setDptCode(String dptCode){this.dptCode=dptCode;}
	/** 设置部门层级编码 */
	public void setDptLevelcode(String dptLevelcode){this.dptLevelcode=dptLevelcode;}
	/** 设置部门顺序 */
	public void setDptSeq(Integer dptSeq){this.dptSeq=dptSeq;}
	/** 设置部门创建时间 */
	public void setDptCreateddate(String dptCreateddate){this.dptCreateddate=dptCreateddate;}
	/** 设置部门创建人 */
	public void setDptCreater(String dptCreater){this.dptCreater=dptCreater;}
	/** 设置部门修改人 */
	public void setDptModifier(String dptModifier){this.dptModifier=dptModifier;}
	/** 设置部门修改时间 */
	public void setDptModifieddate(String dptModifieddate){this.dptModifieddate=dptModifieddate;}
	/** 设置部门启用状态(Y:可用;N:不可用) */
	public void setDptAvlstatus(String dptAvlstatus){this.dptAvlstatus=dptAvlstatus;}
	/** 设置部门删除状态(Y:已删除;N:未删除) */
	public void setDptDelstatus(String dptDelstatus){this.dptDelstatus=dptDelstatus;}
	/** 设置部门来源标识 */
	public void setDptSourceid(String dptSourceid){this.dptSourceid=dptSourceid;}
	/** 获取部门标识 */
	public String getDptId(){return this.dptId;}
	/** 获取部门名称 */
	public String getDptName(){return this.dptName;}
	/** 获取部门编码 */
	public String getDptCode(){return this.dptCode;}
	/** 获取部门层级编码 */
	public String getDptLevelcode(){return this.dptLevelcode;}
	/** 获取部门顺序 */
	public Integer getDptSeq(){return this.dptSeq;}
	/** 获取部门创建时间 */
	public String getDptCreateddate(){return this.dptCreateddate;}
	/** 获取部门创建人 */
	public String getDptCreater(){return this.dptCreater;}
	/** 获取部门修改人 */
	public String getDptModifier(){return this.dptModifier;}
	/** 获取部门修改时间 */
	public String getDptModifieddate(){return this.dptModifieddate;}
	/** 获取部门启用状态(Y:可用;N:不可用) */
	public String getDptAvlstatus(){return this.dptAvlstatus;}
	/** 获取部门删除状态(Y:已删除;N:未删除) */
	public String getDptDelstatus(){return this.dptDelstatus;}
	/** 获取部门来源标识 */
	public String getDptSourceid(){return this.dptSourceid;}

	public String getDptType() {
		return dptType;
	}

	public void setDptType(String dptType) {
		this.dptType = dptType;
	}


	public String getDptRelationid() {
		return dptRelationid;
	}
	public void setDptRelationid(String dptRelationid) {
		this.dptRelationid = dptRelationid;
	}
	/**
	 * 获取部门标识(人事)
	 *
	 * @return 部门标识(人事)
	 */
	public String getDptHrid() {
		return this.dptHrid;
	}
	/**
	 * 设置部门标识(人事)
	 *
	 * @param  dptHrid 部门标识(人事)
	 */
	public void setDptHrid(String dptHrid) {
		this.dptHrid = dptHrid;
	}
	/**
	 * 部门类型(D:部门;S:分校;A:大区;C:全国)
	 */
	public static class DptType{
	
	
		/** 部门 */
		public static String D="D";
	
		/** 分校 */
		public static String S="S";
	
		/** 大区 */
		public static String A="A";
	
		/** 全国 */
		public static String C="C";
	
	
	}
	
	/**
	 * 部门启用状态(Y:可用;N:不可用)
	 */
	public static class DptAvlstatus{
	
	
		/** 可用 */
		public static String Y="Y";
	
		/** 不可用 */
		public static String N="N";
	
	
	}
	
	/**
	 * 部门删除状态(Y:已删除;N:未删除)
	 */
	public static class DptDelstatus{
	
	
		/** 已删除 */
		public static String Y="Y";
	
		/** 未删除 */
		public static String N="N";
	
	
	}



}