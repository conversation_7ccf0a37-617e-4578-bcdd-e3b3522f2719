package com.niceloo.uc.model;

import java.util.Map;

import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 字典类
 * @version 1.0
 */
@POJO
@ClassDesc(comment="字典")
public class UcDict {
	/** 字典标识 */
	@POJO(id=true,generator=ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length=36,comment="字典标识")
	private String dictId;
	/** 字典类型标识 */
	@POJO
	@FieldDesc(length=36,comment="字典类型")
	private String dicttype;
	/** 字典名称*/
	@POJO
	@FieldDesc(length=100,comment="字典名称")
	private String dictName;
	/** 字典编码 */
	@POJO
	@FieldDesc(length=100,comment="字典编码")
	private String dictCode;
	/** 字典数值*/
	@POJO
	@FieldDesc(length=2000,comment="字典数值")
	private String dictVal;
	/** 字典描述 */
	@POJO
	@FieldDesc(length=4000,comment="字典描述")
	private String dictMemo;
	/**字典级层编码*/
	@POJO
	@FieldDesc(length=2000,comment="字典级层编码")
	private String dictLevelcode;
	/**字典序号 */
	@POJO
	@FieldDesc(length=5,comment="字典序号")
	private int dictSeq;
	
	public String getDictId() {
		return dictId;
	}
	public void setDictId(String dictId) {
		this.dictId = dictId;
	}
	public String getDicttype() {
		return dicttype;
	}
	public void setDicttype(String dicttype) {
		this.dicttype = dicttype;
	}
	public String getDictName() {
		return dictName;
	}
	public void setDictName(String dictName) {
		this.dictName = dictName;
	}
	public String getDictCode() {
		return dictCode;
	}
	public void setDictCode(String dictCode) {
		this.dictCode = dictCode;
	}
	public String getDictVal() {
		return dictVal;
	}
	public void setDictVal(String dictVal) {
		this.dictVal = dictVal;
	}
	public String getDictMemo() {
		return dictMemo;
	}
	public void setDictMemo(String dictMemo) {
		this.dictMemo = dictMemo;
	}
	public String getDictLevelcode() {
		return dictLevelcode;
	}
	public void setDictLevelcode(String dictLevelcode) {
		this.dictLevelcode = dictLevelcode;
	}
	public int getDictSeq() {
		return dictSeq;
	}
	public void setDictSeq(int dictSeq) {
		this.dictSeq = dictSeq;
	}


}
