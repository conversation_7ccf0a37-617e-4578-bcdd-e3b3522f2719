package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 用户品牌关系POJO类
 * <AUTHOR>
 * @Date 2019-12-04 16:27:05
 */
@POJO
@ClassDesc(comment = "用户品牌关系")
public class UcUserbrand{

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "用户品牌关系id")
	private String userbrId;
	@POJO
	@FieldDesc(length = 36 ,comment = "用户id")
	private String userId;
	@POJO
	@FieldDesc(length = 36 ,comment = "品牌id")
	private String brandId;

	/**
	 * 获取用户品牌关系id
	 * 
	 * @return 用户品牌关系id
	 */
	public String getUserbrId() {
		return this.userbrId;
	}
	/**
	 * 设置用户品牌关系id
	 * 
	 * @param  userbrId 用户品牌关系id
	 */
	public void setUserbrId(String userbrId) {
		this.userbrId = userbrId;
	}
	/**
	 * 获取用户id
	 * 
	 * @return 用户id
	 */
	public String getUserId() {
		return this.userId;
	}
	/**
	 * 设置用户id
	 * 
	 * @param  userId 用户id
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}
	/**
	 * 获取品牌id
	 * 
	 * @return 品牌id
	 */
	public String getBrandId() {
		return this.brandId;
	}
	/**
	 * 设置品牌id
	 * 
	 * @param  brandId 品牌id
	 */
	public void setBrandId(String brandId) {
		this.brandId = brandId;
	}
}