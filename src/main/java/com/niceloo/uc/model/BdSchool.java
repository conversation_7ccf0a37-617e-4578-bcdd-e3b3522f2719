package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 学校POJO类
 * <AUTHOR>
 * @Date 2019-10-31 17:03:22
 */
@POJO
@ClassDesc(comment = "学校")
public class BdSchool {

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "学校标识")
	private String schoolId;
	@POJO
	@FieldDesc(length = 50 ,comment = "学校名称")
	private String schoolName;
	@POJO
	@FieldDesc(length = 6 ,comment = "学校地区编码")
	private String schoolAreacode;
	@POJO
	@FieldDesc(length = 100 ,comment = "学校域名")
	private String schoolDomain;
	@POJO
	@FieldDesc(length = 20 ,comment = "学校组织机构编码")
	private String schoolOrgno;
	@POJO
	@FieldDesc(length = 1 ,comment = "学校核算类型(I:独立;C:集中)")
	private String schoolBilltype;
	@POJO
	@FieldDesc(length = 1 ,comment = "学校收款类型(I:独立;C:集中)")
	private String schoolPaytype;
	@POJO
	@FieldDesc(length = 1 ,comment = "学校类型(P:省级;C:地级)")
	private String schoolType;
	@POJO
	@FieldDesc(comment = "学校排序")
	private Integer schoolSeq;
	@POJO
	@FieldDesc(length = 19 ,comment = "学校创建日期")
	private String schoolCreateddate;
	@POJO
	@FieldDesc(length = 19 ,comment = "学校修改日期")
	private String schoolModifieddate;
	@POJO
	@FieldDesc(length = 36 ,comment = "学校修改人")
	private String schoolModifier;
	@POJO
	@FieldDesc(length = 1 ,comment = "学校禁用状态(Y:可用;N:禁用)")
	private String schoolAvlstatus;
	@POJO
	@FieldDesc(length = 1 ,comment = "学校删除状态(Y:删除;N:未删除)")
	private String schoolDelstatus;
	@POJO
	@FieldDesc(length = 50 ,comment = "学校的CRM系统IP")
	private String schoolCrmip;
	@POJO
	@FieldDesc(length = 36 ,comment = "学校来源标识")
	private String schoolSourceid;

	/**
	 * 获取学校标识
	 * 
	 * @return 学校标识
	 */
	public String getSchoolId() {
		return this.schoolId;
	}
	/**
	 * 设置学校标识
	 * 
	 * @param  schoolId 学校标识
	 */
	public void setSchoolId(String schoolId) {
		this.schoolId = schoolId;
	}
	/**
	 * 获取学校名称
	 * 
	 * @return 学校名称
	 */
	public String getSchoolName() {
		return this.schoolName;
	}
	/**
	 * 设置学校名称
	 * 
	 * @param  schoolName 学校名称
	 */
	public void setSchoolName(String schoolName) {
		this.schoolName = schoolName;
	}
	/**
	 * 获取学校地区编码
	 * 
	 * @return 学校地区编码
	 */
	public String getSchoolAreacode() {
		return this.schoolAreacode;
	}
	/**
	 * 设置学校地区编码
	 * 
	 * @param  schoolAreacode 学校地区编码
	 */
	public void setSchoolAreacode(String schoolAreacode) {
		this.schoolAreacode = schoolAreacode;
	}
	/**
	 * 获取学校域名
	 * 
	 * @return 学校域名
	 */
	public String getSchoolDomain() {
		return this.schoolDomain;
	}
	/**
	 * 设置学校域名
	 * 
	 * @param  schoolDomain 学校域名
	 */
	public void setSchoolDomain(String schoolDomain) {
		this.schoolDomain = schoolDomain;
	}
	/**
	 * 获取学校组织机构编码
	 * 
	 * @return 学校组织机构编码
	 */
	public String getSchoolOrgno() {
		return this.schoolOrgno;
	}
	/**
	 * 设置学校组织机构编码
	 * 
	 * @param  schoolOrgno 学校组织机构编码
	 */
	public void setSchoolOrgno(String schoolOrgno) {
		this.schoolOrgno = schoolOrgno;
	}
	/**
	 * 获取学校核算类型(I:独立;C:集中)
	 * 
	 * @return 学校核算类型(I:独立;C:集中)
	 */
	public String getSchoolBilltype() {
		return this.schoolBilltype;
	}
	/**
	 * 设置学校核算类型(I:独立;C:集中)
	 * 
	 * @param  schoolBilltype 学校核算类型(I:独立;C:集中)
	 */
	public void setSchoolBilltype(String schoolBilltype) {
		this.schoolBilltype = schoolBilltype;
	}
	/**
	 * 获取学校收款类型(I:独立;C:集中)
	 * 
	 * @return 学校收款类型(I:独立;C:集中)
	 */
	public String getSchoolPaytype() {
		return this.schoolPaytype;
	}
	/**
	 * 设置学校收款类型(I:独立;C:集中)
	 * 
	 * @param  schoolPaytype 学校收款类型(I:独立;C:集中)
	 */
	public void setSchoolPaytype(String schoolPaytype) {
		this.schoolPaytype = schoolPaytype;
	}

	public String getSchoolType() {
		return schoolType;
	}

	public void setSchoolType(String schoolType) {
		this.schoolType = schoolType;
	}

	/**
	 * 获取学校排序
	 * 
	 * @return 学校排序
	 */
	public Integer getSchoolSeq() {
		return this.schoolSeq;
	}
	/**
	 * 设置学校排序
	 * 
	 * @param  schoolSeq 学校排序
	 */
	public void setSchoolSeq(Integer schoolSeq) {
		this.schoolSeq = schoolSeq;
	}
	/**
	 * 获取学校创建日期
	 * 
	 * @return 学校创建日期
	 */
	public String getSchoolCreateddate() {
		return this.schoolCreateddate;
	}
	/**
	 * 设置学校创建日期
	 * 
	 * @param  schoolCreateddate 学校创建日期
	 */
	public void setSchoolCreateddate(String schoolCreateddate) {
		this.schoolCreateddate = schoolCreateddate;
	}
	/**
	 * 获取学校修改日期
	 * 
	 * @return 学校修改日期
	 */
	public String getSchoolModifieddate() {
		return this.schoolModifieddate;
	}
	/**
	 * 设置学校修改日期
	 * 
	 * @param  schoolModifieddate 学校修改日期
	 */
	public void setSchoolModifieddate(String schoolModifieddate) {
		this.schoolModifieddate = schoolModifieddate;
	}
	/**
	 * 获取学校是否禁用
	 * 
	 * @return 学校是否禁用
	 */
	public String getSchoolAvlstatus() {
		return this.schoolAvlstatus;
	}
	/**
	 * 设置学校是否禁用
	 * 
	 * @param  schoolAvlstatus 学校是否禁用
	 */
	public void setSchoolAvlstatus(String schoolAvlstatus) {
		this.schoolAvlstatus = schoolAvlstatus;
	}
	/**
	 * 获取学校是否删除
	 * 
	 * @return 学校是否删除
	 */
	public String getSchoolDelstatus() {
		return this.schoolDelstatus;
	}
	/**
	 * 设置学校是否删除
	 * 
	 * @param  schoolDelstatus 学校是否删除
	 */
	public void setSchoolDelstatus(String schoolDelstatus) {
		this.schoolDelstatus = schoolDelstatus;
	}
	/**
	 * 获取学校的CRM系统IP
	 * 
	 * @return 学校的CRM系统IP
	 */
	public String getSchoolCrmip() {
		return this.schoolCrmip;
	}
	/**
	 * 设置学校的CRM系统IP
	 * 
	 * @param  schoolCrmip 学校的CRM系统IP
	 */
	public void setSchoolCrmip(String schoolCrmip) {
		this.schoolCrmip = schoolCrmip;
	}
	/**
	 * 获取学校来源标识
	 * 
	 * @return 学校来源标识
	 */
	public String getSchoolSourceid() {
		return this.schoolSourceid;
	}
	/**
	 * 设置学校来源标识
	 * 
	 * @param  schoolSourceid 学校来源标识
	 */
	public void setSchoolSourceid(String schoolSourceid) {
		this.schoolSourceid = schoolSourceid;
	}

	public String getSchoolModifier() {
		return schoolModifier;
	}

	public void setSchoolModifier(String schoolModifier) {
		this.schoolModifier = schoolModifier;
	}
}