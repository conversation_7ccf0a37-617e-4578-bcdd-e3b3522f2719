package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 客户端版本POJO类
 * <AUTHOR>
 * @Date 2019-10-10
 */
@POJO
@ClassDesc(comment = "客户端版本")
public class UcCltver{

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "客户端版本标识")
	private String cltverId;
	@POJO
	@FieldDesc(length = 36 ,comment = "客户端类型标识")
	private String clttypeId;
	@POJO
	@FieldDesc(length = 2000 ,comment = "客户端版本描述")
	private String cltverMemo;
	@POJO
	@FieldDesc(length = 200 ,comment = "客户端版本地址")
	private String cltverAddress;
	@POJO
	@FieldDesc(length = 20 ,comment = "客户端版本号")
	private String cltverCode;
	@POJO
	@FieldDesc(length = 1 ,comment = "客户端版本包类型")
	private String cltverPackagetype;
	@POJO
	@FieldDesc(length = 1 ,comment = "客户端版本发布类型")
	private String cltverPublishtype;
	@POJO
	@FieldDesc(length = 19 ,comment = "客户端版本发布时间")
	private String cltverPublishdate;
	@POJO
	@FieldDesc(length = 1 ,comment = "客户端版本迭代类型")
	private String cltverItertype;
	@POJO
	@FieldDesc(length = 1 ,comment = "客户端版本删除状态")
	private String cltverDelstatus;
	@POJO
	@FieldDesc(length = 19 ,comment = "客户端版本创建时间")
	private String cltverCreatedate;
	@POJO
	@FieldDesc(length = 36 ,comment = "客户端版本创建人")
	private String cltverCreater;
	@POJO
	@FieldDesc(length = 200 ,comment = "客户端版本创建人姓名")
	private String cltverCreatername;
	@POJO
	@FieldDesc(length = 19 ,comment = "客户端版本修改时间")
	private String cltverModifieddate;
	@POJO
	@FieldDesc(length = 36 ,comment = "客户端版本修改人")
	private String cltverModifier;
	@POJO
	@FieldDesc(length = 200 ,comment = "客户端版本修改人姓名")
	private String cltverModifiername;
	@POJO
	@FieldDesc(length = 1 ,comment = "客户端版本市场审核状态")//Y在审,N:未在审
	private String cltverMarkcheckstatus;
	@POJO
	@FieldDesc(length = 2 ,comment = "客户端版本延迟天")
	private int cltverDelaymaxday;
	@POJO
	@FieldDesc(length = 1 ,comment = "客户端版本可用状态")//Y:可用;N:不可用
	private String cltverAvlstatus;
	/**
	 * 获取客户端版本标识
	 * 
	 * @return 客户端版本标识
	 */
	public String getCltverId() {
		return this.cltverId;
	}
	/**
	 * 设置客户端版本标识
	 * 
	 * @param  cltverId 客户端版本标识
	 */
	public void setCltverId(String cltverId) {
		this.cltverId = cltverId;
	}
	/**
	 * 获取客户端类型标识
	 * 
	 * @return 客户端类型标识
	 */
	public String getClttypeId() {
		return this.clttypeId;
	}
	/**
	 * 设置客户端类型标识
	 * 
	 * @param  clttypeId 客户端版本类型标识
	 */
	public void setClttypeId(String clttypeId) {
		this.clttypeId = clttypeId;
	}
	/**
	 * 获取客户端版本描述
	 * 
	 * @return 客户端版本描述
	 */
	public String getCltverMemo() {
		return this.cltverMemo;
	}
	/**
	 * 设置客户端版本描述
	 * 
	 * @param  cltverMemo 客户端版本描述
	 */
	public void setCltverMemo(String cltverMemo) {
		this.cltverMemo = cltverMemo;
	}
	/**
	 * 获取客户端版本地址
	 * 
	 * @return 客户端版本地址
	 */
	public String getCltverAddress() {
		return this.cltverAddress;
	}
	/**
	 * 设置客户端版本地址
	 * 
	 * @param  cltverAddress 客户端版本地址
	 */
	public void setCltverAddress(String cltverAddress) {
		this.cltverAddress = cltverAddress;
	}
	/**
	 * 获取客户端版本号
	 * 
	 * @return 客户端版本号
	 */
	public String getCltverCode() {
		return this.cltverCode;
	}
	/**
	 * 设置客户端版本号
	 * 
	 * @param  cltverCode 客户端版本编号
	 */
	public void setCltverCode(String cltverCode) {
		this.cltverCode = cltverCode;
	}
	/**
	 * 获取客户端版本包类型
	 * 
	 * @return 客户端版本包类型
	 */
	public String getCltverPackagetype() {
		return this.cltverPackagetype;
	}
	/**
	 * 设置客户端版本包类型
	 * 
	 * @param  cltverPagetype 客户端版本包类型
	 */
	public void setCltverPackagetype(String cltverPackagetype) {
		this.cltverPackagetype = cltverPackagetype;
	}
	/**
	 * 获取客户端版本发布类型
	 * 
	 * @return 客户端版本发布类型
	 */
	public String getCltverPublishtype() {
		return this.cltverPublishtype;
	}
	/**
	 * 设置客户端版本发布类型
	 * 
	 * @param  cltverPublishtype 客户端版本发布类型
	 */
	public void setCltverPublishtype(String cltverPublishtype) {
		this.cltverPublishtype = cltverPublishtype;
	}
	/**
	 * 获取客户端版本发布时间
	 * 
	 * @return 客户端版本发布时间
	 */
	public String getCltverPublishdate() {
		return this.cltverPublishdate;
	}
	/**
	 * 设置客户端版本发布时间
	 * 
	 * @param  cltverPublishdate 客户端版本发布时间
	 */
	public void setCltverPublishdate(String cltverPublishdate) {
		this.cltverPublishdate = cltverPublishdate;
	}
	/**
	 * 获取客户端版本删除状态
	 * 
	 * @return 客户端版本删除状态
	 */
	public String getCltverDelstatus() {
		return this.cltverDelstatus;
	}
	/**
	 * 设置客户端版本删除状态
	 * 
	 * @param  cltverDelstatus 客户端版本更新类型
	 */
	public void setCltverDelstatus(String cltverDelstatus) {
		this.cltverDelstatus = cltverDelstatus;
	}
	/**
	 * 获取客户端版本创建时间
	 * 
	 * @return 客户端版本创建时间
	 */
	public String getCltverCreatedate() {
		return this.cltverCreatedate;
	}
	/**
	 * 设置客户端版本创建时间
	 * 
	 * @param  cltverCreatedate 客户端版本创建时间
	 */
	public void setCltverCreatedate(String cltverCreatedate) {
		this.cltverCreatedate = cltverCreatedate;
	}
	/**
	 * 获取客户端版本创建人
	 * 
	 * @return 客户端版本创建人
	 */
	public String getCltverCreater() {
		return this.cltverCreater;
	}
	/**
	 * 设置客户端版本创建人
	 * 
	 * @param  cltverCreater 客户端版本创建人
	 */
	public void setCltverCreater(String cltverCreater) {
		this.cltverCreater = cltverCreater;
	}
	/**
	 * 获取客户端版本创建人姓名
	 * 
	 * @return 客户端版本创建人姓名
	 */
	public String getCltverCreatername() {
		return this.cltverCreatername;
	}
	/**
	 * 设置客户端版本创建人姓名
	 * 
	 * @param  cltverCreatername 客户端版本创建人姓名
	 */
	public void setCltverCreatername(String cltverCreatername) {
		this.cltverCreatername = cltverCreatername;
	}
	/**
	 * 获取客户端版本修改时间
	 * 
	 * @return 客户端版本修改时间
	 */
	public String getCltverModifieddate() {
		return this.cltverModifieddate;
	}
	/**
	 * 设置客户端版本修改时间
	 * 
	 * @param  cltverModifieddate 客户端版本修改时间
	 */
	public void setCltverModifieddate(String cltverModifieddate) {
		this.cltverModifieddate = cltverModifieddate;
	}
	/**
	 * 获取客户端版本修改人
	 * 
	 * @return 客户端版本修改人
	 */
	public String getCltverModifier() {
		return this.cltverModifier;
	}
	/**
	 * 设置客户端版本修改人
	 * 
	 * @param  cltverModifier 客户端版本修改人
	 */
	public void setCltverModifier(String cltverModifier) {
		this.cltverModifier = cltverModifier;
	}
	/**
	 * 获取客户端版本修改人姓名
	 * 
	 * @return 客户端版本修改人姓名
	 */
	public String getCltverModifiername() {
		return this.cltverModifiername;
	}
	/**
	 * 设置客户端版本修改人姓名
	 * 
	 * @param  cltverModifiername 客户端版本修改人姓名
	 */
	public void setCltverModifiername(String cltverModifiername) {
		this.cltverModifiername = cltverModifiername;
	}
	/**
	 * 获取客户端版本迭代类型
	 * 
	 * @return 客户端版本迭代类型
	 */
	public String getCltverItertype() {
		return cltverItertype;
	}
	/**
	 * 设置客户端版本迭代类型
	 * 
	 * @param  cltverItertype 客户端版本迭代类型
	 */
	public void setCltverItertype(String cltverItertype) {
		this.cltverItertype = cltverItertype;
	}
	
	/**
	 * 设置客户端版本市场审核状态
	 * 
	 * @param  cltverMarkcheckstatus 客户端版本市场审核状态
	 */
	public String getCltverMarkcheckstatus() {
		return cltverMarkcheckstatus;
	}
	/**
	 * 设置客户端版本市场审核状态
	 * 
	 * @param  cltverMarkcheckstatus 客户端版本市场审核状态
	 */
	public void setCltverMarkcheckstatus(String cltverMarkcheckstatus) {
		this.cltverMarkcheckstatus = cltverMarkcheckstatus;
	}
	
	public int getCltverDelaymaxday() {
		return cltverDelaymaxday;
	}
	
	public void setCltverDelaymaxday(int cltverDelaymaxday) {
		this.cltverDelaymaxday = cltverDelaymaxday;
	}
	
	public String getCltverAvlstatus() {
		return cltverAvlstatus;
	}
	
	public void setCltverAvlstatus(String cltverAvlstatus) {
		this.cltverAvlstatus = cltverAvlstatus;
	}
	
	
}