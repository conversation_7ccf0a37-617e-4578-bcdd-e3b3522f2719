package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 用户与角色关系 独立表
 * <AUTHOR>
 * @Date 2019-11-18 10:35:12
 */
@POJO
@ClassDesc(comment = "用户角色关系")
public class UcUserrole {

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "用户角色关系id")
	private String userroleId;
	@POJO
	@FieldDesc(length = 36 ,comment = "用户id")
	private String userId;
	@POJO
	@FieldDesc(length = 36 ,comment = "角色id")
	private String roleId;
	@POJO
	@FieldDesc(length = 36 ,comment = "是否是超级管理员")
	private String roleAdminstatus;
	@POJO
	@FieldDesc(length = 36 ,comment = "品牌标识")
	private String brandId;

	public String getUserroleId() {
		return userroleId;
	}

	public void setUserroleId(String userroleId) {
		this.userroleId = userroleId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getRoleAdminstatus() {
		return roleAdminstatus;
	}

	public void setRoleAdminstatus(String roleAdminstatus) {
		this.roleAdminstatus = roleAdminstatus;
	}

	public String getBrandId() {
		return brandId;
	}

	public void setBrandId(String brandId) {
		this.brandId = brandId;
	}
}