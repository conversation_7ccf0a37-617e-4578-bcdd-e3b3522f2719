package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 资源策略POJO类
 * <AUTHOR>
 * @Date 2019-11-18 15:26:54
 */
@POJO
@ClassDesc(comment = "资源策略")
public class UcDatapolicy{

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "资源策略标识")
	private String datapolicyId;
	@POJO
	@FieldDesc(length = 40 ,comment = "资源策略code")
	private String datapolicyCode;
	@POJO
	@FieldDesc(length = 50 ,comment = "资源策略名称")
	private String datapolicyName;
	@POJO
	@FieldDesc(length = 50 ,comment = "资源策略分组")
	private String datapolicyGroup;
	@POJO
	@FieldDesc(length = 50 ,comment = "资源策略排序")
	private Integer datapolicySeq;

	/**
	 * 获取资源策略标识
	 * 
	 * @return 资源策略标识
	 */
	public String getDatapolicyId() {
		return this.datapolicyId;
	}
	/**
	 * 设置资源策略标识
	 * 
	 * @param  datapolicyId 资源策略标识
	 */
	public void setDatapolicyId(String datapolicyId) {
		this.datapolicyId = datapolicyId;
	}
	/**
	 * 获取资源策略名称
	 * 
	 * @return 资源策略名称
	 */
	public String getDatapolicyCode() {
		return this.datapolicyCode;
	}
	/**
	 * 设置资源策略名称
	 * 
	 * @param  datapolicyCode 资源策略名称
	 */
	public void setDatapolicyCode(String datapolicyCode) {
		this.datapolicyCode = datapolicyCode;
	}
	/**
	 * 获取资源策略类型
	 * 
	 * @return 资源策略类型
	 */
	public String getDatapolicyName() {
		return this.datapolicyName;
	}
	/**
	 * 设置资源策略类型
	 * 
	 * @param  datapolicyName 资源策略类型
	 */
	public void setDatapolicyName(String datapolicyName) {
		this.datapolicyName = datapolicyName;
	}

	public String getDatapolicyGroup() {
		return datapolicyGroup;
	}

	public void setDatapolicyGroup(String datapolicyGroup) {
		this.datapolicyGroup = datapolicyGroup;
	}

	public Integer getDatapolicySeq() {
		return datapolicySeq;
	}

	public void setDatapolicySeq(Integer datapolicySeq) {
		this.datapolicySeq = datapolicySeq;
	}
}