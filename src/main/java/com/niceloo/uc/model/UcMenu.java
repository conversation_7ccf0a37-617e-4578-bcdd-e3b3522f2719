package com.niceloo.uc.model;
import com.niceloo.core.mongo.annonation.Field;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 菜单POJO类
 * <AUTHOR>
 * @Date 2019-11-18 10:35:12
 */
@POJO
@ClassDesc(comment = "菜单")
public class UcMenu{

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "菜单标识")
	private String menuId;
	@POJO
	@FieldDesc(length = 40 ,comment = "菜单名称")
	private String menuName;
	@POJO
	@FieldDesc(length = 40 ,comment = "菜单类型")
	private String menuType;
	@POJO
	@FieldDesc(length = 500 ,comment = "菜单地址")
	private String menuUrl;
	@POJO
	@FieldDesc(length = 100 ,comment = "权限编码")
	private String menuCode;
	@POJO
	@FieldDesc(length = 1000 ,comment = "关联权限")
	private String menuExtcode;

	@POJO
	@FieldDesc(length = 200 ,comment = "菜单编码")
	private String menuParam;

	@POJO
	@FieldDesc(length = 255,comment = "菜单层级编码")
	private String menuLevelcode;
	@POJO
	@FieldDesc(length = 500 ,comment = "菜单备注")
	private String menuMemo;
	@POJO
	@FieldDesc(comment = "菜单顺序")
	private Integer menuSeq;
	@POJO
	@FieldDesc(length = 500 ,comment = "功能数据权限")
	private String menuDatapolicy;
	@POJO
	@FieldDesc(length = 1 ,comment = "菜单禁用状态")
	private String menuDisablestatus;
	@POJO
	@FieldDesc(length = 200 ,comment = "菜单图标")
	private String menuIcon;
	@POJO
	@FieldDesc(length = 19 ,comment = "菜单创建时间")
	private String menuCreatedate;
	@POJO
	@FieldDesc(length = 19 ,comment = "菜单修改时间")
	private String menuModifieddate;
	/**
	 * 获取菜单标识
	 * 
	 * @return 菜单标识
	 */
	public String getMenuId() {
		return this.menuId;
	}
	/**
	 * 设置菜单标识
	 * 
	 * @param  menuId 菜单标识
	 */
	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}
	/**
	 * 获取菜单名称
	 * 
	 * @return 菜单名称
	 */
	public String getMenuName() {
		return this.menuName;
	}
	/**
	 * 设置菜单名称
	 * 
	 * @param  menuName 菜单名称
	 */
	public void setMenuName(String menuName) {
		this.menuName = menuName;
	}
	/**
	 * 获取菜单名称
	 * 
	 * @return 菜单名称
	 */
	public String getMenuType() {
		return this.menuType;
	}
	/**
	 * 设置菜单名称
	 * 
	 * @param  menuType 菜单名称
	 */
	public void setMenuType(String menuType) {
		this.menuType = menuType;
	}
	/**
	 * 获取菜单地址
	 * 
	 * @return 菜单地址
	 */
	public String getMenuUrl() {
		return this.menuUrl;
	}
	/**
	 * 设置菜单地址
	 * 
	 * @param  menuUrl 菜单地址
	 */
	public void setMenuUrl(String menuUrl) {
		this.menuUrl = menuUrl;
	}
	/**
	 * 获取菜单编码
	 * 
	 * @return 菜单编码
	 */
	public String getMenuCode() {
		return this.menuCode;
	}
	/**
	 * 设置菜单编码
	 * 
	 * @param  menuCode 菜单编码
	 */
	public void setMenuCode(String menuCode) {
		this.menuCode = menuCode;
	}
	/**
	 * 获取菜单层级编码
	 * 
	 * @return 菜单层级编码
	 */
	public String getMenuLevelcode() {
		return this.menuLevelcode;
	}
	/**
	 * 设置菜单层级编码
	 * 
	 * @param  menuLevelcode 菜单层级编码
	 */
	public void setMenuLevelcode(String menuLevelcode) {
		this.menuLevelcode = menuLevelcode;
	}
	/**
	 * 获取菜单备注
	 * 
	 * @return 菜单备注
	 */
	public String getMenuMemo() {
		return this.menuMemo;
	}
	/**
	 * 设置菜单备注
	 * 
	 * @param  menuMemo 菜单备注
	 */
	public void setMenuMemo(String menuMemo) {
		this.menuMemo = menuMemo;
	}

	public Integer getMenuSeq() {
		return menuSeq;
	}

	public void setMenuSeq(Integer menuSeq) {
		this.menuSeq = menuSeq;
	}

	/**
	 * 获取功能数据权限
	 * 
	 * @return 功能数据权限
	 */
	public String getMenuDatapolicy() {
		return this.menuDatapolicy;
	}
	/**
	 * 设置功能数据权限
	 * 
	 * @param  menuDatapolicy 功能数据权限
	 */
	public void setMenuDatapolicy(String menuDatapolicy) {
		this.menuDatapolicy = menuDatapolicy;
	}
	/**
	 * 获取菜单禁用状态
	 * 
	 * @return 菜单禁用状态
	 */
	public String getMenuDisablestatus() {
		return this.menuDisablestatus;
	}
	/**
	 * 设置菜单禁用状态
	 * 
	 * @param  menuDisablestatus 菜单禁用状态
	 */
	public void setMenuDisablestatus(String menuDisablestatus) {
		this.menuDisablestatus = menuDisablestatus;
	}
	/**
	 * 获取菜单图标
	 * 
	 * @return 菜单图标
	 */
	public String getMenuIcon() {
		return this.menuIcon;
	}
	/**
	 * 设置菜单图标
	 * 
	 * @param  menuIcon 菜单图标
	 */
	public void setMenuIcon(String menuIcon) {
		this.menuIcon = menuIcon;
	}

	public String getMenuParam() {
		return menuParam;
	}

	public void setMenuParam(String menuParam) {
		this.menuParam = menuParam;
	}
	/**
	 * 获取菜单创建时间
	 *
	 * @return 菜单创建时间
	 */
	public String getMenuCreatedate() {
		return menuCreatedate;
	}
	/**
	 * 设置菜单创建时间
	 *
	 * @param  menuCreatedate 菜单创建时间
	 */
	public void setMenuCreatedate(String menuCreatedate) {
		this.menuCreatedate = menuCreatedate;
	}

	/**
	 * 获取菜单修改时间
	 *
	 * @return 菜单修改时间
	 */
	public String getMenuModifieddate() {
		return menuModifieddate;
	}

	/**
	 * 设置菜单修改时间
	 *
	 * @param  menuModifieddate  菜单修改时间
	 */
	public void setMenuModifieddate(String menuModifieddate) {
		this.menuModifieddate = menuModifieddate;
	}

	public String getMenuExtcode() {
		return menuExtcode;
	}

	public void setMenuExtcode(String menuExtcode) {
		this.menuExtcode = menuExtcode;
	}
}