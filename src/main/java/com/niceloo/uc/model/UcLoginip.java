package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * ip名单POJO类
 * <AUTHOR>
 * @Date 2020-03-14 14:03:54
 */
@POJO
@ClassDesc(comment = "ip名单")
public class UcLoginip {

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "ip名单标识")
	private String loginipId;
	@POJO
	@FieldDesc(length = 50 ,comment = "ip")
	private String loginip;
	@POJO
	@FieldDesc(length = 1 ,comment = "ip类型")
	private String loginipType;
	@POJO
	@FieldDesc(length = 36 ,comment = "创建人")
	private String loginipCreator;
	@POJO
	@FieldDesc(length = 19 ,comment = "创建时间")
	private String loginipCreateddate;
	@POJO
	@FieldDesc(length = 1 ,comment = "校验方式")
	private String loginipChecktype;
	@POJO
	@FieldDesc(length = 50 ,comment = "备注")
	private String loginipMemo;
	@POJO
	@FieldDesc(length = 1,comment = "ip精确度")
	private String loginipAccuracy;
	@POJO
	@FieldDesc(length = 50,comment = "创建人姓名")
	private String loginipCreatorname;
	@POJO
	@FieldDesc(comment = "起始ip值")
	private String loginipStart;
	@POJO
	@FieldDesc(comment = "结束ip值")
	private String loginipEnd;

	/**
	 * 获取ip名单标识
	 * 
	 * @return ip名单标识
	 */
	public String getLoginipId() {
		return this.loginipId;
	}
	/**
	 * 设置ip名单标识
	 * 
	 * @param  loginipId ip名单标识
	 */
	public void setLoginipId(String loginipId) {
		this.loginipId = loginipId;
	}
	/**
	 * 获取ip
	 * 
	 * @return ip
	 */
	public String getLoginip() {
		return this.loginip;
	}
	/**
	 * 设置ip
	 * 
	 * @param  loginip ip
	 */
	public void setLoginip(String loginip) {
		this.loginip = loginip;
	}
	/**
	 * 获取ip类型
	 * 
	 * @return ip类型
	 */
	public String getLoginipType() {
		return this.loginipType;
	}
	/**
	 * 设置ip类型
	 * 
	 * @param  loginipType ip类型
	 */
	public void setLoginipType(String loginipType) {
		this.loginipType = loginipType;
	}
	/**
	 * 获取创建人
	 * 
	 * @return 创建人
	 */
	public String getLoginipCreator() {
		return this.loginipCreator;
	}
	/**
	 * 设置创建人
	 * 
	 * @param  loginipCreator 创建人
	 */
	public void setLoginipCreator(String loginipCreator) {
		this.loginipCreator = loginipCreator;
	}
	/**
	 * 获取创建时间
	 * 
	 * @return 创建时间
	 */
	public String getLoginipCreateddate() {
		return this.loginipCreateddate;
	}
	/**
	 * 设置创建时间
	 * 
	 * @param  loginipCreateddate 创建时间
	 */
	public void setLoginipCreateddate(String loginipCreateddate) {
		this.loginipCreateddate = loginipCreateddate;
	}
	/**
	 * 获取校验方式
	 * 
	 * @return 校验方式
	 */
	public String getLoginipChecktype() {
		return this.loginipChecktype;
	}
	/**
	 * 设置校验方式
	 * 
	 * @param  loginipChecktype 校验方式
	 */
	public void setLoginipChecktype(String loginipChecktype) {
		this.loginipChecktype = loginipChecktype;
	}
	/**
	 * 获取备注
	 * 
	 * @return 备注
	 */
	public String getLoginipMemo() {
		return this.loginipMemo;
	}
	/**
	 * 设置备注
	 * 
	 * @param  loginipMemo 备注
	 */
	public void setLoginipMemo(String loginipMemo) {
		this.loginipMemo = loginipMemo;
	}

	public String getLoginipAccuracy() {
		return loginipAccuracy;
	}

	public void setLoginipAccuracy(String loginipAccuracy) {
		this.loginipAccuracy = loginipAccuracy;
	}

	public String getLoginipCreatorname() {
		return loginipCreatorname;
	}

	public void setLoginipCreatorname(String loginipCreatorname) {
		this.loginipCreatorname = loginipCreatorname;
	}

	public String getLoginipStart() {
		return loginipStart;
	}

	public void setLoginipStart(String loginipStart) {
		this.loginipStart = loginipStart;
	}

	public String getLoginipEnd() {
		return loginipEnd;
	}

	public void setLoginipEnd(String loginipEnd) {
		this.loginipEnd = loginipEnd;
	}
}