package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 客户端类型POJO类
 * <AUTHOR>
 * @Date 2019-09-25
 */
@POJO
@ClassDesc(comment = "客户端类型")
public class UcClttype{

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "客户端类型标识")
	private String clttypeId;
	@POJO
	@FieldDesc(length = 50 ,comment = "客户端类型编码")
	private String clttypeCode;
	@POJO
	@FieldDesc(length = 100 ,comment = "客户端类型名称")
	private String clttypeName;
	@POJO
	@FieldDesc(length = 20 ,comment = "客户端类型终端")
	private String clttypeTerminal;
	@POJO
	@FieldDesc(length = 1 ,comment = "客户端类型删除状态")
	private String clttypeDelstatus;

	/**
	 * 获取客户端类型标识
	 * 
	 * @return 客户端类型标识
	 */
	public String getClttypeId() {
		return this.clttypeId;
	}
	/**
	 * 设置客户端类型标识
	 * 
	 * @param  clttypeId 客户端版本类型标识
	 */
	public void setClttypeId(String clttypeId) {
		this.clttypeId = clttypeId;
	}
	/**
	 * 获取客户端类型编码
	 * 
	 * @return 客户端类型编码
	 */
	public String getClttypeCode() {
		return this.clttypeCode;
	}
	/**
	 * 设置客户端类型编码
	 * 
	 * @param  clttypeCode 客户端版本类型编码
	 */
	public void setClttypeCode(String clttypeCode) {
		this.clttypeCode = clttypeCode;
	}
	/**
	 * 获取客户端类型名称
	 * 
	 * @return 客户端类型名称
	 */
	public String getClttypeName() {
		return this.clttypeName;
	}
	/**
	 * 设置客户端类型名称
	 * 
	 * @param  clttypeName 客户端版本类型名称
	 */
	public void setClttypeName(String clttypeName) {
		this.clttypeName = clttypeName;
	}
	/**
	 * 获取客户端类型终端
	 * 
	 * @return 客户端类型终端
	 */
	public String getClttypeTerminal() {
		return this.clttypeTerminal;
	}
	/**
	 * 设置客户端类型终端
	 * 
	 * @param  clttypeTerminal 客户端版本类型终端(PC:PC;ANDROID:安卓;H5:H5;IOS:IOS)
	 */
	public void setClttypeTerminal(String clttypeTerminal) {
		this.clttypeTerminal = clttypeTerminal;
	}
	/**
	 * 获取客户端类型删除状态
	 * 
	 * @return 客户端类型删除状态
	 */
	public String getClttypeDelstatus() {
		return this.clttypeDelstatus;
	}
	/**
	 * 设置客户端类型删除状态
	 * 
	 * @param  clttypeDelstatus 客户端类型删除状态
	 */
	public void setClttypeDelstatus(String clttypeDelstatus) {
		this.clttypeDelstatus = clttypeDelstatus;
	}
}