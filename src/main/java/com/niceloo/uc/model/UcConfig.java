package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 配置POJO类
 * <AUTHOR>
 * @Date 2019年1月7日 15:06:22
 */
@POJO
@ClassDesc(comment = "配置")
public class UcConfig{

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "配置标识")
	private String configId;
	@POJO
	@FieldDesc(length = 100 ,comment = "配置类型")
	private String configType;
	@POJO
	@FieldDesc(length = 100 ,comment = "配置编码")
	private String configCode;
	@POJO
	@FieldDesc(length = 2000 ,comment = "配置数值")
	private String configValue;
	@POJO
	@FieldDesc(length = 10 ,comment = "配置范围类型")
	private String configScopetype;
	@POJO
	@FieldDesc(length = 36 ,comment = "配置范围对象")
	private String configScopeobj;
	@POJO
	@FieldDesc(length = 10 ,comment = "配置数据类型")
	private String configUnittype;
	@POJO
	@FieldDesc(length = 36 ,comment = "配置数据对象")
	private String configUnitobj;

	/**
	 * 获取配置标识
	 * 
	 * @return 配置标识
	 */
	public String getConfigId() {
		return this.configId;
	}
	/**
	 * 设置配置标识
	 * 
	 * @param  configId 配置标识
	 */
	public void setConfigId(String configId) {
		this.configId = configId;
	}
	/**
	 * 获取配置类型
	 * 
	 * @return 配置类型
	 */
	public String getConfigType() {
		return this.configType;
	}
	/**
	 * 设置配置类型
	 * 
	 * @param  configType 配置类型
	 */
	public void setConfigType(String configType) {
		this.configType = configType;
	}
	/**
	 * 获取配置编码
	 * 
	 * @return 配置编码
	 */
	public String getConfigCode() {
		return this.configCode;
	}
	/**
	 * 设置配置编码
	 * 
	 * @param  configCode 配置编码
	 */
	public void setConfigCode(String configCode) {
		this.configCode = configCode;
	}
	/**
	 * 获取配置数值
	 * 
	 * @return 配置数值
	 */
	public String getConfigValue() {
		return this.configValue;
	}
	/**
	 * 设置配置数值
	 * 
	 * @param  configValue 配置数值
	 */
	public void setConfigValue(String configValue) {
		this.configValue = configValue;
	}
	/**
	 * 获取配置范围类型
	 * 
	 * @return 配置范围类型
	 */
	public String getConfigScopetype() {
		return this.configScopetype;
	}
	/**
	 * 设置配置范围类型
	 * 
	 * @param  configScopetype 配置范围类型(G:全局;R:角色;U:用户)
	 */
	public void setConfigScopetype(String configScopetype) {
		this.configScopetype = configScopetype;
	}
	/**
	 * 获取配置范围对象
	 * 
	 * @return 配置范围对象
	 */
	public String getConfigScopeobj() {
		return this.configScopeobj;
	}
	/**
	 * 设置配置范围对象
	 * 
	 * @param  configScopeobj 配置范围对象
	 */
	public void setConfigScopeobj(String configScopeobj) {
		this.configScopeobj = configScopeobj;
	}
	/**
	 * 获取配置数据类型
	 * 
	 * @return 配置数据类型
	 */
	public String getConfigUnittype() {
		return this.configUnittype;
	}
	/**
	 * 设置配置数据类型
	 * 
	 * @param  configUnittype 配置数据类型
	 */
	public void setConfigUnittype(String configUnittype) {
		this.configUnittype = configUnittype;
	}
	/**
	 * 获取配置数据对象
	 * 
	 * @return 配置数据对象
	 */
	public String getConfigUnitobj() {
		return this.configUnitobj;
	}
	/**
	 * 设置配置数据对象
	 * 
	 * @param  configUnitobj 配置数据对象
	 */
	public void setConfigUnitobj(String configUnitobj) {
		this.configUnitobj = configUnitobj;
	}
}