package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 角色权限POJO类
 * <AUTHOR>
 * @Date 2019-11-18 10:35:12
 */
@POJO
@ClassDesc(comment = "角色权限")
public class UcRolemenu{

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "角色权限标识")
	private String rolemenuId;
	@POJO
	@FieldDesc(length = 36 ,comment = "角色标识")
	private String roleId;
	@POJO
	@FieldDesc(length = 36 ,comment = "权限标识")
	private String menuId;
	@POJO
	@FieldDesc(length = 1000 ,comment = "资源策略")
	private String roleDatapolicy;

	/**
	 * 获取角色权限标识
	 * 
	 * @return 角色权限标识
	 */
	public String getRolemenuId() {
		return this.rolemenuId;
	}
	/**
	 * 设置角色权限标识
	 * 
	 * @param  rolemenuId 角色权限标识
	 */
	public void setRolemenuId(String rolemenuId) {
		this.rolemenuId = rolemenuId;
	}
	/**
	 * 获取角色标识
	 * 
	 * @return 角色标识
	 */
	public String getRoleId() {
		return this.roleId;
	}
	/**
	 * 设置角色标识
	 * 
	 * @param  roleId 角色标识
	 */
	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
	/**
	 * 获取权限标识
	 * 
	 * @return 权限标识
	 */
	public String getMenuId() {
		return this.menuId;
	}
	/**
	 * 设置权限标识
	 * 
	 * @param  menuId 权限标识
	 */
	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}
	/**
	 * 获取资源策略
	 * 
	 * @return 资源策略
	 */
	public String getRoleDatapolicy() {
		return this.roleDatapolicy;
	}
	/**
	 * 设置资源策略
	 * 
	 * @param  roleDatapolicy 资源策略
	 */
	public void setRoleDatapolicy(String roleDatapolicy) {
		this.roleDatapolicy = roleDatapolicy;
	}
}