package com.niceloo.uc.model;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 客户端灰度POJO类
 * <AUTHOR>
 * @Date 2019-09-25
 */
@POJO
@ClassDesc(comment = "客户端灰度")
public class UcCltgray{

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "客户端灰度标识")
	private String cltgrayId;
	@POJO
	@FieldDesc(length = 36 ,comment = "客户端类型标识")
	private String clttypeId;
	@POJO
	@FieldDesc(length = 1 ,comment = "客户端灰度类型")
	private String cltgrayType;
	@POJO
	@FieldDesc(length = 200 ,comment = "客户端灰度对象")
	private String cltgrayObject;
	@POJO
	@FieldDesc(length = 200 ,comment = "客户端灰度对象名称")
	private String cltgrayObjectname;
	@POJO
	@FieldDesc(length = 19 ,comment = "客户端灰度创建时间")
	private String cltgrayCreatedate;
	@POJO
	@FieldDesc(length = 36 ,comment = "客户端灰度创建人")
	private String cltgrayCreater;
	@POJO
	@FieldDesc(length = 200 ,comment = "客户端灰度创建人姓名")
	private String cltgrayCreatername;

	/**
	 * 获取客户端灰度标识
	 * 
	 * @return 客户端灰度标识
	 */
	public String getCltgrayId() {
		return this.cltgrayId;
	}
	/**
	 * 设置客户端灰度标识
	 * 
	 * @param  cltgrayId 客户端灰度标识
	 */
	public void setCltgrayId(String cltgrayId) {
		this.cltgrayId = cltgrayId;
	}
	/**
	 * 获取客户端类型标识
	 * 
	 * @return 客户端类型标识
	 */
	public String getClttypeId() {
		return this.clttypeId;
	}
	/**
	 * 设置客户端类型标识
	 * 
	 * @param  clttypeId 客户端类型标识
	 */
	public void setClttypeId(String clttypeId) {
		this.clttypeId = clttypeId;
	}
	/**
	 * 获取客户端灰度类型
	 * 
	 * @return 客户端灰度类型
	 */
	public String getCltgrayType() {
		return this.cltgrayType;
	}
	/**
	 * 设置客户端灰度类型
	 * 
	 * @param  cltgrayType 客户端灰度类型
	 */
	public void setCltgrayType(String cltgrayType) {
		this.cltgrayType = cltgrayType;
	}
	/**
	 * 获取客户端灰度对象
	 * 
	 * @return 客户端灰度对象
	 */
	public String getCltgrayObject() {
		return this.cltgrayObject;
	}
	/**
	 * 设置客户端灰度对象
	 * 
	 * @param  cltgrayObject 客户端灰度对象
	 */
	public void setCltgrayObject(String cltgrayObject) {
		this.cltgrayObject = cltgrayObject;
	}
	/**
	 * 获取客户端灰度对象名称
	 * 
	 * @return 客户端灰度对象名称
	 */
	public String getCltgrayObjectname() {
		return this.cltgrayObjectname;
	}
	/**
	 * 设置客户端灰度对象名称
	 * 
	 * @param  cltgrayObjectname 客户端灰度对象名称
	 */
	public void setCltgrayObjectname(String cltgrayObjectname) {
		this.cltgrayObjectname = cltgrayObjectname;
	}
	/**
	 * 获取客户端灰度创建时间
	 * 
	 * @return 客户端灰度创建时间
	 */
	public String getCltgrayCreatedate() {
		return this.cltgrayCreatedate;
	}
	/**
	 * 设置客户端灰度创建时间
	 * 
	 * @param  cltgrayCreatedate 客户端灰度创建时间
	 */
	public void setCltgrayCreatedate(String cltgrayCreatedate) {
		this.cltgrayCreatedate = cltgrayCreatedate;
	}
	/**
	 * 获取客户端灰度创建人
	 * 
	 * @return 客户端灰度创建人
	 */
	public String getCltgrayCreater() {
		return this.cltgrayCreater;
	}
	/**
	 * 设置客户端灰度创建人
	 * 
	 * @param  cltgrayCreater 客户端灰度创建人
	 */
	public void setCltgrayCreater(String cltgrayCreater) {
		this.cltgrayCreater = cltgrayCreater;
	}
	/**
	 * 获取客户端灰度创建人姓名
	 * 
	 * @return 客户端灰度创建人姓名
	 */
	public String getCltgrayCreatername() {
		return this.cltgrayCreatername;
	}
	/**
	 * 设置客户端灰度创建人姓名
	 * 
	 * @param  cltgrayCreatername 客户端灰度创建人姓名
	 */
	public void setCltgrayCreatername(String cltgrayCreatername) {
		this.cltgrayCreatername = cltgrayCreatername;
	}
}