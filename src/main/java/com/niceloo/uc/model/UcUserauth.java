package com.niceloo.uc.model;

import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * @<PERSON><PERSON> hepangui
 * @Date 2020/9/15 0015
 */
@POJO
@ClassDesc
public class UcUserauth {
	@POJO(id = true, generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36, comment = "用户品牌关系id")
	private String userauthId;
	@POJO
	@FieldDesc(length = 36, comment = "用户id")
	private String userId;
	@POJO
	@FieldDesc(length = 36, comment = "认证类型")
	private String userauthType;


	public String getUserauthId() {
		return userauthId;
	}

	public void setUserauthId(String userauthId) {
		this.userauthId = userauthId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserauthType() {
		return userauthType;
	}

	public void setUserauthType(String userauthType) {
		this.userauthType = userauthType;
	}
}
