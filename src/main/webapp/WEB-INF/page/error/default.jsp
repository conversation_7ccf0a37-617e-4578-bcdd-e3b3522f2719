<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<%@ page import="java.io.PrintWriter"%>
<%!
private String n2e(String a){
	if(a==null){
		a="";
	}
	return a;
}
%>
<html>
<body style="background:#417DF2;color:white;font-size:16px;">
<h4>
抱歉，您访问的信息出错了
</h4>
<h1 style="text-align:center">
<%=n2e(exception.getMessage()).replaceAll("<", "[")%>
</h1>
<table>
<%
	for(StackTraceElement st:exception.getStackTrace()){
		String left	=st.getMethodName();
		String right=st.isNativeMethod()?"本地方法":
			((st.getFileName()!=null&&st.getLineNumber()>=0)?
					(st.getFileName()+":"+st.getLineNumber()+"行"):
					("未知")
			);
		if((st.getFileName()!=null&&st.getFileName().indexOf("FrameworkServlet.java")>=0)||(st.getFileName()!=null&&st.getFileName().indexOf("DispatcherServlet.java")>=0)){
			break;
		}
		
%>
	<tr><td><%=n2e(left).replaceAll("<", "[") %></td><td><%=n2e(right).replaceAll("<", "[")%></td></tr>
<%		
	}
%>
</table>
</body>
</html>