(function(){
	API.define("PAGE.Test",[
		"PAGE.BasePage"
	],function(BasePage,ME){

		return {
			extend:BasePage,
			constructor:function(cfg){
				this.callSuper([cfg]);
			},

			init:function(){
				this.build();
			},

			build:function(){

				//1.布局
				this.pMain=new EG.ui.Panel({layout:"border",style:"margin:15px;",
					items:[

						this.pTop   =new EG.ui.Panel({region:"top",height:40,layout:{type:"line",direct:"H"},style:"padding:3px"}),

						this.tree   =new EG.ui.Tree({width:200,region:"left",rootTitle:"模块",style:"overflow:auto",onclick:this.loadApis,onclickSrc:this}),

						this.pCenter=new EG.ui.Panel({region:"center",style:"overflow:auto"}),

						this.pRight=new EG.ui.Panel({region:"right",style:"overflow:auto"}),

						//
						{xtype:"xPanel",region:"bottom",title:"控制台",collapseAble:true,items:[
							this.tpConsole=new EG.ui.TabPanel({
								region:"center",
								direct:"bottom",
								items:[
									{tab:{title:"请求/响应"},panel:{
										layout:{type:"line",direct:"H"},
										items:[
											this.taReq		=new EG.ui.JSONView({style:"border:1px solid black;margin:2px;overflow:auto"}),
											this.taRes		=new EG.ui.JSONView({style:"border:1px solid black;margin:2px;overflow:auto"}),
											{xtype:"panel",layout:{type:"line",direct:"V"},items:[
												this.taReqReal	=new EG.ui.Textarea(),
												this.taResReal	=new EG.ui.Textarea()
											]}
										]
									}}
								]
							})
						]}
					]
				});

				//this.tpConsole.select(1);

				//2.菜单
				this.buildLogin();
				this.loadModus();

				//
				this.pRoot.addItem(this.pMain);
				this.pRoot.render();
			},


			/**
			 * 创建登录
			 */
			buildLogin:function(){
				var me=this;

				//alert(window.MTRADE_CONFIG.servers)
				
				var href=window.location.href;
				var i=window.location.href.indexOf("/api_doc");
				var contextPath=href.substring(href.lastIndexOf("/",i-1)+1,i);
				


				var servers=[];
				this.pTop.addItem([

					//
					this.fiHost=new EG.ui.FormItem({type:"text",width:300,title:"服务器",defValue:window.location.protocol+"//"+window.location.host+"/"+contextPath}),

					//
					this.fLogin=new EG.ui.Form({
						width:600,
						layout:{type:"line",direct:"H"},
						items:[
							{type:"text",name:"userLoginname"	,title:"用户名"			,width:200},
							{type:"text",name:"userPassword"	,title:"密码"			,width:200},
							{type:"text",name:"captcha"			,title:"图形验证码"		,typeCfg:{
								onkeyup:function(e){
									e=EG.Event.getEvent(e);
									if(e.keyCode==13){
										me.btnLogin.click();
									}
								}
							},width:200,after:EG.CE({tn:"img",src:"",style:"width:80px;height:30px",onclick:function(){
								var me_img=this;
								me.callAPI("core/captcha/token",{},function(data){
									me.captchaToken=data["captchaToken"];
									me_img.src=me.getServerUrl()+"/captcha?captchaToken="+me.captchaToken+"&random="+Math.random()*1000;
								});
							}})}
						]
					}),

					//
					this.btnLogin=new EG.ui.Button({text:"登录",style:"margin:3px",click:function(){
						var oParams				= me.fLogin.getData();
						var params	    		=EG.clone(oParams);
						params["captchaToken"]	=me.captchaToken;

						me.call({api:"core/login",params:params,callback:function(data){
							if(data["result"]=="000000"){
								EG.Tools.save("data_login"		,oParams);
								EG.Tools.save("data_loginRtn"	,data["data"]);
							}
						}});
					}}),
					new EG.ui.Button({text:"注销",style:"margin:3px",click:function(){
						me.call({api:"core/logout",params:{},callback:function(data){}});
					}})
				]);

				//
				var data_login=EG.Tools.query("data_login");
				if(data_login!=null){
					this.fLogin.setData(data_login);
				}

				var data_serverUrl=EG.Tools.query("data_serverUrl");
				if(data_serverUrl!=null){
					this.fiHost.setValue(data_serverUrl);
				}

				this.pTop.render();
			},

			/**
			 *
			 */
			getServerUrl:function(){
				return this.fiHost.getValue();
			},

			/**
			 * 清空日志
			 */
			clearLog:function(){
				this.taReqReal	.setValue("");
				this.taReq		.setValue(null);
				this.taResReal	.setValue("");
				this.taRes		.setValue(null);
			},

			/**
			 * 对象序列化
			 */
			serialize : function(d){
				var data=JSON.stringify(d);
				return encodeURIComponent(data).replace(/\+/gm, "%2B");
			},

			/**
			 * 加载模块
			 */
			loadModus:function(){
				var me=this;
				EG.Locker.wait("加模块");
				this.request({
					url:"apidoc/modus",
					content:EG.toJSON({}),
					success:function(data,rtn){
						for(var i=0;i<data.length;i++){
							var d=data[i];
							EG.Locker.lock(false);
							me.tree.add({
								title       :d["comment"],
								value       :d["name"],
								onclick     :me.loadApis,
								onclickSrc  :me
							});
						}
						me.buildModuExt();
					}
				});
			},

			buildModuExt:function(){
				var me=this;
				var modu_exts=[
					{comment:"上传",url:"PAGE.modu.Upload"}
				];

				for(var i=0;i<modu_exts.length;i++){
					var modu=modu_exts[i];
					this.tree.add({
						title       :modu["comment"],
						value       :modu["url"],
						onclick     :function(){
							me.modu_require(this);
						}
					});
				}
			},

			/**
			 * 加载APIS
			 */
			loadApis:function(){
				//
				this.pCenter.clear();

				var me=this;
				var node=this.tree.getSelected();
				if(!node||!node.value) return;
				this.request({
					url:"apidoc/apis?moduName="+node.value,
					content:"",
					success:function(data,rtn){
						for(var i=0;i<data.length;i++){
							me.buildAPI(data[i]);
						}
					}
				});
			},

			/**
			 * 创建API
			 */
			buildAPI:function(data){
				var me=this;
				var bg="";
				if(data["status"]=="W"){
					bg="background-color:#AAAAAA;";
				}else if(data["status"]=="S"){
					bg="background-color:green;";
				}
				this.pCenter.addItem({
					xtype		:"button",
					text		:data["api"]+"<br/>"+data["comment"],
					titleStyle	:"height:auto;"+bg,
					textStyle	:"text-align:center",
					style		:"margin:10px;",
					api			:data["api"],
					api_data    :data,
					click		:data["click"]||function(){
						if(this.api_data["params"].length==0){
							me.prepareFor=this.api_data["api"];
							me.callAPI();
							me.showAPIDoc(this.api_data);
						}else{
							me.openAPI(this.api_data);
							me.showAPIDoc(this.api_data);
						}
					}
				});
			},

			/**
			 * 显示API文档
			 */
			showAPIDoc:function(apiInfo){

				EG.DOM.removeChilds(this.pRight.getElement());

				var tbody=null;
				var sb="border-top:1px solid black;border-left:1px solid black;padding:5px 10px;";
				var tb=EG.CE({tn:"fieldset",style:"background-color:#DDDDDD",cn:[
					{tn:"legend",innerHTML:"输入",style:"margin-left:30px"},
					{tn:"table",cn:[
						tbody=EG.CE({tn:"tbody",cn:[
							{tn:"tr",cn:[
								{tn:"td",style:sb,innerHTML:"<b>标识</b>"},
								{tn:"td",style:sb,innerHTML:"<b>名称</b>"},
								{tn:"td",style:sb,innerHTML:"<b>类型</b>"},
								{tn:"td",style:sb,innerHTML:"<b>备注</b>"}
							]}
						]})
					]}
				]});
				for (i = 0; i < apiInfo.params.length; i++) {
					param		=apiInfo.params[i];
					var memo	=EG.String.n2e(param["memo"]);
					var validate=param["validate"];
					var defVal	=param["defVal"];
					var unnull	=param["unnull"];
					if(unnull){
						memo+="<br><span style='color:red'>[必填]</span>";
					}
					if(defVal){
						memo+="<br>[默认]"+defVal;
					}
					if(validate){
						memo+="<br>[校验]"+
							EG.String.removeStart(
								EG.String.removeStart(validate,"M:org.nobject.common.code.describer.Validate#"),
								"R:org.nobject.common.code.describer.Validate#"
							)
						;
					}

					if(EG.String.startWith(memo,"<br>")){
						memo=EG.String.removeStart(memo,"<br>");
					}

					EG.CE({pn:tbody,tn:"tr",cn:[
						{tn:"td",style:sb,innerHTML:param["name"]},
						{tn:"td",style:sb,innerHTML:param["comment"]},
						{tn:"td",style:sb,innerHTML:param["type"]},
						{tn:"td",style:sb,innerHTML:"<div style='max-width:300px;word-wrap:break-word'>"+memo+"</div>"}
					]});
				}
				this.pRight.getElement().appendChild(tb);

				tb=EG.CE({tn:"fieldset",style:"margin-top:20px",cn:[
					{tn:"legend",innerHTML:"输出",style:"margin-left:30px;"},
					{tn:"table",cn:[
						tbody=EG.CE({tn:"tbody",cn:[
							{tn:"tr",cn:[
								{tn:"td",style:sb,innerHTML:"<b>标识</b>"},
								{tn:"td",style:sb,innerHTML:"<b>名称</b>"},
								{tn:"td",style:sb,innerHTML:"<b>类型</b>"},
								{tn:"td",style:sb,innerHTML:"<b>备注</b>"}
							]}
						]})
					]}
				]});
				for (var i = 0; i < apiInfo.returns.length; i++) {
					var param=apiInfo.returns[i];
					EG.CE({pn:tbody,tn:"tr",cn:[
						{tn:"td",style:sb,innerHTML:param["name"]},
						{tn:"td",style:sb,innerHTML:param["comment"]},
						{tn:"td",style:sb,innerHTML:param["type"]},
						{tn:"td",style:sb,innerHTML:param["memo"]}
					]});
				}
				this.pRight.getElement().appendChild(tb);
			},

			/**
			 * 打开API测试
			 */
			openAPI:function(apiInfo){
				var me=this;
				//
				if (!this.dlgs) {
					this.dlgs = {};
				}
				var api         = apiInfo["api"];
				var isNew       = false;
				var dlg         = this.dlgs[api];
				this.prepareFor =api;
				var size		=EG.getSize(EG.getBody());
				if (!dlg) {
					
					var fPrepare = null;
					var jsonText = null;
					dlg = new EG.ui.Dialog({
						closeable: true,lock: true,title: "状态",width: size.innerWidth*0.6,height: size.innerHeight*0.6,renderTo : me.pRoot.getElement(),layout: {type: "border"},bodyStyle: "overflow:auto",
						items: [
							fPrepare = new EG.ui.Form({region: "center",height: 100,layout: "default",labelWidth: 100,style: "margin:2px"}),
							{region: "right",title: "粘贴",collapseAble: true,collapsed: true,xtype: "xPanel",items: [
								jsonText = new EG.ui.Textarea({width: "100%", height: 200, style: "margin:2px"})
							]}
						],
						btns: [
							new EG.ui.Button({
								text: "请求", cls: "eg_button_small", click: function () {
									me.callAPI(me.prepareFor,me.fPrepare.getData());
									me.dlgs[api].close();
								}
							})
						]
					});
					this.dlgs[api] = dlg;

					dlg.fPrepare = fPrepare;
					isNew = true;

					EG.Event.bindEvent(jsonText.input, "onkeyup", function () {
						if (me.w4parse) {
							clearTimeout(me.w4parse);
						}
						me.w4parse = setTimeout(function () {
							v = EG.DOM.getValue(jsonText.input);
							EG.Style.css(jsonText.input, "color:;")
							try {
								var obj = EG.Object.$eval(v);
								for (var k in obj) {
									var v = obj[k];
									if (v != null && EG.Object.isLit(v)) {
										obj[k] = EG.toJSON(v);
									}
								}
								me.fPrepare.setData(obj);
							} catch (e) {
								EG.Style.css(jsonText.input, "color:red;")
							}
						}, 500);
					});

				}

				//
				dlg.setTitle(api);

				var items=[];
				for (var i = 0; i < apiInfo.params.length; i++) {
					var param=apiInfo.params[i];
					if(param["name"].indexOf(".")>=0||param["name"].indexOf("[")>=0){
						continue;
					}
					var item={
						xtype       :"formItem",
						name        :param["name"],
						title       :param["comment"]+"["+param["name"]+"]",
						unnull      :param["unnull"],
						labelWidth  :300,
						type        :"text"
					};

					var memos		=(param["memo"]||" ").split("\n");
					var memo_captcha=null;
					var c=0;
					if(!memo_captcha){
						for(var j=0;j<memos.length;j++){
							if((c=memos[j].indexOf("[图形验证码]"))>=0){
								memo_captcha=memos[j].substring(c+"[图形验证码]".length);
								break;
							}
						}
					}

					
					if(memo_captcha){
						var key=memo_captcha;

						//刷新图形验证码
						var getCaptcha_value=function(){
							var it_captchaToken=me.fPrepare.getFormItem("captchaToken");
							if(it_captchaToken!=null&&it_captchaToken.getValue()){
								return it_captchaToken.getValue();
							}
							return me.fPrepare.getFormItem(key).getValue();
						};
						
						item.after=EG.CE({tn:"div",cn:[
	                      	this.dCAPTCHA	=EG.CE({tn:"div",style:"width:400px;height:25px;"+EG.Style.c.dv}),
	                    	this.sltCapture =new EG.ui.Select({type:"select",width:100,height:25,style:EG.Style.c.dv,
			                    onchangeOnbuild:true,
	                    		textvalues:[["验证码","RANDOMCODE"]],
	                    		useEmpty:false,
	                    		onchange:function(value){
	                    			EG.DOM.removeChilds(me.dCAPTCHA);
	                    			if(value=="RANDOMCODE"){
	                    				EG.CE({pn:me.dCAPTCHA,tn:"img",src:"",style:"width:80px;height:30px",onclick:function(){
	                    					var captcha_value		=getCaptcha_value();
	                    					this.src=me.getServerUrl()+"/captcha?value="+captcha_value+"&random="+Math.random()*1000;
	                    				}});
	                    			}
	                    		}
	                    	})
	                    ]});
					}else{
						item.after=EG.CE({tn:"div",click:"javascript:void(0)",title:param["memo"],style:"cursor:pointer;width:"+size.innerWidth*0.2+"px;height:30px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;line-height:30px",innerHTML:param["memo"]});
					}

					//图形验证码凭证
					if(param["name"]=="captchaToken"){
						item.after=EG.CE({tn:"div",innerHTML:"获取",item:this,onclick:function(){
							var fi_captchaToken=this.parentNode.item;
							me.call({api:"core/captcha/token",params:{},onsuccess:function(data){
								fi_captchaToken.setValue(data["captchaToken"]);
							}});
						}});
					}

					items.push(item);
				}

				if (isNew) {
					dlg.fPrepare.addItem(items);
				}

				dlg.open();
				this.fPrepare   =dlg.fPrepare;
			},

			/**
			 * 请求
			 * @param cfg
			 */
			call:function(cfg){

				var me=this;
				var api			=cfg["api"],
					params		=cfg["params"],
					callback	=cfg["callback"],
					onsuccess	=cfg["onsuccess"],
					onfaild		=cfg["onfaild"];

				var oApi		=cfg["api"];
				var req			={};
				var apiKey		="api",
					paramsKey	="params";
				var serverUrl	=this.getServerUrl();

				//
				this.clearLog();

				req[apiKey]		    =api;
				req[paramsKey]	    =params;

				var content     ="params="+me.serialize(params);
				var useJSONP    =false;
				var i           =serverUrl.lastIndexOf("/");
				var j           =serverUrl.indexOf("://");
				var hostDomain  =serverUrl.substring(j+3,i);
				var xhrFields   ={
					withCredentials:true
				};
				if(hostDomain!=window.location.host){
					useJSONP=true;

					if(!this.jsonp_prepareds){
						this.jsonp_prepareds={};
					}

					if(this.jsonp_prepareds[hostDomain]){
						useJSONP=false;
					}else{
						//this.jsonp_prepareds[hostDomain]=true;
					}

				}

				serverUrl+="/api/"+api;

				EG.Locker.lock();
				EG.Ajax.send({
					method		:"post",
					url			:serverUrl,
					//url_jsonp	:useJSONP?serverUrl+"/jsonp?callback=":"",
					content		:content,
					//xhrFields	:xhrFields,
					callback	:function(rtn){

						//
						if(useJSONP){
							me.jsonp_prepareds[hostDomain]=true;
						}

						EG.Locker.lock(false);
						//
						me.taResReal.setValue(rtn);

						rtn=EG.Object.$eval(rtn);

						me.taRes.setValue(rtn);

						if(callback){
							callback.apply(me,[rtn]);
						}

						//
						if(rtn["code"]=="0000"){
							onsuccess   &&onsuccess .apply(me,[rtn["data"],rtn]);
						}else{
							onfaild     &&onfaild   .apply(me,[rtn["data"],rtn]);
						}

					},
					erhandler:function(status, req){
						EG.Locker.lock(false);
						me.taResReal.setValue("网络请求失败:"+status);
					}
				});

				//请求
				me.taReqReal.setValue(serverUrl+"?"+content);
				me.taReq	.setValue(params);
			},

			callAPI:function(api,params,onsuccess){
				if(api==null){
					api=this.prepareFor;
				}

				this.call({
					api			:api,
					params		:params||{},
					onsuccess	:onsuccess
				})
			},

			/**
			 * 加载文件
			 */
			modu_require:function(node){
				var me=this;

				var url=node.getValue();
				API.loader.require([url],function(clazz){
					me.pCenter.clear();
					new clazz({
						page:me
					});
				},function(e){
					EG.Locker.message("加载失败["+url+"]"+e);
				});
			}
		};
	});
})();