(function(){
	API.define("PAGE.modu.Upload",[
		"PAGE.Modu"
	],function(Modu,ME){
		return {
			config:{

			},
			extend:Modu,
			constructor:function(cfg){
				this.callSuper([cfg]);
			},

			/**
			 * 创建主体
			 */
			build:function(){
				var me=this;
				var apis=[
					{api:"upload",comment:"上传",click:function(){me.showUpload()}}
				];

				for(var i=0;i<apis.length;i++){
					this.page.buildAPI(apis[i]);
				}
			},

			showUpload:function(){
				var me=this;
				if(!this.dlg){
					var size		=EG.getSize(EG.getBody());
					this.dlg = new EG.ui.Dialog({
						closeable: true,lock: true,title: "上传",width: size.innerWidth*0.6,height: size.innerHeight*0.6,renderTo : me.page.pRoot.getElement(),bodyStyle: "overflow:auto",
						items: [
							{xtype:"upload",autoupload	:true,
								filename	:"file",
								callback	:true,
								action		:API.basePath+"/upload?1=1",
								onUploaded	:function(data){
									alert(EG.toJSON(data));
								}
							}
						]
					});
				}
				this.dlg.open();
			}

		};
	});
})();