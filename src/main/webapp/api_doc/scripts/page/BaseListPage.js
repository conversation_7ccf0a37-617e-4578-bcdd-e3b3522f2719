(function(){
	API.define("PAGE.BaseListPage",[
		"PAGE.BasePage"
	],function(BasePage,ME){
		return {
			extend:BasePage,

			config:{

				/**
				 * 页面类型
				 */
				page_type   :"list",

				/**
				 * 请求
				 */
				action      :"",

				/**
				 * 表单配置
				 */
				formConfig	:null,
				/**
				 * 表格配置
				 */
				gridConfig	:null,
				/**
				 * 表配置
				 */
				treeConfig	:null,
				/**
				 * 查询配置
				 */
				queryConfig	:null,
				/**
				 * 按钮配置
				 */
				btnsConfig	:null,
			},

			constructor:function(cfg){

				this.pRoot=cfg["pRoot"];

				this.callSuper([cfg]);
			},

			/**
			 *
			 */
			init_list:function(){
				var me=this;
				this.build();
				this.buildForm();
				this.buildBtns();
				this.buildGrid();

				if(this.treeConfig){
					this.buildTree();
					this.loadTreeData(function(){
						me.loadData();
					});
				}else{
					this.loadData();
				}
			},

			/**
			 * 创建主体
			 */
			build:function(){
				var me=this;

				this.pRoot.setLayout("border");

				this.pRoot.addItem([
					this.pTop   =new EG.ui.Panel({region:"top"      ,layout:"border",height:"auto"}),
					this.pCenter=new EG.ui.Panel({region:"center"   ,layout:"border"})
				]);
			},

			/**
			 * 创建表单
			 */
			buildForm:function(){
				var me=this;

				//创建Form
				EG.copy(this.formConfig,{
					width	:"100%",
					height	:"auto",
					style	:"margin:0px 0px",
					region  :"top",
					layout  :{type:"line",direct:"H"}
				},false);

				var search_text=function(e){
					var e=EG.Event.getEvent(e);
					if(e.keyCode==13){
						me.loadData();
					}
				};

				var search_select=function(e){
					me.loadData();
				};

				//查询项过滤
				var fn_filter=function(container){
					var items=container.items;
					for(var i=0;i<items.length;i++){
						var item=items[i];

						//高度
						if(item.height==null) item.height=37;

						//
						if(!item.xtype||item.xtype=="formItem"){
							if(item.labelWidth===null) item.labelWidth	=110;

							if(!item.typeCfg){
								item.typeCfg={};
							}
							var typeCfg=item.typeCfg;

							if(item.searchOnReady){
								typeCfg.page=me;
								if(item.type=="text"||item.type=="date"){
									typeCfg.onkeyup=search_text;
								}else if(item.type=="select"||item.type=="selectExpand"||item.type=="boxGroup"){
									typeCfg.onchange=search_select;
								}
							}
						}else{
							if(item.items){
								fn_filter(item);
							}
						}
					}
				}
				fn_filter(this.formConfig);

				this.form=new EG.ui.Form(this.formConfig);
				this.pTop.addItem(this.form);
			},

			/**
			 * 创建表格
			 * @param gridConfig
			 */
			buildGrid:function(){
				var me=this;
				EG.copy(this.gridConfig,{
					pageSize	:15,
					boxAble		:true,
					colOrderAble:true,
					onOrder		:this.loadData,
					onOrderSrc	:this,
					seqTitle	:"序号",
					seqWidth	:32,
					region      :"center",
					rowEvents   :{
						onclick:function(){
							var dl=me.grid.getSelectData().length;
							me.openView();

						}
					},
					remotingCallback:function(){
						me.loadData();
					},
				},false);

				this.grid=new EG.ui.Grid(this.gridConfig);
				this.pCenter.addItem(this.grid);
			},

			/**
			 * 创建树
			 */
			buildTree:function(){
				EG.copy(this.treeConfig,{
					width:200,
					region:"left",
					style:"overflow:auto",
					onclick:this.loadData,
					onclickSrc:this
				});

				this.tree=new EG.ui.Tree(this.treeConfig);
				this.pCenter.addItem(this.tree);
			},

			/**
			 * 创建按钮
			 */
			buildBtns:function(){
				var me=this;

				var pBtns=new EG.ui.Panel({region:"bottom",height:"auto",style:""});
				this.pTop.addItem(pBtns);

				for(var i=0;i<this.btnsConfig.length;i++){
					var btn=this.btnsConfig[i];

					(function(type){
						var btnName,btnClick,btnStyle,btnClickSrc=me,btnMenu;

						if(btnStyle==null){
							btnStyle="";
						}
						btnStyle+=";margin:5px;"

						if(typeof(type)=="string"){
							if(type=="add")				{btnName="添加";btnClick=me.openAdd;}
							else if(type=="edit")		{btnName="修改";btnClick=me.openEdit;}
							else if(type=="view")		{btnName="查看";btnClick=me.openView;}
							else if(type=="delete")		{btnName="删除";btnClick=me.doDelete;}
							else if(type=="exp")		{btnName="导出";btnClick=me.doExp;}
						}else{
							btnName 	=type["name"];
							btnClick    =type["click"];
							btnStyle    =type["style"];
							btnClickSrc =type["btnClickSrc"]||me;
							btnMenu     =type["menu"];
						}

						//权限判断按钮
						//if(me.page_btns!=null&&!EG.Array.has(me.page_btns,btnName)){
						//	return;
						//}

						pBtns.addItem(new EG.ui.Button({
							text    :btnName,
							click   :btnClick,
							clickSrc:btnClickSrc,
							style   :btnStyle,
							menu    :btnMenu
						}));

						//EG.Style.c.dv
					})(btn);
				}
			},

			/**
			 * 加载模块
			 * @param callback
			 */
			loadTreeData:function(callback){
				var me=this;
				EG.Locker.wait("加载树");
				this.request({
					url:this.treeConfig.load_url,
					content:EG.toJSON({}),
					success:function(data){
						for(var i=0;i<data.length;i++){
							var d=data[i];

							//
							var title_key=me.treeConfig.node_title_key;
							var value_key=me.treeConfig.node_value_key;

							me.tree.add({
								title       :(typeof(title_key)=="function")?title_key.apply(me,[d]):d[title_key],
								value       :(typeof(value_key)=="function")?value_key.apply(me,[d]):d[value_key],
								onclick     :me.loadData,
								onclickSrc  :me,
							});
						}
						if(callback){
							callback.apply(me);
						}
					}
				});
			},

			/**
			 * 加载API
			 */
			loadData:function(){
				var me=this;
				var params=this.getLoadDataParam();

				EG.Locker.wait("加载数据");
				this.request({
					url     :this.action+"/finds",
					content :EG.toJSON(params),
					success :function(data){
						EG.Locker.lock(false);
						me.grid.setData       (data["datas"]);
						me.grid.setDataSize   (data["count"]);
					}
				});
			},

			/**
			 * 获取加载数据参数
			 */
			getLoadDataParam:function(){
				//查询数据
				var fd=this.form.getData();
				var searchMap={};
				for(var k in fd){
					if(fd[k]!==""&&searchMap[k]!==null){
						searchMap[k]=fd[k];
					}
				}

				//排序结果
				var orderMap={};
				if(this.grid.curOrderName){
					orderMap[this.grid.curOrderName]=this.grid.curOrderDesc;
				}

				if(this.tree){
					var node=this.tree.getSelected();
					if(node&&node.value!=null){
						searchMap[this.treeConfig.node_value_key]=node.value;
					}
				}

				return {
					searchMap	:searchMap,
					groupMap	:null,
					orderMap	:orderMap,
					startIndex	:this.grid._currentPage*this.grid.pageSize,
					pageIndex	:(this.grid._currentPage+1),
					pageSize	:this.grid.pageSize

				};
			},

			openAdd:function(){
				this.open({
					type        :"PAGE."+this.modu+"."+this.modelName,
					page_type   :"add",
					parent      :this,
					params      :{}
				});
			},

			openEdit:function(){

				var ds=this.grid.getSelectData();
				if(ds==null||ds.length==0) return;

				if(ds.length>1){
					EG.Locker.message({
						message:"请选择单条数据操作"
					})
					return;
				}

				this.open({
					type        :"PAGE."+this.modu+"."+this.modelName,
					page_type   :"edit",
					parent      :this,
					params      :ds[0]
				});
			},

			openView:function(){

				var ds=this.grid.getSelectData();
				if(ds==null||ds.length==0) return;

				if(ds.length>1){
					EG.Locker.message({
						message:"请选择单条数据操作"
					})
					return;
				}

				this.open({
					type        :"PAGE."+this.modu+"."+this.modelName,
					page_type   :"view",
					parent      :this,
					params      :ds[0]
				});
			},

			doDelete:function(){
				var me=this;

				//
				var pp=this.getSelectIds();
				if(!pp||pp.length==0){
					return;
				}

				EG.Locker.confirm("确定删除吗?",function(){
					EG.Locker.wait("正在提交");

					me.request({
						url     :me.action+"/delete",
						content :EG.toJSON({
							ids:pp
						}),
						success :function(data){
							EG.Locker.lock(false);
							me.loadData();
						}
					});
				});

			},

			getSelectIds:function(){
				var ds=this.grid.getSelectData();
				if(ds==null||ds.length==0) return;

				var pp=[];
				for(var i=0;i<ds.length;i++){
					var d={};
					for(var j=0;j<this.keyId.length;j++){
						var kId=this.keyId[j]["id"];
						d[kId]=ds[i][kId];
					}
					pp.push(d);
				}
				return pp;
			},

			doExp:function(){

			},

			reload:function(){
				this.loadData();
			},

			getText:function(textvalues,value){
				for(var i=0;i<textvalues.length;i++){
					if(value===null) value="";
					if(textvalues[i][1]===value){
						return textvalues[i].length>2?("<span style='color:"+textvalues[i][2]+"'>"+textvalues[i][0]+"</span>"):textvalues[i][0];
					}
				}
				return null;
			}
		};
	});
})();