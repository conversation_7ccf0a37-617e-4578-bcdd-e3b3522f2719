(function(){
    API.define("PAGE.Console",[
        "PAGE.BasePage"
    ],function(BasePage,ME){
        return {
            extend:BasePage,

            constructor:function(){
                var me=this;
                this.build();
            },

            build:function(){
                var me=this;

                this.dTip=EG.CE({pn:EG.getBody(),tn:"div",innerHTML:"",cls:"animated pulse infinite",style:"max-width:800px;overflow:hidden;position:absolute;left:40%;top:5px;z-index:9;color:yellow",onclick:function(){

                }});

                EG.CE({pn:EG.getBody(),tn:"div",innerHTML:"APIDOC使用说明",cls:"animated pulse infinite",style:"position:absolute;right:10px;top:5px;z-index:9;color:yellow",onclick:function(){
                    window.open(API.basePath+"/etc/manual.docx");
                }});

                this.tp=new EG.ui.TabPanel({renderTo:EG.getBody(),style:"margin:10px;background-color:transparent"});

                var menus=[
                    {title:"测试"     ,url:"PAGE.Test"},
                    {title:"分析"     ,url:"PAGE.Running"}
                ];

                var tp_idx=0;
                var lastRequire=EG.Tools.query(this.getClass()._className+"#lastRequire");
                if(lastRequire!=null){
                    for(var i=0;i<menus.length;i++){
                        if(lastRequire==menus[i].url){
                            tp_idx=i;
                            break;
                        }
                    }
                }

                for(var i=0;i<menus.length;i++){
                    var menu=menus[i];
                    this.tp.addItem({
                        tab     :{title:menu["title"],url:menu["url"],afterselect:this.fn_require,parentPage:this},
                        panel   :{style:"background-color:transparent"}
                    },false,null);
                }

                this.tp.select(tp_idx);
                this.tp.render();

                var size=EG.getSize(EG.getBody());

                this.dlgOpen=new EG.ui.Dialog({
                    closeable	:true,
                    lock		:true,
                    title		:"",
                    width		:size.innerWidth*0.9,
                    height		:size.innerHeight*0.9,
                    renderTo	:EG.getBody(),
                    items		:[
                        this.pOpen=new EG.ui.Panel({style:"position:relative"})
                    ],
                    btns	:[

                    ]
                });
            }
        };
    });
})();