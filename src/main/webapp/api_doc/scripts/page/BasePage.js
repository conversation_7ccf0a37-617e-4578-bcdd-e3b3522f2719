(function(){
    API.define("PAGE.BasePage",[],function(ME){

        return {

            config:{
                /** 页面类型 */
                page_type   :"",
                /** 父窗口 */
                parent      :null
            },


            constructor:function(cfg){

                //
                this.initConfig(cfg);

                //主键转换
                var ks=this.keyId;
                if(!EG.Array.isArray(ks)){
                    ks=[ks];
                }

                this.keyId=[];
                for(var i=0;i<ks.length;i++){
                    var k=ks[i];
                    if(typeof(k)=="string"){
                        k={
                            id		:k,
                            name	:k
                        }
                    }
                    this.keyId.push(k);
                }

                //主键赋值
                if(this.params){
                    this._initKeys(this.params);
                }

                //根面板
                this.pRoot=cfg.pRoot;

                //
                this["init"+(this.page_type?("_"+this.page_type):"")]();
            },

            _initKeys:function(params){
                for(var i=0;i<this.keyId.length;i++){
                    this.keyId[i]["value"]=params[this.keyId[i]["id"]];
                }
            },

            requestApi:function(cfg){
                var params=cfg["params"]||{};
                cfg["content"]="params="+this.serialize(params);
                this.request(cfg);
            },

            /**
             * 对象序列化
             */
            serialize : function(d){
                var data=JSON.stringify(d);
                return encodeURIComponent(data).replace(/\+/gm, "%2B");
            },

            /**
             *
             * @param cfg
             */
            request:function(cfg){
                EG.Ajax.send({
                    method		:"post",
                    url			:API.basePath+"/"+cfg["url"],
                    content		:cfg["content"],
                    //contentType	:"",
                    callback	:function(data){
                        data=EG.Object.$eval(data);
                        var d=data["data"];
                        if(data["code"]=="0000"){
                            (cfg["success"]||function(){

                            })(d,data);
                        }else{
                            (cfg["faild"]||function(d){
                                EG.Locker.message({message:EG.Tools.toJSON(d),closeable:true})
                            })(d,data);
                        }
                    },
                    erhandler:function(status, req){
                        EG.Locker.message({message:"网络请求失败:"+status,closeable:true})
                    }
                });
            },

            open:function(cfg){
                var me=this;
                API.loader.require([cfg["type"]],function(clazz){
                    window.$console.pOpen.clear();

                    EG.copy(cfg,{
                        pRoot:window.$console.pOpen
                    })

                    window.$console.pOpen.page=new clazz(cfg);
                    window.$console.dlgOpen.open();
                });
            },

            close:function(){
                if(this==window.$console.pOpen.page){
                    window.$console.dlgOpen.close();
                }
            },

            /**
             * 加载文件
             */
            fn_require:function(){

                var tab =this;
                var p   =tab.getPanel();
                if(p.page) return;

                var url=this.url;

                API.loader.require([url],function(clazz){
                    p.clear();
                    p.page=new clazz({
                        pRoot:p
                    });
                    EG.Tools.save(tab.parentPage.getClass()._className+"#lastRequire",url);
                },function(e){
                    EG.Locker.message("加载失败["+url+"]"+e);
                });
            }

        };
    });
})();