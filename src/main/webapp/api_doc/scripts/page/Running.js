(function(){
	API.define("PAGE.Running",[
		"PAGE.BasePage"
	],function(BasePage,ME){

		return {
			extend:BasePage,
			constructor:function(cfg){
				this.callSuper([cfg]);
			},

			init:function(){
				this.build();
			},

			build:function(){
				var me=this;

				//1.布局
				this.pMain=new EG.ui.Panel({
					renderTo:EG.getBody(),
					layout:"border",
					items:[
						new EG.ui.Panel({
							region:"center",
							layout:{type:"line",direct:"V"},
							style:"margin:20px",
							items:[
								this.form=new EG.ui.Form({
									height:"auto",
									style:"background-color:white",
									items:[
										{pos:[0,0],type:"text"	,title:"关键字"		,name:"keyword",after:"协议号/流水号/账号/手机号/其它",typeCfg:{
											onkeyup:function(e){
												var e=EG.Event.getEvent(e);
												if(e.keyCode==13){
													me.doGrepStart();
												}
											}
										}},
										{pos:[0,1],type:"text"	,title:"行数"		,name:"rowsize",defValue:"5",typeCfg:{
											onkeyup:function(e){
												var e=EG.Event.getEvent(e);
												if(e.keyCode==13){
													me.doGrepStart();
												}
											}
										}},
										{pos:[1,0],type:"text"	  ,title:"日志文件"	,name:"logName",defValue:"",after:"<span style='white-space: nowrap'>midservice:当天;catalina.out:全量</span>"},
										{pos:[2,0],type:"boxGroup",title:"自动分析"	,name:"autoAnalysis",typeCfg:{textvalues:[["自动","A"],["手动","M"]],useEmpty:false},defValue:"A"},
										{pos:[2,1],type:"boxGroup",title:"自动深度"	,name:"deepAnalysis",typeCfg:{textvalues:[["自动","A"],["手动","M"]],useEmpty:false},defValue:"A"},
									]
								}),
								this.pBtns=new EG.ui.Panel({
									height:38,
									layout:"border",
									style:"margin:5px",
									items:[
										{xtype:"panel",region:"left",width:200,items:[
											{xtype:"button",text:"查询"			,click:this.doGrepStart			,clickSrc:this},
											{xtype:"button",text:"分析"			,click:this.doAnalysis		,clickSrc:this},
											{xtype:"button",text:"JSON格式化"	,click:this.openJSONFormat	,clickSrc:this},
										]},
										this.pMidBtns=new EG.ui.Panel({xtype:"panel",region:"center",layout:{type:"line",direct:"H"},items:[
											this.labPast=new EG.ui.Label({width:300,text:""}),
											{xtype:"button" ,text:"停止",click:function(){
												if(!me.grep_id){
													EG.Locker.message("当前无运行的查询");
													return;
												}
												me.requestApi({
													url:"api/core/log/grep/stop",
													params:{
														grep_id :me.grep_id
													},
													success:function(data) {
														//
													}
												});
											}},
											{xtype:"button",text:"跳过",click:function(){
												if(!me.grep_id){
													EG.Locker.message("当前无运行的查询");
													return;
												}
												me.requestApi({
													url:"api/core/log/grep/skip",
													params:{
														grep_id :me.grep_id,
														size    :me.t_skip.getValue(),
													},
													success:function(data) {
														//
													}
												});
											}},
											this.t_skip=new EG.ui.Text({width:100,height:20})
										]})
									]
								}),

								this.tp=new EG.ui.TabPanel({
									items:[
										{tab:{title:"报文"},panel:{
											items:[
												this.tGrepResult	=new EG.ui.Label({style:"line-height:25px;background-color:white;overflow:auto;text-align:left;white-space:nowrap"})
											]
										}},
										{tab:{title:"分析"},panel:{
											layout:"border",
											items:[
												this.pAnalysis=new EG.ui.Panel({xtype:"panel",region:"left",width:172,style:"overflow:auto",items:[
													this.bAnalysisSeqnos=new EG.ui.BoxGroup({xtype:"boxGroup"	,boxStyle:"white-space:nowrap",boxHeight:30,afterselect:function(selected){
														if(selected){


															var seqno			=this.getValue();
															var packet			=me.analysis_packets[seqno];

															me.curApi_info		=packet["api_info"];

															me.fAnalysisForm.clear();
															me.fAnalysisForm.addItem([
																{pos:[0,0],xtype:"formItem",type:"text",name:"api"			,title:"API"		,defValue:packet["api"],after:packet["api_after"]},
																{pos:[0,1],xtype:"formItem",type:"text",name:"seqno"		,title:"流水号"	,defValue:packet["seqno"]},
																{pos:[1,0],xtype:"formItem",type:"text",name:"token"		,title:"TOKEN"		,defValue:packet["token"]},
																{pos:[1,1],xtype:"formItem",type:"text",name:"user"			,title:"用户"		,defValue:packet["user"]},
																{pos:[2,0],xtype:"formItem",type:"text",name:"req_date"	,title:"请求时间"	,defValue:packet["req_date"]},
																{pos:[2,1],xtype:"formItem",type:"text",name:"req_params"	,title:"请求"		,defValue:packet["req_params"],after:new EG.ui.Button({text:"匹配显示",click:function(){
																	var txt=me.fAnalysisForm.getData()["req_params"];
																	var obj=EG.Object.$eval(txt);
																	var grid_params=[];
																	for(var k in obj){
																		grid_params.push({
																			name	:k,
																			value	:obj[k]
																		});
																	}
																	if(me.curApi_info){
																		var new_obj={};
																		var params=me.curApi_info["params"];
																		for(var l=0;l<grid_params.length;l++){
																			var gp=grid_params[l];
																			for(var i=0;i<params.length;i++){
																				if(params[i].name==gp.name){
																					EG.copy(gp,params[i]);

																					if(params[i]["unnull"]){
																						gp["unnull_text"]="<span style='color:red'>[必填]</span>"
																					}

																					break;
																				}
																			}
																		}
																	}
																	me.showParams(grid_params);
																}})},
															]);

															var packet_services	=packet["service"]||[];
															for(var i=0;i<packet_services.length;i++){
																var service		=packet_services[i];
																var fs=null;

																//if(service["type"]=="DS-HESSION"){
																//	fs=new EG.ui.Fieldset({xtype:"fieldSet",height:"auto",title:service["type"]+":"+service["protocol"]+(("<span style='margin-left:30px'>"+service["protocol_after"]+"<span>")||"&nbsp;"),items:[
                                                                //
																//	]})
																//	fs.addItem({xtype:"formItem",type:"text",name:"req_date",title:"请求时间",defValue:service["req_date"]});
																//	fs.addItem({xtype:"formItem",type:"text",name:"req_data",title:"请求内容",defValue:service["req_data"],after:new EG.ui.Button({text:"好好显示",click:function(){
																//		me.openJSONFormat(this.getElement().parentNode.item.getValue());
																//	}})});
																//	fs.addItem({xtype:"formItem",type:"text",name:"res_date",title:"响应时间",defValue:service["res_date"]});
																//	fs.addItem({xtype:"formItem",type:"text",name:"res_date",title:"响应类型",defValue:service["res_type"]});
																//	fs.addItem({xtype:"formItem",type:"text",name:"res_data",title:"响应内容",defValue:service["res_data"],after:new EG.ui.Button({text:"好好显示",click:function(){
																//		me.openJSONFormat(this.getElement().parentNode.item.getValue());
																//	}})});
																//}else if(service["type"]=="处理失败"||service["type"]=="应用失败") {
																//	fs=new EG.ui.Fieldset({xtype:"fieldSet",height:"auto",title:service["type"],style:"color:red",items:[
                                                                //
																//	]});
																//	fs.dBody.innerHTML=service["fail_result"];
																//}

																me.fAnalysisForm.addItem(fs);
																//fs.set
															}


															//me.fAnalysisForm.setData();
														}
													}})
												]}),
												this.fAnalysisForm	=new EG.ui.Form({region:"center",labelWidth:140,style:"overflow:auto",items:[
												]})
											]
										}}
									]
								})

							]
						}),
					]
				});

				this.pRoot.addItem(this.pMain);

				//
				var data_search=EG.Tools.query("data_search");
				if(data_search){
					this.form.setData(data_search);
				}
			},
			
			/**
			 * 开始循环获取查找结果
			 */
			doGrepQueryLoop:function(){
				var me=this;
				this.grepQueryLoop_start=new Date().getTime();
				var fn=function(){
					me.requestApi({
						url:"api/core/log/grep/query",
						params:{
							grep_id:me.grep_id,
						},
						success:function(packet){
							EG.Locker.lock(false);

							//判断超时
							if(!packet){
								if((new Date().getTime()-me.grepQueryLoop_start)>1000*30){
									EG.Locker.message("超时30s没有响应");
								}else{
									setTimeout(fn,1000);
								}
								return ;
							}

							var stop		=packet["stop"];
							var unit		="";
							var leftSize	=packet["leftSize"];

							if(leftSize>1024*1024*1024)		{leftSize=leftSize/(1024*1024*1024);unit="GB";}
							else if(leftSize>1024*1024)		{leftSize=leftSize/(1024*1024);unit="MB";}
							else if(leftSize>1024)      	{leftSize=leftSize/1024;unit="KB";}
							else                        	{unit="Byte";}

							//
							me.labPast.setValue((!stop?"运行中":"已停止")+",经过:"+parseInt((packet["past"]/1000))+"s,剩余:"+leftSize.toFixed(2)+unit);
							var matches	=packet["matches"];
							var str		="";
							me.curData=matches;
							for(var i=0;i<matches.length;i++){
								str+=matches[i]+"<br/>";
							}
							me.tGrepResult.setValue(str);
							EG.css(me.tGrepResult.getElement(),"line-height:20px");

							//分析
							me.doAnalysis();

							//停止
							if(!stop){
								setTimeout(fn,1000);
							}
						},
						faild:function(ex_data,ex){
							if(ex.data){
								if(ex.data["status"]=="LOG_FILE_OVERMAX"){
									var l=ex.data["filesize"];
									var s=l/(1024*1024*1024);
									EG.Locker.confirm("文件过大["+s+"G],确定强制查询么",function(){
										force["force"]=true;
										me.doGrepStart(force);
									});
								}
							}else{
								EG.Locker.message(ex["msg"]);
							}
						}
					});
				};
				fn();
			},

			/**
			 * 开始分析
			 * @param force
             */
			doGrepStart:function(force){
				var me=this;
				var fd=this.form.getData();

				this.tGrepResult.setValue("");
				this.fAnalysisForm.clear();
				this.bAnalysisSeqnos.setTextvalues([]);
				this.labPast.setValue("分析中...");
				//this.doAnalysis();

				EG.Tools.save("data_search"	,fd);

				EG.Locker.wait("正在Grep...");
				this.requestApi({
					url:"api/core/log/grep/start",
					params:fd,
					success:function(packet){
						EG.Locker.lock(false);
						me.grep_id=packet["grep_id"];
						me.doGrepQueryLoop();
					},
					faild:function(ex_data,ex){
						if(ex.data){
							if(ex.data["status"]=="LOG_FILE_OVERMAX"){
								var l=ex.data["filesize"];
								var s=l/(1024*1024*1024);
								EG.Locker.confirm("文件过大["+s+"G],确定强制查询么",function(){
									force["force"]=true;
									me.doGrepStart(force);
								});
							}
						}else{
							EG.Locker.message(ex["msg"]);
						}
					}
				})
			},

			/**
			 * 分析
			 */
			doAnalysis:function(){
				if(!this.curData){
					return;
				}
				var data=this.curData;
				var packets={};
				var service={};
				var seqnos=[];
				var matrixType="";

				//倒序解析
				for(var i=0;i<data.length;i++) {
					var str 				= data[i];
					var sign 				= "";//标识
					var signCount 			= 0; //标识数量
					var commonSigns 		= [];//通用标识
					var commonSignsCount	=5;
					var specSigns 			= [];//特殊标识
					var specSignsCharIndex	=[];
					var content 			= "";//内容
					var signStatus 			= "E";
					for (var k = 0; k < str.length; k++) {
						var c = str.charAt(k);

						if (c == '[') {
							signCount++;
							signStatus = "S";
						} else if (c == ']') {
							signStatus = "E";
							specSignsCharIndex.push(k);
							if (signCount > commonSignsCount) {
								specSigns.push(sign);
							} else {
								commonSigns.push(sign);
							}
							sign	="";
							content = "";
						} else {
							if (signStatus == "E") {
								content += c;
							} else {
								sign += c;
							}
						}
					}
					var common_date 	= commonSigns[0];
					var common_logLevel = commonSigns[1];
					var common_api 		= commonSigns[2];
					var common_seqno 	= commonSigns[3];
					var common_user 	= commonSigns[4];

					if (packets[common_seqno] == null) {
						packets[common_seqno] = {};
						if(!common_seqno) {
							continue;
						}
						seqnos.push(common_seqno);
					}

					var packet = packets[common_seqno];

					if(!packet["api"])			packet["api"]		 = common_api;
					if(!packet["seqno"])		packet["seqno"] 	 = common_seqno;
					if(!packet["user"])		packet["user"] 		 = common_user;
					if(!packet["req_date"])	packet["req_date"]  = common_date;

					var mApi;
					packet["api_info"]= mApi;
					if(packet["service"]==null){
						packet["service"]=[];
					}

					if (specSigns[0] == "API") {
						packet["token"] 	= specSigns[1];
						packet["ip"] 		= specSigns[2];
						packet["req_params"]= content;
					//} else if (specSigns[0] == "DS-HESSION"){
                    //
					//	if(specSigns[2]=="REQ"){
					//		service={};
					//		packet["service"].push(service);
					//		service["type"] 	= specSigns[0];
					//		service["protocol"] = specSigns[1];
                    //
					//		if(this.codeInfos) {
					//			var ds 	=this.codeInfos["ds"];
					//			var ds_pro	=ds[service["protocol"]];
					//			if(ds_pro){
					//				service["protocol_after"]="<span style='color:blue;font-weight: bolder'>"+ds_pro+"</span>";
					//			}
					//		}
                    //
					//		service["req_data"] = str.substr(specSignsCharIndex[commonSignsCount+2]+1);
					//		service["req_date"] = common_date;
					//	}else if(specSigns[2]=="RES"){
					//		service["res_type"]	= specSigns[3]
					//		service["res_data"] = str.substr(specSignsCharIndex[commonSignsCount+3]+1);
					//		service["res_date"] = common_date;
					//	}
					//} else if (specSigns[0] == "处理失败"||specSigns[0] == "应用失败"){
					//	service={};
					//	packet["service"].push(service);
					//	service["type"] 	= specSigns[0];
					//	service["fail_result"]	= str.substr(specSignsCharIndex[commonSignsCount+0]+1);
					}else{
						//
					}
				}


				var tvs_seqnos=[];
				for(var i=0;i<seqnos.length;i++){
					var seqno=seqnos[i];
					var txt=seqno.substring(0,4)+"-"+
							seqno.substring(4,6)+"-"+
							seqno.substring(6,8)+" "+
							seqno.substring(8,10)+":"+
							seqno.substring(10,12)+":"+
							seqno.substring(12,14)+"("+
							seqno.substring(14,16)+")"+
							seqno.substring(16);
					tvs_seqnos.push([txt,seqno])
				}
				this.bAnalysisSeqnos.setTextvalues(tvs_seqnos);

				this.analysis_packets=packets;

				this.tp.select(1);
				if(tvs_seqnos.length>0){
					this.bAnalysisSeqnos.setValue(tvs_seqnos[0][1]);
				}
				//this.fAnalysisResult
			},

			/**
			 * JSON格式化
			 */
			openJSONFormat:function(data){
				var me=this;
				if(!this.dlgFormat){
					var size=EG.getSize(EG.getBody());
					this.dlgFormat=new EG.ui.Dialog({
						title:"格式化",
						width:size.innerWidth*0.8,
						height:size.innerHeight*0.8,
						layout:"border",
						renderTo:EG.getBody(),
						items:[
							{xtype:"xPanel",region:"right",width:"30%",title:"输入",layout:{type:"line",direct:"V"},items:[
								this.taJSONInput=new EG.ui.Textarea({style:"margin:10px;"}),
								{xtype:"panel",height:30,items:[
									{xtype:"button",text:"脱字符串",click:function(){
										var str=me.taJSONInput.getValue();
										str=EG.String.replaceAll(str,'\\\\"','"');
										str=EG.String.removeStart(str,'"');
										str=EG.String.removeEnd(str,'"');
										me.taJSONInput.setValue(str);
										me.printJSON();
									}},
									{xtype:"button",text:"脱[]",click:function(){
										var str=me.taJSONInput.getValue();
										str=EG.String.removeStart(str,'[');
										str=EG.String.removeEnd(str,']');
										me.taJSONInput.setValue(str);
										me.printJSON();
									}},
									this.rowConvert=new EG.ui.Text({style:EG.Style.c.dv,width:100,height:25})
								]}
							]},
							this.jv=new EG.ui.JSONView({region:"center",style:"overflow:auto;font-size:14px;margin:10px;"})
						]
					});

					EG.Event.bindEvent(this.taJSONInput.getElement(),"onkeyup",function(){
						var item=this.item;

						if(me.w4parse){
							clearTimeout(me.w4parse);
						}
						me.w4parse=setTimeout(function(){
							me.printJSON();
						},500);
					});
				}
				this.dlgFormat.open();

				if(data){
					this.taJSONInput.setValue(data);
					EG.Event.fireEvent(this.taJSONInput.getElement(),"onkeyup");
				}
			},

			/**
			 * 显示请求参数
			 * @param data
             */
			showParams:function(data){
				if(!this.dlgParams){
					var size=EG.getSize(EG.getBody());
					this.dlgParams=new EG.ui.Dialog({
						title:"参数",
						width:size.innerWidth*0.8,
						height:size.innerHeight*0.8,
						renderTo:EG.getBody(),
						items:[
							this.gParams=new EG.ui.InputGrid({
								addAble:false,
								columns:[
									{ig_title:"字段名"	,ig_name:"name"		,xtype:"text"	,width:100,height:25,fix:true		},
									{ig_title:"数值"		,ig_name:"value"	,xtype:"text"	,width:200,height:25				},
									{ig_title:"描述"		,ig_name:"comment"	,xtype:"text"	,width:100,height:25				},
									{ig_title:"类型"		,ig_name:"type"		,xtype:"text"	,width:80,height:25				},
									{ig_title:"校验"		,ig_name:"validate"	,xtype:"text"	,width:400,height:25				},
									{ig_title:"必填"		,ig_name:"unnull_text"	,xtype:"label"	,width:100,height:25				}
								],
								showFoot:false,
								boxAble	:false
							})
						]
					});
				}
				this.dlgParams.open();
				this.gParams.setValue(data);
			},

			/**
			 * 打印JSON
			 */
			printJSON:function(){
				var v=this.taJSONInput.getValue();
				var obj=null;
				try{
					obj=EG.Object.$eval(v);
				}catch(e){
					return;
				}
				this.jv.setValue(obj);
			}
		};
	});
})();
