(function(){
	API.define("PAGE.BaseShowPage",[
		"PAGE.BasePage"
	],function(BasePage,ME){
		return {
			extend:BasePage,
			config:{

				/**
				 * 页面类型
				 */
				page_type   :"show",

				/**
				 * 请求
				 */
				action      :"",

				/**
				 * 表单配置
				 */
				formConfig	:null,
				/**
				 * 表格配置
				 */
				gridConfig	:null,
				/**
				 * 表配置
				 */
				treeConfig	:null,
				/**
				 * 查询配置
				 */
				queryConfig	:null,
				/**
				 * 按钮配置
				 */
				btnsConfig	:null,
			},

			constructor:function(cfg){
				this.callSuper([cfg]);
			},

			init_add:function(){
				this.submitBtnsConfig=this.getSubmitBtnConfig();
				this.build();
				//this.setPageTitle(this.modelName+"添加");
				this.buildForm();
				this.buildBtns();
				this.buildSubmitBtns();
			},
			init_edit:function(){
				this.fillIdOnSubmit=true;
				this.submitBtnsConfig=this.getSubmitBtnConfig();
				this.build();
				//this.setPageTitle(this.modelName+"修改");
				this.buildForm();
				this.buildBtns();
				this.buildSubmitBtns();
				this.loadData();
			},
			init_view:function(){
				this.submitBtnsConfig=this.getSubmitBtnConfig();
				this.build();
				//this.setPageTitle(this.modelName+"查看");
				this.buildForm();
				this.buildBtns();
				this.buildSubmitBtns();
				this.loadData();
				this.form.setEditable(false);
			},

			getSubmitBtnConfig:function(){
				var btnsConfig=null;
				if(this.page_type=="add"){
					btnsConfig=["submit","reset","cancle"];
				} else if(this.page_type=="edit"){
					btnsConfig=["submit","reset","cancle"];
				} else if(this.page_type=="view"){
					btnsConfig=["cancle"];
				} else if(this.page_type=="audit"||this.page_type=="reject"){
					btnsConfig=["submit"];
				}
				return btnsConfig;
			},

			/**
			 * 创建主体
			 */
			build:function(){
				var me=this;

				this.pRoot.setLayout("border");

				this.pRoot.addItem([
					this.pTop       =new EG.ui.Panel({region:"top"      ,style:"margin:10px",height:"auto"}),
					this.pCenter    =new EG.ui.Panel({region:"center"   ,layout:"border",style:"margin:10px"}),
					this.pBottom    =new EG.ui.Panel({region:"bottom"   ,height:"auto",style:"text-align:center;margin:10px"})
				]);
			},

			/**
			 * 创建表单
			 */
			buildForm:function(){
				var me=this;

				//创建Form
				EG.copy(this.formConfig,{
					width	:"100%",
					style	:"margin:0px 0px",
					region  :"top",
					height  :"auto"
				},false);

				this.form=new EG.ui.Form(this.formConfig);
				this.pCenter.addItem(this.form);
			},

			/**
			 * 创建按钮
			 */
			buildBtns:function(){
				var me=this;

				for(var i=0;i<this.btnsConfig.length;i++){
					var btn=this.btnsConfig[i];

					(function(type){
						var btnName,btnClick,btnStyle="margin:5px;",btnClickSrc=me;

						if(typeof(type)=="string"){
							if(type=="add")				{btnName="添加";btnClick=me.openAdd;}
							else if(type=="edit")		{btnName="修改";btnClick=me.openEdit;}
							else if(type=="view")		{btnName="查看";btnClick=me.openView;}
							else if(type=="delete")		{btnName="删除";btnClick=me.doDelete;}
							else if(type=="exp")		{btnName="导出";btnClick=me.doExp;}
							else if(type=="close")		{btnName="关闭";btnClick=me.doCancle;}
						}else{
							btnName 	=type["name"];
							btnClick    =type["click"];
							btnStyle    =type["style"];
							btnClickSrc =type["btnClickSrc"]||me;
						}

						//权限判断按钮
						//if(me.page_btns!=null&&!EG.Array.has(me.page_btns,btnName)){
						//	return;
						//}

						me.pTop.addItem(new EG.ui.Button({
							text    :btnName,
							click   :btnClick,
							clickSrc:btnClickSrc,
							style   :btnStyle
						}));

						//EG.Style.c.dv
					})(btn);
				}
			},

			buildSubmitBtns:function(){
				var me=this;

				for(var i=0;i<this.submitBtnsConfig.length;i++){
					var btn=this.submitBtnsConfig[i];

					(function(type){
						var btnName,btnClick,btnStyle="margin:5px;",btnClickSrc=me;

						if(type=="submit")			{btnName="保存";btnClick=me.doSubmit;}
						else if(type=="reset")		{btnName="重置";btnClick=me.doReset;}
						else if(type=="back")		{btnName="返回";btnClick=me.doBack;}
						else if(type=="cancle")	    {btnName="取消";btnClick=me.doCancle;}

						me.pBottom.addItem(new EG.ui.Button({
							text    :btnName,
							click   :btnClick,
							clickSrc:btnClickSrc,
							style   :btnStyle
						}));

					})(btn);
				}
			},

			/**
			 * 加载API
			 */
			loadData:function(){
				var me=this;
				var params=this.getLoadDataParam();

				EG.Locker.wait("加载数据");
				this.request({
					url     :this.action+"/find",
					content :EG.toJSON(params),
					success :function(data){

						if(data==null){
							EG.Locker.message({
								message:"数据不存在"
							});
							return ;
						}

						EG.Locker.lock(false);
						me.curData=data;

						me.form.setData(data);
						if(me.afterLoadData){
							me.afterLoadData.apply(me,[data]);
						}
					}
				});
			},

			/**
			 * 获取加载数据参数
			 */
			getLoadDataParam:function(){
				var p={};
				for(var i=0;i<this.keyId.length;i++){
					p[this.keyId[i]["name"]]=this.keyId[i]["value"];
				}
				return p;
			},

			/**
			 * 执行提交
			 */
			doSubmit:function(){
				var me=this;
				if(!this.form.validate()) return;
				var fd=this.form.getData();

				//
				if(this.fillIdOnSubmit){
					for(var i=0;i<this.keyId.length;i++){
						fd[this.keyId[i]["id"]]=this.keyId[i]["value"];
					}
				}

				if(this.beforeSubmit){
					var rtn=this.beforeSubmit.apply(this,[fd]);
					if(rtn===false){
						return;
					}
				}

				EG.Locker.wait("正在提交");


				this.request({
					url     :(this.action?this.action+"/":"")+this.getSubmitMethod(),
					content :EG.toJSON(fd),
					success :function(data){
						EG.Locker.message({
							message:"操作成功",
							autoclose:true,
							callback:function(){
								if(me.afterSubmit){
									me.afterSubmit(data);
								}

								if(me.page_type=="add"){
									me._initKeys(data);
								}

								//me.close();
								if(me.parent!=null&&me.parent.reload!=null){
									me.openView();//
									me.parent.reload();
								}
							}
						});
					}
				});
			},

			/**
			 * 获取提交方法
			 */
			getSubmitMethod:function(){
				return this.page_type;
			},

			afterUpload:function(){

			},

			doBack:function(){
				history.back();
			},
			doCancle:function(){
				this.close();
			},
			/**
			 * 重新加载
			 */
			reload:function(){

			},

			/**
			 * 表单重置
			 */
			doReset:function(){
				this.form.setData(this.curData||{});
			},

			openAdd:function(){
				this.open({
					type        :"PAGE."+this.modu+"."+this.modelName,
					page_type   :"add",
					parent      :this.parent,
					params      :{}
				});
			},

			openEdit:function(){
				this.open({
					type        :"PAGE."+this.modu+"."+this.modelName,
					page_type   :"edit",
					parent      :this.parent,
					params      :this.getLoadDataParam()
				});
			},

			openView:function(){
				this.open({
					type        :"PAGE."+this.modu+"."+this.modelName,
					page_type   :"view",
					parent      :this,
					params      :this.getLoadDataParam()
				});
			},
		};
	});
})();