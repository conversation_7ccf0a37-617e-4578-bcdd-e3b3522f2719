(function(){
	MON.define("PAGE.item.TreeGridChooser",[
		"PAGE.BasePage"
	],function(BasePage,ME){
		return {
			extend:EG.ui.Item,
			config:{
				title			:null,
				keyName			:null,
				keyValue		:null,
				treeConfig		:null,
				gridConfig		:null,
				formConfig:null,
				popWidth		:500,
				popHeight		:300,
				parent			:null,
				afterSelect		:null,
				beforeOpen		:null,
				keySetName		:null,	//当SetValue执行时先插入的
				popOnly			:false,
				action_loadData :null
			},
			extend:BasePage,
			constructor:function(cfg){
				var me=this;
				this.config=cfg;
				this.initConfig(cfg);

				//是否有树
				this.treeAble=(this.treeConfig!=null);

				this.element=EG.CE({tn:"div",cn:[
					this.dMask=EG.CE({tn:"div",style:EG.Style.c.dv}),
					this.btn=new EG.ui.Button({text:"选择",click:this.open,clickSrc:this,cls:"eg_button_small",style:EG.Style.c.dv})
				]});

				//
				if(EG.isArray(this.formConfig)){
					this.formConfig={
						items:this.formConfig
					};
				}

				EG.copy(this.formConfig,{
					region:"top",
					height:"auto",
					layout:"default",
				},false);

				//查询行加按纽
				this.formConfig.items.push({xtype:"button",text:"查询",click:function(){me.grid.curPage();}})

				for(var i=0;i<this.formConfig.items.length;i++){
					var it=this.formConfig.items[i];

					if(it["width"]==null){
						it["width"]=120;
					}

					if(it.searchOnReady){
						it.item=this;
						if(it.type=="text"||it.type=="date"){
							it.onkeyup=this._events.searchItem.search_text;
						}else if(it.type=="select"){
							it.onchange=this._events.searchItem.search_select;
						}
					}

					it.style=EG.Style.c.dv;
				}

				this.pop=new EG.ui.Dialog({
					closeable	:false,
					lock		:true,
					renderTo	:this.parent,
					title		:this.title,
					width		:this.popWidth,
					height		:this.popHeight,
					items:[
						this.panel=new EG.ui.Panel(EG.copy(this.config,{
							layout		:"border",
							style		:"overflow:auto;border:0px;"
						},false))
					],
					btns:[
						new EG.ui.Button({text:"选择",click:function(){me.doSelect();}						,cls:"eg_button_small",style:"margin:1px 5px;"}),
						new EG.ui.Button({text:"取消",click:function(){me.pop.close();me.doValidate();}		,cls:"eg_button_small",style:"margin:1px 5px;"})
					]
				});

				this.build();

				EG.css(this.pop.dBody,"text-align:left");

				//加载树
				if(this.treeAble) this.tree.load();
			},

			open:function(){
				if(this.beforeOpen){
					if(this.beforeOpen.apply(this,[])===false) return;
				}
				this.pop.open();
			},

			close:function(){
				this.pop.close();
			},

			build:function(){
				var me=this;

				//BUILD TREE
				if(this.treeAble){
					this.panel.addItem({xtype:"xPanel",region:"left",width:200,style:"margin:2px",title:this.treeTitle,items:[
						this.tree=new EG.ui.Tree(
							EG.copy(this.treeConfig,{
								width:"100%",
								height:"100%",
								levelcodeLength:10,
								toExpandLv	:2,
								style		:"overflow:auto;border:0px;",
								onclick:function(){
									me.doSearch();
								}
							})
							)
					]});
				}

				//BUILD GRID
				this.panel.addItem({xtype:"panel",region:"center",layout:"border",items:[
					this.form=new EG.ui.Form(this.formConfig),

					this.grid=new EG.ui.Grid(
						EG.copy(this.gridConfig,{
							region:"center",

							remotingCallback	:function(seg){me.setData(seg);},
							pageSize			:30,
							style				:"margin:2px"
						}))
				]});

			},

			getElement:function(){
				return this.element;
			},
			getValue:function(){
				return this.value;
			},
			setValue:function(value,d){
				var v=d!=null?(EG.String.n2e(d[this.keySetName]||d[this.keyName])):"";
				this.dMask.innerHTML=v;
				EG.css(this.dMask,"margin-right:"+(v?6:0)+"px;");
				this.value=value;
			},
			doSelect:function(){
				var v=this.grid.getSelectData()[0];
				if(v==null){
					EG.Locker.message("请选择行数据");
					return;
				}

				if(!this.popOnly){
					this.dMask.innerHTML=v[this.keyName];
					EG.css(this.dMask,"margin-right:"+(v[this.keyName]?6:0)+"px;");
				}

				this.value=v[this.keyValue];
				this.pop.close();

				this.doValidate();

				//选择后动作
				if(this.afterSelect){
					this.afterSelect.apply(this,[this.value,v]);
				}
			},

			doValidate:function(){
				if(this.formItem&&this.formItem.validate) this.formItem.validate();
			},

			render:function(){
				if(!this.popOnly){
					EG.css(this.dMask,"line-height:"+this.height+"px");
				}
			},
			setData:function(seg){
				var me		=this;
				var filterParams=this.gridConfig.filterParams;
				var params	=this.getParams(seg);
				params	=filterParams?filterParams.apply(this,[params]):params;

				EG.Locker.wait("正在加载数据...");
				var me=this;
				this.request({
					url     :this.action_loadData,
					content :EG.toJSON(params),
					success :function(data){
						me.grid.setData       (data["datas"]);
						me.grid.setDataSize   (data["count"]);

					}
				});
			},

			filterParams:function(params){
				return params;
			},

			getParams:function(seg){
				//查询数据
				var fd=this.form.getData();
				var searchMap={};
				for(var k in fd){
					if(fd[k]!==""&&searchMap[k]!==null){
						searchMap[k]=fd[k];
					}
				}

				//
				if(this.treeAble){
					var node=this.tree.getSelected();
					if(node){
						var v=node.getValue();
						if(v!=null){
							if(node.isTypeNode){
								fd[this.treeConfig["keyLeveltype"]] =v;
							}else{
								fd[this.treeConfig["keyLeveltype"]] =v[this.treeConfig["keyLeveltype"]];
								fd[this.treeConfig["searchName"]]   =v[this.treeConfig["searchName"]];
							}
						}
					}
				}

				//排序结果
				var orderMap={};
				if(this.grid.curOrderName){
					orderMap[this.grid.curOrderName]=this.grid.curOrderDesc;
				}

				return {
					searchMap	:searchMap,
					groupMap	:null,
					orderMap	:orderMap,
					startIndex	:this.grid._currentPage*this.grid.pageSize,
					pageIndex	:(this.grid._currentPage+1),
					pageSize	:this.grid.pageSize

				};
			},
			doSearch:function(){
				this.grid.curPage();
			},
			_events:{
				searchItem:{
					search_text:function(e){
						var e=EG.Event.getEvent(e);
						if(e.keyCode==13){
							this.item.item.grid.curPage();
						}
					},
					search_select:function(e){
						if(!this.item.grid) return;
						this.item.grid.curPage();
					}
				}
			},
			getTitle:function(value,data){
				if(!data) return "";
				return this.keySetName?data[this.keySetName]:data[this.keyName];
			}
		}
	});
})();
