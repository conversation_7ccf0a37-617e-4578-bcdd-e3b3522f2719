.fz(){
    //font-size:14px;
}
@eg_lineHeight          :24px;
@eg_lineHeight_ipt      :18px;

.suit(@color,@background,@border){
	color		: @color;
	background	: @background;
	border		: @border;
}

.eg_skin_main		{.suit(#000000	,#FFFFFF								,1px solid #74A6C1);}//主体
.eg_skin_title		{.suit(#FFFFFF	,url(bg/title.png)	#0077AC	repeat-x	,1px solid #8080FF);}//标题
.eg_skin_over		{.suit(#FFFFFF	,url(bg/over.png)	#0473a8 repeat-x	,1px solid #126DAF);}//遮盖时
.eg_skin_disable	{.suit(#878787	,url(bg/disable.png)					,1px solid #878787);}//禁用
.eg_skin_assist		{.suit(#000000	,#E0EFFB								,1px solid #74A6C1);}//辅助
.eg_skin_active		{.suit(#000000	,#FFFFFF								,1px solid #74A6C1);}//激活
.eg_skin_selected	{.suit(#FFFFFF	,url(bg/selected.png)					,1px solid #74A6C1);}//选中

@import "../base";

.eg_button{
	&-on{
		box-shadow	:0 1px 3px rgba(0,0,0,0.2);
	}
}

.eg_grid{
	&-headCol     {
	    border-color:white;
    }
    &-fixCol  {
        border-color:white;
    }
    &-bodyCol       {
    	border-color:white;
    }
}

.eg_dialog{
    .e(){
    	border-color:#0077AC;
        &:hover{
            border-color:white;
        }
    }
    &-closer            {.e();}
    &-fuller            {.e();}
}
