.fz(){
    //font-size:14px;
}

.fz(){
    //font-size:14px;
}
@eg_lineHeight			:24px;
@eg_lineHeight_ipt      :18px;

.suit(@color,@background,@border){
	color		: @color;
	background	: @background;
	border:1px solid #81A4D0;
}

.eg_skin_main		{.suit(#000000	,#FFFFFF								,null);}//主体
.eg_skin_title		{.suit(#000000	,url(bg/title.png)	#BED5F5	repeat-x	,null);}//标题
.eg_skin_over		{.suit(#FFFFFF	,#315dad								,null);}//遮盖时
.eg_skin_disable	{.suit(#878787	,url(bg/disable.png)					,null);}//禁用
.eg_skin_assist		{.suit(#000000	,#F1F7FE								,null);}//辅助
.eg_skin_active		{.suit(#000000	,#FFFFFF								,null);}//激活
.eg_skin_selected	{.suit(#000000	,#DFE8F6								,null);}//选中

@import "../base";

.eg_button{
	&-headCol     {
		
    }
    &-fixCol  {
    	border:1px solid white;
        border-right-color:#808080;
		border-bottom-color:#808080;
    }
    &-bodyCol       {
    	border:1px solid white;
    	border-right-color:#808080;
		border-bottom-color:#808080;
    }

    &-outer     {
		border:1px solid transparent;
	    background:transparent;
    }
    &-on        {
    	color		:#000000; 
    	background	: url(bg/title.png) #BED5F5	repeat-x;
		border		:1px solid #81A4D0 ;
    }
    &-disable   {
    	color:#FDFDFD;
    	border:1px solid transparent;
	    background:transparent;
    }
}