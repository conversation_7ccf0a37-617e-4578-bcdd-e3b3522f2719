.fz(){
    //font-size:14px;
}
@eg_lineHeight			:24px;
@eg_lineHeight_ipt      :18px;

.suit(@color,@background,@border){
	color		: @color;
	background	: @background;
	border:1px solid #FFFFFF;border-right-color:#808080;border-bottom-color:#808080;
}

.eg_skin_main		{.suit(#000000	,#D4D0C8								,null);}//主体
.eg_skin_title		{.suit(#000000	,#D4D0C8								,null);}//标题
.eg_skin_over		{.suit(#FFFFFF	,#000080								,null);}//遮盖时
.eg_skin_disable	{.suit(#878787	,url(bg/disable.png)					,null);}//禁用
.eg_skin_assist		{.suit(#000000	,#D4D0C8								,null);}//辅助
.eg_skin_active		{.suit(#000000	,#FFFFFF								,null);}//激活
.eg_skin_selected	{.suit(#FFFFFF	,#000080								,null);}//选中

@import "../base";

.eg_grid{
	&-headCol     {
		border:1px solid white;
	    border-right-color:#808080;
		border-bottom-color:#808080;
    }
    &-fixCol  {
    	border:1px solid white;
        border-right-color:#808080;
		border-bottom-color:#808080;
    }
    &-bodyCol       {
    	border:1px solid white;
    	border-right-color:#808080;
		border-bottom-color:#808080;
    }
}

.eg_xpanel{
	&-dBody         {background-color:transparent;}
	&-dHead			{background:url(bg/bar.png) repeat-y #99BCE5;color:white}
}

.eg_dialog{
	&-dBody         {background-color:transparent;}
	&-head			{background:url(bg/bar.png) repeat-y #99BCE5;color:white}
}