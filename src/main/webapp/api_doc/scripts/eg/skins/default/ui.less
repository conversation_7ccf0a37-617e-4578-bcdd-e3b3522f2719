.fz(){
    //font-size:14px;
}
@eg_lineHeight			:24px;
@eg_lineHeight_ipt      :18px;

.suit(@color,@background,@border){
	color		: @color;
	background	: @background;
	border		: @border;
}

.eg_skin_main		{.suit(#6A6A6A	,#FAFAFA								,1px solid #D4CECE);}//主体
.eg_skin_title		{.suit(#6A6A6A	,url(bg/title.png)	#EDEDED	repeat-x	,1px solid #DDDDDD);}//标题
.eg_skin_over		{.suit(#FFFFFF	,#C7D3BD								,1px solid #808080);}//遮盖时
.eg_skin_disable	{.suit(#878787	,url(bg/disable.png)					,1px solid #878787);}//禁用
.eg_skin_assist		{.suit(#000000	,#E7EED2								,1px solid #D4CECE);}//辅助
.eg_skin_active		{.suit(#000000	,#FFFFFF								,1px solid #D4CECE);}//激活
.eg_skin_selected	{.suit(#FFFFFF	,#A9C1B3								,1px solid #D4CECE);}//选中

@import "../base";