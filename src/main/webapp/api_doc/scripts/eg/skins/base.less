/** 并排 */
.d(){display:inline-block;*display:inline;zoom:1;}
.dv(){
    display:inline-block;*display:inline;zoom:1;vertical-align:middle;
}
/** 不选择 */
.ns(){
    -moz-user-select:none;-webkit-user-select:none;
}

.trans_bg(@s:0.3s){
    // -webkit-transition: background @s; 
    // -moz-transition: background @s; 
    // -o-transition: background @s; 
    // -ms-transition: background @s; 
    // transition: background @s;
}

/**
 * eg_button 按钮
 */
.eg_button{
    .dv;cursor:pointer;
    &-outer     {.eg_skin_title;}
    &-on        {.eg_skin_over;}
    &-disable   {.eg_skin_disable;}
    &-icon      {.dv;margin-left:6px}
    &-title     {.dv;line-height:@eg_lineHeight;height:@eg_lineHeight;padding:0px 6px;}//.eg_skin_main > .color;
    &-multi     {.dv;margin:0 3px;background:url(button/btnMulti.gif) no-repeat center ;width:7px;height:@eg_lineHeight;}
    &-menu     {border:1px solid #BFBFBF;}
    &-mi       {display:block;background:white;line-height:@eg_lineHeight;height:@eg_lineHeight;color:black;padding:5px;text-decoration:none;}
    &-mi:hover {background:#3470CC;color:white}
}

/**
 * eg_button_small 按钮(小)
 */
.eg_button_small		{
    .eg_button;
    &-title {line-height:@eg_lineHeight * 0.75;height:@eg_lineHeight * 0.75;}
}


/**
 * eg_upload 上传
 */
.eg_upload		            {
    padding:0px;
    &-selectBtn     {
        width:50px;height:20px;background:url(button/btnOnBg.png) repeat-x center;border-radius:12px;color:#FFF;line-height:20px;text-align:center;border:1px solid #1e2a46;font-weight: bold;
        &:hover{color:#FFFFFF;border:1px solid gray}
    }
    &-fileinput     {width:46px;height:16px;margin:2px}
    &-dPath         {.dv;border:1px dotted gray;overflow:hidden;background-color:white;}
    &-dFileName     {.dv;}
    &-dFileinput    {.dv;}
}

/**
 * eg_editor 编辑器
 */
.eg_editor						{
    .e(@x,@y){
        .dv;
        background:url(editor/bg.gif) @x @y;
        cursor:pointer;width:20px;height:20px;border:1px solid #ddd;font-size:0px;color:black;background-color:#EEEEEE;
        &:hover{
            background-color:white;
        }
    }

    .box(){
        &-box{
            display:block;border:1px solid #DDD;border-top-width:0px;width:60px;cursor:pointer;line-height:@eg_lineHeight;padding:3px;background-color:white;color:black;
            &:hover{
                border-color:#AAA;background-color:#E8F2FE
            }  
        }
    }

    position:relative;width: auto;

    &-dHtml                 {background: white;border:1px solid #b5b8c8;overflow: auto;}
    &-dHtml iframe          {margin: 0px;border: 0px;width:100%}
    &-toolbar               {}
    &-toolbar div           {.dv;}
    &-menus                 {position:absolute;}
    &-menu                  {position:absolute;display:none}
    &-uploadLabel           {background-color:#DFE3E8;padding:1px 5px;color:black;}
    &-uploadLabel-closer    {float:right;margin-top:3px;height:18px;width:18px;.dv;background:url(icon.png) -18px -240px;cursor:pointer}
    &-uploadLabel-closer:hover  {background-position:-54px -240px;}

    &-toolbar{
        &-bold              {.e(-140px,0px);}
        &-italic            {.e(-167px,0px);}
        &-underline         {.e(-195px,0px);}
        &-color             {.e(-335px,0px);
            &-box{
                width:10px;height:10px;font-size:9px;border:1px solid white;border-top:0px;border-right:0px;cursor:pointer
            }
        }
        &-fontname          {.e(-54px,-95px);.box;
            width:116px;height:20px;
            &-box{width:110px;line-height:20px;}
        }
        &-fontsize          {.e(-363px,0px);.box;}
        &-textalign         {.e(-224px,0px);.box;}
        &-list              {.e(-252px,0px);.box;}
        &-text-indent       {.e(-308px,0px);.box;}
        &-image             {.e(-419px,0px);.box;}
        &-full              {.e(-1056px,0px);.box;}
        &-program           {.e(-1078px,0px);.box;}
        &-program-code      {margin:4px 0px;}
        &-code-outer        {.e(-827px,0px);}
        &-code-on           {background-color:white;}
    }
}


/**
 * eg_form 表单
 */

.eg_form					{
    &-item                  {                                                
        &-dL                {
                            .dv;text-align:right;padding:2px 4px;word-break:keep-all;margin:1px 0px 1px 0px;
                            .eg_skin_assist;border-width:0px;border-top-left-radius:5px;border-bottom-left-radius:5px;
        }
        &-dR                {.dv;text-align:left;padding:2px 4px;}
        &-read              {border-bottom:1px solid #d9d9d9;overflow:auto;line-height:22px}
        &-star              {.dv;text-align:right;color:red;}
        &-title             {.dv;overflow:hidden}
        &-pre               {.dv;margin-right:3px;overflow:hidden}
        &-after             {.dv;margin-left:3px;overflow:hidden}
        &-error             {background-color:red;color:white}
        &-prop              {.dv;}                
    }
    
}


/**
 * eg_grid 表格
 */
.eg_inputgrid				{
    width:auto;border:1px solid #157FCC;background:#FFF;
    &-head          {background-color:#B7DBFA;}
    &-row           {background-color:#FCFCFC;}
    &-rowSelected   {.eg_skin_selected;}
    &-main          {overflow:auto}
    &-read          {cellspacing:1px;cellpadding:0px;border:0px;}
    &-read  thead   {background-color:#FCFCFC}
    &-read  thead td {background-color:#B7DBFA}
    &-read  tbody   {background-color:#B7DBFA;}
    &-read  tbody td {background-color:#FCFCFC}
}

/**
 * eg_grid 表格
 */
.eg_grid				{
    width:auto;.eg_skin_main;
    &-head          {}
    &-body          {}
    &-fixBody       {}
    &-fixHead       {}
    &-headCol     {
        padding: 0;
        .eg_skin_title;
        border-top-width:0px;
        border-left-width:0px;
    }
    &-fixCol  {
        padding: 0;
        .eg_skin_title;
        border-top-width:0px;
        border-left-width:0px;
    }
    &-bodyCol           {border-bottom:1px solid white;}
    &-fixBodyCellInner  {padding:2px 6px;text-overflow:ellipsis;overflow:hidden;min-width:20px;}
    &-fixHeadCellInner  {line-height:(@eg_lineHeight - 2px);padding:2px 6px;text-overflow:ellipsis;overflow:hidden}
    &-bodyCellInner     {line-height:(@eg_lineHeight - 4px);padding:2px 6px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;}
    &-headCellInner     {line-height:(@eg_lineHeight - 2px);padding:2px 6px 2px 5px;text-align: center;text-overflow:ellipsis;overflow:hidden;}
    &-head_order_desc   {background: url(grid/order_desc.gif) 90% center no-repeat;}
    &-head_order_asc    {background: url(grid/order_asc.gif) 90% center no-repeat;}

    & table         {table-layout:fixed;border-collapse:separate;}
    & .txtcenter    {text-align:center}
    & .txtleft      {text-align:left}
    & .txtright     {text-align:right}
    &-head-box      {}
    &-body-box      {}
    &-row{
        &-a         {.eg_skin_main;}
        &-b         {.eg_skin_assist;}
        &-over      {.eg_skin_over;cursor:pointer;.trans_bg;}
        &-selected  {.eg_skin_selected;cursor:pointer;}
    }
    
    &-foot      {background-color:#EEF6FB;height:30px;line-height:30px;text-align: right;}
    &-firstPage {background:url(grid/firstPage.gif);    width:16px;height:16px;.dv;cursor:pointer;}
    &-prePage   {background:url(grid/prePage.gif);      width:16px;height:16px;.dv;cursor:pointer;}
    &-nextPage  {background:url(grid/nextPage.gif);     width:16px;height:16px;.dv;cursor:pointer;}
    &-lastPage  {background:url(grid/lastPage.gif);     width:16px;height:16px;.dv;cursor:pointer;margin-right:15px;}
    &-gotoPage  {background:url(grid/gotoPage.gif);     width:16px;height:16px;.dv;cursor:pointer;}
    &-state       {
        margin-left:5px;margin-right:5px;.dv;vertical-align:middle;
        *{.dv;}
        input[type='text'] {line-height:20px;height:20px;color:red;width:20px;text-align:center}
    }
    
    &-recordSize  {.dv;margin-right:20px}
}

.pagingGrid{
    &_head_dPanel   {width:100%;height:20px;position:relative;}
    &_head_dAdj     {z-index:2;position:absolute;cursor:col-resize;right:-15px;width:10px;text-align:center;}
    &_head_dOpt     {z-index:2;position:absolute;cursor:pointer;right:0px;width:20px;height:20px;text-align:center;}
    &_head_dContent {text-align:center;width:100%}
    &_adjRuler      {position:absolute;z-index:99;width:1px;background-color:black;height:400px;}

    &_dColOpt       {position:absolute;z-index:99;background:#D4D0C8;color:#000000;border:1px solid #FFFFFF;border-right-color:gray;border-bottom-color:gray;line-height:20px;text-align:left;padding:2px;}
    &_dColOpt .ele  {cursor:pointer;padding:0 15px 0 15px;}
    &_dColSelect    {position:absolute;z-index:99;background:#D4D0C8;color:#000000;border:1px solid #FFFFFF;border-right-color:gray;border-bottom-color:gray;line-height:20px;text-align:left;padding:0 15px 0 15px;}
}

/*
* eg_tabPanel 选项卡
*/
.eg_tabPanel{
    .e(){
        &-tab{
            .dv;.ns;.eg_skin_title;height:26px;line-height:26px;cursor:pointer;font-weight: bold;
        }
        &-selected{
            .eg_skin_active;border-bottom:1px solid #FFFFFF;
        }
    }
    .e1(){
        .eg_skin_main;
    }

    &-tabstop               {
        .e;
        &-tab               {border-top-right-radius:4px;border-top-left-radius:4px;border-bottom:0px;}
    }
    &-panelstop             {.e1;}

    &-tabsbottom            {
        .e;
        &-tab               {border-bottom-right-radius:4px;border-bottom-left-radius:4px;border-top:0px;} 
    }
    &-panelsbottom          {.e1;}

    &-tabsleft              {
        .e;
        &-tab               {display:block;border-right:0px}
    }
    &-panelsleft            {.e1;}

    &-tabsright             {
        .e;
        &-tabsright-tab     {display:block;}
    }
    &-panelsright           {.e1;}

    &-tabs-tab{
        &-title         {.dv;text-align:center;margin:0px;padding:0px 10px;}
        &-closer        {.dv;background-image:url(tabPanel/tab-default-close.gif);right:0px;width:11px;height:11px;}
        &-closer:hover  {opacity:0.60;-moz-opacity:0.60;filter:alpha(opacity=60);}
    }
}


/**
 * eg_tree 树
 */
.eg_tree{
    .h(){
       height:20px;line-height:20px;    
    }
    .ns;padding:5px;margin:0px;background:white;
    &-node{
        margin-left:19px;width:90%;

        &-dNode             {
            .dv;padding:0px;white-space:nowrap;.h;
            &-dExpandBtn  {.dv;.h;}
            &-dTitle      {.dv;.h;cursor:pointer;color:black;margin-left:3px;word-break:keep-all;vertical-align: middle;}
            &-selected    {.dv;.h;background-color:blue;color:white;}
        }
        
        &-dChildNodes       {}
        &-dInsert           {height:4px;width:100px}

        &-l{
            &-l               {background:url(tree/L.png)         no-repeat center;width:19px;.d;.h;}
            &-t               {background:url(tree/T.png)         no-repeat center;width:19px;.d;.h;}
            &-lPlus           {background:url(tree/Lplus.png)     no-repeat center;width:19px;.d;.h;}
            &-tPlus           {background:url(tree/Tplus.png)     no-repeat center;width:19px;.d;.h;}
            &-lMinus          {background:url(tree/Lminus.png)    no-repeat center;width:19px;.d;.h;}
            &-tMinus          {background:url(tree/Tminus.png)    no-repeat center;width:19px;.d;.h;}
        }
        
        &-file              {background:url(tree/file.png)      no-repeat center;width:16px;.d;.h;}
        &-folder            {background:url(tree/foldericon.gif) no-repeat center;width:16px;.d;.h;}
        &-openfolder        {background:url(tree/openfoldericon.gif) no-repeat center;width:16px;.d;.h;}

        &-bgLine            {background:url(tree/I.png)         repeat-y;}
    }
    
}

/**
 * eg_drager 拖拽层
 */
.eg_drager					{
    padding:4px;border:1px dotted black;position:absolute;background-color:#EEE;z-index:999;opacity:0.4;-moz-opacity:0.4;filter:alpha(Opacity=40)
}

/**
 * eg_pop 弹出层
 */
.eg_pop						{
    position:absolute;top:0px;
    &-outer     {position:absolute;}
    &-locker    {position:absolute;background:#FFFFFF;opacity:0.8;-moz-opacity:0.8;filter:alpha(opacity=80);}
}

/**
 * eg_dialog 对话框
 */
.eg_dialog{
    position:absolute;
    .eg_skin_title;
    border-radius:5px; background-clip: content-box;box-shadow:0 10px 80px #555
    &-outer{
        position:absolute;
        -webkit-transition:width 4s ease-in,height 2s linear;
        -moz-transition:width 4s ease-in,height 2s linear;
        -ms-transition:width 4s ease-in,height 2s linear;
        -khtml-transition:width 4s ease-in,height 2s linear;
        transition:display 4s ease-in,display 2s linear;
        animation: myfirst 5s;
    }

    .e(@x,@y){
        display:inline-block;height:18px;width:18px;.eg_skin_title;background:url(icon.png) @x @y;cursor:pointer;
        &:hover{
            .eg_skin_over;background:url(icon.png) @x @y;
        }
    }

    &-locker            {position:absolute;background:#FFFFFF;
        opacity:0.8;
        -moz-opacity:0.8;
        filter:alpha(opacity=80);

        // filter: url(blur.svg#blur); //FireFox, Chrome, Opera */
        // -webkit-filter: blur(10px); //Chrome, Opera 
        // -moz-filter: blur(10px);
        // -ms-filter: blur(10px);    
        //     filter: blur(10px);
    }
    &-head              {height:15px;padding:5px 10px;border-bottom:1px solid #CCCCCC;}
    &-title             {font-weight:bolder;}
    &-trBtns            {position:absolute;top:5px;right:10px;width:40px;height:18px;text-align:right}
    &-closer            {.e(-18px,-240px);}
    &-fuller            {.e(-75px,-240px);}
    &-body              {background:#FFFFFF;color:#000000}
    &-foot              {height:21px;padding:2px 10px;border-top:1px solid #CCCCCC;}
}

/**
 * eg_locker
 */
.eg_locker{
    position:absolute;.eg_skin_title;
    border-radius:5px; background-clip: content-box;box-shadow:0px 0px 8px rgba(0,0,0,0.2);
    &-outer         {position:absolute;}
    &-locker        {position:absolute;background:#FFFFFF;opacity:0.5;-moz-opacity:0.5;filter:alpha(opacity=50);}
    &-head          {height:15px;padding:5px 10px;border-bottom:1px solid #CCCCCC;}
    &-title         {font-weight:bolder;}
    &-closer        {position:absolute;top:4px;right:10px;height:18px;width:18px;background:url(icon.png) -18px -240px;cursor:pointer}
    &-closer:hover  {background-position:-54px -240px;}
    &-body          {width:100%;background:#FFFFFF;color:#000000}
    &-foot          {width:100%;height:25px;border-top:1px solid #CCCCCC;}
    &-wait          {.dv;text-align:center;line-height:1.5;padding: 10px;word-break:break-all;}
    &-fontL         {font-weight:bold;line-height:15px;padding: 10px}
    &-fontM         {line-height:15px;padding: 10px;}
    &-fontS         {line-height:15px;padding: 10px;}
    &-type          {width:28px;height:28px;.dv;margin:10px}
    &-type-success  {background:url(locker/success_small.png);}
    &-type-faild    {background:url(locker/faild_small.png);}
}


.eg_pop_blank{
    position:absolute;
    &-outer         {position:absolute;}
    &-locker        {position:absolute;background:#FFFFFF;opacity:0.5;-moz-opacity:0.5;filter:alpha(opacity=50);}
    &-head          {}
    &-title         {}
    &-closer        {display:none}
    &-closer_on     {}
    &-body          {}
    &-foot          {}
}

.eg_ipt(){
    .e{
        line-height:@eg_lineHeight_ipt;height:@eg_lineHeight_ipt;border:1px solid #b5b8c8;margin:0px;padding:2px
    }
    &-input          {.e;background:url(text/text_bg.gif)    white repeat-x top;}
    &-error          {background:url(text/invalid_line.gif)  white repeat-x bottom;}
}
.eg_text                {.eg_ipt}
.eg_password            {.eg_ipt}
.eg_textarea            {.eg_ipt}
.eg_date                {
    .eg_ipt;
    &-input{
        background:url(text/datePicker.gif) no-repeat white right;
    }
}

/**
 * eg_select 选择器
 */
.eg_select              {
    .ns;
    &-iptText      {.dv;vertical-align: middle;line-height:@eg_lineHeight_ipt;height:@eg_lineHeight_ipt;border:0px;background:transparent;padding:0px;margin:0px;}
    &-arrow        {.dv;vertical-align: middle; width:16px;height:20px;background:url(text/arrow.gif) center no-repeat;cursor:pointer;margin:0px;}
    &-input        {background:url(text/text_bg.gif) white repeat-x top;overflow:hidden;border:1px solid #b5b8c8;padding-left:3px}
    &-error        {background:url(text/invalid_line.gif) white repeat-x bottom;border:1px solid #b5b8c8;padding-left:3px}
    &-opts         {position:absolute;z-index:1;max-height:300px;border:1px solid black;overflow-y:auto;overflow-x:auto;background-color:#FFFFFF;}
    &-opt          {
        .eg_skin_main;border-color:transparent;cursor:pointer;line-height:14px;padding:2px;white-space:nowrap;
        &-over     {.eg_skin_over}
        &-selected {.eg_skin_selected}
        &-b        {
            .dv;vertical-align: middle;border:1px solid rgb(181,184,200);background:#FFF;height:10px;width:10px;text-align:center;cursor:pointer;margin:0px;font-size:0px;
            &:hover   {border-color:gray;}
            &-select  {background:url(box/inner.gif) #FFF no-repeat center;}
            &-unselect{}
        }
        
    }
    
}

/**
 * eg_selectArea 选择器
 */
.eg_selectArea          {
    .e(){
        .dv;border:1px solid ;margin:0px;overflow:scroll;.ns;
    }

    &-slts     {.e;.eg_skin_main;}
    &-error    {.e;border-color:red;background-color:yellow;}
    &-dMid     {.dv;vertical-align: middle;}
    &-slted    {.eg_skin_selected;padding:0 5px;white-space:nowrap;}
    &-unslt    {padding:0 5px;white-space:nowrap;}
}

/**
 * eg_selectExpand 选择展开
 */
.eg_selectExpand          {
    &-opts      {}
    &-opt       {
        .eg_skin_main;.dv;cursor:pointer;line-height:1;padding:5px 10px;border:0px;
        &-over      {.eg_skin_over;     border:0px;color:#417DF2;background:transparent;}
        &-selected {.eg_skin_selected;  border:0px;color:#417DF2;background:transparent;font-weight:bolder;}
    }
}
   

/**
 * eg_box Box
 */
.eg_box 				{
    .dv;
    &-b                 {
        .dv;vertical-align: middle;border:1px solid rgb(181,184,200); background:#FFF;height:10px;width:10px;text-align:center;cursor:pointer;margin:0px;font-size:0px;
        &:hover         {border-color:gray;}
    }
    &-select            {background:url(box/inner.gif) #FFF no-repeat center;}
    &-unselect          {}
    &-text              {.dv;vertical-align: middle;height:@eg_lineHeight;line-height:@eg_lineHeight;cursor:pointer;margin-left:5px;margin-right:5px;}
}

.eg_boxgroup 			{
    &-error         {background:url(text/invalid_line.gif) white repeat-x bottom;}
}

/**
 * eg_xpanel
 */
.eg_xpanel{
    &-dCollapse        {
        float:right;width:@eg_lineHeight - 6px;height:@eg_lineHeight - 6px;margin:3px;cursor:pointer;.dv;
        &-top    {background:url(panel/arrow_top.png) white;}
        &-bottom {background-image:url(panel/arrow_bottom.png);}
        &-left   {background-image:url(panel/arrow_left.png);}
        &-right  {background-image:url(panel/arrow_right.png);}
    }
    &-dTitle           {height:@eg_lineHeight - 8px;line-height:@eg_lineHeight - 8px;margin:4px;margin-left:6px;font-weight:bold;.dv;}
    &-dBar             {.dv;}
    &-dHead            {height:26px;.eg_skin_title;border-bottom:1px solid #d0d0d0;}
    &-dBody            {background-color:white;}    
}

/**
 * eg_fieldset
 */
.eg_fieldset            {
    .eg_skin_main;margin:0px;padding:0px;
    &-legend     {margin-left:20px}
    &-dTitle     {margin:0px 3px}
    &-dBody      {padding:0 10px}
}

/**
 * eg_table
 */
.eg_table               {
    position:relative
    &-table         {position:absolute;width:100%;-webkit-user-select:none;cursor:pointer}
    &-table td      {height:20px}
    &-selectTop     {height:2px;    background:url(table/EwaAntH.gif);position:absolute;top:0px;left:0px        ;background-position-x:center   ;background-position-y:bottom;}
    &-selectLeft    {width:2px;     background:url(table/EwaAntV.gif);position:absolute;top:0px;left:0px        ;background-position-x:right    ;background-position-y:center;}
    &-selectRight   {width:2px;     background:url(table/EwaAntV.gif);position:absolute;top:0px;left:100px; }
    &-selectBottom  {height:2px;    background:url(table/EwaAntH.gif);position:absolute;top:100px;left:0px      ;background-position-x:center   ;background-position-y:top;}

    &-dMenu   {
        border:2px solid #d5d5d5;position:absolute;background-color:white;padding:2px
        a{
            background-color:white;color:black;display:block;line-height:30px;padding-left:4px;padding-right:4px;min-width:100px;cursor:pointer;
            &:hover{background-color:#d5d5d5;}
        }
    }
}

/**
 * eg_menu 菜单
 */
.eg_menu {

}

/**
 * eg_menuItem 菜单子项
 */
.eg_menuItem{
    .e(){
        position:relative;.dv;border-radius:0px;cursor:pointer;    
    }
    &-outer          {.e;.eg_skin_title;}
    &-outerOn        {.e;.eg_skin_over;     position:relative;font-weight:bolder;cursor:pointer;}
    &-outerSelected  {.e;.eg_skin_selected; position:relative;font-weight:bolder;cursor:pointer;}

    .e1(){
        .dv;
        line-height:1.5;height:1.5;padding:0px 6px;
    }
    &-text           {.e1;}
    &-textOn         {.e1;}
    &-textSelected   {.e1;}
    &-multi         {position:absolute;right:8px;top:0px;background:url(button/btnMulti.gif) no-repeat center ;width:7px;height:24px;}
}

/**
 * eg_calander 日历
 */
.eg_calander {
    &-top {
        border-bottom:1px solid #A3BAD9;text-align: center;height:20px;line-height:20px;.eg_skin_title;
        &-pre {background-image:url(grid/prePage.gif); width:16px;height:16px;.dv;cursor:pointer;margin:auto;}
        &-cur {.dv;margin:auto;}
        &-next{background-image:url(grid/nextPage.gif);width:16px;height:16px;.dv;cursor:pointer;margin:auto;}
    }

    &-main {
        overflow:auto;
        &-head  {
            tbody td {text-align:center;border:1px solid #DDDDDD;border-top-width:0px;border-left-width:0px;.eg_skin_title;}
        }
        
        &-body {
            overflow:auto;
            tbody{
                td {border:1px solid #DDDDDD;border-top-width:0px;border-left-width:0px;text-align:center}
                .selected{background-color:#DDEAF3;}    
            }
        }
    }
    
    &-left{
        &-head  {}
        &-body {overflow:hidden;}
        &-body tbody td{border:1px solid #DDDDDD;border-top-width:0px;border-left-width:0px;text-align:center;}
    }

    &-chooser            {
        border:1px solid #718BB7;background-color:white;
        a          {
            line-height:20px;color:#1B376C;padding:3px 5px;cursor:pointer;text-align:center
            &:hover    {background-color:#1B376C;color:white;}
        }
    }
}

/**
 * eg_fc 流程图
 */
.eg_fc{
    .e(){
        width:32px;height:32px;
    }
    &_select    {.e;background: url(fc/select.png);}
    &_connect   {.e;background: url(fc/connect.png);}
    &_start     {.e;background: url(fc/start.png);}
    &_end       {.e;background: url(fc/end.png);}
    &_node      {.e;background: url(fc/node.png);}
    &_branch    {.e;background: url(fc/branch.png);}
    &_combine   {.e;background: url(fc/combine.png);}
    &_zoomin    {.e;background: url(fc/zoomin.png);}
    &_zoomout   {.e;background: url(fc/zoomout.png);}

    &_editor   {
        &-dMenu   {
            border:2px solid #d5d5d5;position:absolute;background-color:white;padding:2px;
            a{
                background-color:white;color:black;display:block;line-height:30px;padding-left:4px;padding-right:4px;min-width:100px;cursor:pointer;
                &:hover{background-color:#d5d5d5;}  
            }
        }
    }
}