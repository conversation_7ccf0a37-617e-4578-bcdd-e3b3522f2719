.fz(){
    //font-size:14px;
}
@eg_lineHeight          :24px;
@eg_lineHeight_ipt      :18px;

.suit(@color,@background,@border){
	color		: @color;
	background	: @background;
	border		: @border;
}

.eg_skin_main		{.suit(#000000	,#FFFFFF								,1px solid #AAAAAA);}//主体
.eg_skin_title		{.suit(#FFFFFF	,#E44F51                                ,1px solid #F5BBBC);}//标题
.eg_skin_over		{.suit(#FFFFFF	,#EF7F81                                ,1px solid #F5BBBC);}//遮盖时
.eg_skin_disable	{.suit(#878787	,url(bg/disable.png)					,1px solid #AAAAAA);}//禁用
.eg_skin_assist		{.suit(#686868	,#FFFFFF								,1px solid #AAAAAA);}//辅助
.eg_skin_active		{.suit(#000000	,#FFFFFF								,1px solid #AAAAAA);}//激活
.eg_skin_selected	{.suit(#FFFFFF	,url(bg/selected.png)					,1px solid #AAAAAA);}//选中

@import "../base";

.eg_button{
	&-on{
		box-shadow	:0 1px 3px rgba(0,0,0,0.2);
	}
}

// .eg_form                    {
//     &-item  {}
// }
.eg_grid{
	&-headCol     {
        color:#888888;
        background:#E4E4E4;
        font-size:14px;
	    border-color:#DFDFDF;
    }
    &-fixCol  {
        color:#36BeAC;
        background:#FFFFFF;
        border-color:#DFDFDF;
    }
    &-bodyCol       {
        color:#888888;
        background:#FFFFFF;
    	border-color:#DFDFDF;
        border-right-width:1px;
    }
}

.eg_dialog{
    .e(){
    	border-color:#FFFFFF;
        &:hover{
            border-color:white;
        }
    }
    &-closer            {.e();}
    &-fuller            {.e();}
}

