/**
 * <AUTHOR>
 * EG 引擎类(Engine)
 * Core.js是EG的核心入口，实现了：
 */
(function(){

	if(typeof(_EG_NS_)=="undefined"){
		_EG_NS_=window;
	}

	/**
	 * @class EG
	 * @type {Window.EG}
	 */
	var EG=_EG_NS_["EG"]=window.EG=function(){

	};

	EG._className="EG";

	/**
	 *
	 * @type {boolean}
	 */
	EG.debug = true;

	/**
	 * ONERROR
	 * @param sMsg
	 * @param sUrl
	 * @param sLine
	 */
	EG.onError=function(sMsg, sUrl, sLine){
		if(EG.debug){
			var errorMsg = "<div style='text-align:left'><b>原因</b>:" + sMsg + "<br/>";
			if(sUrl!=null) errorMsg += "<b>行数</b>:" + sLine + "<br/>";
			if(sLine!=null)errorMsg += "<b>URL</b>:" + sUrl + "<br/></div>";
			if(EG.Locker){
				EG.Locker._lock=true;
				EG.Locker.message({message:errorMsg,closeable:true,force:true});
			}
			else alert(errorMsg);
		}
	};

	/**
	 * @property {HTMLDocument} document高速索引
	 */
	EG.doc=document;

	/**
	 * @property {HTMLBodyElement} document.body高速索引
	 */
	EG.body=null;

	/**
	 * 获取Body
	 * @return {HTMLBodyElement}
	 */
	EG.getBody=function(){
		if(!EG.body) EG.body=EG.doc.body;
		return EG.body;
	};

	EG.head=null;

	/**
	 * 获取Head
	 * @return {HTMLHeadElement}
	 */
	EG.getHead=function(){
		if(!EG.head) EG.head=EG.doc.head;
		return EG.head;
	};

	/**
	 * 返回非空的数值
	 * @returns {*}
	 */
	EG.unnull=function(){
		for(var i= 0;i<arguments.length;i++){
			if(arguments[i]!=null) return arguments[i];
		}
		return null;
	};

	/**
	 * 如果对象为空返回默认值,否则返回对象
	 * @param {*} o 对象
	 * @param {*} d 默认值
	 * @return {*}
	 */
	EG.n2d=function(o,d){
		if(o==null) return d;
		else return o;
	};

	/**
	 * 函数转对象,如果对象为null返回null,如果对象是函数返回函数执行结果，否则返回对象
	 * @param {*} o 对象
	 * @return {*}
	 */
	EG.f2o=function(o){
		if(o==null) return null;
		if(typeof(o)=="function"){
			return o();
		}else{
			return o;
		}
	};

	/**
	 * 获取脚本路径
	 * @param {String} scriptName 脚本名称
	 * @returns {String}
	 */
	EG.getScriptPath = function(scriptName) {
		var js = EG.doc.scripts;
		var jsPath;
		for ( var i = js.length; i > 0; i--) {
			if (js[i - 1].src.indexOf(scriptName) > -1) {
				jsPath = js[i - 1].src.substring(0, js[i - 1].src.lastIndexOf("/") + 1);
				if (jsPath.indexOf("http://") >= 0) {
					jsPath = jsPath.substring(jsPath.indexOf("/", 10));
				}
				return jsPath;
			}
		}
		return null;
	};

	var scriptPath="eg";

	/**
	 * 基础路径
	 * @property {String}
	 */
	EG.basePath = window._EG_BASEPATH_||EG.getScriptPath(scriptPath);

	/**
	 * @property {String} 上下文路径
	 */
	EG.contextPath="";
//	/**
//	 * 继承
//	 * 仅仅对原型链上的function进行继承,使子类与父类prototype指向一致
//	 *
//	 * @param {Class} sCls 子类
//	 * @param {Class|Object} pCls 父对象(属性集)
//	 * @param {Boolean?} override
//	 * @param {Boolean?} withNative 是否带原生方法
//	 * @param {Boolean?} forceMark 是否强迫标注方法所属类
//	 */
//	EG.extend = function(sCls, pCls, override,withNative,forceMark) {
//		// 默认为不覆盖
//		EG.n2d(override,false);
//
//		var pProp=pCls;
//
//		//类
//		if(typeof(pCls)=="function") {
//			pProp=pCls.prototype;
//		}else if(typeof(pCls)=="object"){
//			pCls=null;
//		}else{
//			throw new Error("EG#extend:父类型错误:" + typeof (pCls));
//		}
//
//		EG._extend(sCls.prototype,pProp,override,withNative,forceMark,pCls,sCls);
//		return sCls;
//	};
//
//	/**
//	 * 方法用引用,字面量递归继承,其它类型Clone
//	 */
//	EG._extend=function(sObj,pObj,override,withNative,forceMark,pClass,sClass){
//		for(var k in pObj){
//			var pV=pObj[k];
//			var sTp=typeof(sObj[k]);
//			if (!override&&sTp!="undefined") continue;
//			if(typeof(pV)=="function"){
//				if(!withNative&&(k=="initConfig"||k=="getSuperclass"||k=="callSuper"||k=="getClass")){
//					continue;
//				}
//				//第一次被继承时标注原始类=>用来callSuper时使用
//				if(!pV._class){
//					if(pClass){
//						pV._class=pClass;
//					}else if(forceMark&&sClass){
//						pV._class=sClass;
//					}
//				}
//				sObj[k]=pV;
//			//如果属性是对象进行copy
//			}else if(pV&&pV.constructor==Object){
//				sObj[k]={};
//				EG._extend(sObj[k],pV,override,withNative,forceMark,pClass,sClass);
//				//非function类型数据使用clone
//			}else{
//				sObj[k]=EG.clone(pV);
//			}
//		}
//	}


	/**
	 * 复制
	 * @param {Object} obj 目标对象
	 * @param {Object} props 被复制的属性s
	 * @param {Boolean?} override 是否覆盖
	 * @param {Boolean?} clone 是否使用clone
	 * @return {Object}
	 */
	EG.copy = function(obj, props, override,clone) {
		for (var key in props){
			if (override || typeof (obj[key]) == "undefined"){
				if(clone){
					obj[key] = EG.clone(props[key]);
				}else{
					obj[key] = props[key];
				}
			}
		}
		return obj;
	};

	/**
	 * 获取对象类型
	 * @param {*} o 任意类型
	 * @return {String}
	 */
	EG.getType=function(o){
		return Object.prototype.toString.call(o).slice(8,-1).toLowerCase();
	};


	var types={'undefined':1,'null':1,'number':1,'boolean':1,'string':1,'array':2,'function':3,'date':4};
	
	/**
	 * 克隆
	 * @param {*} dObj 目标
	 * @param {*} sObj 源
	 * @param {*} key 键值
	 * @private
	 */
	EG._clone=function(dObj,sObj,key){
		var val=sObj[key];
		var type=types[EG.getType(val)];
		if(1==type) {			// undefined, null, number, boolean, string
			dObj[key]=val;
		}else if(4== type) {	// date
			dObj[key]=new Date();
			dObj[key].setTime(val.getTime());
		} else {				// object, array, or function
			dObj[key]=EG.clone(val);
		}
	};

	/**
	 * 克隆
	 * @param {*} obj 对象
	 * @returns {*}
	 */
	EG.clone=function(obj){
		if(obj==null) return null;
		if(typeof(obj)=="undefined") throw new Error("EG.clone#待clone参数未定义");

		var cObj,typeNum, i,il,fn_body, fn_args;
		var type=EG.getType(obj);
		typeNum = types[type];
		cObj=obj;
		if (!typeNum) {
			// object
			if(type!="object"){
			//
			}else{
				if(obj==null||obj.constructor==null){
					alert(obj);
				}
				if(obj.constructor!=Object){	//如HTMLElement不能使用构造函数
					cObj=obj;
				}else{
					cObj=new obj.constructor();	//?
					for(i in obj) EG._clone(cObj,obj,i);
				}
			}
		// array
		}else if(2==typeNum) {
			cObj=[];
			for(i=0,il=obj.length;i<il;i++)	EG._clone(cObj,obj,i);

		// function
		}else if(3==typeNum) {
			//cObj=obj+'';
			//fn_args = cObj.substring(cObj.indexOf('(') + 1, cObj.indexOf(')')).replace(/\s/g, '');
			//fn_body = cObj.substring(cObj.indexOf('{') + 1, cObj.lastIndexOf('}'));
			//if ('[native code]' == fn_body) throw new Error('无法clone native方法 : ' + cObj);
			//if (0<fn_args.il) {
			//	fn_args = fn_args.split(',');
			//	fn_args[fn_args.il] = fn_body;
			//	cObj = Function.constructor.apply(win, fn_args);
			//} else
			//	cObj = new Function(fn_body);
			cObj=obj;
		// date
		} else if (4 == typeNum) {
			cObj = new Date();
			cObj.setTime(obj.getTime());

		}//else{}// number, boolean, string

		return cObj;
	};

	//记录代码路径
	EG._markLoadPath=false;

	/**************************************************************************
	 * 								EG.Repository 资源库
	 **************************************************************************/

	/**
	 * 资源库
	 */
	EG.Repository=function(container){
		if(!container){
			container={};
		}
		this.container=container;
	};

	EG.Repository.prototype={

		/**
		 * 存储
		 * @param {String} key 键值
		 * @param {Object} data 数值
		 */
		put:function(key,data){
			var ks=key.split("."),obj=this.container;
			for(var i= 0,il=ks.length;i<il;i++){
				if(i==(il-1)){
					obj[ks[i]]=data;
				}else{
					if(!obj[ks[i]]) obj[ks[i]]={};
					obj=obj[ks[i]];
				}
			}
		},

		/**
		 * 获取值
		 * @param {String} key 键值
		 * @return {*}
		 */
		get:function(key){
			var ks=key.split("."),obj=this.container;
			for(var i=0,il=ks.length;i<il;i++){
				if(!obj[ks[i]]) return null;
				obj=obj[ks[i]];
			}
			return obj;
		},

		/**
		 * 定义命名空间
		 * @param {String} namespace 命名空间
		 * @param {Object?} fn 对象
		 * @returns {Object}
		 */
		defineNamespace:function(namespace,fn){
			if(!fn) fn=EG._newConst();
			var ss=namespace.split(".");
			var p=this.container;
			for(var i=0;i<ss.length;i++){
				if(!p[ss[i]]){
					if(i<ss.length-1){
						p[ss[i]]=EG._newConst();
						p[ss[i]]._undefined=true;
					}else{
						p[ss[i]]=fn;
					}
				}
				p=p[ss[i]];
			}
			return p;
		}
	};

	//默认资源库
	EG._defaultRepository=new EG.Repository(_EG_NS_);

	/**
	 * 存储
	 * @param {String} key 键值
	 * @param {Object} data 数值
	 */
	EG.put=function(key,data){
		EG._defaultRepository.put(key,data);
	};

	/**
	 * 获取值
	 * @param {String} key 键值
	 * @return {*}
	 */
	EG.get=function(key){
		return EG._defaultRepository.get(key);
	};

	/**************************************************************************
	 * 								EG.Loader 加载器
	 **************************************************************************/

	/**
	 * 加载器
	 * 非常重要:EG的核心1，类定义的基础
	 * 记载器提供了加载、获取路径、获取类路径、查找是否已加载
	 */
	EG.Loader=function(cfg){
		if(!cfg){
			cfg={};
		}

		this.repository=cfg.repository||EG._defaultRepository;
	};

	EG.Loader.prototype={

		_isLoader:true,

		/**
		 * 加载配置
		 *
		 * @param cfg
		 * @param callback
		 * @param onError
		 * @returns {*}
		 */
		load:function(cfg,callback,onError){
			var me=this;
			cfg=this.convert(cfg);

			var key	=cfg["key"]||cfg["path"];
			var type=cfg["type"];
			var	path=cfg["path"];	//Class类型为空

			var r=this.find(key);
			if(r){
				return callback(r,cfg);
			}else{
				if(callback){
					me.onReady(cfg,function(c){
						callback(c,cfg);
					});
				}
			}

			//异步加载
			var url=this.getUrl(type,key,path);
			if(type=="class"){
				if(!EG.Browser.isLocal()){
					EG.Ajax.send({
						url:url,
						method:"GET",
						callback:function(text){
							if(EG._markLoadPath){
								text=("//@ sourceURL="+url+"\n"+text);
							}
							eval(text);	//执行define过程
						},
						erhandler:onError
					});
				}else{
					var el=EG.CE({tn:"script",type:"text/javascript",charset:"UTF-8",src:url});

					EG.getHead().appendChild(el);
				}
			}else if(type=="text"){
				EG.Ajax.send({
					url:url,
					method:"GET",
					callback:function(text){
						var obj={"path":url,item:text};
						me.repository.put(key,obj);
						me.doAfterReady(key,obj);
					},
					erhandler:onError
				});
			}else if(type=="ele"){
				EG.Ajax.send({
					url:url,
					method:"GET",
					callback:function(text){
						var ele=EG.CE({tn:"div"});
						ele.innerHTML=text;
						var obj={"path":url,item:ele.childNodes[0]};
						me.repository.put(key,obj);			//TODO 容器要自定义
						me.doAfterReady(key,obj);
					},
					erhandler:onError
				});
			}else if(type=="img"){
				var img=EG.CE({tn:"img",onload:function(event){
					var obj={"path":url,item:this};
					me.repository.put(key,obj);
					me.doAfterReady(key,obj);
				},onerror:function(){
					if(onError){
						onError.apply(this,arguments);
					}
				}});
				img.src=url;
			}
		},
		/**
		 * 根据类型获取URL
		 * @param type
		 * @param key
		 * @param path
		 * @returns {*}
		 */
		getUrl:function(type,key,path){
			if(type=="class"){
				return this.getClassUrl(key);
			}else return path;
		},
		/**
		 * 获取类URL
		 * @param className
		 * @returns {string}
		 */
		getClassUrl:function(className){
			var ss= className.split(".");
			ss.shift();
			var classFileName=ss[ss.length-1];
			ss.pop();
			return EG.basePath+"src"+"/"+ss.join("/").toLowerCase()+"/"+classFileName+".js";
		},
		/**
		 * 查看是否已存在
		 * @param key 键值
		 * @returns {*}
		 */
		find:function(key){
			if(typeof(key)!="string"){
				key=key["key"]||key["path"];
			}
			var d=this.repository.get(key);
			//TODO 把_defineUnfinish加入到浅定义中
			if(d==null) return null;
			return d;
		},
		/**
		 * 是否已加载
		 * @param cfg
		 * @returns {boolean}
		 */
		isLoaded:function(cfg){
			var d=this.find(cfg);
			if(d==null||d._defineUnfinish) return false;
			return true;
		},

		/**
		 * 转换配置
		 * @param cfg
		 * @returns {*}
		 */
		convert:function(cfg){
			if(typeof(cfg)=="string"){
				cfg={key:cfg,type:"class"};
			}
			return cfg;
		},

		/**
		 * 完毕事件队列
		 */
		readyEvents:{},

		/**
		 * 完毕事件
		 * 传入资源标识，判断资源是否已被加载，已加载时立即执行回调，否则加入完毕事件队列
		 * @param {*} cfg 资源标识
		 * @param {Function} callback
		 */
		onReady:function(cfg,callback){
			cfg=this.convert(cfg);
			var key=cfg["key"]||cfg["path"];
			var d=this.find(key);
			if(d){
				callback(d,cfg);
			}else{
				if(!this.readyEvents[key]){
					this.readyEvents[key]=[];
				}
				this.readyEvents[key].push(callback);
			}
		},

		/**
		 * ready后执行
		 * @param key
		 * @param obj
		 */
		doAfterReady:function(key,obj){
			if(this.readyEvents[key]){
				var ev;
				//保证多线程同时ready一个class时，用shift
				while((ev=this.readyEvents[key].shift())){
					ev(obj);
				}
			}
		},

		/**
		 * 加载
		 * 注意:reqs会被shift掉
		 * @param reqs
		 * @param onFinish
		 * @param onError
		 * @returns {*}
		 */
		require:function(reqs,onFinish,onError){
			var me=this;

			if(!reqs.reqEd){
				reqs.reqEd=[];
			}

			if(reqs.length==0){
				var rs=[];
				for(var i=0;i<reqs.reqEd.length;i++){
					var ed=reqs.reqEd[i];
					var r=this.find(ed);
					if(r==null) throw new Error("未找到："+ ed)
					rs.push(r);
				}
				return onFinish.apply(this,rs);
			}

			var req=reqs.shift();
			reqs.reqEd.push(req);

			var fn=function(){
				if(me.isLoaded(req)){
					return me.require(reqs,onFinish,onError);
				}else{
					return me.load(req,function(){
						return me.require(reqs,onFinish,onError);
					},onError);
				}
			};

			var clazz=this.find(req);
			if(clazz!=null&& clazz._defineUnfinish){
				this.onReady(req,fn);
			}else{
				fn();
			}
		}
	};

	/**
	 * 默认加载器
	 * @type {EG.Loader}
	 * @private
	 */
	EG._defaultLoader=new EG.Loader();

	/**
	 * EG OnReady事件
	 */
	EG.onReady=function(){
		EG._defaultLoader.onReady.apply(EG._defaultLoader,arguments);
	};

	/**
	 * 原生方法
	 */
	EG.nativeMethod={
		/**
		 * 初始化
		 * @param cfg
		 */
		initConfig:function(cfg){
			//如果this中含有xxConfig字样的成员,将cfg["xx"]赋值给xxConfig
			if(cfg){
				for(var key in cfg){
					var cK=key+"Config";
					if(typeof(this[cK])!="undefined"){
						this[cK] = cfg[key];
					}else{
						this[key] = cfg[key];
					}
				}
			}
		},

		/**
		 * 获取父类
		 */
		getSuperclass:function(){
			return this.getClass()._superClass;
		},

		/**
		 * 调用父类#不能被me调用
		 * 没有name时,调用构造函数
		 * 采用方法名标记进入的层次
		 * @param name 方法名
		 * @param args 参数
		 */
		callSuper:function(name,args){
			var o;

			//找到谁调用的callSuper,如果被me等闭包函数调用无法找到本层调用函数，如果被上层调用下层会乱
			var fn=arguments.callee.caller;

			//类名||或者所属类
			var sc=(fn._className?fn:fn._class)._superClass;

			if(!sc) throw new Error("没有父类");

			//分析方法名
			var methodName,params;
			if(arguments.length==0){
				methodName="$constructor";
				params=[];
			}else if(arguments.length==1){
				if(typeof(arguments[0])=="string"){
					methodName=arguments[0];
					params=[];
				}else{
					methodName="$constructor";
					params=arguments[0];
				}
			}else{
				methodName=arguments[0];
				params=arguments[1];
			}

			//传递执行
			if(methodName=="$constructor"){
				o=sc.apply(this,params);
			}else{
				o=sc.prototype[methodName].apply(this,params);
			}

			return o;
		},

		/**
		 * 获取到类
		 * @returns {Function}
		 */
		getClass:function(){
			return this._class;
		}
	};

	/**
	 * 类本体
	 * @returns {Function}
	 */
	EG._newConst=function(){

		return function(){
			//类本体
			var c=arguments.callee;
			var args=arguments;
			//初始化config中的固定变量到this中
			if(c._config){
				EG.copy(this,EG.clone(c._config));
			}

			//构造前执行
			if(c._constructor){
				if(c._beforeConstructor){
					var me=this;
					return c._beforeConstructor.apply(this,[function(){
						return c._constructor.apply(me,args);
					}]);
				}else{
					return c._constructor.apply(this,args);
				}
			}
		};
	};

	/**
	 * 别名
	 * @type {{}}
	 * @private
	 */
	EG._alias={};

	/**
	 * 定义类过程
	 * @param cfg
	 * @param name
	 * @param loader
	 * @returns {Function}
	 * @private
	 */
	EG._defineClass=function(cfg,clazz,name,loader){
		if(!cfg) cfg={};

		var statics		=cfg["statics"],
			config		=cfg["config"]||{},
			constructor	=cfg["constructor"],
			extend		=cfg["extend"],
			beforeConstructor=cfg["beforeConstructor"]
		;

		//保留预定义数据
		if(config){
			config=EG.clone(config);
		}

		//判断是否为Object的默认构造函数
		if({}.constructor==constructor) constructor=null;

		//挂载使callSuper可识别
		if(constructor) constructor._class=clazz;

		//关联重要参数
		clazz._constructor		=constructor;
		clazz._config			=config;
		clazz._beforeConstructor=beforeConstructor;
		if(name){
			clazz._className=name;
		}

		//父类
		if(extend){
			if(typeof(extend)=="string"){
				extend=loader.find(extend);
			}

			//继承核心
			var f=function(){};
			f.prototype=extend.prototype;
			clazz.prototype=new f();

			//原型链继承
			clazz._superClass=extend;

			//参数继承
			if(extend._config){
				EG.copy(config,EG.clone(extend._config),false);
			}
		}

		var prototyps	=clazz.prototype;

		//原生方法
		EG.copy(prototyps,EG.nativeMethod);
		prototyps._class=clazz;

		//配置方法
		for(var key in cfg){
			if(key!="extend"&&
				key!="alias"&&
				key!="statics"&&
				key!="config"&&
				key!="constructor"&&
				key!="beforeConstructor"&&
				key!="require"){
				//此处若是状态变量则有风险。其作用于原型链
				prototyps[key]=cfg[key];

				if(typeof(cfg[key])=="function"){
					prototyps[key]._class=clazz;
				}
			}
		}

		//copy类的静态方法
		if(statics) EG.copy(clazz,statics,true);

		if(extend){
			//继承后动作
			if(extend.afterExtend){
				extend.afterExtend(clazz);
			}
		}

		//清除未定义标记
		clazz._defineUnfinish=false;
		delete clazz["_defineUnfinish"];

		//别名
		if(cfg["alias"]){
			EG._alias[cfg["alias"]]=clazz;
		}


		//定义后执行
		if(name){
			loader.doAfterReady(name,clazz);
		}

		return clazz;
	};

	/**
	 * 定义类
	 *
	 * EG的Loader.require与命名空间关联（非类路径，类路径与Loader有关系），原因是Loader.onReady的触发时机
	 * 缺点:较AMD的define可以不与命名空间关联
	 * 优点:在一个文件里可以定义多个类;
	 *
	 * 默认使用{EG.Loader}加载类
	 *
	 * 参数模式:
	 * 1:name,require,cfg||function,loader?
	 * 2:require,cfg||function,loader?
	 * 3:cfg||function,loader?
	 *
	 *
	 *
	 * @param {String||Object} name 名称||cfg配置
	 * @param {Object?} cfg 配置
	 * @param {Function?} loader 类加载器
	 * @returns {Class}
	 */
	EG.define= function(){
		var name,reqs,cfg,loader;

		//参数
		for(var i=0;i<arguments.length;i++){
			var arg=arguments[i];
			var type=EG.getType(arg);
			if(!name)	{if(type=="string")						{name		=arg;continue;}}
			if(!reqs)	{if(type=="array")						{reqs		=arg;continue;}}
			if(!cfg)	{if(type=="object"||type=="function")	{cfg		=arg;continue;}}
			if(!loader)	{if(arg._isLoader)						{loader		=arg;continue;}}
		}
		loader		=loader||EG._defaultLoader;
		reqs		=reqs||[];

		//
		var clazz;
		if(name){
			clazz=loader.find(name);
			if(!clazz){
				clazz=loader.repository.defineNamespace(name,clazz);
			}else{
				if(clazz._undefined===false){
					throw new Error("类已定义:"+name);
				}
			}
		}else{
			clazz=EG._newConst();
		}

		clazz._defineUnfinish=true;

		if(typeof(cfg)=="function"){
			loader.require(reqs,function(){
				var args=[];
				for(var i=0;i<arguments.length;i++){
					args.push(arguments[i]);
				}
				args.push(clazz);
				cfg=cfg.apply(this,args);
				return EG._defineClass(cfg,clazz,name,loader);
			});
			return clazz;
			//
		}else{
			var extend	=cfg["extend"];
			var require	=cfg["require"];

			//继承类加入Require
			if(typeof(extend)=="string"){
				reqs.push(extend);
			}

			//require
			if(require){
				reqs=reqs.concat(require);
			}

			loader.require(reqs,function(){
				return EG._defineClass(cfg,clazz,name,loader);
			});

			return clazz;
		}
	};

	EG.isInstanceof=function(obj,pClazz){
		if(obj==null) return false;
		return EG.isAssignableFrom(obj.getClass(),pClazz);
	};

	EG.isAssignableFrom=function(clazz,pClazz){
		if(clazz==pClazz) return true;

		while(clazz._superClass){
			clazz=clazz._superClass;
			if(clazz==pClazz){
				return true;
			}
		}

		return false;
	};

	/**
	 * 寻找类
	 * @param {String} classNameExp 类名表达式
	 * @return {Object}
	 */
	EG.findClass=function(classNameExp){
		return EG.findClasses(classNameExp,false)[0];
	};

	/**
	 * 寻找类
	 * @param classNameExp
	 * @param returnObj 返回类型
	 * @param pClass
	 * @param classes
	 * @param packageName
	 * @return {*}
	 */
	EG.findClasses=function(classNameExp,returnObj,pClass,classes,packageName){
		classes=classes||((returnObj)?{}:[]);
		pClass=pClass||_EG_NS_;
		packageName=packageName||"";
		if(!classNameExp) return classes;
		var classEles=classNameExp.split(".");
		//alert(classNameExp);

		var classNamePattern=classEles.shift().replace("*",".*");
		for(var key in pClass){
			if((typeof(pClass[key])=="function"||typeof(pClass[key])=="object")&&new RegExp("^"+classNamePattern+"$","g").test(key)){
				var fullKey=packageName?(packageName+"."+key):key;
				if(classEles.length>0){
					EG.findClasses(classEles.join("."),returnObj,pClass[key],classes,fullKey);
				}else{
					if(returnObj){
						classes[fullKey]=(pClass[key]);
					}else{
						classes.push(pClass[key]);
					}
				}
			}
		}
		return classes;
	};

	/**
	 * 寻找方法,支持用.*来匹配
	 * @param {Class} clazz 类
	 * @param {String} methodNameExp 名称表达式
	 * @param {?String} rangeType 范围类型,prototype||static||all
	 * @param {?String} returnObj 返回类型
	 * @return {*}
	 */
	EG.findMethods=function(clazz,methodNameExp,rangeType,returnObj){
		var ms=((returnObj)?{}:[]);
		rangeType=rangeType||"all";
		methodNameExp=methodNameExp.replace("*",".*");

		var f=function(range){
			for(var key in range){
				if(typeof(range[key])=="function"&&new RegExp("^"+methodNameExp+"$","g").test(key)){
					if(returnObj){
						ms[key]=range[key];
					}else{
						ms.push(range[key]);
					}
				}
			}
		};
		if(rangeType=="all"||rangeType=="prototype") 	f(clazz.prototype);
		if(rangeType=="all"||rangeType=="static") 		f(clazz);
		return ms;
	};

	/**
	 * 是否为某类的子类
	 * @param {Object} obj 对象
	 * @param {Class} klass 父类
	 * @return {Boolean}
	 */
	EG.isAssignbleFrom=function(obj,klass){
		var sc=obj.getClass();
		while(sc=sc._superClass){
			if(sc==klass) return true;
		}
		return false;
	};

	/**
	 * 存储
	 * @param {String} key 键值
	 * @param {Object} data 数值
	 */
	EG.put=function(key,data){
		var ks=key.split("."),obj=_EG_NS_;
		for(var i= 0,il=ks.length;i<il;i++){
			if(i==(il-1)){
				obj[ks[i]]=data;
			}else{
				if(!obj[ks[i]]) obj[ks[i]]={};
				obj=obj[ks[i]];
			}
		}
	};

	/**
	 * 获取值
	 * @param {String} key 键值
	 * @return {*}
	 */
	EG.get=function(key){
		var ks=key.split("."),obj=_EG_NS_;
		for(var i=0,il=ks.length;i<il;i++){
			if(!obj[ks[i]]) return null;
			obj=obj[ks[i]];
		}
		return obj;
	};

	EG._importCache={

	};

	/**
	 * 获取Refs
	 * @returns {string}
	 */
	EG.importPath=function(keys,flag){

	};

	EG.importPath_static=function(){
		return EG.importPath(arguments,2);
	};

	EG.importPath_class=function(){
		return EG.importPath(arguments,1);
	};


	/**
	 * 显示源
	 * @param text
	 */
	EG.showSource=function(text){
		if(!EG.showSourceEle){
			EG.showSourceEle=EG.CE({tn:"div",style:"position:absolute;z-index:9999",cn:[
				{tn:"textarea",style:"width:500px;height:500px"},
				{tn:"button",innerHTML:"hide",onclick:function(){EG.hide(EG.showSourceEle)}}
			]});
			EG.getBody().appendChild(EG.showSourceEle);
		}

		EG.showSourceEle.childNodes[0].value=text;
		EG.show(EG.showSourceEle);
	}
})();

(function(){
	/**
	 * 字符串操作类
	 * @class EG.String
	 * <AUTHOR>
	 */
	EG.define("EG.String",function(ME) {
		return {
			statics:
			/**
			 * @lends EG.String
			 */
			{
				/**
				 * 是否为空字符串
				 * @param {String} str 字符串
				 */
				isString: function (str) {
					return typeof (str) === 'string';
				},
				/**
				 * NULL转换为空字符串
				 * @param {String} str 字符串
				 */
				n2e: function (str) {
					return str != null ? str : "";
				},
				/**
				 * 是否为空字符串
				 * @deprecated
				 * @param {String} str 字符串
				 * @returns {Boolean}
				 */
				isBlank: function (str) {
					return (str == null || str === "" || ME.trim(str) === "");
				},
				/**
				 * 是否为空字符串
				 * @param str
				 * @returns {boolean}
				 */
				isEmpty: function (str) {
					return (str == null || str === "" || ME.trim(str) === "");
				},
				/**
				 * 是否相等
				 * @param {String} str1 字符串1
				 * @param {String} str2 字符串2
				 * @param {Boolean} ignoreCase 是否忽略大小写
				 * @returns {Boolean}
				 */
				equals: function (str1, str2, ignoreCase) {
					return ignoreCase == true ?
					str1.toUpperCase() === str2.toUpperCase()
						:
					str1 === str2;
				},
				/**
				 * 是否以什么开始
				 *
				 * @param {String} str 源字符串
				 * @param {String} prefix 结尾字符串
				 * @param {Boolean?} ignoreCase 忽略大小写
				 * @returns {Boolean}
				 */
				startWith: function (str, prefix, ignoreCase) {

					if (str == null || prefix == null) return false;

					var l = prefix.length;
					if (str.length < l) return false;

					var i;
					if (ignoreCase) {
						for (i = 0; i < l; i++) {
							if (prefix.charAt(i).toLocaleLowerCase() != str.charAt(i).toLocaleLowerCase()) {
								return false;
							}
						}
					} else {
						for (i = 0; i < l; i++) {
							if (prefix.charAt(i) != str.charAt(i)) {
								return false;
							}
						}
					}

					return true;
				},
				/**
				 * 是否以什么结尾
				 *
				 * @param {String} str 源字符串
				 * @param {String} endStr 结尾字符串
				 * @param {Boolean?} ignoreCase 忽略大小写
				 * @returns {Boolean}
				 */
				endWith: function (str, endStr, ignoreCase) {
					return (
						str !== null
						&&
						endStr !== null
						&&
						(ignoreCase == true ?
							str.toUpperCase().lastIndexOf(endStr.toUpperCase()) > 0
								:
							str.lastIndexOf(endStr) >= (str.length - endStr.length)
						)
					);
				},
				/**
				 * 截两端空字符
				 *
				 * @param str 字符串
				 * @returns {String}
				 */
				trim: function (str) {
					return str.replace(/(^\s*)|(\s*$)/g, "");
				},
				/**
				 * 移除尾部字串
				 *
				 * @param {String} str 源字符串
				 * @param {String} endStr 结尾字符串
				 * @returns {String}
				 */
				removeEnd: function (str, endStr) {
					return (ME.endWith(str, endStr)) ? str.substring(0, str.length - endStr.length) : str;
				},
				/**
				 * 移除首部字串
				 *
				 * @param {String} str 源字符串
				 * @param {String} startStr 结尾字符串
				 * @returns {String}
				 */
				removeStart: function (str, startStr) {
					return (ME.startWith(str, startStr)) ? str.substring(startStr.length, str.length) : str;
				},
				/**
				 * 调换字符串
				 * @param str
				 * @param regex
				 * @param replacer 替换
				 */
				replaceAll: function (str, regex, replacer) {
					return str.replace(new RegExp(regex, "g"), replacer);
				}
			}
		}
	});
})();

(function(){
	/**
	 * 日期操作类
	 * @class EG.Date
	 * <AUTHOR>
	 * @variation 2
	 */
	EG.define("EG.Date",function(ME){

		var unit_second	=1*1000;
		var unit_min	=60*1000;
		var unit_hour	=60*unit_min;
		var unit_day	=24*unit_hour;
		var unit_week	=7*unit_day;
		var unit_month	=30*unit_day;
		var unit_year	=12*unit_month;

		return {
			statics:
			/**
			 * @lends EG.Date
			 */
			{
				
				/**
				 * 格式化时间
				 */
				format:function(date,fmt){
					
					if(arguments.length==1){
						fmt	=arguments[0];
						date=new Date();
					}
					if(typeof(date)=="number"){
						date=new Date(date);
					}else if(typeof(date)=="string"){
						return date;
					}
					
					//分解
					var cl		="";
					var ps		=[];
					for(var i=0;i<fmt.length;i++){
						var c		=fmt.charAt(i);
						if(cl.length>0&&cl.charAt(cl.length-1)!=c){
							ps.push(cl);
							cl=c;
						}else{
							cl+=c;
						}
					}
					ps.push(cl);
					
					//替换
					for(var i=0;i<ps.length;i++){
						var p=ps[i];
						var s=p;
						
						var fixZero=false;
						if(p=="yyyy"||p=="yy"){
							s=date.getFullYear();
							if(p=="yy"){
								s=s.substr(2);
							}
						}
						else if(p=="MM"||p=="M"){s=date.getMonth()+ 1;	fixZero=(p=="MM");}
						else if(p=="dd"||p=="d"){s=date.getDate();		fixZero=(p=="dd");}
						else if(p=="HH"||p=="H"){s=date.getHours();		fixZero=(p=="HH");}
						else if(p=="mm"||p=="m"){s=date.getMinutes();	fixZero=(p=="mm");}
						else if(p=="ss"||p=="s"){s=date.getSeconds();	fixZero=(p=="ss");}
						
						if(fixZero){
							if(s<10){
								s="0"+s;
							}
						}
						
						ps[i]=s;
					}
					return ps.join("");
				},
				
				/**
				 * 把日期转为字符串
				 * @param {Date?} date 日期
				 * @param {Boolean?} flag 标志
				 * @param {Boolean?} lang 是否使用汉字
				 * @return {String}
				 */
				d2s:function(date,flag,lang){
					if(!date) date=new Date();
					var year = date.getFullYear();
					var month= date.getMonth()+ 1;
					month	=month<10?("0"+month):month;
					var day= date.getDate();
					day 	= day<10?("0"+day):day;

					if(flag==null) flag=true;
					if(flag) {
						var hour=date.getHours();		hour	=hour<10?("0"+hour):hour;
						var minute=date.getMinutes();	minute	=minute<10?("0"+minute):minute;
						var second=date.getSeconds();	second	=second<10?("0"+second):second;
						if(lang) return(year+"年"+month+"月"+day+"日"+hour+" 小时:"+minute+"分"+second+"秒");
						else return(year+"-"+month+"-"+day+" " +hour+":"+minute+":"+second);
					}else{
						if(lang) return(year+"年"+month+"月"+day+"日");
						else return(year+"-"+month+"-"+day);
					}
				},
				/**
				 * 时间字符串转时间
				 * @param {String} str 字符串
				 * @return {Date} 时间
				 */
				s2d:function(str){
					//2012-08-19 11:22:33
					//1234567890123456789
					var d=new Date();
					if(str.length==4){
						return new Date(str,d.getMonth(),d.getDay(),d.getHours(),d.getMinutes(),d.getSeconds());
					}else if(str.length==7){
						return new Date(str.substring(0,4),str.substring(5,7)-1,d.getDay(),d.getHours(),d.getMinutes(),d.getSeconds());
					}else if(str.length==10){
						return new Date(str.substring(0,4),str.substring(5,7)-1,str.substring(8,10),d.getHours(),d.getMinutes(),d.getSeconds());
					}else if(str.length==13){
						return new Date(str.substring(0,4),str.substring(5,7)-1,str.substring(8,10),str.substring(11,13),d.getHours(),d.getMinutes(),d.getSeconds());
					}else if(str.length==16){
						return new Date(str.substring(0,4),str.substring(5,7)-1,str.substring(8,10),str.substring(11,13),str.substring(14,16),d.getSeconds());
					}else if(str.length==19){
						return new Date(str.substring(0,4),str.substring(5,7)-1,str.substring(8,10),str.substring(11,13),str.substring(14,16),str.substring(17,19));
					}else{
						alert("str:"+str)
						throw new Error("参数不完整");
					}
				},
				/**
				 * 比较两个时间
				 * @param {String|Date} d1 时间1
				 * @param {String|Date} d2 时间2
				 * @returns {Number} 时间差
				 */
				compare:function(d1,d2){
					if(typeof(d1)=="string") d1=EG.Date.s2d(d1);
					if(typeof(d2)=="string") d2=EG.Date.s2d(d2);
					return d1.getTime()-d2.getTime();
				},
				/**
				 * 数字转时间
				 * @param {Number} l 数值
				 * @return {Date} 时间
				 */
				l2d:function(l){
					var d=new Date();
					d.setTime(l);
					return d;
				},
				/**
				 * 数字转时间串
				 * @param {Number} l 数值
				 * @return {String} 时间
				 */
				l2s:function(l){
					return EG.Date.d2s(EG.Date.l2d(l));
				},

				/**
				 * 获取月份日个数
				 * @param date
				 * @returns {*}
				 */
				getMonthday:function(date){
					if(date==null) date=EG.clone(this.curDate);
					else if(typeof(date)=="string"){
						date=EG.Date.s2d(date);
						date=new Date(d.getFullYear(),d.getMonth(),0);//date.substring(0,7)+"-01"
					}

					var m=date.getMonth();
					var y=date.getFullYear();
					if(m==1) return(((y%4 == 0) && (y%100 != 0) || (y%400 == 0))? 29: 28);
					else return(ME.solarMonthday[m]);
				},
				solarMonthday:[31,28,31,30,31,30,31,31,30,31,30,31],

				/**
				 * 是同一天
				 * @param {Date} d1
				 * @param {Date} d2
				 */
				sameDay:function(d1,d2){
					return d1.getYear()	==d2.getYear()&&
						d1.getMonth()	==d2.getMonth()&&
						d1.getDate()	==d2.getDate()
					;
				},

				toSimpleFmt:function(dateStr){
					var ds=dateStr.split(":");
					var d0=parseInt(ds[0]);
					var d1=parseInt(ds[1]);
					if(d0>=13){
						return "下午"+(d0-12)+""+(d1>0?(":"+d1):"点");
					}else{
						return "上午"+(d0)+(d1>0?(":"+d1):"点");
					}
				},

				/**
				 * 转换为距离x时间
				 * @param date
				 * @returns {string}
				 */
				space:function(date,now){
					if(now==null) now=new Date();
					var c=EG.Date.compare(now,date);

					if(c<0) return "未来";

					var ks=[
						[unit_year	,"年"],
						[unit_month	,"月"],
						[unit_week	,"周"],
						[unit_day	,"日"],
						[unit_hour	,"年"],
						[unit_min	,"分"],
						[unit_second,"秒"]
					];

					//alert(c);

					var s="";
					for(var i=0;i<ks.length;i++){
						if(c>ks[i][0]){
							s=parseInt(c/ks[i][0])+ks[i][1];
							break;
						}
					}

					return s+"前";
				},

				unit_second	:unit_second,
					unit_min	:60*1000,
					unit_hour	:60*unit_min,
					unit_day	:24*unit_hour,
					unit_week	:7*unit_day,
					unit_month	:30*unit_day,
					unit_year	:12*unit_month
			}
		}
	});
})();

/**
 * @class EG.Object
 * <AUTHOR>
 * 对象操作类
 */
(function() {
	var ME=EG.define("EG.Object",{
		statics:{
			/**
			 * 解压一个数组对象变为对象
			 *
			 * [
			 * 	 {name:"bobby",age:11},
			 * 	 {name:"rose",age:12}
			 * ]
			 *
			 * ==>
			 *
			 * {
			 * 	bobb:11,
			 * 	rose:12
			 * }
			 *
			 * @param {Array} arr 源数组
			 * @param {String} kKey 键值Key
			 * @param {String} vKey 数值Key
			 * @return {Object}
			 */
			extract:function(arr,kKey,vKey){
				var o={};
				for(var i=0,il=arr.length;i<il;i++){
					o[arr[i][kKey]]=vKey!=null?arr[i][vKey]:arr[i];
				}
				return o;
			},
			/**
			 * 是否在某个组里
			 * @param {Object} obj 对象
			 * @param {Array} arr 数组
			 * @return {Boolean}
			 */
			$in:function(obj, arr) {
				if (arr == null) return false;
				for (var i=0,il=arr.length;i<il;i++){
					if (ME.equals(arr[i],obj)) return true;
				}
				return false;
			},

			/**
			 * 是否为对象字面量
			 * @param {Object} obj 对象
			 * @return {Boolean}
			 */
			isLit:function(obj) {
				return obj.constructor==Object;
			},

			/**
			 * 是否相等
			 * @param {Object} o1 对象1
			 * @param {Object} o2 对象2
			 * @return {Boolean}
			 */
			equals:function(o1,o2){
				if(o1==null&&o2==null) return true;
				var ot=EG.getType(o1);
				if(ot!=EG.getType(o2)) return false;
				switch(ot){
					case "string":
					case "function":
					case "boolean":
					case "number":{
						return (o1===o2);
					}
					case "object":{
						for(var k in o1){
							if(!ME.equals(o1[k],o2[k]))  return false;
						}
						return true;
					}
					case "array":{
						if(o1.length!=o2.length) return false;
						for(var i= 0,il=o1.length;i<il;i++){
							if(!ME.equals(o1[i],o2[i])) return false;
						}
						return true;
					}
					default:{
						throw new Error("EG.Object#equals:暂不支持类型"+ot);
					}
				}
			},

			/**
			 * 是否有键值
			 * @param obj
			 * @param keys
			 * @returns {boolean}
			 */
			hasKey:function(obj,keys){
				if(!EG.Array.isArray(keys)){
					keys=[keys];
				}

				for(var i=0;i<keys.length;i++){
					var has=false;
					for(var k in obj){
						if(keys[i]===k){
							has=true;
							break;
						}
					}

					if(!has) return false;
				}

				return true;

			},

			/**
			 * 删除键值
			 * @param obj
			 * @param keys
			 */
			delKey:function(obj,keys){
				if(!EG.Array.isArray(keys)){
					keys=[keys];
				}

				for(var i=0;i<keys.length;i++){
					var key=keys[i];
					if(key.indexOf(".")>=0){
						var wdks=[];
						for(var k in obj){
							if(EG.RegExp.match(k,key)){
								wdks.push(k);
							}
						}

						//
						for(var j=0;j<wdks.length;j++){
							delete obj[wdks[j]];
						}

					}else{
						delete obj[key];
					}

				}
			},

			/**
			 *
			 * @param str
			 * @returns {*}
			 */
			$eval:function(str){
				var o=null;
				if(str==null||str.trim()==""){
					return null;
				}
				eval("o="+str);
				return o;
			},

			/**
			 *
			 * @param src
			 * @param keys
			 * @returns {{}}
			 */
			fetchKeys:function(src,keys){
				var o={};
				for(var i=0;i<keys.length;i++){
					o[keys[i]]=src[keys[i]];
				}
				return o;
			}
		}
	});

	EG.$in	=ME.$in;
	EG.isLit=ME.isLit;
})();

(function(){
	/**
	 * 数组操作类
	 * @class EG.Array
	 * <AUTHOR>
	 */
	EG.define("EG.Array",function(ME){
		EG.copy(ME,
			/**
			 * @lends EG.Array
			 */
			{
			/**
			 * 是否为数组或伪数组对象
			 * @param {Object} o 对象
			 */
			isArray:function(o){
				return o
					&&typeof o==='object'
					&&typeof o.length==='number'
					&&typeof o.splice==='function'
					&&!(o.propertyIsEnumerable('length'));
			},
			/**
			 * 删除某个位置的元素
			 * @param {Array} arr 源数组
			 * @param {Number} n 位置
			 * @returns {Array} 新数组
			 */
			del:function(arr,n){
				if(n==null){throw new Error("EG.Array#del:待删除坐标不能为null");}
				arr.splice(n,1);
				return arr;
			},
			/**
			 * 删除某个元素
			 * @param {Array} arr 源数组
			 * @param {*} obj 待插入元素
			 */
			remove:function(arr,obj){
				for(var i=arr.length;i>=0;i--){
					if(obj===arr[i]) {
						ME.del(arr,i);
					}else if(obj instanceof Function){
						if(obj(arr[i])){
							ME.del(arr,i);
						}
					}
				}
				return arr;
			},
			/**
			 * 指定位置掺入元素
			 * @param {Array} arr 源数组
			 * @param {Number} n 位置
			 * @param {Object} obj 待插入元素
			 * @returns {Array}
			 */
			insert:function(arr,n,obj){
				arr.splice(n,0,obj);
				return arr;
			},
			/**
			 * 第一个元素
			 * @param {Array} arr 源数组
			 * @returns {Object}
			 */
			first:function(arr){
				return ME.get(arr,0);
			},
			/**
			 * 最后一个元素
			 * @param {Array} arr 源数组
			 * @returns {Object}
			 */
			last:function(arr){
				return ME.get(arr,arr.length-1);
			},
			/**
			 * 获取指定位置的元素
			 * 支持传first,last,支持用负数逆向查找元素
			 * @param {Array} arr 源数组
			 * @param {Number|String} n 位置
			 */
			get:function(arr,n){
				if(typeof(n)=="number"){
					if(arr.length==0) return null;
					if(n<0) n=arr.length+n;
					return arr[n];
				}else if(typeof(n)=="string"){
					if(n=="n") n="last";
					return ME[n](arr);
				}
				else throw new Error("EG.Array#get:参数错误"+n);
			},
			/**
			 * 是否有某个对象
			 * @param {Array} arr 源数组
			 * @param {*} obj 待插入元素
			 */
			has:function(arr,obj){
				if(!arr) return false;
				for(var i=0,l=arr.length;i<l;i++){if(obj===arr[i]) return true;}
				return false;
			},
			/**
			 * 每个对象迭代执行方法
			 * @param {Array} arr 源数组
			 * @param {Function} fn 遍历方法
			 */
			each:function(arr,fn){
				if(!arr||!fn) return null;
				var r=null;
				for(var i=0,l=arr.length;i<l;i++) {
					r=fn.apply(arr[i],[i,arr[i]]);
					if(r===false) break;
				}
			},
			/**
			 * 抽取
			 * @param {Array} arr 数组
			 * @param {String|Number} key 键值
			 * @returns {Array}
			 */
			extract:function(arr,key){
				var vs=[];
				for(var i=0,il=arr.length;i<il;i++){
					vs.push(arr[i][key]);
				}
				return vs;
			},

			/**
			 * 抽取成2维数组
			 * @param {Array} arr 源数组
			 * @param {Array} keys 键值数组
			 * @param {Array?} vs 栈
			 * @return {Array}
			 */
			extract2Arrays:function(arr,keys,vs){
				if(!vs) vs=[];
				for(var i=0,il=arr.length;i<il;i++){
					var v=[];
					for(var j=0;j<keys.length;j++){
						var k=keys[j];
						if(typeof(k)=="string"){
							v.push(arr[i][k]);
						}else{
							v.push(k.apply(this,[arr[i]]));
						}

					}
					vs.push(v);
				}
				return vs;
			},
			/**
			 * 抽取成为键值对
			 * @param {Array} arr 数组
			 * @param {String} key 键值
			 * @param {String?} keyVal 键值
			 * @return {Object}
			 */
			extract2Obj:function(arr,key,keyVal){
				var obj={};
				for(var i=0,il=arr.length;i<il;i++){
					obj[arr[i][key]]=keyVal!=null?arr[i][keyVal]:arr[i];
				}
				return obj;
			},

			/**
			 * 清空数组
			 * @param {Array} arr 数组
			 * @return {Array} 原数组
			 */
			clear:function(arr){
				arr.splice(0,arr.length);
				return arr;
			},
			/**
			 * 截取数组
			 * @param arr
			 * @param start
			 * @param idx
			 * @returns {Array}
			 */
			sub:function(arr,start,idx){
				var nA=[];
				for(var i=start;i<idx;i++){
					nA.push(arr[i]);
				}
				return nA;
			},
			/**
			 * 添加数组
			 * @param intoArr
			 * @param fromA
			 * @returns {Array}
			 */
			addAll:function(intoArr,fromA){
				Array.prototype.push.apply(intoArr,fromA);	//聪明的使用
				return intoArr;
			},
			/**
			 * 获取元素位于数组中的位置
			 * @param arr
			 * @param obj
			 */
			getIdx:function(arr,obj){
				for(var i=0,l=arr.length;i<l;i++){
					if(obj===arr[i]) return i;
				}
				return -1;
			},
			/**
			 * 获取数组中匹配ID的元素
			 * @param {Array} arr
			 * @param {String} idKey
			 * @param {Object} id
			 * @param {String?} valKey
			 */
			fetch:function(arr,idKey,id,valKey){
				var isArray=EG.isArray(id);
				var vs=[];
				for(var i=0;i<arr.length;i++){
					if(isArray){
						for(var j=0;j<id.length;j++){
							if(arr[i][idKey]==id[j]){
								vs.push(arr[i]);
							}
						}
					}else{
						if(arr[i][idKey]==id){
							return valKey!=null?arr[i][valKey]:arr[i];
						}
					}
				}
				return isArray?vs:null;
			}
		});

		//
		EG.each		=ME.each;
		EG.isArray	=ME.isArray;
	});
})();

/**
 * @class EG.RegExp
 * <AUTHOR>
 * 正则式操作类
 */
(function(){
	EG.define("EG.RegExp",{
		statics:{
			/**
			 * 是否匹配
			 * @param {String} str 字符串
			 * @param {String} regex 正则式
			 * @return {Boolean}
			 */
			match:function(str,regex){
				return new RegExp("^"+regex+"$").test(str);
			}
		}
	});
})();
/**
 * @class EG.Ajax
 * <AUTHOR>
 * Ajax操作类
 */
(function(){
	var ME=EG.define("EG.Ajax", {
		statics:{
			
			JSONP_callbackers:{},
			
			/**
			 * 获取XMLHttpRequest对象
			 * @static
			 * @returns {XMLHttpRequest}
			 */
			getXMLHttpRequest:function () {
				var req = null;
				if (window.XMLHttpRequest) {
					req = new XMLHttpRequest();
				} else if (window.ActiveXObject) {
					try {
						req = new ActiveXObject("Msxml2.XMLHTTP");
					} catch (e1) {
						try {
							req = new ActiveXObject("Microsoft.XMLHTTP");
						} catch (e2) {
							req = null;
						}
					}
				}
				return req;
			},
			/**
			 * URL编码
			 * @static
			 * @param {String} str 字符串
			 * @returns {String}
			 */
			javaURLEncoding:function (str) {
				return window.encodeURIComponent(str).replace(/!/g, "%21").replace(/\"/g, "%22").replace(/'/g, "%27").replace(/\(/g, "%28").replace(/\)/g, "%29").replace(/~/g, "%7E");
			},


			handleJSONP:function(url,cfg){
				var jsonp_id=new Date().getTime()+"_"+Math.random()*1000;
				if(ME.tag_JSONP){
					EG.DOM.remove(ME.tag_JSONP);
				}
				ME.tag_JSONP=EG.CE({pn:EG.getBody(),tn:"script",type:"text/javascript"});

				//
				ME.JSONP_callbackers[jsonp_id]=function(){
					var ncfg=EG.copy(cfg);
					delete ncfg["url_jsonp"];
					ME.send.apply(this,[ncfg]);
				};

				ME.tag_JSONP.src=url+"EG.Ajax.sendJSONPBack(\""+jsonp_id+"\")";
				return;
			},

			/**
			 * 发送HTTP请求
			 * <code>
			 * 	EG.Ajax.send({
			 * 	    url:"/mmvc/jsrpc/call",
			 * 	    charset:"utf-8",
			 * 	    httpPost:true,
			 * 	    content:"a=1&b=2",
			 * 	    callback:function(responseText,req){
			 *				alert(responseText);
			 * 	    },
			 * 	    erhandler:function(erhandler){
			 *				alert("Error");
			 * 	    }
			 * 	});
			 * </code>
			 * @static
			 * @param {Object} cfg 配置
			 */
			send:function (cfg) {
				if (cfg == null)	throw new Error("EG.Ajax.call#参数不能为空");

				//JSONP
				var url_jsonp 	=cfg.url_jsonp;
				if(url_jsonp){
					ME.handleJSONP(url_jsonp,cfg);
					return;
				}

				var url 		=cfg["url"],							//URL
					charset 	=cfg["charset"]||"UTF-8",				//字符集
					req 		=EG.Ajax.getXMLHttpRequest(),			//创建新的XMLHTTPRequest对象
					method 		=EG.unnull(cfg["method"],"GET"),		//method
					async 		=EG.unnull(cfg["async"],true),			//异步传输
					callback 	=EG.unnull(cfg["callback"],""),			//回调
					erhandler 	=cfg["erhandler"],						//错误处理器 TODO 改为onError
					content 	=EG.unnull(cfg["content"],""),			//内容
					timeout		=cfg.timeout||30*1000 ,					//超时
					contentType =EG.unnull(cfg.contentType,"application/x-www-form-urlencoded"),
					xhrFields	=cfg["xhrFields"]

					;

				if (async) {
					req.onreadystatechange=function(){
						if (req.readyState==4){
							if(req.status==200){
								if(callback) return callback(req.responseText, req);
							}else{
								if(erhandler) return erhandler(req.status, req);
								else throw new Error("EG.Ajax#send:" + req.status + ": " + req.statusText+",\n["+method+"]"+url);
							}
						}
						return null;
					};
				}

				if(!EG.Browser.isIE()){
					req.timeout = timeout;
				}

				req.open(method,url,async);
				if(xhrFields){
					for(var k in xhrFields){
						req[k]=xhrFields[k];
					}
				}
				if(contentType) req.setRequestHeader("Content-Type", contentType);//BUGFIX:Tomcat 如果不设定,Sevlet无法获取参数
				req.send(content);

				//同步返回值
				if (!async&&callback) return callback(req.responseText, req);

				return;
			},

			sendJSONPBack:function(id){
				var fn=ME.JSONP_callbackers[id];
				if(fn){
					delete ME.JSONP_callbackers[id];
					fn();
				}
			}
		}
	});

	/**
	 * 共用的Request对象
	 * @property {XMLHttpRequest}
	 */
	 ME.globeRequest= ME.getXMLHttpRequest();
})();

/**
 * @class EG.Browser
 * <AUTHOR>
 * 浏览器对象
 */
(function(){
	EG.define("EG.Browser",[],function(ME){

		ME.Sys={};

		var app 	= navigator.appVersion;
		var u		= navigator.userAgent;
		var ua 		= navigator.userAgent.toLowerCase();
		var s;
		(s = ua.match(/msie ([\d.]+)/))?ME.Sys.ie=s[1]:
			(s = ua.match(/firefox\/([\d.]+)/))?ME.Sys.firefox=s[1] :
				(s = ua.match(/chrome\/([\d.]+)/))?ME.Sys.chrome=s[1] :
					(s = ua.match(/opera.([\d.]+)/))?ME.Sys.opera=s[1] :
						(s = ua.match(/version\/([\d.]+).*safari/))?ME.Sys.safari=s[1]:0;

		var isLocal	=  !window.location.domain;

		//
		ME.Version={																	//移动终端浏览器版本信息
			trident	: u.indexOf('Trident') 		> -1, 										//IE内核
			presto	: u.indexOf('Presto') 		> -1, 										//opera内核
			webkit	: u.indexOf('AppleWebKit') 	> -1, 										//苹果、谷歌内核
			gecko	: u.indexOf('Gecko') 		> -1 && u.indexOf('KHTML') 	== -1, 			//火狐内核
			mobile	: !!u.match(/.*Mobile.*/), 												//是否为移动终端
			ios		: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), 							//ios终端
			android	: u.indexOf('Android') 		> -1 || u.indexOf('Linux') 	> -1, 			//android终端或者uc浏览器
			iphone	: u.indexOf('iPhone') 		> -1 || u.indexOf('Mac') 	> -1, 			//是否为iPhone或者QQHD浏览器
			ipad	: u.indexOf('iPad') 		> -1, 										//是否iPad
			webApp	: u.indexOf('Safari') 		== -1 										//是否web应该程序，没有头部与底部
		};

		return {
			statics:{
				/**
				 * 是否为IE
				 * @returns {Boolean}
				 */
				isIE:function(){return (!!window.ActiveXObject || "ActiveXObject" in window);},
				/**
				 * 是否为IE6
				 * @returns {Boolean}
				 */
				isIE6:function(){return ME.isIE()&&ME.getIEVersion()==6;},
				/**
				 * 是否为IE7
				 * @returns {Boolean}
				 */
				isIE7:function(){return ME.isIE().tooUp&&ME.getIEVersion()==7;},
				/**
				 * 是否为IE8
				 * @returns {Boolean}
				 */
				isIE8:function(){return ME.isIE()&&ME.getIEVersion()==8;},

				/**
				 * 获取IE主版本
				 * @return {Number}
				 */
				getIEVersion:function(){
					if(ME.Sys.ie){
						return parseInt(ME.Sys.ie.substr(0,ME.Sys.ie.indexOf(".")));
					}else{
						return -1;
					}

				},

				/**
				 * 是否为IE8之前
				 * @return {Boolean}
				 */
				isIE8Before:function(){
					return ME.isIE()&&ME.getVersion()<=8;
				},

				/**
				 * 是否为Firefox
				 * @returns {Boolean}
				 */
				isFirefox:function(){return ME.Sys.firefox!=null;},
				/**
				 * 是否为 Chrome
				 * @returns {Boolean}
				 */
				isChrome:function(){return ME.Sys.chrome!=null;},
				/**
				 * 是否为Safari
				 * @returns {Boolean}
				 */
				isSafari:function(){return ME.Sys.safari!=null;},
				/**
				 * 是否为Opera
				 * @returns {Boolean}
				 */
				isOpera:function(){return ME.Sys.opera!=null;},
				/**
				 * 浏览器版本
				 * @returns {String}
				 */
				getVersion:function(){
					return ME.Sys.ie||ME.Sys.firefox||ME.Sys.chrome||ME.Sys.safari||ME.Sys.opera;
				},
				/**
				 * 获取域根地址
				 * @returns {String}
				 */
				getDomainAddress:function(){
					return window.location.href.substr(0,window.location.href.indexOf("/",10));
				},

				/**
				 * 是否为本地
				 */
				isLocal:function(){
					return isLocal;
				}
			}
		};
	});



})();
/**
 * @class EG.Tools
 * <AUTHOR>
 * 工具操作类
 */
(function(){
	/**
	 * 工具类
	 */
	var ME=EG.define("EG.Tools",{
		statics:{

			clear:function(key){
				if (window.localStorage) {
					localStorage.removeItem(key);
				}
			},

			/**
			 * 保存
			 */
			save:function(key,value){
				var s=ME.toJSON(value);
				if (window.localStorage) {
					localStorage.setItem(key,s);
				}
			},

			/**
			 * 查询
			 * @param key
			 * @returns {*}
			 */
			query:function(key){
				if (!window.localStorage) {
					return null;
				}

				var s=localStorage.getItem(key);
				return EG.Object.$eval(s);
			},


			/**
			 * POST方式跳转
			 */
			post2:function(url, params){
				var f = EG.CE({pn:EG.getBody(),tn:"form",action:url,method:"post",style:"display:none"});
				for (var x in params) {
					EG.CE({pn:f,tn:"textarea",name:x,value:params[x]});
				}
				f.submit();
				return f;
			},

			/**
			 * 过滤特殊字符
			 * @param str
			 */
			filterSpecChar:function(str){
				return EG.String.replaceAll(EG.String.replaceAll(str,"<","&lt;"),">","&gt;");
			},

			/**
			 *
			 * @returns {Array}
			 */
			getDPI:function() {
				var arrDPI ;
				if (window.screen.deviceXDPI != undefined) {
					arrDPI	=[window.screen.deviceXDPI,window.screen.deviceYDPI]
				}else {
					var tmpNode = EG.CE({tn:"div",style:"width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden"});
					document.body.appendChild(tmpNode);
					arrDPI = [parseInt(tmpNode.offsetWidth),parseInt(tmpNode.offsetHeight)];
					tmpNode.parentNode.removeChild(tmpNode);
				}
				return arrDPI;
			},

			/**
			 * 获取每英寸像素
			 * @returns {Array}
			 */
			getPerInchPix:function(){
				if(!ME.gDPIRate){
					var tmpNode = EG.CE({tn:"div",style:"width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden"});
					document.body.appendChild(tmpNode);
					ME.gDPIRate = [parseInt(tmpNode.offsetWidth),parseInt(tmpNode.offsetHeight)];
					tmpNode.parentNode.removeChild(tmpNode);
				}
				return ME.gDPIRate;
			},

			/**
			 * 是否按住左键
			 * @param e
			 */
			isPressLeft:function(e){
				e=EG.Event.getEvent(e);
				if(e.which!=null){
					return e.which==1;
				}else{
					return e.button==1;
				}
			},

			/**
			 * 是否按住右键
			 * @param e
			 */
			isPressRight:function(e){
				e=EG.Event.getEvent(e);
				if(e.which!=null){
					return e.which==3;
				}else{
					return e.button==2;
				}
			},

			/**
			 * 是否按住Ctrl键
			 * @param e
			 * @returns {boolean}
			 */
			isPressCtrl:function(e){
				e=EG.Event.getEvent(e);
				return e.ctrlKey||e.keyCode==17;
			},

			/**
			 * 获取路径参数
			 * @param {String} name 参数名称
			 * @returns {String}
			 */
			getParam:function(name){
				var params = EG.doc.location.search;
				params = params.substring(1);
				if(!params) return "";
				var paramArr = params.split("&");
				for(var i=0,il=paramArr.length; i<il; i++) {
					if(paramArr[i].indexOf(name) != -1) {return paramArr[i].substring(paramArr[i].indexOf("=") + 1);}
				}
				return null;
			},

			/**
			 * 获取所有参数
			 * @returns {Object}
			 */
			getParams:function(){
				var params={};
				var strParams = EG.doc.location.search;
				strParams = strParams.substring(1);
				if(!strParams) return {};
				var paramArr = strParams.split("&");
				for(var i=0,il=paramArr.length; i<il; i++) {
					params[paramArr[i].substring(0,paramArr[i].indexOf("="))]=paramArr[i].substring(paramArr[i].indexOf("=") + 1);
				}
				return params;
			},

			/**
			 * 转换为JSON字符串
			 * @param {*} obj 对象
			 * @param {Array?} as 对象
			 * @returns {String}
			 */
			toJSON:function(obj,as){
				if(as==null){
					as=[];
				}
				if(obj==null) return "null";
				if(typeof(obj)== "object"){
					as.push(obj);
					var json = [];
					if(obj instanceof Array){
						for (var i=0; i < obj.length; i++) {
							var o=obj[i];
							//判断递归引用
//							if(typeof(o)=="object"&&EG.Array.has(as,o)){
//								continue;
//							}
							json[i] = (o !== null) ? ME.toJSON(o,as): "null";
						}
						return "[" + json.join(", ") + "]";
					}else{
						for (var key in obj) {
							if (!obj.hasOwnProperty(key)) continue;

							var o=obj[key];
							//判断递归引用
							if(typeof(o)=="object"&&EG.Array.has(as,o)){
								continue;
							}
							json.push( "\""+ key + "\" : " + ((o != null) ? ME.toJSON(o,as) : "null"));
						}
						return "{\n " + json.join(",\n ") + "\n}";
					}
				}else if(typeof(obj)== "string"){
					return "\""+obj.replace(/\\/g,"\\\\").replace(/"/g,"\\\"")+"\"";
				}else if(typeof(obj)== "boolean"){
					return obj;
				}else if(typeof(obj)== "function"){
					return obj;
				}else if(typeof(obj)== "number"){
					return obj;
				}else if(typeof(obj)== "regexp"){
					return obj;
				}else throw new Error("Engin#toJSON:不支持类型:"+typeof(obj));
			},

			/**
			 * 转换成XMLDoc
			 * @param str
			 * @returns {*}
			 */
			toXMLDoc:function(text){
				var xmlDoc;
				try{
					xmlDoc=new ActiveXObject("Microsoft.XMLDOM");
					xmlDoc.async="false";
					xmlDoc.loadXML(text);
				}catch(e){
					try{
						parser=new DOMParser();
						xmlDoc=parser.parseFromString(text,"text/xml");
					}catch(e) {alert(e.message)}
				}
				return xmlDoc;
			},

			/**
			 * 获取脚本路径
			 */
			getScriptPath:EG.getScriptPath,

			/**
			 * 计算精确鼠标位置
			 * @param {Event} evt 事件
			 * @param {HTMLElement?} refer 参照物
			 */
			getMousePos:function(evt,refer){
				var mp;
				evt=EG.Event.getEvent(evt);
				if(evt.pageX || evt.pageY){
					mp={x:evt.pageX, y:evt.pageY};
				}else{
					mp={
						x:evt.clientX + EG.getBody().scrollLeft - EG.getBody().clientLeft,
						y:evt.clientY + EG.getBody().scrollTop  - EG.getBody().clientTop
					};
				}

				if(refer){
					var rp=ME.getElementPos(refer);
					mp={
						x:mp.x-rp.x,
						y:mp.y-rp.y
					};
				}

				return mp;
			},

			/**
			 * 获取元素相对屏幕的位置
			 * @param {HTMLElement} ele 元素
			 * @param {HTMLElement?} parent 元素
			 * @param {Boolean?} isOffsetParent 是否为offset父元素
			 */
			getElementPos:function(ele,parent,isOffsetParent){
				if(isOffsetParent==null) isOffsetParent=true;
				if(!parent) parent=EG.getBody();
				var t = ele.offsetTop;
				var l = ele.offsetLeft;
				while ((ele = isOffsetParent?ele.offsetParent:ele.parentNode)!=parent&&ele!=null){//offsetParent!=parentNode
					t += ele.offsetTop;
					l += ele.offsetLeft;
				}
				return {x:l,y:t};
			},
//			getElementPos:function(ele,parent){
//				if(parent!=null){
//					var pe=ME.getElementPos(parent);
//					var e=ME.getElementPos(ele);
//					return {x:e.x-pe.x,y:e.y-pe.y};
//				}else{
//					var t = ele.offsetTop,l = ele.offsetLeft;
//					while(ele!= null){
//						t += ele.offsetTop;
//						l += ele.offsetLeft;
//						alert(ele.tagName);
//						ele = ele.offsetParent;
//					}
//					return {x:t,y:l};
//				}
//			},



			/**
			 * 调试对象
			 */
			debugObject:function(obj){
				var s="";
				for(var key in obj){
					s+=key+",";
				}
				alert(s);
			},

			/**
			 * AOP方法
			 * @param expression
			 * @param before
			 * @param after
			 */
			aop:function(expression,before,after){
				if(!before&&!after) return;

				var mType=-1;
				if(mType<0) mType=expression.indexOf("###")>0?2:-1;
				if(mType<0) mType=expression.indexOf("##")>0?1:-1;
				if(mType<0) mType=expression.indexOf("#")>0?0:-1;
				if(mType<0) throw new Error("EG#aop:表达式错误>"+expression);

				var classNameExp=expression.substr(0,expression.indexOf("#"));
				var methodNameExp=expression.substr(expression.lastIndexOf("#")+1);

				//寻找类
				var classes=EG.findClasses(classNameExp,true);
				var cms={};
				for(var className in classes){
					(function(){
						//寻找类方法
						var clazz=classes[className];
						var f=function(num,tp){
							var ms=EG.findMethods(clazz,methodNameExp,tp,"all");
							for(var methodName in ms){
								//alert(className+","+num+","+methodName);
								cms[className+","+num+","+methodName]=ms[methodName];
							}
						};

						if(mType==2||mType==1){
							f(1,"static");
						}
						if(mType==2||mType==0){
							f(0,"prototype");
						}
					})();
				}

				//植入前置和后置
				for(var cmName in cms){

					(function(){//循环时避免重叠

						var method		=cms[cmName];
	//				alert(cmName);
						var ks			=cmName.split(",");
						var className	=ks[0];
						var isProptype	=(ks[1]=="0");
						var methodName	=ks[2];
						var clazz		=classes[className];

						//过滤掉自身
						//if(className=="EG"&&methodName=="aop") continue;


						if(isProptype){
							clazz.prototype[methodName]=function(){
								if(before) before.apply(this,[clazz,className,method,methodName,isProptype]);
								var r=method.apply(this,arguments);
								if(after) after.apply(this,[clazz,className,method,methodName,isProptype]);
								return r;
							};
						}else{
							clazz[methodName]=function(){
								if(before) before(clazz,className,method,methodName,isProptype);

								//动态触发new
								var s="";
								for(var i=0,il=arguments.length;i<il;i++){
									if(s!=0) s+=",";
									s+=("arguments["+i+"]");
								}
								var r=null;
								var p="r=new method("+s+");";
								eval(p);

								if(after) after(clazz,className,method,methodName,isProptype);
								return r;
							};
						}
					})();
				}
			},

			/**
			 * 获取文本
			 * @param textvalues
			 * @param value
			 */
			getText:function(textvalues,value){
				for(var i=0;i<textvalues.length;i++){
					if(textvalues[i][1]==value) return textvalues[i][0];
				}
				return null;
			},

			/**
			 * 转换为省略词
			 * @param txts
			 * @param count
			 * @param endChar
			 * @returns {string}
			 */
			getEllipsis:function(txts,count,endChar){
				var t="";
				if(!endChar) endChar="";

				if(txts.length>count){
					t=EG.Array.sub(txts,0,count).join(",");
					t+="...("+(txts.length)+endChar+")";
				}else{
					t=txts.join(",");
				}
				return t;
			},

			/**
			 * 将以base64的图片url数据转换为Blob
			 * @param urlData
			 *            用url方式表示的base64图片数据
			 */
			convertBase64UrlToBlob:function(urlData) {
				var bytes = window.atob(urlData.split(',')[1]);        //去掉url的头，并转换为byte
				//处理异常,将ascii码小于0的转换为大于0
				var ab = new ArrayBuffer(bytes.length);
				var ia = new Uint8Array(ab);
				for (var i = 0; i < bytes.length; i++) {
					ia[i] = bytes.charCodeAt(i);
				}
				return new Blob([ab], {type: 'image/png'});
			},

			/**
			 * 累计调用
			 * @param expression
			 */
			watch:function(expression){
				ME.aop(expression,ME.watch_f,null);
			},
			watch_f:function(clazz,className,method,methodName,isProptype){
				//监测
				if(!window.watchCount){

					window.watchBox=EG.doc.createElement("div");
					var ss="position:absolute;z-index:9999;left:0px;top:0px;border:1px solid red;font-size:12px;max-height:500px;overflow:auto;background-color:white".split(";");
					for(var i= 0,il=ss.length;i<il;i++){
						var sss=ss[i].split(":");
						watchBox.style[sss[0]]=sss[1];
					}
					EG.getBody().appendChild(watchBox);
					window.watchCount={};
					window.watchBoxs={};
				}
				var key=className+(isProptype?"#":"##")+methodName;

				if(!watchCount[key]){
					watchCount[key]=0;
					watchBoxs[key]=EG.doc.createElement("div");
					watchBox.appendChild(watchBoxs[key]);
				}

				watchCount[key]++;
				watchBoxs[key].innerHTML=(key+":"+watchCount[key]);
			}
		}
	});

	EG.toJSON=ME.toJSON;
})();

/**
 * @class EG.Tpl
 * <AUTHOR>
 * 模板操作类
 */
(function(){
	EG.define("EG.Tpl",{
		/**
		 * 构造函数
		 * @param content
		 */
		constructor:function(content){
			this.setContent(content);
		},



		/**
		 * 设定内容
		 * @param content
		 */
		setContent:function(content){
			this.contentSrc	=content;
			this.content	=EG.String.replaceAll(this.contentSrc,"\\{([^}]+)\\}","'+EG.Tpl.fill(data,'$1')+'");
		},

		/**
		 * 匹配
		 * @param data
		 * @returns {*}
		 */
		match:function(data){
			var s;
			eval("s='"+this.content+"'");
			return s;
		},

		statics:{
			/**
			 * 过滤
			 * @param data
			 * @param key
			 * @returns {*}
			 */
			fill:function(data,key){
				return data[key];
			}
		}
	});
})();


/**
 * @class EG.Validate
 * <AUTHOR>
 * 校验操作类
 */
(function(){
	EG.define("EG.Validate",{
		statics:{
			/**
			 * 常用正则
			 */
			common_regex:{
				/** 邮箱 */
				email		:"([a-zA-Z0-9_\\.\\-])+\\@(([a-zA-Z0-9])+\\.)+([a-zA-Z0-9]{2,10})+",
				/** 手机号:13X,158,159号段 */
				phone		:"(1\\d{10})",
				/** QQ号 */
				qq			:"[1-9]\\d*",
				/** 电话号:国家代码-区域号码-电话号-分机号*/
				tel			:"(([0+]\\d{2,3}-)?(0\\d{2,3})-)(\\d{7,8})(-(\\d{3,}))?",
				/** 身份证号 */
				idcardNo	:"\\d{15}(\\d{3})?",
				/** 密码 */
				password	:"[0-9a-zA-Z]*",
				/** 字母数字 */
				wordnum		:"[0-9a-zA-Z_]*"
			},

			common_comment:{
				/** 邮箱 */
				email		:"xxx@域名",
				/** 开头的11位数字 */
				phone		:"1开头的11位数字",
				/** QQ号 */
				qq			:"非0开头的纯数字",
				/** 电话号:国家代码-区域号码-电话号-分机号*/
				tel			:"国家(非必填)-区域(非必填)-电话-分机(非必填)",
				/** 身份证号 */
				idcardNo	:"15或18位数字",
				/** 密码 */
				password	:"字母(含大小写)和数字组合形式",
				/** 字母数字 */
				wordnum		:"字母(含大小写)和数字组合形式"
			},

			/**
			 * 获取电话正则
			 * @param {String} split 分隔符
			 * @return {String}
			 */
			getTelRegex:function(split){
				if(split==null) split="-?";
				return "(([0+]\\d{2,3}"+split+")?(0\\d{2,3})"+split+")(\\d{7,8})("+split+"(\\d{3,}))?";
			},

			/**
			 * 是否为字母数字
			 * @param {String} str 字符串
			 * @return {Boolean}
			 */
			isWordnum:function(str){
				return EG.RegExp.match(str,EG.Validate.common_regex.wordnum)?"":ME.getComment("wordnum");
			},

			/**
			 * 是否为邮箱
			 * @param {String} str 字符串
			 * @return {Boolean}
			 */
			isEmail:function(str){
				return EG.RegExp.match(str,EG.Validate.common_regex.email)?"":ME.getComment("email");
			},

			/**
			 * 是否为密码
			 * @param {String} str 字符串
			 * @return {Boolean}
			 */
			isPassword:function(str){
				return EG.RegExp.match(str,EG.Validate.common_regex.password)?"":ME.getComment("password");
			},

			/**
			 * 是否为手机号
			 * @param {String} str 字符串
			 * @return {Boolean}
			 */
			isPhone:function(str){
				return EG.RegExp.match(str,EG.Validate.common_regex.phone)?"":ME.getComment("phone");
			},

			/**
			 * 是否为手机号
			 * @param {String} str 字符串
			 * @return {Boolean}
			 */
			isQq:function(str){
				return EG.RegExp.match(str,EG.Validate.common_regex.qq)?"":ME.getComment("qq");
			},

			/**
			 * 是否为电话号
			 * @param {String} str 字符串
			 * @param {String?} split 分隔符
			 * @return {Boolean}
			 */
			isTel:function(str,split){
				return EG.RegExp.match(str,EG.Validate.getTelRegex(split))?"":ME.getComment("tel");
			},

			/**
			 * 是否为身份证号
			 * @param {String} str 字符串
			 * @return {Boolean}
			 */
			isIdcardNo:function(str){
				var len=str.length;
				return EG.RegExp.match(str,EG.Validate.common_regex.idcardNo)?"":ME.getComment("idcardNo");
			},

			/**
			 * 判断是否为type类型数据
			 * @param {String} type 类型
			 * @param {String} str 字符串
			 * @return {Boolean}
			 */
			$is:function(type,str){
				var m="is"+EG.Word.first2Uppercase(type);
				var f=EG.Validate[m];
				if(!f) throw new Error("EG.Validate:不支持函数"+m);
				return f(str);
			},

			/**
			 * 获取正则格式说明
			 * @param {String} type 类型
			 * @return {String}
			 */
			getComment:function(type){
				return EG.Validate.common_comment[type];
			}
		}
	});

	var ME=EG.Validate;
})();
/**
 * @class EG.Word
 * <AUTHOR>
 * 单词操作类
 */
(function(){
	EG.define("EG.Word",{
		statics:{
			/**
			 * 首字母变大写
			 * @param {String} str 字符串
			 * @returns {String}
			 */
			first2Uppercase:function(str){
				return str.substring(0,1).toUpperCase()+str.substring(1);
			},

			/**
			 * 首字母变小写
			 * @param {String} str 字符串
			 * @returns {String}
			 */
			first2LowerCase:function(str){
				return str.substring(0,1).toLowerCase()+str.substring(1);
			},

			numtochinese:function (num){
				if(typeof(num)=="Number") num=num+"";

				for(i=num.length-1;i>=0;i--){
					num = num.replace(",","")//替换tomoney()中的“,”
					num = num.replace(" ","")//替换tomoney()中的空格
				}

				num = num.replace("￥","")//替换掉可能出现的￥字符
				if(isNaN(num)){
					//验证输入的字符是否为数字
					alert("请检查小写金额是否正确");
					return;
				}
				//---字符处理完毕，开始转换，转换采用前后两部分分别转换---//
				part = String(num).split(".");
				newchar = "";
				//小数点前进行转化
				for(i=part[0].length-1;i>=0;i--){
					if(part[0].length > 10){ alert("位数过大，无法计算");return "";}//若数量超过拾亿单位，提示
					tmpnewchar = ""
					perchar = part[0].charAt(i);
					switch(perchar){
						case "0": tmpnewchar="零" + tmpnewchar ;break;
						case "1": tmpnewchar="壹" + tmpnewchar ;break;
						case "2": tmpnewchar="贰" + tmpnewchar ;break;
						case "3": tmpnewchar="叁" + tmpnewchar ;break;
						case "4": tmpnewchar="肆" + tmpnewchar ;break;
						case "5": tmpnewchar="伍" + tmpnewchar ;break;
						case "6": tmpnewchar="陆" + tmpnewchar ;break;
						case "7": tmpnewchar="柒" + tmpnewchar ;break;
						case "8": tmpnewchar="捌" + tmpnewchar ;break;
						case "9": tmpnewchar="玖" + tmpnewchar ;break;
					}

					switch(part[0].length-i-1){
						case 0: tmpnewchar = tmpnewchar +"元" ;break;
						case 1: if(perchar!=0)tmpnewchar= tmpnewchar +"拾" ;break;
						case 2: if(perchar!=0)tmpnewchar= tmpnewchar +"佰" ;break;
						case 3: if(perchar!=0)tmpnewchar= tmpnewchar +"仟" ;break;
						case 4: tmpnewchar= tmpnewchar +"万" ;break;
						case 5: if(perchar!=0)tmpnewchar= tmpnewchar +"拾" ;break;
						case 6: if(perchar!=0)tmpnewchar= tmpnewchar +"佰" ;break;
						case 7: if(perchar!=0)tmpnewchar= tmpnewchar +"仟" ;break;
						case 8: tmpnewchar= tmpnewchar +"亿" ;break;
						case 9: tmpnewchar= tmpnewchar +"拾" ;break;
					}
					newchar = tmpnewchar + newchar;
				}
				//小数点之后进行转化
				if(num.indexOf(".")!=-1){
					if(part[1].length > 2){
						alert("小数点之后只能保留两位,系统将自动截段");
						part[1] = part[1].substr(0,2)
					}

					for(i=0;i<part[1].length;i++)
					{
						tmpnewchar = ""
						perchar = part[1].charAt(i)
						switch(perchar){
							case "0": tmpnewchar="零" + tmpnewchar ;break;
							case "1": tmpnewchar="壹" + tmpnewchar ;break;
							case "2": tmpnewchar="贰" + tmpnewchar ;break;
							case "3": tmpnewchar="叁" + tmpnewchar ;break;
							case "4": tmpnewchar="肆" + tmpnewchar ;break;
							case "5": tmpnewchar="伍" + tmpnewchar ;break;
							case "6": tmpnewchar="陆" + tmpnewchar ;break;
							case "7": tmpnewchar="柒" + tmpnewchar ;break;
							case "8": tmpnewchar="捌" + tmpnewchar ;break;
							case "9": tmpnewchar="玖" + tmpnewchar ;break;
						}
						if(i==0)tmpnewchar =tmpnewchar + "角";
						if(i==1)tmpnewchar = tmpnewchar + "分";
						newchar = newchar + tmpnewchar;
					}
				}
				//替换所有无用汉字
				while(newchar.search("零零") != -1){
					newchar = newchar.replace("零零", "零");
				}

				newchar = newchar.replace("零亿", "亿");
				newchar = newchar.replace("亿万", "亿");
				newchar = newchar.replace("零万", "万");
				newchar = newchar.replace("零元", "元");
				newchar = newchar.replace("零角", "");
				newchar = newchar.replace("零分", "");

				if (newchar.charAt(newchar.length-1) == "元" || newchar.charAt(newchar.length-1) == "角")
					newchar = newchar+"整"
				return newchar;
			},

			intToChinese:function(str) {
				str = str+'';
				var len = str.length-1;
				var idxs = ['','十','百','千','万','十','百','千','亿','十','百','千','万','十','百','千','亿'];
				var num = ['零','壹','贰','叁','肆','伍','陆','柒','捌','玖'];
				var s=str.replace(/([1-9]|0+)/g,function( $, $1, idx, full) {
					var pos = 0;
					if( $1[0] != '0' ){
						pos = len-idx;
						if( idx == 0 && $1[0] == 1 && idxs[len-idx] == '十'){
							return idxs[len-idx];
						}
						return num[$1[0]] + idxs[len-idx];
					} else {
						var left = len - idx;
						var right = len - idx + $1.length;
						if( Math.floor(right/4) - Math.floor(left/4) > 0 ){
							pos = left - left%4;
						}
						if( pos ){
							return idxs[pos] + num[$1[0]];
						} else if( idx + $1.length >= len ){
							return '';
						}else {
							return num[$1[0]]
						}
					}
				});

				s=EG.String.replaceAll(s,"\\.","点");
				return s;
			}
		}
	});
})();
(function(){
	/**
	 * 调试
	 */
	EG.define("EG.Promise",[

	],function(ME){

		return {
			constructor:function(fn){
				this.resolvesQueue	=[];
				this.rejectQueue	=[];
				this.status			="pendding";
				this.promiseMain	=fn;
				return this;
			},


			handle:function(status,value){
				var me=this;
				this.status=status;

				var fn=null;
				if(this.status=="fullfilled"){
					fn=this.resolvesQueue.shift();
				}else if(this.status=="rejected"){
					fn=this.rejectQueue.shift();
				}

				setTimeout(function(){
					fn.apply(me,[me,value]);
				},0);
			},

			then:function(onFulfilled,onRejected){

				var me=this;

				this.resolvesQueue	.push(onFulfilled);
				this.rejectQueue	.push(onRejected);

				if(this.status=="pendding"){
					this.status=="fullfilled";
					if(!this.t){
						this.t=setTimeout(function(){
							me.promiseMain.apply(me,[me]);
						},0);
					}
				}
				return this;
			},

			/**
			 * 解决
			 */
			resolve:function(value){
				this.handle("fullfilled",value);
			},

			reject:function(value){
				this.handle("rejected",value);
			}
		}
	});
})();
/**
 * @class EG.Event
 * <AUTHOR>
 * 事件操作类
 */
(function(){
	var ME=EG.define("EG.Event",{
		statics:{
			/**
			 * 触发事件
			 * @param ele 元素
			 * @param action 事件
			 */
			fireEvent:function(ele,action){
				//去掉前缀on
				action=EG.String.removeStart(action,"on");
				
				if(document.createEvent){
					var e = document.createEvent('HTMLEvents');
					e.initEvent(action, false, false);
					ele.dispatchEvent(e);
				} else if (ele.fireEvent) { // IE  
					ele.fireEvent('on'+action);  
				}else{
					eval("ele." + action + "();");
				}
			},
			/**
			 * 存放on事件的function事件
			 */
			_eventFns:{},
			/**
			 * 获取事件的fn
			 *
			 * 即便是不同类型的元素，同名的事件也会是同一个函数
			 * EXP:window.onload和body.onload是同一个函数
			 *
			 * @param action
			 * @returns {*}
			 * @private
			 */
			_getEventFN:function(action){
				var eventKey="events_"+action;
				if(!ME._eventFns[eventKey]){
					ME._eventFns[eventKey]=function(e){
						e=ME.getEvent(e);
						var rs=null;
						var target=this;
						if(target[eventKey]==null){	//FIX:DatePicker ON IE
							target=e.target;
						}
						if(!target[eventKey]){
							return rs;
						}
						for(var i = 0,il=target[eventKey].length;i<il;i++){
							var rs=target[eventKey][i].apply(target["on"+action+"Src"]||target,[e]);
							if(rs!=null){
								return rs;
							}
						}
						return rs;
					};
				}
				return ME._eventFns[eventKey];
			},

			/**
			 * 绑定事件,将事件处理添加到执行队列中
			 *
			 * 每个DOM元素的事件执行队列	会以events_click,events_load等类似的数组作为元素的关联变量
			 * 每次DOM元素的事件执行时		会从当前元素中取出执行队列遍历执行
			 * 每个DOM元素的事件执行本体	会以click_fn,load_fn的形式作为元素的关联变量
			 *
			 * @param {HTMLElement} ele 元素
			 * @param {String} action 动作
			 * @param {Function} handler 处理器
			 * @param {Boolean?} cap cap
			 */
			bindEvent : function(ele, action, handler, cap) {
				//去掉前缀onEG.onload
				action=EG.String.removeStart(action,"on");
				var eventKey="events_"+action;

				//创建事件队列
				if(!ele[eventKey]){
					//创建队列
					ele[eventKey]=[];
					//执行本体
					ele[action+"_fn"]=ME._getEventFN(action);

					//使用Attach方式
					var useAttach=false;

					//特殊处理:IFRAME load
					if(EG.Browser.isIE8()||EG.Browser.isIE8Before()){
						useAttach=true;
					}

					//首次绑定执行本体
					if(useAttach){
						ele.attachEvent("on" + action,function(e){
							ele[action+"_fn"].apply(ele,[e]);
						});
					}else if(EG.Browser.isIE()){
						ele["on" + action]=ele[action+"_fn"];
					}else if(ele.addEventListener){
						ele.addEventListener(action,ele[action+"_fn"], cap);
					}
				}

				//存放到事件队列
				ele[eventKey].push(handler);
			},

			/**
			 * 是否有事件处理器
			 */
			hasEventHandle:function(ele,action,fn){
				return ele[action+"_fn"]&&EG.Array.has(ele[action+"_fn"],fn);
			},

			_fn:{
				false_return:function(){return false;}
			},

			/**
			 * 绑定不被选择IE
			 * @param ele
			 */
			bindUnselect:function(ele){
				EG.css(ele,EG.Style.c.selectnone);


				if (EG.Browser.isIE()){
					ME.bindEvent(ele,"onselectstart",ME._fn.false_return);
				}
			},

			/**
			 * 移除事件
 			 * @param ele
			 * @param action
			 * @param handler
			 */
			removeEvent:function(ele,action,handler){
				action=EG.String.removeStart(action,"on");
				var eventKey="events_"+action;
				if(!ele[eventKey]) return;
				EG.Array.remove(ele[eventKey],handler);
			},

			/**
			 * 启动事件
			 * @param {Function} fn 函数
			 */
			onload : function(fn) {
				ME.bindEvent(window,"load",fn);
			},

			/**
			 * 获取Event事件
			 * @param {Event} e 元素
			 * @returns {*|Event}
			 */
			getEvent:function(e){
				return e||window.event;
			},
			/**
			 * 获取目标对象
			 * @param {Event} e 元素
			 * @returns {*|EventTarget|String}
			 */
			getTarget:function(e){
				return e ? e.target : window.event.srcElement;
			},

			/**
			 * 停止传播事件
			 * @param {Event} e 元素
			 */
			stopPropagation:function(e){
				e=ME.getEvent(e);
				if (e&&e.stopPropagation) e.stopPropagation();
				else window.event.cancelBubble = true;

				if(e.preventDefault){
					e.preventDefault();
				}else{
					e.returnValue = false;
				}
			},

			keycodes:{
				esc:27
			}
		}
	});

	// 注册到全局
	EG.bindEvent 	= ME.bindEvent;
	EG.removeEvent 	= ME.removeEvent;
	EG.onload 		= ME.onload;
})();

/**
 * @class EG.DOM
 * <AUTHOR>
 * DOM操作类
 */
(function(){
	//BUGFIX#FireFox 无outerHTML、canHaveChildren
	var fixOuterHTML4Firefox=false;
	if(fixOuterHTML4Firefox){
		if (!EG.Browser.isIE() && typeof (HTMLElement) != "undefined" && !window.opera) {
			//outerHTML
			HTMLElement.prototype.__defineGetter__("outerHTML", function() {
				var a = this.attributes, str = "<" + this.tagName;
				for ( var i = 0; i < a.length; i++) if (a[i].specified) str += "   " + a[i].name + '="' + a[i].value + '"';
				if (!this.canHaveChildren) return str + "   />";
				return str + ">" + this.innerHTML + "</" + this.tagName + ">";
			});
			HTMLElement.prototype.__defineSetter__("outerHTML", function(s) {
				var d = EG.doc.createElement("DIV");
				d.innerHTML = s;
				for ( var i = 0; i < d.childNodes.length; i++) this.parentNode.insertBefore(d.childNodes[i], this);
				this.parentNode.removeChild(this);
			});
			//canHaveChildren
			HTMLElement.prototype.__defineGetter__("canHaveChildren",function() {
				return !/^(area|base|basefont|col|frame|hr|img|br|input|isindex|link|meta|param)$/.test(this.tagName.toLowerCase());
			});
		}
	}
	var ME=EG.define("EG.DOM",{
		statics:{
			/**
			 * 添加子元素
			 * @param {HTMLElement}	ele 父元素
			 * @param {HTMLElement|Array|Object|EG.ui.Item} child 子元素
			 * @param {Number} idx 位置
			 */
			addChildren:function(ele,child,idx){
				var childs=(!EG.isArray(child))?[ child ]:child;
				for (var i = 0,il = childs.length; i < il; i++) {
					var c = childs[i];
					if (EG.isLit(c)) {
						c = EG.CE(c);
					}else if(c.getElement){
						c=c.getElement();
					}
					ME.insertChild(ele,c,idx);
					idx++;
				}
			},

			/**
			 * 在某个对象后面插入元素
			 * @param {HTMLElement} ele 待插入元素
			 * @param {HTMLElement} target 参考元素
			 */
			insertAfter:function(ele, target){
				var pn = target.parentNode;
				if(pn.lastChild == target){
					pn.appendChild(ele);
				}else{
					pn.insertBefore(ele,ME.nextNode(target));
				}
			},

			/**
			 * 在某个对象前面插入元素
			 * @param {HTMLElement} ele 待插入元素
			 * @param {HTMLElement} target 参考元素
			 */
			insertBefore:function(ele, target){
				var pn = target.parentNode;
				pn.insertBefore(ele,target);
			},

			/**
			 * 指定位置插入子元素
			 * @param {HTMLElement} pn 父元素
			 * @param {HTMLElement} ele 子元素
			 * @param {HTMLElement} target 参考元素
			 */
			insertChild:function(pn,ele,target){
				if(!target) return pn.appendChild(ele);

				if(typeof(target)=="number"){
					target=ME.childNodes(pn)[target];
				}
				if(target){
					pn.insertBefore(ele,target);
				}else{//IE
					pn.appendChild(ele);
				}

			},

			/**
			 * 获取元素的outerHTML,只支持无父节点的节点
			 * @param {HTMLElement} ele 元素
			 */
			getOuterHTML:function(ele){
				if(ele.parentNode) throw new Error("EG.DOM#getOuterHTML:不支持有父节点的节点");
				return EG.CE({tn:"div",cn:[ele]}).innerHTML;
			},

			/**
			 * 查询元素
			 * @param {String|HTMLElement}	selector 字符串|表达式|元素
			 */
			$:function(selector,doc) {
				if(!doc) doc=EG.doc;
				if(ME.isElement(selector)) return selector;

				if("string"==typeof(selector)){
					var e=doc.getElementById(selector);
					if(e!=null) return e;
					else if((e=doc.getElementsByName(selector))!=null&&e.length>0) return e;
					else return null;

					//TODO 待支持 JQuery的表达式索引 #id>aa
				}else if("object"==typeof(selector)){
					var objs=null,dObjs=[];

					if(selector["name"]){
						var ele=selector["ele"]||doc;
						objs=ele.getElementsByName(selector["name"]);
					}else if(selector["tn"]){
						var ele=selector["ele"]||doc;
						objs=ele.getElementsByTagName(selector["tn"]);
					}else{
						throw new Error("EG.DOM#$:name和tn不能同时为空");
					}

					var idx=selector["idx"];
					
					//TODO 待修复为不删除的判断
					delete selector["name"];
					delete selector["tn"];
					delete selector["ele"];
					delete selector["idx"];

					for( var i=0,il=objs.length;i<il;i++){
						var match=true;
						for(var key in selector){
							if(EG.$in("name","tn","ele","idx")) continue;
							if(selector[key]!=objs[i][key]){
								match=false;
								break;
							}
						}
						if(match) dObjs.push(objs[i]);
					}

					if(idx!=null){
						return EG.Array.get(dObjs,idx);
					}

					return dObjs;
				}
			},

			/**
			 * @property {Array} CE关键词
			 * @private
			 */
			CEKeywords:["ele","element","tn","tagName","pn","parentNode","cn","childNodes","style"],

			/**
			 * @property {Object} CE昵称
			 * @private
			 */
			CENicknames:{
				"cls":"className"
			},

			/**
			 * SVG 命名空间
			 */
			NS_SVG:"http://www.w3.org/2000/svg",

			/**
			 * SVG元素创建
			 * @param {Object} atrs 属性
			 * @returns {HTMLElement}
			 */
			CSVG:function(atrs){
				return ME.CE(atrs,{svg:true});
			},

			/**
			 * VML元素创建
			 * @param {Object} atrs 属性
			 * @return {HTMLElement}
			 */
			CVML:function(atrs){
				return ME.CE(atrs,{vml:true});
			},

			/**
			 * 创建元素 特殊属性:pn:父节点,tn:标签,cn:子节点,ele:元素,style:样式
			 *
			 * @param {Object} atrs 属性
			 * @param {?Object} ext 扩展
			 * @returns {HTMLElement}
			 */
			CE:function(atrs,ext) {
				var isSvg		=ext?ext["svg"]		:false;
				var isVml		=ext?ext["vml"]		:false;
				var setAtr		=ext?ext["setAtr"]	:false;
				if(typeof(atrs)=="string"){
					return ME.childNodes(EG.CE({tn:"div",innerHTML:atrs}));
				}

				var ele		=EG.unnull(atrs["ele"],atrs["element"]),	//对象Element
					tn		=EG.unnull(atrs["tn"],atrs["tagName"]),		//标签名
					pn		=EG.unnull(atrs["pn"],atrs["parentNode"]),	//父节点
					cn		=EG.unnull(atrs["cn"],atrs["childNodes"]),	//子节点
					style	=atrs["style"];					//样式

				if(!ele&&!tn) throw new Error("EG.DOM#CE:标签名和ele不能同时为空");

				if(tn){
					//BUGFIX#IE6 iframe动态创建时name无法指定
					if(tn.toUpperCase()=="IFRAME"&&atrs["name"]&&EG.Browser.isIE6()){
						ele=EG.doc.createElement("<iframe name='"+atrs["name"]+"'></iframe>");
					}else{
						if(isSvg){
							ele=EG.doc.createElementNS(ME.NS_SVG,tn);
						}else if(isVml){
							ele=EG.doc.createElement(tn);
						}else{
							ele=EG.doc.createElement(tn);
						}

						//VML命名空间
						if(isVml){
							ele.xmlns="urn:schemas-microsoft-com:vml";
							//ele.style.behavior='url(#default#VML);';
							//ele.style.behavior='url(#default#VML);';
							atrs["cls"]="vml";
						}

					}
				}

				//赋属性
				for(var key in atrs){
					//过滤关键字
					if(EG.$in(key,ME.CEKeywords)) continue;

					var atrV=atrs[key];

					//设置Name
					if(key=="name"){
						ele.setAttribute("name",atrV);
						ele.name=atrV;
					//支持x$a方式的子属性赋值
					}else if(key.indexOf("$")>0){
						var si=key.indexOf("$"),
							pre=key.substring(0,si),
							end=key.substring(si+1,key.length);

						if(!ele[pre]){
							ele[pre]={};
						}

						ele[pre][end]=atrV;
					//事件绑定
					}else if(key.indexOf("on")==0&&!EG.String.endWith(key,"Src")){
						if(atrV!=null){
							EG.bindEvent(ele,key.substr(2).toLowerCase(),atrV);
						}
					//直接赋值
					}else{
						if(ME.CENicknames[key]){
							key=ME.CENicknames[key];
						}

						//SVG:使用attribute:svg的属性
						if(isSvg){
							if(key=="innerText"){
								ME.removeChilds(ele);
								//ele.removeChilds();
								ele.appendChild(document.createTextNode(atrV));
							}else if(key=="DATA"){
								ele["DATA"]=atrV;
							}else{
								//特殊
								if(key=="className"){
									key="class";
								}
								//ele.setAttribute(key,atrV);
								ele.setAttributeNS(null,key ,atrV);
							}
//							}else if(isVml){
//								ele.setAttribute(key,atrV);
						//默认使用=号来赋值属性
						}else{
							//强行设定属性
							if(setAtr){
								ele.setAttribute(key,atrV);
							}
							ele[key]=atrV;
						}
					}
				}

				//样式设定
				if(style) EG.css(ele,style);

				//父添加
				if(pn){
					if(pn.isItem){
						pn.getElement().appendChild(ele);
					}else{
						pn.appendChild(ele);
					}
				}

				//添加子
				if(cn){
					if(typeof(cn)=="string"){
						cn=ME.CE(cn);
					}
					for(var i=0,il=cn.length;i<il;i++){
						if(EG.isLit(cn[i])) 		cn[i]=EG.CE(cn[i],ext);
						else if(cn[i].isItem) 		cn[i]=cn[i].getElement();
						ele.appendChild(cn[i]);
					}
				}
				return ele;
			},

			/**
			 * 是否为Element
			 * @param {Node} ele 元素
			 * @returns {Boolean}
			 */
			isElement:function(ele) {
				return ele&&ele.nodeType == 1;
			},

			/**
			 * 是否为多个元素数组
			 * @param {Node} ele 元素
			 * @returns {Boolean}
			 */
			isNodeList : function(ele){
				return ele!=null
					&&ele.nodeType==null	//不具有nodeType
					&&ele.length!=null
					&&typeof(ele.length)=="number"
					&&ele.item!=null
				;
			},

			/**
			 * 是否为指定标签的元素
			 *
			 * @param {HTMLElement} ele 元素
			 * @param {String} tagName 标签名
			 * @returns {Boolean}
			 */
			isTag:function(ele,tagName) {
				return ele.tagName.toUpperCase()==tagName.toUpperCase();
			},

			/**
			 * 父元素是否含有子元素
			 * @param {Node} pEle 父元素
			 * @param {Node} cEle 子元素
			 * @return {Boolean}
			 */
			has:function(pEle,cEle){
				var pNode=cEle.parentNode;
				while(pNode!=null){
					if(pNode==pEle) return true;
					pNode=pNode.parentNode;
				}
				return false;
			},

			remove:function(ele){
				if(!EG.Array.isArray(ele)){
					ele=[ele];
				}

				for(var i=0;i<ele.length;i++){
					ele[i].parentNode.removeChild(ele[i]);
				}

			},

			/**
			 * 删除所有子元素
			 * @param {Node} ele 父元素
			 */
			removeChilds:function(ele) {
				while(ele.childNodes&&ele.childNodes.length>0){
					ele.removeChild(ele.childNodes[0]);
				}
				return ele;
			},



			/**
			 * 前一个节点
			 * @param {Node} node 节点
			 */
			preNode:function(node) {
				return ME._nextNode(node,-1);
			},

			/**
			 * 下一个节点
			 * @param {Node} node 节点
			 */
			nextNode : function(node) {
				return ME._nextNode(node,1);
			},

			/**
			 * 下一N个节点
			 * @param {Node} node 节点
			 * @param {Number} n 相对位置
			 */
			_nextNode : function(node,n) {
				//检测节点及父节点
				if(!node||!node.parentNode) return null;
				//所有真实节点
				var cns=ME.childNodes(node.parentNode);
				for(var i=0,il=cns.length;i<il;i++){
					if(cns[i]==node){
						return (i+n<il)?cns[i+n]:null;
					}
				}
			},

			/**
			 * 获取指定子节点
			 * 支持x_x_x_x索引子节点
			 * @param {Node} node 父节点
			 * @param {Number|String} n 坐标
			 */
			childNode:function(node,n){
				if(typeof(n)=="string"){
					var ns=n.split("_");
					for(var i=0,il=ns.length;i<il;i++){
						if(ns[i]!="n") ns[i]=parseInt(ns[i]);
						node=EG.Array.get(ME.childNodes(node),ns[i]);
					}
					return node;
				}else if(typeof(n)=="number"){
					var cns=ME.childNodes(node);
					return EG.Array.get(cns,n);
				}throw new Error("EG.DOM#childNode不支持索引类型:"+typeof(n));
			},

			/**
			 * 真实子节点(忽略空白节点、注释等其它节点)
			 * @param {Node} node 父节点
			 * @return {Array}
			 */
			childNodes : function(node) {
				var res=[],cns=node.childNodes;
				for( var i=0,il=cns.length;i<il;i++){
					if(cns[i].nodeType==1) res.push(cns[i]);
				}
				return res;
			},

			/**
			 * 获取Value
			 * @param {HTMLElement|HTMLInputElement} ele 元素
			 * @param {Object?} ext 扩展选项,select:getText 获取文本 select:ignoreEmpty 忽略空值
			 * @return {String|Array}
			 */
			getValue : function(ele, ext) {
				// 扩展参数
				ext=ext||{};

				if(typeof(ele)=="string"){
					ele=ME.$(ele);
				}

				if(ele==null){ throw new Error("EG.DOM#getValue:未找到匹配元素"); }

				var isArray=ME.isNodeList(ele);
				var tn=(isArray?ele[0]:ele).tagName.toUpperCase();

				if(tn=="INPUT"){

					var type=(isArray?ele[0]:ele).type.toUpperCase();

					if(type=="TEXT"||type=="PASSWORD"||type=="HIDDEN"||type=="FILE"){
						if(isArray) throw new Error("EG.DOM#getValue:暂不支持数组Element INPUT "+type);
						return ele.value;
					}else if(type=="RADIO"){
						if(isArray){
							for( var i=0,il=ele.length;i<il;i++){
								if(ele[i].checked==true){ return ele[i].value; }
							}
						}else{
							return (ele.checked==true)?ele.value:null;
						}
					}else if(type=="CHECKBOX"){
						if(isArray){
							var vs=[];
							for( var i=0,il=ele.length;i<il;i++){
								if(ele[i].checked==true) vs.push(ele[i].value);
							}
							return vs;
						}else{
							return (ele.checked==true)?ele.value:null;
						}
					}else{
						throw new Error("EG.DOM#getValue:不支持input "+type+"类型");
					}
				}else if(tn=="SELECT"){
					if(isArray) throw new Error("EG.DOM#getValue:暂不支持数组Element SELECT");
					// 扩展
					var extGetText=ext["getText"]!=null?ext["getText"]:false; 				// 提取文本
					var extIgnoreEmpty=ext["ignoreEmpty"]!=null?ext["ignoreEmpty"]:false; 	// 是否忽略空值

					var opts=ele.options;
					var vs=[];
					var multiple=ele.multiple;
					for( var i=0,il=opts.length;i<il;i++){
						if(opts[i].selected==true){
							var v=null;
							if(extIgnoreEmpty&&opts[i].value=="") v=null;
							else v= (extGetText)?opts[i].text:opts[i].value;
							if(multiple){
								vs.push(v);
							}else{
								return v;
							}
						}
					}

					if(multiple) return vs;
					else return null;
				}else if(tn=="TEXTAREA"){
					if(isArray){
						var vs=[];
						for(var i=0,il=ele.length;i<il;i++){
							vs.push(ele.value);
						}
						return vs;
					}else{
						return ele.value;
					}
				}else if(ele.innerHTML!=null){
					if(isArray){
						var vs=[];
						for(var i=0,il=ele.length;i<il;i++){
							vs.push(ele.innerHTML);
						}
						return vs;
					}else{
						return ele.innerHTML;
					}
				}else{
					throw new Error(" EG.DOM#getValue:不支持其它类型值");
				}
				return null;
			},

			/**
			 * 获取输入组件的数值,返回字面量对象
			 * @param {HTMLElement} ele 容器
			 */
			getValues : function(ele) {
				var c={};
				var e=ME.$(ele);
				var es=ele.getElementsByTagName("INPUT");
				es.concat(ele.getElementsByTagName("SELECT"));
				es.concat(ele.getElementsByTagName("TEXTAREA"));
				for( var i=0,il=es.length;i<il;i++){
					if(e[i].id==""&&e[i].name=="") continue;
					var key=e[i].id!=""?e[i].id:e[i].name;
					c[key]=ME.getValue(key);
				}
				return c;
			},

			/**
			 * 设置数值
			 * @param {HTMLElement|HTMLInputElement} ele 元素
			 * @param {String|Array} value 数值
			 * @param {Object?} ext 扩展
			 */
			setValue : function(ele, value, ext) {
				ext=ext||{};
				var fireOnchange=ext["fireOnchange"]!=null?ext["fireOnchange"]:true;//自动触发onchange事件

				if(typeof (ele)=="string"){
					ele=EG.$(ele);
				}
				if(ele==null) throw new Error("EG.DOM#setValue未找到匹配元素");

				var isArray=ME.isNodeList(ele);
				var tn=(isArray?ele[0]:ele).tagName.toUpperCase();

				if(value==null) value="";

				if(tn=="INPUT"){
					var type=(isArray?ele[0]:ele).type.toUpperCase();

					if(type=="TEXT"||type=="PASSWORD"||type=="HIDDEN"){
						ele.value=value;
					}else if(type=="RADIO"){
						if(isArray){
							for( var i=0,il=ele.length;i<il;i++)
								if(ele[i].value==value){
									ele[i].checked=true;
									break;
								}
						}else if(ele.value==value) ele.checked=true;
					}else if(type=="CHECKBOX"){
						var spliter=ext["spliter"]||",";
						if(value instanceof Array){
							if(!isArray) throw new Error("EG.DOM#setValue:值为数组时,Element类型必须为数组");
							for( var i=0,il=ele.length;i<il;i++){
								if(EG.Array.has(value,ele[i].value)){
									ele[i].checked=true;

									if(fireOnchange){
										EG.Event.fireEvent(ele[i],"onchange");
									}
								}
							}
						}else{
							if(isArray){
								for( var i=0,il=ele.length;i<il;i++){
									if(value==ele[i].value){
										ele[i].checked=true;
										if(fireOnchange){
											EG.Event.fireEvent(ele[i],"onchange");
										}
									}
								}
							}else{
								if(ele.value==value){
									ele.checked=true;
									if(fireOnchange){
										EG.Event.fireEvent(ele,"onchange");
									}
								}
							}
						}



					}else throw new Error("Engin#setValue:不支持该input "+type+"类型");
				}else if(tn=="SELECT"){
					var extCmpText=ext["cmpText"]!=null?ext["cmpText"]:false; // 文本比对
                    var opts=ele.options;
					for( var i=0,il=opts.length;i<il;i++){
						if(		( extCmpText&&opts[i].text==value)
							||	(!extCmpText&&opts[i].value==value)){
							opts[i].selected=true;
							if(fireOnchange){
                                EG.Event.fireEvent(ele,"onchange");
                            }
							return;
						}
					}
				}else if(tn=="TEXTAREA"){
					ele.value=value;
				}else if(ele.innerHTML!=null){
					ele.innerHTML=value;
				}else{
					throw new Error(" EG.DOM#setValue:不支持对其它类型设值");
				}
			},

			/**
			 * 根据字面量对象，寻找组件并设置对应值
			 * @param {Object} data 数据
			 */
			setValues : function(data) {
				if(data!=null) {
					for(var key in data){
						ME.setValue(key,data[key]);
					}
				}
			},

			/**
			 * 移除Select Options
			 * @param {HTMLElement} ele 元素
			 */
			removeOptions : function(ele) {
				ele = EG.$(ele);
				if(!ME.isTag(ele, "SELECT")) throw new Error("EG.DOM#removeOptions:待删除的对象非Select组件");
				ME.removeChilds(ele);
			},

			/**
			 * 添加option
			 * @param {HTMLElement} ele 元素
			 * @param {Array} data 数据
			 * @param {?String|?Function} textKey 文本键值
			 * @param {?String|?Function} valueKey 数据键值
			 * @param {?String|?Function} attributeName 属性名
			 * @param {?String|?Function} attributeKey 属性值
			 */
			addOptions 	: function(ele, data, textKey,valueKey, attributeName,attributeKey) {
				ele = EG.$(ele);
				valueKey = valueKey||1;
				textKey = textKey||0;
				for ( var i = 0, il = data.length; i < il; i++) {
					var opt = EG.CE({tn:"option",
						value		:(typeof(valueKey)=="function") ?valueKey(data[i])	:data[i][valueKey],
						innerHTML	:(typeof(textKey) =="function") ?textKey(data[i])	:data[i][textKey]
					});
					if (attributeName&&attributeKey){
						opt[attributeName]=(typeof (attributeKey) == "function") ? attributeKey(data[i]):data[i][attributeKey];
					}

					ele.options[ele.options.length] = opt;
				}
			},

			/**
			 * 获取值对应的Option
			 * @param {HTMLElement} ele 元素
			 * @param {String} val 数值
			 * @return {HTMLOptionElement}
			 */
			getOption:function(ele,val){
				var opts=ele.options;
				for(var i= 0,il=opts.length;i<il;i++){
					var opt=opts[i];
					if(opt.value==val) return opt;
				}
			},

			/**
			 * 获取已选中的Option
			 * @param {HTMLElement} ele 元素
			 * @return {HTMLOptionElement}
			 */
			getSelectedOption:function(ele){
				return ele.options[ele.options.selectedIndex];
			},

			/**
			 * 删除Option
			 * @param {HTMLElement} ele 元素
			 * @param {String|Number|Object} n 位置
			 */
			removeOption:function(ele,n){
				var model=null,v;
				if(typeof(n)=="string"){
					n={value:n};
				}

				if((v=n["idx"])!=null){
					model="idx";
				}else if((v=n["value"])!=null){
					model="value";
				}else if((v=n["text"])!=null){
					model="text";
				}else throw new Error("EG.DOM#removeOption:参数不正确");

				var opts=ele.options;
				//多重的删除
				if(EG.isArray(v)){
					for(var i=opts.length-1;i>=0;i--){
						for(var j=0,jl=v.length;j<jl;j++){
							if((   model=="idx"&&i==v[j])
								||(model=="value"&&v[j]==opts[i].value)
								||(model=="text"&&v[j]==opts[i].text)){
								ele.removeChild(opts[i]);
								break;
							}
						}
					}
				}else{
					for(var i=opts.length-1;i>=0;i--){
						if((   model=="idx"&&i==v)
							||(model=="value"&&v==opts[i].value)
							||(model=="text"&&v==opts[i].text)){
							ele.removeChild(opts[i]);
						}
					}
				}
			},

			/**
			 * 获取文本键值数组
			 * @param {HTMLElement} ele 元素
			 * @param {?Object} ext 扩展,{egnoreEmpty:true}
			 * @return {Array}
			 */
			getTextvalues:function(ele,ext){
				ext=ext||{};
				var egnoreEmpty=ext["egnoreEmpty"]!=null?ext["egnoreEmpty"]:true;
				var tvs=[];
				var opts=ele.options;
				for(var i=0,il=opts.length;i<il;i++){
					if(egnoreEmpty&&opts[i].value=="") continue;
					tvs.push([opts[i].text,opts[i].value]);
				}
				return tvs;
			},

			/**
			 * 批量选择box,寻找同name,同值的box设置
			 * @param {String} boxName box名称
			 * @param {?Boolean} checked 是否选择
			 * @param {?String} value 数值
			 */
			selectBoxes : function(boxName,checked,value) {
				if(!checked) checked=true;
				var boxes = EG.doc.getElementsByName(boxName);
				for ( var i = 0, il = boxes.length; i < il; i++) {
					if(value!=null) boxes[i].checked = (boxes[i].value = value)?checked:!checked;
					else boxes[i].checked=checked;
				}
			},

			/**
			 * 移除所有Row
			 * @param {HTMLElement} ele 元素
			 */
			removeAllRows : function(ele) {
				ele = EG.$(ele);
				if (ele == null) return;
				ME.removeChilds(ele);
			},

			/**
			 * 获取隐藏处理的ActionFrame
			 * @return {HTMLIFrameElement}
			 */
			getActionFrame:function(){
				if (!ME.actionFrame) {
					ME.actionFrame=EG.CE({pn:EG.getBody(),tn:"iframe",id:"actionFrame",name:"actionFrame",style:"display:none"});
				}
				return ME.actionFrame;
			},

			/**
			 * 获取元素坐标
			 * @param ele
			 * @return {Number}
			 */
			getIdx:function(ele){
				var cns=ME.childNodes(ele.parentNode);
				for(var i=0;i<cns.length;i++){
					if(cns[i]==ele) return i;
				}
				throw new Error("未找到索引");
			},

			/**
			 * 包含
			 */
			contains:document.contains?function(ele){
				return document.contains(ele);
			}:function(ele){
				return document.documentElement.contains(ele);
			}
		}
	});
	EG.CSVG		=ME.CSVG;
	EG.CVML		=ME.CVML;
	EG.$ 		=ME.$;
	EG.CE 		=ME.CE;
	EG.getValue =ME.getValue;
	EG.setValue =ME.setValue;
}());



/**
 * @class EG.Style
 * <AUTHOR>
 * 样式操作类
 */
(function(){
	EG.define("EG.Style",[

	],function(ME){

		var getComputedStyle=window.getComputedStyle								//新版本 Firefox Safari Chrome Opera
			||(EG.doc&&EG.doc.defaultView&&EG.doc.defaultView.getComputedStyle);	//老版本

		return {
			statics:{
				/**
				 * CSS常用值
				 */
				c:{
					dv			:"display:inline-block;vertical-align:middle;",
					selectnone	:"-moz-user-select:none;-webkit-user-select:none;user-select:none;",
					selectauto	:"-moz-user-select:auto;-webkit-user-select:auto;user-select:auto;"
				},
				debugSize:function(ele){
					if(ele.getElement) ele=ele.getElement();
					alert(EG.toJSON(EG.getSize(ele)));
				},
				/**
				 * 解析transform
				 * @param {String} transform 值
				 * @return {Object}
				 */
				parseTransform:function(transform){
					var m={};
					var regex=new RegExp("([a-zA-Z0-9]+)\\(([^)]*)\\)","ig");
					if(!transform) return m;
					var as=transform.match(regex);
					if(!as) return m;
					for (var i=0;i<as.length ;i++ ){
						var a=as[i];
						var k=a.replace(regex,"$1");
						var vs=null;

						var ss=a.replace(regex,"$2");

						//FIX-IE:IE下transform中间用空格而不是逗号
						if(EG.Browser.isIE()){
							ss=ss.replace(new RegExp(" ","ig"),",");
						}

						//alert(a+"\n"+ss);
						eval("vs=["+ss+"]");
						m[k]=vs;
					}
					return m;
				},
				/**
				 * 尺寸转数字
				 * @param {String|Number} size 尺寸
				 * @param {Number} refferNum 参考尺寸
				 * @returns {Number}
				 */
				size2Num:function(size,refferNum){
					if(typeof(size)=="string"){
						if(EG.String.endWith(size,"%")){
							if(refferNum==null) Error("EG.Style#size2Num:百分比时参考尺寸不能为空.");
							return parseInt(refferNum*parseInt(size.substr(0,size.length-1))/100);
						}else if(EG.String.endWith(size,"px")){
							return parseInt(size.substr(0,size.length-2));
						}else if(EG.String.endWith(size,"vh")){
							return parseInt(size.substr(0,size.length-2))*Style.vh;
						}else if(EG.String.endWith(size,"vw")){
							return parseInt(size.substr(0,size.length-2))*Style.vw;
						}else if(size=="thin"){
							return 1;
						}else if(size=="medium"){
							return 3;
						}else if(size=="thick"){
							return 5;
						}else if(size=="auto"){//TODO 需要根据属性类型判定
							return 0;
						}else if(size==""){
							return 0;
						}else{
							throw new Error("EG.Style#size2Num:暂不支持数值转换."+size);
						}
					}else if(typeof(size)=="number"){
						return size;
					}else throw new Error("EG.Style#size2Num:参数类型无法识别."+typeof(size));
				},

				/**
				 * 获取元素的尺寸
				 *
				 * @param {HTMLElement|EG.ui.Item} ele 元素
				 * @param {Boolean?} css 使用CSS值计算
				 * @return {Object}
				 */
				getSize:function(ele,css){
					if(ele.getElement) ele=ele.getElement();

					var cs=Style.current(ele);
					var size={
						offsetWidth		:ele.offsetWidth,
						offsetHeight	:ele.offsetHeight,
						clientWidth		:css?cs.width:ele.clientWidth,
						clientHeight	:css?cs.height:ele.clientHeight,
						borderLeft		:(cs.borderLeftStyle	!="none")?(Style.size2Num(cs.borderLeftWidth))	:0,
						borderTop		:(cs.borderTopStyle		!="none")?(Style.size2Num(cs.borderTopWidth))	:0,
						borderRight		:(cs.borderRightStyle	!="none")?(Style.size2Num(cs.borderRightWidth))	:0,
						borderBottom	:(cs.borderBottomStyle	!="none")?(Style.size2Num(cs.borderBottomWidth)):0,
						paddingLeft		:(Style.size2Num(cs.paddingLeft)),
						paddingTop		:(Style.size2Num(cs.paddingTop)),
						paddingRight	:(Style.size2Num(cs.paddingRight)),
						paddingBottom	:(Style.size2Num(cs.paddingBottom))
					};

					size.marginLeft		=(Style.size2Num(cs.marginLeft));
					size.marginTop		=(Style.size2Num(cs.marginTop));
					size.marginRight	=(Style.size2Num(cs.marginRight));
					size.marginBottom	=(Style.size2Num(cs.marginBottom));

					size.vScrollWidth	=size.offsetWidth	-size.clientWidth	-size.borderLeft	-size.borderRight;//  size.offsetHeight	+size.marginTop		+size.marginBottom;
					size.hScrollWidth	=size.offsetHeight	-size.clientHeight	-size.borderTop		-size.borderBottom;//	+size.marginTop		+size.marginBottom;

					size.innerWidth		=size.clientWidth	-size.paddingLeft	-size.paddingRight;
					size.innerHeight	=size.clientHeight	-size.paddingTop	-size.paddingBottom;
					size.outerWidth		=size.offsetWidth	+size.marginLeft	+size.marginRight;
					size.outerHeight	=size.offsetHeight	+size.marginTop		+size.marginBottom;

					return size;
				},

				getInnerTop		:function(s){return Style.getInnerOut(s,"Top");},
				getInnerBottom	:function(s){return Style.getInnerOut(s,"Bottom");},
				getInnerLeft	:function(s){return Style.getInnerOut(s,"Left");},
				getInnerRight	:function(s){return Style.getInnerOut(s,"Right");},
				getInnerOut:function(s,type){
					return s["margin"+type]+s["padding"+type]+s["border"+type];
				}
				,
				/**
				 * 数字转尺寸
				 * @param {Number|String} num 数字
				 * @returns {String}
				 */
				num2Size:function(num){
					if(typeof(num)=="number"){
						return num+"px";
					}else if(typeof(num)=="string"){
						return num;
					}else throw new Error("EG.Style#num2Size:参数类型无法识别."+typeof(num));
				},

				/**
				 * 创建样式
				 * @param {Object} styles 样式
				 */
				create:function(styles) {
					var ss = "";
					for (var key in styles){
						ss += (key + "{" + styles[key] + "}\n");
					}

					if (Style.element.styleSheet)
						Style.element.styleSheet.cssText += ss;
					else
						Style.element.appendChild(EG.doc.createTextNode(ss));// IE6
				},

				/**
				 * 创建样式
				 * @param {String} url 路径
				 * @returns {HTMLStyleElement}
				 */
				createSheet:function(url) {
					var css=EG.CE({tn:"link",rel:"stylesheet",rev:"stylesheet",type:"text/css",media:"screen",href:url});
					EG.doc.getElementsByTagName("head")[0].appendChild(css);
					return css;
				},

				/**
				 * 获取Element的计算后的当前样式
				 * @param {HTMLElement} ele 元素
				 * @returns {Object}
				 */
				current:function(ele) {
					if(getComputedStyle){				//Support Firefox Safari Chrome Opera
						return getComputedStyle(ele);
					}else{
						return ele.currentStyle;		//IE6 IE7 IE8 Opera
					}
				},

				/**
				 * 解析Style
				 * @returns {Object}
				 */
				parse:function(style){
					//ele.setAttribute("style",style);
					var m={};
					var ses = style.split(";");
					for(var i=0;i<ses.length;i++){
						//var sp=EG.String.trim(ses[i]);
						var sp=ses[i];
						if(sp=="") continue;
						var sess=sp.split(":");
						m[sess[0]]=sess[1];
					}
					return m;
				},

				/**
				 * 设置Element的样式
				 * @param {HTMLElement|EG.ui.Item} ele 元素
				 * @param {String|Object} style 样式
				 */
				css:function(ele, style) {

					if(ele.getElement){
						ele=ele.getElement();
					}

					if(!ele.cacheStyle){
						ele.cacheStyle={};
					}

					var m = {};
					if (typeof (style) == "string") {
						m=ME.parse(style);
					}else if(typeof (style)=="object"){
						m=style;
					}else{
						throw new Error("EG.Style#set:style类型错误");
					}

					//TODO 用setAttribute来可以屏蔽float特殊的兼容性
					var clearSize=false;
					for(var k in m){
						var v=m[k];
						if(k=="float"){//float是Javascript关键字
							k=('cssFloat' in ele.style)?"cssFloat":'styleFloat';	// FIX:IE下是styleFloat Firefox下float为cssFloat
						}else if(k=="vertical-align"){
							k="verticalAlign";
						}

						if(EG.Browser.isIE8()){
							var ks= k.split("-");
							if(ks.length>1){
								k=ks[0]+ks[1].substr(0,1).toUpperCase()+ks[1].substr(1);
							}
						}

						ele.style[k]=v;

						if(ele.cacheStyle[k]!==v){
							ele.cacheStyle[k]=v;
							if(k=="width"||k=="height"|| k.indexOf("margin")==0|| k.indexOf("padding"==0)){
								clearSize=true;
							}
						}
					}

					if(clearSize) ele.egSize=null;
				},

				/**
				 * 是否Element已隐藏
				 * inherit 支持判断该节点是否因上层被隐藏
				 * @param {HTMLElement} ele
				 * @param {Boolean?} inherit
				 */
				isHide:function(ele,inherit) {
					if(!inherit){
						var cs=Style.current(ele);
						return cs.display == "none";
					}else{
						var p=ele;
						while(p){
							if(p==EG.getBody()) return false;

							var cs=Style.current(p);
							if(cs.display == "none"||cs.visible == "none") return true;
							p=p.parentNode;
						}
						return true;
					}
				},

				/**
				 * 显示Element 支持多个参数
				 */
				show:function() {
					Style.displays(arguments, true);
				},

				/**
				 * 隐藏Element 支持多个参数
				 */
				hide:function() {
					Style.displays(arguments, false);
				},

				/**
				 * 显示
				 * @param eles
				 * @param visible
				 */
				visibles:function(eles,visible){
					for(var i=0,il=eles.length;i<il;i++){
						Style.visible(eles[i],visible);
					}
				},

				/**
				 * 显示
				 * @param ele
				 * @param visible
				 */
				visible:function(ele,visible){
					ele.style.visibility=visible?"visible":"hidden";
				},

				/**
				 * 设置Element的display
				 * @param eles
				 * @param display
				 */
				displays:function(eles,display){
					for(var i=0,il=eles.length;i<il;i++){
						Style.display(eles[i],display);
					}
				},

				/**
				 * 设置Element的display
				 *
				 * @param ele {HTMLElement}
				 * @param display 是否显示
				 */
				display :function(ele, display) {

					if(typeof(ele)=="string") ele=EG.$(ele);

					if(ele.getElement) ele=ele.getElement();

					if(typeof(display)=="boolean"){
						var cs=Style.current(ele);
						var cd=ele.style["display"]||(cs?cs["display"]:"");	//C:当chorme的元素未被添加到DOM树中时,display无论是何值currentStyle.display都是空
						if(!display){//隐藏
							if(cd!="none"){
								if(EG.DOM.contains(ele)){
									ele.oDisplay=cd;
								}
								ele.style.display="none";
							}
						}else{
							if(cd=="none"){
								ele.style.display=ele.oDisplay||"";
							}
						}
					}else if(typeof(display)=="string"){
						ele.style.display=display;
					}else{
						throw new Error("EG.Style#display:不支持"+display);
					}
				},

				/**
				 * 居中子元素
				 * @param {HTMLElement} pn 父元素
				 * @param {Boolean?} horizontal 是否横向并排
				 */
				centerChilds:function(pn,horizontal){
					//加起来所有子元素的outerWidth,设置第一个margin-left和最后一个的margin-right为0
					//var cns=this.container.getItemContainer().childNodes;
					var cns=pn.childNodes;
					if(cns.length>0){
						//水平
						if(horizontal){
							var mw=0;
							for(var i=0;i<cns.length;i++){
								var cn=cns[i];
								var s=EG.getSize(cn);
								if(i==0){
									mw+=s.outerWidth-s.marginLeft-s.marginRight;
								}else{
									var sl=EG.getSize(cns[i-1]);
									//取出最大的magrin重叠区
									var lm=Math.max(sl.marginRight,s.marginLeft);
									mw+=lm+s.outerWidth-s.marginRight-s.marginLeft;
								}
							}

							var m=parseInt((EG.getSize(pn).innerWidth-mw)/2);
							EG.css(cns[0],"margin-left:"+m+"px");
							EG.css(cns[cns.length-1],"margin-right:"+m+"px");
						}else{
							alert("暂时不支持");
						}
					}

				},

				/**
				 * 垂直居中
				 * @param {HTMLElement} pn 父元素
				 * @param {Boolean?} horizontal 是否横向并排
				 */
				middleChilds:function(pn,horizontal){
					var cns=pn.childNodes;
					var ps=EG.getSize(pn);
					if(cns.length>0){
						if(horizontal){
							//每个元素用垂直高度间隔的一半
							for(var i=0;i<cns.length;i++){
								var cn=cns[i];
								var s=EG.getSize(cn);
								var m=parseInt((ps.innerHeight-(s.outerHeight-s.marginTop-s.marginBottom))/2)
								EG.css(cn,"margin-top:"+m+"px;margin-bottom:"+m+"px");
							}
						}else{
							alert("暂不支持");
						}
					}
				},

				topChilds:function(pn,horizontal){
					var cns=pn.childNodes;
					var ps=EG.getSize(pn);
					if(cns.length>0){
						if(horizontal){
							//每个元素用垂直高度间隔的一半
							for(var i=0;i<cns.length;i++){
								var cn=cns[i];
								var s=EG.getSize(cn);
								var m=parseInt((ps.innerHeight-(s.outerHeight-s.marginTop-s.marginBottom)))
								EG.css(cn,"margin-top:"+0+"px;margin-bottom:"+m+"px");
							}
						}else{
							alert("暂不支持");
						}
					}
				},

				bottomChilds:function(pn,horizontal){
					var cns=pn.childNodes;
					var ps=EG.getSize(pn);
					if(cns.length>0){
						if(horizontal){
							//每个元素用垂直高度间隔的一半
							for(var i=0;i<cns.length;i++){
								var cn=cns[i];
								var s=EG.getSize(cn);
								var m=parseInt((ps.innerHeight-(s.outerHeight-s.marginTop-s.marginBottom)))
								EG.css(cn,"margin-top:"+m+"px;margin-bottom:"+0+"px");
							}
						}else{
							alert("暂不支持");
						}
					}
				},


				/*************************************************************
				 * 						位置控制
				 *************************************************************/

				/**
				 * 使元素横向居中
				 * (容器innerWidth - 元素可视宽度)/2
				 * @param {HTMLElement} ele 元素
				 * @param {HTMLElement?} ct 相对容器
				 */
				center:function(ele, ct) {
					var l=Math.abs(parseInt((ct||ele.parentNode).clientWidth - EG.getSize(ele).outerWidth) / 2);
					ele.style.position="absolute";
					ele.style.left = l + "px";
				},

				/**
				 * 使元素垂直居中
				 * (容器innerHeight - 元素可视高度)/2
				 * @param {HTMLElement} ele 元素
				 * @param {HTMLElement?} ct 相对容器
				 */
				middle:function(ele, ct) {
					var l=Math.abs(parseInt((ct||ele.parentNode).clientHeight - EG.getSize(ele).outerHeight) / 2);
					ele.style.position="absolute";
					ele.style.top = l + "px";
				},

				/**
				 * 使元素居左
				 * @param {HTMLElement} ele 元素
				 * @param {HTMLElement?} ct 相对容器
				 */
				left:function(ele, ct) {
					ele.style.position="absolute";
					ele.style.left = "0px";
				},

				/**
				 * 使元素居右
				 * @param {HTMLElement} ele 元素
				 * @param {HTMLElement?} ct 相对容器
				 */
				right:function(ele, ct) {
					ele.style.position="absolute";
					ele.style.right = "0px";
				},

				/**
				 * 使元素居上
				 * @param {HTMLElement} ele 元素
				 * @param {HTMLElement?} ct 相对容器
				 */
				top:function(ele,ct) {
					ele.style.position="absolute";
					ele.style.top = "0px";
				},

				/**
				 * 使元素居上
				 * @param {HTMLElement} ele 元素
				 * @param {HTMLElement?} ct 相对容器
				 * @param {Boolean?} useMargin 使用margin
				 */
				bottom:function(ele,ct) {
					ele.style.position="absolute";
					ele.style.bottom = "0px";
				},

				/**
				 * 撑满对象
				 * @param ele
				 * @param ct
				 */
				full:function(ele, ct) {
					if(!ct) ct=ele.parentNode;
					if(!ct) throw new Error("EG.Style#full:父元素不能为空");
					ele.style.width=ct.clientWidth+"px";
					if(ct==EG.getBody()){
						ele.style.height=Math.max(
								(EG.doc.documentElement?EG.doc.documentElement.clientHeight:EG.getBody().clientHeight),
								EG.getBody().clientHeight)
							+"px";
					}else{
						ele.style.height=ct.clientHeight+"px";
					}
				},

				/**
				 * 移动到某坐标
				 * @param ele
				 * @param pos
				 */
				moveTo:function(ele, pos){
					if(pos.x) Style.css(ele,"left:"+pos.x+"px");
					if(pos.y) Style.css(ele,"top:"+pos.y+"px");
				},

				/**
				 * 设置透明度
				 * @param ele
				 * @param val
				 */
				capcity:function(ele,val){
					if (EG.doc.all) {
						ele.style.filter = "alpha(opacity=" + parseInt(val) + ")";
					} else {
						ele.style.opacity = parseInt(val) / 100;
					}
				},

				/**
				 * 渐变
				 *
				 * @param ele 对象
				 * @param start 起始值
				 * @param end 结束值
				 * @param callback 回调
				 * @param speed 速度
				 */
				fade:function(ele, start, end, callback, speed) {
					if (!speed) speed = 20;
					var _a = (start > end) ? -5 : 5;
					Style.capcity(ele,start + _a);
					if (start == end) {
						if (!callback) return;
						return callback();
					}
					window.setTimeout(function() {
						Style.fade(ele, start + _a, end, callback, speed);
					}, speed);
					return null;
				},

				/**
				 * 设置样式类
				 * 支持样式名前缀,例如 (el,[aa,bb],pre) 形成 pre-aa pre-bb
				 * @param ele
				 * @param cls
				 * @param {String?} clsPre
				 */
				setCls:function(ele,cls,clsPre){
					if(cls==null) return;
					clsPre=clsPre?(clsPre+"-"):"";
					if(!EG.isArray(cls)) cls=[cls];
					if(cls.length==0) return;
					var s="";
					for(var i=0;i<cls.length;i++){
						if(i!=0) s+=" ";
						s+=clsPre+cls[i];
					}
					ele.className = s;
				},

				/**
				 * 添加样式
				 * @param ele 元素
				 * @param cls 样式
				 */
				addCls:function(ele,cls){
					var cn=ele.className||"";
					var clss = cn.split(' ');
					if(EG.Array.has(clss,cls)) return;
					for(var i=clss.length;i>=0;i--){
						if(clss[i]==""){
							EG.Array.del(clss,i);
						}
					}
					clss.push(cls);
					ele.className = clss.join(' ');
				},

				/**
				 * 移除样式
				 * @param ele 元素
				 * @param cls 样式
				 */
				removeCls:function(ele,cls){
					var cn=ele.className||"";
					var clss = cn.split(' ');
					if(!EG.Array.has(clss,cls)) return;
					EG.Array.remove(clss,cls);
					ele.className = clss.join(' ');
				}
			}
		};
	});

	var Style=EG.Style;
	Style.vh=window.screen.height/100;
	Style.vw=window.screen.width/100;

	EG.getSize		=Style.getSize;
	EG.debugSize	=Style.debugSize;
	EG.css			=Style.css;
	EG.hide			=Style.hide;
	EG.show			=Style.show;
	EG.setCls		=Style.setCls;
	// 创建动态样式表
	EG.onload(function() {
		Style.element = EG.doc.createElement("style");
		Style.element.type = "text/css";
		EG.doc.getElementsByTagName("HEAD").item(0).appendChild(Style.element);
		//IE6附加
		if(EG.Browser.isIE6()){
			Style.c.dv+="*display:inline;zoom:1;";
		}
	});
	
})();

/**
 * @class EG.$Q
 * <AUTHOR>
 * JQuery仿真
 */
(function(){
	EG.define("EG.$Q",{
		constructor:function(selector){
			return new EG.$Q.prototype._init(selector);
		},
		/**
		 * 初始化
		 * @param {String|HTMLElement} selector 选择表达式
		 * @private
		 */
		_init:function(selector){
			this.ele=EG.$(selector);
			return this;
		},
		/**
		 * 获值&&设值
		 * @param {String} value 数值
		 */
		val:function(value){
			if(arguments.length==0) return EG.DOM.getValue(this.ele);
			else return EG.DOM.setValue(this.ele,value);
		},
		/**
		 * 显示
		 */
		show:				function()			{EG.show(this.ele);return this;},
		/**
		 * 隐藏
		 */
		hide: 				function()			{EG.hide(this.ele);return this;},
		/**
		 * 居中
		 */
		center:				function(ct)		{EG.Style.center(this.ele,ct);return this;},
		/**
		 * 是否有指定的样式
		 */
		hasClass:			function(className)	{return this.ele.className.indexOf(className)>=0;},
		/**
		 * 移除所有子节点
		 */
		removeChilds:		function()			{return EG.DOM.removeChilds(this.ele);},
		/**
		 * 增加子节点
		 * @param {HTMLElement} obj 子节点
		 */
		appendChild:		function(obj)		{this.ele.appendChild(obj);return this;},
		/**
		 * 设置innerHTML
		 * @param {String} html html
		 */
		innerHTML:			function(html)		{this.ele.innerHTML=html;return this;},
		/**
		 * 获取指定子
		 * @param {Number} n 数值
		 */
		getChild:			function(n)			{return this.ele.childNodes[n];},
		/**
		 * 设置样式
		 * @param {String} style 样式
		 */
		css:				function(style)		{EG.css(this.ele,style);return this;}
	});

	//增加事件
	(function(){
		var events=["click","dblclick","mouseover","mouseout"];
		for(var i=0;i<events.length;i++){
			EG.$Q.prototype[events[i]]=function(fn){
				var me=this;
				EG.bindEvent(this.ele,function(){
					fn.apply(me);	
				});
				return this;
			};
		}
	})();

    /**
     * 挂接
     */
    EG.$Q.prototype._init.prototype=EG.$Q.prototype;
})();


/**
 * @class EG.MMVC
 * <AUTHOR>
 * Nobject.org的MiniMVC框架的远程请求操作类
 */
(function(){
	EG.define("EG.MMVC",{
		statics:{
			mmvcPath:"/mmvc/"
			,
			/**
			 * 获取MMVC请求路径集合
			 * @param {String?} uri
			 * @returns {Object}
			 * */
			getPath:function(uri){
				if(uri==null) uri=EG.MMVC.mmvcPath;
				return {
					call		:uri+"jsonrpc/call",
					connect		:uri+"push/connect",
					moditor		:uri+"utils/profile",
					upload		:uri+"upload",
					download	:uri+"download"
				};
			}
			,
			/**
			 * 远程调度
			 * 是:正确结果->[0,返回结果],错误结果->[1,{exClass:XXX,exMsg:XXX}EG.MMVC.Exception]
			 * 否:正确结果->返回结果,错误结果->EG.MMVC.Exception
			 * @param {Object} cfg 请求参数
			 */
			call:function(cfg) {//TODO 将参数简化为onException,name,method,params,post
				cfg=cfg||{};
				var muti		=cfg["muti"],
					rpc			=cfg["rpc"],
					method		=cfg["method"],
					params		=EG.unnull(cfg["params"],[]),
					exHandler	=cfg["exHandler"],
					cb			=cfg["callback"],
					httpMethod	=EG.unnull(cfg["httpMethod"],"POST"),
					mutiReturn	=cfg["mutiReturn"]||"map",
					useRandom	=EG.unnull(cfg["useRandom"],true),
					erhandler	=null
				;

				if(!muti&&EG.String.isEmpty(rpc)) 		throw new Error("EG#call:rpc不能为空");
				if(!muti&&EG.String.isEmpty(method)) 	throw new Error("EG#call:method不能为空");

				var url			=cfg["callPath"]||EG.MMVC.getPath().call;
				
				//参数转换
				var strParams="";
				if(muti){
					strParams=(mutiReturn!="map"?"mutiReturn="+mutiReturn+"&":"")+"muti="+EG.Ajax.javaURLEncoding(EG.Tools.toJSON(muti));
				}else{
					strParams="rpc="+rpc+"&method="+method+"&params="+EG.Ajax.javaURLEncoding(EG.Tools.toJSON(params));
				}
						
				var content		=null;

				//使用随机码使得URL路径或内容每次都不一样
				//TODO get方式暂时不支持
				if(useRandom) strParams+="&random="+new Date().getTime()+"-"+Math.random()*1000;

				if(httpMethod=="GET") url+="?"+strParams;
				else content	=strParams;

				//处理异常
				var handleEx=function(ex){
					if(EG.MMVC.exClassHandlers[ex["exClass"]]){
						return EG.MMVC.exClassHandlers[ex["exClass"]](ex);
					}else{
						if(exHandler) return exHandler(ex);
						else if(EG.MMVC.defExHandler){
							return EG.MMVC.defExHandler(ex);
						}else{
							throw new Error(ex.exMsg);
						}
					}
				};

				//回调处理
				var callback=function(resText,req){
					var obj=null;
					if(resText != null && resText != "")  eval("obj=" + resText + ";");
					//打包分拆
					if(obj!=null){
						if(!EG.isArray(obj)) throw new Error("结构非数组:"+resText);
						if(obj[0]===0){
							return cb(obj[1]);
						}else{
							return handleEx(new EG.MMVC.Exception(obj[1]["exClass"],obj[1]["exMsg"]));
						}
					}else{
						return cb(obj);
					}
				};
				return EG.Ajax.send({
					url		:url,
					method	:httpMethod,
					content	:content,
					callback:callback,
					erhandler:erhandler
				});
			}
			,
			/**
			 * 异常类型处理器
			 */
			exClassHandlers:{}
			,
			/**
			 * 默认异常处理器
			 */
			defExHandler:null
			,
			/**
			 * 发起一个直连请求
			 * @param {Object} cfg 连接参数
			 */
			connect:function(cfg){
				cfg=cfg||{};
				var actionFrame=cfg["actionFrame"];
				if(!actionFrame) actionFrame=EG.DOM.getActionFrame();
				actionFrame.src=EG.MMVC.getPath().connect;
			}
			,
			/**
			 * 发起一个下载请求
			 * @param {Object} cfg 连接参数
			 */
			download:function(cfg){
				var actionFrame	=cfg["actionFrame"];
				var policy		=cfg["policy"];
				var params		=cfg["params"]||{};
				var ps="";
				for(var key in params){
					ps+="&"+key+"="+EG.Ajax.javaURLEncoding(params[key]);
				}
				if(!actionFrame) actionFrame=EG.DOM.getActionFrame();

				actionFrame.src=EG.MMVC.getPath().download+"?policy="+policy+ps;
			}
		}
	});

	/**
	 * MMVC 异常类型
	 * @param {String} exClass 异常类型
	 * @param {String} exMsg 异常消息内容
	 * @constructor
	 */
	EG.MMVC.Exception=function(exClass,exMsg){
		this.exClass = exClass;this.exMsg = exMsg;
	};

	//快捷
	EG.call=EG.MMVC.call;
})();

(function(){
	EG.define("EG.Anim",function(){
		return {
			/**
			 * @alias EG.Anim
			 */
			statics:{
				/**
				 * 添加动画
				 * @param ele
				 * @param anims
				 */
				add:function(ele,anims){
					if(!EG.Array.isArray(anims)){
						anims=[anims];
					}

					if(!ele.anims){
						ele.anims=[];
					}

					for(var i=0;i<anims.length;i++){
						if(EG.Array.has(ele.anims,anims[i])) continue;
						ele.anims.push(anims[i]);
					}

				},

				/**
				 * 删除动画
				 * @param ele
				 * @param {?Array} anims
				 */
				remove:function(ele,anims){

					if(anims==null&&ele.anims!=null) anims=ele.anims;


					if(!EG.Array.isArray(anims)){
						anims=[anims];
					}

					if(!ele.anims){
						ele.anims=[];
					}

					for(var i=0;i<anims.length;i++){
						EG.Array.remove(ele.anims,anims[i]);
					}
				},

				/**
				 * 停止动画
				 * @param ele
				 */
				stop:function(ele){
					ele.fn_stopAnima.apply(ele);
				},

				/**
				 * 在播放前隐藏
				 */
				beforePlay:function(ele){
					var cs=EG.Style.current(ele);
					ele.oldOpacity=ele.style["opacity"]||(cs?cs["opacity"]:1);

					EG.css(ele,"opacity:0");
				},

				/**
				 * 播放动画
				 * @param ele
				 */
				play:function(ele){
					if(!ele.oldOpacity){
						EG.Anim.beforePlay(ele);
					}

					for(var i=0;i<ele.anims.length;i++){
						EG.Style.addCls(ele,ele.anims[i]);
					}

					if(!ele.fn_stopAnima){
						ele.fn_stopAnima=function(){
							for(var i=0;i<this.anims.length;i++){
								EG.Style.removeCls(this,this.anims[i]);
							}
						};
						var animEndEventNames = {
							'WebkitAnimation' 	: 'webkitAnimationEnd',
							'OAnimation' 		: 'oAnimationEnd',
							'msAnimation' 		: 'MSAnimationEnd',
							'animation' 		: 'animationend'
						};
						if(!Modernizr) return;
						var animEndEventName = animEndEventNames[ Modernizr.prefixed( 'animation' ) ];
						EG.Event.bindEvent(ele,animEndEventName,ele.fn_stopAnima);
					}

					var op=ele.oldOpacity||1;
					EG.css(ele,"opacity:"+op);
				},


				moveout:function(ele,direct,speed){

				},
				movein:function(){

				},
				/**
				 *
				 * @param ele
				 * @param direct
				 * @param size
				 * @param callback
				 * @param period
				 * @param fps
				 * @param _s
				 * @returns {*}
				 */
				move:function(ele,direct,size,callback,period,fps,_s){
					if(!period) period = 1;
					if(!fps) fps=25;
					var timeout=period/fps;
					if(_s==null) _s=0;
					_s +=size/fps;
					if(_s>size) _s=size;

					if (_s<size) {
						if(!EG.Array.isArray(ele)){
							ele=[ele];
						}
						for(var i=0;i<ele.length;i++){
							EG.css(ele[i],"margin-"+direct+":"+(-_s)+"px");
						}
						window.setTimeout(function() {
							EG.Anim.move(ele,direct,size,callback,period,fps,_s);
						},timeout);
					}else{
						if (!callback) return;
						return callback();
					}
				},

				/**
				 * 转圈
				 * @param ele
				 * @param period
				 * @param speed
				 * @param clockwise 顺时针
				 * @param fps FPS
				 * @param _s
				 * @param _t
				 */
				rotate:function(ele,period,speed,clockwise,fps,_s,_t){
					var timeout;
					if(!fps) fps=25;
					if(!_t){
						_t=new Date().getTime();
						_t+=period;
					}

					if(new Date().getTime()>=_t){
						alert("OVER");
						return;
					}
					timeout=1000/fps;
					if(_s==null) _s=0;
					_s+=speed;
					_s=_s%360;

					EG.css(ele,
						"-webkit-transform: rotate(-"+_s+"deg);"+
						"-moz-transform: rotate(-"+_s+"deg);"+
						"-ms-transform: rotate(-"+_s+"deg);"+
						"-o-transform: rotate(-"+_s+"deg);"+
						"transform: rotate(-"+_s+"deg);"
					);

					window.setTimeout(function() {
						EG.Anim.rotate(ele,period,speed,clockwise,fps,_s,_t);
					},timeout);

				}
			}
		};
	});

})();

