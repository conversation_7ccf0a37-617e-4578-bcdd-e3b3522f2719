(function(){
	/**
	 * EG.UI UI包
	 */
	EG.define("EG.UI",{
		statics:{
			//皮肤
			skin:"default",
			/**
			 * 获取皮肤路径
			 */
			getSkinPath:function(ui) {
				return EG.basePath + "/skins/" + EG.UI.skin + "/" + ui;
			},
			sheet:null,
			/**
			 * 设置Skin
			 * @param skin
			 */
			setSkin:function(skin) {
				if(!EG.UI.sheet){
					EG.UI.sheet=EG.Style.createSheet("");
				}
				EG.UI.sheet.href=EG.basePath + "/skins/" + skin + "/ui.css";
				EG.UI.skin=skin;
			},
			//全局组件索引
			GITEMIDX:0,
			//全局组件集合
			GITEMS:{}
		}
	});

	//是否正在变化尺寸
	EG.RESIZING=false;

	/**
	 * 设置默认样式
	 */
	EG.onload(function(){
		//EG.UI.setSkin(EG.UI.skin);
	});
})();

/**
 * @class EG.ui.Item
 * <AUTHOR>
 * 组件
 */
(function(){
	EG.define("EG.ui.Item",[

	],function(ME){
		return {
			config:{
				/** @cfg {?HTMLElement} renderTo 被添加到某节点下，并被渲染 */
				renderTo	:null,
				/** @cfg {?String} width ID */
				id			:null,
				/** @cfg {?String|?Number} width 宽度 */
				width		:null,
				/** @cfg {?String|?Number} height 高度 */
				height		:null,
				/** @cfg {?String} style 样式 */
				style		:null,
				/** @cfg {?String} cls CSS样式类 */
				cls			:null,
				/** @cfg {?String} region Layout布局时使用的区位标识 */
				region		:null,
				/** @cfg {?Boolean} hidden 是否隐藏 */
				hidden		:false,
				/** @cfg {?String} animate 动画 */
				animate		:null
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				//初始化配置
				this.initItem(cfg);
				//创建Element
				this.build();

				//绑定Element与Item
				this.getElement().item=this;

				//设置ID
				if(this.id) this.getElement().id = this.id;
				//设置样式
				ME.setStyle(this);
				//执行创建后动作
				this.afterBuild();

				//添加动画
				this.setAnimate(this.animate);

				//renderTo
				ME.doRenderTo(this);

				//自动隐藏
				if(this.hidden) EG.hide(this.getElement());
			},

			/**
			 * 设置动画
			 */
			setAnimate:function(animate){
				this.animate=animate;
				if(this.animate){
					var el=this.getElement();
					EG.Anim.remove(el)
					EG.Anim.add(el,this.animate.split(" "));
				}
			},

			/**
			 * 创建后的动作
			 */
			afterBuild:function(){},

			/**
			 * 获取要样式化的元素
			 * @returns {HTMLElement}
			 */
			getStyleEle:function(){
				return this.getElement();
			},

			/**
			 * 获取Element
			 * @returns {HTMLElement}
			 */
			getElement	:function(){
				return this.element;
			},

			/** 渲染 */
			render		:function(){

			},

			/** 渲染外框，为autoSize使用 */
			renderOuter	:function(){
				ME.fit(this);
				this.saveOuterSize();
			},

			saveOuterSize:function(){
				var overflow=EG.Style.current(this.getElement()).overflow;
				EG.css(this.getElement(),"overflow:hidden;");
				this._outerSize=this.getSize();
				EG.css(this.getElement(),"overflow:"+overflow+";");
			},

			/**
			 * 初始化Item
			 * @param {Object} config 配置
			 */
			initItem:function(config){
				this.initConfig(config);
				this.oWidth		=this.width;	//原宽
				this.oHeight	=this.height;	//原高
				this.pItem		=null;			//父组件
			},

			/**
			 * 获取原始尺寸
			 * @return {*}
			 */
			getOWidth:function(){
				return this.oWidth;
			},

			/**
			 * 获取原始宽度
			 * @return {*}
			 */
			getOHeight:function(){
				return this.oHeight;
			},

			/**
			 * 获取元素尺寸
			 * @return {Object}
			 */
			getSize:function(){
				return EG.getSize(this.getElement());
			},

			/**
			 * 获取内部高
			 * @return {*}
			 */
			getInnerHeight:function(){
				return EG.getSize(this.getElement()).innerHeight;
			},

			/**
			 * 获取内部宽
			 * @return {*}
			 */
			getInnerWidth:function(){
				return EG.getSize(this.getElement()).innerWidth;
			},

			/**
			 * 构建
			 */
			build:function(){
				this.element 		=EG.CE({tn : "div",item:this});
			},

			/**
			 * 销毁
			 */
			destroy:function(){

			},

			/**
			 * 设置Hidden
			 * @param hidden
			 */
			setHidden:function(hidden){
				this.hidden=hidden;
				if(this.hidden){
					EG.hide(this.getElement());
				}else{
					EG.show(this.getElement());
				}
			},

			/**
			 * 是组件
			 */
			isItem:true,

			statics:{

				itemClasses:{},

				/**
				 * 注册部件
				 * @param {String} type 类型
				 * @param {Class} itemClass 部件类
				 */
				regist : function(type, itemClass) {
					ME.itemClasses[type] = itemClass;
				},

				/**
				 * 创建组件
				 * @param {String} xtype 类型
				 * @param {Object} cfg 配置
				 * @return {*}
				 */
				create:function(xtype,cfg){
					if(!xtype) xtype="panel";
					var itemClass=EG._alias[xtype];
					if(!itemClass) throw new Error("EG.ui.Item#create:不支持类型:" + xtype);
					return new itemClass(cfg);
				},

				/**
				 * 设置尺寸:宽
				 * @param {HTMLElement} element 元素
				 * @param {Number|String} width 宽
				 */
				setWidth:function(element,width){
					ME.setSize(element,width,"width");
				},

				/**
				 * 设置尺寸:长
				 * @param {HTMLElement} element 元素
				 * @param {Number|String} height 高
				 */
				setHeight:function(element,height){
					ME.setSize(element,height,"height");
				},

				/**
				 * 设置样式
				 * @param {EG.ui.Item} item 部件
				 */
				setStyle:function(item){
					//设定样式类
					if(item.cls) EG.setCls(item.getStyleEle(),item.cls);
					//设置样式
					if(item.style) EG.css(item.getStyleEle(),item.style);
				},

				/**
				 * 初始化Item
				 * @param {EG.ui.Item} item 部件
				 */
				initItem:function(item){
					//自动renderTo
					if(item.renderTo){
						if(item.element.parentNode==null){
							if(ME.isItem(item.renderTo)){
								item.renderTo.addItem(item);
							}else if(EG.DOM.isElement(item.renderTo)){
								item.renderTo.appendChild(item.element);
							}
						}
						item.render();
					}
				},

				/**
				 * 设置尺寸
				 * @param {HTMLElement} element Element
				 * @param {String|Number} value 数值
				 * @param {String} type 种类
				 */
				setSize:function(element,value,type){
					if (value != null) {
						if (typeof (value) == "number"){
							EG.css(element, type+":" + value + "px");
						}else{
							EG.css(element, type+":" + value);
						}
					} else{
						EG.css(element, type+":100%");//TODO 取消自动放大
					}
				},

				/**
				 * 计算出element要适应的大小,保持原样式的padding,margin,bodrer值
				 * @param {Object} cfg 配置
				 * @return {Object}
				 */
				fit:function(cfg){
					if(ME.isItem(cfg)) cfg={item:cfg};
					var item		=cfg["item"],
						element		=cfg["element"];

					if(!element){
						if(item) element=item.element;
						else throw new Error("fit:没有element");
					}

					//原始尺寸,必须含有innerWidth,outerWidth||innerHeight,outerHeight
					var sSize=cfg["sSize"]||EG.getSize(element);

					//预期尺寸,必须是width和height格式,
					var dSize=cfg["dSize"];
					if(!dSize){
						if(item&&(item.width!=null||item.height!=null)){	//使用item的高宽作为预期尺寸
							dSize={width:item.width,height:item.height};
						}else{												//默认撑满
							dSize={width:EG.unnull(cfg["width"],"100%"),height:EG.unnull(cfg["height"],"100%")};
						}
					}

					//父尺寸
					var pSize=cfg["pSize"]||EG.getSize(element.parentNode);//EG.getSize(item&&item.pItem?item.pItem.getElement():element.parentNode); //TODO
					if(pSize["width"]==null &&pSize["innerWidth"]!=null) 	pSize["width"]=pSize["innerWidth"];
					if(pSize["height"]==null&&pSize["innerHeight"]!=null) 	pSize["height"]=pSize["innerHeight"];

					//尺寸设置种类
					var type=cfg["type"]||"all",w,h;

					//TODO dSize["width"]!="auto" 是否应该换为item.getOWidth()!="auto"

					//目标尺寸应为内部尺寸减去boreder,margin,scrollWidth之后的尺寸

					//设宽
					if(dSize["width"]!=null&&dSize["width"]!="auto"&&(type=="all"||type=="width")){
						w=EG.Style.size2Num(dSize["width"],pSize["width"])-(sSize["outerWidth"]-sSize["innerWidth"]-sSize["vScrollWidth"]);
						if(w<0) w=0;  //TODO 等待处理负数
						EG.css(element,{
							width:w+"px"
						});
					}

					//设高
					if(dSize["height"]!=null&&dSize["height"]!="auto"&&(type=="all"||type=="height")){
						h=EG.Style.size2Num(dSize["height"],pSize["height"])-(sSize["outerHeight"]-sSize["innerHeight"]-sSize["hScrollWidth"]);
						//if(element.id=="slt2") alert("h:"+h);
						if(h<0) h=0;
						EG.css(element,{
							height:h+"px"
						});
					}

					return {
						width:w,
						height:h,
						pSize:pSize
					};
				},

				/**
				 * 用一个元素包裹另一个元素
				 * @param {Object} cfg 配置
				 */
				pack:function(cfg){
					var item		=cfg["item"];
					var pEle		=cfg["pEle"]||(item?item.getElement():null);
					var cEle		=cfg["cEle"];	//TODO 参考Default的autoSize
					var width		=cfg["width"];
					var height		=cfg["height"];
					var pSize		=cfg["pSize"]||EG.getSize(pEle);
					if(cEle&&(width==null||height==null)){
						var cS=EG.getSize(cEle);
						if(width==null) 	width=cS.outerWidth;
						if(height==null) 	height=cS.outerHeight;
					}

					var rs={};

					if(width!=null) 	{width	=(pSize.paddingLeft+width+pSize.paddingRight);	EG.css(pEle,"width:"+width+"px");		rs["width"]=width;}
					if(height!=null) 	{height	=(pSize.paddingTop+height+pSize.paddingBottom);	EG.css(pEle,"height:"+height+"px");		rs["height"]=height;}

					return rs;
				},

				/**
				 * 是否为item
				 * @param {EG.ui.Item} item 部件
				 */
				isItem:function(item) {
					return item&&typeof(item.getElement)=="function";
				},

				/**
				 * 将Item添加到renderTo指向的对象后执行render
				 * @param {EG.ui.Item} item 部件
				 */
				doRenderTo:function(item){
					if(item.renderTo){
						EG.$(item.renderTo).appendChild(item.getElement());
						item.render();
					}
				},

				/**
				 * 被继承后的特殊处理
				 * @param {Object} clazz 类
				 */
				afterExtend:function(clazz){
					//render Proxy
					(function(){
						var f=clazz.prototype.render;
						if(f){
							clazz.prototype.render=function(){
								//如果未添加到节点中，不渲染
								if(!EG.DOM.contains(this.getElement())){
									return;
								}

								//如果父节点已隐藏，不渲染
								if(EG.Style.isHide(this.getElement().parentNode,true)) return;

								f.apply(this,arguments);
							};
						}
					})();
				},

				/**
				 * 父元素为body时设其样式为固定
				 * @param el
				 */
				fixBody:function(el){
					var pElement=el.parentNode;
					if (pElement == EG.getBody()) {
						EG.css(EG.getBody(),"margin:0px;padding:0px;width:100%;height:100%");
						EG.css(el, "position:absolute;left:0px;top:0px;");
					}
				},

				/**
				 * 寻找ITEM
				 * @param id
				 * @return {*|Function|*|*|*}
				 */
				$:function(id){
					return EG.$(id).item;
				},

				/**
				 * 获取组件的路径
				 * @param item
				 */
				getPath:function(item){
					var s=item.getClass()._className;
					while((item=item.pItem)){
						s=item.getClass()._className+"("+(item.id?item.id:"")+")>"+s;
					}
					return s;
				}
			}
		};
	});
})();

/**
 * @class EG.ui.Container
 * <AUTHOR>
 * @extends EG.ui.Item
 * 容器
 * 可以添加子Item,children，拥有布局器
 */
(function(){
	EG.define("EG.ui.Container",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			extend:Item,

			config:{
				/** @cfg {String?|Object?} layout 布局 */
				layout		:"default",
				/** @cfg {Array?} items 子组件 */
				itemsConfig	:[],
				/** @cfg {Array?} cns 子元素 */
				cnConfig	:[],
				/** @cfg {String?} innerHTML HTML */
				innerHTML	:null,
				/** 渲染外层 */
				isRenderingOuter:false
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.items=[];

				this.callSuper([cfg]);
			},

			/**
			 * 创建后执行
			 */
			afterBuild:function(){
				//布局管理器
				this.setLayout(this.layout,false);

				//添加子组件
				if (this.itemsConfig.length > 0){
					this.addItem(this.itemsConfig,false);
					//添加子元素
				}else if(this.cnConfig.length > 0) {
					this.addChildren(this.cnConfig,null,false);
					//innerHTML
				}else if(this.innerHTML){
					this.getItemContainer().innerHTML=this.innerHTML;
				}
			},

			/**
			 * 获取Item容器
			 * @return {HTMLElement}
			 */
			getItemContainer:function(){
				return this.element;
			},

			/**
			 * 添加组件
			 * @param {Object|EG.ui.Item|Array} item 组件
			 * @param {Boolean?} autoLayout 自动布局
			 * @param {Number?} idx 添加到哪个位置
			 */
			addItem:function(item,autoLayout,idx) {
				if(typeof(autoLayout)=="undefined") autoLayout=true;

				//转换为数组
				var items=(!EG.isArray(item))?[item]:item;
				var rs=[];
				for (var i=0,il=items.length;i<il;i++) {
					item = items[i];
					if (EG.isLit(item)) {
						item=Item.create(item.xtype,item);
					}
					item.pItem=this;

					if(idx>=0){
						EG.Array.insert(this.items,idx,item);
					}else{
						this.items.push(item);
					}


					//若为layout添加模式则返回:TableLayout
					if(this.layoutManager.addOnLayout) continue;
					//添加子
					EG.DOM.addChildren(this.getItemContainer(),item.getElement(),idx);
					//.appendChild();

					rs.push(item);
				}
				if(autoLayout){
					if(!this.isPItemAuto()){
						this.doLayout();
					}
				}
				return rs.length>1?rs:rs[0];
			},

			/**
			 * 移除Item
			 * @param item
			 * @param {Boolean?} autoLayout 是否重新布局
			 */
			removeItem:function(item,autoLayout){
				if(autoLayout==null) autoLayout=true;

				if(typeof(item)=="number"){
					item=EG.Array.get(this.items,item);
				}

				//DOM移除
				this.getItemContainer().removeChild(item.getElement());
				//数组移除
				EG.Array.remove(this.items,item);
				//清空关系
				item.pItem=null;

				if(autoLayout){
					if(!this.isPItemAuto()){
						this.doLayout();
					}
				}
			},

			/**
			 * 添加子节点
			 * @param {Node} child 子节点
			 * @param {Number?} idx 插入坐标
			 * @param {Boolean?} layout 是否布局
			 */
			addChildren:function(child,idx,layout) {
				if(layout==null) layout=true;
				EG.DOM.addChildren(this.getItemContainer(),child,idx);
				if(layout) this.doLayout();
			},

			/**
			 * 渲染
			 */
			render:function() {

				//修父BODY样式
				EG.ui.Item.fixBody(this.element);
				//渲染尺寸
				Item.fit(this);

				if(this.animate){
					EG.Anim.beforePlay(this.getElement());
				}
				//执行布局
				if(this.items.length>0||this.layoutManager.force){
					this.doLayout();
				}

				//播放动画
				if(this.animate){
					EG.Anim.play(this.getElement());
				}
			},

			/** 渲染外框，为autoSize使用 */
			renderOuter	:function(){
				this.isRenderingOuter=true;
				Item.fit(this);
				if(this.isAutoWidth()||this.isAutoHeight()){
					//执行布局
					if(this.items.length>0||this.layoutManager.force){
						this.doLayout();
					}
				}

				this.saveOuterSize();
				this.isRenderingOuter=false;
			},

			/**
			 * 获取ITEM
			 * @param {Object|Number} cfg 参数
			 */
			getItem:function(cfg) {
				if(typeof(cfg)=="number")	{return this.items[cfg];}
				else if(this.layoutManager)	{return this.layoutManager.getItem(cfg);}
				else throw new Error("无法获取Item,参数不对或布局器不存在");
			},

			/**
			 * 设置布局
			 * @param layout 布局
			 * @param autoLayout 自动执行布局
			 */
			setLayout:function(layout,autoLayout){

				if(autoLayout==null) autoLayout=true;

				this.layout=layout;

				this.layoutManager 	=EG.ui.Layout.create(this.layout,this);

				//自动执行布局
				if(autoLayout) this.layoutManager.doLayout();
			},

			/**
			 * 执行布局
			 */
			doLayout:function() {
				//COMPAT-CHROME:[被撑时不会自动刷新]
				var oOverflow=EG.Style.current(this.element).overflow;
				EG.css(this.element,"overflow:hidden");

				this.layoutManager.doLayout();

				EG.css(this.element,"overflow:"+oOverflow);
			},

			isPItemAuto:function(){
				//如果有父组件，自身是Auto尺寸并且自身尺寸发生变化，父组件重新渲染
				if(this.pItem){
					var pWA=this.pItem.isAutoWidth();
					var pHA=this.pItem.isAutoWidth();
					//父尺寸是非自动，切自身是自动，父元素重新布局
					if(!pWA&&!pHA&& (this.isAutoWidth()||this.isAutoHeight())){
						this.pItem.doLayout();
						return true;
						//如果父尺寸是自动，应在父方向寻找均为自动且最后一个为自动的父容器
					}else if(pWA||pHA){
						var p=this.pItem;
						while(p.pItem){
							if(!p.pItem.isAutoWidth()&&!p.pItem.isAutoHeight()){
								break;
							}else{
								p=p.pItem;
							}
						}
						p.doLayout();
						return true;
					}
				}
				return false;
			},

			/**
			 * 获取Left Item
			 * @retuns {EG.ui.Item}
			 **/
			getItemLeft		:function(){return this.getItem({region:"left"});},

			/**
			 * 获取Center Item
			 * @retuns {EG.ui.Item}
			 * */
			getItemCenter	:function(){return this.getItem({region:"center"});},

			/**
			 * 获取Right Item
			 * @retuns {EG.ui.Item}
			 * */
			getItemRight	:function(){return this.getItem({region:"right"});},

			/**
			 * 获取Top Item
			 * @retuns {EG.ui.Item}
			 * */
			getItemTop		:function(){return this.getItem({region:"top"});},

			/**
			 * 获取Bottom Item
			 * @retuns {EG.ui.Item}
			 * */
			getItemBottom	:function(){return this.getItem({region:"bottom"});},

			/**
			 * 是否为自动宽度
			 * @return {Boolean}
			 */
			isAutoWidth:function(){
				return this.getOWidth()=="auto";
			}
			,

			/**
			 * 是否为自动高度
			 * @return {Boolean}
			 */
			isAutoHeight:function(){
				return this.getOHeight()=="auto";
			},

			/****************************************************************
			 *
			 * 布局时，如果父元素的尺寸为自动，需要叠加子元素尺寸重新设置父的内尺寸
			 *
			 ****************************************************************/

			/**
			 * 设置内宽
			 *
			 * @param width 宽度为innerWidth
			 * @param {Boolean?} syc	将width同步为外宽
			 */
			setInnerWidth:function(width,syc){
				syc=EG.n2d(syc,true);
				EG.ui.Container.autoSize(this.getItemContainer(),width,"width");
				if(syc) this.width=this.getSize().outerWidth;
			},

			/**
			 * 设置内高
			 * @param height 高
			 * @param {Boolean?} syc	将height同步为外高
			 */
			setInnerHeight:function(height,syc){
				syc=EG.n2d(syc,true);
				EG.ui.Container.autoSize(this.getItemContainer(),height,"height");
				if(syc) this.height=this.getSize().outerHeight;
			},

			/**
			 * 获取内部高
			 * @return {*}
			 */
			getInnerHeight:function(){
				return EG.getSize(this.getItemContainer()).innerHeight;
			},

			/**
			 * 获取内部宽
			 * @return {*}
			 */
			getInnerWidth:function(){
				return EG.getSize(this.getItemContainer()).innerWidth;
			},

			/**
			 * 销毁
			 */
			destroy:function(){
				for(var i=0;i<this.items.length;i++){
					this.items[i].destroy();
				}
				//this.items=null;
				//EG.Array.clear(this.items);
			},
			/**
			 * 清空
			 */
			clear:function(){
				for(var i=0;i<this.items.length;i++){
					if(this.items[i].isContainer){
						this.items[i].clear();
					}
					this.items[i].destroy();
				}
				EG.DOM.removeChilds(this.getItemContainer());
				EG.Array.clear(this.items);
			},

			/**
			 * 是容器
			 */
			isContainer:true,

			statics:{
				/**
				 * 自动尺寸
				 * @param item
				 * @param size
				 * @param type
				 */
				autoSize:function(item,size,type){
					var ele=item.isItem?item.getElement():item;
					EG.css(ele,(type=="width"?"width":"height")+":"+size+"px");
				},
//			,
//			/**
//			 * 获取自动尺寸的组件
//			 * @param item
//			 * @param type
//			 * @return {*}
//			 */
//			getAutoParentItem:function(item,type){
//				if(type=="width") type="oWidth";
//				else if(type=="height") type="oHeight";
//
//				if(item.pItem&&item.pItem[type]=="auto"){
//					var pi=item;
//					while(pi.pItem&&pi.pItem[type]=="auto"){
//						pi=pi.pItem;
//					}
//					return pi;
//				}
//				return null
//			}
				afterExtend:function(clazz){
					//doLayout Proxy
					(function(){
						var f=clazz.prototype.doLayout;
						if(f){
							clazz.prototype.doLayout=function(){
								if(!EG.DOM.contains(this.getElement())){
									return;
								}
								f.apply(this,arguments);
							};
						}
					})();
				}
			}
		};
	});
})();

(function(){
	/******************************************************************************************************************
	 * 
	 *  EG.ui.Component 组件
	 * 
	 *******************************************************************************************************************/
	EG.ui.Component=function(){};
	
	EG.ui.Component.addChildren=function(cpt,cn){
		var cns=(!EG.isArray(cn))?[cn]:cn;
		for(var i=0,l=cns.length;i<l;i++){
			cn=cns[i];
			if(EG.isLit(cn)) cn=EG.CE(cn);
			cpt.getElement().appendChild(cn);
		}
	};

})();
/**
 * @class EG.ui.Drag
 * <AUTHOR>
 * @extends EG.ui.Container
 * Drag表单
 *
 * 污染:
 * 	首次调用时会对Body产生污染，绑定onmouseup和onmousemove事件
 * 	TODO 1.在startDrag的时候绑定事件，在endDrag的时候取消事件
 *  TODO 2.onmouseup和onmousemove事件只绑定在parent上
 * 注意:
 * 1.拖拽把手上的如果有按钮，onclick事件则不会被触发
 * 2.拖拽层只会创建一次，每次拖拽时会被移动到parent对象内
 * 3.parent是可移动的区域
 */
(function(){
	EG.define("EG.ui.Drag",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			config:{
				/** @cfg {Object} target 拖拽对象 */
				target			:null,
				/** @cfg {Object?} handle 拖拽把手，可以有多个 */
				handle			:null,
				/** @cfg {Object?} parent 父移动区域 */
				parent			:null,
				/** @cfg {Boolean?} moveTarget 是否移动目标对象 */
				moveTarget		:false,
				/** @cfg {Function?} beforeStartDrag 拖拽时执行 */
				beforeStartDrag     		:null,
				/** @cfg {Function?} afterEndDrag 拖拽后执行体 */
				afterEndDrag	:null,
				/** @cfg {Boolean?} sycHandleSize 拖拽体与Handle尺寸同步 */
				sycHandleSize	:false,
				cursoron		:true 				//光标在拖动层上（不在拖动层上时为可以触发元素的onmouseu事件p）
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				//加载
				ME.load();

				//初始化
				this.initConfig(cfg);

				this.handles=[];

				//默认handle为target本身
				if(this.handle||this.target) this.bindHandle(this.handle||this.target);

				//绑定父
				this.bindParent(this.parent||EG.getBody());
			},

			/**
			 * 绑定拖拽把手
			 * 支持多个把手
			 * @param {Object} handle 拖拽把手
			 */
			bindHandle:function(handle){
				this.handles.push(handle);

				//转换为数组
				handle=(!EG.isArray(handle))?[handle]:handle;
				for (var i=0,il=handle.length;i<il;i++) {
					var h = handle[i];
					if (h.getElement) {
						h=h.getElement;
					}
					EG.CE({ele:h,style:"cursor:move",onmousedown:ME._events.handle.onmousedown,me:this});
				}
			},

			/**
			 * 绑定父移动区域
			 * @param {Object} parent 父移动区域
			 */
			bindParent:function(parent){
				this.parent=parent;
			},

			statics:{

				/**
				 * 首次被实例化时加载
				 */
				load:function(){
					if(!ME.loaded){
						ME.dragGhost=EG.CE({pn:EG.getBody(),tn:"div",cls:"eg_drager"});
						EG.bindEvent(EG.getBody(),"onselectstart"	,ME._events.parent.onselectstart);
						EG.bindEvent(EG.getBody(),"onmouseup"		,ME._events.parent.onmouseup);
						EG.bindEvent(EG.getBody(),"onmousemove"		,ME._events.parent.onmousemove);
						ME.loaded=true;
						EG.hide(ME.dragGhost);
					}
				},

				/**
				 * 是否正在拖拽
				 * @returns {boolean}
				 */
				isDraging:function(){
					return ME.cur!=null;
				},

				/**
				 * 开始拖拽
				 */
				startDrag:function(e){
					//TODO 增加延迟触发
					ME.cur	=this.me;

					//开启拖拽
					var cd=this;
					while(true){
						if(EG.Array.has(ME.cur.handles,cd)){
							break;
						}else{
							cd=this.parentNode;
							if(cd==null){
								break;
							}
						}
					}

					ME.selectable=false;

					if(cd==null) throw new Error("发生预料外的错误->未找到拖拽节点");
					//当前把手
					ME.curHandle=cd;
					//记录鼠标与拖拽物的相对位置
					var t		=ME.cur.sycHandleSize?cd:ME.cur.target;
					ME.refPos	=EG.Tools.getMousePos(e,t);
					ME.refPos_w	=EG.Tools.getMousePos(e);
					//显示拖拽层
					ME.showDragGhost(e);
				},

				/**
				 * 拖拽中
				 */
				draging:function(e){
					//判断是否开启
					if(!ME.isDraging()) return;

					//如果没有按住左键，进行拖拽完成动作
					if(!EG.Tools.isPressLeft(e)){
						ME.endDrag(e);
						return;
					}

					//定位拖拽层
					var pos	=EG.Tools.getMousePos(e);

					var top	=pos.y;
					var left=pos.x;

					//光标不在拖拽层上,错位10px
					if(!ME.cur.cursoron){
						top	+=10;
						left+=10;
						//光标在拖拽层上,并且相对于物件
					}else{
						top	=top	-ME.refPos.y;
						left=left	-ME.refPos.x;
					}

					EG.css(ME.dragGhost,"top:"+top+"px;left:"+left+"px;");

					//拖拽时事件
					if(ME.cur.beforeStartDrag){
						ME.cur.beforeStartDrag.apply(ME.cur,[e]);
					}

					//TODO 碰边检测
				},

				/**
				 * 拖拽完成
				 */
				endDrag:function(e){
					//判断是否开启
					if(!ME.cur) return;

					//判断是否隐藏
					if(EG.Style.isHide(ME.dragGhost)) return;
					var pos_w	=EG.Tools.getMousePos(e);

					//定位Target,获取父与Body的相对位置
					var cs	=EG.Style.current(ME.dragGhost);
					var top	=cs.top	=="auto"?"0":cs.top;
					var left=cs.left=="auto"?"0":cs.left;


					if(ME.cur.moveTarget&&ME.cur.parent!=EG.getBody()){
						var cs_t	=EG.Style.current(ME.cur.target||ME.cur.moveTarget);
						var top_t	=cs_t.top	=="auto"?"0":cs_t.top;
						var left_t	=cs_t.left	=="auto"?"0":cs_t.left;

						top		=(parseInt(EG.String.removeEnd(top_t ,"px")) +pos_w.y-ME.refPos_w.y)+"px";//(parseInt(EG.String.removeEnd(top,"px"))	-pPos.y)+"px";
						left	=(parseInt(EG.String.removeEnd(left_t,"px")) +pos_w.x-ME.refPos_w.x)+"px";//(parseInt(EG.String.removeEnd(left,"px"))	-pPos.x)+"px";
					}

					//定位目标对象
					if(ME.cur.moveTarget){
						EG.css(ME.cur.target,"position:absolute;top:"+top+";left:"+left+";");
					}

					//隐藏拖拽层
					EG.hide(ME.dragGhost);
					ME.selectable=true;
					//执行拖拽后动作
					if(ME.cur.afterEndDrag){
						ME.cur.afterEndDrag.apply(this,[e]);
					}

					//关闭拖拽
					ME.cur=null;
				},

				/**
				 * 变化是否可以选择
				 * @param selectable
				 */
				changeSelectable:function(selectable){
					if(!selectable){
						EG.Style.css(EG.getBody(),EG.Style.c.selectnone);
					}else{
						EG.Style.css(EG.getBody(),EG.Style.c.selectauto);
					}
					//TODO 应该采用还原方式
					ME.selectable=selectable;
				},

				/**
				 * 事件
				 */
				_events:{
					/** 把手 */
					handle:{
						onmousedown:function(e){
							var me=this;
							ME.changeSelectable(false);
							ME.dragThread=window.setTimeout(function(){ME.startDrag.apply(me,[e]);},100);
							return true;
						}
					},
					/** 拖拽体的容器 */
					parent:{
						onselectstart:function(e){
							return ME.selectable;
						},
						onmousemove:function(e){
							ME.draging(e);
						},

						onmouseup:function(e){
							if(ME.dragThread){
								window.clearTimeout(ME.dragThread);
								ME.dragThread=null;
							}

							if(ME.cur){
								ME.endDrag(e);
							}

							ME.changeSelectable(true);
						}
					}
				},

				/**
				 * 绑定
				 * @param cfg
				 */
				bindDrag:function(cfg){
					new EG.ui.Drag(cfg);
				},

				/**
				 * 显示拖拽层
				 * @param {Object} e 事件
				 */
				showDragGhost:function(e){
					e=EG.Event.getEvent(e);

					//定位&显示
					var pos	=EG.Tools.getMousePos(e);
					var top	=pos.y-ME.refPos.y;
					var left=pos.x-ME.refPos.x;
					EG.css	(ME.dragGhost,"top:"+top+"px;left:"+left+"px;");
					EG.show	(ME.dragGhost);

					//同步尺寸
					var sycT=ME.cur.sycHandleSize?ME.curHandle:ME.cur.target;
					var s=EG.getSize(sycT);
					Item.fit({
						element:ME.dragGhost,
						dSize:{width:s.outerWidth,height:s.outerHeight}
					});
				},

				/**
				 * 隐藏拖拽层
				 */
				hideDragGhost:function(){
					EG.hide(ME.dragGhost);
				}
			}
		};
	});
})();

(function(){
	/**
	 * EG.ui.Pop 弹窗
	 */
	EG.define("EG.ui.Pop",[
		"EG.ui.Container",
		"EG.ui.Item"
	],function(Container,Item,ME){
		return {
			extend:Container,
			config:{
				cls				:"eg_pop",
				lock			:false,				//是否有关闭层
				posFix			:true,
				innerHTML		:null,
				target			:null,				//目标对象
				parent			:null,				//参考父
				afterOpen		:null,				//点开后
				expandOuter		:true,				//展开Outer
				onClose			:null
			},
			constructor:function(cfg){
				var me=this;
				this.callSuper([cfg]);
				//隐藏
				EG.hide(this.element);
			},
			/**
			 * 创建
			 */
			build:function(){
				this.element=EG.CE({tn:"div",cls:this.cls+"-outer",item:this,cn:[
					this.dLocker=EG.CE({tn:"div",cls:this.cls+"-locker",item:this}),
					this.dPop	=EG.CE({tn:"div",cls:this.cls,item:this})
				]});
			},
			/**
			 * 获取Style对象
			 * @returns {*}
			 */
			getStyleEle:function(){
				return this.dPop;
			},
			/**
			 * 获取容器
			 * @returns {*}
			 */
			getItemContainer:function(){
				return this.dPop;
			},
			/**
			 * 显示Pop
			 */
			open:function(){

				//移动到父级最下端
				EG.DOM.insertAfter(this.element,this.element.parentNode.lastChild);

				EG.show(this.element);
				this.render();


				if(this.afterOpen){
					this.afterOpen.apply(this);
				}
			},
			/**
			 * 关闭Pop
			 */
			close:function(){
				if(this.onClose){
					var r=this.onClose.apply(this.onCloseSrc||this,[]);
					if(r===false){
						return;
					}
				}
				EG.hide(this.element);
			},

			/**
			 * 设置动画
			 */
			setAnimate:function(animate){
				this.animate=animate;
				if(this.animate){
					var el=this.dPop;
					EG.Anim.remove(el)
					EG.Anim.add(el,this.animate.split(" "));
				}
			},

			render:function(){

				//设置element大小同parent
				var parent=this.parent||this.getElement().parentNode;
				var pSize=EG.getSize(parent);

				if(this.expandOuter){
					Item.fit({
						element:this.element,
						dSize:{width:pSize.innerWidth,height:pSize.innerHeight}
					});
				}

				//设置大小
				Item.fit({
					element:this.dPop,
					dSize:{width:this.width,height:this.height}
				});

				//子组件布局
				if(this.items.length>0){
					//执行布局
					this.doLayout();
				}

				//准备播放动画
				if(this.animate){
					EG.Anim.beforePlay(this.dPop);
				}

				if(this.lock) this.fullLock();

				if(this.posFix) this.doPosFix();

				//播放动画
				if(this.animate){
					EG.Anim.play(this.dPop);
				}
			},
			/**
			 * 锁住屏幕
			 */
			fullLock:function(){
				//锁屏大小适配
				Item.fit({
					element:this.dLocker,
					dSize:{width:"100%",height:"100%"},
					pSize:EG.getSize(this.getElement())
				});
			},
			/**
			 * 固定到中央位置
			 */
			doPosFix:function(){
				EG.Style.center(this.dPop,this.getElement().parentNode);
				EG.Style.middle(this.dPop,this.getElement().parentNode);
			},
			/**
			 * 移动到某位置
			 * @param pos 位置
			 */
			moveTo:function(pos){
				EG.Style.moveTo(this.dPop,pos);
			},
			/**
			 * 是否已打开
			 * @return {Boolean}
			 */
			isOpen:function(){
				return !EG.Style.isHide(this.element);
			}

		};
	});
})();

/**
 * @class EG.ui.Button
 * <AUTHOR>
 * @extends EG.ui.Item
 * Button按钮类
 */
(function(){
	/**
	 * 按钮
	 */
	EG.define("EG.ui.Button",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			alias:"button",
			extend:Item,
			config:{
				/** @cfg {String} text 按钮名称 */
				text		:"",
				/** @cfg {Function} click 事件 */
				click	:null,
				/** @cfg {Function?} mouseover mouseover事件 */
				mouseover	:null,
				/** @cfg {Function?} mouseout mouseout事件 */
				mouseout	:null,
				/** @cfg {String?} icon 图标 */
				icon		:null,
				/** @cfg {String?} cls CSS样式类 */
				cls			:"eg_button",
				/** @cfg {Boolean?} iconAble 是否显示图标 */
				iconAble	:true,
				/** @cfg {Boolean?} menuConfig 下拉菜单 */
				menuConfig	:null,
				/** @cfg {Boolean?} iconOnly 只显示图标 */
				iconOnly	:false,
				/** @cfg {String?} cls 文本样式 */
				textStyle	:null,
				/** @cfg {NUmber?|Boolean?} tabIndex Tab值 */
				tabIndex	:0,
				/** @cfg {Boolean?} renderSizeAble 是否渲染尺寸 */
				renderSizeAble	:false,

				menuStyle:null,

				titleStyle:null,
				/** @cfg {Boolean?} isDisable 禁用 */
				disable		:false
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){

				this.callSuper([cfg]);

				//设置ICON
				if(!this.icon||!this.iconAble){
					EG.hide(this.dIcon);
				}else{
					this.dIcon.className=this.cls+"-icon icon_"+this.icon;
				}

				//创建菜单
				this.buildMenu();

				if(this.textStyle) this.setTextStyle(this.textStyle);

				//
				this.setDisable(this.disable);
			},

			/**
			 * 创建
			 */
			build:function(){
				var me=this;
				var ME=this.getClass();
				if(this.iconOnly){
					this.element=EG.CE({tn:"div",cls:this.cls,item:this,onclick	:ME._events.element.onclick,cn:[
						this.dIcon	=EG.CE({tn:"a",title:this.text})
					]});
				}else{
					this.element=EG.CE({tn:"div",cls:this.cls,item:this,
						onclick		:ME._events.element.onclick,
						onmouseover	:ME._events.element.onmouseover,
						onmouseout	:ME._events.element.onmouseout,
						cn:[
							this.dOuter=EG.CE({tn:"div",cls:this.cls+"-outer",item:this,
								cn:[
									this.dIcon	=EG.CE({tn:"div"}),
									this.dTitle	=EG.CE({tn:"div",cls:this.cls+"-title",innerHTML:this.text,style:EG.unnull(this.titleStyle,"")}),
									this.dMulti	=EG.CE({tn:"div",cls:this.cls+"-multi",onclick:this.showMenu,onclickSrc:this,item:this})
								],
								onmouseover	:ME._events.outer.onmouseover,
								onmouseout	:ME._events.outer.onmouseout
							})
						]
					});
				}


				if(typeof(this.tabIndex)=="number"){
					this.element.tabIndex=this.tabIndex;
				}
			},

			doClick:function(){
				if(this.disable) return;
				if(this.click) this.click.apply(this["clickSrc"]||this);
			},

			/**
			 * 设置文字样式
			 */
			setTextStyle:function(style){
				this.textStyle=style;
				EG.css(this.dTitle,this.textStyle);
			},

			/**
			 * 设置文字
			 * @param text
			 */
			setText:function(text){
				this.text=text;
				this.dTitle.innerHTML=text;
			},

			setMenu:function(menu){
				this.menuConfig=menu;
				if(this.dMenus){
					EG.DOM.remove(this.dMenus);
				}

				if(this.dMulti){
					EG.DOM.remove(this.dMulti);
				}
				this.buildMenu();
			},

			/**
			 * 创建按钮
			 */
			buildMenu:function(){
				//创建多级按钮
				if(this.menuConfig&&this.menuConfig.length>0){
					this.dMenus=EG.CE({pn:this.element,tn:"div",cls:this.cls+"-menu",style:"position:absolute;z-index:1;overflow:hidden;"});
					for(var i= 0,il=this.menuConfig.length;i<il;i++){
						var mc=this.menuConfig[i];
						EG.CE({pn:this.dMenus,tn:"a",idx:i,innerHTML:mc["text"],href:"javascript:void(0)",cls:this.cls+"-mi",item:this,onclick:ME._events.aMenuEle.onclick});
					}
				}else{
					if(this.dMulti) EG.hide(this.dMulti);
				}

				if(this.dMenus){
					EG.hide(this.dMenus);
					if(this.menuStyle){
						EG.css(this.dMenus,this.menuStyle);
					}
				}
			},

			/**
			 * 渲染
			 */
			render:function(){
				if(this.renderSizeAble){
					Item.fit(this);

					var s=EG.getSize(this.element);

					Item.fit({
						element:this.dOuter,pSize:s,
						dSize:{width:"100%",height:"100%"}
					});


					Item.fit({
						element:this.dIcon,pSize:EG.getSize(this.dOuter),
						dSize:{height:"100%"}
					});

					Item.fit({
						element:this.dTitle,
						pSize:s,
						dSize:{
							height:"100%"
						}
					});

					Item.fit({
						element:this.dMulti,
						pSize:s,
						dSize:{height:"100%"}
					});

					EG.css(this.dTitle,"line-height:"+EG.getSize(this.dTitle).innerHeight+"px");
				}

				if(this.dMenus){
					var eSize=EG.getSize(this.element);
					Item.fit({
						element	:this.dMenus,
						sSize	:eSize,
						dSize	:{
							width	:eSize.outerWidth
						}
					})
				}
			},

			/**
			 * 设置禁止
			 * @param disable
			 */
			setDisable:function(disable){
				this.disable=disable;
				if(disable){
					if(this.dOuter) this.dOuter.className=this.cls+"-disable";
				}else{
					if(this.dOuter) this.dOuter.className=this.cls+"-outer";
				}
			},


			/**
			 * 渲染外层
			 */
			renderOuter:function(){
				if(this.renderSizeAble){
					Item.fit(this);
				}
				this._outerSize=this.getSize();
			},

			/**
			 * 显示菜单
			 * @param e
			 */
			showMenu:function(e){
				if(this.dMenus){
					EG.show(this.dMenus);
				}

				if(e){
					EG.Event.stopPropagation(e);
				}
			},

			statics:{
				_events:{
					element:{
						onclick:function(e){
							this.item.doClick();
							EG.Event.stopPropagation(e);
						},
						onmouseout:function(e){
							var me=this.item;
							if(me.disable) return;
							if(!me.dMenus) return;
							if(me.outThread!=null) return;
							me.outThread=setTimeout(function(){
								EG.hide(me.dMenus);
							},10);
							EG.Event.stopPropagation(e);
						}
						,
						onmouseover:function(e){
							var me=this.item;
							if(me.disable) return;
							if(!me.dMenus) return;
							if(me.outThread!=null){
								clearTimeout(me.outThread);
								me.outThread=null;
							}
							EG.Event.stopPropagation(e);
						}
					},
					outer:{
						onmouseover:function(){
							var me=this.item;
							if(me.disable) return;
							this.className=me.cls+"-outer "+me.cls+"-on";
						},

						onmouseout:function(){
							var me=this.item;
							if(me.disable) return;
							this.className=me.cls+"-outer";
						}
					},
					aMenuEle:{
						onclick:function(e){
							var me=this.item;
							if(me.disable) return;
							var mc=me.menuConfig[this.idx];
							mc["click"].apply(mc["clickSrc"]||me);
							EG.hide(me.dMenus);
							EG.Event.stopPropagation(e);
						}
					}
				}
			}
		};
	});
})();

(function(){
	/**
	 * EG.ui.Dialog 对话框
	 */
	EG.define("EG.ui.Dialog",[
		"EG.ui.Pop",
		"EG.ui.Drag",
		"EG.ui.Button",
		"EG.ui.Item"
	],function(Pop,Drag,Button,Item,ME){
		return {
			alias:"dialog",
			extend:Pop,
			config:{
				cls				:"eg_dialog",
				title			:null,				//标题
				btnsConfig		:null,				//底部按钮
				zIndex			:null,
				bodyStyle		:null,
				headStyle		:null,
				footStyle		:null,
				closeable		:true,				//是否能关闭
				fullable		:false,				//是否能全屏
				dragable		:false				//是否能拖拽
			},
			constructor:function(cfg){

				this.callSuper([cfg]);

				//设置标题
				if(this.title)			this.setTitle(this.title);

				//底部按钮
				if(this.btnsConfig) 	this.setButtons(this.btnsConfig);

				//全屏按钮
				this.setFullable(this.fullable);

				//关闭按钮
				this.setCloseable(this.closeable);

				//设定拖拽
				this.setDragable(this.dragable);
			},

			//创建
			build:function(){
				var me=this;

				this.callSuper("build");

				//Head
				this.dHead=EG.CE({pn:this.dPop,tn:"div",cls:this.cls+"-head",cn:[
					this.dTitle	=EG.CE({tn:"div",cls:this.cls+"-title"}),

					EG.CE({tn:"div",cls:this.cls+"-trBtns",cn:[
						//最大化按钮
						this.dFuller=EG.CE({tn:"a",cls:this.cls+"-fuller",me:this,
							onmouseup	:ME._events.dFuller.onmouseup,
							onmousedown	:ME._events.dFuller.onmousedown
						}),

						//关闭按钮
						this.dCloser=EG.CE({tn:"a",cls:this.cls+"-closer",me:this,
							onmouseup	:ME._events.dCloser.onmouseup,
							onmousedown	:ME._events.dCloser.onmousedown
						})
					]})
				]});

				//Body
				this.dBody=EG.CE({pn:this.dPop,tn:"div",cls:this.cls+"-body"});

				//Foot
				this.dFoot=EG.CE({pn:this.dPop,tn:"div",cls:this.cls+"-foot"});

				//设置Body样式
				if(this.bodyStyle)	EG.css(this.dBody,this.bodyStyle);

				//设置Head样式
				if(this.headStyle)	EG.css(this.dHead,this.headStyle);

				//设置Foot样式
				if(this.footStyle)	EG.css(this.dFoot,this.footStyle);

				//层值
				if(this.zIndex!=null){
					EG.css(this.element,"z-index:"+this.zIndex);
				}

//			/**
//			 * 绑定ESC关闭
//			 * TODO 放在destroy中销毁
//			 */
//			EG.bindEvent(this.dBody,"onkeydown",function(e){
//				e=EG.Event.getEvent(e);
//				if(e.keyCode==EG.Event.keycodes.esc){
//					if(me.closeable){
//						me.close();
//					}
//				}
//			});
			},

			/**
			 * 获取Item容器
			 * @returns {*}
			 */
			getItemContainer:function(){
				return this.dBody;
			},

			/**
			 * 设置是否可以全屏
			 * @param fullable
			 */
			setFullable:function(fullable){
				if(fullable){
					EG.show(this.dFuller);
				}else{
					EG.hide(this.dFuller);
				}
			},

			/**
			 * 设置是否能拖拽
			 * @param dragable
			 */
			setDragable:function(dragable){
				this.dragable=dragable;
				if(!this.drag){
					this.drag=new Drag({
						target:this.dPop,
						parent:this.dLocker,
						handle:this.dHead,
						moveTarget:true
					});
				}
			},

			/**
			 * 设置是否可以关闭
			 * @param closeable
			 */
			setCloseable:function(closeable){
				if(closeable){
					EG.show(this.dCloser);
				}else{
					EG.hide(this.dCloser);
				}
			},

			/**
			 * 设置按钮
			 * @param btns
			 */
			setButtons:function(btns){
				EG.DOM.removeChilds(this.dFoot);
				if(btns==null) return;
				for(var i=0,il=btns.length;i<il;i++){
					var btn=EG.isLit(btns[i])?new Button(btns[i]):btns[i];
					this.dFoot.appendChild(btn.getElement());
				}
			},
			/**
			 * 设置标题
			 * @param title
			 */
			setTitle:function(title){
				EG.setValue(this.dTitle,title);
			},

			setInnerWidth:function(w){
				Item.pack({
					pEle:this.dBody,
					width:w
				});

				Item.pack({
					pEle:this.dPop,
					width:EG.getSize(this.dBody).outerWidth
				});
			},

			setInnerHeight:function(h){
				//设定Body高度
				Item.pack({
					pEle:this.dBody,
					height:h
				});

				//设定外层高度
				Item.pack({
					pEle:this.dPop,
					height:EG.getSize(this.dBody).outerHeight+EG.getSize(this.dHead).outerHeight+EG.getSize(this.dFoot).outerHeight
				});
			},

			getInnerHeight:function(){
				if(typeof(this.oHeight)=="number"){
					return EG.getSize(this.dPop).innerHeight-EG.getSize(this.dHead).outerHeight-EG.getSize(this.dFoot).outerHeight;
				}else{
					return this.oHeight;
				}
			},

			full:function(){

				//放大
				if(!this._fulled){
					//保留当前元素对应的父元素和参考元素
					this.oParent=this.parent;
					this.oParentNode=this.getElement().parentNode;
					this._oWidth=this.width;
					this._oHeight=this.height;
					//将当前元素放在Body下
					EG.getBody().appendChild(this.getElement());
					this.width="100%";
					this.height="100%";
					this.render();
					this._fulled=true;
					//还原
				}else{
					this.parent=this.oParent;
					this.width=this._oWidth;
					this.height=this._oHeight;
					//将当前元素放在Body下
					this.oParentNode.appendChild(this.getElement());
					this.render();
					this._fulled=false;
				}
			},

			render:function(){

				//设置element大小同parent
				var parent=this.parent||this.getElement().parentNode;
				var pSize=EG.getSize(parent);
				Item.fit({
					element:this.element,
					dSize:{width:pSize.innerWidth,height:pSize.innerHeight},
					pSize:pSize
				});

				EG.css(this.element,"top:0px;left:0px");

//			alert("HERE")

				//设置大小
				Item.fit({
					element:this.dPop,
					dSize:{width:this.width,height:this.height}
				});

				//设定dBody高度
				var ah=this.isAutoHeight();
				if(!ah){
					//alert(this.height+":"+EG.getSize(this.dHead).outerHeight+":"+EG.getSize(this.dFoot).outerHeight)
					Item.fit({
						element:this.dBody,
						dSize:{height:(EG.getSize(this.dPop).innerHeight-EG.getSize(this.dHead).outerHeight-EG.getSize(this.dFoot).outerHeight)}
					});
				}

				//设定dBody宽度
				var aw=this.isAutoWidth();
				if(!aw){
					Item.fit({
						element:this.dBody,
						dSize:{width:"100%"}
					});
				}

				//子组件布局
				if(this.items.length>0){
					//执行布局
					this.doLayout();
				}


				pSize=EG.getSize(this.dPop);
				//设置头宽
				Item.fit({
					element:this.dHead,
					dSize:{width:pSize.innerWidth}
				});

				//设置尾宽
				Item.fit({
					element:this.dFoot,
					dSize:{width:pSize.innerWidth}
				});

				EG.css(this.dBody,"position:relative;");
//			EG.css(this.dHead,"position:absolute;top:0px");
//			EG.css(this.dBody,"position:absolute;top:"+EG.getSize(this.dHead).outerHeight+"px");
//			EG.css(this.dFoot,"position:absolute;bottom:0px");

				//准备播放动画
				if(this.animate){
					EG.Anim.beforePlay(this.dPop);
				}

				if(this.lock) this.fullLock();

				if(this.posFix) this.doPosFix();

				//播放动画
				if(this.animate){
					EG.Anim.play(this.dPop);
				}
			},

			statics:{
				_events:{
					dCloser:{
						onmousedown:function(e){
							EG.Event.stopPropagation(e);
						},
						onmouseup:function(e){
							var me=this.me;
							me.close();
							EG.Event.stopPropagation(e);
						}
					},

					dFuller:{
						onmousedown:function(e){
							EG.Event.stopPropagation(e);
						},
						onmouseup:function(e){
							var me=this.me;
							me.full();
							EG.Event.stopPropagation(e);
						}
					}
				}
			}
		};
	});
})();

(function(){
	/**
	 * EG.ui.Tip 贴示
	 */
	EG.define("EG.ui.Tip",[
		"EG.ui.Item"
	],function(Item,ME){

		//全局
		EG.onload(function(){
			var tip;
			var fn=function(){
				if(!tip) tip=new ME();
			};
			EG.Tip={
				open	:function(){fn();tip.open.apply(tip	,arguments)},
				error	:function(){fn();tip.error.apply(tip,arguments)},
				close	:function(){fn();tip.close.apply(tip,arguments)},
				info	:function(){fn();tip.info.apply(tip,arguments)},
				message	:function(){fn();tip.message.apply(tip,arguments)}
			}
		});

		return {
			extend:Item,
			config:{
				lock			:false,
				cls				:"eg_tip"
			},
			constructor:function(cfg){
				this.callSuper([cfg]);
			},
			build:function(){
				var me=this;
				this.element=EG.CE({pn:EG.getBody(),tn:"div",style:"border:1px solid red;border-radius:2px;background:#FFFFC0;cursor:pointer;position:absolute;z-index:2",cn:[
					this.dIcon=EG.CE({tn:"div",style:EG.Style.c.dv}),
					this.dMessage=EG.CE({tn:"div",style:EG.Style.c.dv+";padding:5px"})
				],onclick:function(e){
					if(me.currentClick!=null){
						me.currentClick.apply(this,[e]);
						me.currentClick=null;
						EG.Event.stopPropagation(e);
					}
					EG.hide(this);
				}});
				EG.hide(this.element);
			},
			close:function(){
				EG.hide(this.element);
			},
			open:function(){
				EG.show(this.element);
			},
			error:function(msg,target){
				this.message({
					message:msg,
					target:target,
					type:"error"
				});
			},
			info:function(msg,target){
				this.message({
					message:msg,
					target:target,
					type:"info"
				});
			},
			message:function(cfg){
				cfg=cfg||{};

				var message	=cfg["message"],
					target	=cfg["target"],
					type	=cfg["target"],
					pos		=cfg["pos"],
					style	=cfg["style"],
					animate	=cfg["animate"],
					click	=cfg["click"];

				var autoclose=cfg["autoclose"]||false;
				var closetime=cfg["closetime"];
				if(closetime){
					autoclose=true;
				}
				this.dMessage.innerHTML=message;

				EG.show(this.element);
				EG.css(this.dMessage,"width:100px");

				if(style){
					EG.css(this.element,style);
				}else{
					//寻找aim的坐标,设定浮动位置
					if(!pos&&target){
						pos=EG.Tools.getElementPos(target);
						var tSize	=EG.getSize(target);
						var size	=EG.getSize(this.dMessage);
						if((pos.x+tSize.clientWidth+size.clientWidth)>screen.width){
							pos.x=pos.x+tSize.clientWidth-size.clientWidth;
							pos.y=pos.y+tSize.clientHeight;
						}else{
							pos.x=pos.x+tSize.clientWidth;
						}
					}

					EG.css(this.element,"left:"+pos.x+"px;top:"+pos.y+"px");
				}

				if(animate){
					EG.Anim.remove(this.element)
					EG.Anim.add(this.element,animate.split(" "));
				}

				EG.show(this.element);


			}
		};
	});
})();

/**
 * @class EG.ui.Locker
 * <AUTHOR>
 * @extends EG.ui.Dialog
 * 锁定器,Locker只有在window.onload时才创建
 */
(function(){
	EG.define("EG.ui.Locker",[
		"EG.ui.Dialog",
		"EG.ui.Button"
	],function(Dialog,Button,ME){

		//全局
		EG.onload(function(){
			EG.Locker=new ME({
				width	:Math.min(500,document.documentElement.clientWidth),
				height	:Math.min(120,document.documentElement.clientHeight),
				renderTo:EG.getBody()
			});
		});

		return {
			extend:Dialog,
			config:{
				/** @cfg {String?} CSS样式类 */
				cls			:"eg_locker",
				/** @cfg {String?} 标题*/
				title		:"提示",
				/** @cfg {Number?} 层级*/
				zIndex		:2
			},
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.callSuper([cfg]);
				EG.css(this.dBody,"text-align:center;overflow:auto");
			},

			/**
			 * 创建
			 */
			build:function(){
				this.callSuper("build");
				var pn=this.dBody;
				this.dType=EG.CE({pn:pn,tn:"div",cls:this.cls+"-type"});
				this.dWait=EG.CE({pn:pn,tn:"div",cls:this.cls+"-wait",innerHTML:"正在加载..."});
			},

			/**
			 * 锁定
			 * @param {Boolean?} iflock 是否锁定
			 */
			lock:function(iflock){
				if(iflock) {
//				EG.hide(this.dPop);
//				EG.show(this.dLocker);
					this.open();
				}else{
					//清空线程
					if(this.t!=null){
						clearTimeout(this.t);
						this.t=null;
					}
					this.close();
				}
			},

			/**
			 * 等待层
			 * @param {String?} message 消息
			 */
			wait:function(message){
				this.message({
					message:message,
					closeable:false
				});
			},

			/**
			 * 确认
			 * @param cfg
			 */
			confirm:function(cfg){

				if(typeof(cfg)=="string"){
					cfg={
						message:cfg
					};
					if(arguments.length>1){
						cfg["yes_callback"]=arguments[1];
					}
					if(arguments.length>2){
						cfg["no_callback"]=arguments[2];
					}
				}

				var message		=cfg["message"],
					yes_title   =cfg["yes_title"]||"确定",
					no_title	=cfg["no_title"]||"取消",
					yes_callback=cfg["yes_callback"],
					no_callback =cfg["no_callback"]||function(){
							EG.Locker.lock(false);
						};

				var btnYes,btnNo;
				cfg["message"] 	=EG.CE({tn:"div",cn:[
					{tn:"div",innerHTML:message},
					{tn:"div",style:"margin-top:0.5rem;",cn:[
						btnYes	=new Button({text:yes_title,click:yes_callback,cls:"eg_button_small"}),
						btnNo	=new Button({text:no_title	,click:no_callback,cls:"eg_button_small",style:"margin-left:2rem;"})
					]}
				],onkeydown:function(e){
					e=EG.Event.getEvent(e);

					//空格和回车
					if(e.keyCode == 32 || e.keyCode ==13){
						btnYes.doClick();
					}else if(e.keyCode == 27){
						btnNo.doClick();
					}
				}});

				cfg["reqFocus"]			=true;
				cfg["reqFocusElement"]	=cfg["message"];

				this.message(cfg);
			},

			/**
			 * 消息提示
			 * @param {Object?} cfg 配置
			 */
			message:function(cfg){
				cfg=cfg||{};

				//if(!cfg["force"]&&this._lock) return;

				//消息类型
				var type=cfg["type"];
				if(type){
					EG.setCls(this.dType,["type","type-"+type],this.cls);
					EG.show(this.dType);
				}else{
					EG.hide(this.dType);
				}
				EG.setCls(this.dWait,["wait","fontM"],this.cls);

				//消息体
				var message=typeof(cfg)=="string"?cfg:EG.unnull(cfg["message"],"请稍等...");
				if(typeof(message)=="string"){
					this.dWait.innerHTML=message;
				}else{
					EG.DOM.removeChilds(this.dWait);
					this.dWait.appendChild(message);
				}

				var closeable=cfg["closeable"]!=null?cfg["closeable"]:true;

				var autoclose=cfg["autoclose"]||false;
				var closetime=cfg["closetime"];
				if(closetime){
					autoclose=true;
				}
				var callback=cfg["callback"];
				//不能关闭和自动关闭时禁止显示关闭按钮
				if(!closeable||autoclose) this.setCloseable(false);
				else this.setCloseable(true);

				this.open();

				//聚焦
				var reqFocus=cfg["reqFocus"];
				if(reqFocus){
					var ele=cfg["reqFocusElement"];
					EG.CE({ele:ele,tabIndex:1});
					ele.focus();
				}

				//渐进显示
				//EG.Style.fade(this.dPop,0,90,null,10);

				//自动关闭
				if(autoclose){
					if(closetime==null) closetime=1200;
					var me=this;

					//清空线程
					if(this.t!=null){
						clearTimeout(this.t);
						this.t=null;
					}
					this.t=setTimeout(function(){
						//EG.Style.fade(me.dPop,90,0,function(){
						me.t=null;
						me.close();
						if(callback) callback();
						//},20);
					},closetime);
				}
			}
		};
	});


})();


(function(){
	
	EG.ui.Option=function(cfg){
		this.btns=cfg["btns"];
		this.pop=new EG.ui.Pop({closeable:false});
		
		this.element=EG.CE({tn:"div",style:"padding:10px"});
		
		this.pop.getElement().appendChild(this.element);
		if(this.btns) this.setBtns(this.btns);
		document.body.appendChild(this.element);
	};
	EG.ui.Option.prototype={};
	var Option=EG.ui.Option;
	
	EG.ui.Option.prototype.setTextactions=function(textactions){
		for(var i=0,il=textactions.length;i<il;i++){
			
		}
	};
	
	EG.ui.Option.prototype.open=function(){
		this.pop.open();
	};
	
	EG.ui.Option.prototype.close=function(){
		this.pop.close();
	};
	
	EG.ui.Option.prototype.setBtns=function(btns){
		for(var i=0,il=btns.length;i<il;i++){
			this.element.appendChild(btns[i].getElement());
			//this.pop.addChildren();
		}
	};
	
	
})();

/**
 * @class EG.ui.Layout
 * <AUTHOR>
 * 布局器父类
 */
(function(){
	/**
	 * EG.ui.Layout 布局
	 */
	EG.define("EG.ui.Layout",[

	],function(ME){
		return {
			statics:{
				layoutManagers:{},

				/**
				 * 获取布局器
				 * @param {Object|String} cfg 配置
				 * @param {EG.ui.Container} container 容器
				 * @return {EG.ui.Layout}
				 */
				create:function(cfg,container) {
					if (EG.String.isString(cfg)){
						cfg = {
							type : cfg
						};
					}
					var layoutManagerClass=EG.ui.layout[EG.Word.first2Uppercase(cfg["type"])+"Layout"];
					if(!layoutManagerClass) throw new Error("EG.ui.Layout#create:该布局器无法识别:"+cfg["type"]);
					return new layoutManagerClass(cfg,container);
				},

				/**
				 * 注册布局器
				 * @param {String} type 类型
				 * @param {EG.ui.Layout} layoutManager 布局Manager
				 */
				regist:function(type, layoutManager) {
					EG.ui.Layout.layoutManagers[type] = layoutManager;
				},

				/**
				 * 组件描述
				 */
				sizeDesc:function(item){
					var d={};
					d.ow=item.getOWidth();
					d.oh=item.getOHeight();
					d.isWA=item.isContainer&&item.isAutoWidth();							//宽度-自动
					d.isHA=item.isContainer&&item.isAutoHeight();						//高度-自动
					d.isWP=(typeof(d.ow)=="string"&&d.ow.lastIndexOf("%")>=0);		//宽度-百分比
					d.isHP=(typeof(d.oh)=="string"&&d.oh.lastIndexOf("%")>=0);		//高度-百分比
					d.isWE=d.ow==null;												//宽度-空
					d.isHE=d.oh==null;												//高度-空
					d.isWN=(typeof(d.ow)=="number");									//宽度-固定值
					d.isHN=(typeof(d.ow)=="number");									//高度-固定值
					return d;
				},

				/**
				 * 渲染子元素
				 */
				renderItems:function(layout){
					for(var i=0,l=layout.items.length;i<l;i++){
						var item=layout.items[i];
						item.render();
					}
				}
			},
			/**
			 * layout添加模式
			 */
			addOnLayout:false
			,
			/**
			 * 执行布局
			 * @interface
			 */
			doLayout:function(){},

			/**
			 * 分辨出hidden属性的Item不进行布局
			 */
			hideItems:function(){
				var unHides=[];
				for(var i=0;i<this.items.length;i++){
					if(!this.items[i].hidden){
						unHides.push(this.items[i]);
					}
				}
				this.items=unHides;
			},

			/**
			 * 渲染自动的子组件
			 */
			renderItemsOuter:function(force){
				for(var i=0,l=this.items.length;i<l;i++){
					var item=this.items[i];
					if(force||(item.isContainer&&(item.isAutoHeight()||item.isAutoWidth()))) item.renderOuter();
				}
			}
		};
	});
})();

/**
 * @class EG.ui.layout.DefaultLayout
 * <AUTHOR>
 * @extends EG.ui.Layout
 * 默认布局器
 */
(function(){
	EG.define("EG.ui.layout.DefaultLayout",[
		"EG.ui.Layout",
		"EG.ui.Item"
	],function(Layout,Item,ME){
		return {
			extend:Layout,
			config:{
				/** @cfg {String?} align 横向对齐方式 */
				align			:null,
				/** @cfg {String?} verticalAlign 纵向对齐方式 */
				verticalAlign	:null,
				/** @cfg {Boolean?} horizontal 是否平行并排 */
				horizontal		:true
			},
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 * @param {EG.ui.Container} container 容器
			 */
			constructor:function(cfg,container){
				this.container	=container;			//容器
				this.initConfig(cfg);
				this.force=this.align||this.verticalAlign;
			},

			/**
			 * 布局
			 * @implements {EG.ui.Layout.prototype.doLayout}
			 */
			doLayout:function(){
				this.init();

				//先计算auto子组件外尺寸，DefaultLayout需要强制render子外层
				if(this.isCtWA||this.isCtHA){
					this.renderItemsOuter(true);
					this.autoSize();
				}

				if(!this.container.isRenderingOuter){
					Layout.renderItems(this);

					//行向居中
					if(this.align=="center"){
						EG.Style.centerChilds(this.container.getItemContainer(),this.horizontal);
					}

					//纵向对齐
					if(this.verticalAlign=="middle"){
						EG.Style.middleChilds	(this.container.getItemContainer(),this.horizontal);
					}else if(this.verticalAlign=="top"){
						EG.Style.topChilds		(this.container.getItemContainer(),this.horizontal);
					}else if(this.verticalAlign=="bottom"){
						EG.Style.bottomChilds	(this.container.getItemContainer(),this.horizontal);
					}

					//
					for(var i=0,l=this.items.length;i<l;i++){
						var item=this.items[i];
						if(item.region){
							var rs=item.region.split[";"];		//支持 {xtype:"panel",region:"top,left"}
							for(var r in rs){
								if(r=="center"){
									EG.Style.center(item.getElement());
								}else if(r=="middle"){
									EG.Style.middle(item.getElement());
								}else{
									EG.css(item.getElement(),"position:absolute;"+r+"0px;");
								}
							}
						}
					}

				}
			},

			init:function(){
				this.items		=this.container.items;	//items

				this.hideItems();

				//容器尺寸自动宽高
				this.isCtWA=this.container.isAutoWidth();
				this.isCtHA=this.container.isAutoHeight();
				//子组件尺寸有自动宽高
				this.hasItemWA=false;this.hasItemHA=false;
				for(var i=0,l=this.items.length;i<l;i++){
					var item=this.items[i];
					//尺寸描述
					var sd=Layout.sizeDesc(item);
					//是否有子组件自动尺寸
					this.hasItemWA=this.hasItemWA||sd.isWA;
					this.hasItemHA=this.hasItemHA||sd.isHA;

					//清空尺寸
					item.width=item.oWidth;
					item.height=item.oHeight;
					EG.css(item.getElement(),"width:auto;height:auto");
				}
			},

			/**
			 * 自动尺寸
			 * 具有剪裁特性，允许内容多了换行
			 */
			autoSize:function(){
				//计算当前元素排列的左边距、右边距
				var minX=-1,maxX=-1,minY=-1,maxY=-1;
				for(var i=0,l=this.items.length;i<l;i++){
					var item=this.items[i];
					try{
						var p=EG.Tools.getElementPos(item.getElement());
						var s=item.getSize();
						minX=minX==-1?(p.x-s.marginLeft)				:Math.min(p.x-s.marginLeft,minX);
						maxX=maxX==-1?(p.x-s.marginLeft+s.outerWidth)	:Math.max(p.x-s.marginLeft	+s.outerWidth,maxX);
						minY=minY==-1?(p.y-s.marginTop)					:Math.min(p.y-s.marginTop,minY);
						maxY=maxY==-1?(p.y-s.marginTop+s.outerHeight)	:Math.max(p.y-s.marginTop	+s.outerHeight,maxY);
					}catch(e){
						alert(Item.getPath(item)+":"+item.getElement());
						throw e;
					}
				}
				var w=Math.max(0,maxX-minX);
				var h=Math.max(0,maxY-minY);
				//alert(this.isCtWA+":"+w+","+this.isCtHA+":"+h);
				if(this.isCtWA)	this.container.setInnerWidth(w);
				if(this.isCtHA)	this.container.setInnerHeight(h);
				//if(this.container.cls=="eos_main-head")

//			//还原Overflow
//			for(var i=0,l=this.items.length;i<l;i++){
//				EG.css(this.items[i].getElement(),"overflow:"+ofs[i]+";");
//			}

//			EG.css(this.container.getElement(),"overflow:"+of+";");
			}
		};
	});
})();

/**
 * @class EG.ui.layout.BorderLayout
 * <AUTHOR>
 * @extends EG.ui.Layout
 * Border布局器
 */
(function(){
	/**
	 * EG.ui.layout.BorderLayout Border布局器
	 */
	EG.define("EG.ui.layout.BorderLayout",[
		"EG.ui.Layout"
	],function(Layout,ME){

		var size2Num=EG.Style.size2Num;

		return {
			extend:Layout,
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 * @param {EG.ui.Container} container 容器
			 */
			constructor:function(cfg,container){
				this.container	=container;			//容器
			},
			/**
			 * 获取Item
			 * @param {Object} cfg 配置
			 */
			getItem:function(cfg) {
				for (var i=0,l=this.container.items.length;i<l;i++)
					if(this.container.items[i].region == cfg["region"])
						return this.container.items[i];
				return null;
			},

			//Layout分配应按照原始尺寸
			//1.如果外层尺寸固定
			//	1.1	子的尺寸非auto,依赖Layout规则,重新分配尺寸
			//  1.2	子的尺寸为auto,暂不分配尺寸,待setItemSize结束后,将所有自动尺寸子render,固定子的尺寸
			//2.如果外层尺寸为auto,用子组件计算总的尺寸,如果总尺寸因部份子为auto而暂不能确定,标记为-1
			//
			//	子的尺寸若为固定,先render子,结束后设置外层高度
			//
			/**
			 * 布局
			 * @implements {EG.ui.Layout.prototype.doLayout}
			 */
			doLayout:function(){
				//初始化
				this.init();

				//容器是自动尺寸||有子组件自动尺寸
				if(this.isCtWA||this.isCtHA||this.hasItemHA||this.hasItemWA){
					//alert("-1("+this.itemT.width+","+this.itemT.height+"):"+this.itemT.getSize().outerHeight)
					//欲分配尺寸
					if(!this.isCtWA) 	this.setItemWidth	(this.ctW);
					if(!this.isCtHA)	this.setItemHeight	(this.ctH);
					//alert("0("+this.itemT.width+","+this.itemT.height+"):"+this.itemT.getSize().outerHeight)
					//先计算auto子组件外尺寸
					this.renderItemsOuter();
					//alert("1("+this.itemT.width+","+this.itemT.height+"):"+this.itemT.getSize().outerHeight)
					//计算外层尺寸
					if(this.isCtWA)	this.setCtWidth();
					if(this.isCtHA)	this.setCtHeight();
					//alert("2("+this.itemT.width+","+this.itemT.height+"):"+this.itemT.getSize().outerHeight)
				}

				//当外层正式渲染时
				if(!this.container.isRenderingOuter){
					//分配高宽
					this.setItemWidth	(this.ctW,true);
					this.setItemHeight	(this.ctH,true);
					//alert("3("+this.itemT.width+","+this.itemT.height+"):"+this.itemT.getSize().outerHeight)
					Layout.renderItems(this);
					//alert("4("+this.itemT.width+","+this.itemT.height+"):"+this.itemT.getSize().outerHeight)
				}
			},

			/**
			 * 外层自动宽时,收集一次高
			 */
			setCtWidth:function(){
				this.ctW=0;
				if(this.refMW)								{for(var i=0;i<this.mItems.length;i++){this.ctW+=this.getItemWidth(this.mItems[i]);}}	//中
				if(this.itemT&&this.itemT.getOWidth()!=null){this.ctW=Math.max(this.ctW,this.getItemWidth(this.itemT));}							//上
				if(this.itemB&&this.itemB.getOWidth()!=null){this.ctW=Math.max(this.ctW,this.getItemWidth(this.itemB));}							//下
				this.container.setInnerWidth(this.ctW);
			},

			/**
			 * 外层自动高时,收集外层高
			 */
			setCtHeight:function(){
				this.ctH=0;
				//中
				if(this.refMH){
					for(var i=0;i<this.mItems.length;i++){
						var h=this.getItemHeight(this.mItems[i]);
						if(h==null) continue;
						this.ctH=Math.max(h,this.ctH);
					}
				}
				if(this.itemT&&this.itemT.getOHeight()!=null){this.ctH+=this.getItemHeight(this.itemT);}//上
				if(this.itemB&&this.itemB.getOHeight()!=null){this.ctH+=this.getItemHeight(this.itemT);}//下
				this.container.setInnerHeight(this.ctH);
			},
			getItemWidth	:function(item){return item.isContainer&&item.isAutoWidth()	?item._outerSize.outerWidth	:item.getOWidth();},
			getItemHeight	:function(item){return item.isContainer&&item.isAutoHeight()?item._outerSize.outerHeight:item.getOHeight();},


			/**
			 * 根据父宽分配子组件的宽度，主要是中间组件的宽度分配
			 *
			 * @param {Number} ctW 容器宽
			 * @param {Boolean?} force
			 */
			setItemWidth:function(ctW,force){
				//如果强制||中间无自动
				if(this.mItems.length>0){

					var wL=this.setItemWidth_FN(this.itemL,ctW,force);		//Left宽
					var wC=this.setItemWidth_FN(this.itemC,ctW,force);		//Center宽
					var wR=this.setItemWidth_FN(this.itemR,ctW,force);		//Right宽

					//有自动宽时无法分配
					if(wL==-2||wC==-2||wR==-2){
						//
					}else{
						//当ctW有定值时,计算余宽
						if(wC<0){
							if(wL<0&&wR<0)			{						wC=ctW/3;			wL=ctW/3;					wR=ctW/3;}
							else if(wL>=0&&wR>=0)	{						wC=ctW-wL-wR;}
							else if(wL<0&&wR<0)		{var restW=ctW-wR;		wC=0.5*restW;		wL=0.5*restW;}
							else if(wR<0)			{var restW=ctW-wL;		wC=0.5*restW;									wR=0.5*restW;}
						}else{
							if(wL<0&&wR<0)			{var restW=ctW-wC;							wL=0.5*restW;				wR=0.5*restW;}
							else if(wL<0)			{											wL=ctW-wC-wR;}
							else if(wR<0)			{																		wR=ctW-wC-wL;}
						}
					}

					wL=parseInt(wL);
					wC=parseInt(wC);
					wR=parseInt(wR);

					if(this.itemL&&wL>=0){
						this.itemL.width=wL;
					}

					if(this.itemC&&wC>=0){
						this.itemC.width=wC;
						var _posLeft=wL>=0?wL:0;
						EG.css(this.itemC,"left:"+_posLeft+"px");
					}

					if(this.itemR&&wR>=0){
						this.itemR.width=wR;
					}
				}

				this.setItemWidth_FN2(this.itemT,ctW,force);
				this.setItemWidth_FN2(this.itemB,ctW,force);
			},
			setItemWidth_FN:function(item,ctW,force){
				if(!item) return 0;
				var oW=item.getOWidth();
				var w=-1;
				if(oW==null)											{w=-1;}
				else if(oW=="auto")										{w=force?item._outerSize.outerWidth:-2;}
				else if(typeof(oW)=="string"&&oW.lastIndexOf("%")>=0)	{if(ctW>0) w=size2Num(oW,ctW);}
				else if(typeof(oW)=="number")							{w=oW;}

				return w;
			},
			setItemWidth_FN2:function(item,ctW,force){
				if(!item||ctW<0||item.getOWidth()=="auto") return;
				item.width=ctW;
			},


			/**
			 * 根据父宽分配子组件的高度
			 *
			 * @param {Number} ctH 容器高
			 * @param {Boolean?} force
			 */
			setItemHeight:function(ctH,force){
				//MID高
				var hM=-1;
				//如果强制||中间无自动
				if(this.mItems.length>0){
					for(var i=0;i<this.mItems.length;i++){
						var item=this.mItems[i];
						var h=item.getOHeight();
						if(h==null) continue;
						if(item.isContainer&&item.isAutoHeight()){//如果是自动高
							if(force) {h=item._outerSize.outerHeight;}
							else {hM=-2;break;}
						}else{
							h=size2Num(h,this.ctH);
						}
						hM=Math.max(hM,h);

					}
				}else{
					hM=0;
				}

				//Bootom高
				var hB=this.setItemHeight_FN(this.itemB,ctH,force);
				var hT=this.setItemHeight_FN(this.itemT,ctH,force);

				//有自动高时无法分配
				if(hM==-2||hB==-2||hT==-2){
					//
				}else{
					//未分配的组件采用等分原则
					if(hM==-1){//如果中间没有分配高度
						if(hT<0&&hB<0)			{						hM=ctH/3;			hT=ctH/3;			hB=ctH/3;}
						else if(hT>=0&&hB>=0)	{						hM=ctH-hB-hT;}
						else if(hT<0)			{var restH=ctH-hB;		hM=restH*0.5;		hT=restH*0.5;}
						else if(hB<0)			{var restH=ctH-hT;		hM=restH*0.5;							hB=restH*0.5;}
					}else{
						if(hT<0&&hB<0)			{var restH=ctH-hM;							hT=restH*0.5;		hB=restH*0.5;}
						else if(hT<0)			{											hT=ctH-hM-hB;}
						else if(hB<0)			{																hB=ctH-hM-hT;}
					}
				}

				hM=parseInt(hM);
				hB=parseInt(hB);
				hT=parseInt(hT);

				if(this.itemT&&hT>=0) 	{this.itemT.height=hT;}
				if(this.itemB&&hB>=0)	{this.itemB.height=hB;}

				if(this.mItems.length>0&&hM>=0){
					for(var i=0;i<this.mItems.length;i++){
						var item=this.mItems[i];
						item.height=hM;
						EG.css(item,"top:"+hT+"px");
					}
				}
			},
			setItemHeight_FN:function(item,ctH,force){
				if(!item) return 0;
				var oH=item.getOHeight();
				var h=-1;
				if(oH==null)											{h=-1;}
				else if(oH=="auto")										{h=force?item._outerSize.outerHeight:-2;}
				else if(typeof(oH)=="string"&&oH.lastIndexOf("%")>=0)	{if(ctH>0) h=size2Num(oH,ctH);}
				else if(typeof(oH)=="number")							{h=oH;}
				return h;
			},

			/**
			 * 初始化
			 */
			init:function(){
				this.items		=this.container.items;	//items

				this.hideItems();

				//父元素固定
				this.ctEle=this.container.getItemContainer();
				EG.css(this.ctEle, "position:absolute");

				//初始化各个参数
				this.itemT=this.itemB=this.itemC=this.itemL=this.itemR=null;

				//容器尺寸自动宽高
				this.isCtWA=this.container.isAutoWidth();
				this.isCtHA=this.container.isAutoHeight();

				this.hasItemWA=false;	this.hasItemHA=false;	//子组件尺寸有自动宽高
				this.hasMidWA=false;	this.hasMidHA=false;	//中间组件尺寸有自动宽高
				this.hasMidWE=false;	this.hasMidHE=false;	//中间组件尺寸有空值
				this.hasMidWP=false;	this.hasMidHP=false;	//中间组件尺寸有百分比
				this.hasMidWN=false;	this.hasMidHN=false;//中间组件尺寸有百分比

				//容器高
				this.ctW=this.container.getInnerWidth();
				this.ctH=this.container.getInnerHeight();
				this.mItems=[];

				//1.设置组件初始的Position样式,获取各种高宽属性
				for(var i=0,l=this.items.length;i<l;i++){
					var item=this.items[i];
					if(!item.region){
						var s="";
						while(item.pItem!=null){s+=">"+item.pItem.getClass()._className+"["+EG.Array.getIdx(item.pItem.items,item)+"]["+item.cls+"]["+item.pItem.layout+"]";item=item.pItem;}
						alert(s);
						throw new Error("BorderLayout#doLayout:无region信息");
					}

					//尺寸描述
					var sd=Layout.sizeDesc(item);

					//是否为中间组件
					var isMid=false;

					//是否有子组件自动尺寸
					this.hasItemWA=this.hasItemWA||sd.isWA;
					this.hasItemHA=this.hasItemHA||sd.isHA;

					//识别方位
					if(item.region=="top")			{this.itemT=item;}
					else if(item.region=="bottom")	{this.itemB=item;}
					else if(item.region=="center")	{this.itemC=item;isMid=true;}
					else if(item.region=="left")	{this.itemL=item;isMid=true;}
					else if(item.region=="right")	{this.itemR=item;isMid=true;}
					else{
						throw new Error("BorderLayout#doLayout:暂不支持"+item.region);
					}

					//如果是中间组件
					if(isMid){
						this.mItems.push(item);

						this.hasMidWA=this.hasMidWA||sd.isWA;this.hasMidHA=this.hasMidHA||sd.isHA;
						this.hasMidWE=this.hasMidWE||sd.isWE;this.hasMidHE=this.hasMidHE||sd.isHE;
						this.hasMidWP=this.hasMidWP||sd.isWP;this.hasMidWP=this.hasMidWP||sd.isHP;
						this.hasMidWN=this.hasMidWN||sd.isWN;this.hasMidHN=this.hasMidHN||sd.isHN;
					}

					//设定position,设定缩进的方向
					if(item.region=="center"){
						EG.css(item,"position:absolute;");
						if(item.setCollapseAble){
							item.setCollapseAble(false);
						}
					}else{
						EG.css(item,"position:absolute;"+item.region+":0px");
						if(item.collapseAble&&item.setCollapseBehavior){
							item.setCollapseBehavior(item.region);
						}
					}

					//清空尺寸
					item.width=item.oWidth;
					item.height=item.oHeight;
					EG.css(item.getElement(),"width:auto;height:auto");
				}

				//2.判断外层尺寸计算是否参考中间尺寸计算方式
				this.refMW=false;//外层宽度是否参考中间
				this.refMH=false;//外层高度是否参考中间
				if(this.mItems.length>0){
					//中间L不含空也不含百分比=(固定值||自动)
					this.refMW=this.isCtWA&&(!this.hasMidWE&&!this.hasMidWP);
					//存在有固定值||自动
					this.refMH=this.isCtHA&&(this.hasMidHN||this.hasMidHA);
				}
			}
		};
	});


})();

/**
 * @class EG.ui.layout.TableLayout
 * <AUTHOR>
 * @extends EG.ui.Layout
 * Table布局器
 */
(function(){
	EG.define("EG.ui.layout.TableLayout",[
		"EG.ui.Layout"
	],function(Layout,ME){
		return {

			extend:Layout,

			config:{
				/** @cfg {?Number} maxRow 最大行 */
				maxRow		:0,
				/** @cfg {?Number} maxCol 最大列 */
				maxCol		:0,
				/** @cfg {?Number} cellSpacing 间隙 */
				cellSpacing :0,
				/** @cfg {?Number} cellPadding 间隔 */
				cellPadding :0,
				/** @cfg {?Number} border 边框 */
				border		:0,
				/** @cfg {?String} style 样式 */
				style		:null
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 * @param {EG.ui.Container} container 容器
			 */
			constructor:function(cfg,container){
				this.container	=container;			//容器
				this.initConfig(cfg);
				this.addOnLayout=true;				//layout添加模式
			},

			/**
			 * 获取Item
			 * @param {Object} cfg 配置
			 */
			getItem:function(cfg) {
				return EG.Array.get(cfg);
			},

			/**
			 * 补充列
			 */
			fixPos:function(){
				for(var i = 0,l=this.items.length;i<l; i++) {
					var pos=this.items[i].pos||[i,0];
					if(!EG.isArray(pos)) throw new Error("EG.ui.layout#fixPos:item的pos类型需为数组");
					if(pos.length==0){
						pos=[i,0];
					}else if(pos.length==1){
						pos=[pos[0],0];
					}else if(pos.length==2){
						//
					}else{
						throw new Error("EG.ui.layout#fixPos:item的pos长度最大为2");
					}


					this.maxRow=Math.max(pos[0]+1,this.maxRow);
					this.maxCol=Math.max(pos[1]+1,this.maxCol);

					this.items[i].pos=pos;
				}
			},

			/**
			 * 重新创建Table中的Row,Col
			 */
			resetTable:function(){
				//清除掉所有Row
				EG.DOM.removeAllRows(this.tbody);

				//创建Row
				for(var r=0;r<this.maxRow;r++){
					var tr=EG.CE({pn:this.tbody,tn:"tr"});
					//创建Col
					for(var c=0;c<this.maxCol;c++){
						EG.CE({pn:tr,tn:"td"});
					}
				}
			},

			/**
			 * 放置Items
			 */
			putItems:function(){
				for(var i=0,l=this.items.length;i<l; i++) {
					var item=this.items[i];
					var pos=item.pos;
					var el=item.getElement();
					var td=this.tbody.childNodes[pos[0]].childNodes[pos[1]];
					if(item.vAlign){
						EG.css(td,"vertical-align:"+item.vAlign);
					}

					//
					if(EG.Style.current(el).position=="absolute"){
						EG.css(el,"position:relative");
					}

					td.appendChild(el);
				}
			},

			/**
			 * 自动合并,设定宽比
			 */
			autoSpan:function(){
				var trs=this.tbody.childNodes;
				for ( var j = 0,jl= trs.length; j <jl; j++) {
					var tds=trs[j].childNodes;
					var rc=0;
					var oc=tds.length;

					//删除末端空TD
					for(var k=oc-1;k>=0;k--){
						if(tds[k].innerHTML=="") {
							rc++;
							trs[j].removeChild(tds[k]);
						}else{
							break;
						}
					}

					if(tds.length==0) continue;

					//设定末TD的colSpan
					if(rc>0){
						var tdEnd=tds[tds.length-1];
						tdEnd.colSpan=rc+1;
					}


					//设定TD宽比
					var p=parseInt(100/oc);
					for(var k=0,kl=tds.length;k<kl;k++){
						if(k==kl-1){
							tds[k].width=parseInt(((rc>0)?(p*(rc+1)):p))+"%";
						}else{
							tds[k].width=p+"%";
						}
					}
				}
			},

			/**
			 * @implements {EG.ui.Layout.prototype.doLayout}
			 */
			doLayout:function(){
				this.items		=this.container.items;	//items
//			if(this.container.id=="p2"){
//				alert("layout");
//			}
				this.hideItems();

				var cEle=this.container.getItemContainer();
				if(!this.table) {
					//TODO 当container Width,height为auto时候不指定
					this.table=EG.CE({tn:"table",style:"width:100%;table-layout:fixed;"+(this.style?this.style:""),cellPadding:this.cellPadding,cellSpacing:this.cellSpacing,border:this.border});
					this.tbody=EG.CE({pn:this.table,tn:"tbody",style:"width:100%;"});
				}

				//在父元素Clear后，关系被取消
				cEle.appendChild(this.table);


				//修正坐标
				this.fixPos();
				//重置Table
				this.resetTable();
				//放置Items
				this.putItems();
				//自动合并行
				this.autoSpan();
//			if(this.container.id=="fp") alert(this.container)

				for(var i=0,l=this.items.length;i<l;i++){
					var item=this.items[i];
					item.render();//渲染
				}

				this.autoSize();
			},

			/**
			 * 自动尺寸
			 */
			autoSize:function(){
				var aw=this.container.isAutoWidth();
				var ah=this.container.isAutoHeight();
				if(aw||ah){
					var s=EG.getSize(this.table);
					if(aw)	this.container.setInnerWidth(s.outerWidth);
					if(ah)	this.container.setInnerHeight(s.outerHeight);
				}
			}
		};
	});
})();

/**
 * @class EG.ui.layout.LineLayout
 * <AUTHOR>
 * @extends EG.ui.Layout
 * 线性布局器
 */
(function(){
	EG.define("EG.ui.layout.LineLayout",[
		"EG.ui.Layout"
	],function(Layout,ME){
		return {
			extend:Layout,

			config:{
				/** @cfg {?String} direct 方向 */
				direct		:"H",
				align		:null
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 * @param {EG.ui.Container} container 容器
			 */
			constructor:function(cfg,container){
				this.container	=container;			//容器
				this.initConfig(cfg);
				if(!this.align){
					this.align=this.direct=="H"?"left":"top";//默认值:左||上
				}
			},

			/**
			 * 获取Item
			 * @param {Object} cfg 配置
			 */
			getItem:function(cfg) {
				return EG.Array.get(cfg);
			},

			/**
			 * 布局
			 * @implements {EG.ui.Layout.prototype.doLayout}
			 */
			doLayout:function(){
				//初始化
				this.init();

				//自动尺寸||有子组件自动尺寸时
				if(this.isCtWA||this.isCtHA||this.hasItemHA||this.hasItemWA){

					//欲分配尺寸
					this.setItemSize();

					//先计算auto子组件外尺寸
					this.renderItemsOuter();
					//自动尺寸
					if(this.isCtWA||this.isCtHA) this.autoSize();
				}

				//子组件尺寸有自动时,要重新分配尺寸(只渲染子组件外层尺寸),不分配时是正常的渲染，
				if(!this.container.isRenderingOuter){
					//
					this.setItemSize(true);

					//最终定位
					var sub=0;
					for(var i=0,l=this.items.length;i<l;i++){
						var item=this.items[i];

						item.render();
						if(this.direct=="H"){
							EG.css(item,"position:absolute;"+this.align+":"+sub+"px");
							sub+=item.getSize().outerWidth;
						}else{
							EG.css(item,"position:absolute;"+this.align+":"+sub+"px");
							sub+=item.getSize().outerHeight;
						}
					}
				}
			},

			setItemSize:function(force){
				//如果宽高均为自动:子宽只能是WN或WA,高如果有HE或HP或HN默认使用最大值
				var max$= 0,	//最大尺寸
					unuse$=0,	//待分配数量
					avg$= 0,	//待平均尺寸
					use$=0  	//已占用尺寸
					;

				var sds={};

				//自动尺寸时如果有同向的子组件自动尺寸，则无法分配
				if(!force){
					if(this.direct=="H"){
						if(this.isCtWA&&this.hasItemWA) return;
					}else{
						if(this.isCtHA&&this.hasItemHA) return;
					}
				}

				for(var i=0,l=this.items.length;i<l;i++){
					var item=this.items[i];
					var sd=Layout.sizeDesc(item);
					sds[i]=sd;

					//如果父不是自动宽,子是百分比,则分配子宽
					if(this.direct=="H"){
						if(!this.isCtWA&&sd.isWP){item.width	=size2Num(item.getOWidth(),this.ctW);}
					}else{
						if(!this.isCtHA&&sd.isHP){item.height	=size2Num(item.getOHeight(),this.ctH);}
					}

					//如果父为自动高,计算最大高
					if(this.direct=="H"){
						if(this.isCtHA){
							if(sd.isHA){
								if(force) max$=Math.max(max$,item._outerSize.outerHeight);
								else return;
							}else if(sd.isHN){
								max$=Math.max(max$,item.height);
							}
						}
					}else{
						if(this.isCtWA){
							if(sd.isWA){
								if(force) max$=Math.max(max$,item._outerSize.outerWidth);
								else return;
							}else if(sd.isWN){
								max$=Math.max(max$,item.width);
							}
						}
					}

					//非空占用
					if(this.direct=="H"){
						if(!sd.isWE){
							if(sd.isWA){
								item.render();
								use$+=item.getSize().outerWidth;
							}else{
								use$+=item.width;
							}
						}else{
							unuse$++;
						}
					}else{
						if(!sd.isHE){
							if(sd.isHA){
								item.render();
								use$+=item.getSize().outerHeight;
							}else{
								use$+=item.height;
							}
						}else{
							unuse$++;
						}
					}
				}

				//如果父不是自动高,最大高为父高,
				if(this.direct=="H"){
					if(!this.isCtHA){
						max$=this.ctH;
					}
					if(unuse$) avg$=parseInt((this.ctW-use$)/unuse$);
				}else{
					if(!this.isCtWA){
						max$=this.ctW;
					}
					if(unuse$) avg$=parseInt((this.ctH-use$)/unuse$);
				}

				//设置所有子高为最大高
				for(var i=0,l=this.items.length;i<l;i++){
					var item=this.items[i];
					var sd=sds[i];
					if(this.direct=="H"){
						item.height	=max$;
						if(sd.isWE){
							item.width=avg$;
						}
					}else{
						item.width	=max$;
						if(sd.isHE){
							item.height=avg$;
							//alert(this.ctH+"/"+use$+"/"+unuse$+"/"+avg$)
						}
					}
				}
			},

			/**
			 * 自动尺寸
			 */
			autoSize:function(){
				//var cEle=this.container.getElement();
				var h=0,w=0;
				for(var i=0,l=this.items.length;i<l;i++){
					var item=this.items[i];
					if(this.direct=="V"){
						if(this.isCtHA){h+=this.getItemHeight(item);}
						if(this.isCtWA){w=Math.max(w,this.getItemWidth(item));}
					}else{
						if(this.isCtHA){h=Math.max(h,this.getItemHeight(item));}
						if(this.isCtWA){w+=this.getItemWidth(item);}
					}
				}
				//alert(this.isCtHA+":"+h);
				if(this.isCtWA)	{this.container.setInnerWidth(w);	this.ctW=w;}
				if(this.isCtHA)	{this.container.setInnerHeight(h);	this.ctH=h;}
			},
			getItemWidth:function(item)	{return item.isContainer&&item.isAutoWidth()	?item._outerSize.outerWidth:item.getOWidth();},
			getItemHeight:function(item){return item.isContainer&&item.isAutoHeight()	?item._outerSize.outerHeight:item.getOHeight();},

			init:function(){
				this.items		=this.container.items;	//items

				this.hideItems();

				//设定样式
				var curPos=EG.Style.current(this.container.getItemContainer()).position;
				if(curPos!="absolute"&&curPos!="relative"){
					EG.css(this.container.getItemContainer(),"position:relative");
				}

				//容器尺寸自动宽高
				this.isCtWA=this.container.isAutoWidth();
				this.isCtHA=this.container.isAutoHeight();
				//子组件尺寸有自动宽高
				this.hasItemWA=false;this.hasItemHA=false;
				//子组件尺寸有固定宽高
				this.hasItemWN=false;this.hasItemHN=false;
				//子组件尺寸有空宽高
				this.hasItemWE=false;this.hasItemHE=false;
				//子组件尺寸有百分比宽高
				this.hasItemWP=false;this.hasItemHP=false;

				this.ctW=this.isCtWA?-1:this.container.getInnerWidth();
				this.ctH=this.isCtHA?-1:this.container.getInnerHeight();

				//检测参数、设置尺寸
				for(var i=0,l=this.items.length;i<l;i++){
					var item=this.items[i];

					//尺寸描述
					var sd=Layout.sizeDesc(item);

					//是否有子组件自动尺寸
					this.hasItemWA=this.hasItemWA||sd.isWA;this.hasItemHA=this.hasItemHA||sd.isHA;
					this.hasItemWN=this.hasItemWN||sd.isWN;this.hasItemHN=this.hasItemHN||sd.isHN;
					this.hasItemWE=this.hasItemWE||sd.isWE;this.hasItemHE=this.hasItemHE||sd.isHE;
					this.hasItemWP=this.hasItemWP||sd.isWP;this.hasItemHP=this.hasItemHP||sd.isHP;

					//如果父为自动，子组件不能含空||百分比
					if(this.direct=="H"){
						if(this.isCtWA&&(sd.isWE||sd.isWP)) throw new Error("EG.ui.layout.LineLayout:父组件宽度自动时，子组件宽度不能同为空||百分比");
					}else{
						if(this.isCtHA&&(sd.isHE||sd.isHP)) throw new Error("EG.ui.layout.LineLayout:父组件高度自动时，子组件高度不能同为空||百分比");
					}

					//清空尺寸
					item.width=item.oWidth;
					item.height=item.oHeight;
					EG.css(item.getElement(),"width:auto;height:auto");
				}

				//检测
				if(this.direct=="H"){
					if(this.isCtHA&&(!this.hasItemHN&&!this.hasItemHA)) throw new Error("EG.ui.layout.LineLayout:横向时,父组件高度为自动时,子组件高度不能既没有固定也没有自动");
				}else{
					if(this.isCtWA&&(!this.hasItemWN&&!this.hasItemWA)) throw new Error("EG.ui.layout.LineLayout:纵向时,父组件宽度为自动时,子组件宽度不能既没有固定也没有自动");
				}
			}
		};
	});

	var size2Num=EG.Style.size2Num;
})();

/**
 * @class EG.ui.Panel
 * <AUTHOR>
 * @extends EG.ui.Container
 * 面板
 */
(function() {
	EG.define("EG.ui.Panel",[
		"EG.ui.Container"
	],function(Container,ME){
		return {
			alias:"panel",
			extend:Container,
			constructor:function(cfg){
				this.callSuper([cfg]);
			}
		};
	});
})();
(function(){
	/**
	 * XPanel是带标题,可拖拽,可缩进的面板
	 */
	EG.define("EG.ui.XPanel",[
		"EG.ui.Panel",
		"EG.ui.Item"
	],function(Panel,Item,ME){
		return {
			alias:"xPanel",
			extend:Panel,
			config:{
				cls				:"eg_xpanel",
				/** @cfg {?Boolean} showTitle 显示标题 */
				showTitle		:true,
				/** @cfg {?Boolean} showExpand 显示扩展 */
				showExpand		:true,
				/** @cfg {?Boolean} showBorder 显示边框 */
				showBorder		:true,
				/** @cfg {?String} title 标题 */
				title			:null,
				collapseBehavior:"top",
				/** @cfg {?Boolean} collapseAble 能否收缩 */
				collapseAble	:false,
				collapsed		:false,
				barConfig		:null,
				bodyStyle		:null,
				headStyle		:null
			},
			constructor:function(cfg){
				this.callSuper([cfg]);

				this.setCollapseAble(this.collapseAble);
			},

			/**
			 * 创建
			 */
			build:function(){
				var me=this;
				this.element=EG.CE({tn:"div",cn:[
					this.dHead=EG.CE({tn:"div",cls:this.cls+"-dHead",cn:[
						this.dCollapse=EG.CE({tn:"div",cls:this.cls+"-dCollapse "+this.cls+"-dCollapse-"+this.collapseBehavior,onclick:function(){
							var d=EG.Style.isHide(me.dBody);
							me.collapse(!d);
						}}),
						this.dTitle=EG.CE({tn:"div",cls:this.cls+"-dTitle"})
					]}),
					this.dBody=EG.CE({tn:"div",cls:this.cls+"-dBody"})
				]});

				//设置标题
				if(this.title)		this.setTitle(this.title);

				//设置Bar
				if(this.barConfig)	this.setBar(this.barConfig);

				//设置Body样式
				if(this.bodyStyle)	EG.css(this.dBody,this.bodyStyle);

				//设置Head样式
				if(this.headStyle)	EG.css(this.dHead,this.headStyle);
			},

			/**
			 * 设置Bar
			 * @param bar
			 */
			setBar:function(bar){
				if(!this.dBar){
					this.dBar=new Panel({cls:this.cls+"-dBar"});
					this.dHead.appendChild(this.dBar.getElement());
				}
				this.dBar.clear();
				this.dBar.addItem(bar);
			},
			/**
			 * 渲染
			 */
			render:function(){
				if (this.element.parentNode == EG.getBody()) {
					EG.css(EG.getBody(),"margin:0px;padding:0px;width:100%;height:100%");
					EG.css(this.element,"position:absolute;left:0px;top:0px;");
				}
				Item.fit(this);

				//关键控制隐藏Body
				var dSize={width:"100%"};
				if(this.collapsed){
					dSize["height"]="100%";
					EG.hide(this.dBody);
				}else{
					EG.show(this.dBody);
				}

				//设置header
				EG.hide(this.dHead);
				Item.fit({
					element	:this.dHead,
					dSize	:dSize
				});
				EG.show(this.dHead);
				if(!this.collapsed){
					if(this.dBar){

						//BD:小数撑多了
						this.dBar.width	=Math.floor(EG.getSize(this.dHead).innerWidth-EG.getSize(this.dTitle).outerWidth-EG.getSize(this.dCollapse).outerWidth)-1;

//					if(EG.Browser.isIE()){
//						this.dBar.width=this.dBar.width-1;
//					}
//					alert(this.dTitle.offsetWidth)
//					alert("this.dBar.width:"+
//						EG.getSize(this.dHead).innerWidth+","+
//						EG.getSize(this.dTitle).outerWidth+","+
//						EG.getSize(this.dCollapse).outerWidth+","+
//						this.dBar.width)
						this.dBar.height=parseInt(EG.getSize(this.dHead).innerHeight);
						this.dBar.render();
					}
				}

				//设置body
				if(!this.collapsed){
					var sizeHead=EG.getSize(this.dHead);
					Item.fit({
						element	:this.dBody,
						dSize	:{
							width:"100%",
							height:(EG.getSize(this.element).innerHeight-sizeHead.outerHeight)
						}
					});

					if(this.items.length>0){//执行布局
						this.doLayout();
					}

//				if(this.oHeight=="auto"){
//					EG.css(this.element,"height:"+(sizeHead.outerHeight+EG.getSize(this.dBody).outerHeight)+"px");
//					//dSize["height"]=(EG.getSize(this.element).innerHeight-sizeHead.outerHeight);
//				}
				}else{
					//EG.css(this.element,"height:auto");
				}
			},
			/**
			 * 获取原高
			 * @return {*}
			 */
			getOHeight:function(){
				return this.oHeight;
			},
			/**
			 * 获取原宽
			 * @return {*}
			 */
			getOWidth:function(){
				if(this.collapsed){
					var s=EG.getSize(this.element);
					return s.outerWidth-s.innerWidth+EG.getSize(this.dCollapse).outerWidth;
				}else{
					return this.oWidth;
				}
			},		/**
			 * 获取Item容器
			 * @return {*}
			 */
			getItemContainer:function(){
				return this.dBody;
			},
			/**
			 * 设置能否缩进
			 * @param collapseAble
			 */
			setCollapseAble:function(collapseAble){
				this.collapseAble=collapseAble;
				if(!this.collapseAble){
					EG.hide(this.dCollapse);
				}
			},
			/**
			 * 设置缩进行为
			 * @param collapseBehavior 缩进行为
			 */
			setCollapseBehavior:function(collapseBehavior){
				this.collapseBehavior=collapseBehavior;
				this.refreshCollapseCln(this.collapsed);
			},
			/**
			 * 刷新缩进按钮样式
			 * @param collapsed
			 */
			refreshCollapseCln:function(collapsed){
				var b=this.collapseBehavior;
				var tv=false;
				if(collapsed){
					switch(this.collapseBehavior){
						case "top":			b="bottom"		;			break;
						case "right":		b="left"		;tv=true;	break;
						case "bottom":		b="top"			;			break;
						case "left":		b="right"		;tv=true;	break;
					};
				}
				EG.setCls(this.dCollapse,["dCollapse","dCollapse-"+b],this.cls);
				if(tv){
					EG.css(this.dTitle,"writing-mode:lr-tb");
					if(this.dBar) EG.hide(this.dBar);
				}else{
					EG.css(this.dTitle,"writing-mode:;");
					if(this.dBar) EG.show(this.dBar);
				}
			},

			/**
			 * 收缩
			 */
			collapse:function(collapsed){
				//刷新样式
				this.refreshCollapseCln(collapsed);
				if(collapsed){
					var bSize=EG.getSize(this.dBody);
					EG.hide(this.dBody);

					//保存当前尺寸
					this._oWidth	=this.oWidth;
					this._oHeight	=this.oHeight;
					if(EG.$in(this.collapseBehavior,["top","bottom"])){
						this.oHeight=this.height-bSize.outerHeight;
					}else if(EG.$in(this.collapseBehavior,["left","right"])){
						this.oWidth=20;
						EG.css(this.dHead,"overflow:hidden;writing-mode:lr-tb;");
					}
				}else{
					this.oWidth=this._oWidth;
					this.oHeight=this._oHeight;
					//EG.show(this.dBody);
					EG.css(this.dHead,"height:auto;");
				}

				this.collapsed=collapsed;

				//父容器渲染
				if(this.pItem){
					this.pItem.render();
				}
			},

			/**
			 * 设置标题
			 * @param title
			 */
			setTitle:function(title){
				this.title=title;
				this.dTitle.innerHTML=this.title;
			},

			/**
			 * 设置内高(dBody)
			 * @param h
			 */
			setInnerHeight:function(h){
				//设定Body高度
				Item.pack({
					pEle:this.dBody,
					height:h
				});

				//设定外层高度
				var rs=Item.pack({
					pEle:this.element,
					height:EG.getSize(this.dBody).outerHeight+EG.getSize(this.dHead).outerHeight
				});

				this.height=rs.height;
			},
			/**
			 * 获取内高(dBody)
			 * @return {*}
			 */
			getInnerHeight:function(){
//			alert(this.oHeight);
//			if(typeof(this.oHeight)=="number"){
				return EG.getSize(this.element).innerHeight-EG.getSize(this.dHead).outerHeight;
//			}else{
//				return this.oHeight;
//			}
			}
		};
	});


})();

/**
 * @class EG.ui.TabPanel
 * <AUTHOR>
 * @extends EG.ui.Containeralias:"formItem",
 * 选项卡面板,不支持布局管理器
 */
(function(){
	EG.define("EG.ui.TabPanel",[
		"EG.ui.Container",
		"EG.ui.Item",
		"EG.ui.Tab",
		"EG.ui.Panel",
	],function(Container,Item,Tab,Panel,ME){
		return {
			alias:"tabPanel",
			extend:Container,

			config:{
				runOnSelectOnBuild	:true,			//是否在创建时运行Tab的 onselect
				cls					:"eg_tabPanel",	//CSS类
				direct				:"top",			//选项卡位置
				isShowTabs			:true,			//显示Tabs
				tabsStyle			:null,			//当tab的样式
				tabWidthLR			:90				//左右时Tab宽度
			},

			constructor:function(cfg){
				this.curIdx		=-1;//当前已选择的索引
				this.panels		=[];
				this.tabs		=[];
				this.callSuper([cfg]);
			},

			/**
			 * @override
			 */
			afterBuild:function(){
				if(this.itemsConfig&&this.itemsConfig.length>0){
					this.addItem(this.itemsConfig,true);
					this.select(0);
				}
			},

			/**
			 * @override {EG.ui.Container#build}
			 */
			build:function(){
				this.element=EG.CE({tn:"div",cn:[
					this.dPanels=EG.CE({tn:"div",cls:this.cls+"-panels"	+this.direct}),
					this.dTabs	=EG.CE({tn:"div",cls:this.cls+"-tabs"	+this.direct})	//DS:dTabs放在下方是为了Tab可以遮盖Panel
				]});

				if(!this.isShowTabs){
					this.hideTabs(false);
				}
			},

			/**
			 *
			 * @param render
			 */
			showTabs:function(render){
				if(render==null) render=true;
				EG.show(this.dTabs);
				if(render) this.render();
			},

			/**
			 *
			 * @param render
			 */
			hideTabs:function(render){
				if(render==null) render=true;
				EG.hide(this.dTabs);
				if(render) this.render();
			},

			/**
			 * @override
			 *
			 * 渲染时，先渲染dTabs,根据不同的方向计算剩余空间给dPanels位置并定位
			 */
			render:function() {
				//修父BODY样式
				Item.fixBody(this.element);

				//设定外部尺寸
				Item.fit(this);

				//size
				var size=EG.getSize(this.element);
				var tabsSize={
					outerHeight:0,
					outerWidth:0
				};
				var panelsSize=EG.getSize(this.dPanels);

				//Tab上下
				if(this.direct=="top"||this.direct=="bottom"){
					//设定dTabs尺寸
					if(this.showTabs){
						Item.fit({
							element:this.dTabs,
							dSize:{width:"100%"},
							pSize:{width:size.innerWidth},
							type:"width"
						});
						EG.css(this.dTabs,"position:absolute;"+this.direct+":0px;left:0px");
						tabsSize=EG.getSize(this.dTabs);
					}

					//设定dPanels尺寸
					//距离=tabSize.outerHeight-dPanels方向边宽(遮盖的)
					var dist=tabsSize.outerHeight-(this.direct=="top"?panelsSize.borderTop:panelsSize.borderBottom);
					Item.fit({
						element:this.dPanels,
						pSize:{
							width	:size.innerWidth,
							height	:size.innerHeight-tabsSize.outerHeight
						}
					});
					EG.css(this.dPanels,"position:absolute;"+this.direct+":"+dist+"px;left:0px");

				//Tab左右
				}else if(this.direct=="left"||this.direct=="right"){
					//设定dTabs尺寸
					if(this.showTabs){
						Item.fit({
							element:this.dTabs,
							dSize:{height:size.innerHeight},
							pSize:size,
							type:"height"
						});

						EG.css(this.dTabs,"position:absolute;"+this.direct+":0px;top:0px");
						tabsSize=EG.getSize(this.dTabs);
					}

					//设定dPanels尺寸
					//距离=tabSize.outerWidth-dPanels方向边宽(遮盖的)
					var dist=tabsSize.outerWidth-(this.direct=="left"?panelsSize.borderLeft:panelsSize.borderRight);
					Item.fit({
						element:this.dPanels,
						pSize:{width:size.innerWidth-dist,height:size.innerHeight}
					});
					EG.css(this.dPanels,"position:absolute;"+this.direct+":"+dist+"px;top:0px");
				}

				this.doLayout();
			},

			/**
			 * 添加组件
			 * @param cfg 配置
			 * @param {Boolean?} render 渲染
			 * @param {Number?} selectIdx 选中坐标
			 * @returns {EG.ui.Tab}
			 */
			addItem:function(cfg,render,selectIdx){
				if(render==null) render=true;
				if(typeof(selectIdx)=="undefined") selectIdx=-1;

				//!:首次添加时Tabs的TB高度||LR宽度才被计算出,需要重新渲染
				var needRender=this.items.length==0;

				var cfgs=(!EG.isArray(cfg))?[ cfg ]:cfg;
				var tab,panel,tab0;
				for (var i = 0,il = cfgs.length; i < il; i++) {
					cfg = cfgs[i];

					if(cfg["panel"]["width"]==null) 	cfg["panel"]["width"]="100%";
					if(cfg["panel"]["height"]==null) 	cfg["panel"]["height"]="100%";

					cfg["tab"]["clsPre"]		=this.cls+"-tabs"+this.direct;
					cfg["panel"]["className"]	=this.cls+"-"+"panels-panel";

					tab		=new Tab	(this,cfg["tab"]);
					panel	=new Panel(cfg["panel"]);

					this.tabs.push(tab);
					this.panels.push(panel);

					tab		.pItem=this;
					panel	.pItem=this;

					this.dTabs.appendChild(tab.getElement());
					this.dPanels.appendChild(panel.getElement());

					this.items.push(panel);

					if(i==0) tab0=tab;
				}

				//在被添加到DOM树中时首次
				if(needRender&&render&&EG.DOM.contains(this.element)){
					this.render();
				}

				//自动选择新添加的
				if(selectIdx!==null){
					this.select(selectIdx);
				}

				return tab;
			},

			/**
			 * 关闭
			 * @param idx 索引
			 */
			close:function(idx){
				// 获取Tab
				var tab;
				if(idx instanceof Tab){
					tab=idx;
					idx=this.getTabIdx(tab);
				}else{
					tab=EG.Array.get(this.tabs, idx);
				}

				var p=tab.getPanel();

				var selected=(idx==this.curIdx);	//待删除的是否已选中
				var selectTab=this.tabs[this.curIdx];

				//销毁
				tab.destroy();
				p.destroy();

				//DOM移除
				this.dTabs.removeChild	(tab.element);
				this.dPanels.removeChild(p.getElement());


				EG.Array.del(this.items	,idx);
				EG.Array.del(this.tabs	,idx);
				EG.Array.del(this.panels,idx);

				//刷新
				this.curIdx=EG.Array.getIdx(this.tabs,selectTab);

				if(!selected) return;

				//自动选择Tab
				if(idx>=this.panels.length) idx=this.panels.length-1;
				if(idx>=0) this.select(idx);
			},

			/**
			 * 关闭所有
			 */
			closeAll:function(){
				for(var i=this.tabs.length-1;i>=0;i--){
					this.close(i);
				}
			},

			/**
			 * 选择选项卡
			 * @param tab 索引||Tab
			 * @param noRender
			 */
			doSelect:function(tab,noRender){
				var idx=-1;

				// 变换样式,隐藏Panel
				for (var i=0,l=this.panels.length;i<l;i++) {
					var p = this.panels[i];
					var t = this.tabs[i];
					if (t!= tab) {
						EG.setCls(t.element,"tab",this.cls+"-tabs"+this.direct);
						EG.hide(p.getElement());
					} else {
						idx=i;
					}
				}

				//CP[Chrome]:如果不先隐藏掉其它Panel,会一瞬间把外层容器撑大，外层容器是overflow,当子再隐藏的时候不会还原
				EG.setCls(this.tabs[idx].element,["tab","selected"],this.cls+"-tabs"+this.direct);
				EG.show(this.panels[idx].getElement());
				if(!noRender) this.panels[idx].render();

				//设置当前索引
				this.curIdx = idx;
			},

			/**
			 * 选择
			 */
			select:function(idx){
				var tab=(idx instanceof Tab)?idx:EG.Array.get(this.tabs, idx);
				tab.select();
			},

			/**
			 * 获取Tab
			 */
			getTabs:function() {
				return this.tabs;
			},

			/**
			 * 获取Panels
			 */
			getPanels:function() {
				return this.panels;
			},

			/**
			 * 获取Panel
			 *
			 * @param idx 索引
			 * @returns {EG.ui.Panel}
			 */
			getPanel:function(idx) {
				return EG.Array.get(this.panels,idx);
			},

			/**
			 * 获取Panel索引
			 *
			 * @param {EG.ui.Panel} panel 面板
			 * @returns {Number}
			 */
			getPanelIdx:function(panel){
				return EG.Array.getIdx(this.panels,panel);
			},

			/**
			 * 获取Tab
			 *
			 * @param idx 索引
			 * @returns {EG.ui.TabPanel.Tab}
			 */
			getTab:function(idx) {
				return EG.Array.get(this.tabs,idx);
			},

			/**
			 * 获取Tab索引
			 *
			 * @param tab {Number}
			 */
			getTabIdx:function(tab) {
				for ( var i = 0; i < this.tabs.length; i++){
					if (this.tabs[i] == tab) {
						return i;
					}
				}
			},

			/**
			 * 获取选中的Tab索引
			 */
			getSelectedIdx:function() {
				return this.curIdx;
			},

			/**
			 * 获取选中Tab
			 * @returns {EG.ui.TabPanel.Tab}
			 */
			getSelectedTab:function() {
				return this.getTab(this.curIdx);
			},

			/**
			 * 获取个数
			 * @returns {Number}
			 */
			getLength:function(){
				return this.items.length;
			},

			/**
			 * 执行
			 */
			doLayout:function() {
				if(!this.element.parentNode) return;

				var sIdx=this.getSelectedIdx();
				if(sIdx<0) return;

				var p=this.panels[sIdx];
				if(p==null) return;
				p.pSize=EG.getSize(this.dPanels);
				p.render();
			},

			/**
			 * 销毁
			 */
			destroy:function(){
				for(var i=0;i<this.tabs.length;i++){
					this.tabs[i].destroy();
				}

				for(var i=0;i<this.panels.length;i++){
					this.panels[i].destroy();
				}

				this.tabs=null;
				this.panels=null;
				this.items=null;
			}
		};
	});
})();

(function(){
	/**
	 * EG.ui.Tab 选项卡
	 */
	EG.define("EG.ui.Tab",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			alias:"tab",
			extend:Item,
			config:{
				title		:"选项卡",	//标题
				closeable	:false,		//是否可以关闭
				onclick		:null,		//点击时的事件
				onselect	:null,		//被选中时事件
				onclose		:null,		//在关闭时事件
				afterselect	:null,		//选中后事件
				clsPre		:null,
				/** @cfg {Number|Boolean?} tabIndex Tab值 */
				tabIndex	:false,
				onfocus		:null
			},
			constructor:function(tabPanel,cfg){
				this.pTabPanel	=tabPanel;

				this.callSuper([cfg]);

				//设置能否关闭
				this.setCloseable(this.closeable);
			},

			/**
			 * 创建
			 */
			build:function(){
				var me=this;
				this.element=EG.CE({tn:"div",cls:this.clsPre+"-tab "+this.clsPre+"-selected",item:this,cn:[
					this.dTitle	=EG.CE({tn:"div"	,cls:this.pTabPanel.cls+"-tabs-tab-title",innerHTML:this.title}),
					this.dCloser=EG.CE({tn:"a"		,cls:this.pTabPanel.cls+"-tabs-tab-closer",item:this,
						onclick		:ME._events.dCloser.onclick,
						onmouseover	:ME._events.dCloser.onmouseover,
						onmouseout	:ME._events.dCloser.onmouseout
					})
				],
					onclick:ME._events.element.onclick,
					onkeydown:function(e){
						e=EG.Event.getEvent(e);
						if(e.keyCode==13){
							me.doClick();
						}
					}
				});

				if(typeof(this.tabIndex)=="number"){
					this.element.tabIndex=this.tabIndex;
				}

			},

			/**
			 * 设置是否能关闭
			 * @param closeable 可关闭
			 */
			setCloseable:function(closeable){
				this.closeable=closeable;
				if(this.closeable){
					EG.show(this.dCloser);
				}else{
					EG.hide(this.dCloser);
				}
			},

			/**
			 * 执行点击
			 */
			doClick:function() {
				if(this.onclick) this.onclick.apply(this);
				this.select();
			},

			/**
			 * 设置标题
			 * @param title 标题
			 */
			setTitle:function(title) {
				this.dTitle.innerHTML = title;
			},

			/**
			 * 获取索引
			 */
			getIdx:function() {
				return this.pTabPanel.getTabIdx(this);
			},

			/**
			 * 选中选项卡
			 */
			select:function() {
				if(this.onselect)	{this.onselect.apply(this);}
				this.pTabPanel.doSelect(this);
				if(this.afterselect){this.afterselect.apply(this);}
			},

			/**
			 * 是否已被选中
			 * @returns {Boolean}
			 */
			isSelected:function() {
				return this.pTabPanel.curIdx == this.getIdx();
			},

			/**
			 * 关闭
			 */
			close:function(){
				if(this.onclose) this.onclose.apply(this);
				this.pTabPanel.close(this);
			},

			/**
			 * 获取关联的Panel
			 * @returns {EG.ui.Panel}
			 */
			getPanel:function(){
				var i=this.getIdx();
				return this.pTabPanel.getPanel(i);
			},

			statics:{
				_events:{
					element:{
						onclick:function(e){
							var me=this.item;
							me.doClick();EG.Event.stopPropagation(e);
						}
					},
					dCloser:{
						onclick		:function(e){
							var me=this.item;
							me.close();EG.Event.stopPropagation(e);
						},
						onmouseover	:function(){
							var me=this.item;
							EG.setCls(this,["tabs-tab-closer","tabs-tab-closer_on"],me.pTabPanel.cls);
						},
						onmouseout	:function(){
							var me=this.item;
							EG.setCls(this,["tabs-tab-closer"],me.pTabPanel.cls	);
						}

					}

				}
			}
		};
	});
})();


/**
 * @class EG.ui.Tree
 * <AUTHOR>
 * @extends EG.ui.Item
 * 树
 */
(function(){
	/**
	 * EG.ui.Tree 树
	 */
	EG.define("EG.ui.Tree",[
		"EG.ui.Item",
		"EG.ui.TreeNode"
	],function(Item,TreeNode,ME){
		return {
			alias:"tree",
			extend:Item,
			config:{
				/** @cfg {String?} 根节点标题 */
				rootTitle		:"根目录",
				/** @cfg {Boolean?} 是否多选 */
				multiple		:false,
				/** @cfg {Boolean?} 是否启用Box,如果多选自动启用box */
				usebox			:false,
				/** @cfg {Boolean?} 是否可以拖拽 */
				dragable		:false,
				/** @cfg {String?} 样式 */
				cls				:"eg_tree",
				/** @cfg {Function?} 拖拽时事件 */
				ondrag			:null,
				/** @cfg {Boolean?} 能否取消选择 */
				deSelectable	:true,
				/** @cfg {Function?} 默认点击事件 */
				onclick			:null,

				nodesConfig		:null
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				//加载
				ME.load();

				this.callSuper([cfg]);

				//如果过选自动启用UseBox
				if(this.multiple) 	this.usebox=true;

				//创建RootNode
				this.rootNode=new TreeNode({
					title		:this.rootTitle,
					root		:true,
					onclick		:this.onclick,
					onclickSrc	:this.onclickSrc
				},this);
				this.rootNode.setClassNamePre(this.cls+"-");

				//是否能拖拽
				if(!this.dragable){
					EG.hide(this.rootNode.dInsert);
				}

				//根节点添加
				EG.CE({pn:this.element,ele:this.rootNode.getElement(),style:"margin-left:0px"});

				//隐藏Box
				if(!this.box){
					EG.hide(this.rootNode.box.getElement());
				}

				//清空初始化
				this.reset();

				//设置子节点
				if(this.nodesConfig){
					this.rootNode.setNodes(this.nodesConfig);
				}
			},

			setNodes:function(nodes,pNode){
				if(pNode==null) pNode=this.rootNode;
				for(var i=0;i<nodes.length;i++){

					pNode.add(new TreeNode(nodes[i]))
				}
			},

			/**
			 * 创建Element
			 */
			build:function(){
				this.element=EG.CE({tn:"div",cls:this.cls,unselectable:"on",item:this,
					onmousemove:function(e){
						e=EG.Event.getEvent(e);
						if(ME.curDrag!=null&&ME.curDrag.style.display!="none"){
							EG.css(ME.curDrag,"top:"+(e.clientY+10)+"px;left:"+(e.clientX+10)+"px;");
						}
					},
					onselectstart:function(){return false;}
				});
			},

			/**
			 * 清空初始化
			 */
			reset:function(){
				this.treeNodes		=[];														//所有树节点
				if(this.rootNode.selected){
					this.rootNode.blur();
				}

				this.selectedNode	=null;														//已选节点
				this.selectedNodes	=[];														//已选的多节点
				//this.rootNode		=null;														//根节点
				EG.DOM.removeChilds(this.rootNode.dChildNodes);
			},

			/**
			 * 渲染
			 */
			render:function(){
				Item.fit(this);
			},

			/**
			 * 添加
			 * @param cfg
			 */
			add:function(cfg) {
				TreeNode.prototype.add.apply(this.rootNode, arguments);
			},

			/**
			 * 添加子
			 * @param treeNode
			 */
			appendChild:function(treeNode) {
				treeNode.tree = this;
				this.childNodes.push(treeNode);
			},

			/**
			 * 获取选中的
			 */
			getSelected:function() {
				return this.multiple ? this.selectedNodes : this.selectedNode;
			},

			/**
			 * 设置选中的
			 * @param value
			 */
			setSelected:function(value){
				if(value==null) {
					value=[];
				}else if(!EG.Array.isArray(value)){
					value=[value];
				}

				for(var i=0;i<this.treeNodes.length;i++){
					var n=this.treeNodes[i];
					var selected=false;
					for(var j=0;j<value.length;j++){
						if(!selected&&n.value===value[j]){
							selected=true;
							break;
						}
					}

					n.select(selected);
				}
			},

			/**
			 * 设置值
			 * @param value
			 */
			setValue:function(value){
				this.setSelected(value);
			},

			/**
			 * 获取Element
			 */
			getElement:function() {
				return this.element;
			},

			/**
			 * 获取根节点
			 * @returns {EG.ui.TreeNode}
			 */
			getRootNode:function(){
				return this.rootNode;
			},

//		/**
//		 * 选中节点
//		 */
//		selectedNode:function(){},

			/**
			 * 删除所有节点
			 */
			removeAll:function(){
				this.rootNode.removeChilds();
			},

			/**
			 * 注册节点
			 *
			 * @param treeNode {TreeNode}
			 */
			registNode:function(treeNode) {
				this.treeNodes.push(treeNode);
				treeNode.tree = this;
				treeNode.setClassNamePre(this.cls+"-");
			},

			/**
			 * 卸载节点
			 *
			 * @param treeNode
			 */
			ungistNode:function(treeNode) {
				EG.Array.remove(this.treeNodes, treeNode);
			},

			/**
			 * 展开所有节点
			 */
			expandAll:function() {
				for ( var i = 0, l = this.treeNodes.length; i < l; i++)
					this.treeNodes[i].expand();
			},

			/**
			 * 收缩所有节点
			 */
			collapseAll:function() {
				for ( var i = 0, l = this.treeNodes.length; i < l; i++) {
					this.treeNodes[i].collapse();
				}
			},

			/**
			 * 展开到指定层数的节点
			 * @param maxlv
			 */
			expandLv:function(maxlv){
				this.collapseAll();
				var f=function(node,lv){
					node.expand();
					if(lv>=maxlv) return;
					lv++;
					if(node.childNodes.length>0) for(var i=0,l=node.childNodes.length;i<l;i++) f(node.childNodes[i],(lv));
				};
				f(this.getRootNode(),1);
			},
			/**
			 * 取消所有选择
			 */
			deSelect:function(){
				if(this.multiple){
					if(!this.selectedNodes) return;
					for(var i=this.selectedNodes.length-1;i>=0;i--){
						this.selectedNodes[i].select(false);
					}
				}else{
					if(!this.selectedNode) return;
					this.selectedNode.select(false);
				}
			},


			/**
			 * 展开某个节点链
			 * @param node
			 */
			expandChain:function(node){																			//
				this.collapseAll();
				while((node=node.parentNode)!=null) node.expand();
			},



			/**
			 * 获取选中后的值
			 */
			getValue:function(){
				var node=tree.getSelected();
				if(this.multiple){
					var vs=[];
					for(var i=0;i<node.length;i++){
						vs.push(node[i].value);
					}
					return vs;
				}else{
					return node.value;
				}
			},

			statics:{
				//拖拽线程
				dragThread:null,
				//当前坐标
				curXY:null,
				//当前拖拽对象
				curDrag:null,
				/**
				 * 是否正在拖拽
				 */
				isDraging:function() {
					return ME.curDrag != null&&!EG.Style.isHide(ME.curDrag);
				},
				loaded:false,
				/**
				 * 加载
				 */
				load:function(){
					if(ME.loaded) return;
					//监听Document onmouseup事件
					EG.bindEvent(EG.doc,"mouseup",function(){
						if(ME.dragThread!=null) window.clearTimeout(ME.dragThread);
						if(ME.curDrag){
							EG.hide(ME.curDrag);
						}
					});
					ME.loaded=true;
				}
			}
		};
	});

})();

/**
 * @class EG.ui.TreeNode
 * <AUTHOR>
 * @extends EG.ui.Item
 * 树节点
 */
(function(){
	EG.define("EG.ui.TreeNode",[
		"EG.ui.Item",
		"EG.ui.Box",
		"EG.ui.Tree"
	],function(Item,Box,Tree,ME){
		return {
			alias:"treeNode",
			extend:Item,
			config:{
				/** @cfg {String?} 标题 */
				title			:"节点",
				/** @cfg {Object?} 值 */
				value			:null,
				/** @cfg {Boolean?} 是否显示Boxs */
				showbox			:false,
				/** @cfg {Boolean?} 是否根节点 */
				root			:false,
				/** @cfg {Function?} 点击事件 */
				onclick			:null,
				/** @cfg {Function?} 拖拽事件 */
				ondrag			:null,
				/** @cfg {EG.ui.Tree?} 树 */
				tree			:null,
				/** @cfg {String?} CSS类 */
				cls				:"eg_tree",
				/** @cfg {String?} 标题样式 */
				titleStyle		:null
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 * @param {EG.ui.Tree?} tree 树
			 */
			constructor:function(cfg,tree){

				//点击事件源
				//this.onclickSrc=this;

				this.callSuper([cfg]);

				this.tree		=tree||this.tree;
				this.parentNode	=null;								//父节点
				this.childNodes	=[];								//子节点
				this.ondrag		=this.ondrag||(this.tree!=null?this.tree.ondrag:null);
				//Element
				var me=this;

				//BOX
				this.box=new Box({showText:false,style:"display:inline",onclick:function(){
					me.select(!me.selected);
				}});
				this.dNode.appendChild(this.box.getElement());
				EG.css(this.box.getElement().childNodes[0],"margin-left:3px");
			},

			/**
			 * 静态
			 */
			statics:{
				/**
				 * 事件
				 */
				_events:{
					dExpandBtn:{
						onclick:function(){
							var me=this.me;
							me.changeCollapsed();
						}
					},

					dTitle:{
						ondblclick:function(){
							var me=this.me;
							me.changeCollapsed();
						},
						onclick:function(){
							var me=this.me;
							me.select(me.tree.deSelectable?!me.box.selected:true);
							if(me.onclick) me.onclick.apply(me["onclickSrc"]||me);
						},
						onmouseup:function(){
							var me=this.me;
							if(Tree.isDraging()) Tree.curDrag["node"].moveto("in",me);
						},
						onmousedown:function(event){
							var me=this.me;
							//如果不能拖拽或对象为根节点，返回
							if(!me.tree.dragable||me==me.tree.rootNode) return;
							event=EG.Event.getEvent(event);
							Tree.curXY=[event.clientX,event.clientY];
							var curNe=me;
							//执行创建拖动线程
							Tree.dragThread=window.setTimeout(function(){
								if(Tree.curDrag==null) Tree.curDrag=EG.CE({pn:EG.getBody(),tn:"div",cls:"eg_drager"});
								Tree.curDrag.node=curNe;
								Tree.curDrag.innerHTML=curNe.title;
								EG.show(Tree.curDrag);
								EG.css(Tree.curDrag,"top:"+(Tree.curXY[1]+10)+"px;left:"+(Tree.curXY[0]+10)+"px;");
							},300);
						}

					},

					dInsert:{
						onmouseover:function(){
							var me=this.me;
							if(me==me.tree.rootNode) return;

							if(Tree.isDraging()){
								EG.css(this,"background-color:red;margin-left:7px;");
							}
						},
						onmouseout:function(){
							EG.css(this,"background-color:;margin-left:;");
						},
						onmouseup:function(){
							var me=this.me;
							if(me==me.tree.rootNode){
								return;
							}
							if(Tree.isDraging()){
								Tree.curDrag["node"].moveto("after",me);
								//Tree.curDrag=null;
							}
						}
					}

				}
			},
			/**
			 * 创建
			 */
			build:function(){
				//此处赋的className只是示意
				this.element=EG.CE({tn:"div",cn:[
					this.dNode=EG.CE({tn:"div",cn:[ 															//节点
						//树形展开符
						this.dExpandBtn=EG.CE({tn:"div",me:this,onclick:ME._events.dExpandBtn.onclick}),

						//标题
						this.dTitle=EG.CE({tn:"div",innerHTML:this.title,me:this,
							ondblclick	:ME._events.dTitle.ondblclick,
							onclick		:ME._events.dTitle.onclick,
							onmouseup	:ME._events.dTitle.onmouseup,
							onmousedown	:ME._events.dTitle.onmousedown
						})
					]}),
					//子节点集合
					this.dChildNodes=EG.CE({tn:"div"}),
					//插入的div
					this.dInsert=EG.CE({tn:"div",me:this,
						onmouseover	:ME._events.dInsert.onmouseover,
						onmouseout	:ME._events.dInsert.onmouseout,
						onmouseup	:ME._events.dInsert.onmouseup
					})
				]});

				if(this.titleStyle) EG.css(this.dTitle,this.titleStyle);
			},

			/**
			 * 执行点击
			 */
			doClick:function(){
				EG.Event.fireEvent(this.dTitle,"click");
			},

			/**
			 *
			 * @param nodes
			 * @param render
			 */
			setNodes:function(nodes,render){
				//清空现有节点
				this.removeChilds(false);
				for(var i=0;i< nodes.length;i++){
					var n=nodes[i];
					if(EG.Object.isLit(n)){
						n=new ME(n);
					}
					this.add(n);

					if(n.nodes){
						n.setNodes(n.nodes);
					}
				}
				if(render) this.render();
			},

			/**
			 * 设值标题
			 * @param title 标题
			 */
			setTitle:function(title){
				this.title=title;
				this.dTitle.innerHTML=title;
			},

			/**
			 * 设值
			 * @param value 值
			 */
			setValue:function(value){
				this.value=value;
			},

			/**
			 * 获值
			 */
			getValue:function(){
				return this.value;
			},

			/**
			 * 选中
			 * @param selected 是否选中
			 */
			select:function(selected){

				if(selected){
					this.focus();
					this.box.select(true);
					if(this.tree.multiple){
						if(!EG.Array.has(this.tree.selectedNodes,this)){
							this.tree.selectedNodes.push(this);
						}
					}else{
						if(this.tree.selectedNode!=null&&this.tree.selectedNode!=this) this.tree.selectedNode.select(false);
						this.tree.selectedNode=this;
					}
				}else{
					this.blur();
					this.box.select(false);
					if(this.tree.multiple){
						EG.Array.remove(this.tree.selectedNodes,this);
					}else this.tree.selectedNode=null;
				}
				this.selected=selected;
			},

			/**
			 * 设置ClassNamePre
			 * @param clsPre
			 */
			setClassNamePre:function(clsPre){
				this.element.className		=clsPre+"node";
				this.dNode.className		=clsPre+"node-dNode";
				this.dExpandBtn.className	=clsPre+"node-dNode-dExpandBtn";
				this.dTitle.className		=clsPre+"node-dNode-dTitle";
				this.dChildNodes.className	=clsPre+"node-dChildNodes";
				this.dInsert.className		=clsPre+"node-dInsert";
			},

			/**
			 * 失去焦点
			 */
			blur:function(){
				EG.Style.removeCls(this.dTitle,this.cls+"-node-dNode-selected");
			},

			/**
			 * 聚焦
			 */
			focus:function(){
				EG.Style.addCls(this.dTitle,this.cls+"-node-dNode-selected");
			},

			/**
			 * 是否已合并
			 */
			isCollapsed:function(){
				return EG.Style.isHide(this.dChildNodes);
			},

			/**
			 * 切换合并状态
			 */
			changeCollapsed:function(){
				if(this.isCollapsed()) this.expand();
				else this.collapse();
			},

			/**
			 * 展开
			 */
			expand:function(force){

				EG.show(this.dChildNodes);

				if(force){
					var pNodes=[];
					var pNode=this;
					while(pNode.parentNode){
						pNodes.push(pNode);
						pNode=pNode.parentNode;
					}
					for(var i=pNodes.length-1;i>=0;i--){
						pNodes[i].expand();
					}

				}

				this.refreshCollapseElement();
			},

			/**
			 * 合并
			 */
			collapse:function(){
				EG.hide(this.dChildNodes);
				this.refreshCollapseElement();
			},

			/**
			 * 前一个节点
			 */
			preNode:function(){
				var cns=this.parentNode.childNodes;
				for(var i=0; i<cns.length; i++) if(cns[i]==this) return (i==0)?null:cns[i-1];
				return null;
			},

			/**
			 * 后一个节点
			 */
			nextNode:function(){
				var cns=this.parentNode.childNodes;
				for(var i=0; i<cns.length; i++) if(cns[i]==this) return (i>=cns.length-1)?null:cns[i+1];
				return null;
			},

			/**
			 * 添加节点
			 * @param {EG.ui.TreeNode} treeNode 节点
			 * @param {Number?} idx 插入位置
			 */
			add:function(treeNode,idx){

				if(EG.Object.isLit(treeNode)){
					treeNode=new ME(treeNode);
				}

				//加到末尾
				if(idx==null){
					idx=this.childNodes.length-1;
					this.childNodes.push(treeNode);

					this.dChildNodes.appendChild(treeNode.getElement());

					//插到指定位置
				}else{
					this.childNodes=EG.Array.insert(this.childNodes,idx+1,treeNode);
					if(this.dChildNodes.childNodes.length>idx+1){
						var oChild=this.dChildNodes.childNodes[idx+1];
						this.dChildNodes.insertBefore(treeNode.getElement(),oChild);
					}else{
						this.dChildNodes.appendChild(treeNode.getElement());
					}
				}

				//注册到树
				this.tree.registNode(treeNode);

				//设定父节点
				treeNode.parentNode=this;

				//刷新当前节点
				this.refreshCollapseElement();

				//刷新子节点
				treeNode.refreshCollapseElement();

				//如果子节点大于1，刷新倒数第二个子节点的收缩显示
				if(this.childNodes.length>1){
					this.childNodes[this.childNodes.length-2].refreshCollapseElement();
				}

				//如果未开启多选，隐藏Box
				if(!this.tree.multiple) EG.hide(treeNode.box.getElement());
			},

			/**
			 * 删除
			 * @param {Boolean?} refresh 是否刷新
			 */
			remove:function(refresh){
				if(refresh==null) refresh=true;

				//移除子节点
				this.removeChilds();

				//前节点
				var preN=this.preNode();

				//从树上移除
				this.tree.ungistNode(this);

				//从父节点移除
				EG.Array.remove(this.parentNode.childNodes,this);

				//从DOM上移除
				this.parentNode.dChildNodes.removeChild(this.getElement());

				//刷新节点
				if(refresh){
					this.parentNode.refreshCollapseElement();//刷新父节点
					if(preN) preN.refreshCollapseElement(); //刷新前节点
				}
			},

			/**
			 * 删除所有子节点
			 * @param {Boolean?} refresh 是否刷新
			 */
			removeChilds:function(refresh){
				if(refresh==null) refresh=true;

				for(var i=this.childNodes.length-1; i>=0; i--){
					this.childNodes[i].remove(false);
				}

				if(refresh) this.refreshCollapseElement();
			},

			/**
			 * 刷新展开区
			 */
			refreshCollapseElement:function(){														//刷新节点
				//清除子节点
				EG.DOM.removeChilds(this.dExpandBtn);

				//设置线和文件夹的样式
				var p1	//线
					, p2;//文件夹
				if(this.isLeaf()){									//如果是叶节点
					p1=this.cls+"-node-l-"+(this.isLast()?"l":"t");
					p2=this.cls+"-node-file";//"file.png";
				}else{												//如果有子节点
					if(this.isCollapsed()){								//如果已经闭合
						p1=this.cls+"-node-l-"+(this.isLast()?"lPlus":"tPlus");
						p2=this.cls+"-node-folder";//"foldericon.gif";
					}else{												//如果已经打开
						p1=this.cls+"-node-l-"+(this.isLast()?"lMinus":"tMinus");
						p2=this.cls+"-node-openfolder";//"openfoldericon.gif";

						//刷新dChildNodes样式
						if(!this.isLast()){
							EG.setCls(this.dChildNodes,["node-dChildNodes","node-bgLine"],this.cls);
						}else{
							EG.setCls(this.dChildNodes,["node-dChildNodes"],this.cls);
						}
					}
				}
				if(!this.isRoot()){
					EG.CE({pn:this.dExpandBtn,tn:"div",cls:p1});
				}
				EG.CE({pn:this.dExpandBtn,tn:"div",cls:p2});

				//刷新dInsert样式
				if(!this.isLast()){
					EG.setCls(this.dInsert,["node-dInsert","node-bgLine"],this.cls);
				}else{
					EG.setCls(this.dInsert,["node-dInsert"],this.cls);
				}

				//刷新最margin
				if(this.parentNode&&this.parentNode.isRoot()){
					EG.css(this.element,"margin-left:0px");
				}else{
					EG.css(this.element,"margin-left:;");
				}
			},
			/**
			 * 是否为最后一个
			 * @returns {Boolean}
			 */
			isLast:function(){																	//是否为末节点
				if(this.parentNode==null) return true;
				return this.parentNode.childNodes[this.parentNode.childNodes.length-1]==this;
			},
			/**
			 * 是否为第一个节点
			 * @returns {boolean}
			 */
			isFirst:function(){
				return this.parentNode.childNodes[0]==this;
			},
			/**
			 * 是否为就页节点
			 */
			isLeaf:function(){
				return this.childNodes.length==0;
			},
			isRoot:function(){
				return this.root;
			},
			/**
			 * 当前Node的索引
			 * @return {Number}
			 */
			getIdx:function(){
				var cns=this.parentNode.childNodes;
				for(var i=0, il=cns.length; i<il; i++){
					if(cns[i]==this) return i;
				}
			},

			/**
			 * 移动节点
			 * @param action
			 * @param node
			 */
			moveto:function(action,node){//TODO 移动BUG

				if(this==node) return;

				if(this.ondrag){
					if(this.ondrag.apply(this["ondragSrc"]||this,[this,action,node])===false) return;
				}

				var oPNode		=this.parentNode;
				var oPreNode	=this.preNode(this);
				var oNextNode	=this.nextNode(this);
				var pNode		=null;
				var idx=-1;
				var oIdx=this.getIdx();
				if(action=="after"){
					idx		=node.getIdx();
					pNode	=node.parentNode;
				}else if(action=="in"){
					pNode	=node;
				}

				//pNode若为子节点，弹出错误
				var find=false;
				var ppNode=pNode;
				while((ppNode=ppNode.parentNode)&&ppNode!=null&&ppNode!=this.tree.rootNode){
					if(ppNode==this){
						find=true;
						break;
					}
				}
				if(find) throw new Error("不能移动到子节点");

				//移除父关联
				EG.Array.remove(oPNode.childNodes,this);
				//pNode.r

				if(idx<0){
					pNode.add(this);
				}else{
					if(oPNode==pNode){//同级目录下移动
						if(oIdx<idx){
							idx--;
						}
					}
					pNode.add(this,idx);
				}

				//同步原前节点样式
				oPNode.refreshCollapseElement();
				if(oPreNode){
					oPreNode.refreshCollapseElement();
				}

				//同步原后节点样式
				if(oNextNode) oNextNode.refreshCollapseElement();

				if(this.parentNode.isRoot()){
					EG.css(this.element,"margin-left:0px");
				}

				this.refreshCollapseElement();
			}
		};
	});
})();

(function(){
	/**
	 * EG.ui.Table
	 */
	EG.define("EG.ui.Table",[
		"EG.ui.Item",
		"EG.ui.Dialog",
		"EG.ui.Form",
		"EG.ui.Button"
	],function(Item,Dialog,Form,Button,ME){
		return {
			extend:Item,
			config:{
				cls				:"eg_table",
				title			:null,				//标题
				rowCount		:3,					//行数
				colCount		:3,					//列数
				afterSelect		:null
				//,tdHeight		:20
			},
			constructor:function(cfg){
				this.callSuper([cfg]);
			},
			/**
			 * 重新绑定Table,用来动态构建Table组件
			 * @param nTable
			 */
			rebindTable:function(nTable){
				var me=this;

				this.element.replaceChild(nTable,this.dTable);
				this.dTable=nTable;
				this.dBody=nTable.getElementsByTagName("tbody")[0];

				EG.Event.bindEvent(this.dTable,ondragstart,function(){me.refreshSelect();});
				EG.Event.bindEvent(this.dTable,onmouseup,function(){me.selecting=false;if(me.afterSelect) me.afterSelect.apply(me);});

				var trs=this.dBody.childNodes;
				for(var i=0;i<trs.length;i++){
					var tds=trs[i].childNodes;
					for(var j=0;j<tds.length;j++){
						var td=tds[j];
						//从POS中获取经纬度
						var pos=td.getAttribute("pos").split(",");

						EG.CE({ele:td,r:pos[0],c:pos[1],table:this,
							onmousedown		:this.tdEvent.onmousedown,
							onmouseover		:this.tdEvent.onmouseover,
							oncontextmenu	:this.tdEvent.oncontextmenu
						});
					}
				}

			},
			/**
			 * 创建
			 */
			build:function(){
				var me=this;
				//Element
				this.element=EG.CE({tn:"div",cls:this.cls,cn:[
					this.dTable=EG.CE({tn:"table",cls:this.cls+"-table",border:1,cn:[
						this.dBody=EG.CE({tn:"tbody"})
					]}),
					this.dSelectTop		=EG.CE({tn:"div",cls:this.cls+"-selectTop"}),
					this.dSelectRight	=EG.CE({tn:"div",cls:this.cls+"-selectRight"}),
					this.dSelectBottom	=EG.CE({tn:"div",cls:this.cls+"-selectBottom"}),
					this.dSelectLeft	=EG.CE({tn:"div",cls:this.cls+"-selectLeft"})

				],
					ondragstart:function(){me.refreshSelect();},
					onmouseup:function(){me.selecting=false;if(me.afterSelect) me.afterSelect.apply(me);}
				});
				EG.hide(
					this.dSelectTop,
					this.dSelectRight,
					this.dSelectBottom,
					this.dSelectLeft
				);

				//创建RC
				this.buildRowCol(this.rowCount,this.colCount);

				//菜单
				this.dMenu=EG.CE({pn:this.getElement(),cls:this.cls+"-dMenu",tn:"div",cn:[
					{tn:"a",innerHTML:"属性"				,onclick:function(){me.openPFCol();			EG.hide(me.dMenu);}},
					{tn:"a",innerHTML:"合并"				,onclick:function(){me.merge();				EG.hide(me.dMenu);}},
					{tn:"a",innerHTML:"取消合并"			,onclick:function(){me.broke();				EG.hide(me.dMenu);}},
					{tn:"a",innerHTML:"左侧插入一列"		,onclick:function(){EG.hide(me.dMenu);}},
					{tn:"a",innerHTML:"右侧插入一列"		,onclick:function(){EG.hide(me.dMenu);}},
					{tn:"a",innerHTML:"上方插入一行"		,onclick:function(){EG.hide(me.dMenu);}},
					{tn:"a",innerHTML:"下方插入一行"		,onclick:function(){EG.hide(me.dMenu);}},
					{tn:"a",innerHTML:"删除所在列"		,onclick:function(){EG.hide(me.dMenu);}},
					{tn:"a",innerHTML:"删除所在行"		,onclick:function(){EG.hide(me.dMenu);}},
					{tn:"a",innerHTML:"清除"				,onclick:function(){EG.hide(me.dMenu);}},
					{tn:"a",innerHTML:"表格属性"			,onclick:function(){me.openPFTable();		EG.hide(me.dMenu);}}
				]});
				EG.hide(this.dMenu);
			},

			/**
			 * TD事件
			 */
			tdEvent:{
				onmousedown:function(e){
					var me=this.table;
					e=EG.Event.getEvent(e);
					if(e.button!=0) return;

					EG.hide(me.dMenu);

					me.selecting=true;
					me.startTd	=this;
					me.endTd	=this;
					EG.show(
						me.dSelectTop,
						me.dSelectRight,
						me.dSelectBottom,
						me.dSelectLeft
					);

					me.refreshSelect();
				},
				onmouseover:function(e){
					var me=this.table;
					if(!me.selecting) return;
					me.endTd=this;
					me.refreshSelect();
				},
				oncontextmenu:function(evt){
					var me=this.table;
					if(!me.startTd) return;
					me.showMenu(evt,me.node);
				}
			},

			/**
			 * 显示contextMenu
			 * @param e
			 */
			showMenu:function(e){
				var p=EG.Tools.getMousePos(e,this.getElement());
				EG.css(this.dMenu,{
					top	:p.y+"px",
					left:p.x+"px"
				});

				EG.show(this.dMenu);
				EG.Event.stopPropagation(e);
			},

			/**
			 * 刷新Select边框
			 * @param force
			 */
			refreshSelect:function(force){
				if(!force&&!this.selecting) return;
				else if(!this.endTd){
					return;
				}


				var ps={
					x:this.startTd.offsetLeft	-this.dTable.offsetLeft,
					y:this.startTd.offsetTop	-this.dTable.offsetTop
				};

				var pe={
					x:this.endTd.offsetLeft	-this.dTable.offsetLeft,
					y:this.endTd.offsetTop	-this.dTable.offsetTop
				};

				var x, y, w,h;

				var l=Math.min(ps.x,pe.x);
				var t=Math.min(ps.y,pe.y);
				var w=Math.max((ps.x+this.startTd.clientWidth)	,(pe.x+this.endTd.clientWidth))	-l;
				var h=Math.max((ps.y+this.startTd.clientHeight)	,(pe.y+this.endTd.clientHeight))-t;

				EG.css(this.dSelectTop,{
					left	:l+"px",
					top		:t+"px",
					width	:w+"px"
				});

				EG.css(this.dSelectRight,{
					left	:(l+w)+"px",
					top		:t+"px",
					height	:h+"px"
				});

				EG.css(this.dSelectBottom,{
					left	:l+"px",
					top		:(t+h)+"px",
					width	:w+"px"
				});

				EG.css(this.dSelectLeft,{
					left	:l+"px",
					top		:t+"px",
					height	:h+"px"
				});

			},

			/**
			 * 清除Select
			 */
			clearSelect:function(){
				EG.hide(
					this.dSelectTop,
					this.dSelectRight,
					this.dSelectBottom,
					this.dSelectLeft
				);
			},

			/**
			 * 合并单元格
			 */
			merge:function(){
				if(this.startTd==this.endTd) return;

				//左上坐标
				var r1=Math.min(this.startTd.r,this.endTd.r);
				var c1=Math.min(this.startTd.c,this.endTd.c);

				//右下坐标
				var r2=Math.max(this.startTd.r,this.endTd.r);
				var c2=Math.max(this.startTd.c,this.endTd.c);


				//获取选取内要合并的单元格，找到起始单元格

				var aimTd=null;
				var w4d=[];
				for(var i=r1;i<=r2;i++){
					var tds=this.dBody.childNodes[i].childNodes;
					for(var j=0;j<tds.length;j++){
						var td=tds[j];

						if(td.c==c1&&td.r==r1){
							aimTd=td;
						}else{
							if(td.c>=c1&&td.c<=c2){
								w4d.push(td);

								//先打散再合并
								if((td.colSpan&&td.colSpan>1)||(td.rowSpan&&td.rowSpan>1)){
									EG.Locker.message("待合并中有已合并的单元格");
									return;
									//throw new Error();
								}
							}
						}
					}
				}

				//删除其它单元格
				for(var i=0;i<w4d.length;i++){
					w4d[i].parentNode.removeChild(w4d[i]);
				}

				//设置起始单元格
				aimTd.colSpan=c2-c1+1;
				aimTd.rowSpan=r2-r1+1;

				this.startTd	=aimTd;
				this.endTd		=aimTd;

				this.refreshSelect(true);

				//重新设置所有空行，空列
				this.collect();
			},

			/**
			 * 回收所有空行，并充值索引
			 */
			collect:function(){
				var trs=this.dBody.childNodes;
				var hasEmptyTr=false;
				//检测是否有空的tr,如果有，删除tr,重新编号所有
				for(var i=trs.length-1;i>=0;i--){
					if(trs[i].childNodes.length==0){
						this.dBody.removeChild(trs[i]);
						hasEmptyTr=true;
					}
				}

				//检查出当前行中最大的行数



				trs=this.dBody.childNodes;
				for(var i=0;i<trs.length;i++){
					var tds=trs[i].childNodes;
					trs[i]=i;
					for(var j=0;j<tds.length;j++){
						tds[j].r=i;
						if(tds.length==1){
							tds[j].rowSpan="";
						}
						//if(tds.length==1) tds[j].rowSpan="";
					}
				}

			},

			/**
			 * 拆分单元格
			 */
			broke:function(){
				//找到所有的rowSpan和colSpan的TD 还原

				//左上坐标
				var r1=Math.min(this.startTd.r,this.endTd.r);
				var c1=Math.min(this.startTd.c,this.endTd.c);

				//右下坐标
				var r2=Math.max(this.startTd.r,this.endTd.r);
				var c2=Math.max(this.startTd.c,this.endTd.c);


				//获取选取内要合并的单元格，找到起始单元格

				var aimTd=null;
				var spans=[];
				for(var i=r1;i<=r2;i++){
					var tds=this.dBody.childNodes[i].childNodes;
					for(var j=0;j<tds.length;j++){
						var td=tds[j];
						if(td.c>=c1&&td.c<=c2){
							if((td.colSpan&&td.colSpan>1)||(td.rowSpan&&td.rowSpan>1)){
								spans.push(td);
							}
						}
					}
				}

				//轮训所有带分拆的td
				for(var i=0;i<spans.length;i++){
					var spanTd=spans[i];
					var rs=spanTd.rowSpan?spanTd.rowSpan:1;
					var cs=spanTd.colSpan?spanTd.colSpan:1;
					var startR=spanTd.r;
					var startC=spanTd.c;
					var startTr=spanTd.parentNode;


					//轮训在每行加入TD
					for(var j=0;j<=rs-1;j++){

						//找到所在行
						var tr=startTr;
						for(var k=0;k<j;k++){
							tr=tr.nextSibling;
						}

						//寻找要插入TD的位置
						var tds=tr.childNodes;
						var preTd=null;
						for(var k=0;k<tds.length;k++){
							if(tds[i].c>=startC){
								if(i>=1){
									preTd=tds[i-1]
								}else{
									preTd=null;
								}
							}
						}

						//插入TD
						for(var k=startC;k<=(startC+cs-1);k++){
							if(k==startC&&j==0){

							}else{
								var td=EG.CE({pn:tr,tn:"td",r:tr.r,c:k,table:this,
									onmousedown		:this.tdEvent.onmousedown,
									onmouseover		:this.tdEvent.onmouseover,
									oncontextmenu	:this.tdEvent.oncontextmenu
								});
								if(preTd){
									EG.DOM.insertAfter(td,preTd);
								}else{
									tr.appendChild(td);
								}
								preTd=td;
							}
						}
					}
					spanTd.rowSpan=1;
					spanTd.colSpan=1;
				}
			},

			/**
			 * 获取TD
			 * @param rowIdx
			 * @param colIdx
			 * @returns {*}
			 */
			getTd:function(rowIdx,colIdx){
				return this.dBody.childNodes[rowIdx].childNodes[colIdx];
			},

			/**
			 * 创建ROW和COL
			 * @param rowCount
			 * @param colCount
			 */
			buildRowCol:function(rowCount,colCount){

				this.rowCount=rowCount;
				this.colCount=colCount;

				//清除
				EG.DOM.removeChilds(this.dBody);

				for(var i=0;i<this.rowCount;i++){
					var tr=EG.CE({pn:this.dBody,tn:"tr",r:i});
					for(var j=0;j<this.colCount;j++){
						var td=EG.CE({pn:tr,tn:"td",r:i,c:j,table:this,//innerHTML:"1",
							onmousedown		:this.tdEvent.onmousedown,
							onmouseover		:this.tdEvent.onmouseover,
							oncontextmenu	:this.tdEvent.oncontextmenu
						});

						//标识TD经纬度
						td.setAttribute("pos",i+","+j);
					}
				}
			},

			inserCol:function(col,action){
				//找到点选项的c,

				//遍历所有TD,凡是c小于等于点选c的c都放入待加1的队列中在添加完毕后每个+1

				//遍历每行
				//如果找到c-1的TD
				//如果其colSpan为空或,在其后添加一个td，设置其C为点选C
				//如果其colSpan>1,将其colSpan+1
				//如果没有找到c-1的TD(第一列或前一列已经跨列)，直接加1个TD


			},

			inserRow:function(row,action){

			},


			deleteRow:function(row){

			},

			deleteCol:function(col){

			},
			/**
			 * 打开单元格属性
			 * @param col
			 */
			openPFCol:function(col){
				if(!this.pfCol) this.buildPFCol();
				if(!this.startTd) return;
				this.colForm.setData(this.getColStyle(this.startTd));
				this.pfCol.open();
			},
			/**
			 * 打开表格属性
			 * @param col
			 */
			openPFTable:function(col){
				if(!this.pfTable) this.buildPFTable();
				this.tableForm.setData(this.getTableStyle(this.dTable));
				this.pfTable.open();
			},
			/**
			 * 获取单元格样式
			 * @param td
			 * @returns {Object}
			 */
			getColStyle:function(td){
				var cfg={};
				var ks=ME.sKey;
				var s=td.style;
				if(s){
					for(var i=0;i<ks.length;i++){
						if(s[ks[i]]!=null) cfg["td_"+ks[i]]=s[ks[i]];
					}
				}
				cfg["td_inner"]=td.itemCfg?"":td.innerHTML;
				return cfg;
			},

			/**
			 * 获取table样式
			 * @param table
			 * @returns {Object}
			 */
			getTableStyle:function(table){
				var cfg={};
				var s=table.style;
				if(s){
					if(s.borderColor!=null) 		cfg["table_border-color"]	=s.borderColor;
					if(s.borderWidth!=null) 		cfg["table_border-width"]	=s.borderWidth;
				}

				if(table.cellSpacing!=null) 	cfg["table_cellSpacing"]	=table.cellSpacing;
				if(table.cellPadding!=null) 	cfg["table_cellPadding"]	=table.cellPadding;

				return cfg;
			},

			/**
			 * 设置单元格样式
			 * @param td
			 * @param fd
			 * @param force
			 */
			setColStyle:function(td,fd,force){
				var s={};
				var ks=ME.sKey;
				for(var i=0;i<ks.length;i++){
					s[ks[i]]=fd["td_"+ks[i]];
				}

				EG.css(td,s);
				td.innerHTML=EG.unnull(fd["td_inner"],"");
				this.refreshSelect(true);
			},

			/**
			 * 设置表格样式
			 * @param table
			 * @param fd
			 * @param force
			 */
			setTableStyle:function(table,fd,force){
				if(!EG.isBlank(fd["table_border-color"])) 	EG.css(table,"border-color:"+fd["table_border-color"]);
				if(!EG.isBlank(fd["table_border-width"])) 	EG.css(table,"border-width:"+fd["table_border-width"]);
				if(!EG.isBlank(fd["table_cellSpacing"])) 	table.cellSpacing=fd["table_cellSpacing"];
				if(!EG.isBlank(fd["table_cellPadding"])) 	table.cellPadding=fd["table_cellPadding"];

				this.refreshSelect(true);
			},

			btn_events:{
				col:{
					save:function(){
						var me=this.table;
						if(!me.startTd) return;
						var fd=me.colForm.getData();
						me.setColStyle(me.startTd,fd);
					},
					cancel:function(){
						var me=this.table;
						me.pfCol.close();
					}
				},
				table:{
					save:function(){
						var me=this.table;
						var fd=me.tableForm.getData();
						me.setTableStyle(me.dTable,fd);
					},
					cancel:function(){
						var me=this.table;
						me.pfTable.close();
					}
				}
			},

			/**
			 * 创建单元格属性
			 */
			buildPFCol:function(){
				this.pfCol=new Dialog({
					title	:"单元格",
					layout	:"border",
					width	:300,
					height	:"auto",
					lock	:true,
					items:[
						this.colForm=new Form({region:"center",layout:"table",height:"auto",width:"100%",items:[
							{xtype:"formItem",pos:[1,0],title:"内容"		,name:"td_inner"		,type:"textarea",height:40},
							{xtype:"formItem",pos:[2,0],title:"对齐"		,name:"td_text-align"	,type:"boxGroup"	,textvalues:[["左","left"],["中","center"],["右","right"],["无",""]]},
							{xtype:"formItem",pos:[3,0],title:"宽度"		,name:"td_width"		,type:"text"},
							{xtype:"formItem",pos:[3,1],title:"高度"		,name:"td_height"		,type:"text"},
							{xtype:"formItem",pos:[4,0],title:"字号"		,name:"td_font-size"	,type:"text"},
							{xtype:"formItem",pos:[4,1],title:"颜色"		,name:"td_color"		,type:"text"},
							{xtype:"formItem",pos:[5,0],title:"加重"		,name:"td_font-weight"	,type:"boxGroup",textvalues:[["是","bolder"],["否",""]]},
							{xtype:"formItem",pos:[5,1],title:"倾斜"		,name:"td_font-style"	,type:"boxGroup",textvalues:[["是","italic"],["否",""]]}
						]})
					],
					btns:[
						new Button({text:"保存",click:this.btn_events.col.save		,cls:"eg_button_small",table:this}),
						new Button({text:"取消",click:this.btn_events.col.cancel	,cls:"eg_button_small",table:this})
					],
					renderTo:this.getElement()
				})
			},
			/**
			 * 创建表格属性
			 */
			buildPFTable:function(){
				this.pfTable=new Dialog({
					title	:"表格",
					layout	:"border",
					width	:300,
					height	:"auto",
					lock	:true,
					items:[
						this.tableForm=new Form({region:"center",layout:"table",height:"auto",width:"100%",items:[
							{pos:[1,0],title:"边色"		,name:"table_border-color"	,type:"text"},
							{pos:[1,1],title:"边宽"		,name:"table_border-width"	,type:"text"},
							{pos:[2,0],title:"缝隙"		,name:"table_cellSpacing"	,type:"text"},
							{pos:[2,1],title:"间距"		,name:"table_cellPadding"	,type:"text"}
						]})
					],
					btns:[
						new Button({text:"保存",click:this.btn_events.table.save		,cls:"eg_button_small",table:this}),
						new Button({text:"取消",click:this.btn_events.table.cancel	,cls:"eg_button_small",table:this})
					],
					renderTo:this.getElement()
				})
			},

			render:function(){
				//设置大小
				Item.fit({
					element:this.element
				});

				if(this.pfCol) this.pfCol.render();
			}
			,
			statics:{
				//单元格样式Key
				sKey:["text-align","width","height","font-size","color","font-weight","font-style"]

			}
		};
	});

})();

/**
 *
 * 	Box盒子类,类似于HTMLInput组件的checkbox,可以通过
 *
 * 	@example
 * 		var box=new EG.ui.Box({
 * 			textvalues:[
 * 				["A","a"],
 * 				["B","b"]
 * 			],
 * 			width:100,
 * 			height:20,
 *			renderTo:EG.getBody()
 * 		});
 *
 *
 * @class EG.ui.Box
 * <AUTHOR>
 * @extends EG.ui.Item
 *
 */
(function(){
	EG.define("EG.ui.Box",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			extend:Item,
			config:{
				/** @cfg {String?} 文本 */
				text		:"",
				/** @cfg {Object?} 值 */
				value		:"",
				/** @cfg {String} 样式类 */
				cls			:"eg_box",
				/** @cfg {Function?} select选前执行事件 */
				onselect	:null,
				/** @cfg {Function?} click时执行的动作(可以完全控制点击的行为) */
				onclick		:null,
				/** @cfg {Function?} select选中后的动作 */
				afterselect	:null,
				/** @cfg {Boolean?} 是否已选 */
				selected	:false,
				/** @cfg {Boolean?} 显示文本 */
				showText	:true
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.callSuper([cfg]);

				//是否显示文字
				if(!this.showText){
					EG.hide(this.dText);
					EG.css(this.dBox,"display:block;")
				}
			},

			/**
			 * 创建
			 */
			build:function(){
				this.element=EG.CE({tn:"div",cls:this.cls,item:this,
					onclick:ME._events.element.onclick,
					cn:[
						this.dBox	=EG.CE({tn:"a",cls:this.cls+"-b"}),
						this.dText	=EG.CE({tn:"div",cls:this.cls+"-text",innerHTML:this.text})
					]
				});
			},

			/**
			 * 选择Box
			 *
			 *
			 * @param {Boolean} selected 准备选择的状态
			 * @param {Boolean?} fireEvt 触发事件
			 */
			select:function(selected,fireEvt){

				if(fireEvt==null) fireEvt=true;

				//执行选中前的动作
				if(fireEvt&&this.onselect){
					if(this.onselect.apply(this,[selected])===false) return;
				}
				//标识
				this.selected=selected;
				//样式
				EG.setCls(this.dBox,["b",this.selected?"select":"unselect"],this.cls);

				//选中后执行
				if(fireEvt&&this.afterselect){
					this.afterselect.apply(this,[selected]);
				}
			},

			/**
			 * 取消选择
			 */
			deSelect:function(){
				this.selected=null;
				EG.setCls(this.dBox,["b","unselect"],this.cls);
			},

			/**
			 * 设值
			 * @param {Object} value 值
			 */
			setValue:function(value){
				this.value=value;
			},

			/**
			 * 返回值
			 * @returns {Object}
			 */
			getValue:function(){
				return this.value;
			},

			/**
			 * 渲染
			 */
			render:function(){
				Item.fit(this);

				var h=EG.getSize(this.element).innerHeight;

				var m=parseInt((h-EG.getSize(this.dBox).outerHeight)/2);
				EG.css(this.dBox,"margin-top:"+m+"px;margin-bottom:"+m+"px");

				EG.css(this.dText,"line-height:"+h+"px;height:"+h+"px");
			},

			statics:{
				_events:{
					element:{
						onclick:function(){
							var me=this.item;
							if(me.onclick){					//onclick事件优先，可以完全控制选中不选中
								me.onclick.apply(me);
							}else{
								me.select(!me.selected);
							}
						}
					}
				}
			}
		};
	});
})();

/**
 * @class EG.ui.BoxGroup
 * <AUTHOR>
 * @extends EG.ui.Item
 * Box盒子组类
 */
(function(){
	EG.define("EG.ui.BoxGroup",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			alias:"box",
			extend:Item,
			config:{
				/** @cfg {Boolean?} multiple 是否可多选 */
				multiple	:false,
				/** @cfg {Function?} onselect select选前执行事件,点击任意一个box时候都会触发所有的box的onselect事件 */
				onselect	:null,
				/** @cfg {Function?} onclick click时执行的动作(可以完全控制点击的行为) */
				onclick		:null,
				/** @cfg {Function?} onchange 数值变化事件 */
				onchange	:null,
				/** @cfg {Function?} afterselect select以后执行的动作,点击任意一个box时候都会触发所有的box的onselect事件 */
				afterselect	:null,
				/** @cfg {Array?} textvalues 文本-值 数组 */
				textvalues	:[],
				/** @cfg {String?} cls 样式类 */
				cls			:"eg_boxgroup",
				/** @cfg {String?} defValue 默认值 */
				defValue	:null,
				/** @cfg {Number?} boxHeight 盒子高 */
				boxHeight	:null,
				/** @cfg {String?} boxStyle 盒子 */
				boxStyle	:null
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.callSuper([cfg]);
				//box组
				this.boxes=[];
				//onchange事件
				this.onchangeEvents=[];
				if(this.onchange!=null) this.bindOnchange(this.onchange);
				//创建Box
				this.buildBoxes();

				//绑定校验
				EG.ui.FormItem.bindValidate.apply(this,[]);

				//默认值
				if(this.defValue) this.setValue(this.defValue);
			},

			/**
			 * 创建元素
			 */
			build:function(){
				this.element=EG.CE({tn:"div",cls:this.cls});
			},

			/**
			 * 创建BOX
			 */
			buildBoxes:function(){
				var me=this;
				//创建子box
				for(var i=0,il=this.textvalues.length;i<il;i++){
					var tv=this.textvalues[i];
					this.add({
						text		:tv[0],
						value		:tv[1],
						style		:this.boxStyle,
						onclick		:ME._events.box.onclick,
						group		:this,
						onselect	:me.onselect,
						afterselect	:me.afterselect
					});
				}
			},

			/**
			 * 设值
			 * @param {Object} value 值
			 * @param {Boolean?} chain 联动
			 */
			setValue:function(value,chain){
				if(chain==null) chain=true;

				//旧数据
				var oV=this.getValue();
				if(this.multiple){
					EG.Array.each(this.boxes,function(){
						this.select(EG.Array.has(value,this.value));
					});
				}else{
					EG.Array.each(this.boxes,function(){
						this.select(this.value===value);
					});
				}

				//触发onchange
				if(!EG.Object.equals(oV,value)&&chain){
					//执行OnChange
					this.doOnChange(value,oV);
				}
			},

			/**
			 * 获值
			 * @returns {Object}
			 */
			getValue:function(){
				var values=[];
				for(var i=0,l=this.boxes.length;i<l;i++){
					var box=this.boxes[i];
					if(this.multiple){
						if(box.selected) values.push(box.value);
					}else{
						if(box.selected) return box.value;
					}
				}
				if(this.multiple) return values;
				return null;
			},

			/**
			 * 获取选中的文本
			 * @returns {*}
			 */
			getText:function(){
				var texts=[];
				for(var i=0,l=this.boxes.length;i<l;i++){
					var box=this.boxes[i];
					if(this.multiple){
						if(box.selected) texts.push(box.text);
					}else{
						if(box.selected) return box.text;
					}
				}
				if(this.multiple) return texts;
				return null;
			},

			/**
			 * 获取已选Box
			 */
			getSelectedBox:function(){
				var bs=[]
				for(var i=0,l=this.boxes.length;i<l;i++){
					var box=this.boxes[i];
					if(this.multiple){
						if(box.selected) bs.push(box);
					}else{
						if(box.selected) return box;
					}
				}
				if(this.multiple) return bs;
				return null;
			},

			/**
			 * 添加Box
			 * @param {Object|EG.ui.Box} box BOX
			 */
			add:function(box){
				if(EG.isLit(box)) box=new EG.ui.Box(box);
				box.pItem=this;
				this.boxes.push(box);
				this.element.appendChild(box.getElement());
			},

			/**
			 * 获取Box组
			 * @returns {Array<EG.ui.Box>}
			 */
			getBoxes:function(){
				return this.boxes;
			},

			/**
			 * 绑定变化时事件
			 * @param {Function} onchange 当改变时事件
			 */
			bindOnchange:function(onchange){
				this.onchangeEvents.push(onchange);
			},

			/**
			 * 执行OnChange
			 * @param {Object?} value
			 * @param {Object?} oldValue
			 */
			doOnChange:function(value,oldValue){
				for(var i=0;i<this.onchangeEvents.length;i++){
					this.onchangeEvents[i].apply(this,[value,oldValue]);
				}
			},

			/**
			 * 渲染
			 */
			render:function(){
				Item.fit(this);

				var h=EG.getSize(this.element).innerHeight;
				for(var i=0;i<this.boxes.length;i++){
					this.boxes[i].height=this.boxHeight!=null?this.boxHeight:h;
					this.boxes[i].render();
				}
			},

			/**
			 * 设置文本值
			 * @param {Array} tvs 文本值
			 */
			setTextvalues:function(tvs){
				EG.Array.clear(this.boxes);
				this.textvalues=tvs;
				EG.DOM.removeChilds(this.element);
				this.buildBoxes();
			},

			/**
			 * 获取标题
			 * @param {String} value 数值
			 * @param {Object} data 数据集
			 * @param {Object} cfg 扩展
			 * @return {String}
			 */
			getTitle:function(value,data,cfg){

				var textvalues=cfg.textvalues||[];
				if(!EG.isArray(value)){
					value=[value];
				}
				var titles=[];
				for(var j=0,jl=value.length;j<jl;j++){
					var v=value[j];
					for(var i=0,il=textvalues.length;i<il;i++){
						if(textvalues[i][1]==v){
							titles.push(textvalues[i][0]);
							break;
						}
					}
				}
				return titles.join(",");
			},

			statics:{
				_events:{
					box:{
						onclick:function(){
							var me=this.group;
							var oV=this.group.getValue();
							var needChange=false;
							//多选
							if(me.multiple){
								this.select(!this.selected);
								needChange=true;
								//单选
							}else{
								if(me.onclick){
									me.onclick();
								}else{
									if(!this.selected)  needChange=true;
									//清除同组其它box
									for(var i=0,l=me.boxes.length;i<l;i++){
										if(this!=me.boxes[i]) me.boxes[i].select(false);
									}
									this.select(true);
								}
							}

							var val=this.group.getValue();

							//触发onchange
							if(needChange){
								for(var i=0;i<me.onchangeEvents.length;i++){
									me.onchangeEvents[i].apply(me,[val,oV]);
								}
							}
						}
					}

				}
			}
		};
	});
})();

(function(){
	/**
	 * Date时间类,需要MyDatapiker支持
	 * @namespace EG.ui.Date
	 * <AUTHOR>
	 * @extends EG.ui.Item
	 */
	EG.define("EG.ui.Date",[
		"EG.ui.Item"
	],function(Item,ME){
		return{
				alias:"date",
				extend:Item,
				config:{
				/** @cfg {?String} fmt 日期格式 */
				fmt			:"YMDHMS",
				/** @cfg {?Number} maxLength 最大长度 */
				maxLength	:10,
				/** @cfg {?String} 日期格式 */
				dateFmt		:null,
				cls         :"eg_date",
				onkeydown	:null,
				onkeyup		:null,
				placeholder	:null
			},
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.callSuper([cfg]);

				if(this.dateFmt==null){
					if (this.fmt == "Y"){
						this.dateFmt="yyyy";
						this.maxLength=4;
					}else if (this.fmt == "YMD"){
						this.dateFmt="yyyy-MM-dd";
						this.maxLength=10;
					}else if (this.fmt == "YMDHMS"){
						this.dateFmt="yyyy-MM-dd HH:mm:ss";
						this.maxLength=20;
					}else throw new Error("暂不支持时间格式" + this.fmt);
				}
			},

			build:function(){
				this.element = EG.CE({tn:"div",cls:this.cls,cn:[
					this.input=EG.CE({tn : "input",cls:this.cls+"-input",type : "text",maxLength : this.maxLength,length: this.maxLength,placeholder:this.placeholder||" ",item:this,onclick:ME._events.input.onclick})
				]});

//			if(this.onkeydown){
//				EG.Event.bindEvent(this.input,"onkeydown",this.onkeydown);
//			}
//
				//绑定校验
				EG.ui.FormItem.bindValidate.apply(this,[]);


				if(this.onkeyup){
					EG.Event.bindEvent(this.input,"onkeyup",this.onkeyup);
				}
			},

			/**
			 * 设值
			 * @param {String} value 数值
			 */
			setValue:function(value){
				EG.setValue(this.input,value);
			},
			/**
			 * 获值
			 * @returns {String}
			 */
			getValue:function(){return EG.getValue(this.input);},
			/**
			 * 渲染
			 */
			render:function(){
				Item.fit(this);

				Item.fit({
					element:this.input,
					dSize:{
						width:"100%",
						height:"100%"
					},
					pSize:EG.getSize(this.element)
				});
			},
			statics:{
				_events:{
					input:{
						onclick:function(){
							var me=this.item;
							new WdatePicker({dateFmt:me.dateFmt,skin : 'ext'});
						}
					}
				}
			}
		}
	});
})();

/**
 * @class EG.ui.Editor
 * <AUTHOR>
 * @extends EG.ui.Item
 * Editor编辑类
 */
(function(){
	EG.define("EG.ui.Editor",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			alias:"editor",
			extend:Item,
			config:{
				/** @cfg {String?} pluginGroupName 分组名 */
				pluginGroupName		:"def",
				/** @cfg {String?} cls CSS样式类 */
				cls					:"eg_editor",
				/** @cfg {String?} uploadPolicy 上传策略 */
				uploadPolicy		:null,
				/** @cfg {String?} imgUploadPolicy 图像上传策略 */
				imgUploadPolicy		:null,

				uploadAction		:null,

				imgUploadAction		:null,

				/** @cfg {Function?} onUploaded 上传后事件 */
				onUploaded			:null,
				/** @cfg {Function?} imgOnUploaded 图像上传后事件 */
				imgOnUploaded		:null,
				/** @cfg {String?} uploadHandleType 上传处理类型 */
				uploadHandleType	:null,
				/** @cfg {Function?} deleteUpload 删除上传的操作 */
				deleteUpload		:null,
				/** @cfg {Object?} parent 外部参考物 */
				parent				:null,

				imgPickers			:null,		//图片选取器

				uploadOnPaste		:false,
				readLineHeight		:22
			},
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.initItem(cfg);
				this.pluginGroup=ME.pluginGroups[this.pluginGroupName];

				if(typeof(this.onUploaded)=="string"){
					this.onUploaded=ME.defOnUploaded;
				}

				this.dToolbarButtons=null;
				this.dIframe		=null;
				this.dSource		=null;
				this.dCache			=null;

				this.element=EG.CE({tn:"div",cls:this.cls,cn:[
					this.dMenuPanels		=EG.CE({tn:"div"}),
					this.dToolbarButtons	=EG.CE({tn:"div",cls:this.cls+"-toolbar"}),
					this.dHtml				=EG.CE({tn:"div",cls:this.cls+"-dHtml",cn:[
						this.frame			=EG.CE({tn:"iframe",frameBorder:"0",designMode:"on",style:"height:100%",item:this})
					]}),
					this.dCache			=EG.CE({tn:"div",style:"display:none"})
				]});

				//设定样式
				EG.css(this.element,this.style);

				//初始化Iframe
				this.initIframe();

				//初始化插件
				this.buildPlugins();
			},
			/**
			 * 渲染
			 */
			render:function(){
				Item.fit(this);

				var size=EG.getSize(this.element);
				Item.fit({
					element	:this.dToolbarButtons,
					dSize	:{width:"100%"},
					pSize	:size,
					type	:"height"
				});


				var tbSize=EG.getSize(this.dToolbarButtons);
				Item.fit({
					element:this.dHtml,
					dSize:{width:"100%",height:size.innerHeight-tbSize.outerHeight},
					pSize:size
				});

				for(var i= 0,il=this.plugins.length;i<il;i++){
					this.plugins[i].render();
				}
			},
			plugins			:[],
			pluginsMap		:{},
			clickHandlers	:[],
			dblclickHandlers:[],
			/**
			 * 创建插件s
			 * @private
			 */
			buildPlugins:function(){
				this.buildInParents=[];

				for(var i=0,il=this.pluginGroup.length;i<il;i++){
					//创建插件
					var name=this.pluginGroup[i];
					var plugin=new ME.pluginsMap[name](this);

					this.plugins.push(plugin);
					this.pluginsMap[name]=plugin;
					//创建按钮
					var toolbarButton=plugin.getToolbarButton();
					if(toolbarButton)	this.dToolbarButtons.appendChild(toolbarButton.getElement());

					//创建弹出面板
					if(plugin.getMenuPanel){
						var menuPanel=plugin.getMenuPanel();
						if(menuPanel){
							if(plugin.buildInParent&&this.parent){
								var pn=(this.parent.getElement)?this.parent.getElement():this.parent;
								pn.appendChild(menuPanel.getElement());

								//记录
								this.buildInParents.push(menuPanel);
							}else{
								this.dMenuPanels.appendChild(menuPanel.getElement());
							}
						}
					}
				}
			},
			/**
			 * 获取组件
			 * @param {String} name 组件名
			 */
			getPlugin:function(name){
				return this.pluginsMap[name];
			},
			/**
			 * 初始化Frame，加入监听事件
			 * @private
			 */
			initIframe:function(){
				//加载Onload事件
				EG.bindEvent(this.frame,"onload",ME._events.iframe.load);

				//BD:IE8默认会有onload事件
				if(!EG.Browser.isIE8())	this.frame.src="about:blank";
			},

			/**
			 * 设值内容
			 * @param {String} content 内容
			 */
			setContent:function(content){
				this.cacheHTML = content;
				if (this.loaded) this.doc.body.innerHTML = content;
				//this.frame.contentWindow.document.body.style.fontSize = EG.ui.Editor.def.fontSize;//TODO 测试
			},
			/**
			 * @link EG.ui.Editor.setContent
			 */
			setValue:function(value){
				var ct=EG.CE({tn:"div",innerHTML:value});

				var tns=["img","a"]
				for(var i=0;i<tns.length;i++){
					var els=ct.getElementsByTagName(tns[i]);
					for(var j=0;j<els.length;j++){
						var el=els[j];
						var uri;
						var attName="";
						if(el.hasAttribute("srcUri")){
							attName="src";
							uri=el.getAttribute("srcUri");
						}else if(el.hasAttribute("hrefUri")){
							attName="href";
							uri=el.getAttribute("hrefUri");
						}else{
							continue;
						}
						el.setAttribute(attName,uri);
					}
				}

				this.setContent(value);
			},

			/**
			 * 获取内容
			 * @returns {String}
			 */
			getContent:function(){
				//alert(this.frame.)
				if (!this.loaded||!this.doc){
					return EG.n2d(this.cacheHTML,"");
				}

				return this.doc.body.innerHTML;
			},

			/**
			 * @link EG.ui.Editor.getContent
			 */
			getValue:function(){return this.getContent();},

			/**
			 * 聚焦
			 */
			focus:function(){
				if(EG.Browser.isChrome()){//21.0.1180.60
					this.doc.body.focus();
				}else if(EG.Browser.isIE()){
					this.frame.contentWindow.focus();
				}else{
					this.frame.focus();
				}
			},
			/**
			 * 隐藏menu
			 */
			hideMenus:function(){
				for(var i=0,il=this.plugins.length;i<il;i++){
					var plugin=this.plugins[i];
					if(!plugin.getMenuPanel) continue;
					var menuPanel=plugin.getMenuPanel();
					if(menuPanel) menuPanel.close();
				}
			},
			/**
			 * 执行HTML变换
			 * TODO 检测以前的getSelection方式
			 * @param {String} type 类型
			 * @param {Object} para 数值
			 */
			htmlexec:function(type,para){
				//		if(!para){
				//			var selection=this.getSelection();
				//
				//			if(EG.Browser.isIE()) this.getSelection().createRange().execCommand(type,false);
				//			else this.getSelection().createRange().execCommand(type,false,false);
				//		}else {
				//			this.frame.contentWindow.document.execCommand(type,false,para);
				//		}
				this.focus();
				if(!para){
					if(EG.Browser.isIE()){
						this.doc.execCommand(type,false);
					}else{
						this.doc.execCommand(type,false,false);
					}
				}else{
					this.doc.execCommand(type,false,para);
				}
				this.focus();
			},
			/**
			 * 粘贴HTML
			 * @param {String} html HTML
			 */
			pasteHTML:function(html){
				this.focus();
				if(EG.Browser.isIE()){
					this.getSelection().createRange().pasteHTML(html);
				}else{
					this.doc.execCommand('InsertHtml',false,html);//也可以用this.getSelection().getRangeAt(0).surroundContents(ele)
				}
			},
			/**
			 * 获取选项
			 */
			getSelection:function(){
				if(EG.Browser.isIE()) return this.doc.selection;
				return this.doc.getSelection();
			},
			/**
			 * 当资源上传时自动添加标签
			 * @param {Object} cfg 配置
			 */
			addResOnUploaded:function(cfg){
				var me=this;
				var ct=cfg["ct"];
				var r=cfg["r"];
				var type=r["type"];
				var file=r["file"];
				var doDelete=r["doDelete"];
				//在相同form中找一个相关字段或页面元素中某一个元素,插入一个带图片标识的可以删除的标签
				//方式1:横向插入一个带图片标识的可以删除的标签,名字为文件名
				var el=EG.CE({tn:"div",cls:this.cls+"-uploadLabel",style:EG.Style.c.dv+";min-width:40px;max-width:200px",
					onmouseover	:ME._events.dUploadLabel.onmouseover,
					onmouseout	:ME._events.dUploadLabel.onmouseout,
					cn:[
						{tn:"div",onclick:function(){
							var p;
							if(type=="image"){
								p=me.getPlugin("image");
								p.showMenuPanel();
								p.setImageForm({
									src:file["path"]
								});
							}else if(type=="video"){
								p=me.getPlugin("video");
								p.showMenuPanel();
								p.setVideoForm(file);
							}else if(type=="zip"){
								p=me.getPlugin("zip");
								p.showMenuPanel();
								p.setZipForm(file);
							}
						},innerHTML:file["name"],style:EG.Style.c.dv+";cursor:pointer"},
						{tn:"a",cls:this.cls+"-uploadLabel-closer",onclick:function(){
							var ma=this;
							doDelete(function(){
								ma.parentNode.parentNode.removeChild(ma.parentNode);
							});
						}}
					]});

				ct.appendChild(el);
				//方式2:纵向插入一个带图片标识的可以删除的标签,名字为文件名
			},

			destroy:function(){
				for(var i=0;i<this.buildInParents.length;i++){
					this.buildInParents[i].destroy();
					EG.DOM.remove(this.buildInParents[i].getElement());
				}
			},

			execute:function(name,args){
				var p=this.getPlugin(name);
				p.execute(args);
			},

			statics:{
				def:{
					fontSize:"14px"
				},
				/**
				 * 全局插件类映射
				 */
				pluginsMap:{},
				/**
				 * 插件组
				 */
				pluginGroups:{
					//默认插件组
					def:[
						"bold",
						"italic",
						"underline",
						"fontname",
						"fontsize",
						"color",
						"textalign",
						"list",
						"indent",
						"image",
						"code",
						"full",
						"program"
					],
					simple:[
						"bold",
						"italic",
						"underline",
						"fontname",
						"fontsize",
						"color",
						"textalign",
						"list",
						"indent"
					]
				},
				/**
				 * 注册组件
				 * @param {String} name 组件名
				 * @param {EG.ui.editor.Plugin} plugin 插件
				 */
				registPlugin:function(name,plugin){
					ME.pluginsMap[name]=plugin;
				},
				_events:{
					iframe:{
						load:function(){
							var me	=this.item;
							me.doc=this.contentDocument || this.contentWindow.document;
//						EG.bindEvent(this.doc.body,"load",function(){
//							alert("MC:"+me.cacheHTML)
//
//						});

							var fn1=function(){
								if(me.loaded) return;

								me.doc.body.designMode="on";
								me.doc.body.contentEditable = true;
								me.doc.body.style.fontSize=ME.def.fontSize;

								EG.css(me.doc.body,"line-height:1.5;margin:0; padding:8px 8px;font-size:"+ME.def.fontSize);
//						alert("MC0:"+me.cacheHTML)
								if(me.cacheHTML!=null) me.doc.body.innerHTML=me.cacheHTML||"<p>&nbsp;</p>";
								else me.doc.body.innerHTML="<p>&nbsp;</p>";

								//双击事件
								EG.bindEvent(me.doc.body,"blur",function(e){
									//BD:当Iframe在被转移到其它节点后时，Iframe会被重新onload，此时要缓存
									me.cacheHTML=me.getContent();
								});

								me.loaded=true;
							};

							if(EG.Browser.isIE8()){
								me.doc.designMode = "on";

								EG.bindEvent(me.doc,"onreadystatechange",function(){
									if(me.doc.body){
										fn1();
									}
								});
							}else{
								fn1();
							}

							me.doc["editor"]=me;
							me.doc.onclick = function(){
								me.hideMenus();
							};

							EG.bindEvent(me.doc,"onpaste",function(e){
								if(me.uploadOnPaste){
									if ( e.clipboardData.items ) {
										var ele = e.clipboardData.items;
										for (var i = 0; i < ele.length; ++i) {
											if ( ele[i].kind == 'file' && ele[i].type.indexOf('image/') !== -1 ) {
												var blob = ele[i].getAsFile();
												var content = new window.FormData(EG.CE({
													tn: "form", enctype: "multipart/form-data",
													encoding: "multipart/form-data"	//FIX-IE6~8
												}));
												content.append("uploadfile", blob);

												EG.Ajax.send({
													method: "POST",
													url:me.imgUploadAction,// EG.MMVC.getPath().upload + "/?uploadPolicy=basCommonUP&objtype=ODM.ROOM",
													content: content,
													contentType: false,
													callback: function (data) {
														var result;
														eval("result=" + data + ";");
														if (result[0] == 0) {
															me.pasteHTML("<img src='" + result[1]["path"] + "'>");
														}
													}
												});
											}
										}
									}
								}
								return false;
							});
							//BUGFIX:IE Iframe失去焦点后再回来失去光标
							if(EG.Browser.isIE()){
								EG.bindEvent(this,"beforedeactivate",function(){
									var range = me.doc.selection.createRange();
									if(range.getBookmark) this.ieSelectionBookmark = range.getBookmark();
								});
								EG.bindEvent(this,"activate",function(){
									if(this.ieSelectionBookmark){
										var range = me.doc.body.createTextRange();
										range.moveToBookmark(this.ieSelectionBookmark);
										range.select();
										this.ieSelectionBookmark = null;
									}
								});
							}

							//Event支持
							EG.bindEvent(me.doc,"keydown",function(e){
								if(e==null) e=window.event;
								var code=e.keyCode;
								if(e.ctrlKey){
									//switch(code){
									//TODO case 90:{me.undo();}
									//ctrl+z Undo撤销
									//TODO ctrl+y支持重做事件
									//}
								}else if(e.shiftKey){

								}else{
									switch(code){
										//case 8:	{me.htmlexec('delete');return false;}
										//TODO 删除文字时有 BUG backspace 删除选中，防止回退
										case 13:{
											//me.saveStep();break;
											//TODO !!!!!临时取消
										}//回车保存
									}
								}
							});
							//单击事件
							EG.bindEvent(me.doc,"click",function(e){
								for(var i=0,il=me.clickHandlers.length;i<il;i++){
									me.clickHandlers[i](e);
								}
							});
							//双击事件
							EG.bindEvent(me.doc,"dblclick",function(e){
								for(var i=0,il=me.dblclickHandlers.length;i<il;i++){
									me.dblclickHandlers[i](e);
								}
							});
						}
					},
					dUploadLabel:{
						onmouseover:function(){
							EG.show(this.childNodes[1]);
						},
						onmouseout:function(){
							EG.hide(this.childNodes[1]);
						}
					},
					dUploadName:{

					}
				}
			}
		};
	});

	var Editor=EG.ui.Editor;
})();

/**
 * @class EG.ui.editor.Plugin
 * <AUTHOR>
 * @extends EG.ui.Item
 * 编辑器插件
 */
(function(){
	/**
	 * EG.ui.editor.Plugin 插件
	 */
	EG.define("EG.ui.editor.Plugin",{

		config:{
			buildInParent:false //在父参考系中创建
		},

		/**
		 * 获取Button
		 */
		getToolbarButton:function(){
			return this.toolbarButton;
		},
		/**
		 * 获取菜单
		 */
		getMenuPanel:function(){
			return this.menuPanel;
		},
		render:function(){

		}
	});
})();

/**
 * @class EG.ui.editor.ToolbarButton
 * <AUTHOR>
 * @extends EG.ui.Item
 * 工具栏按钮
 */
(function(){
	/**
	 * 工具栏按钮
	 */
	EG.define("EG.ui.editor.ToolbarButton",{
		extend:"EG.ui.Item",
		config:{
			/** @cfg {String} type 类型 */
			type:null,
			/** @cfg {String} cls CSS样式类 */
			cls:null
		},
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 * @param {Object} cfg 配置
		 */
		constructor:function(editor,cfg){
			this.initItem(cfg);
			this.editor=editor;
//			EG.copy(cfg,{
//				text:cfg["text"]||" ",
//				cls:
//			},true);
			this.button=EG.CE({tn:"a",cls:editor.cls+"-toolbar-"+this.cls,href:"javascript:void(0)"});

			if(cfg["click"]){
				EG.Event.bindEvent(this.button,"onclick",cfg["click"]);
			}

			//this.button=new EG.ui.Button(cfg);
			this.element=this.button;
		}
	});
})();





/**
 * @class EG.ui.editor.plugin.Bold
 * <AUTHOR>
 * @extends EG.ui.editor.Plugin
 * 工具栏插件-字体加粗
 */
(function(){
	EG.define("EG.ui.editor.plugin.Bold",{
		extend:"EG.ui.editor.Plugin",
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 */
		constructor:function(editor){
			this.editor=editor;
			var me=this;
			this.toolbarButton=new EG.ui.editor.ToolbarButton(editor,{
				type:"bold",
				click:function(){me.execute();},
				cls:"bold"
			});
		},
		/**
		 * 执行
		 */
		execute:function(){
			this.editor.htmlexec("Bold");
		}
	});
	var Bold=EG.ui.editor.plugin.Bold;
	//注册
	EG.ui.Editor.registPlugin("bold",Bold);
})();


/**
 * 
 */
/**
 * @class EG.ui.editor.plugin.Code
 * <AUTHOR>
 * @extends EG.ui.editor.Plugin
 * 工具栏插件-源码编辑
 */
(function(){
	EG.define("EG.ui.editor.plugin.Code",{
		extend:"EG.ui.editor.Plugin",
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 */
		constructor:function(editor){
			this.editor=editor;
			var me=this;
			this.codeModel=false;
			this.toolbarButton=new EG.ui.editor.ToolbarButton(editor,{
				type:"code",
				click:function(){
					me.codeModel=!me.codeModel;
					me.execute(me.codeModel);
				},
				mouseover:function(){

				},
				cls:"code"
			});
			this.editPanel=EG.CE({pn:this.editor.dHtml,tn:"textarea",style:"width:100%;background:#FEFEFE;border:0px;margin:0px;padding:0px"});

			EG.hide(this.editPanel);
		},
		
		/**
		 * 执行
		 * @param {String} codeModel 模式
		 */
		execute:function(codeModel){
			if(codeModel){
				EG.setValue(this.editPanel,this.editor.getContent());
				EG.css(this.editPanel,"height:"+this.editor.frame.clientHeight+"px;width:"+this.editor.frame.clientWidth+"px");
				EG.show(this.editPanel);
				EG.hide(this.editor.frame);

				var btns=this.editor.dToolbarButtons.childNodes;
				for(var i=0,il=btns.length;i<il;i++){
					if(btns[i]!=this.toolbarButton.getElement()){
						EG.hide(btns[i]);
					}
				}

			}else{
				this.editor.setContent(EG.getValue(this.editPanel));
				EG.hide(this.editPanel);
				EG.show(this.editor.frame);
				var btns=this.editor.dToolbarButtons.childNodes;
				for(var i=0,il=btns.length;i<il;i++){
					EG.show(btns[i]);
				}
			}
		}
	});
	var Code=EG.ui.editor.plugin.Code;

	//注册
	EG.ui.Editor.registPlugin("code",Code);
})();


/**
 * @class EG.ui.editor.plugin.Color
 * <AUTHOR>
 * @extends EG.ui.editor.Plugin
 * 工具栏插件-字体颜色
 */
(function(){
	EG.define("EG.ui.editor.plugin.Color",{
		extend:"EG.ui.editor.Plugin",
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 */
		constructor:function(editor){
			this.editor=editor;
			var me=this;
			this.toolbarButton=new EG.ui.editor.ToolbarButton(editor,{
				type:"color",
				click:function(){
					me.editor.hideMenus();
					var top=me.toolbarButton.getElement();
					var pos=EG.Tools.getElementPos(top,me.editor.getElement());
					pos.y=top.clientHeight;
					EG.Style.moveTo(me.menuPanel.dPop,pos);
					me.menuPanel.open();
				},
				mouseover:function(){
//				me.menuPanel.open();
				},
				cls:"color"
			});

			//创建面板
			this.buildMenuPanel();
		},
		
		/**
		 * 执行
		 * @param {String} color 颜色
		 */
		execute:function(color){
			this.editor.htmlexec("ForeColor",color);
		},
		
		/**
		 * 创建颜色板
		 */
		buildMenuPanel:function(){
			this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});
			var me=this;
			var tbody;
			var tab=EG.CE({tn:"table",cellSpacing:0,cellPadding:0,style:"float:left;border:0px;margin:0px",cn:[
				tbody=EG.CE({tn:"tbody"})
			]});

			var sl=5;
			for(var i=0;i<16;i++){

				//忽略组数
				if(i%4==0) continue;

				var tr=EG.CE({pn:tbody,tn:"tr"});
				for(var j=0;j<30;j++){
					var n1=j%sl;

					//忽略组数
					if(j%4==0) continue;
					var n2=Math.floor(j/sl)*3;
					var n3=n2+3;
					(function(){
						var color= Color.wc(
							(Color.cnum[n3]*n1		+Color.cnum[n2]*(sl-n1)),
							(Color.cnum[n3+1]*n1	+Color.cnum[n2+1]*(sl-n1)),
							(Color.cnum[n3+2]*n1	+Color.cnum[n2+2]*(sl-n1)),
							i
						);
						EG.CE({pn:tr,tn:"td",cn:[
							{tn:"div",cls:me.editor.cls+"-toolbar-color-box",style:"background-color:"+color,
								onclick:function(){
									me.execute(color);
									me.menuPanel.close();
								},
								onmouseover:function(){
									EG.setCls(this,["toolbar-color-box","toolbar-color-boxOn"],me.editor.cls);
								},
								onmouseout:function(){
									EG.setCls(this,["toolbar-color-box"],me.editor.cls);
								}
							}
						]});
					})();
				}
			}

			this.menuPanel.addChildren(tab);
		},
		statics:{
			hexch:["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F"],
			cnum:[1, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0],
			colors:["#FF0000","#00FF00","#0000FF","#FFFF00","#00FFFF","#FF00FF"],
			
			/**
			 * toHex
			 * @param {Number} n
			 */
			toHex:function(n){
				var h,l,n=Math.round(n);
				l=n%16;
				h=Math.floor((n/16))%16;
				return (Color.hexch[h]+Color.hexch[l]);
			},
			/**
			 *
			 * @param {Number} r
			 * @param {Number} g
			 * @param {Number} b
			 * @param {Number} n
			 */
			wc:function(r, g, b, n){
				r=((r*16+r)*3*(15-n)+0x80*n)/15;
				g=((g*16+g)*3*(15-n)+0x80*n)/15;
				b=((b*16+b)*3*(15-n)+0x80*n)/15;
				return '#'+Color.toHex(r)+Color.toHex(g)+Color.toHex(b);
			}
		}
	});
	var Color=EG.ui.editor.plugin.Color;
	//注册
	EG.ui.Editor.registPlugin("color",Color);
})();


/**
 * @class EG.ui.editor.plugin.Fontname
 * <AUTHOR>
 * @extends EG.ui.editor.Plugin
 * 工具栏插件-字体
 */
(function(){
	EG.define("EG.ui.editor.plugin.Fontname",{
		extend:"EG.ui.editor.Plugin",
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 */
		constructor:function(editor){
			this.editor=editor;
			var me=this;
			this.toolbarButton=new EG.ui.editor.ToolbarButton(editor,{
				type:"fontname",
				text:"字体",
				click:function(){
					me.editor.hideMenus();
					var top=me.toolbarButton.getElement();
					var pos=EG.Tools.getElementPos(top,me.editor.getElement());
					pos.y=top.clientHeight;
					EG.Style.moveTo(me.menuPanel.dPop,pos);
					me.menuPanel.open();
				},
				mouseover:function(){

				},
				style:"font-size:12px",
				cls:"fontname"
			});

			this.buildMenuPanel();
		},
		/**
		 * 执行
		 * @param {String} fontname 字体
		 */
		execute:function(fontname){
			this.editor.htmlexec("Fontname",fontname);
		},
		/**
		 * 创建字体板
		 */
		buildMenuPanel:function(){
			this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});
			var me=this;
			var d=EG.CE({tn:"div",style:""});
			for(var i=0,il=Fontname.fontnames.length;i<il;i++){
				(function(){
					var fontname=Fontname.fontnames[i];
					EG.CE({pn:d,tn:"a",cls:me.editor.cls+"-toolbar-fontname-box",style:"font-family:"+fontname,innerHTML:fontname,onclick:function(){
						me.execute(fontname);
						me.menuPanel.close();
					}});
				})();
			}
			this.menuPanel.addChildren(d);
		},
		statics:{
			fontnames:['微软雅黑','宋体','黑体','楷体_GB2312','隶书','幼圆','Arial','Arial Narrow','Arial Black','Comic Sans MS','Courier','System','Times New Roman']
		}
	});

	var Fontname=EG.ui.editor.plugin.Fontname;
	//注册
	EG.ui.Editor.registPlugin("fontname",Fontname);
})();
/**
 * @class EG.ui.editor.plugin.Code
 * <AUTHOR>
 * @extends EG.ui.editor.Plugin
 * 工具栏插件-字体大小
 */
(function(){
	EG.define("EG.ui.editor.plugin.Fontsize",{
		extend:"EG.ui.editor.Plugin",
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 */
		constructor:function(editor){
			this.editor=editor;
			var me=this;
			this.toolbarButton=new EG.ui.editor.ToolbarButton(editor,{
				type:"fontsize",
				click:function(){
					me.editor.hideMenus();
					var top=me.toolbarButton.getElement();
					var pos=EG.Tools.getElementPos(top,me.editor.getElement());
					pos.y=top.clientHeight;
					EG.Style.moveTo(me.menuPanel.dPop,pos);
					me.menuPanel.open();
				},
				mouseover:function(){
	//				me.menuPanel.open();
				},
				cls:"fontsize"
			});

			//创建面板
			this.buildMenuPanel();
		}
		/**
		 * 执行
		 * @param {String} fontname 字体大小
		 */,
		execute:function(fontsize){
			this.editor.htmlexec("FontSize",fontsize);
		},
		/**
		 * 创建字体大小
		 */
		buildMenuPanel:function(){
			this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});
			var me=this;
			var d=EG.CE({tn:"div",style:""});
			for(var i=0,il=Fontsize.fontsizes.length;i<il;i++){
				(function(){
					var fontsize=Fontsize.fontsizes[i];
					EG.CE({pn:d,tn:"a",cls:me.editor.cls+"-toolbar-fontsize-box",style:"font-size:"+(fontsize+10)+"px",innerHTML:fontsize+"号",onclick:function(){
						me.execute(fontsize);
						me.menuPanel.close();
					}});
				})();
			}
			this.menuPanel.addChildren(d);
		},
		statics:{
			fontsizes:[1,2,3,4,5,6,7]
		}
	});
	var Fontsize=EG.ui.editor.plugin.Fontsize;
	//注册
	EG.ui.Editor.registPlugin("fontsize",Fontsize);
})();


/**
 * @class EG.ui.editor.plugin.Full
 * <AUTHOR>
 * @extends EG.ui.editor.Plugin
 * 工具栏插件-全屏
 */
(function(){
	EG.define("EG.ui.editor.plugin.Full",{
		extend:"EG.ui.editor.Plugin",
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 */
		constructor:function(editor){
			this.editor=editor;
			var me=this;
			this.toolbarButton=new EG.ui.editor.ToolbarButton(editor,{
				type:"full",
				click:function(){me.execute();},
				cls:"full"
			});
		},
		/**
		 * 执行
		 */
		execute:function(){
			 if(!this.fulled){
				 if(!this.d){
					 this.d=new EG.ui.Dialog({
						 lock		:true,
						 closeable	:false,
						 height		:"100%",
						 width		:"100%",
						 renderTo	:EG.getBody(),
						 id:"dddd",
						 btns:[
							 {text:"关闭",click:function(){me.d.close();}}
						 ]
					 });
					 this.d.open();
					 this.d.dBody.appendChild(this.editor.getElement());
					 this.editor.width="100%";
					 this.editor.height="100%";
					 this.editor.render();
				 }
				 this.d.open();
			 }else{
				 this.d.close();
			 }
		}
	});
	var Full=EG.ui.editor.plugin.Full;
	//注册
	EG.ui.Editor.registPlugin("full",Full);
})();


/**
 * @class EG.ui.editor.plugin.Image
 * <AUTHOR>
 * @extends EG.ui.editor.Plugin
 * 工具栏插件-图片
 */
(function(){
	EG.define("EG.ui.editor.plugin.Image",{
		extend:"EG.ui.editor.Plugin",
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 */
		constructor:function(editor){
			this.editor=editor;
			var me=this;

			//在父中创建
			this.buildInParent=true;

			this.toolbarButton=new EG.ui.editor.ToolbarButton(editor,{
				type:"image",
				click:function(){
					if(me.resPicker) me.resPicker.close();
					me.showMenuPanel();
				},
				mouseover:function(){
//				me.menuPanel.open();
				},
				cls:"image"
			});

			//上传策略
			this.uploadPolicy	=this.editor.imgUploadPolicy||this.editor.uploadPolicy;
			this.uploadAction	=this.editor.imgUploadAction||this.editor.uploadAction;
			//上传后动作
			this.onUploaded		=this.editor.imgOnUploaded||this.editor.onUploaded;

			//创建面板
			this.buildMenuPanel();

			//编辑区双击事件
			this.editor.dblclickHandlers.push(function(e){
				if(e.target&&e.target.tagName.toUpperCase()=="IMG"){
					//设置当前选中的图像
					me.selectedImg=e.target;

					//双击讲图像属性设置到Form中
					me.setImageForm(e.target,true);
					me.showMenuPanel();
				}
			});

			//编辑区单击事件
			this.editor.clickHandlers.push(function(e){
				if(e.target&&e.target.tagName.toUpperCase()=="IMG"){
					//
				}else{
					me.selectedImg=null;
				}
			});
		},
		/**
		 * 显示面板
		 */
		showMenuPanel:function(){
			this.editor.hideMenus();
//			var top=this.toolbarButton.getElement();
//			var pos=EG.Tools.getElementPos(top,this.editor.getElement());
//			pos.y=top.clientHeight;
			//EG.Style.moveTo(this.menuPanel.getElement(),pos);
			this.menuPanel.open();

			//EG.Style.center(this.menuPanel.getElement(),this.editor.getElement());
			this.editor.curMenuPanel=this.menuPanel;
			this.render();
		},
		/**
		 * 执行
		 * @param {String} atrs 图像参数
		 */
		execute:function(atrs){
			var img;
			var me=this;
			if(typeof(atrs)=="string") atrs={src:atrs};
			if(this.selectedImg){
				img=this.selectedImg;
				this.setImageAtrs(img,atrs);
			}else{
				//插入新Img时是异步的，需要在onload中完成插入动作
				img=EG.CE({tn:"img",onload:function(){
					var ctx=EG.DOM.getOuterHTML(this);
					me.editor.pasteHTML(ctx);
				}});
				this.setImageAtrs(img,atrs);
				img.src=atrs.src;
			}
		},
		/**
		 * 渲染
		 */
		render:function(){
			this.imgForm.render();
		},
		/**
		 * 获取上传Action
		 * @param type
		 * @return {String}
		 */
		getUploadAction:function(type){
			return EG.MMVC.getPath().upload+"/?uploadPolicy="+this.uploadPolicy+"&type="+type;
		},
		/**
		 * 上传前检查
		 */
		beforeUpload:function(){
			//TODO 用activeXObject来检测文件大小
			//function ShowSize(files) {var fso=new ActiveXObject("Scripting.FileSystemObject");var mySize = fso.GetFile(files).size/1024;alert(mySize+" K ");}
			return true;
		},
		/**
		 * 创建图上先传层
		 */
		buildMenuPanel:function(){

			var me=this;

			var p=this.editor.parent||this.editor;
			if(p.getElement) p=p.getElement();

			//图像
			var imgPickers=this.editor.imgPickers;


			this.menuPanel=new EG.ui.Dialog({
				closeable	:true,
				lock		:true,
				posFix		:true,
				title		:"图片设置",
				//cls			:"eg_pop_blank",
				//style		:"z-index:1",
				fullable	:true,
				parent		:p,
				width		:350,
				height		:"auto",
				layout		:{type:"line",direct:"V"},
				items:[
					this.imgForm=new EG.ui.Form({
						width		:350,
						height		:"auto",
						labelWidth	:50,
						layout		:"default",
						items:[
							{xtype:"tabPanel",width:"100%",height:120,items:[
								{
									tab:{title:"图片",style:"width:60px"},
									panel:{
										layout:"table",
										items:[
											{xtype:"formItem",pos:[0,0],title:"图片"	,name:"imgShowArea"		,type:"label",length:15,notnull:true,height:40},
											{xtype:"button"	 ,pos:[0,1],text:"选择",hidden:imgPickers?false:true,click:function(){
												me.resPicker.open();
											}},
											{xtype:"formItem",pos:[1,0],title:"路径"	,name:"imgSrc"			,type:"text",style:"overflow:hidden"},
											{xtype:"formItem",pos:[2,0],title:"宽"	,name:"imgWidth"		,type:"text",length:5,style:"width:40px",after:"px"},
											{xtype:"formItem",pos:[2,1],title:"高"	,name:"imgHeight"		,type:"text",length:5,style:"width:40px",after:"px"},
											{xtype:"formItem",pos:[3,0],title:"隐藏"	,name:"imgSrcUri"		,type:"text",hidden:true}
										]
									}
								},
								{
									tab:{title:"浮动",style:"width:60px"},
									tabStyle:"width:100",
									panel:{
										layout:"table",
										items:[
											{xtype:"formItem",pos:[0,0],title:"环绕"	,name:"imgFloat"		,type:"select",textvalues:[["不环绕",""],["向左","left"],["向右","right"]]},
											{xtype:"formItem",pos:[1,0],title:"左距"	,name:"imgMarginLeft"	,type:"text",length:10,after:"px"},
											{xtype:"formItem",pos:[1,1],title:"右距"	,name:"imgMarginRight"	,type:"text",length:10,after:"px"},
											{xtype:"formItem",pos:[2,0],title:"上距"	,name:"imgMarginTop"	,type:"text",length:10,after:"px"},
											{xtype:"formItem",pos:[2,1],title:"下距"	,name:"imgMarginBottom"	,type:"text",length:10,after:"px"}
										]
									}
								}
							]}
						]
					})
				],
				btns:[
					{text:"确定",cls:"eg_button_small",click:function(){me.doInsertImage();me.menuPanel.close();}},
					{text:"取消",cls:"eg_button_small",click:function(){me.menuPanel.close();},style:"margin-left:10px"}
				]
			});


			if(imgPickers){
				this.resPicker_opened=false;
				var ipTypes=imgPickers.types;
				var tps=[];
				this.tpm={};
				for(var i=0;i<ipTypes.length;i++){
					var ipT=ipTypes[i];
					tps.push([ipT["title"],ipT["type"]]);
					this.tpm[ipT["type"]]=ipT;
				}

				//创建一个选择器
				this.resPicker=new EG.ui.Dialog({
					renderTo	:p,
					title		:"选择图片",
					width		:"100%",
					height		:"auto",
					style		:"margin:10px;z-index:2;",
					lock		:true,
					posFix		:true,
					layout:{type:"line",direct:"V"},
					items:[
						{xtype:"panel",height:EG.ui.FormItem._config.height,layout:"line",items:[
							this.sltCg=new EG.ui.FormItem({xtype:"formItem",title:"分类",type:"select",textvalues:tps,width:200,onchange:function(v){
								me.loadPickerFiles(v);
							}}),
							{xtype:"button",text:"刷新",cls:"eg_button_small",onclick:function(){
								me.loadPickerFiles(me.sltCg.getValue());
							}}
						]},
						this.rpm=new EG.ui.Panel({xtype:"panel",style:"margin:3px;border:1px solid gray;overflow:auto",height:450}),
						this.rpf=new EG.ui.Panel({xtype:"panel",style:"margin:3px",height:30})
					],
					btns:[
						{xtype:"button",text:"选择",cls:"eg_button_small",click:function(){
							me.doPickerSelect();
						}}
					],
					afterOpen:function(){
						if(!me.resPicker_opened){
							var v=me.sltCg.getValue();
							if(v){
								me.loadPickerFiles(v);
							}
						}
						me.resPicker_opened=true;
					}
				});
			}


			if(this.uploadPolicy||this.uploadAction){
				this.imgForm.items[0].addItem({
					tab:{title:"上传",style:"width:60px"},
					panel:{
						layout:"table",
						items:[
							{xtype:"formItem",title:"上传",name:"imgUpload",type:"upload",height:50,style:"overflow:hidden",autoupload:true,action:this.uploadAction||this.getUploadAction("image"),
								callback:function(r){
									var f=r["file"]?r["file"]:r;
									//设置图像预览及路径
									me.setImageForm({src:f["path"]});
									//取消等待信息
									EG.Locker.lock(false);
									//执行OnUpload
									if(me.onUploaded) me.onUploaded.apply(me.editor,[r]);
								},
								exts:["JPEG","JPG","PNG","GIF","BMP"],showWait:true,beforeUpload:function(){return me.beforeUpload("img");}
							}
						]
					}
				});
			}

			//this.menuPanel.addItem(this.imgForm);
		},

		doPickerSelect:function(){
			var tp=this.tpm[this.sltCg.getValue()];
			var uri=tp.getSelectedUri();
			if(uri==null){
				EG.Locker.message("请选择文件");
				return;
			}

			this.imgForm.getFormItem("imgSrc")		.setValue(uri.src);
			this.imgForm.getFormItem("imgShowArea")	.setValue("<img src='"+uri.src+"' height='40' />");
			this.imgForm.getFormItem("imgSrcUri")	.setValue(uri.uri);
			this.resPicker.close();
		},

		loadPickerFiles:function(type){
			var tp=this.tpm[type];
			tp.load(this.rpm);
		},

		setFiles:function(files){

			//清空
			this.rpm.clear();

			//设置页标
			this.rpf.getElement().innerHTML=""+files.length+"个文件";

			//根据类型创建列表或表格


		},

		/**
		 * 执行插入或替换已选中的图片
		 */
		doInsertImage:function(fd){
			var img={};

			if(arguments.length==0){
				fd=this.imgForm.getData();
			}

			img.src=fd["imgSrc"];
			if(img.src==""){EG.Locker.message("请选择图片或上传新图片");return;}
			img.width				=fd["imgWidth"];
			img.height				=fd["imgHeight"];
			img.style={};
			if(fd["imgFloat"]		!="")	{EG.css(img,"float:"		+fd["imgFloat"]);}
			if(fd["imgMarginLeft"]	!="")	{EG.css(img,"marginLeft:"	+fd["imgMarginLeft"]);}
			if(fd["imgMarginRight"]	!="")	{EG.css(img,"marginRight:"	+fd["imgMarginRight"]);}
			if(fd["imgMarginTop"]	!="")	{EG.css(img,"marginTop:"	+fd["imgMarginTop"]);}
			if(fd["imgMarginBottom"]!="")	{EG.css(img,"marginBottom:"	+fd["imgMarginBottom"]);}

			img.srcUri=this.imgForm.getFormItem("imgSrcUri").getValue();

			this.execute(img);
		},
		/**
		 * 设置图像属性
		 * @param {HTMLImageElement} img 图像元素
		 * @param {Object} atrs 属性值
		 */
		setImageAtrs:function(img,atrs){

			if(atrs["style"]) EG.css(img,atrs["style"]);
			if(atrs["width"]==null||atrs["width"]==""){
				img.removeAttribute("width");
			}else{
				img.width=atrs["width"];
			}

			if(atrs["height"]==null||atrs["height"]==""){
				img.removeAttribute("height");
			}else{
				img.height=atrs["height"];
			}

			if(atrs["srcUri"]!=null&&atrs["srcUri"]!=""){
				img.setAttribute("srcUri",atrs["srcUri"]);
			}

		},
		/**
		 * 双击时设置表单,上传后设置表单
		 * @param {Object} img 属性值
		 */
		setImageFormByImg:function(img){
			var atrs={
				src:img.src,
				style:img.style
			};
			this.setImageForm(atrs);
		},
		/**
		 * 设置图像表单
		 * @param {Object} atrs 属性值
		 */
		setImageForm:function(atrs){
			this.imgForm.items[0].getTab(0).select();
			//创建预览图
			var preViewImg=EG.CE({tn:"img"});
			var me=this;
			preViewImg.onload=function(){
				var ow=this.width,oh=this.height;
				me.imgForm.getFormItem("imgWidth").setValue(this.width);
				me.imgForm.getFormItem("imgHeight").setValue(this.height);
				if(this.width>this.height){
					this.height=oh/(ow/100);
					this.width=100;//最大宽
				}else{
					this.width=ow/(oh/40);
					this.height=40;//最大高
				}
			};
			preViewImg.style.cursor	="pointer";
			preViewImg.ondblclick	=function(){window.open(this.src);};
			preViewImg.title		="双击查看图片";
			preViewImg.src			=atrs.src;
			//TODO 待使用currentStyle,并且要解决FIREFOX中float的cssFloat别名问题
			if(atrs.style){
				if(atrs.style["float"])		{this.imgForm.getFormItem("imgFloat")		.setValue(atrs.style["float"].toLowerCase());}
				if(atrs.style.marginLeft)	{this.imgForm.getFormItem("imgMarginLeft")	.setValue(atrs.style.marginLeft.toLowerCase());}
				if(atrs.style.marginRight)	{this.imgForm.getFormItem("imgMarginRight")	.setValue(atrs.style.marginRight.toLowerCase());}
				if(atrs.style.marginTop)	{this.imgForm.getFormItem("imgMarginTop")	.setValue(atrs.style.marginTop.toLowerCase());}
				if(atrs.style.marginBottom)	{this.imgForm.getFormItem("imgMarginBottom").setValue(atrs.style.marginBottom.toLowerCase());}
			}


			if(atrs.width!=null) 	this.imgForm.getFormItem("imgWidth").setValue(atrs.width);
			if(atrs.height!=null) 	this.imgForm.getFormItem("imgHeight").setValue(atrs.height);

			this.imgForm.getFormItem("imgShowArea").setValue("");
			this.imgForm.getFormItem("imgShowArea").prop.getElement().appendChild(preViewImg);


			//如果是同域名的

			var src=atrs.src;
			var domainAddress=EG.Browser.getDomainAddress();
			if(src.indexOf(domainAddress)==0){
				src=src.substr(domainAddress.length);
			}
			this.imgForm.getFormItem("imgSrc").setValue(src);
		}
	});
	var Image=EG.ui.editor.plugin.Image;
	//注册
	EG.ui.Editor.registPlugin("image",Image);
})();


/**
 * @class EG.ui.editor.plugin.Indent
 * <AUTHOR>
 * @extends EG.ui.editor.Plugin
 * 工具栏插件-字体编号
 */
(function(){
	EG.define("EG.ui.editor.plugin.Indent",{
		extend:"EG.ui.editor.Plugin",
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 */
		constructor:function(editor){
			this.editor=editor;
			var me=this;
			this.toolbarButton=new EG.ui.editor.ToolbarButton(editor,{
				type:"indent",
				click:function(){
					me.editor.hideMenus();
					var top=me.toolbarButton.getElement();
					var pos=EG.Tools.getElementPos(top,me.editor.getElement());
					pos.y=top.clientHeight;
					EG.Style.moveTo(me.menuPanel.dPop,pos);
					me.menuPanel.open();
				},
				mouseover:function(){
	//				me.menuPanel.open();
				},
				cls:"indent"
			});

			//创建面板
			this.buildMenuPanel();
		}
		/**
		 * 执行
		 * @param {String} indent 缩进
		 */,
		execute:function(indent){
			this.editor.htmlexec(indent);
		},
		/**
		 * 创建字体大小
		 */
		buildMenuPanel:function(){
			this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});
			var me=this;
			var d=EG.CE({tn:"div",style:""});
			for(var i=0,il=Indent.indents.length;i<il;i++){
				(function(){
					var indent=Indent.indents[i];
					EG.CE({pn:d,tn:"a",cls:me.editor.cls+"-toolbar-indent-box",innerHTML:indent[0],onclick:function(){
						me.execute(indent[1]);
						me.menuPanel.close();
					}});
				})();
			}
			this.menuPanel.addChildren(d);
		},
		statics:{
			indents:[["增加缩进","Indent"],["减少缩进","Outdent"]]
		}
	});

	var Indent=EG.ui.editor.plugin.Indent;
	//注册
	EG.ui.Editor.registPlugin("indent",Indent);
})();


/**
 * @class EG.ui.editor.plugin.Indent
 * <AUTHOR>
 * @extends EG.ui.editor.Plugin
 * 工具栏插件-字体倾斜
 */
(function(){
	EG.define("EG.ui.editor.plugin.Italic",{
		extend:"EG.ui.editor.Plugin",
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 */
		constructor:function(editor){
			this.editor=editor;
			var me=this;
			this.toolbarButton=new EG.ui.editor.ToolbarButton(editor,{
				type:"italic",
				click:function(){me.execute();},
				cls:"italic"
			});
		},
		/**
		 * 执行
		 */
		execute:function(){
			this.editor.htmlexec("Italic");
		}
	});

	var Italic=EG.ui.editor.plugin.Italic;
	//注册
	EG.ui.Editor.registPlugin("italic",Italic);
})();


/**
 * 
 */
/**
 * @class EG.ui.editor.plugin.List
 * <AUTHOR>
 * @extends EG.ui.editor.Plugin
 * 工具栏插件-字体编号
 */
(function(){
	EG.define("EG.ui.editor.plugin.List",{
		extend:"EG.ui.editor.Plugin",
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 */
		constructor:function(editor){
			this.editor=editor;
			var me=this;
			this.toolbarButton=new EG.ui.editor.ToolbarButton(editor,{
				type:"list",
				click:function(){
					me.editor.hideMenus();
					var top=me.toolbarButton.getElement();
					var pos=EG.Tools.getElementPos(top,me.editor.getElement());
					pos.y=top.clientHeight;
					EG.Style.moveTo(me.menuPanel.dPop,pos);
					me.menuPanel.open();
				},
				mouseover:function(){
	//				me.menuPanel.open();
				},
				cls:"list"
			});

			//创建面板
			this.buildMenuPanel();
		},
		
		/**
		 * 执行
		 * @param {String} list 编号
		 */
		execute:function(list){
			this.editor.htmlexec("Insert"+list+"List");
		},
		
		/**
		 * 创建字体大小
		 */
		buildMenuPanel:function(){
			this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});
			var me=this;
			var d=EG.CE({tn:"div",style:""});
			for(var i=0,il=List.lists.length;i<il;i++){
				(function(){
					var list=List.lists[i];
					EG.CE({pn:d,tn:"a",cls:me.editor.cls+"-toolbar-list-box",innerHTML:list[0],onclick:function(){
						me.execute(list[1]);
						me.menuPanel.close();
					}});
				})();
			}
			this.menuPanel.addChildren(d);
		},
		statics:{
			lists:[["数字列表","Ordered"],["符号列表","Unordered"]]
		}
	});
	var List=EG.ui.editor.plugin.List;
	//注册
	EG.ui.Editor.registPlugin("list",List);
})();


/**
 * @class EG.ui.editor.plugin.Program
 * <AUTHOR>
 * @extends EG.ui.editor.Plugin
 * 工具栏插件-语言
 */
(function(){
	EG.define("EG.ui.editor.plugin.Program",{
		extend:"EG.ui.editor.Plugin",
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 */
		constructor:function(editor){
			this.editor=editor;
			var me=this;
			this.toolbarButton=new EG.ui.editor.ToolbarButton(editor,{
				type:"program",
				click:function(){me.menuPanel.open();},
				cls:"program"
			});

			//创建面板
			this.buildMenuPanel();
		},

		/**
		 * 创建菜单层
		 */
		buildMenuPanel:function(){

			var me=this;

			var p=this.editor.parent||this.editor;
			if(p.getElement) p=p.getElement();

			this.menuPanel=new EG.ui.Dialog({
				closeable	:true,
				lock		:true,
				posFix		:true,
				title		:"代码编辑",
				fullable	:true,
				parent		:p,
				width		:350,
				height		:"auto",
				layout		:{type:"line",direct:"V"},
				items:[
					this.fiText=new EG.ui.FormItem({xtype:"formItem",title:"",showLeft:false,type:"textarea",length:15,notnull:true,height:150})
				],
				btns:[
					{text:"确定",cls:"eg_button_small",click:function(){me.doInsertProgram();}},
					{text:"取消",cls:"eg_button_small",click:function(){me.menuPanel.close();},style:"margin-left:10px"}
				]
			});
		},

		/**
		 * 插入代码
		 */
		doInsertProgram:function(){
			var str	=this.fiText.getValue();
			str		=EG.String.trim(str);
			str		=EG.Tools.filterSpecChar(str);
			if(!EG.String.startWith	(str,"<pre ")) 	str="<pre class='"+this.editor.cls+"-toolbar-program-code'>"+str;
			if(!EG.String.endWith	(str,"</pre>")) str=str+"</pre>";
			this.editor.pasteHTML(str);
		}
	});
	var Program=EG.ui.editor.plugin.Program;
	//注册
	EG.ui.Editor.registPlugin("program",Program);
})();


/**
 * 
 */
/**
 * @class EG.ui.editor.plugin.Textalign
 * <AUTHOR>
 * @extends EG.ui.editor.Plugin
 * 工具栏插件-字体对齐
 */
(function(){
	/**
	 * EG.ui.editor.plugin.Textalign 字体对齐
	 */
	EG.define("EG.ui.editor.plugin.Textalign",{
		extend:"EG.ui.editor.Plugin",
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 */
		constructor:function(editor){
			this.editor=editor;
			var me=this;
			this.toolbarButton=new EG.ui.editor.ToolbarButton(editor,{
				type:"textalign",
				click:function(){
					me.editor.hideMenus();
					var top=me.toolbarButton.getElement();
					var pos=EG.Tools.getElementPos(top,me.editor.getElement());
					pos.y=top.clientHeight;
					EG.Style.moveTo(me.menuPanel.dPop,pos);
					me.menuPanel.open();
				},
				mouseover:function(){
	//				me.menuPanel.open();
				},
				cls:"textalign"
			});

			//创建面板
			this.buildMenuPanel();
		},
		/**
		 * 执行
		 * @param {String} align 对齐
		 */
		execute:function(align){
			this.editor.htmlexec("Justify"+align);
		},
		/**
		 * 创建字体大小
		 */
		buildMenuPanel:function(){
			this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});
			var me=this;
			var d=EG.CE({tn:"div",style:""});
			for(var i=0,il=Textalign.fontaligns.length;i<il;i++){
				(function(){
					var fontalign=Textalign.fontaligns[i];
					EG.CE({pn:d,tn:"a",cls:me.editor.cls+"-toolbar-textalign-box",innerHTML:fontalign[0],onclick:function(){
						me.execute(fontalign[1]);
						me.menuPanel.close();
					}});
				})();
			}
			this.menuPanel.addChildren(d);
		},
		statics:{
			fontaligns:[["左对齐","Left"],["居中对齐","Center"],["右对齐","Right"]]
		}
	});

	var Textalign=EG.ui.editor.plugin.Textalign;
	//注册
	EG.ui.Editor.registPlugin("textalign",Textalign);
})();


/**
 * @class EG.ui.editor.plugin.Textalign
 * <AUTHOR>
 * @extends EG.ui.editor.Plugin
 * 工具栏插件-字体倾斜
 */
(function () {
	EG.define("EG.ui.editor.plugin.Underline", {
		extend:"EG.ui.editor.Plugin",
		/**
		 * @constructor 构造函数
		 * @param {EG.ui.Editor} editor 编辑器
		 */
		constructor:function (editor) {
			this.editor = editor;
			var me = this;
			this.toolbarButton = new EG.ui.editor.ToolbarButton(editor, {
				type:"underline", click:function () {
					me.execute();
				},
				cls:"underline"
			});

			//this.menuPanel=new EG.ui.editor.MenuPanel();
		},
		/**
		 * 执行
		 */
		execute:function () {
			this.editor.htmlexec("Underline");
		}
	});
	var Underline=EG.ui.editor.plugin.Underline;
	//注册
	EG.ui.Editor.registPlugin("underline", Underline);
})();


/**
 * 
 */
/**
 * 
 */
/**
 * 
 */
(function(){
	/**
	 * EG.ui.Upload 上传
	 */
	EG.define("EG.ui.Upload",[
		"EG.ui.Item",
		"EG.ui.Button"
	],function(Item,Button,ME){
		return {
			alias:"upload",
			extend:Item,
			config:{
				button				:null,
				action				:null,
				callback			:null,
				beforeUpload		:null,
				exts				:null,
				showWait			:false,
				showFilename		:true,
				showPath			:true,
				autoupload			:false,     //自动上传
				onselect			:null,
				filename			:null,
				subSelf		    	:true,		//用自身提交
				cls					:"eg_upload",
				showProcess			:false,
				name				:null,		//从FormItem中传递过来
				paramsConfig		:null,
				btnText				:"选择",		//按钮字
				btnCls				:null,		//按钮样式类
				btnStyle			:""			//按钮样式
			},
			constructor:function(cfg){
				this.initItem(cfg);

				this.idx = EG.UI.GITEMIDX++;
				EG.UI.GITEMS[this.idx] = this;

				this.filename		=this.name||this.filename||("file" + this.idx);

				//if(!this.action)	throw new Error("EG.ui.Upload#上传action不能为空");



				//创建ActionFrame
				EG.DOM.getActionFrame();

				//创建Element
				if(this.subSelf){
					this.element=EG.CE({tn:"form",cls:this.cls,method : "post",encoding : "multipart/form-data",target:"actionFrame"});
				}else{
					this.element=EG.CE({tn:"div",cls:this.cls});
				}

				//设置Action
				this.setAction(this.action);

				//子元素
				EG.CE({ele:this.element,item:this,cn:[
					this.dPath 	=EG.CE({tn:"div",cls:this.cls+"-dPath",style:"display:none;height:22px;"}),
					this.dFileName =EG.CE({tn:"div",cls:this.cls+"-dFileName",style:"display:none",cn:[]}),
					this.dFileinput=EG.CE({tn:"div",cls:this.cls+"-dFileinput",style:"position:relative",cn:[

						this.dSelectBtn=new Button({text:this.btnText,click:function(){},style:"margin-left:2px"+this.btnStyle}),
						/*
						 this.dSelectBtn=EG.CE({tn:"div",cls:this.cls+"-selectBtn",innerHTML:"选择",style:"position:relative;cursor:pointer;",cn:[
						 ]}),
						 * */
						this.fileinput=EG.CE({tn:"input",cls:this.cls+"-fileinput",name:this.filename,type :"file",style:"cursor:pointer;position:absolute;left:0px;top:0px;opacity:0;filter:alpha(opacity=0);"})
					]}),
					//隐藏参数区
					this.dHiddenParams=EG.CE({tn:"div",style:"display:none"})
				]});

				// BUGFIX:firefox的input file需要设置size来定宽度
//  		if (EG.Browser.isFirefox()) {
//  			this.fileinput.size = 0;
//  		}
				var me=this;

				EG.bindEvent(this.fileinput, "onchange", function() {
					var path=EG.getValue(me.fileinput);
					if(path){
						var pos = path.lastIndexOf("/");
						if(pos==-1){
							pos=path.lastIndexOf("\\");
						}
						var filename = path.substr(pos +1);
						EG.setValue(me.dFileName,filename);
						if(me.showFilename) EG.show(me.dFileName);
						if(me.onselect){
							me.onselect.apply(me,[path,filename]);
						}
						if(me.autoupload){
							me.submit();
						}
					}else{
						EG.hide(me.dFileName);
					}
				});


				//自动上传事件
				if (!this.autoupload&&this.showBtn){
					if (!this.button){
						this.button = new Button({text:this.btnText,click:function(){me.submit();},style:"veritical-align:middle;margin-left:10px"}).getElement();
					}
					this.element.appendChild(this.button);
				}

				EG.css(this.element,this.style);

				if(this.paramsConfig){
					this.setParams(this.paramsConfig);
				}
			},
			/**
			 * 设置Action
			 * @param action
			 */
			setAction:function(action){
				if(this.callback) 	action += "&callback=parent.EG.UI.GITEMS[" + this.idx + "].onUploaded&onerror=parent.EG.UI.GITEMS["+this.idx+"].onError";

				if(this.showProcess){
					action +="&callbackProcess=parent.EG.UI.GITEMS[" + this.idx + "].onProcess";
				}

				this.action=action;

				if(this.subSelf){
					EG.CE({ele:this.element,action:action});
				}
			},

			/**
			 * 设值
			 * @param value 值
			 */
			setValue:function(value) {
				EG.setValue(this.dPath,value);
			},
			/**
			 * 获值
			 * @returns {String}
			 */
			getValue:function() {
				return EG.getValue(this.dPath);
			},
			/**
			 * 获取Element
			 */
			getElement:function() {
				return this.element;
			},

			/**
			 * 设置参数值
			 * @param params
			 */
			setParams:function(params){
				for(var key in params){
					this.setParam(key,params[key]);
				}
			},



			/**
			 * 设置参数值
			 * @param name
			 * @param value
			 */
			setParam:function(name,value){

				var cns=this.dHiddenParams.childNodes;
				var ipt=null;
				for(var cn in cns){
					if(cn.name=="name"){
						ipt=cn;
						break;
					}
				}

				if(ipt==null){
					ipt=EG.CE({pn:this.dHiddenParams,tn:"input",type:"hidden",name:name,value:value});
				}

				ipt.value=value;
			},

			/**
			 * 移除混合提交的参数
			 */
			removeParam:function(name){
				var cns=this.dHiddenParams.childNodes;
				var ipt=null;
				for(var cn in cns){
					if(cn.name=="name"){
						ipt=cn;
						break;
					}
				}

				if(ipt!=null) EG.DOM.remove(ipt);
			},

			/**
			 * 提交
			 */
			submit:function() {
				if (EG.getValue(this.fileinput) == "") {
					EG.Locker.message( "请选择文件上传");
					return;
				}
				if (!this.checkExt()) {
					EG.Locker.message( "上传类型需为" + this.exts + "的一种");
					return;
				}
				if (this.beforeUpload&&!this.beforeUpload.apply(this))
					return;
				if (this.showWait)
					EG.Locker.wait( "正在上传文件,请稍后");
				this.element.submit();
			},
			/**
			 * 检测扩展类型
			 * @returns {Boolean}
			 */
			checkExt:function() {
				if (!this.exts) return true;
				var ext = this.getExt();
				for ( var i = 0; i < this.exts.length; i++){
					if (this.exts[i].toUpperCase() === ext.toUpperCase()) return true;
				}
				return false;
			},
			/**
			 * 获取扩展名
			 */
			getExt:function() {
				var path = EG.getValue(this.fileinput);
				return (path.substr(path.length - 5)).substr((path.substr(path.length - 5)).indexOf('.') + 1);
			},
			/**
			 * 上传前执行
			 * @param file
			 */
			onUploaded:function(file) {
				if(this.showPath) EG.show(this.dPath);
				//设值
				EG.setValue(this.dPath,file["path"]);

				if (this.showWait) EG.Locker.lock(false);

				if(typeof(this.callback) == "string") {
					if(this.callback == "showImg"){
						if(file["path"] == null) throw new Error("上传的返回值中不带path");
						EG.setValue(this.dPath,file["path"]);
						EG.DOM.removeChilds(this.dPreview);
						EG.CE({pn:this.dPreview,tn:"img",width:"50",height:"30",src:file["path"],style:"cursor:pointer",onclick:function(){window.open(this.src);}});
						//if (this.showWait) EG.Locker.lock(false);;
					}
				}else{
					this.callback.apply(this,arguments);//(arguments);
				}


			},
			/**
			 * 错误时执行
			 * @param error
			 */
			onError:function(error) {
				EG.Locker.message(error["exMessage"]);
			},
			onProcess:function(length,totalLength){
				EG.Locker.message("已上传:"+parseInt(length/1024)+"K"+parseInt((length/totalLength*100))+"%");
			},
			render:function(){
				Item.fit(this);
			},
			destroy:function(){
				EG.UI.GITEMS[this.idx]=null;
			},
			statics:{
				Callback:{
					showImg:"showImg"
				}
			}
		};
	});
})();

(function(){
	/**
	 * EG.ui.Text 文本框
	 */
	EG.define("EG.ui.Text",[
		"EG.ui.Item",
		"EG.ui.FormItem"
	],function(Item,FormItem,ME){
		return {
			alias:"text",
			extend:Item,
			config:{
				length		:null,			//长度
				cls			:"eg_text",		//样式类
				onkeydown	:null,
				onkeyup		:null,
				inputStyle	:null,
				dataType	:null,
				placeholder	:null
			},
			constructor:function(cfg){
				this.callSuper([cfg]);
				//最大长度
				if(this.length!=null) this.input.maxLength=this.length;
			},
			/**
			 * 创建
			 */
			build:function(){
				this.element=EG.CE({tn:"div",cls:this.cls,item:this,style:"position:relative;",cn:[
					this.input		=EG.CE({tn:"input",type:"text",cls:this.cls+"-input",item:this,style:"position:absolute;"+EG.unnull(this.inputStyle,""),placeholder:this.placeholder||" "})
				]});

				if(this.dataType){
					this.dDataType	=EG.CE({pn:this.element,tn:"div",style:"position:absolute;right:0px;bottom:0px;width:10px;height:10px",cls:this.dataType})
				}

				//绑定校验
				FormItem.bindValidate.apply(this,[]);


				if(this.onkeydown){
					EG.Event.bindEvent(this.input,"onkeydown",this.onkeydown);
				}

				if(this.onkeyup){
					EG.Event.bindEvent(this.input,"onkeyup",this.onkeyup);
				}
			},
			/**
			 * 设值
			 * @param value 数值
			 */
			setValue:function(value) {
				EG.setValue(this.input, value);
			},
			/**
			 * 获值
			 */
			getValue:function() {
				var v=EG.getValue(this.input);
				if(this.dataType&&this.dataType=="num"){
					v=parseFloat(v);
				}else if(this.dataType&&this.dataType=="bool"){
					v=(v==='true');
				}

				return v;
			},
			render:function(){
				Item.fit(this);
//			alert(this.height)
				var s=EG.getSize(this.element);
				Item.fit({
					element:this.input,
					pSize:s
				});

				EG.css(this.input,"line-height:"+EG.getSize(this.input).innerHeight+"px");
			}
		};
	});
})();

(function(){
	/**
	 * EG.ui.Textarea 文本区域
	 */
	EG.define("EG.ui.Textarea",[
		"EG.ui.Item",
		"EG.ui.FormItem"
	],function(Item,FormItem,ME){
		return {
			alias:"textarea",
			extend:Item,
			config:{
				length		:null,				//长度
				style		:null,				//样式
				cls			:"eg_textarea",		//样式类
				readLineHeight:22
			},
			constructor:function(cfg){
				this.initItem(cfg);

				//Element
				this.element=EG.CE({tn:"div",cls:this.cls,item:this,cn:[
					this.input=EG.CE({tn:"textarea",cls:this.cls+"-input"})
				]});

				//最大长度
				if(this.length!=null){
					this.input.maxLength=this.length;
				}

				//绑定校验
				FormItem.bindValidate.apply(this,[]);

				EG.css(this.element,this.style);
			},
			/**
			 * 设值
			 * @param value 数值
			 */
			setValue:function(value) {
				EG.setValue(this.input, value);
			},
			/**
			 * 获值
			 */
			getValue:function() {
				return EG.getValue(this.input);
			},
			render:function(){
				Item.fit(this);

				Item.fit({
					element:this.input,
					pSize:EG.getSize(this.element)
				});
			}
		};
	});
})();
(function(){
	/**
	 * EG.ui.Password 密码
	 */
	EG.define("EG.ui.Password",[
		"EG.ui.Item",
		"EG.ui.FormItem"
	],function(Item,FormItem,ME){
		return {
			alias:"password",
			extend:Item,
			config:{
				length		:null,		//长度
				cls			:"eg_password",		//样式类
				inputStyle	:null
			},
			constructor:function(cfg){
				this.initItem(cfg);

				//Element
				this.element=EG.CE({tn:"div",cls:this.cls,cn:[
					this.input=EG.CE({tn:"input",type:"password",cls:this.cls+"-input",item:this,style:EG.unnull(this.inputStyle,"")})
				]});

				//绑定校验
				FormItem.bindValidate.apply(this,[]);

				//最大长度
				if(this.length!=null) this.input.maxLength=this.length;
			},

			/**
			 * 设值
			 * @param value 数值
			 */
			setValue:function(value) {
				EG.setValue(this.input, value);
			},

			/**
			 * 获值
			 */
			getValue:function() {
				return EG.getValue(this.input);
			},

			render:function(){
				Item.fit(this);

				var s=EG.getSize(this.element);
				Item.fit({
					element:this.input,
					pSize:s
				});

				EG.css(this.input,"line-height:"+EG.getSize(this.input).innerHeight+"px");
			}
		};
	});
})();

(function(){
	/**
	 * EG.ui.Label 标签
	 */
	EG.define("EG.ui.Label",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			alias:"label",
			extend:Item,
			config:{
				title		:"",				//文字
				width		:null,				//宽度
				height		:null,				//高度
				style		:null,				//样式
				onclick		:null,
				onclickSrc	:null
			},
			constructor:function(cfg){
				this.initItem(cfg);

				this.element=EG.CE({tn:"div",innerHTML:this.title,item:this});

				//
				if(this.onclick){
					EG.CE({ele:this.element,onclick:this.onclick,onclickSrc:this.onclickSrc});

				}

				Item.setWidth(this.element,this.width);
				Item.setHeight(this.element,this.height);
				EG.css(this.element,this.style);

				if(this.style){
					var mStyle=EG.Style.parse(this.style);
					if(mStyle["line-height"]!=null){
						this.render_lineHeight=mStyle["line-height"];
					}
				}

			},

			/**
			 * 设值
			 * @param value 数值
			 */
			setValue:function(value) {
				if(typeof(value)=="undefined") return;//TODO 其它类型也许考虑此问题
				this.value=value;
				EG.setValue(this.element, value);
			},

			/**
			 * 获值
			 */
			getValue:function() {
				return this.value;
			},

			render:function(){
				Item.fit(this);

				var size=EG.getSize(this.element);

				EG.css(this.element,"line-height:"+(this.render_lineHeight||size.innerHeight)+"px");
			}
		};
	});
})();

(function(){
	/**
	 * EG.ui.Code 代码
	 * 需要ACE支持
	 */
	EG.define("EG.ui.Code",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			alias:"code",
			extend:Item,
			config:{
				mode	:null,
				theme	:"monokai"
			},
			constructor:function(cfg){
				this.callSuper([cfg]);
			},

			/**
			 * 创建
			 */
			build:function(){
				this.callSuper("build");

				ace.require("ace/ext/language_tools");
				this.editor = ace.edit(this.element);
				this.editor.setOptions({
					enableBasicAutocompletion: true,
					enableSnippets: true,
					enableLiveAutocompletion: true
				});
				this.editor.setTheme("ace/theme/"+this.theme);
				if(this.mode){
					this.editor.getSession().setMode("ace/mode/"+this.mode);
				}
			},
			setValue:function(value){
				if(value==null){
					value="";
				}
				this.editor.setValue(value);
			},
			getValue:function(){
				console.log(this.editor.getValue())
				return this.editor.getValue();
			},
			render:function(){
				Item.fit(this);
			}
		};
	});
})();
(function(){
	/**
	 * EG.ui.Select 选择区
	 */
	EG.define("EG.ui.Select",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			alias:"select",
			extend:Item,
			config:{
				onchange	:null,				//数值变化事件
				textvalues	:[],				//文本-值 数组
				cls			:"eg_select",		//样式类
				edit		:false,
				onchangeOnbuild:false,			//在创建的时候就触发
				multi		:false,				//多选模式
				/** @cfg {Number|Boolean?} tabIndex Tab值 */
				tabIndex	:0,
				useEmpty	:true
			},

			constructor:function(cfg){
				this.vs=[];
				this.callSuper([cfg]);
			},

			/**
			 * 创建
			 */
			build:function(){
				var me =this;

				var tvs=EG.clone(this.textvalues);
				this.textvalues=[];

				this.builded=false;

				//onchange事件
				this.onchangeEvents=[];
				if(this.onchange!=null) this.bindOnchange(this.onchange);

				//创建Element
				this.element=EG.CE({tn:"div",cls:this.cls,item:this,tabIndex:this.tabIndex,
					onmouseover	:ME._events.element.onmouseover,
					onmouseout	:ME._events.element.onmouseout,
					onkeyup		:ME._events.element.onkeyup,
					cn:[
						this.input=EG.CE({tn:"div",cls:this.cls+"-input",style:"overflow:hidden",item:this,cn:[
							this.iptText=EG.CE({tn:"input",cls:this.cls+"-iptText",style:"overflow:hidden"}),
							this.dArrow	=EG.CE({tn:"div",cls:this.cls+"-arrow"})
						]
							,onclick:ME._events.input.onclick
						})
						,
						this.dOpts=EG.CE({tn:"div",cls:this.cls+"-opts"})
					]
				});

				//设置编辑状态
				//TODO 待支持用输入选择
				if(!this.edit){
					this.iptText.readOnly=true;
				}

				EG.hide(this.dOpts);

				//设置默认选项
				this.iptText.value="";
				//增加选项
				this.setOptions(tvs);

				this.builded=true;
			},

			//显示选项Div
			showOptions:function(){
				EG.show(this.dOpts);

				//将已选中的值样式进行标注
				var v=this.getValue();
				var opts=this.dOpts.childNodes;
				for(var i= 0,il=opts.length;i<il;i++){
					if(v===opts[i].value){
						EG.setCls(opts[i],["opt","opt-selected"],this.cls);
					}else{
						EG.setCls(opts[i],"opt",this.cls);
					}
				}
			},

			/**
			 * 设值
			 * @param {String} value 数值
			 * @param {Boolean?} chain 是否连锁
			 */
			setValue:function(value,chain){
				if(this.multi){
					this.setValues(value,chain);
					return;
				}

				if(chain==null) chain=true;
				if(value==null){
					return;
				}

				//value="";
				var tv,text;
				for(var i= 0,il=this.textvalues.length;i<il;i++){
					tv=this.textvalues[i];
					if(tv[1]===value){
						text=tv[0];
						break;
					}
				}

				//未找到值
				if(text==null){
					this.iptText.value="";
					return;
				}

				//旧值
				var oV=this.getValue();

				this.iptText.value=text;
				this.iptText.v=value;

				//触发onchange
				if(oV!==value&&chain){

					//刚创建的时候是否触发
					if(!this.builded&&!this.onchangeOnbuild) return;

					//执行OnChange
					this.doOnChange(value,oV);
				}
			},

			refreshValues:function(chain){
				var oV=this.getValue();

				var cns=this.dOpts.childNodes;
				EG.Array.clear(this.vs);
				var text=[];
				for(var i=0;i<cns.length;i++){
					if(cns[i].childNodes[0].checked){
						this.vs.push(cns[i].value);
						text.push(cns[i].childNodes[1].innerHTML);
					}
				}
				this.iptText.value=text.join(",");

				var value=this.getValue();
				//触发onchange
				if(chain){
					//刚创建的时候是否触发
					if(!this.builded&&!this.onchangeOnbuild) return;

					//执行OnChange
					this.doOnChange(value,oV);
				}
			},

			setValues:function(val,chain){
				var oV=this.getValue();

				var cns=this.dOpts.childNodes;
				var text=[];
				EG.Array.clear(this.vs);
				for(var j=0;j<val.length;j++){
					for(var i=0;i<cns.length;i++){
						if(cns[i].value==val[j]){
							var el=cns[i].childNodes[0];
							el.checked=true;
							//样式
							EG.setCls(el,["opt-b","opt-b-select"],this.cls);
							text.push(cns[i].childNodes[1].innerHTML);
						}
					}
					this.vs.push(val[j]);
				}

				this.iptText.value=text.join(",");

				//触发onchange
				if(chain){
					//刚创建的时候是否触发
					if(!this.builded&&!this.onchangeOnbuild) return;

					//执行OnChange
					this.doOnChange(this.vs,oV);
				}
			},

			/**
			 * 执行OnChange
			 * @param {Object?} value
			 * @param {Object?} oldValue
			 */
			doOnChange:function(value,oldValue){
				for(var i=0;i<this.onchangeEvents.length;i++){
					this.onchangeEvents[i].apply(this,[value,oldValue]);
				}
			},

			/**
			 * 获值
			 */
			getValue:function(){
				if(this.multi) return this.vs;
				return this.iptText.v;
			},

			/**
			 * 获取选择的索引
			 * @returns {number}
			 */
			getSelectedIdx:function(){
				var v=this.getValue();
				for(var i=0;i<this.textvalues.length;i++){
					if(this.textvalues[i][1]==v) return i;
				}
				return -1;
			},

			/**
			 * 设置选项
			 * @param idx 索引
			 * @param textvalue 文本值
			 */
			setOption:function(idx,textvalue){
				//设值
				this.textvalues[idx]=textvalue;

				//文本
				//TODO
				EG.CE({ele:this.dOpts.childNodes[idx],value:textvalue[1],innerHTML:textvalue[0]});

				if(this.getSelectedIdx()==idx){
					this.iptText.value	=textvalue[0];
					this.iptText.v		= textvalue[1];
				}
			},

			/**
			 * 设值选项,该操作会清空、重置Option，默认触发onchange
			 * @param {Array} textvalues 选项
			 * @param {Boolean?} fireOnchange 是否触发onchange
			 */
			setOptions:function(textvalues,fireOnchange){
				//清除Option
				this.removeOptions();

				if(this.useEmpty){
					this.addOption(["请选择",""],false);
				}

				//添加Option
				this.addOptions(textvalues,false);

				//触发Onchange
				if(this.multi){
					this.refreshValues(fireOnchange);
				}else{
					if(this.textvalues&&this.textvalues.length>0) this.setValue(this.textvalues[0][1],fireOnchange);
				}

				this.render();
			},

			/**
			 * 删除选项
			 */
			removeOptions:function(){
				EG.Array.clear(this.textvalues);
				EG.DOM.removeChilds(this.dOpts);
				this.setValue("",false);
			},

			/**
			 * 绑定变化时事件
			 *
			 * onchange执行时自动获取到[新选取的值,旧值]
			 *
			 * @param {Function} onchange 变化时事件
			 */
			bindOnchange:function(onchange){
				this.onchangeEvents.push(onchange);
			},

			/**
			 * 添加选项
			 * @param textvalues
			 * @param {Boolean?} fireOnchange
			 */
			addOptions:function(textvalues,fireOnchange){
				if(fireOnchange==null) fireOnchange=true;

				//添加Option
				for(var i=0,il=textvalues.length;i<il;i++){
					this.addOption(textvalues[i],false);
				}

				//触发Onchange
				if(fireOnchange){
					if(this.textvalues&&this.textvalues.length>0) this.setValue(this.textvalues[0][1],true);
				}
			},

			/**
			 * 添加选项
			 * @param textvalue
			 * @param {Boolean?} fireOnchange
			 */
			addOption:function(textvalue,fireOnchange){

				if(fireOnchange==null) fireOnchange=true;

				//TODO
//			var box=new EG.ui.Box({
//				text:"",
//				value:""
//			});
				var d=EG.CE({pn:this.dOpts,tn:"div",cls:this.cls+"-opt",value:textvalue[1],item:this,
					onmouseover	:ME._events.option.onmouseover,
					onmouseout	:ME._events.option.onmouseout,
					onclick		:ME._events.option.onclick
				});

				if(this.multi){
					EG.CE({pn:d,tn:"a",cls:this.cls+"-opt-b "+this.cls+"-opt-b-unselect"});
					//EG.CE({tn:"input",type:"checkbox",style:EG.Style.c.dv,item:this});
				}

				EG.CE({pn:d,tn:"div",innerHTML:textvalue[0],style:EG.Style.c.dv,item:this});

				this.textvalues.push(textvalue);

				//触发Onchange
				if(fireOnchange){
					this.setValue(textvalue[1],true);
				}
			},

			/**
			 * 删除选项
			 */
			removeOption:function(idx,fireOnchange){
				if(fireOnchange==null) fireOnchange=true;

				var selectedIdx=this.getSelectedIdx();

				//删除TVS
				EG.Array.del(this.textvalues,idx);

				//DOM移除
				this.dOpts.removeChild(this.dOpts.childNodes[idx]);


				//已选择时触发新选项的onchange
				if(selectedIdx==idx){

					this.iptText.value="";
					if(this.textvalues.length==0) return;

					var nIdx=Math.min(idx,this.textvalues.length-1);
					var v=this.textvalues[nIdx][1];

					this.setValue(v,fireOnchange);
				}
			},

			/**
			 * 获取Text（根据值）
			 * @param val
			 * @returns {*}
			 */
			getTextByValue:function(val){
				for(var i=0;i<this.textvalues.length;i++){
					if(this.textvalues[i][1]==val) return this.textvalues[i][0];
				}
				return null;
			},

			/**
			 * 获取Text
			 * @param {Boolean?} ignoreEmpty 是否忽略空值
			 */
			getText:function(ignoreEmpty){
				return EG.getValue(this.iptText,{getText:true,ignoreEmpty:ignoreEmpty});
			},

			/**
			 * 获取标题
			 * @param {String} value 数值
			 * @param {Object} data 数据集
			 * @param {Object} cfg 扩展
			 * @return {String}
			 */
			getTitle:function(value,data,cfg){
				for(var i=0,il=this.textvalues.length;i<il;i++){
					if(this.textvalues[i][1]===value) return this.textvalues[i][0];
				}
				return null;
			},

			destroy:function(){

			},
			/**
			 * 渲染
			 */
			render:function(){

				//
				Item.fit(this);

				//this.input
				Item.fit({
					element:this.input,
					pSize:EG.getSize(this.element)
				});

				var size_select=EG.getSize(this.input);

				EG.css(this.dOpts,"width:"+size_select.outerWidth+"px");

				var size_arrow=EG.getSize(this.dArrow);

				EG.css(this.dArrow,"line-height:"+size_select.innerHeight+"px;height:"+size_select.innerHeight+"px");


				var pSize=EG.getSize(this.input);

				//alert("H:"+pSize.innerHeight)


				//alert(EG.toJSON(EG.getSize(this.iptText)));
				//this.iptText
				Item.fit({
					element:this.iptText,
					dSize:{
						width:size_select.innerWidth-size_arrow.outerWidth,
						height:"100%"
					},
					pSize:EG.getSize(this.input)
				});

				EG.css(this.iptText,"line-height:"+EG.getSize(this.iptText).innerHeight+"px;");
			},
			statics:{
				/**
				 * 事件
				 */
				_events:{
					//选项
					option:{
						onmouseover:function(){
							var me=this.item;
							EG.setCls(this,["opt","opt-over"],me.cls);
						},
						onmouseout:function(){
							var me=this.item;
							var v=me.getValue();
							if(this.value==v){
								EG.setCls(this,["opt","opt-selected"],me.cls);
							}else{
								EG.setCls(this,"opt",me.cls);
							}
						},
						onclick:function(e){
							var me=this.item;
							if(!me.multi){
								EG.hide(me.dOpts);
								me.setValue(this.value);
							}else{
								var el=this.childNodes[0];
								el.checked=!el.checked;
								//样式
								EG.setCls(el,["opt-b",el.checked?"opt-b-select":"opt-b-unselect"],me.cls);

								var chain=true;
								//alert(EG.Tools.isPressCtrl(e))
								//使用control键时不触发
								if(EG.Tools.isPressCtrl(e)){
									chain=false;
								}
								me.refreshValues(chain);
							}
							EG.Event.stopPropagation(e);
						}
					},
					//外层Element
					element:{
						onmouseout:function(e){
							var me=this.item;
							if(me.outThread!=null) return;
							me.outThread=setTimeout(function(){
								EG.hide(me.dOpts);
							},10);
							EG.Event.stopPropagation(e);
						}
						,
						onmouseover:function(e){
							var me=this.item;
							if(me.outThread!=null){
								clearTimeout(me.outThread);
								me.outThread=null;
							}
							EG.Event.stopPropagation(e);
						},
						onkeyup:function(e){
							var me=this.item;
							if(EG.Tools.isPressCtrl(e)){
								me.refreshValues(true);
							}
						}
					},
					//输入框
					input:{
						onclick:function(){
							var me=this.item;
							if(EG.Style.isHide(me.dOpts)){
								me.showOptions();
							}else{
								EG.hide(me.dOpts);
							}
						}
					}

				}
			}
		};
	});
})();

(function(){
	/**
	 * EG.ui.SelectArea 多项选择区
	 */
	EG.define("EG.ui.SelectArea",[
		"EG.ui.Item",
		"EG.ui.Button"
	],function(Item,Button,ME){
		return {
			alias:"selectArea",
			extend:Item,
			config:{
				onchange	:null,				//数值变化事件
				textvalues	:[],				//文本-值 数组
				cls			:"eg_selectArea",	//样式类
				edit		:false
			},

			constructor:function(cfg){
				this.callSuper([cfg]);

				if(this.textvalues) this.setTextvalues(this.textvalues);
			},

			/**
			 * 创建
			 */
			build:function(){
				var me=this;
				this.element=EG.CE({tn:"div",cls:this.cls,cn:[
					this.srcSlt=EG.CE({tn:"div",cls:this.cls+"-slts"}),
					this.dMid=EG.CE({tn:"div",cls:this.cls+"-dMid",cn:[
						new Button({text:"添加 &gt;",click:function(){me.move(true)}		,style:"display:block;margin:10px;"}),
						new Button({text:"&lt; 删除",click:function(){me.move()}			,style:"display:block;margin:10px;"})
					],style:"width:80px;"}),
					this.destSlt=EG.CE({tn:"div",cls:this.cls+"-slts"})
				]});

				//禁止选择
				EG.Event.bindUnselect(this.element);
			},

			/**
			 *
			 * @param tvs
			 */
			setTextvalues:function(tvs){
				EG.DOM.removeChilds(this.srcSlt);
				EG.DOM.removeChilds(this.destSlt);

				this.textvalues=tvs;
				this.addOptions(this.srcSlt,this.textvalues);
			},

			/**
			 * 移动
			 * @param {Boolean?} f 是否是正向
			 */
			move:function(f){
				var slt1=this.destSlt;
				var slt2=this.srcSlt;
				if(f){
					slt1=this.srcSlt;
					slt2=this.destSlt;
				}

				var v1=this._getValues(slt1).concat(this._getValues(slt2,true));

				var tvs2=this._getTextvalues(slt2);
				var svs=[],dvs=[];
				for(var i=0;i<this.textvalues.length;i++){
					var tv=this.textvalues[i];
					if(EG.Array.has(v1,tv[1])){
						svs.push(tv);
					}else{
						dvs.push(tv);
					}
				}

				EG.DOM.removeChilds(slt1);
				EG.DOM.removeChilds(slt2);


				this.addOptions(slt2,svs);
				this.addOptions(slt1,dvs);

				//onchange事件
				if(this.onchange){
					this.onchange.apply(this,[v1,this.getValue()])
				}
			},

			/**
			 * 获取选区的值
			 * @param {HTMLElement} ele 选区对象
			 * @param {Boolean?} all 是否全选
			 * @returns {Array}
			 * @private
			 */
			_getValues:function(ele,all){
				var cns=ele.childNodes;
				var vs=[];
				for(var i=0;i<cns.length;i++){
					if(all||cns[i].selected) {
						vs.push(cns[i].value);
					}
				}
				return vs;
			},

			/**
			 * 选择选项
			 * @param {HTMLElement} ele 选项元素
			 * @param {Boolean?} selected 是否选择
			 * @private
			 */
			_selectOpt:function(ele,selected){
				if(selected==null) selected=true;
				ele.selected	=selected;
				ele.className	=selected?this.cls+"-slted":this.cls+"-unslt";
			},

			/**
			 * 获取选项的索引
			 * @param {HTMLElement} ele 选项元素
			 * @returns {number}
			 * @private
			 */
			_getIdx:function(ele){
				var cns=ele.paren.childNodes;
				for(var i=0;i<cns.length;i++){
					if(cns[i]==cn) return i;
				}
				throw new Error("未找到索引");
			},

			/**
			 * 添加选项
			 * @param {HTMLElement} ele 选项区
			 * @param {Array} tvs TextValues
			 */
			addOptions:function(ele,tvs){
				var me=this;
				//alert(tvs)
				for(var i=0;i<tvs.length;i++){
					var tv=tvs[i];
					//alert(tv)
					EG.CE({pn:ele,tn:"div",innerHTML:tv[0],cls:this.cls+"-unslt",value:tv[1],onclick:function(e){
						e=EG.Event.getEvent(e);
						//如果按住shift，最后一次点击的项进行选择
						if(e.shiftKey){
							if(ele.lastIdx!=null){
								var idx=EG.DOM.getIdx(this);
								var sIdx=Math.min(ele.lastIdx,idx),bIdx=Math.max(ele.lastIdx,idx);
								var cns=this.parentNode.childNodes;
								//先取消
								for(var j=0;j<cns.length;j++){
									me._selectOpt(cns[j],false);
								}
								//再选中
								for(var j=sIdx;j<=bIdx;j++){
									me._selectOpt(cns[j],true);
								}
							}else{
								ele.lastIdx=EG.DOM.getIdx(this);
								me._selectOpt(this,!this.selected);
							}
							//me._selectOpt(this.parentNode,idx);
							//先清空选择，再选择当前
						}else{
							if(!e.ctrlKey){
								var cns=this.parentNode.childNodes;
								for(var j=0;j<cns.length;j++){
									me._selectOpt(cns[j],false);
								}
							}
							ele.lastIdx=EG.DOM.getIdx(this);
							me._selectOpt(this,!this.selected);
						}
					}});

				}
			},

			clear:function(){
				EG.DOM.removeChilds(this.destSlt);
				EG.DOM.removeChilds(this.srcSlt);
			},

			clearSourceOptions:function(){
				EG.DOM.removeChilds(this.srcSlt);
			},

			clearSelectedOptions:function(){
				EG.DOM.removeChilds(this.destSlt);
			},

			addSourceOptions:function(tvs){
				this.addOptions(this.srcSlt,tvs);
			},

			/**
			 * 添加选区的Options
			 * @param tvs
			 */
			addSelectedOptions:function(tvs){
				this.addOptions(this.destSlt,tvs);
			},

			/**
			 * 还原
			 */
			reset:function(){
				EG.DOM.removeChilds(this.srcSlt);
				EG.DOM.removeChilds(this.destSlt);
				this.addOptions(this.srcSlt,this.textvalues);
			},

			/**
			 * 设值
			 * @param value 数值
			 */
			setValue:function(value){
				//还原
				this.reset();

				//选中
				var cns=this.srcSlt.childNodes;
				for(var j=0;j<cns.length;j++){
					if(EG.Array.has(value,cns[j].value)){
						this._selectOpt(cns[j],true);
					}
				}
				// move
				this.move(true);
			},

			/**
			 * 获值
			 */
			getValue:function(){
				return this._getValues(this.destSlt,true);
			},

			/**
			 * 获取Text
			 */
			getText:function(){
				var tvs=[];
				var cns=this.destSlt.childNodes;
				for(var j=0;j<cns.length;j++){
					tvs.push(cns[j].innerHTML);
				}
				return tvs;
			},

			/**
			 * 获取TextValues
			 * @param ele
			 * @returns {Array}
			 * @private
			 */
			_getTextvalues:function(ele){
				var tvs=[];
				var cns=ele.childNodes;
				for(var i=0;i<cns.length;i++){
					tvs.push([cns[i].innerHTML,cns[i].value]);
				}
				return tvs;
			},

			getSelectedTextvalues:function(){
				return this._getTextvalues(this.destSlt);
			},

			/**
			 * 渲染
			 */
			render:function(){
				Item.fit(this);
				var pSize=EG.getSize(this.element);
				var wDMid=EG.getSize(this.dMid).outerWidth;
				var iw=(pSize.innerWidth-wDMid)/2;
				Item.fit({
					element:this.dMid,
					dSize:{height:"100%"},
					pSize:pSize
				});

				var sSize=EG.getSize(this.srcSlt);
				Item.fit({
					element:this.srcSlt,
					dSize:{width:iw,height:"100%"},
					pSize:pSize
				});
				Item.fit({
					element:this.destSlt,
					dSize:{width:iw,height:"100%"},
					pSize:pSize
				});
			},
			/**
			 * 获取标题
			 * @param {String} value 数值
			 * @param {Object} data 数据集
			 * @param {Object} cfg 扩展
			 * @return {String}
			 */
			getTitle:function(value,data,cfg){
				if(value==null) value=[];
				var textvalues=this.textvalues;
				var s="";
				for(var i=0,il=textvalues.length;i<il;i++){
					for(var j=0;j<value.length;j++){
						if(textvalues[i][1]==value[j]){
							s+=(s!=""?",":"")+textvalues[i][0];
							break;
						}
					}
				}
				return s;
			}

		};
	});
})();

(function(){
	EG.define("EG.ui.SelectExpand",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			extend:Item,
			config:{
				onchange	:null,					//数值变化事件
				textvalues	:[],					//文本-值数组
				cls			:"eg_selectExpand",		//样式类
				edit		:false,
				onchangeOnbuild:false,
				multi		:false,					//创建时触发
				useEmpty	:false					//多选模式
			},

			constructor:function(cfg){
				this.callSuper([cfg]);
			},

			build:function(){
				var me=this;

				var tvs=EG.clone(this.textvalues);
				this.textvalues=[];

				this.builded=false;

				this.onchangeEvents=[];
				if(this.onchange!=null) this.bindOnchange(this.onchange);

				//
				this.element=EG.CE({tn:"div",cls:this.cls,item:this,cn:[
					this.dOpts=EG.CE({tn:"div",cls:this.cls+"-opts"})
				]});

				this.setOptions(tvs);

				this.builded=true;
			},

			setValue:function(value,chain){

				if(value===null) return;
				if(chain==null) chain=true;

				var oV=this.getValue();

				var cns=this.dOpts.childNodes;
				for(var i=0,il=cns.length;i<il;i++){
					var selected=this.multi?EG.Array.has(value,cns[i].value):value===cns[i].value;
					this.selectOpt(cns[i],selected);
				}

				if(oV!==value&&chain){
					if(!this.builded&&!this.onchangeOnbuild) return;
					this.doOnChange(value,oV);
				}
			},

			refreshValues:function(chain){
				var oV=this.getValue();

				var cns=this.dOpts.childNodes;
				EG.Array.clear(this.vs);
				var text=[];

				for(var i=0;i<cns.length;i++){
					if(cns[i].childNodes[0].checked){
						this.vs.push(cns[i].value);
						text.push(cns[i].childNodes[1].innerHTML);
					}
				}
				this.iptText.value=text.join(",");

				var value=this.getValue();
				//
				if(chain){
					if(!this.builded&&!this.onchangeOnBuild) return;

					//
					this.doOnChange(value,oV);
				}
			},

			selectOpt:function(opt,selected){
				if(selected){
					EG.Style.addCls(opt,this.cls+"-opt-selected");
					opt.selected=true;
				}else{
					opt.selected=false;
					EG.Style.removeCls(opt,this.cls+"-opt-selected");
				}
			},

			doOnChange:function(value,oldValue){
				for(var i=0;i<this.onchangeEvents.length;i++){
					this.onchangeEvents[i].apply(this,[value,oldValue]);
				}
			},

			getValue:function(){
				var vs=[];
				var opts=this.dOpts.childNodes;
				for(var i=0;i<opts.length;i++){
					if(opts[i].selected){
						vs.push(opts[i].value);
					}
				}
				return this.multi?vs:(vs.length>0?vs[0]:null);
			},

			getSelectedIdx:function(){
				var v=this.getValue();
				if(this.multi){
					var idx=[];
					for(var j=0;j<v.length;j++){
						for(var i=0;i<this.textvalues.length;i++){
							if(this.textvalues[i][1]===v[j]){
								idx.push(i);
							}
						}
					}
					return idx;
				}else{
					for(var i=0;i<this.textvalues.length;i++){
						if(this.textvalues[i][1]===v) return i;
					}
					return -1;
				}
			},

			setOption:function(idx,textvalue){
				this.textvalues[idx]=textvalue;

				EG.CE({ele:this.dOpts.childNodes[idx],value:textvalue[1],innerHTML:textvalue[0]});
			},

			setOptions:function(textvalues,fireOnChange){
				this.removeOptions();

				if(this.useEmpty){
					this.addOption(["全部",""],false);
				}

				this.addOptions(textvalues,false);

				if(this.multi){
					this.refreshValues(fireOnChange);
				}else{
					if(this.textvalues&&this.textvalues.length>0) this.setValue(this.textvalues[0][1],fireOnChange);
				}

				this.render();
			},

			removeOptions:function(){
				EG.Array.clear(this.textvalues);
				EG.DOM.removeChilds(this.dOpts);
				this.setValue("",false);
			},

			bindOnchange:function(onchange){
				this.onchangeEvents.push(onchange);
			},

			addOptions:function(textvalues,fireOnChange){
				if(fireOnChange==null) fireOnChange=true;

				for(var i= 0,il=textvalues.length;i<il;i++){
					this.addOption(textvalues[i],false);
				}

				if(fireOnChange){
					if(this.textvalues&&this.textvalues.length>0) this.setValue(this.textvalues[0][1],true);
				}
			},

			addOption:function(textvalue,fireOnchange){

				if(fireOnchange==null) fireOnchange=true;

				var d=EG.CE({pn:this.dOpts,tn:"div",cls:this.cls+"-opt",style:EG.Style.c.dv,value:textvalue[1],innerHTML:textvalue[0],item:this,
					onmouseover	:ME._events.option.onmouseover,
					onmouseout	:ME._events.option.onmouseout,
					onclick		:ME._events.option.onclick
				});

				this.textvalues.push(textvalue);

				if(fireOnchange){
					this.setValue(textvalue[1],true);
				}
			},

			removeOption:function(idx,fireOnchange){
				if(fireOnchange==null) fireOnchange=true;

				var selectedIdx=this.getSelectedIdx();

				EG.Array.del(this.textvalues,idx);

				this.dOpts.removeChild(this.dOpts.childNodes[idx]);

				if(selectedIdx==idx){
					var nIdx=Math.min(idx,this.textvalues.length-1);
					var v =this.textvalues[nIdx][1];
					this.setValue(v,fireOnchange);
				}

			},

			getTextByValue:function(val){
				for(var i=0;i<this.textvalues.length;i++){
					if(this.textvalues[i][1]==val) return this.textvalues[i][0];
				}
				return null;
			},

			getText:function(ignoreEmpty){
				return EG.getValue(this.iptText,{getText:true,ignoreEmpty:ignoreEmpty});

			},

			getTitle:function(value,data,cfg){
				for(var i=0,il=this.textvalues.length;i<il;i++){
					if(this.textvalues[i][1]===value) return this.textvalues[i][0];
				}
				return null;
			},

			destroy:function(){

			},

			render:function(){
				Item.fit(this);

				Item.fit({
					element:this.dOpts,
					pSize:EG.getSize(this.element)
				});

				var s_opts=EG.getSize(this.dOpts);
				var opts=this.dOpts.childNodes;
				for(var i=0;i<opts.length;i++){
					var opt=opts[i];

					Item.fit({
						element:opt,
						pSize:s_opts,
						dSize:{
							height:"100%"
						}
					});

					var os=EG.getSize(opt);
					EG.css(opt,"line-height:"+os.innerHeight+"px");
				}
			},

			statics:{
				_events:{
					option:{
						onmouseover:function(){
							var me=this.item;
							EG.Style.setCls(this,["opt","opt-over"],me.cls);
						},

						onmouseout:function(){
							var me=this.item;
							EG.Style.removeCls(this,me.cls+"-opt-over");
							if(this.selected){
								EG.Style.addCls(this,me.cls+"-opt-selected");
							}
						},
						onclick:function(e){
							var me=this.item;
							var oVal=me.getValue();
							if(!me.multi){
								var cns=me.dOpts.childNodes;
								for(var i=0;i<cns.length;i++){
									me.selectOpt(cns[i],cns[i]==this);
								}
							}else{
								me.selectOpt(this,!this.selected);
							}

							if(!EG.Tools.isPressCtrl(e)){
								me.doOnChange(me.getValue(),oVal);
							}

							EG.Event.stopPropagation(e);
						}
					}
				}
			}
		};
	});
})();
/**
 * @class EG.ui.Grid
 * <AUTHOR>
 * @extends EG.ui.Item
 * 表格组件
 */
(function(){
	EG.define("EG.ui.Grid",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			alias:"grid",
			extend:Item,
			config:{
				cls					:"eg_grid",		//样式类
				rowClasses			:["row-a","row-b"],	//行样式
				changeRowClassAble	:true,			//是否改变行样式
				selectRowOnclick	:true,			//是否在点击时选中
				selectSingleOnclick	:false,			//是否在点击时只单选
				boxAble				:true,			//是否显示checkbox
				seqAble				:true,			//是否显示序号
				colSelectAble		:false,			//是否可以选列
				colOrderAble		:false,			//是否可以排序
				colAdjAble			:false,			//是否可以调整列宽
				gridFixed			:false,			//无内容时是否用&nbsp;填充
				columns				:null,			//列
				rowEvents			:{},			//行事件
				colEvents			:{},			//列事件
				remotingCallback	:null,			//远程请求回调处理
				showHead			:true,			//显示头
				showFoot			:true,			//显示尾
				renderTo			:null,			//被添加到某节点下，并被渲染
				pageSize			:30,
				toolbar				:"first pre stat next last size",//工具栏
				cellInnerStyle		:null,			//表格内部样式
				onOrder				:null			//排序时动作
			},

			/**
			 * 构造函数
			 * @param cfg
			 */
			constructor:function(cfg){
				ME.load();
				this.callSuper([cfg]);
			},

			/**
			 * 初始化组件
			 * @param cfg
			 */
			initItem:function(cfg){
				this.callSuper("initItem",[cfg]);

				this._currentPage	=0;
				this._dataSize		=0;
				this._pageCount		=0;
				this._segment		=-1;
				this.colOptAble		=false;
			},

			_ef_row:{
				changeRow_mouseover:function(e){
					this.grid.overRow(this,true);
					if(this.grid.fn_row_mouseover) this.grid.fn_row_mouseover.apply(this,[e]);
				},
				changeRow_mouseout:function(e){
					this.grid.overRow(this,false);
					if(this.grid.fn_row_mouseout) this.grid.fn_row_mouseout.apply(this,[e]);
				},
				selectRow_click:function(e){
					e=EG.Event.getEvent(e);
					var cns=this.parentNode.childNodes;
					//SHIFT范围选
					if(e.shiftKey&&this.grid.lastIdx!=null){
						var sIdx=Math.min(this.grid.lastIdx,this.index),bIdx=Math.max(this.grid.lastIdx,this.index);
						cns=this.parentNode.childNodes;
						for(var i=0;i<cns.length;i++)	{this.grid.selectRow(cns[i],false);}
						for(var i=sIdx;i<=bIdx;i++)		{this.grid.selectRow(cns[i],true);}
						//CTRL+普通点选
					}else{
						this.grid.lastIdx=this.selected?null:this.index;
						if(!e.ctrlKey){
							for(var i=0;i<cns.length;i++){if(cns[i]!=this) this.grid.selectRow(cns[i],false);}
						}
						this.grid.selectRow(this,!this.selected);
					}
					if(this.grid.clickFn) this.grid.clickFn.apply(this,[e]);
				}
			},

			/**
			 * 创建
			 */
			build:function(){
				//Element
				this.element=EG.CE({tn:"div",cn:[
					this.dMain=EG.CE({tn:"div",style:"width:100%;overflow:hidden;position:relative;"}),
					this.dFoot=EG.CE({tn:"div",cls:this.cls+"-foot"})
				],
					//TODO bindUnselect
					onkeydown:function(){

					},
					//TODO enableSelect
					onkeyup:function(){

					}});

				//禁止选择
				EG.Event.bindUnselect(this.element);



				//创建区域
				this.buildHead();
				this.buildBody();
				this.buildFixBody();
				this.buildFixHead();

				//初始化头部
				this.initHead();
				this.buildToolBar();

				//控制显示头
				if(!this.showHead) EG.hide(this.dHead,this.dFixHead);

				//控制显示尾
				if(!this.showFoot) EG.hide(this.dFoot);

				//行样式变化
				if(this.changeRowClassAble){
					this.fn_row_mouseover	=this.rowEvents["onmouseover"];
					this.fn_row_mouseout	=this.rowEvents["onmouseout"];

					this.rowEvents["onmouseover"]	=(this._ef_row.changeRow_mouseover);
					this.rowEvents["onmouseout"]	=(this._ef_row.changeRow_mouseout);
				}

				//点击选中
				if(this.selectRowOnclick){
					this.clickFn=this.rowEvents["onclick"];
					this.rowEvents["onclick"]=(this._ef_row.selectRow_click);
				}

				//可调整
				if(this.colAdjAble){

					//创建可调整
					this.colAdjRulerL=EG.CE({pn:this.element,tn:"div",cls:this.cls+"-adjRuler"});
					this.colAdjRulerR=EG.CE({pn:this.element,tn:"div",cls:this.cls+"-adjRuler"});

					EG.hide(this.colAdjRulerL,this.colAdjRulerR);

//				EG.bindEvent(this.grid,"mousemove",function(evt){
//					evt = evt || window.event;
//					if(PagingGrid.adjIng){
//						var x=EG.Tools.getMousePos(evt).x;
//						//边界判断
//						/*if(rulerRight.offsetLeft<rulerLeft.offsetLeft+20){
//							x=(parseInt(EG.String.removeEnd(rulerLeft.style.left,"px"))+20)+"px";
//							rulerRight.style.left=x+"px";}*/
//						PagingGrid.colAdjRulerR.style.left=x+"px";
//
//						//计算table宽度,计算所有列的宽度
//						//this._head
//					}
//				});
				}

				//创建选项区
				if(this.colOptAble){
					this.buildColOpt();
				}
			},
			_fn_selectBox:function(){
				this.grid.selectRow(this.row,!this.selected);
			},

			getTr:function(i){
				return this.tbBody.childNodes[i];
			},

			/**
			 * 设置数据
			 */
			setData:function(data){
				//alert(EG.toJSON(data))
				var me=this;
				this.data=data;
				this.boxes=[];

				EG.DOM.removeChilds(this.tbBody);
				EG.DOM.removeChilds(this.tbFixBody);

				var globalRowNumber=0;

				//设置行，列数据
				var i,j,key;
				//行处理
				for(i=0;i<this.pageSize;i++){

					var d=this.data[i];

					//如果数据为空 && 无 空格填满策略 则断开
					if(!d&&!this.gridFixed) break;

					var row		=EG.CE({pn:this.tbBody		,tn:"tr",index:i,grid:this,data:d});
					var rowFix	=EG.CE({pn:this.tbFixBody	,tn:"tr",index:i,grid:this,data:d});

					//绑定行事件
					for(key in me.rowEvents){
						EG.Event.bindEvent(row,key,me.rowEvents[key]);
					}

					//设定行样式,若是多样式会出现间隔性样式
					if(this.rowClasses&&this.rowClasses.length) {
						var rowClassName=this.cls+"-"+this.rowClasses[i%this.pageSize%this.rowClasses.length];
						EG.setCls(row,rowClassName);
						EG.setCls(rowFix,rowClassName);
					}

					//全部里的行标号
					globalRowNumber=this._currentPage*this.pageSize+i;

					//是否带选框
					if(this.boxAble){
						var box=new EG.ui.Box({showText:false,row:row,grid:this,onclick:this._fn_selectBox});
						EG.CE({pn:rowFix,tn:"td",cls:(this.cls+"-fixCol"),style:"width:30px",cn:[
							{tn:"div",cls:this.cls+"-fixBodyCellInner",cn:[box]}
						]});
						row.box=box;
					}

					//是否带序号
					if(this.seqAble){
						EG.CE({pn:rowFix,tn:"td",cls:this.cls+"-fixCol",style:"text-align:center;vertical-align:middle;width:30px",cn:[
							{tn:"div",cls:this.cls+"-fixBodyCellInner",innerHTML:(globalRowNumber+1)}
						]});
					}

					//列处理
					for(j=0;j<this.columns.length;j++){
						var column=this.columns[j];
						var textlength	=column["textlength"],
							textlengthEnd=column["textlengthEnd"]||"",
							handle		=column["handle"],
							field		=column["field"],
							fieldClass	=column["fieldClass"]||"txtcenter",
							width		=column["width"],
							fix			=column["fix"],
							showCode	=EG.n2d(column["showCode"],false);

						var col,cellInner;
						col=EG.CE({tn:"td",cls:this.cls+"-bodyCol",cn:[
							cellInner=EG.CE({tn:"div",cls:this.cls+"-bodyCellInner"})
						]});

						//内样式
						if(this.cellInnerStyle){
							EG.css(cellInner,this.cellInnerStyle);
						}

						//设置样式
						if(fieldClass) 		col.className+=(" "+fieldClass);
						//绑定列事件
						for(key in this.colEvents){
							EG.Event.bindEvent(col,key,this.colEvents[key]);
						}
						//数据处理
						var ihtml;
						if(d){
							ihtml=null;
							//自定义处理器
							if(handle){
								var hr=handle.apply(this,[this.data[i],i,globalRowNumber,column]);
								if(hr==null) hr="";
								if(hr.nodeType){
									cellInner.appendChild(hr);
								}else{
									if(typeof(hr)=="object"&&hr.length!=null){
										for(var x=0;x<hr.length;x++){
											if(EG.ui.Item.isItem(hr[x])){
												cellInner.appendChild(hr[x].getElement());
											}else{
												cellInner.appendChild(hr[x]);
											}
										}
									}else cellInner.innerHTML=showCode?EG.Tools.filterSpecChar(hr):hr;
								}
								//从field取值
							}else if(this.data[i][field]!=null||!isNaN(this.data[i][field])){
								ihtml=this.data[i][field];
								if(ihtml&&textlength!=null&&ihtml.length>textlength) ihtml=ihtml.substr(0,textlength)+textlengthEnd;
								cellInner.innerHTML=showCode?EG.Tools.filterSpecChar(ihtml):ihtml;
							}else{
								cellInner.innerHTML=showCode?EG.Tools.filterSpecChar(field):"";
							}
							//填白
						}else if(this.gridFixed) cellInner.innerHTML="&nbsp;";

						if(fix) {
							rowFix.appendChild(col);
							EG.setCls(col,"fixCol",this.cls);
							EG.setCls(cellInner,"fixBodyCellInner",this.cls);
						}else{
							row.appendChild(col);
						}
						//设定列宽
						if(width){
							EG.css(col,"width:"+(column.outerWidth)+"px");			//TODO 1要变为动态检测Header的Border宽
							EG.css(cellInner,"width:"+(width)+"px");//TODO 需将固定值12变为自动检测bodyCellInner的左右padding和
						}
					}
				}


				this.fitFixSize();
			},

			/**
			 * 创建头
			 */
			buildHead:function(){
				this.dHead=EG.CE({pn:this.dMain,cls:this.cls+"-head",tn:"div",style:"overflow:hidden;",cn:[
					this.tHead=EG.CE({tn:"table",border:0,cellPadding:0,cellSpacing:0,style:"table-layout:fixed;",cn:[
						this.tbHead=EG.CE({tn:"tbody"})
					]})
				]});
			},

			/**
			 * 创建Body
			 */
			buildBody:function(){
				var me=this;
				this.dBody=EG.CE({pn:this.dMain,cls:this.cls+"-body",tn:"div",style:"overflow:auto;",onscroll:function(){
					me.dHead.scrollLeft=this.scrollLeft;
					me.dFixBody.style.top=((-this.scrollTop)+me.dFixHead.clientHeight)+"px";
				},cn:[
					this.tBody=EG.CE({tn:"table",border:0,cellPadding:0,cellSpacing:0,style:"table-layout:fixed;",cn:[
						this.tbBody=EG.CE({tn:"tbody"})
					]})
				]});
			},

			/**
			 * 创建固定头
			 */
			buildFixHead:function(){
				this.dFixHead=EG.CE({pn:this.dMain,cls:this.cls+"-fixHead",tn:"div",style:"position:absolute;left:0px;top:0px",cn:[
					{tn:"table",style:"table-layout:fixed;",border:0,cellPadding:0,cellSpacing:0,cn:[
						this.tbFixHead=EG.CE({tn:"tbody"})
					]}
				]});
			},

			/**
			 * 创建固定Body
			 */
			buildFixBody:function(){
				this.dFixBody=EG.CE({pn:this.dMain,cls:this.cls+"-fixBody",tn:"div",style:"position:absolute;overflow:hidden;left:0px;top:0px",cn:[
					{tn:"table",style:"table-layout:fixed;",border:0,cellPadding:0,cellSpacing:0,cn:[
						this.tbFixBody=EG.CE({tn:"tbody"})
					]}
				]});
			},

			/**
			 * 渲染
			 */
			render:function(){
				//设置总尺寸
				Item.fit(this);

				//计算设置中间高度
				var size			=EG.getSize(this.element);
				var mainHeadsize	=EG.getSize(this.dHead);
				var footHeight		=this.showFoot?EG.getSize(this.dFoot).innerHeight:0;

				var mainHeight		=size.innerHeight-footHeight;
				var mainBodyHeight	=mainHeight-mainHeadsize.outerHeight;

				var pSize			=EG.getSize(this.element.parentNode);
				//alert(this.region+","+this.width+","+this.height+","+pSize.innerHeight+","+size.innerHeight+","+mainHeadsize.outerHeight+","+footSize.outerHeight)
				EG.css(this.dMain,"height:"+mainHeight+"px;width:"+size.innerWidth+"px");
				EG.css(this.dBody,"height:"+mainBodyHeight+"px");

				//计算设置中间宽度
				var tableWidth=0;
				for(var i=0;i<this.columns.length;i++){
					var column		=this.columns[i];
					//根据head计算总宽
					var width		=column["width"];
					EG.css(column.dHeadInner,"width:"+width+"px");
					var w=EG.getSize(column.dHeadInner).outerWidth;
					EG.css(column.tdHead,"width:"+w+"px");
					column.outerWidth=w;
					tableWidth+=w;
				}
				EG.css(this.tHead,"width:"+(tableWidth+ME.appendHeadWidth)+"px");

				//固定部分尺寸
				this.fitFixSize();
			},

			/**
			 * 设置固定部分的尺寸
			 */
			fitFixSize:function(){
				var i,il;
				//如果没有设定列宽,同步fixHead列宽度
				if(this.tbFixBody.childNodes.length>0){
					var fixBodyTr=this.tbFixBody.childNodes[0];
					for(i=0,il=fixBodyTr.childNodes.length;i<il;i++){
						var cSize=EG.getSize(fixBodyTr.childNodes[i]);
						EG.css(this.tbFixHead.childNodes[0].childNodes[i],"width:"+cSize["innerWidth"]+"px");
					}

					//简单实现内容TD高度和fixTD高度的同步
					for(i=0,il=this.tbBody.childNodes.length;i<il;i++){
						if(		this.tbBody		.childNodes[i].childNodes.length==0
							||	this.tbFixBody	.childNodes[i].childNodes.length==0
						) continue;

						var td		=this.tbBody	.childNodes[i].childNodes[0];
						var tdFix	=this.tbFixBody	.childNodes[i].childNodes[0];

						var s	=EG.getSize(td);
						var sFix=EG.getSize(tdFix);

						if(sFix.innerHeight>s.innerHeight){
							EG.css(td	,"height:"+	sFix["innerHeight"]	+"px");
						}else{
							EG.css(tdFix,"height:"+	s["innerHeight"]	+"px");
						}
					}
				}

				//同步dBody和dHead的左边距
				var fixHeadsize=EG.getSize(this.dFixHead);
				EG.css(this.dBody,"margin-left:"+fixHeadsize.outerWidth+"px");
				EG.css(this.dHead,"margin-left:"+fixHeadsize.outerWidth+"px");

				//同步fixBody的Top
				this.dFixBody.style.top=((-this.dBody.scrollTop)+this.dFixHead.clientHeight)+"px";
			},

			/**
			 * 初始化头部
			 */
			initHead:function(){
				var me=this;

				//固定行
				var hrFix	=EG.CE({pn:this.tbFixHead	,tn:"tr",cls:me.cls+"-head"});
				//普通行
				var hr		=EG.CE({pn:this.tbHead		,tn:"tr",cls:me.cls+"-head"});

				//头部全选BOX
				if(this.boxAble){
					EG.CE({pn:hrFix,tn:"td",cls:this.cls+"-headCol",style:"text-align:center;",cn:[
						{tn:"div",cls:this.cls+"-fixHeadCellInner",style:"",cn:[
							this.boxHead=new EG.ui.Box({showText:false,onselect:function(){me.selectAllBox(!this.selected);}})
						]}
					]});
				}

				//头部序号
				if(this.seqAble){
					EG.CE({pn:hrFix,tn:"td",cls:this.cls+"-headCol",style:"text-align:center;",cn:[
						{tn:"div",cls:this.cls+"-fixHeadCellInner",style:"width:12px",innerHTML:"&nbsp;"}
					]});
				}

				//头部列
				for(var i=0;i<this.columns.length;i++){
					var column		=this.columns[i];
					var header		=column["header"],
						headerEvent	=column["headerEvent"],
						headerStyle	=column["headerStyle"],
						width		=column["width"],
						fix 		=column["fix"],
						order 		=column["order"],
						field		=column["field"];

					//列TD
					var col=EG.CE({pn:fix?hrFix:hr,tn:"td",cls:me.cls+"-headCol",style:"white-space:nowrap",me:this});
					col.dContent=EG.CE({pn:col,tn:"div",cls:me.cls+"-headCellInner",me:this});

					//内容
					if(typeof(header)=="string") col.dContent.innerHTML=header;
					else col.dContent.appendChild(header);

					//排序
					if(me.colOrderAble&&order){
						col.orderName=typeof(order)=="boolean"?field:order;
						EG.Event.bindEvent(col,"click",ME._events.head.onclick);
					}

					//可调整区域
					if(this.colAdjAble){
						col.dAdj=EG.CE({pn:col,tn:"div",innerHTML:"&nbsp;",className:me.cls+"-head_adj",
							ondblclick:function(){
								alert("待实现，双击调整宽度");
							},
							onmousedown:function(){
								me.startAdjColWidth(this.parentNode);
							}
						});
					}

					column.tdHead		=col;
					column.dHeadInner	=col.dContent;
				}

				//头行尾部填充列
				EG.CE({pn:hr,tn:"td",cls:this.cls+"-headCol",cn:[
					{tn:"div",cls:this.cls+"-headCellInner",style:"width:"+ME.appendHeadWidth+"px"}
				]});
			},

			/**
			 * 鼠标移动到行上时的动作
			 * @param tr 行
			 * @param over
			 */
			overRow:function(tr,over){
				if(typeof(tr)=="number") tr=this.tbBody.childNodes[tr];
				if(tr.selected) return;

				if(over){
					if(!tr.oldClass) tr.oldClass=tr.className;
					EG.setCls(tr,"row-over",this.cls);
				}else{
					EG.setCls(tr,tr.oldClass);
				}
			},

			/**
			 * 选择行,样式变化
			 * @param tr
			 * @param selected
			 * @param {Boolean?} single
			 */
			selectRow:function(tr,selected,single){
				if(single==null) single=this.selectSingleOnclick;

				if(typeof(tr)=="number") tr=this.tbBody.childNodes[tr];
				tr.selected=selected;
				if(tr.selected){
					if(!tr.oldClass) tr.oldClass=tr.className;
					EG.setCls(tr,"row-selected",this.cls);
				}else{
					EG.setCls(tr,tr.oldClass);
				}
				if(this.boxAble){
					tr.box.select(tr.selected);
				}

				if(single){
					var cns=tr.parentNode.childNodes;
					for(var i=0;i<cns.length;i++){
						if(cns[i]!=tr&&cns[i].selected){
							cns[i].selected=false;
							cns[i].box.select(false);
							EG.setCls(cns[i],cns[i].oldClass);
						}
					}
				}
			},

			/**
			 * 选择所有box
			 * @param selected {Boolean}
			 */
			selectAllBox:function(selected){
				var trs=this.tbBody.childNodes;
				for(var i=0,l=trs.length;i<l;i++){
					this.selectRow(trs[i],selected);
				}
			},

			/**
			 * 获取选中的IDX
			 * @returns {Array}
			 */
			getSelectIdx:function(){
				var sd=[];
				var trs=this.tbBody.childNodes;
				for(var i=0,l=trs.length;i<l;i++){
					if(trs[i].selected){
						sd.push(i);
					}
				}
				return sd;
			},

			/**
			 * 获取选中的数据
			 * @param {String?} key
			 * @returns {Array}
			 */
			getSelectData:function(key){
				var sd=[];
				var trs=this.tbBody.childNodes;
				for(var i=0,l=trs.length;i<l;i++){
					if(trs[i].selected){
						var d=trs[i]["data"];
						sd.push(key?d[key]:d);
					}
				}
				return sd;
			},

			/**
			 * 设置数据大小
			 */
			setDataSize:function(dataSize){
				this._dataSize=dataSize;
				this._pageCount=Math.ceil(this._dataSize/this.pageSize);

				//当前页码检测
				if(this._currentPage+1>this._pageCount){
					this._currentPage=this._pageCount-1;
				}

				if(this._currentPage<0){
					this._currentPage=0;
				}

				//表格变化时发生的动作
				for(var i=0;i<ME.handler.gridChangedAction.length;i++){
					if(this[ME.handler.gridChangedAction[i]]&&this[ME.handler.gridChangedAction[i]]instanceof Function){
						this[ME.handler.gridChangedAction[i]]();
					}
				}
			},

			/**
			 * 找到列，转移列所有数据列和到fixBody和fixHead中
			 * @param colIdx 列索引
			 */
			fixColumn:function(colIdx){
				if(typeof(colIdx)=="string"){//根据headName转化成idx
					colIdx=this.getColumnIdx(colIdx);
				}
				//移动Body
				var cns=this.tbBody.childNodes;
				for(var i=cns.length-1;i>=0;i++){
					EG.CE({pn:this.tbFixBody,tn:"tr",cn:cns[i].childNodes[colIdx]});
				}
				//移动头部
				EG.CE({pn:this.tbFixHead,tn:"tr",cn:this.tbHead.childNodes[0].childNodes[colIdx]});
			},

//		/**
//		 * 解锁列
//		 * @param colIdx 列索引
//		 */
//		unfixColumn:function(colIdx){
//			//TODO 转移
//		},

			/**
			 *
			 * @param header
			 * @return {Number}
			 */
			getColumnIdx:function(header){
				var startIdx=0;
				if(this.boxAble) startIdx++;
				if(this.seqAble) startIdx++;

				for(var i=0;i<this.columns.length;i++){
					if(header==this.columns[i]["header"]) return i+startIdx;
				}
				throw new Error("EG.ui.Grid#getColumnIdx:未找到对应列");
			},



			/**
			 * 获取数据
			 */
			getData:function() {
				return this.data;
			},

			/**
			 * 跳转创建动作,同时远程读取
			 */
			go:function(pageIdx){
				//alert(pageIdx+":"+this._pageCount);
				if(pageIdx>this._pageCount-1) pageIdx=this._pageCount-1;
				if(pageIdx<0) pageIdx=0;

				this._currentPage=pageIdx;

				//远程读取
				if(this.remotingCallback){
					this.remotingCallback.apply(this["remotingCallbackSrc"]||this,[pageIdx,this]);
				}

				if(this.boxAble){
					this.boxHead.select(false,true);
					//this.tbFixHead.childNodes[0].childNodes[0]
				}
			},

			/** 前一页 */
			prePage:function(){this.go(this._currentPage - 1);},
			/** 后一页 */
			nextPage:function(){this.go(this._currentPage+1);},
			/** 首页 */
			firstPage:function(){this.go(0);},
			/** 末页 */
			lastPage:function(){this.go(this._pageCount-1);},
			/** 当前页 */
			curPage:function(){this.go(this._currentPage);},

			/**
			 * 创建列选项
			 */
			buildColOpt:function(){
				this.dColOtp=EG.CE({pn:EG.getBody(),tn:"div",cls:"pagingGrid_dColOpt",style:"display:none",onmouseleave:function(){
					EG.hide(this);
				}});
				var overFn=function(){this.style.backgroundColor="white";};
				var outFn =function(){this.style.backgroundColor="";};
				if(this.colOrderAble){

					EG.CE({pn:this.dColOtp,tn:"div",innerHTML:"正序",cls:"ele",onclick:function(){
						//EG.show(this.dColSelect);
					},onmouseover:overFn,onmouseout:outFn});
					EG.CE({pn:this.dColOtp,tn:"div",innerHTML:"倒序",cls:"ele",onclick:function(){
						//EG.show(this.dColSelect);
					},onmouseover:overFn,onmouseout:outFn});
				}

				if(this.colSelectAble){
					EG.CE({pn:this.dColOtp,tn:"div",grid:this,innerHTML:"列",cls:"ele",onmouseenter:function(){
						var p=EG.Tools.getElementPos(this);
						EG.css(this.grid.dColSelect, {top:p.y+"px",left:(p.x+this.grid.dColOtp.clientWidth-20)+"px",display:""});
					},onmouseover:overFn,onmouseout:outFn});

					this.dColSelect=EG.CE({pn:EG.getBody(),tn:"div",cls:"pagingGrid_dColSelect",style:"display:none",onmouseleave:function(){EG.hide(this);}});

					for(var i=0,l=this.columns.length;i<l;i++){
						var column=this.columns[i];
						var b=new EG.ui.Box({
							title:column["header"],
							onselect:function(){}
						});
						this.dColSelect.appendChild(b.getElement());
					}
				}
			},

			/**
			 * 创建工具栏
			 */
			buildToolBar:function(){
				var tools=this.toolbar.split(/\s+/);
				for(var i=0;i<tools.length;i++){
					var method=ME.handler.toolsMap[tools[i]];
					var tool;
					if(!method) continue;
					tool=eval('this.'+method+'()');
					this.dFoot.appendChild(tool);
				}
			},

			getOptions:function(){
				return EG.CE({tn:"a",href:"javascript:void(0)",innerHTML:"选择",onclick:function(){alert("1");}});//TODO 待实现
			},

			/**
			 * 获取第一页HTML
			 */
			getFirstPage:function(){
				var me=this;
				return EG.CE({tn:"a",href:"javascript:void(0)",cls:this.cls+"-firstPage",onclick:function(){me.firstPage();return false;}});
			},

			/**
			 * 获取第一页HTML
			 */
			getPrePage:function(){
				var me=this;
				return EG.CE({tn:"a",href:"javascript:void(0)",cls:this.cls+"-prePage",onclick:function(){me.prePage();return false;}});
			},

			/**
			 * 获取第一页HTML
			 */
			getNextPage:function(){
				var me=this;
				return EG.CE({tn:"a",href:"javascript:void(0)",cls:this.cls+"-nextPage",onclick:function(){me.nextPage();return false;}});
			},

			/**
			 * 获取第一页HTML
			 */
			getLastPage:function(){
				var me=this;
				return EG.CE({tn:"a",href:"javascript:void(0)",cls:this.cls+"-lastPage",onclick:function(){me.lastPage();return false;}});
			},

			/**
			 * 获取状态栏
			 */
			getState:function(){
				var me=this;
				var span=EG.CE({tn:"span",cls:this.cls+"-state",cn:[
					{tn:"a",cls:this.cls+"-gotoPage",onclick:function(){me.go(parseInt(me.currentPageValue.value)-1);}},
					{tn:"span",innerHTML:"第"},
					this.currentPageValue=EG.CE(
						{tn:"input",type:"text",size:2,style:""}
					),
					{tn:"span",innerHTML:"/"},
					this.pageCountValue=EG.CE({tn:"span",innerHTML:this._pageCount}),
					{tn:"span",innerHTML:"页"}
				],style:""});
				return span;
			},

			/**
			 * 获取RecordSize
			 * @returns {Number}
			 */
			getRecordSize:function(){
				var me=this;
				return EG.CE({tn:"span",cls:this.cls+"-recordSize",cn:[
					{tn:"span",innerHTML:"每页"},
					this.sPageSize=EG.CE({tn:"span",innerHTML:this.pageSize,onclick:function(e){
						e=EG.Event.getEvent(e);
						if(e.ctrlKey){
							var ps=prompt("RS");
							if(ps){
								me.pageSize=parseInt(ps);
								me.go(0);
							}
						}
					}}),
					{tn:"span",innerHTML:"条",style:"margin-right:5px"},
					{tn:"span",innerHTML:"共"},
					this.sizeValue=EG.CE({tn:"span",innerHTML:this._dataSize}),
					{tn:"span",innerHTML:"条"}
				]});
			},

			/**
			 * 刷新状态数字
			 */
			_changeState:function(){
				EG.setValue(this.currentPageValue,this._currentPage+1);
				this.pageCountValue.innerHTML		=this._pageCount;
				this.sizeValue.innerHTML			=this._dataSize;
				this.sPageSize.innerHTML			=this.pageSize;
			},

			/**
			 *
			 */
			dispose:function() {
				for (var p in this){
					if (this[p] instanceof Function){
						this[p] = function() {
							alert("The object has been released.");
						};
					}
					else this[p] = null;
				}
			},

			/**
			 * 开始调整列
			 * @param td
			 */
			startAdjColWidth:function(td){
				ME.adjAim=td;
				var me=td.me;
				//alert(EG.DOM.has(me.getElement(),ME.adjAim));

				//alert(EG.toJSON(EG.Tools.getElementPos(ME.adjAim)))

				var p=EG.Tools.getElementPos(ME.adjAim,me.getElement(),false);
				var h=(me.dHead.clientHeight+me.dBody.clientHeight)+"px";

				EG.css(me.colAdjRulerL, {
					top		:p.y+"px",
					left	:p.x+"px",
					height	:h
				});
				EG.css(me.colAdjRulerR, {
					top		:p.y+"px",
					left	:(p.x+td.offsetWidth)+"px",
					height	:h
				});

				EG.show(me.colAdjRulerL,me.colAdjRulerR);

				ME.adjIng=true;
			},

			/**
			 * 结束调整列
			 */
			endAdjColWidth:function(){
				EG.hide(ME.colAdjRulerL,ME.colAdjRulerR);
				ME.adjAim.style.width=parseInt(EG.String.removeEnd(ME.colAdjRulerR.style.left,"px"))-parseInt(EG.String.removeEnd(ME.colAdjRulerL.style.left,"px"))+"px";
				ME.adjIng=false;
			},

			statics:{
				/**
				 * 事件
				 */
				_events:{
					head:{
						onclick:function(){
							var me=this.me;
							//清空之前的选项
							if(me.curOrderCol&&me.curOrderCol!=this){
								EG.Style.removeCls(me.curOrderCol.dContent,me.cls+"-head_order_asc");
								EG.Style.removeCls(me.curOrderCol.dContent,me.cls+"-head_order_desc");
								me.curOrderDesc="";
								me.curOrderName="";
								me.curOrderCol=null;
							}

							me.curOrderCol=this;
							me.curOrderName=this.orderName;
							if(me.curOrderDesc=="desc"){
								me.curOrderDesc="asc";
							}else if(me.curOrderDesc=="asc"){
								me.curOrderDesc="";
								me.curOrderName="";
								me.curOrderCol=null;
							}else{
								me.curOrderDesc="desc";
							}


							//变化样式
							EG.Style.removeCls(this.dContent,me.cls+"-head_order_asc");
							EG.Style.removeCls(this.dContent,me.cls+"-head_order_desc");
							if(me.curOrderDesc){
								EG.Style.addCls(this.dContent,me.cls+"-head_order_"+me.curOrderDesc);
							}

							//执行Order
							if(me.onOrder) me.onOrder.apply(me["onOrderSrc"]||me,[this.orderName,me.curOrderDesc]);
						}
					}
				},
				loaded:false,
				/**
				 * 首次加载
				 */
					load:function(){
					if(ME.loaded) return;

//
//				EG.doc.onmouseup=function(){
//					//if(EG.doc.onmouseup) EG.doc.onmouseup();//TODO 冲突解决
//					if(ME.adjIng) ME.endAdjColWidth();
//				};
					ME.loaded=true;
				},
				/**
				 * 处理器
				 */
				handler:{
					grids:[],
						count:0,
						toolsMap:{
						"option"	:"getOptions",
							"pre"		:"getPrePage",
							"next"		:"getNextPage",
							"first"		:"getFirstPage",
							"last"		:"getLastPage",
							"stat"		:"getState",
							"size"		:"getRecordSize",
							"skip"		:"getSkip"
					},
					gridChangedAction:["_changeState"]
				},
				adjAim:null,
				adjIng:false,
				appendHeadWidth:1000
			}
		}
	});
})();

/**
 * 输入型表格
 *
 * {
	 * 	columns:[
	 * 		{name:"a",xtype:"text",width:100,height:20},
	 *		{name:"b",xtype:"box",width:100,height:20}
	 * 	]
	 *
	 * }
 *
 */
EG.define("EG.ui.InputGrid",[
	"EG.ui.Item"
],function(Item,ME){
	return {
		extend		:Item,
		alias		:"inputGrid",
		config		:{
			cls			 		:"eg_inputgrid",
			columnsConfig		:null,
			afterRowDelete		:null,
			afterRowAdd			:null,
			seqAble				:true,
			addAble				:true,
			/** @cfg {Number} readLineHeight 读取模式行高 */
			readLineHeight		:22,
			onClickAdd			:null,
			onClickDel			:null,
			seq_ig_name			:null,
			/** @cfg {Function?} onvalidate 校验 */
			onvalidate			:null
		},

		/**
		 * 构造函数
		 * @param cfg
		 */
		constructor	:function(cfg){
			//BD:在读取模式时也setValue
			this.setPropValueOnRead=true;

			this.callSuper([cfg])
		},

		/**
		 * 创建
		 */
		build:function(){
			//

			if(this.seqAble){
				EG.Array.insert(this.columnsConfig,0,{
					ig_name:(this.seq_ig_name||"idx"),ig_title:"序号"		,xtype:"label"	,width:100,height:30
				});
			}

			this.element=EG.CE({tn:"div"});


			//添加&删除
			if(this.addAble){
				this.element.appendChild(
					this.dBar	=EG.CE({tn:"div",cn:[
						new EG.ui.Button({text:"添加",click:this.onClickAdd||this.addRow		,clickSrc:this}),
						new EG.ui.Button({text:"删除",click:this.onClickDel||this.deleteRow	,clickSrc:this})
					]})
				);
			}

			//
			this.element.appendChild(
				this.dMain	=EG.CE({tn:"div",cls:this.cls+"-main",cn:[
					this.table	=EG.CE({tn:"table",cn:[
						this.thead=EG.CE({tn:"thead",cn:[{tn:"tr",cls:this.cls+"-head"}]}),
						this.tbody=EG.CE({tn:"tbody"}),
						this.tfoot=EG.CE({tn:"tfoot"})
					]})
				]})
			);

			this.buildHead();
		},

		/**
		 * 设置表头
		 */
		buildHead:function(){
			var pn=this.thead.childNodes[0];

			for(var i=0;i<this.columnsConfig.length;i++){
				var config=this.columnsConfig[i];
				var hidden=!!config.hidden;
				EG.CE({pn:pn,tn:"td",innerHTML:hidden?"":config["ig_title"],style:"width:"+config["width"]+"px"});
			}
		},

		/**
		 * 添加行
		 * @returns {*}
		 */
		addRow:function(){
			var me=this;
			var cls=this.cls+"-row";
			var selectedCls=this.cls+"-rowSelected";
			var tr=EG.CE({pn:this.tbody,tn:"tr",cls:cls,onclick:function(){
				if(me.selectedRow!=null){
					EG.Style.removeCls(me.selectedRow,selectedCls);
				}
				me.selectedRow=this;
				EG.Style.addCls(this,selectedCls);
			}});

			for(var i=0;i<this.columnsConfig.length;i++){
				var config=this.columnsConfig[i];
				var typeCfg=config["typeCfg"]||config;
				var hidden=!!typeCfg.hidden;

				var item=config.xtype instanceof Function?new config.xtype(typeCfg):Item.create(config.xtype,typeCfg);
				var td	=EG.CE({pn:tr,tn:"td",cn:[item]});
				if(hidden){
					EG.css(td,"overflow:hidden");
				}

				td.item	=item;
				td.ig_name=config["ig_name"];
				item.hidden=hidden;
				item.pItem=this;
				item.width=config["width"];
				item.height=config["height"];
				item.render();
				EG.css(item,"position:relative");
			}

			if(this.afterRowAdd){
				this.afterRowAdd.apply(this,[tr,this]);
			}

			if(this.seqAble){
				this.refreshSeq();
			}

			return tr;
		},

		getCellPos:function(item){
			var trs=this.tbody.childNodes;
			for(var i=0;i<trs.length;i++){
				var tds=trs[i].childNodes;
				for(var j=0;j<tds.length;j++){
					var td=tds[j];
					if(td.item==item){
						return [i,j];
					}
				}
			}
			return null;
		},

		refreshSeq:function(){
			var trs=this.tbody.childNodes;
			for(var i=0;i<trs.length;i++){
				var td=trs[i].childNodes[0];
				td.item.setValue(i+1);
			}
		},

		/**
		 * 删除行
		 */
		deleteRow:function(){
			if(!this.selectedRow) return;

			EG.DOM.remove(this.selectedRow);

			//
			if(this.afterRowDelete){
				this.afterRowDelete.apply(this,[this.selectedRow,this]);
			}

			if(this.seqAble){
				this.refreshSeq();
			}

			this.selectedRow=null;
		},

		/**
		 * 设值
		 * @param v
		 * @param d
		 */
		setValue:function(v,d){
			if(v==null) return;
			//
			EG.DOM.removeChilds(this.tbody);

			var value=v;
			for(var i=0;i<value.length;i++){
				var data_row=value[i];
				var tr=this.addRow();
				var tds=tr.childNodes;
				for(var j=0;j<tds.length;j++){
					var item=tds[j].item;
					item.setValue(data_row[tds[j].ig_name],data_row);
				}
			}
		},

		/**
		 * 设置行数据
		 * @param rowIdx
		 * @param value
		 */
		setRowValue:function(rowIdx,value){
			var tr;
			if(typeof(rowIdx)!="number"){
				tr=rowIdx.parentNode.parentNode;
				rowIdx=EG.DOM.getIdx(tr);
			}

			tr=this.tbody.childNodes[rowIdx];
			var tds=tr.childNodes;
			var o={};
			for(var j=0;j<tds.length;j++){
				var item=tds[j].item;
				item.setValue(value[tds[j].ig_name]);
			}
		},

		/**
		 * 获取行数据
		 * @param rowIdx
		 * @returns {{}}
		 */
		getRowValue:function(rowIdx){
			var tr;
			if(typeof(rowIdx)!="number"){
				tr=rowIdx.parentNode.parentNode;
				rowIdx=EG.DOM.getIdx(tr);
			}

			tr=this.tbody.childNodes[rowIdx];
			var tds=tr.childNodes;
			var o={};
			for(var j=0;j<tds.length;j++){
				var item=tds[j].item;
				o[tds[j].ig_name]=item.getValue();
			}
			return o;
		},

		/**
		 * 取值
		 */
		getValue:function(){
			var trs=this.tbody.childNodes;
			var d=[];
			for(var i=0;i<trs.length;i++){
				var tds=trs[i].childNodes;
				var o={};
				for(var j=0;j<tds.length;j++){
					var item=tds[j].item;
					o[tds[j].ig_name]=item.getValue();
				}
				d.push(o);
			}
			return d;
		},

		/**
		 * 删除所有
		 */
		removeAll:function(){
			EG.DOM.removeAllRows(this.tbody);
		},

		/**
		 * 获取表格对象
		 * @param rowIdx
		 * @param colIdx
		 * @returns {*}
		 */
		getColumnItem:function(rowIdx,colIdx){
			if(typeof(rowIdx)!="number"){
				var tr=rowIdx.parentNode.parentNode;
				rowIdx=EG.DOM.getIdx(tr);
			}

			if(typeof(colIdx)=="string"){
				for(var i=0;i<this.columnsConfig.length;i++){
					var cc=this.columnsConfig[i];
					if(cc["ig_name"]==colIdx){
						colIdx=i;
						break;
					}
				}

				if(typeof(colIdx)=="string") throw new Error("未找到对应列:"+colIdx);
			}

			return this.tbody.childNodes[rowIdx].childNodes[colIdx].item;

		},

		/**
		 * 渲染
		 */
		render:function(){
			//渲染尺寸
			Item.fit(this);
			var size			=EG.getSize(this.element);
			var barHeadsize		=this.addAble?EG.getSize(this.dBar):{outerHeight:0};
			var mainHeight		=size.innerHeight-barHeadsize.outerHeight;
			EG.css(this.dMain,"height:"+mainHeight+"px");

			var trs=this.tbody.childNodes;
			for(var i=0;i<trs.length;i++){
				var tds=trs[i].childNodes;
				for(var j=0;j<tds.length;j++){
					var item=tds[j].item;
					item.render();
					EG.css(item,"position:relative");
				}
			}
		},

		getTitle:function(value,data){
			var config,j;

			if(!value) return "";
			var columnsConfig=this.columnsConfig;

			var table=EG.CE({tn:"table",cls:"eg_inputgrid-read"});
			var thead=EG.CE({pn:table,tn:"thead"});
			var tbody=EG.CE({pn:table,tn:"tbody"});

			var trHead=EG.CE({pn:thead,tn:"tr"});


			for(j=0;j<columnsConfig.length;j++){
				config=columnsConfig[j];
				EG.CE({pn:trHead,tn:"td",width:config["width"],innerHTML:config["ig_title"]});
			}

			for(var i=0;i<value.length;i++){
				var v=value[i];
				var tr=EG.CE({pn:tbody,tn:"tr"});
				for(j=0;j<columnsConfig.length;j++){
					config=columnsConfig[j];
					var type=config["xtype"]||config["type"];
					var item=this.tbody.childNodes[i].childNodes[j].item;
					var r=(item&&item.getTitle)?item.getTitle(v[config["ig_name"]],v):v[config["ig_name"]];
					if(r==null) r="";

					if(this.seqAble){
						if(j==0){
							r=(i+1);
						}
					}

					if(EG.DOM.isElement(r)){
						EG.CE({pn:tr,tn:"td",cn:[r]});
					}else{
						EG.CE({pn:tr,tn:"td",innerHTML:r});
					}
				}
			}

			return table;
		},

		/**
		 * 校验
		 */
		validate:function(){
			var vs=this.getValue();
			if(this.unnull){
				if(!vs||vs.length==0){
					return "不能为空";
				}
			}

			if(vs){
				for(var i=0;i<vs.length;i++){
					var v=vs[i];
					for(var j=0;j<this.columnsConfig.length;j++){
						var cc=this.columnsConfig[j];
						if(cc.unnull){
							var ccv=v[cc["ig_name"]];
							if(ccv!==0&&!ccv){
								return cc["ig_title"]+"不能为空";
							}
						}
					}
				}
			}


			if(this.onvalidate){
				var msg=this.onvalidate(vs);
				if(msg){
					return msg;
				}
			}
		}

	};
});
/**
 * @class EG.ui.Form
 * <AUTHOR>
 * @extends EG.ui.Container
 * Form表单
 */
(function(){
	/**
	 * EG.ui.Form 表单
	 */
	EG.define("EG.ui.Form",[
		"EG.ui.Container"
	],function(Container,ME){

		return {
			alias:"form",
			extend:Container,
			config:{
				/** @cfg {?Boolean} editable 是否可编辑 */
				editable:true,
				/** @cfg {?Number} labelWidth Lab宽度 */
				labelWidth:40,
				/** @cfg {?Boolean} validateAble 是否开启校验 */
				validateAble:true,
				/** @cfg {?String|?Object} layout 布局 */
				layout:"table",
				/** @cfg {Boolean?} realForm 是否为真Form */
				realForm:false,
				/** @cfg {String?} action 动作地址 */
				action:null,
				/** @cfg {Boolean?} isUpload 是否使用Upload */
				isUpload:false,
				/** @cfg {String?} target 提交的目标 */
				target:null,
				/** @cfg {Boolean?} useTopCls 启用顶部样式  */
				useTopCls:false
			},

			/**
			 * 构建
			 */
			build:function(){
				if(this.isUpload)  this.realForm=true;

				this.element=EG.CE({tn:this.realForm?"form":"div"});

				if(this.isUpload){
					this.element.encoding="multipart/form-data";
				}

				if(this.realForm) this.element.method="POST";
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				//过滤配置
				cfg=ME.filterConfig(cfg,cfg);

				this.callSuper([cfg]);
			},

			/**
			 * 获取下级及深层的FormItem元素
			 * @param {String} name formItem名称
			 * @returns {*}
			 */
			getFormItem:function(name){
				return EG.ui.Form.getFormItem(this,name);
			},

			/**
			 * 获取所有的FormItem
			 * @return {Array<EG.ui.FormItem>}
			 */
			getFormItems:function(){
				return EG.ui.Form.getFormItems(this);
			},

			/**
			 * 显示表单元素
			 * @param names
			 * @param display
			 * @private
			 */
			_displayFormItems:function(names,display){
				for(var i=0;i<names.length;i++){
					var fi=this.getFormItem(names[i]);
					if(fi==null) continue;
					fi.setHidden(!display);
				}
				this.render();
			},

			/**
			 * 显示表单元素
			 */
			showFormItems:function(){
				this._displayFormItems(arguments,true);
			},

			/**
			 * 隐藏表单元素
			 */
			hideFormItems:function(){
				this._displayFormItems(arguments,false);
			},


			/**
			 * 获取数据
			 * @return {Object}
			 */
			getData:function(){
				var data={};
				var fis=this.getFormItems();
				for(var i=0, il=fis.length; i<il; i++){
					var item=fis[i];
					var prop=item.getProp();
					if(prop&&prop.onGetData){
						prop.onGetData(data,item.name,this);
					}else{
						data[item.name]=item.getValue();
					}

				}
				return data;
			},

			/**
			 * 设置数据
			 * @param {Object} data 数据
			 * @param {Object?} ext 扩展
			 */
			setData:function(data,ext){
				if(data==null) throw new Error("EG.ui.Form.prototype#setData:data不能为null");
				var fis=this.getFormItems();

				if(!ext){
					ext={};
				}

				//设值时同时定位默认值
				var setDefVal		=EG.n2d(ext["setDefVal"]		,false);
				var ignoreUnExist	=EG.n2d(ext["ignoreUnExist"],false);

				for(var i=0, il=fis.length; i<il; i++){
					var fi=fis[i];

					//数值不存在时不设值
					if(ignoreUnExist&&!EG.Object.hasKey(data,fi.name)){
						continue;
					}

					fi.setValue(data[fi.name],data);

					//设值时同设为默认值
					if(setDefVal){
						fi.defValue=data[fi.name];
					}
				}
			},

			/**
			 * 提交
			 */
			submit:function(){
				//遍历所有的formItems
				var fis=this.getFormItems();
				for(var i=0;i<fis.length;i++){
					var fi=fis[i];
					fi.setSubmitValue(fi.getValue());
				}

				this.element.action=this.action;
				if(this.target) this.element.target=this.target;
				this.element.submit();
			},

			/**
			 * 重置
			 */
			reset:function(){
				var fis=this.getFormItems();
				for(var i=0, il=fis.length; i<il; i++){
					var fi=fis[i];
					if(fi.defValue!=null){
						fi.setValue(fi.defValue);
					}else{
						fi.setValue(null);
					}
				}
			},

			/**
			 * 清空数据
			 */
			clearData:function(){
				var fis=this.getFormItems();
				for(var i=0, il=fis.length; i<il; i++){
					var fi=fis[i];
					fi.setValue("",{});
				}
			},

			/**
			 * 校验Form表单
			 * 如果Item的cfg中含有validate属性并且处于编辑状态,进行校验
			 * @param {?Boolean} full 全字段校验
			 * @return {Boolean}
			 */
			validate:function(full){
				var v=true;
				var fis=this.getFormItems();
				var tabPanel=null;
				var tabIdx	=null;
				var erorrFi	=null;
				for(var i=0, il=fis.length; i<il; i++){
					var fi=fis[i];
					if(fi.hidden) continue;
					if(fi.editable){
						var rv=fi.validate();
						v=(rv&&v);//小心短路不执行

						if(!v){
							if(!erorrFi){
								erorrFi=fi;
							}

							//自动切换到错误的所在的TabPanel
							var pItem=fi;
							var last=null;
							while(tabPanel==null&&(pItem=pItem.pItem)!=null){
								if(pItem==this){
									break;
								}

								//自动切换Tab
								if(pItem.xtype=="tabPanel"){
									tabPanel=pItem;
									tabIdx	=pItem.getPanelIdx(last);
									erorrFi=fi;
									break;
								}
								last=pItem;
							}

							if(!full){
								break;
							}
						}
					}
				}

				if(tabPanel!=null){
					tabPanel.select(tabIdx);
				}

				//提示TIP
				if(erorrFi){
					EG.Tip.error(erorrFi.vErrorMsg,erorrFi.rDiv);
				}


				return v;
			},

			/**
			 * 设置是否可编辑
			 * @param {?Boolean} editable 是否可编辑
			 */
			setEditable:function(editable){
				var fis=this.getFormItems();
				for(var i=0, il=fis.length; i<il; i++){
					var fi=fis[i];
					fi.setEditable(editable);
				}
			},
			statics:{

				/**
				 * 获取容器下层的FormItem
				 * @param {EG.ui.Container} container 容器
				 * @param {String} name 名称
				 * @return {*}
				 */
				getFormItem:function(container,name){
					for(var i=0, il=container.items.length; i<il; i++){
						var item=container.items[i];
						if(item instanceof EG.ui.FormItem){
							if(item.name==name){
								return item;
							}
						}else if(item.isContainer){
							var fi=EG.ui.Form.getFormItem(item,name);
							if(fi){
								return fi;
							}
						}
					}
					return null;
				},

				/**
				 * 获取容器下层及深层的FormItem
				 * @param {EG.ui.Container} container 容器
				 * @param {Array} formItems 缓存数组
				 * @return {*}
				 */
				getFormItems:function(container,formItems){
					if(!formItems) formItems=[];
					for(var i=0, il=container.items.length; i<il; i++){
						var item=container.items[i];
						if(item instanceof EG.ui.FormItem){
							formItems.push(item);
						}else if(item.isContainer){
							EG.ui.Form.getFormItems(item,formItems);
						}
					}
					return formItems;
				},

				/**
				 * 过滤配置数据
				 *
				 * 可以设置未定义xtype的值为formItem
				 *
				 * @param {Object} cfg 配置
				 * @param {Object?} topConfig 外层扩展
				 * @return {Object}
				 */
				filterConfig:function(cfg,topConfig){

					var topLabelWidth	=topConfig["labelWidth"];
					var topCls			=topConfig["cls"];
					var topItemHeight	=topConfig["itemHeight"];

					var isTabPanel=cfg["xtype"]=="tabPanel";
					var items=cfg["items"];
					if(!items) return cfg;


					for(var i=0; i<items.length; i++){
						var item=items[i];

						if(!EG.isLit(item)) continue;
						if(isTabPanel){
							item["panel"]["xtype"]="panel";
							this.filterConfig(item["panel"],topConfig);
						}else{
							if(!item["xtype"]) item["xtype"]="formItem";



							if(item["xtype"]=="formItem"){

								if(item["labelWidth"]==null&&topLabelWidth!=null){
									item["labelWidth"]=topLabelWidth;
								}

								if(!item.cls&&topCls&&topConfig.useTopCls){
									item.cls=topCls+"-item";
								}

								if(item["height"]==null&&topItemHeight!=null){
									item["height"]=topItemHeight;
								}
								//
							}else{
								ME.filterConfig(item,topConfig);
							}
						}
					}
					return cfg;
				}
			}
		};
	});
})();

/**
 * @class EG.ui.Fieldset
 * <AUTHOR>
 * @extends EG.ui.Container
 * Fieldset控件组
 */
(function(){
	EG.define("EG.ui.Fieldset",[
		"EG.ui.Container",
		"EG.ui.Item"
	],function(Container,Item,ME){
		return {
			alias:"fieldset",
			extend:Container,
			config:{
				/** @cfg {String?} CSS样式类 */
				cls			:"eg_fieldset",
				/** @cfg {Boolean?} 显示标题 */
				showTitle	:true,
				/** @cfg {Boolean?} 显示扩展 */
				showExpand	:true,
				/** @cfg {Boolean?} 显示边框*/
				showBorder	:true,
				/** @cfg {String?|Object?} 布局 */
				layout		:"table",
				/** @cfg {String?|Number?} Body间隔 */
				bodyPadding	:null,
				/** @cfg {String?} 标题 */
				title		:null,
				/** @cfg {String?} Body样式 */
				bodyStyle	:null
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				var me=this;
				this.callSuper([cfg]);

				if(!this.showTitle) 	EG.Style.isHide(this.dTitle);
				if(!this.showExpand) 	EG.Style.isHide(this.dCollapse);
				if(!this.showBorder){
					EG.setCls(this.dBody,"dBody-noborder",me.cls);
				}

				if(this.bodyPadding!=null){
					EG.css(this.dBody,"padding:"+this.bodyPadding+"px");
				}

			},

			/**
			 * 构建
			 */
			build:function(){
				this.element=EG.CE({tn:"fieldset",cls:this.cls,cn:[
					this.legend=EG.CE({tn:"legend",cls:this.cls+"-legend",cn:[
						this.dCollapse=EG.CE({tn:"div",cls:this.cls+"-dCollapse "+this.cls+"-dCollapse-show",onclick:function(){
							var d=EG.Style.isHide(me.dBody);
							var cls=me.cls+"-dCollapse "+me.cls+"-dCollapse-";
							if(d){
								EG.show(me.dBody.getElement());
								EG.setCls(this,"hide",me.cls);
							}else{
								EG.hide(me.dBody.getElement());
								EG.setCls(this,"show",me.cls);
							}
						}}),
						this.dTitle	=EG.CE({tn:"div",cls:this.cls+"-dTitle",innerHTML:this.title})
					]}),
					this.dBody=EG.CE({tn:"div",cls:this.cls+"-dBody"})
				]});

				//设置Body样式
				if(this.bodyStyle)	EG.css(this.dBody,this.bodyStyle);
			},

			/**
			 * @override {@link EG.ui.Container.getItemContainer}
			 */
			getItemContainer:function(){
				return this.dBody;
			},

			/**
			 * @override {@link EG.ui.Container#setInnerHeight}
			 */
			setInnerHeight:function(h){
				Item.pack({
					pEle:this.dBody,
					height:h
				});

				Item.pack({
					pEle:this.element,
					height:EG.getSize(this.dBody).outerHeight+EG.getSize(this.legend).outerHeight
				});
			},
			/**
			 * @see EG.ui.Item.render
			 */
			render:function(){
				//外层
				Item.fit(this);

				var s=EG.getSize(this.element);

				//pBody
				Item.fit({
					element:this.dBody,
					pSize:s,
					dSize:{width:"100%",height:s.innerHeight-EG.getSize(this.legend).outerHeight}
				});

				if(this.items.length>0){
					//执行布局
					this.doLayout();
				}
				//pBody内部render
			}
		};
	});
})();

/**
 * @class EG.ui.FormItem
 * <AUTHOR>
 * @extends EG.ui.Item
 * 表单元素
 */
(function(){
	/******************************************************************************************************************
	 *
	 *  EG.ui.form.FormItem
	 *
	 *******************************************************************************************************************/
	EG.define("EG.ui.FormItem",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			extend:Item,
			alias:"formItem",
			config:{
				/** @cfg {Array?} pos 坐标 */
				pos:null,
				/** @cfg {Boolean?} editable 是否可编辑 */
				editable:true,
				/** @cfg {String} name 名称 */
				name:null,
				/** @cfg {String} title 标题 */
				title:null,			//
				/** @cfg {String|Class} type 类型名 */
				type:null,			//
				/** @cfg {Class} typeClass 类型类 */
				typeClass:null,			//类型类
				/** @cfg {String} pre 前置信息 */
				pre:null,
				/** @cfg {String} after 后置信息 */
				after:null,
				/** @cfg {String?} cls CSS样式类 */
				cls:"eg_form-item",
				/** @cfg {Number?} labelWidth 标题宽度 */
				labelWidth:60,
				/** @cfg {String|Number} width 宽度 */
				width:"100%",
				/** @cfg {Number?} height 高度 */
				height:28,
				/** @cfg {String?} validateConfig 校验类型 */
				validateConfig:null,
				/** @cfg {String?} unnull 能否为空 */
				unnull:false,
				/** @cfg {Object?} unnull 默认值 */
				defValue:null,
				/** @cfg {Object?} unnull 默认值 */
				typeCfg:null,
				/** @cfg {Boolean?} showLeft 是否显示左侧 */
				showLeft:true,
				/** @cfg {Number?} readLineHeight read模式行高度 */
				readLineHeight:22,
				/** @cfg {String?} readStyle 只读样式 */
				readStyle:null,
				/** @cfg {Boolean?} showCode 显示代码 */
				showCode :false,

				labelStyle:null,
				/** @cfg {Function?} handle 数据处理 */
				handle:null,
				/** @cfg {Function?} readClick 读取模式下点击 */
				readClick:null,
				/** @cfg {Function?} setRead 设置读取 */
				setRead:null,
				/** @cfg {Object?} vldType 校验类型 */
				vldType:null
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.cacheValue	=null;														//缓存值
				this.prop		=null;
				this.cfg		=cfg;

				this.callSuper([cfg]);

				//编辑状态
				this.setEditable(this.editable,true);

				//
				this.setValidate(this.validateConfig);

				//默认值
				if(this.defValue!=null){
					this.setValue(this.defValue);
				}
			},
			/**
			 * BUILD
			 */
			build:function(){
				var me=this;

				this.element=EG.CE({tn:"div",cls:this.cls,cn:[
					this.lDiv=EG.CE({tn:"div",cls:this.cls+"-dL",item:this,style:EG.unnull(this.labelStyle,""),cn:[
						this.dStar	=EG.CE({tn:"div",cls:this.cls+"-star",item:this,innerHTML:" * "}),
						this.dTitle	=EG.CE({tn:"div",cls:this.cls+"-title",item:this,innerHTML:this.title})
					]})
					,
					this.rDiv=EG.CE({tn:"div",cls:this.cls+"-dR",cn:[
						this.dPre	=EG.CE({tn:"div",cls:this.cls+"-pre",item:this,style:EG.Style.c.dv}),
						this.dProp	=EG.CE({tn:"div",cls:this.cls+"-prop",item:this,cn:[
							this.elementRead=EG.CE({tn:"div",item:this,cls:this.cls+"-read"})
						],style:EG.Style.c.dv}),
						this.dAfter	=EG.CE({tn:"div",cls:this.cls+"-after",item:this,style:EG.Style.c.dv}),
						this.dError	=EG.CE({tn:"div",cls:this.cls+"-error",item:this,style:EG.Style.c.dv})
					]})
				]});

				EG.hide(this.dPre,this.dAfter,this.dError);

				//读取模式样式
				if(this.readStyle){
					EG.css(this.elementRead,this.readStyle);
				}

				//读取模式的点击事件
				if(this.readClick){
					this.elementRead.onclickSrc=this;
					EG.Event.bindEvent(this.elementRead,"onclick",this.readClick);
					EG.css(this.elementRead,"text-decoration:underline;color:blue");
				}

				//创建提交使用的input
				if(this.type=="upload"){
					this.iptSub=EG.CE({pn:this.rDiv,tn:"input",type:"hidden",name:this.name,style:"display:none"});
				}

				//后组件
				if(this.after){
					this._renderAfter=false;
					if(typeof(this.after)=="string")			{this.dAfter.innerHTML=this.after;}
					else if(EG.DOM.isElement(this.after))		{this.dAfter.appendChild(this.after);}
					else if(this.after.getElement)				{this.dAfter.appendChild(this.after.getElement());this._renderAfter=true;}
					else if(typeof(this.after)=="function")		{this.after.apply(this,[this.dAfter]);}

					EG.show(this.dAfter);
				}

				//前组件
				if(this.pre){
					this._renderPre=false;
					if(typeof(this.pre)=="string")				{this.dPre.innerHTML=this.pre;}
					else if(EG.DOM.isElement(this.pre))			{this.dPre.appendChild(this.pre);}
					else if(this.pre.getElement)				{this.dPre.appendChild(this.pre.getElement());this._renderPre=true;}
					else if(typeof(this.pre)=="function")		{this.pre.apply(this,[this.dPre]);}

					EG.show(this.dPre);
				}

				//读取组件类
				this.readPropClass();

				//创建
				this.buildProp();

				//绑定错误信息提示事件

				//
				EG.bindEvent(this.rDiv,"mouseover",function(){
					if(me.vErrorMsg) EG.Tip.error(me.vErrorMsg,this);
				});

				//
				EG.bindEvent(this.rDiv,"mouseout",function(e){
					EG.Tip.close();
					EG.Event.stopPropagation(e);
				});
			},


			setSubmitValue:function(value){
				if(this.iptSub) this.iptSub.value=value;
			},

			/**
			 * 渲染
			 */
			render:function(){
				var s;
				Item.fit(this);

				//非空判断
				if(!this.unnull||!this.editable){
					EG.hide(this.dStar);
				}else{
					EG.show(this.dStar);
				}

				var pSize=EG.getSize(this.element);

				//设置左Div的尺寸和行高
				var lWidth;
				if(this.showLeft){
					Item.fit({element:this.lDiv,dSize:{width:this.labelWidth,height:"100%"},pSize:pSize});
					var lLineHeight=EG.getSize(this.lDiv).innerHeight;
					EG.css(this.lDiv,"line-height:"+lLineHeight+"px");
					EG.show(this.lDiv);
					lWidth=EG.getSize(this.lDiv).outerWidth;
				}else{
					EG.hide(this.lDiv);
					lWidth=0;
				}

				//设置右Div的尺寸

				Item.fit({element:this.rDiv,dSize:{width:pSize.innerWidth-lWidth,height:"100%"},pSize:pSize});

				//设置Prop Div的尺寸
				var rSize=EG.getSize(this.rDiv);
				var w=rSize.innerWidth;
				if(this.after){
					//渲染
					if(this._renderAfter) this.after.render();

					s=EG.getSize(this.dAfter);
					w=w-s.outerWidth;
					EG.css(this.dAfter,"width:"+s.innerWidth+"px");
				}
				if(this.pre){
					//渲染
					if(this._renderPre) this.pre.render();

					s=EG.getSize(this.dPre).outerWidth;
					w=w-s.outerWidth;
					EG.css(this.dPre,"width:"+s.innerWidth+"px");
				}

				Item.fit({element:this.dProp,dSize:{width:w,height:"100%"},pSize:rSize});

				if(this.editable){
					if(this.prop&&this.prop.render){
						if(this.prop.width==null){
							this.prop.width=w;
						}
						this.prop.height=rSize.innerHeight;
						this.prop.render();
					}
				}else{
					Item.fit({
						element:this.elementRead,
						pSize:rSize
					});

					s=EG.getSize(this.elementRead);
					EG.css(this.elementRead,"line-height:"+ (EG.unnull(this.readLineHeight,s.innerHeight))+"px");


					if(this.prop.afterRenderRead){//FIX:在渲染read后(百度地图需在show后添加)
						this.prop.afterRenderRead();
					}
					//EG.css(this.elementRead,"line-height:"+readLineHeight+"px");//TODO 待设计可设置的行高识别
				}
			},

			/** 渲染外框，为autoSize使用 */
			renderOuter	:function(){
//			var overflow=EG.Style.current(this.getElement()).overflow;
//			EG.css(this.getElement(),"overflow:hidden;");
				Item.fit(this);
				this.saveOuterSize();
				this._outerSize=this.getSize();
//			EG.css(this.getElement(),"overflow:"+overflow+";");
			},

			/**
			 * 设置是否可编辑
			 * @param {Boolean} editable 编辑
			 * @param {Boolean} force 强制
			 */
			setEditable:function(editable,force){
				if(this.editable==editable&&force!=true) return;

				this.editable=editable;

				if(this.editable){
					EG.hide(this.elementRead);
					EG.show(this.propElement);
				}else{
					EG.hide(this.dStar,this.propElement);
					EG.show(this.elementRead);
					EG.Style.removeCls(this.lDiv,this.cls+"-error");
				}

				//
				if(this.cacheValue||this.cacheData){
					this.setValue(this.cacheValue,this.cacheData);
				}

				this.render();
			},

			/**
			 * 设置非空
			 */
			setUnnull:function(unnull,doRender){
				if(doRender==null) doRender=true;
				this.unnull=unnull;
				if(doRender) this.render();
			},

			/**
			 * 识别Prop类
			 */
			readPropClass:function(){
				if(!this.typeClass){
					if(typeof(this.type)=="string"){
						this.typeClass=EG._defaultLoader.find(this.type);
						if(!this.typeClass){
							var cName="EG.ui."+EG.Word.first2Uppercase(this.type);
							this.typeClass=EG._defaultLoader.find(cName);
						}
					}else if(typeof(this.type)=="function"){
						this.typeClass=this.type;
					}
				}

				if(!this.typeClass) throw new Error("无法识别类型"+this.type);
			},

			/**
			 * 换Prop
			 */
			changeProp:function(type,typeCfg){
				
				if(typeof(type)=="function"){
					this.typeClass=type;
				}else{
					this.typeClass=null;
				}
				this.typeCfg=typeCfg;
				this.type=type;
				
				EG.DOM.remove(this.propElement);
				
				this.readPropClass();
				
				this.buildProp();
			},
			
			/**
			 * 创建prop
			 */
			buildProp:function(){
				var cfg=this.typeCfg?this.typeCfg:this.cfg;
				cfg.formItem=this;
				cfg.unnull	=this.unnull;
				this.prop=new this.typeClass(cfg);
				this.prop.formItem=this;

				this.propElement=this.prop.getElement();
				this.dProp.appendChild(this.propElement);
			},

			/**
			 * 获取父form
			 * @return {EG.ui.Form}
			 */
			getForm:function(){
				var pi=this.pItem;
				while(pi&& !(pi instanceof EG.ui.Form)){
					pi=pi.pItem;
				}
				return (pi instanceof EG.ui.Form)?pi:null;
			},

			/**
			 * 设值
			 * @param {*} value 数值
			 * @param {Object?} data 数据集
			 */
			setValue:function(value,data){
				//
				value=this.setValueBefore(value,data);

				//缓存值
				this.cacheData=data;
				this.cacheValue=value;

				var cfg=this.typeCfg||this.cfg;

				if(this.editable||this.prop.setPropValueOnRead){
					this.prop.setValue(this.cacheValue,data);
				}

				if(!this.editable){
					var t=this.prop.getTitle?this.prop.getTitle(value,data,cfg):value;
					if(this.setRead||this.prop.setRead) {
						(this.setRead||this.prop.setRead).apply(this,[this.elementRead,value, data,cfg]);
					}else if(EG.DOM.isElement(t)){
						EG.DOM.removeChilds(this.elementRead);
						this.elementRead.appendChild(t);
					}else{
						EG.setValue(this.elementRead,t);
					}
				}
			},

			/**
			 * setValue前重新处理
			 * @param value
			 * @param data
			 */
			setValueBefore:function(value,data){
				return value;
			},

			/**
			 * 获值
			 * @returns {*}
			 */
			getValue:function(){

				var v=null;
				if(this.editable){
					v=this.prop.getValue();
				}else{
					v=this.cacheValue;
				}

				v=this.getValueAfter(v);

				return v;
			},

			/**
			 * getValue前重新处理
			 * @param v
			 * @returns {*}
			 */
			getValueAfter:function(v){
				return v;
			},

			/**
			 * 获取Prop
			 * 会忽略掉FormProp
			 * @returns {*}
			 */
			getProp:function(){
				if(this.prop){
					return EG.unnull(this.prop.prop||this.prop);
				}
			},

			/**
			 * 设定校验器
			 * @param validate
			 */
			setValidate:function(validate){
				this.validateConfig=validate;
			},

			/**
			 * 校验
			 */
			validate:function(){

				this.vErrorMsg	=null;

				EG.Style.removeCls(this.lDiv,this.cls+"-error");

				if(this.prop.validate){
					var ev=this.prop.validate();
					this.vErrorMsg=ev?(this.title+ev):null;
				}else{
					var val=this.prop.getValue();

					//非空判断
					if(val==null||(typeof(val)=="string"&&val==="")) {
						if (this.unnull == true) {
							this.vErrorMsg = this.title + "不能为空";
						}
					}else{
						if(!this.vErrorMsg&&typeof(val)=="string"){
							//最小长度校验
							var minLength=this.minLength;
							if(minLength!=null&&val.length<minLength){
								this.vErrorMsg=this.title+"最小长度为"+minLength+"个字符";
							}

							//格式校验
							if(!this.vErrorMsg){
								var vld=this.vldType;
								if(vld){
									if(typeof(vld)=="string"){
										this.vErrorMsg=EG.Validate.$is(vld,val);
									}else if(typeof(vld)=="function"){
										this.vErrorMsg=vld(val);
									}else{
										if(!this.vldTypeObj){
											this.vldTypeObj=new vld["type"](vld);
										}

										this.vErrorMsg=this.vldTypeObj.validate(val);
									}
								}
							}
						}
					}
				}



				if(this.vErrorMsg){
					this.onError();
				}

				return !this.vErrorMsg;
			},

			/**
			 * 出错时
			 */
			onError:function(){

				EG.Style.addCls(this.lDiv,this.cls+"-error");

				if(this.prop.onError){
					this.prop.onError();
				}
			},

			/**
			 * 格式化
			 */
			format:function(){
				if(!this.vldType||!this.vldType.type) return;

				if(!this.vldTypeObj){
					this.vldTypeObj=new this.vldType["type"](this.vldType);
				}

				var val=this.vldTypeObj.format(this.getValue());
				this.setValue(val);
			},

			/**
			 *
			 */
			destroy:function(){
				if(this.prop&&this.prop.destroy) this.prop.destroy();
			},

			/**
			 * 在Form GetData 时合并数据时可以用组件的onGetData来处理
			 * @interface
			 */
			onGetData:null,

			statics:{

				//TODO 所有的validate&&format 要匹配到所有的输入功能组件上，默认在EG.ui.Input上

				/**
				 * 绑定校验
				 */
				bindValidate:function(){
					var me=this;
					this.vError=false;

					var fn_validate=function(){
						if(me._tValidate){
							clearTimeout(me._tValidate);
							me._tValidate=null;
						}

						me._tValidate=setTimeout(function(){
							if(me.formItem){
								me.formItem.format();
								me.formItem.validate();
							}
						},500);
					};

					var fn_validate_ipt=function(e){

						e=EG.Event.getEvent(e);
						if(37<=e.keyCode&&e.keyCode<=40){
							return;
						}
						fn_validate();

					};


					var cName=this.getClass()._className;
					if(EG.$in(cName,["EG.ui.Text","EG.ui.Password","EG.ui.Textarea","EG.ui.Date"])){
						EG.bindEvent(this.input,"keyup",fn_validate_ipt);
						EG.bindEvent(this.input,"blur",fn_validate_ipt);
					}else if(cName=="EG.ui.Select"){
						this.bindOnchange(fn_validate);
						EG.bindEvent(this.prop.input,"blur",fn_validate_ipt);
					}else if(cName=="EG.ui.BoxGroup"){
						this.bindOnchange(fn_validate);
					}
				},

				/**
				 * 校验
				 * @return {Boolean}
				 */
				validate:function(){
					//校验是否开启
					var form=this.getForm();

					//alert("OK :"+this.formItem.name)

					if(form&&!form.validateAble) return true;

					var val=this.prop.getValue();
					this.vError=false;
					this.v_msg="";
					//非空判断
					if(EG.String.isEmpty(val)){
						if(this.unnull==true){
							this.vError=true;
							this.v_msg=this.formItem.title+"不能为空";
						}
					}else{
						//最小长度校验
						var minLength=this.minLength;
						if(minLength!=null&&val.length<minLength){
							this.vError=true;
							this.v_msg=this.formItem.title+"最小长度为"+minLength+"个字符";
						}

						//格式校验
						if(!this.vError){
							var vld=this.vldType;
							if(vld){
								if(typeof(vld)=="string"){
									this.vError=!(EG.Validate.$is(vld,val));
									if(this.vError){
										this.v_msg=this.formItem.title+"格式应为"+EG.Validate.getComment(vld);
									}else{
										this.v_msg="";
									}
								}else if(typeof(vld)=="function"){
									this.vError=!(vld(val));
								}else{
									if(!this.vldTypeObj){
										this.vldTypeObj=new vld["type"](vld);
									}

									this.vError=!this.vldTypeObj.validate(val);
									if(this.vError){
										this.v_msg= this.vldTypeObj.getMessage();
									}
								}
							}
						}
					}

					if(this.vError&&!this.v_msg) this.v_msg="请输入正确的"+this.formItem.title;
					//if(this.vError) alert(this.formItem.name+":"+val+","+this.v_msg);
					//错误时样式
					if(this.onError){
						this.onError();
					}
					//必须返回
					return !this.v_msg;
				}
			}
		};
	});
})();

/**
 * @class EG.ui.form.Prop
 * <AUTHOR>
 * @extends EG.ui.Item
 * FormItenm 表单元素
 */
(function(){

	/**
	 * EG.ui.form.Prop 父Prop类
	 * 定义待实现接口
	 */
	EG.define("EG.ui.form.Prop",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			extend:Item,
			/**
			 * 获取Element
			 * @interface
			 * @returns {HTMLElement}
			 */
			getElement:function(){
				return this.prop.getElement();
			},
			/**
			 * 设值
			 * @param {Object} value 数值
			 * @param {Object?} ext 扩展
			 * @interface
			 */
			setValue:function(value,ext){
				this.prop.setValue(value,ext);
			},
			/**
			 * 获值
			 * @interface
			 * @returns {String}
			 */
			getValue:function(){
				return this.prop.getValue();
			},
			/**
			 * 显示标题
			 * @interface
			 */
			getTitle:null,


			statics:{


				/**
				 * 校验
				 * @return {Boolean}
				 */
				validate:function(){
					//校验是否开启
					var form=this.getForm();

					//alert("OK :"+this.formItem.name)

					if(form&&!form.validateAble) return true;

					var val=this.prop.getValue();
					this.vError=false;
					this.v_msg="";
					//非空判断
					if(EG.String.isBlank(val)){
						if(this.unnull==true){
							this.vError=true;
							this.v_msg=this.formItem.title+"不能为空";
						}
					}else{
						//最小长度校验
						var minLength=this.minLength;
						if(minLength!=null&&val.length<minLength){
							this.vError=true;
							this.v_msg=this.formItem.title+"最小长度为"+minLength+"个字符";
						}

						//格式校验
						if(!this.vError){
							var vld=this.vldType;
							if(vld){
								if(typeof(vld)=="string"){
									this.vError=!(EG.Validate.$is(vld,val));
									if(this.vError){
										this.v_msg=this.formItem.title+"格式应为"+EG.Validate.getComment(vld);
									}else{
										this.v_msg="";
									}
								}else if(typeof(vld)=="function"){
									this.vError=!(vld(val));
								}else{
									if(!this.vldTypeObj){
										this.vldTypeObj=new vld["type"](vld);
									}

									this.vError=!this.vldTypeObj.validate(val);
									if(this.vError){
										this.v_msg= this.vldTypeObj.getMessage();
									}
								}
							}
						}
					}

					if(this.vError&&!this.v_msg) this.v_msg="请输入正确的"+this.formItem.title;
					//if(this.vError) alert(this.formItem.name+":"+val+","+this.v_msg);
					//错误时样式
					if(this.onError){
						this.onError();
					}
					//必须返回
					return !this.v_msg;
				},
				/**
				 * 校验出错时
				 */
				onError:function(){
					//显示错误样式
					if(this.vError){
						EG.setCls(this.prop.input,"error",this.prop.cls);
					}else{
						EG.setCls(this.prop.input,"input",this.prop.cls);
					}
				}
			}
		};
	});
})();

/**
 * @class EG.ui.form.prop.Box
 * <AUTHOR>
 * @extends EG.ui.form.Prop
 * 表单元素控件封装类-盒子
 */
(function(){
	EG.define("EG.ui.form.prop.Box",[
		"EG.ui.form.Prop",
		"EG.ui.BoxGroup"
	],function(Prop,BoxGroup,ME){
		return {
			extend:Prop,
			config:{
				/** @cfg {String} type 类型 */
				type:"boxGroup"
			},
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				try{
					this.prop=new BoxGroup(cfg);
				}catch(e){
					alert(e.message);
				}

				//EG.ui.form.Prop.bindValidate.apply(this,[cfg]);
			},
			/**
			 * 校验
			 * @return {Boolean}
			 */
			validate:function(){
				//校验是否开启
				var form=this.getForm();
				if(form&&!form.validateAble) return true;

				var val=this.prop.getValue();
				this.vError=false;

				if(typeof(val)=="number"){
					//
				}else{
					//非空判断
					if((this.prop.multiple&&val.length==0)||(!this.prop.multiple&&EG.String.isBlank(val))){
						if(this.unnull==true){
							this.vError=true;
							this.v_msg=this.formItem.title+"不能为空";
						}
					}
				}


				if(this.vError&&!this.v_msg) this.v_msg="请选择正确的"+this.formItem.title;
				//alert(me.vError);
				//显示错误样式
				//alert(this.vError)
				if(this.vError){
					EG.setCls(this.prop.element,"error",this.prop.cls);
				}else{
					EG.setCls(this.prop.element,"input",this.prop.cls);
				}
				return !this.vError;
			},

			/**
			 * 获取标题
			 * @param {String} value 数值
			 * @param {Object} data 数据集
			 * @param {Object} cfg 扩展
			 * @return {String}
			 */
			getTitle:function(value,data,cfg){

				var textvalues=cfg.textvalues||[];
				if(!EG.isArray(value)){
					value=[value];
				}
				var titles=[];
				for(var j=0,jl=value.length;j<jl;j++){
					var v=value[j];
					for(var i=0,il=textvalues.length;i<il;i++){
						if(textvalues[i][1]===v){
							titles.push(textvalues[i][0]);
							break;
						}
					}
				}
				return titles.join(",");
			}

		};
	});
})();
/**
 * @class EG.ui.form.prop.Date
 * <AUTHOR>
 * @extends EG.ui.form.Prop
 * 表单元素控件封装类-日期
 */
(function(){
	EG.define("EG.ui.form.prop.Date",[
		"EG.ui.form.Prop",
		"EG.ui.Date"
	],function(Prop,Date,ME){
		return {
			extend:Prop,
			config:{
				/** @cfg {String} type 类型 */
				type		:"date",
				/** @cfg {?Boolean} unnull 不能为空 */
				unnull		:false,
				/** @cfg {?Boolean} minLength 最小长度 */
				minLength	:null,
				/** @cfg {?Number|?String} width 宽度 */
				width		:"100%",
				/** @cfg {?Number|?String} height 高度 */
				height		:20,
				/** @cfg {String} vldType 校验种类 */
				vldType		:null
			},
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.initConfig(cfg);

				this.prop=new Date(cfg);

				//绑定校验
				Prop.bindValidate.apply(this,[cfg]);
			},
			/**
			 * 校验
			 * @return {Boolean}
			 */
			validate:function(){
				return Prop.validate.apply(this);
			},
			/**
			 * 错误时
			 * @return {*}
			 */
			onError:function(){
				return Prop.onError.apply(this);
			}
		};
	});
})();
/**
 * @class EG.ui.form.prop.Date
 * <AUTHOR>
 * @extends EG.ui.form.Prop
 * 表单元素控件封装类-编辑器
 */
(function(){
	EG.define("EG.ui.form.prop.Editor",[
		"EG.ui.form.Prop",
		"EG.ui.Editor"
	],function(Prop,Editor,ME){
		return {
			extend:Prop,
			config:{
				readLineHeight:22
			},
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.prop=new Editor(cfg);
			}
		};
	});
})();

/**
 * @class EG.ui.form.prop.Date
 * <AUTHOR>
 * @extends EG.ui.form.Prop
 * 表单元素控件封装类-标签
 */
(function(){
	EG.define("EG.ui.form.prop.Label",[
		"EG.ui.form.Prop",
		"EG.ui.Label"
	],function(Prop,Label,ME){
		return {
			extend:Prop,
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.prop=new Label(cfg);
			}
		}
	});
})();
/**
 * @class EG.ui.form.prop.Password
 * <AUTHOR>
 * @extends EG.ui.form.Prop
 * 表单元素控件封装类-密码
 */
(function(){
	EG.define("EG.ui.form.prop.Password",[
		"EG.ui.form.Prop",
		"EG.ui.Password"
	],function(Prop,Password,ME){
		return {
			extend:Prop,
			config:{
				/** @cfg {String} type 类型 */
				type		:"password",//类型
				/** @cfg {?Boolean} unnull 不能为空 */
				unnull		:false,
				/** @cfg {?Boolean} minLength 最小长度 */
				minLength	:null,
				/** @cfg {?Number|?String} width 宽度 */
				width		:"100%",
				/** @cfg {?Number|?String} height 高度 */
				height		:20,
				/** @cfg {String} vldType 校验种类 */
				vldType		:null
			},
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.initConfig(cfg);

				this.prop=new Password(cfg);

				//绑定校验
				Prop.bindValidate.apply(this,[cfg]);
			},
			/**
			 * 校验
			 * @return {Boolean}
			 */
			validate:function(){
				return Prop.validate.apply(this);
			},
			/**
			 * 错误时
			 * @return {*}
			 */
			onError:function(){
				return Prop.onError.apply(this);
			}
		}
	});
})();
/**
 * @class EG.ui.form.prop.Select
 * <AUTHOR>
 * @extends EG.ui.form.Prop
 * 表单元素控件封装类-选择框
 */
(function(){
	/**
	 * EG.ui.form.prop.Select 选择框
	 *
	 */
	EG.define("EG.ui.form.prop.Select",[
		"EG.ui.form.Prop",
		"EG.ui.Select"
	],function(Prop,Select,ME){
		return {
			extend:Prop,
			config:{
				/** @cfg {String} type 类型 */
				type		:"select",
				/** @cfg {?Boolean} unnull 不能为空 */
				unnull		:false
			},
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.initConfig(cfg);

				this.prop=new Select(cfg);

				Prop.bindValidate.apply(this,[cfg]);
			},
			/**
			 * 校验
			 * @return {Boolean}
			 */
			validate:function(){
				//校验是否开启
				var form=this.getForm();
				if(form&&!form.validateAble) return true;

				var val=this.prop.getValue();
				this.vError=false;
				this.v_msg="";

				//非空判断
				if(EG.String.isBlank(val+"")){
					if(this.unnull==true){
						this.vError=true;
						this.v_msg=this.formItem.title+"不能为空";
					}
				}

				if(this.vError&&!this.v_msg) this.v_msg="请选择正确的"+this.cfg["title"];
				//if(this.vError) alert(this.formItem.name+":"+val+","+this.v_msg);
				//错误时样式
				if(this.onError){
					this.onError();
				}
				return !this.vError;
			},
			/**
			 * 校验出错时
			 */
			onError:function(){
				//显示错误样式
				if(this.vError){
					EG.setCls(this.prop.input,"error",this.prop.cls);
				}else{
					EG.setCls(this.prop.input,"input",this.prop.cls);
				}
			},
			/**
			 * 获取标题
			 * @param {String} value 数值
			 * @param {Object} data 数据集
			 * @param {Object} cfg 扩展
			 * @return {String}
			 */
			getTitle:function(value,data,cfg){
				var textvalues=cfg["textvalues"]||[];
				for(var i=0,il=textvalues.length;i<il;i++){
					if(textvalues[i][1]==value) return textvalues[i][0];
				}
				return null;
			}
		};
	});
})();

/**
 * @class EG.ui.form.prop.SelectArea
 * <AUTHOR>
 * @extends EG.ui.form.Prop
 * 表单元素控件封装类-多选框
 */
(function(){
	/**
	 * EG.ui.form.prop.SelectArea 选择框
	 *
	 */
	EG.define("EG.ui.form.prop.SelectArea",[
		"EG.ui.form.Prop",
		"EG.ui.SelectArea"
	],function(Prop,SelectArea,ME){
		return {
			extend:Prop,
			config:{
				/** @cfg {String} type 类型 */
				type		:"SelectArea",
				/** @cfg {?Boolean} unnull 不能为空 */
				unnull		:false
			},
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				var me=this;
				this.initConfig(cfg);

				var oc=cfg["onchange"];
				cfg["onchange"]=function(){
					me.validate();
					if(oc) oc.apply(this,arguments);
				}

				this.prop=new SelectArea(cfg);

				Prop.bindValidate.apply(this,[cfg]);
			},
			/**
			 * 校验
			 * @return {Boolean}
			 */
			validate:function(){
				this.vError=false;
				this.v_msg="";
				var val=this.prop.getValue();
				//非空判断
				if(this.unnull==true&&(!val||val.length==0)){
					this.v_msg=this.formItem.title+"不能为空";
					this.vError=true;
				}
				//错误时样式
				if(this.onError){
					this.onError();
				}
				return !this.vError;
			},

			/**
			 * 校验出错时
			 */
			onError:function(){
				//显示错误样式
				if(this.vError){
					EG.setCls(this.prop.destSlt,"error",this.prop.cls);
				}else{
					EG.setCls(this.prop.destSlt,"slts",this.prop.cls);
				}
			}

		};
	});
})();

/**
 * @class EG.ui.form.prop.Text
 * <AUTHOR>
 * @extends EG.ui.form.Prop
 * 表单元素控件封装类-文本框
 */
(function(){
	EG.define("EG.ui.form.prop.Text",[
		"EG.ui.form.Prop",
		"EG.ui.Text"
	],function(Prop,Text,ME){
		return {
			extend:Prop,
				config:{
				/** @cfg {String} type 类型 */
				type		:"text",
				/** @cfg {?Boolean} unnull 不能为空 */
				unnull		:false,
				/** @cfg {?Boolean} minLength 最小长度 */
				minLength	:null,
				/** @cfg {?Number|?String} width 宽度 */
				width		:"100%",
				/** @cfg {?Number|?String} height 高度 */
				height		:20,
				/** @cfg {String} vldType 校验种类 */
				vldType		:null
			},
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.initConfig(cfg);

				this.prop=new Text(cfg);
				//绑定校验
				Prop.bindValidate.apply(this,[cfg]);
			},
			/**
			 * 校验
			 * @return {Boolean}
			 */
			validate:function(){
				return Prop.validate.apply(this);
			},
			/**
			 * 错误时
			 * @return {*}
			 */
			onError:function(){
				return Prop.onError.apply(this);
			}
		}
	});
})();
/**
 * @class EG.ui.form.prop.Textarea
 * <AUTHOR>
 * @extends EG.ui.form.Prop
 * 表单元素控件封装类-文本区域
 */
(function(){
	EG.define("EG.ui.form.prop.Textarea",[
		"EG.ui.form.Prop",
		"EG.ui.Textarea"
	],function(Prop,Textarea,ME){
		return {
			extend:Prop,
			config:{
				/** @cfg {String} type 类型 */
				type		:"textarea",
				/** @cfg {?Boolean} unnull 不能为空 */
				unnull		:false,
				/** @cfg {?Boolean} minLength 最小长度 */
				minLength	:null,
				/** @cfg {?Number|?String} width 宽度 */
				width		:"100%",
				/** @cfg {?Number|?String} height 高度 */
				height		:40,
				/** @cfg {String} vldType 校验种类 */
				vldType		:null,
				/** @cfg {Number} readLineHeight 读取模式行高 */
				readLineHeight:22
			},
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.initConfig(cfg);
				this.prop=new Textarea(cfg);
				//绑定校验
				Prop.bindValidate.apply(this,[cfg]);
			},
			/**
			 * 校验
			 * @return {Boolean}
			 */
			validate:function(){
				return Prop.validate.apply(this);
			},
			/**
			 * 错误时
			 * @return {*}
			 */
			onError:function(){
				return Prop.onError.apply(this);
			},
			/**
			 * 强行设置Read模式下的内容
			 * @param elRead
			 * @param value
			 */
			setRead:function(elRead,value){
				if(!value) value="";
				//value=EG.String.replaceAll(value,"\n","<br>");
				value=EG.String.replaceAll(value," ","&nbsp;");
				var ss=value.split("\n");
				for(var i=0;i<ss.length;i++){
					ss[i]=("<span>"+ss[i]+"</span>");
				}
				value=ss.join("<br/>");
				value=EG.String.replaceAll(value,"\t","<div style='margin-left:4em;display:inline-block;'></div>");
				EG.setValue(elRead, value);
			}
		};
	});
})();
/**
 * @class EG.ui.form.prop.Upload
 * <AUTHOR>
 * @extends EG.ui.form.Prop
 * 表单元素控件封装类-上传
 */
(function(){
	EG.define("EG.ui.form.prop.Upload",[
		"EG.ui.form.Prop",
		"EG.ui.Upload"
	],function(Prop,Upload,ME){
		return {
			extend:Prop,
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.prop=new Upload(cfg);
			}
		};
	});
})();
/**
 * @class EG.ui.Menu
 * <AUTHOR>
 * @extends EG.ui.Item
 * 菜单类
 */
(function(){
	/**
	 * 菜单
	 */
	EG.define("EG.ui.Menu",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			extend:Item,
			config:{
				/** @cfg {String?} direct 方向 */
				direct	:"H",
				/** @cfg {Boolean?} autoExpand 自动展开 */
				autoExpand	:false,
				/** @cfg {Number?} autoExpandLv 自动展开层级 */
				autoExpandLv:0,

				itemsConfig:null,
				/** @cfg {Boolean?} pop 是否弹出 */
				pop:false,
				/** @cfg {String?} verticalAlign 纵向对齐方式 */
				verticalAlign	:null,
				/** @cfg {Boolean?} isContextMenu 是否是上下文菜单 */
				isContextMenu	:false,
				/** @cfg {HTMLElement?} contextElement 上下文元素 */
				contextElement	:null
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.callSuper([cfg]);

				//绑定
				if(this.contextElement){
					this.attachContext(this.contextElement);
				}
			},

			/**
			 * 创建后执行
			 */
			afterBuild:function(){
				this.items=[];
				//添加子组件
				if (this.itemsConfig!=null&&this.itemsConfig.length > 0){
					this.addItem(this.itemsConfig,false);
				}
			},


			/**
			 * 构建
			 */
			build:function(){
				var me=this;
				this.element 		=EG.CE({tn : "div"});

				if(this.isContextMenu){
					EG.css(this.element,"position:absolute");
					EG.hide(this.element);
				}
			},

			/**
			 * 绑定上下文
			 * @param ele
			 */
			attachContext:function(ele){
				var me=this;
				this.contextElement=ele;
				EG.Event.bindEvent(this.contextElement,"oncontextmenu",function(e){
					//获取当前鼠标相对通讯面板的鼠标
					e=EG.Event.getEvent(e);
					var pos=EG.Tools.getMousePos(e,me.contextElement);
					EG.Style.moveTo(me.element,pos);
					if(me.onOpen){
						if(me.onOpen.apply(me,[e])===false) return;
					}
					EG.show(me.element);
					EG.Event.stopPropagation(e);
					return false;
				});

				EG.Event.bindEvent(this.contextElement,"onclick",function(e){
					EG.hide(me.element);
				});
			},

			/**
			 * 隐藏
			 */
			hide:function(){
				EG.hide(this.element);
			},

			//TODO 尝试变 Container

			addItem:function(item,idx){

				var items=(!EG.isArray(item))?[item]:item;
				for (var i=0,il=items.length;i<il;i++) {
					item = items[i];
					item["menu"]=this;
					if (EG.isLit(item)) {
						item=Item.create(item.xtype,item);
					}

					if(idx>=0){
						EG.Array.insert(this.items,idx,item);
					}else{
						this.items.push(item);
					}
					EG.DOM.addChildren(this.element,item.getElement(),idx);

					item.pItem=this;
				}
				this.render();
			},

			removeItem:function(item,autoLayout){
				if(autoLayout==null) autoLayout=true;
				//DOM移除
				this.element.removeChild(item.getElement());
				//数组移除
				EG.Array.remove(this.items,item);
				//清空关系
				item.pItem=null;

				if(autoLayout){
					//if(!this.isPItemAuto()){
					//this.doLayout();
					//}
				}
			},

			/**
			 * 清空
			 */
			clear:function(autoLayout){
				if(autoLayout==null) autoLayout=true;
				for(var i=0;i<this.items.length;i++){
					if(this.items[i].isContainer) this.items[i].clear();
				}
				EG.DOM.removeChilds(this.getItemContainer());
				EG.Array.clear(this.items);
				if(autoLayout){
					//if(!this.isPItemAuto()){
					//this.doLayout();
					//}
				}
			},

			/**
			 * 渲染
			 */
			render:function(){
				Item.fit(this);
				var s=this.getSize();
				if(this.direct=="H"){
					for(var i=0;i<this.items.length;i++){
						var it=this.items[i];
						EG.css(it.getElement(),EG.Style.c.dv);
						it.render();
					}
				}else{
					for(var i=0;i<this.items.length;i++){
						var it=this.items[i];
						EG.css(it.getElement(),"display:block;");
						it.render();
					}
				}

				//纵向对齐
				if(this.verticalAlign=="middle"){
					EG.Style.middleChilds(this.element,this.direct);
				}else if(this.verticalAlign=="top"){
					EG.Style.topChilds(this.element,this.direct);
				}else if(this.verticalAlign=="bottom"){
					EG.Style.bottomChilds(this.element,this.direct);
				}

			},

			/**
			 * 清理
			 */
			clear:function(){
				EG.Array.clear(this.items);
				EG.DOM.removeChilds(this.element);
			}
		};
	});
})();

/**
 * @class EG.ui.MenuItem
 * <AUTHOR>
 * @extends EG.ui.Item
 * 菜单项
 */
(function(){
	/**
	 * 菜单项
	 */
	EG.define("EG.ui.MenuItem",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			extend:Item,
			config:{
				/** @cfg {String?} cls 样式 */
				cls		:"eg_menuItem",
				/** @cfg {String?} layoutDirect 方向(H:水平,V:垂直) */
				layoutDirect	:null,
				/** @cfg {String?} menuDirect 子菜单方向(T:上,B:下,L:左,R:右) */
				menuDirect		:null,
				/** @cfg {String?} text 文本 */
				text	:null,

				menuWidth:null,

				menuHeight:null,

				itemsConfig:null,
				/** @cfg {EG.ui.Menu?} menu 菜单 */
				menu:null,
				/** @cfg {Function?} click 点击事件 */
				click:null,
				/** @cfg {Boolean?} fade 渐显 */
				fade:true,
				/** @cfg {Boolean?} showMuti 现实多个提示 */
				showMuti:true,
				/** @cfg {String?} mutiDirect 多个箭头方向 */
				mutiDirect:"right",
				/** @cfg {Boolean?} showMutiOnEmpty 当子为空时仍然显示更多的箭头 */
				showMutiOnEmpty:false,
				/** @cfg {Boolean?} selectedAble 是否能选择 */
				selectedAble:true
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.callSuper([cfg]);

				//设置文字
				if(this.text){
					this.setText(this.text);
				}

				//设置Menu
				if(this.menu){
					this.setMenu(this.menu);
				}

				this.items=[];
				if(this.itemsConfig!=null&&this.itemsConfig.length>0){
					this.addItem(this.itemsConfig);
				}
			},

			/**
			 * 创建
			 */
			build:function(){
				var me=this;

				this.element=EG.CE({tn:"div",cls:this.cls,item:this,
					cn:[
						this.dOuter=EG.CE({tn:"div",cls:this.cls+"-outer",item:this,
							cn:[
//						this.dIcon	=EG.CE({tn:"div"}),
								this.dText 	=EG.CE({tn:"div",cls:this.cls+"-text",item:this}),
								this.dMulti	=EG.CE({tn:"div",cls:this.cls+"-multi",item:this})
							]
						}),
						this.dMenus=EG.CE({tn:"div",cls:this.cls+"-menu",item:this,style:"display:none;"})
					]});

				//
				if(this.mutiDirect=="right"){
					EG.DOM.insertAfter(this.dMulti,this.dText);
				}else if(this.mutiDirect=="left"){
					EG.DOM.insertBefore(this.dMulti,this.dText);
				}

				//挂OVER、OUT事件
				EG.CE({ele:this.dOuter,
					onmouseover	:ME._events.element.onmouseover	,onmouseoverSrc	:this,
					onmouseout	:ME._events.element.onmouseout	,onmouseoutSrc	:this,
					onclick		:ME._events.element.onclick		,onclickSrc		:this
				});

				EG.CE({ele:this.dMenus,
					onmouseover	:ME._events.element.onmouseover	,onmouseoverSrc	:this,
					onmouseout	:ME._events.element.onmouseout	,onmouseoutSrc	:this
				});

				EG.CE({ele:this.dText	,onclick:this.expandClick,onclickSrc	:this});
				EG.CE({ele:this.dMulti	,onclick:this.expandClick,onclickSrc	:this});

				EG.hide(this.dMenus);

				if(!this.showMuti){
					EG.hide(this.dMulti);
				}
			},

			setMenu:function(menu){
				this.menu=menu;
				EG.css(this.dMenus,(this.menu.pop?"position:absolute;z-index:1;":""))
			},

			/**
			 * 设置文本
			 * @param text
			 */
			setText:function(text){
				this.text=text;
				if(typeof(text)=="string"){
					this.dText.innerHTML=text;
				}else{
					var cn=text;
					if(!EG.Array.isArray(cn)){
						cn=[cn];
					}

					EG.CE({ele:this.dText,cn:cn});
				}

			},

			/**
			 * 点击展开
			 */
			expandClick:function(){

				//收缩变换
				if(!this.menu.pop){
					if(EG.Style.current(this.dMenus).display=="none"){
						this.showChildMenus();
					}else{
						this.hideChildMenus();
					}
				}

				//点击事件
				if(this.click){
					this.click.apply(this);
				}
			},

			/**
			 * 添加子
			 * @param item
			 * @param idx
			 */
			addItem:function(item,idx){
				var items=(!EG.isArray(item))?[item]:item;
				for (var i=0,il=items.length;i<il;i++) {
					item = items[i];
					item["menu"]=this.menu;
					if (EG.isLit(item)) {
						item=Item.create(item.xtype,item);
					}
					item.pItem=this;

					if(idx>=0){
						EG.Array.insert(this.items,idx,item);
					}else{
						this.items.push(item);
					}
					EG.DOM.addChildren(this.dMenus,item.getElement(),idx);
				}
				this.render();
			},

			removeItem:function(item,autoLayout){
				if(autoLayout==null) autoLayout=true;
				//DOM移除
				this.dMenus.removeChild(item.getElement());
				//数组移除
				EG.Array.remove(this.items,item);
				//清空关系
				item.pItem=null;

				if(autoLayout){
					//if(!this.isPItemAuto()){
					//this.doLayout();
					//}
				}
			},

			/**
			 * 清空
			 */
			clear:function(autoLayout){
				if(autoLayout==null) autoLayout=true;
				for(var i=0;i<this.items.length;i++){
					if(this.items[i].isContainer) this.items[i].clear();
				}
				EG.DOM.removeChilds(this.getItemContainer());
				EG.Array.clear(this.items);
				if(autoLayout){
					//if(!this.isPItemAuto()){
					//this.doLayout();
					//}
				}
			},

			/**
			 * 渲染
			 */
			render:function(){

				//切换显示箭头
				if(this.showMuti&&(this.showMutiOnEmpty||(this.items!=null&&this.items.length>0))){
					EG.show(this.dMulti);
				}else{
					EG.hide(this.dMulti);
				}

				//隐藏
				if(this.hidden){
					EG.hide(this.getElement());
					return;
				}else{
					EG.show(this.getElement());
				}

				if(this.rendered) return;

				//字体与更多按钮横向时
				if(this.width){
					Item.fit({
						element:this.element,
						width:this.width,
						type:"width"
					});

					Item.fit({
						element:this.dOuter,
						width:"100%",
						type:"width"
					});

					Item.fit({
						element:this.dText,
						width:EG.getSize(this.dOuter).innerWidth,
						type:"width"
					});
				}
				this.rendered=true;
			},

			renderMenus:function(){

				//设定横向或纵向
				if(!this.menu.pop){
					if((this.pItem&&this.pItem.layoutDirect||this.menu&&this.menu.direct)=="H"){
						EG.css(this.dMenus	,EG.Style.c.dv);
						EG.css(this.dText	,EG.Style.c.dv);
					}else{
						EG.css(this.dMenus	,"display:block;");
						EG.css(this.dText	,"display:block;");
					}
				}

				//子元素横向或纵向分布
				var s=this.getSize();
				if(this.layoutDirect=="H"){
					for(var i=0;i<this.items.length;i++){
						var it=this.items[i];
						EG.css(it.getElement(),EG.Style.c.dv);
						if(this.menuHeight!=null) it.height=this.menuHeight;
						it.render();
					}
				}else{
					for(var i=0;i<this.items.length;i++){
						var it=this.items[i];
						EG.css(it.getElement(),"display:block;");
						if(this.menuWidth!=null) it.width=this.menuWidth;
						it.render();
					}
				}
			},

			/**
			 * 打开子菜单
			 */
			showChildMenus:function(){

				if(this.items.length==0) return;

				//弹出式定位
				if(this.menu.pop){
					var pos=EG.Tools.getElementPos(this.element,this.element.offsetParent);
					var tPos=EG.Tools.getElementPos(this.dOuter,this.element.offsetParent);
					var size=EG.Style.getSize(this.dOuter);

					//底部
					if(this.menuDirect=="B"){
						pos.y=pos.y+size.innerHeight+size.borderBottom+size.borderTop;
						//右侧
					}else if(this.menuDirect=="R"){

						var w=size.innerWidth+size.borderLeft+size.borderRight;
						//当父节点是absolute时，菜单的相对高度要注意
						if(EG.Style.current(this.getElement().parentNode).position=="absolute"){
							pos.y=tPos.y;
							pos.x=w;
						}else{
							pos.x=pos.x+w;
							pos.y=tPos.y;
						}
					}else{
						throw new Error("暂不支持");
					}

					EG.css(this.dMenus,"top:"+pos.y+"px;left:"+pos.x+"px;");
					if(this.menuWidth!=null) {
						//alert(this.menuWidth);
						EG.css(this.dMenus,"width:"+this.menuWidth+"px");
					}
					if(this.menuHeight!=null) 	EG.css(this.dMenus,"height:"+this.menuHeight+"px");
				}

				EG.show(this.dMenus);
				if(this.fade){
					EG.Style.fade(this.dMenus,0,90,null,10);
				}

				this.renderMenus();
			},

			/**
			 * 隐藏子菜单
			 */
			hideChildMenus:function(){
				if(this.menu.pop){
					for(var i=0;i<ME.w4h.length;i++){
						EG.hide(ME.w4h[i].dMenus);
					}
					this.hiding=false;
				}else{
					EG.hide(this.dMenus);
				}

			},
			/**
			 * 添加到隐藏队列
			 */
			addHideQue:function(){
				if(!EG.Array.has(ME.w4h,this)){
					ME.w4h.push(this);
					var p=this;
					while((p=p.pItem)!=null){
						if(p.getClass()._className!="EG.ui.MenuItem"){
							break;
						}
						ME.w4h.push(p);
					}
				}
			},
			/**
			 * 移除到隐藏队列
			 */
			removeHideQue:function(){
				EG.Array.remove(ME.w4h,this);
				var p=this;
				while((p=p.pItem)!=null){
					if(p.getClass()._className!="EG.ui.MenuItem"){
						break;
					}
					EG.Array.remove(ME.w4h,p);
				}

				this.showChildMenus();
			},

			/**
			 * 刷新
			 * @param {String?} type
			 */
			refreshCls:function(type){
				type=type?EG.Word.first2Uppercase(type):"";
				EG.Style.setCls(this.dOuter	,this.cls+"-outer"	+EG.n2d(type,""));
				EG.Style.setCls(this.dText	,this.cls+"-text"	+EG.n2d(type,""));
			},

			statics:{
				_events:{
					element:{
						/**
						 * 移上时去除隐藏元素（自身及父）
						 * 并且打开自身的子菜单
						 */
						onmouseover:function(e){

							this.refreshCls("on");

							if(this.menu.pop){
								this.removeHideQue();
							}

							EG.Event.stopPropagation(e);
						},

						/**
						 * 移出时添加到隐藏队列（自身及父）
						 * 开启隐藏动作
						 */
						onmouseout:function(e){
							var me=this;

							if(this.pItem.selectedItem==this){
								this.refreshCls("selected");
							}else{
								this.refreshCls();
							}


							if(this.menu.pop){
								//加入隐藏队列
								this.addHideQue();

								//开启隐藏线程
								if(!this.hiding){
									this.hiding=true;
									setTimeout(function(){
										me.hideChildMenus();
									},100);
								}
							}

							EG.Event.stopPropagation(e);
						},

						/**
						 *
						 * @param e
						 */
						onclick:function(e){

							if(this.pItem.selectedItem){
								this.pItem.selectedItem.refreshCls();
							}

							if(this.selectedAble){
								this.pItem.selectedItem=this;
								this.refreshCls("selected");
							}
						}
					}
				},
				hiding:false,
				//待隐藏队列
				w4h:[]
			}
		};
	});
})();

/**
 * @class EG.ui.Calendar
 * <AUTHOR>
 * @extends EG.ui.Item
 * 日历类
 */
(function(){
	/**
	 * Calendar
	 */
	EG.define("EG.ui.Calendar",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			extend:Item,
			config:{
				/** @cfg {String?} cls CSS样式类 */
				cls				:"eg_calander",
				/** @cfg {String?} mode 模式:M月/W周/D日 */
					mode		:"M",
				/** @cfg {Number?} cellSpacing 间隙 */
					cellSpacing :0,
				/** @cfg {Number?} cellPadding 间隔 */
					cellPadding :0,
				/** @cfg {Number?} border 边框 */
					border		:0,
				/** @cfg {Number?} date 日期 */
					date		:null,
				/** @cfg {Number?} scale 刻度(单位:分钟) */
					scale		:15
			},

			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				//首次加载
				ME.load();

				this.callSuper([cfg]);

				//当前日期
				this.setDate(this.date||new Date());
			},

			/**
			 * 创建
			 */
			build:function(){
				var me=this;

				this.p=new EG.ui.Panel({layout:"border",items:[

					this.pTop	=new EG.ui.Panel({region:"top"	,height:30,cls:this.cls+"-top",layout:{type:"default",verticalAlign:true},cn:[
						this.dPre=EG.CE({tn:"div",cls:this.cls+"-top-pre"	,onclick:function(){me.goPre()}}),
						this.dCur=EG.CE({tn:"div",cls:this.cls+"-top-cur"	,onclick:function(){me.choose()},cn:[
							//年月选择
							this.dChooserYear	=EG.CE({tn:"div",onclick:this.chooseYear	,onclickSrc:this,style:EG.Style.c.dv}),
							this.dChooserMonth	=EG.CE({tn:"div",onclick:this.chooseMonth	,onclickSrc:this,style:EG.Style.c.dv+"margin-left:10px"})
						]}),
						this.dPre=EG.CE({tn:"div",cls:this.cls+"-top-next",onclick:function(){me.goNext()}})
					]}),

					this.pLeft	=new EG.ui.Panel({region:"left"	,cls:this.cls+"-left",layout:"border",width:70,items:[
						this.pLeftHead=new EG.ui.Panel({region:"top",cls:this.cls+"-left-head",height:30}),
						this.pLeftBody=new EG.ui.Panel({region:"center",cls:this.cls+"-left-body",cn:[
							this.leftBodyTable=EG.CE({tn:"table",cls:this.cls+"-left-table",item:this,cellPadding:this.cellPadding,cellSpacing:this.cellSpacing,border:this.border,cn:[
								this.leftBodyTableBody	=EG.CE({tn:"tbody"})
							]})
						]})
					]}),

					this.pMain	=new EG.ui.Panel({region:"center",layout:"border",cls:this.cls+"-main",items:[
						this.pMainHead=new EG.ui.Panel({region:"top",cls:this.cls+"-main-head",height:30,cn:[
							this.mainHeadTable=EG.CE({tn:"table",cls:this.cls+"-main-head-table",item:this,cellPadding:this.cellPadding,cellSpacing:this.cellSpacing,border:this.border,cn:[
								this.mainHeadTableBody	=EG.CE({tn:"tbody",cn:[{tn:"tr"}]})
							]})
						]}),
						this.pMainBody=new EG.ui.Panel({region:"center",cls:this.cls+"-main-body",cn:[
							this.mainBodyTable=EG.CE({tn:"table",cls:this.cls+"-main-body-table",style:"",item:this,border:0,cellPadding:0,cellSpacing:0,cn:[
								this.mainBodyTableBody	=EG.CE({tn:"tbody"})
							]})
						]})
					]})
				]});

				//创建chooser
				EG.DOM.addChildren(this.p.getElement(),
					this.dChooser=EG.CE({tn:"div",cls:this.cls+"-chooser",style:"position:absolute;"})
				);
				EG.hide(this.dChooser);

				//同步滚动
				EG.bindEvent(this.pMainBody.getElement(),"onscroll",function(){
					me.pLeftBody.getElement().scrollTop=this.scrollTop;
				});

				this.element=this.p.getElement();
			},

			/**
			 * 设置月时自动跳转到目标月
			 * 设置日时自动跳转到目标日
			 */
			setDate:function(date){
				//过滤日期
				date=this.filterDate(date);
				//当前日期
				this.curDate=date;
				//绘制
				this.draw();
			},

			/**
			 * 过滤日期
			 * @param date
			 * @returns {*}
			 */
			filterDate:function(date){
				if(typeof(date)=="string"){
					date=EG.Date.s2d(date);
				}
				return date;
			},
			/** @interface */
			getPreDate:function(){},
			/** @interface */
			getNextDate:function(){},

			goPre:function(){
				this.setDate(this.getPreDate());
			},
			goNext:function(){
				this.setDate(this.getNextDate());
			},
			choose:function(){

			},
			getDate:function(){
				return this.curDate;
			},
			/**
			 * 刷新
			 */
			refresh:function(){
				this.setDate(this.curDate);
			},

			/**
			 * @interface
			 */
			draw:function(){

			},

			go2:function(action){

			},

			render:function(){
				this.p.width	=this.width;
				this.p.height	=this.height;
				this.p.render();
			},

			statics:{
				_events:{

				},

				/**
				 * 加载
				 */
				load:function(){
					if(ME.loaded) return;

					EG.bindEvent(EG.getBody(),"onclick",function(){
						if(ME.chooser&&!EG.Style.isHide(ME.chooser)){
							EG.Style.hide(ME.chooser);
						}
					});

					ME.loaded=true;
				},

				weeks:["日","一","二","三","四","五","六"],

				/**
				 * 创建
				 * @param cfg
				 */
					create:function(cfg){
					if(cfg["mode"]=="M"){
						return new EG.ui.calander.Month(cfg);
					}else if(cfg["mode"]=="W"){
						return new EG.ui.calander.Week(cfg);
					}else if(cfg["mode"]=="D"){
						return new EG.ui.calander.Day(cfg);
					}
				}
			}
		}
	});

	var ME=EG.ui.Calendar;
})();


/**
 * @class EG.ui.Calendar
 * <AUTHOR>
 * @extends EG.ui.Item
 * 日历-月类
 */
(function(){
	/**
	 * Calendar
	 */
	EG.define("EG.ui.calendar.Month",[
		"EG.ui.Item",
		"EG.ui.Calendar"
	],function(Item,Calendar,ME){
		return {
			extend:Calendar,
			config:{

			},
			/**
			 * @constructor 构造函数
			 * @param {Object} cfg 配置
			 */
			constructor:function(cfg){
				this.callSuper([cfg]);
			},

			/**
			 * 创建:周模式
			 */
			build:function(){
				var me=this;

				this.callSuper("build");

				//年月选择
				EG.CE({ele:this.dCur,cn:[
					this.dChooserYear	=EG.CE({tn:"div",onclick:this.chooseYear	,onclickSrc:this,style:EG.Style.c.dv}),
					this.dChooserMonth	=EG.CE({tn:"div",onclick:this.chooseMonth	,onclickSrc:this,style:EG.Style.c.dv+"margin-left:10px"})
				]});

				this.pLeft.setHidden(true);

				//设置头
				for(var i=0;i<7;i++){
					EG.CE({pn:this.mainHeadTableBody.childNodes[0],tn:"td",innerHTML:"周"+PC.weeks[i]});
				}
				var fn=function(){
					if(me.lastSelected){
						EG.Style.removeCls(me.lastSelected,"selected");
					}
					me.lastSelected=this;
					EG.Style.addCls(me.lastSelected,"selected");
				}
				//创建日单元格
				for(var i=0;i<6;i++){
					var tr=EG.CE({pn:this.mainBodyTableBody,tn:"tr"});
					for(var j=0;j<7;j++){
						EG.CE({pn:tr,tn:"td",onclick:fn});
					}
				}
			},

			draw:function(){
				this.callSuper("draw");

				this.dChooserYear.innerHTML	=this.curDate.getFullYear()+"年";
				this.dChooserMonth.innerHTML=(this.curDate.getMonth()+1)+"月";

				var monthday=EG.Date.getMonthday(this.curDate);

				var firstday=EG.clone(this.curDate);
				firstday.setDate(1);
				var firstdayOfWeek=firstday.getDay();
				var tds=this.mainBodyTableBody.getElementsByTagName("td");
				//alert(firstdayOfWeek);
				var sd=EG.clone(this.curDate);
				sd.setDate(1);
				for(var i=0;i<tds.length;i++){
					//alert(i-firstdayOfWeek)
					tds[i].innerHTML="";
					var d=EG.Date.l2d(sd.getTime()+EG.Date.unit_day*(i-firstdayOfWeek));
					var flag=i<firstdayOfWeek?-1:0;
					if(flag==0){
						if(this.curDate.getMonth()!=d.getMonth()){
							flag=1;
						}
					}
					this.drawDay(tds[i],d,flag);
				}
			},
			drawDay:function(td,date,flag){
				//var s=EG.Date.d2s(date,false);
				//alert(date)
				td.innerHTML="<span style='color:"+(flag!=0?"gray":"black")+"'>"+date.getDate()+"</span>";
			},
			render:function(){
				this.callSuper("render");

				Item.fit({element:this.mainHeadTable});	Item.fit({element:this.mainHeadTableBody});
				Item.fit({element:this.mainBodyTable});	Item.fit({element:this.mainBodyTableBody});

				var sMainBodyTable=EG.getSize(this.mainBodyTable);

				//平均TD的宽度与高度,多余的高度和宽度分别会补在最后一行和最后一列上
				var c=this.mainBodyTableBody.childNodes.length;
				var avgH=parseInt(sMainBodyTable.innerHeight/c);
				var avgW=parseInt(sMainBodyTable.innerWidth/7);
				var mH=sMainBodyTable.innerHeight%c;
				var mW=sMainBodyTable.innerWidth%7;

				var tdHeadSize=EG.getSize(this.mainHeadTableBody.childNodes[0].childNodes[0]);
				var tdSize=EG.getSize(this.mainBodyTableBody.childNodes[0].childNodes[0]);

				for(var i=0;i<7;i++){
					var headW=(avgW-(tdHeadSize.outerWidth-tdHeadSize.innerWidth));
					var bodyW=(avgW-(tdSize.outerWidth-tdSize.innerWidth));

					if(i==6&&mW>0){
						headW+=mW;
						bodyW+=mW;
					}
					EG.css(this.mainHeadTableBody.childNodes[0].childNodes[i],"width:"+headW+"px");
					EG.css(this.mainBodyTableBody.childNodes[0].childNodes[i],"width:"+bodyW+"px");

				}

				for(var i=0;i<c;i++){
					var bodyH=(avgH-(tdSize.outerHeight-tdSize.innerHeight));
					if(i==c-1&&mH>0){
						bodyH+=mH;
					}
					EG.css(this.mainBodyTableBody.childNodes[i].childNodes[0],"height:"+bodyH+"px");
				}
			},
			/**
			 * 获取前一个日期
			 */
			getPreDate:function(){
				var d=EG.clone(this.curDate);
				if(d.getMonth==0){
					d.setMonth(11);
					d.setYear(d.getFullYear()-1);
				}else{
					d.setMonth(d.getMonth()-1);
				}
				return d;
			},
			getNextDate:function(){
				var d=EG.clone(this.curDate);
				if(d.getMonth==11){
					d.setMonth(0);
					d.setYear(d.getFullYear()+1);
				}else{
					d.setMonth(d.getMonth()+1);
				}
				this.setDate(d);
				return d;
			},

			getLeft:function(e){
				var offset=e.offsetLeft;
				if(e.offsetParent!=null){
					offset+=this.getLeft(e.offsetParent);
				}
				return offset;
			},

			/**
			 * 选择年
			 * @param e
			 * @param page
			 */
			chooseYear:function(e,page){
				var me=this;
				if(page==null) page=0;

				var pos=EG.Tools.getElementPos(this.dChooserYear,this.element);
				pos.y=this.dChooserYear.clientHeight;

				EG.DOM.removeChilds(this.dChooser);

				EG.CE({ele:this.dChooser,cn:[
					{tn:"div",style:EG.Style.c.dv},
					{tn:"div",style:EG.Style.c.dv}
				]});

				for(var i=5;i>=1;i--){
					EG.CE({pn:this.dChooser.childNodes[0],tn:"a",innerHTML:(this.curDate.getFullYear()-i+page*10),style:"display:block",item:this,onclick:ME._events.aYear.click});
				}

				for(var i=0;i<5;i++){
					EG.CE({pn:this.dChooser.childNodes[1],tn:"a",innerHTML:(this.curDate.getFullYear()+i+page*10),style:"display:block",item:this,onclick:ME._events.aYear.click});
				}
				EG.CE({pn:this.dChooser.childNodes[0],tn:"a",innerHTML:"<-",style:"display:block",item:this,onclick:function(){me.chooseYear(null,page-1);}});
				EG.CE({pn:this.dChooser.childNodes[1],tn:"a",innerHTML:"->",style:"display:block",item:this,onclick:function(){me.chooseYear(null,page+1);}});

				ME.chooser=this.dChooser;

				EG.show(this.dChooser);
				EG.css(this.dChooser,"top:"+pos.y+"px;left:"+((pos.x-EG.getSize(this.dChooser.childNodes[0]).outerWidth/2))+"px");
				EG.Event.stopPropagation(e);
			},

			/**
			 * 选择月
			 * @param e
			 */
			chooseMonth:function(e){
				var pos=EG.Tools.getElementPos(this.dChooserMonth,this.element);
				pos.y=this.dChooserMonth.clientHeight;

				EG.DOM.removeChilds(this.dChooser);

				EG.CE({ele:this.dChooser,cn:[
					{tn:"div",style:EG.Style.c.dv},
					{tn:"div",style:EG.Style.c.dv}
				]});

				for(var i=1;i<=6;i++){
					EG.CE({pn:this.dChooser.childNodes[0],tn:"a",innerHTML:i,style:"display:block",item:this,onclick:ME._events.aMonth.click});
				}
				for(var i=7;i<=12;i++){
					EG.CE({pn:this.dChooser.childNodes[1],tn:"a",innerHTML:i,style:"display:block",item:this,onclick:ME._events.aMonth.click});
				}

				ME.chooser=this.dChooser;

				EG.show(this.dChooser);
				EG.css(this.dChooser,"top:"+pos.y+"px;left:"+((pos.x-EG.getSize(this.dChooser.childNodes[1]).outerWidth/2))+"px");
				EG.Event.stopPropagation(e);
			},
			statics:{
				_events:{
					aYear:{
						click:function(){
							var d=EG.clone(this.item.curDate);
							d.setYear(parseInt(this.innerHTML));
							this.item.setDate(d);
							EG.hide(this.item.dChooser);
						}
					},
					aMonth:{
						click:function(){
							var d=EG.clone(this.item.curDate);
							d.setMonth(parseInt(this.innerHTML)-1);
							this.item.setDate(d);
							EG.hide(this.item.dChooser);
						}
					}
				}
			}
		};
	});

	var ME=EG.ui.calendar.Month;
	var PC=EG.ui.Calendar;
})();

/**
 * @class EG.ui.calander.Week
 * <AUTHOR>
 * @extends EG.ui.Item
 * 日历-周类
 */
(function(){
	/**
	 * Calander
	 */
	EG.define("EG.ui.calendar.Week",{
		extend:"EG.ui.Calendar",
		config:{

		},

		/**
		 * @constructor 构造函数
		 * @param {Object} cfg 配置
		 */
		constructor:function(cfg){
			this.callSuper([cfg]);
		},

		/**
		 * 创建:周模式
		 */
		build:function(){
			var me=this;

			this.callSuper("build");

			//年月选择
			EG.CE({ele:this.dCur,cn:[
				this.dChooserYear	=EG.CE({tn:"div",onclick:this.chooseYear	,onclickSrc:this,style:EG.Style.c.dv}),
				this.dChooserMonth	=EG.CE({tn:"div",onclick:this.chooseMonth	,onclickSrc:this,style:EG.Style.c.dv+"margin-left:10px"})
			]});

			//计算数量
			var count=24*60/this.scale;
			var d=new Date();
			d.setMinutes(0);
			d.setHours(0);
			d.setSeconds(0);
			//alert(count);
			for(var i=0;i<count;i++){
				var h=parseInt((i*this.scale)/60);
				var m=(i*this.scale)%60;
				EG.CE({pn:this.leftBodyTableBody,tn:"tr",cn:[
					{tn:"td",style:"height:25px;line-height:25px;",innerHTML:(h+":"+m)}
				]});

				var tr=EG.CE({pn:this.mainBodyTableBody,tn:"tr"});
				for(var j=0;j<7;j++){
					EG.CE({pn:tr,tn:"td",style:"height:25px;line-height:25px;"});
				}
			}

			//TODO 计算当前周
			for(var i=0;i<7;i++){
				EG.CE({pn:this.mainHeadTableBody.childNodes[0],tn:"td",innerHTML:  "周"+PC.weeks[i]});
			}
			EG.CE({pn:this.mainHeadTableBody.childNodes[0],tn:"td"});
			//alert("OK");
			//创建右侧对应的TD,并且绑定其滚动
			//TODO
			//alert("待实现");	//TODO
		},

		draw:function(){
			this.callSuper("draw");

			this.dChooserYear.innerHTML	=this.curDate.getFullYear()+"年";
			this.dChooserMonth.innerHTML=(this.curDate.getMonth()+1)+"月";

//			var dates=[];
//			for(var i=0;i<7;i++){
//				dates.push(i+EG.Date.l2d(this.curDate.getTime()+EG.Date.unit_day*(i-this.curDate.getDay())));
//			}
//
//			var monthday=EG.Date.getMonthday(this.curDate);
//
//			var firstday=EG.clone(this.curDate);
//			firstday.setDate(1);
//			var firstdayOfWeek=firstday.getDay();
//			var tds=this.mainBodyTableBody.getElementsByTagName("td");
//			//alert(firstdayOfWeek);
//			var sd=EG.clone(this.curDate);
//			sd.setDate(1);
//			for(var i=0;i<tds.length;i++){
//				//alert(i-firstdayOfWeek)
//				tds[i].innerHTML="";
//				var d=EG.Date.l2d(sd.getTime()+EG.Date.unit_day*(i-firstdayOfWeek));
//				var flag=i<firstdayOfWeek?-1:0;
//				if(flag==0){
//					if(this.curDate.getMonth()!=d.getMonth()){
//						flag=1;
//					}
//				}
//				this.drawCol(tds[i],d,flag);
//			}
		},
		drawCol:function(td,date,flag){

		},

		/**
		 *
		 * @param {Date} from 从
		 * @param {Date} to 到
		 * @param ele 元素
		 */
		addBlock:function(from,to,ele){
			if(EG.Date.sameDay(from,to)){
				var day=from.getDay();

				//计算遮盖尺寸，计算位置
				var aimTd	=this.mainHeadTableBody.childNodes[0].childNodes[day];
				var size	=EG.getSize(aimTd)
				//宽度
				var width	=size.innerWidth;
				var left	=aimTd.offsetLeft;
				//alert(this.mainBodyTableBody.childNodes.length)
				var top		=this.mainBodyTableBody.childNodes[this.getRowIdx(from)].offsetTop;	//TODO check  wether is Empty
				var height	=this.mainBodyTableBody.childNodes[this.getRowIdx(to)].offsetTop-top;
				alert(top+","+left+","+width+","+height)
				//定位，改变尺寸
				this.pMainBody.getElement().appendChild(ele);
				Item.fit({
					element:ele,
					pSize:{
						width:width,
						height:height
					}
				})
				EG.css(ele,"position:absolute;top:"+top+"px;left:"+left+"px");
				EG.bindEvent(ele,"onmouseover",function(){
					//显示拉伸杆和虚线



				});
				EG.bindEvent(ele,"onmouseout",function(){
					//显示拉伸杆和虚线
				});
				EG.bindEvent(ele,"onclick",function(){
					//选中状态，设定为当前可移动的状态
				});
			}else{




			}

		},

		/**
		 *
		 * @param {Date} date
		 */
		getRowIdx:function(date){
			return (date.getHours()*60+date.getMinutes())/this.scale;
		},

		render:function(){
			this.callSuper("render");

			Item.fit({element:this.leftBodyTable});	Item.fit({element:this.leftBodyTableBody});
			Item.fit({element:this.mainHeadTable});	Item.fit({element:this.mainHeadTableBody});
			Item.fit({element:this.mainBodyTable});	Item.fit({element:this.mainBodyTableBody});


			var sizeMainBodyTableBody=EG.getSize(this.mainBodyTableBody);
			var sizeMainHeadTableBody=EG.getSize(this.mainHeadTableBody);

			//要用Body的内部宽度r
			var mW=sizeMainBodyTableBody.innerWidth%7;
			var bTdSize=EG.getSize(this.mainBodyTableBody.childNodes[0].childNodes[0]);var bAvgW=parseInt(sizeMainBodyTableBody.innerWidth/7)-(bTdSize.outerWidth-bTdSize.innerWidth);var bTds=this.mainBodyTableBody.childNodes[0].childNodes;
			var hTdSize=EG.getSize(this.mainHeadTableBody.childNodes[0].childNodes[0]);var hAvgW=parseInt(sizeMainBodyTableBody.innerWidth/7)-(hTdSize.outerWidth-hTdSize.innerWidth);var hTds=this.mainHeadTableBody.childNodes[0].childNodes;

			for(var i=0;i<7;i++){
				EG.css(bTds[i],"width:"+(bAvgW+(i==6?mW:0))+"px");
				EG.css(hTds[i],"width:"+(hAvgW+(i==6?mW:0))+"px");
			}
			EG.getSize(hTds[7],"width:"+this.mainHeadTable.innerWidth-7*hAvgW+"px");
		},
		statics:{
			_events:{
				aYear:{
					click:function(){
						var d=EG.clone(this.item.curDate);
						d.setYear(parseInt(this.innerHTML));
						this.item.setDate(d);
					}
				},
				aMonth:{
					click:function(){
						var d=EG.clone(this.item.curDate);
						d.setMonth(parseInt(this.innerHTML)-1);
						this.item.setDate(d);
					}
				}
			}
		}
	});

	var ME=EG.ui.calendar.Week;
	var PC=EG.ui.Calendar;
})();

(function(){
	/**
	 * EG.ui.Slide 滑动块
	 */
	EG.define("EG.ui.Slide",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			alias:"slide",
			extend:Item,
			config:{
				onchange	:null,				//变化事件
				cls			:"eg_slide",		//样式类
				range_start	:0,
				range_end	:100,
				doubleEnable:false,
				/** @cfg {Boolean?} showScale 显示刻度 */
				showScale	:false,
				/** @cfg {Boolean?} showTip 显示提示 */
				showTip		:false,
				/** @cfg {String?} tipStyle 提示样式 */
				tipStyle	:null,
				/** @cfg {Function?} onSlideUp 值变动事件 */
				onSlideUp	:null

			},

			constructor:function(cfg){
				this.callSuper([cfg]);
			},

			/**
			 * 创建
			 */
			build:function(){
				//创建Element
				this.element=EG.CE({tn:"div",cls:this.cls,item:this,
					cn:[
						this.el_tip			=EG.CE({tn:"div",cls:this.cls+"-tip"		,style:"position:absolute;",item:this,innerHTML:this.range_start||" "}),
						this.el_background	=EG.CE({tn:"div",cls:this.cls+"-background"	,style:"position:absolute;",item:this}),
						this.el_block		=EG.CE({tn:"div",cls:this.cls+"-block"		,style:"position:absolute"})
					],
					onmousemove :ME._events.element.onmousemove,
					onmouseup	:ME._events.element.onmouseup
				});

				if(this.tipStyle){
					EG.css(this.el_tip,this.tipStyle);
				}

				this.builded=true;
			},

			/**
			 * 设值
			 * @param {Number} value 数值
			 */
			setValue:function(value){
				this._value=value;
				var size=this.value2Size(value);
				EG.css(this.el_block,"margin-left:"+size+"px");
				if(this.showTip){
					var size_block	=EG.getSize(this.el_block);
					var size_tip	=EG.getSize(this.el_tip);
					EG.css(this.el_tip,"margin-left:"+(size+(size_block.clientWidth/2-size_tip.clientWidth/2))+"px");
					this.el_tip.innerHTML=value;
				}
			},

			/**
			 * 获值
			 */
			getValue:function(){
				return this._value;
			},

			destroy:function(){

			},

			/**
			 * 尺寸转换数值
			 * @param size
			 * @returns {number}
			 */
			size2Value:function(size){
				var w=this.el_background.clientWidth;
				var v=this.range_start+(size/w)* (this.range_end-this.range_start);
				if(!this.doubleEnable){
					v=parseInt(v);
				}
				if(v<this.range_start){
					v=this.range_start;
				}else if(v>this.range_end){
					v=this.range_end;
				}
				return v;
			},

			/**
			 * 数值转尺寸
			 * @param value
			 * @returns {number}
			 */
			value2Size:function(value){
				var w=this.el_background.clientWidth;
				var v=value/(this.range_end-this.range_start);
				return w*v;
			},

			/**
			 * 渲染
			 */
			render:function(){

				//
				Item.fit(this);

				var size=EG.getSize(this.getElement());

				var unit=size.innerHeight/2;

				//
				Item.fit({
					element:this.el_background,
					pSize:size,
					dSize:{
						width:size.innerWidth-unit*2,
						height:unit
					}
				});
				//var size_background=EG.getSize(this.el_background);
				EG.css(this.el_background,"margin-top:"+(unit/2)+"px;border-radius:"+unit/2+"px;margin-left:"+unit+"px");

//
				var size_block=EG.getSize(this.el_block);
				EG.css(this.el_block,{
					width			:size.innerHeight-(size_block.borderLeft+size_block.borderRight)  +"px",
					height			:size.innerHeight-(size_block.borderTop+size_block.borderBottom)+"px",
					"border-radius"	:unit+"px"
				});
				size_block=EG.getSize(this.el_block);

				if(this.showTip){
					EG.show(this.el_tip);
					EG.css(this.el_tip,"width:"+unit+"px;height:"+unit+"px;margin-top:-"+unit+"px;line-height:"+unit+"px");
					var size_tip=EG.getSize(this.el_tip);
					EG.css(this.el_tip,"margin-left:"+(size_block.clientWidth/2-size_tip.clientWidth/2)+"px");
				}else{
					EG.hide(this.el_tip);
				}
			},

			statics:{
				/**
				 * 事件
				 */
				_events:{
					//选项
					element:{
						onmousemove:function(e){
							var me=this.item;
							if(EG.Tools.isPressLeft(e)){

								if(!ME._startMousePos){
									ME._startMousePos	=EG.Tools.getMousePos(e,this);
									ME._startBlockSize	=EG.getSize(me.el_block);
								}

								var pos		=EG.Tools.getMousePos(e,this);
								var left	=pos.x-ME._startMousePos.x+ME._startBlockSize.marginLeft;

								//边界
								if(left<0){
									left=0;
								}
								var size		=EG.getSize(me.element);
								var size_block	=EG.getSize(me.el_block);
								if(left>=(size.innerWidth-size_block.clientWidth)){
									left=size.innerWidth-size_block.clientWidth;
								}
								EG.css(me.el_block,"margin-left:"+left+"px");

								//计算数值显示刻度
								var size_background	=EG.getSize(me.el_background);
								var distance=(left+size_block.clientWidth/2)-size_background.marginLeft;
								var value=me.size2Value(distance);
								me.setValue(value);
							}
							EG.Event.stopPropagation(e);
						},
						onmouseup:function(e){
							var me=this.item;
							if(EG.Tools.isPressLeft(e)){
								var value=me.getValue();
								if(me.onSlideUp){
									me.onSlideUp.apply(this,[value]);
								}
							}
							ME._startPos=null;
						}
					}
				}
			}
		};
	});
})();

(function(){
	/**
	 * EG.ui.Switch 开关
	 */
	EG.define("EG.ui.Switch",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			alias:"switch",
			extend:Item,
			config:{
				onchange	:null,				//变化事件
				cls			:"eg_switch",		//样式类
				range_start	:0,
				range_end	:100,
				doubleEnable:false,

				textvalues	:[],

				/** @cfg {Object?} openValue 打开值 */
				openValue	:null,
				/** @cfg {Object?} closeValue 打开值 */
				closeValue	:null,
				/** @cfg {String?} openText 打开文字 */
				openText	:"打开",
				/** @cfg {String?} closeText 关闭文字 */
				closeText	:"关闭",
				/** @cfg {Boolean?} closeText 关闭文字 */
				showText	:false,
				/** @cfg {Number} marginSpacing 间隙 */
				marginSpacing :3,
				/** @cfg {String?} textStyle 文字样式 */
				textStyle	:null
			},

			constructor:function(cfg){

				//
				var textvalues=cfg["textvalues"];
				if(textvalues){
					if(this.openValue==null){
						this.openValue=textvalues[0][1];
					}

					if(this.closeValue==null){
						this.closeValue=textvalues[1][1];
					}

					if(this.openText==null){
						this.openText=textvalues[0][0];
					}

					if(this.closeText==null){
						this.closeText=textvalues[1][0];
					}
				}

				this.callSuper([cfg]);

			},

			/**
			 * 创建
			 */
			build:function(){
				var me =this;

				//创建Element
				this.element=EG.CE({tn:"div",cls:this.cls,item:this,
					cn:[
						this.el_background	=EG.CE({tn:"div",cls:this.cls+"-background-close",style:"position:absolute;",item:this}),
						this.el_block		=EG.CE({tn:"div",cls:this.cls+"-block",style:"position:absolute",onclick:function(){
							var opVal=me.getOppositeValue(me.getValue());
							me.setValue(opVal);
						}}),
						this.el_text		=EG.CE({tn:"div",cls:this.cls+"-text",style:"position:absolute;text-align:center;",onclick:function(){

						}})
					]
				});

				if(this.textStyle){
					EG.css(this.el_text,this.textStyle);
				}

				this.builded=true;
			},

			/**
			 * 获取反向数值
			 * @returns {*}
			 */
			getOppositeValue:function(value){
				return value==this.openValue?this.closeValue:this.openValue;
			},

			/**
			 * 设值
			 * @param {Number} value 数值
			 */
			setValue:function(value){
				this._value=value;
				this.render();
			},

			/**
			 * 获值
			 */
			getValue:function(){
				return this._value;
			},

			destroy:function(){

			},

			/**
			 * 切换
			 */
			doSwitch:function(){
				this.setValue(this.getOppositeValue(this.getValue()));
			},

			/**
			 * 获取标题
			 * @param {String} value 数值
			 * @param {Object} data 数据集
			 * @param {Object} cfg 扩展
			 * @return {String}
			 */
			getTitle:function(value,data,cfg){
				if(value==this.openValue){
					return this.openText;
				}else if(value==this.closeValue){
					return this.closeText;
				}
				return "";
			},

			/**
			 * 渲染
			 */
			render:function(){

				//
				var value=this.getValue();

				//
				Item.fit(this);

				var size=EG.getSize(this.element);

				//背景
				EG.Style.setCls(this.el_background,this.cls+"-background-"+(value==this.openValue?"open":"close"));
				Item.fit({
					element:this.el_background,
					pSize:size
				});
				EG.css(this.el_background,"border-radius:"+size.innerHeight/2+"px");
				var size_background=EG.getSize(this.el_background);

				//块
				EG.css(this.el_block,{
					"width"			:(size.innerHeight-this.marginSpacing*2)+"px",
					"height"		:(size.innerHeight-this.marginSpacing*2)+"px",
					"border-radius"	:size.innerHeight/2+"px",
					"margin-top"	:this.marginSpacing+"px",
					"margin-left"	:this.marginSpacing+"px",
				});
				var size_block=EG.getSize(this.el_block);
				EG.css(this.el_block,"margin-top:"+this.marginSpacing+"px;");

				//左
				var block_margin	=size_background.innerWidth-size_block.clientWidth;
				if(value==this.openValue){
					EG.css(this.el_block,"margin-left:"+(block_margin-this.marginSpacing)+"px");
				}else{
					EG.css(this.el_block,"margin-left:"+(this.marginSpacing)+"px");
				}

				//文字
				var text_width=(size_background.innerWidth-size_block.clientWidth-this.marginSpacing*2);
				EG.css(this.el_text,"width:"+text_width+"px;height:"+size.innerHeight+"px;line-height:"+size.innerHeight+"px");
				if(this.showText){
					EG.hide(this.el_text);
				}else{
					EG.show(this.el_text);
				}
				if(value==this.openValue){
					EG.CE({ele:this.el_text,cls:this.cls+"-text-open",style:"margin-left:"+(this.marginSpacing)+"px",innerHTML:this.openText});
				}else{
					EG.CE({ele:this.el_text,cls:this.cls+"-text-close",style:"margin-left:"+(size_block.clientWidth+this.marginSpacing*2)+"px",innerHTML:this.closeText});
				}
			}
		};
	});
})();

(function(){
	EG.define("EG.ui.JSONView",[
		"EG.ui.Item"
	],function(Item,ME){
		return {
			alias:"jsonView",
			extend:Item,
			constructor:function(cfg){
				this.callSuper([cfg]);
			},

			build:function(){
				this.element=EG.CE({tn:"div"});
			},

			setValue:function(value){
				this.value=value;
				this.element.innerHTML=this.getObjHtml(value);
			},

			getValue:function(){
				return this.value;
			},

			getIndentSpace:function(indent){
				var s="";
				for(var i=0;i<indent*4;i++){
					s+="&nbsp;";
				}
				return s;
			},

			getObjHtml:function(obj,indent,prekey){
				if(indent==null){
					indent=0;
				}

				if(prekey==null){
					prekey=false;
				}

				var style_brace		="color:#00AA00";
				var style_bracket	="color:#0033FF";
				var style_null		="color:#AA00AA";
				var style_string	="color:#007777";
				var style_key		="color:#CC0000";
				var style_number	="color:#AA00AA";
				var style_boolean	="color:#0033FF";

				var objType=EG.getType(obj);

				var str="";

				if(obj==null){
					str="<span style=\""+style_null+"\">"+"null"+"</span>";
				}else if(typeof(obj)=="string"){
					str= "<span style=\""+style_string+"\">"+'"'+obj+'"'+"</span>";
				}else if(typeof(obj)=="number"){
					str= "<span style=\""+style_number+"\">"+obj+"</span>";
				}else if(typeof(obj)=="boolean"){
					str= "<span style=\""+style_boolean+"\">"+obj+"</span>";
				}else if(obj instanceof Object){
					if(EG.Array.isArray(obj)){
						str="<span style=\""+style_bracket+"\">[</span><br/>";
						for(var i=0;i<obj.length;i++){
							if(i>0){
								str+=",<br/>";
							}
							str+=this.getIndentSpace(indent+1)+this.getObjHtml(obj[i],indent+1);
						}

						str+="<br/>";

						str+="<span style=\""+style_bracket+"\">"+this.getIndentSpace(indent)+"]</span>";
					}else{
						str="<span style=\"display-line:block;"+style_brace+"\">{</span><br/>";
						var i=0;
						for(var k in obj){
							if(i>0){
								str+=",<br/>";
							}
							str+=this.getIndentSpace(indent+1)+"<span style=\""+style_key+"\">\""+k+"\"</span>"+":"+this.getObjHtml(obj[k],indent+1,true);
							i++;
						}

						str+="<br/>";

						str+="<span style=\""+style_brace+"\">"+this.getIndentSpace(indent)+"}</span>";
					}
				}else{
					str="#δ֪��ʽgetObjHtml#";
				}

				return str;
			},

			render:function(){
				Item.fit(this);
			}

		};
	});
})();
