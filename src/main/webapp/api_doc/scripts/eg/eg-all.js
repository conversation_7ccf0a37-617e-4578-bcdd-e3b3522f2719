(function(){if(typeof(_EG_NS_)=="undefined"){_EG_NS_=window}var EG=_EG_NS_.EG=window.EG=function(){};EG._className="EG";EG.debug=true;EG.onError=function(sMsg,sUrl,sLine){if(EG.debug){var errorMsg="<div style='text-align:left'><b>原因</b>:"+sMsg+"<br/>";if(sUrl!=null){errorMsg+="<b>行数</b>:"+sLine+"<br/>"}if(sLine!=null){errorMsg+="<b>URL</b>:"+sUrl+"<br/></div>"}if(EG.Locker){EG.Locker._lock=true;EG.Locker.message({message:errorMsg,closeable:true,force:true})}else{alert(errorMsg)}}};EG.doc=document;EG.body=null;EG.getBody=function(){if(!EG.body){EG.body=EG.doc.body}return EG.body};EG.head=null;EG.getHead=function(){if(!EG.head){EG.head=EG.doc.head}return EG.head};EG.unnull=function(){for(var i=0;i<arguments.length;i++){if(arguments[i]!=null){return arguments[i]}}return null};EG.n2d=function(o,d){if(o==null){return d}else{return o}};EG.f2o=function(o){if(o==null){return null}if(typeof(o)=="function"){return o()}else{return o}};EG.getScriptPath=function(scriptName){var js=EG.doc.scripts;var jsPath;for(var i=js.length;i>0;i--){if(js[i-1].src.indexOf(scriptName)>-1){jsPath=js[i-1].src.substring(0,js[i-1].src.lastIndexOf("/")+1);if(jsPath.indexOf("http://")>=0){jsPath=jsPath.substring(jsPath.indexOf("/",10))}return jsPath}}return null};var scriptPath="eg";EG.basePath=window._EG_BASEPATH_||EG.getScriptPath(scriptPath);EG.contextPath="";EG.copy=function(obj,props,override,clone){for(var key in props){if(override||typeof(obj[key])=="undefined"){if(clone){obj[key]=EG.clone(props[key])}else{obj[key]=props[key]}}}return obj};EG.getType=function(o){return Object.prototype.toString.call(o).slice(8,-1).toLowerCase()};var types={"undefined":1,"null":1,number:1,"boolean":1,string:1,array:2,"function":3,date:4};EG._clone=function(dObj,sObj,key){var val=sObj[key];var type=types[EG.getType(val)];if(1==type){dObj[key]=val}else{if(4==type){dObj[key]=new Date();dObj[key].setTime(val.getTime())}else{dObj[key]=EG.clone(val)}}};EG.clone=function(obj){if(obj==null){return null}if(typeof(obj)=="undefined"){throw new Error("EG.clone#待clone参数未定义")}var cObj,typeNum,i,il,fn_body,fn_args;var type=EG.getType(obj);typeNum=types[type];cObj=obj;if(!typeNum){if(type!="object"){}else{if(obj==null||obj.constructor==null){alert(obj)}if(obj.constructor!=Object){cObj=obj}else{cObj=new obj.constructor();for(i in obj){EG._clone(cObj,obj,i)}}}}else{if(2==typeNum){cObj=[];for(i=0,il=obj.length;i<il;i++){EG._clone(cObj,obj,i)}}else{if(3==typeNum){cObj=obj}else{if(4==typeNum){cObj=new Date();cObj.setTime(obj.getTime())}}}}return cObj};EG._markLoadPath=false;EG.Repository=function(container){if(!container){container={}}this.container=container};EG.Repository.prototype={put:function(key,data){var ks=key.split("."),obj=this.container;for(var i=0,il=ks.length;i<il;i++){if(i==(il-1)){obj[ks[i]]=data}else{if(!obj[ks[i]]){obj[ks[i]]={}}obj=obj[ks[i]]}}},get:function(key){var ks=key.split("."),obj=this.container;for(var i=0,il=ks.length;i<il;i++){if(!obj[ks[i]]){return null}obj=obj[ks[i]]}return obj},defineNamespace:function(namespace,fn){if(!fn){fn=EG._newConst()}var ss=namespace.split(".");var p=this.container;for(var i=0;i<ss.length;i++){if(!p[ss[i]]){if(i<ss.length-1){p[ss[i]]=EG._newConst();p[ss[i]]._undefined=true}else{p[ss[i]]=fn}}p=p[ss[i]]}return p}};EG._defaultRepository=new EG.Repository(_EG_NS_);EG.put=function(key,data){EG._defaultRepository.put(key,data)};EG.get=function(key){return EG._defaultRepository.get(key)};EG.Loader=function(cfg){if(!cfg){cfg={}}this.repository=cfg.repository||EG._defaultRepository};EG.Loader.prototype={_isLoader:true,load:function(cfg,callback,onError){var me=this;cfg=this.convert(cfg);var key=cfg.key||cfg.path;var type=cfg.type;var path=cfg.path;var r=this.find(key);if(r){return callback(r,cfg)}else{if(callback){me.onReady(cfg,function(c){callback(c,cfg)})}}var url=this.getUrl(type,key,path);if(type=="class"){if(!EG.Browser.isLocal()){EG.Ajax.send({url:url,method:"GET",callback:function(text){if(EG._markLoadPath){text=("//@ sourceURL="+url+"\n"+text)}eval(text)},erhandler:onError})}else{var el=EG.CE({tn:"script",type:"text/javascript",charset:"UTF-8",src:url});EG.getHead().appendChild(el)}}else{if(type=="text"){EG.Ajax.send({url:url,method:"GET",callback:function(text){var obj={path:url,item:text};me.repository.put(key,obj);me.doAfterReady(key,obj)},erhandler:onError})}else{if(type=="ele"){EG.Ajax.send({url:url,method:"GET",callback:function(text){var ele=EG.CE({tn:"div"});ele.innerHTML=text;var obj={path:url,item:ele.childNodes[0]};me.repository.put(key,obj);me.doAfterReady(key,obj)},erhandler:onError})}else{if(type=="img"){var img=EG.CE({tn:"img",onload:function(event){var obj={path:url,item:this};me.repository.put(key,obj);me.doAfterReady(key,obj)},onerror:function(){if(onError){onError.apply(this,arguments)}}});img.src=url}}}}},getUrl:function(type,key,path){if(type=="class"){return this.getClassUrl(key)}else{return path}},getClassUrl:function(className){var ss=className.split(".");ss.shift();var classFileName=ss[ss.length-1];ss.pop();return EG.basePath+"src/"+ss.join("/").toLowerCase()+"/"+classFileName+".js"},find:function(key){if(typeof(key)!="string"){key=key.key||key.path}var d=this.repository.get(key);if(d==null){return null}return d},isLoaded:function(cfg){var d=this.find(cfg);if(d==null||d._defineUnfinish){return false}return true},convert:function(cfg){if(typeof(cfg)=="string"){cfg={key:cfg,type:"class"}}return cfg},readyEvents:{},onReady:function(cfg,callback){cfg=this.convert(cfg);var key=cfg.key||cfg.path;var d=this.find(key);if(d){callback(d,cfg)}else{if(!this.readyEvents[key]){this.readyEvents[key]=[]}this.readyEvents[key].push(callback)}},doAfterReady:function(key,obj){if(this.readyEvents[key]){var ev;while((ev=this.readyEvents[key].shift())){ev(obj)}}},require:function(reqs,onFinish,onError){var me=this;if(!reqs.reqEd){reqs.reqEd=[]}if(reqs.length==0){var rs=[];for(var i=0;i<reqs.reqEd.length;i++){var ed=reqs.reqEd[i];var r=this.find(ed);if(r==null){throw new Error("未找到："+ed)}rs.push(r)}return onFinish.apply(this,rs)}var req=reqs.shift();reqs.reqEd.push(req);var fn=function(){if(me.isLoaded(req)){return me.require(reqs,onFinish,onError)}else{return me.load(req,function(){return me.require(reqs,onFinish,onError)},onError)}};var clazz=this.find(req);if(clazz!=null&&clazz._defineUnfinish){this.onReady(req,fn)}else{fn()}}};EG._defaultLoader=new EG.Loader();EG.onReady=function(){EG._defaultLoader.onReady.apply(EG._defaultLoader,arguments)};EG.nativeMethod={initConfig:function(cfg){if(cfg){for(var key in cfg){var cK=key+"Config";if(typeof(this[cK])!="undefined"){this[cK]=cfg[key]}else{this[key]=cfg[key]}}}},getSuperclass:function(){return this.getClass()._superClass},callSuper:function(name,args){var o;var fn=arguments.callee.caller;var sc=(fn._className?fn:fn._class)._superClass;if(!sc){throw new Error("没有父类")}var methodName,params;if(arguments.length==0){methodName="$constructor";params=[]}else{if(arguments.length==1){if(typeof(arguments[0])=="string"){methodName=arguments[0];params=[]}else{methodName="$constructor";params=arguments[0]}}else{methodName=arguments[0];params=arguments[1]}}if(methodName=="$constructor"){o=sc.apply(this,params)}else{o=sc.prototype[methodName].apply(this,params)}return o},getClass:function(){return this._class}};EG._newConst=function(){return function(){var c=arguments.callee;var args=arguments;if(c._config){EG.copy(this,EG.clone(c._config))}if(c._constructor){if(c._beforeConstructor){var me=this;return c._beforeConstructor.apply(this,[function(){return c._constructor.apply(me,args)}])}else{return c._constructor.apply(this,args)}}}};EG._alias={};EG._defineClass=function(cfg,clazz,name,loader){if(!cfg){cfg={}}var statics=cfg.statics,config=cfg.config||{},constructor=cfg.constructor,extend=cfg.extend,beforeConstructor=cfg.beforeConstructor;if(config){config=EG.clone(config)}if({}.constructor==constructor){constructor=null}if(constructor){constructor._class=clazz}clazz._constructor=constructor;clazz._config=config;clazz._beforeConstructor=beforeConstructor;if(name){clazz._className=name}if(extend){if(typeof(extend)=="string"){extend=loader.find(extend)}var f=function(){};f.prototype=extend.prototype;clazz.prototype=new f();clazz._superClass=extend;if(extend._config){EG.copy(config,EG.clone(extend._config),false)}}var prototyps=clazz.prototype;EG.copy(prototyps,EG.nativeMethod);prototyps._class=clazz;for(var key in cfg){if(key!="extend"&&key!="alias"&&key!="statics"&&key!="config"&&key!="constructor"&&key!="beforeConstructor"&&key!="require"){prototyps[key]=cfg[key];if(typeof(cfg[key])=="function"){prototyps[key]._class=clazz}}}if(statics){EG.copy(clazz,statics,true)}if(extend){if(extend.afterExtend){extend.afterExtend(clazz)}}clazz._defineUnfinish=false;delete clazz._defineUnfinish;if(cfg.alias){EG._alias[cfg.alias]=clazz}if(name){loader.doAfterReady(name,clazz)}return clazz};EG.define=function(){var name,reqs,cfg,loader;for(var i=0;i<arguments.length;i++){var arg=arguments[i];var type=EG.getType(arg);if(!name){if(type=="string"){name=arg;continue}}if(!reqs){if(type=="array"){reqs=arg;continue}}if(!cfg){if(type=="object"||type=="function"){cfg=arg;continue}}if(!loader){if(arg._isLoader){loader=arg;continue}}}loader=loader||EG._defaultLoader;reqs=reqs||[];var clazz;if(name){clazz=loader.find(name);if(!clazz){clazz=loader.repository.defineNamespace(name,clazz)}else{if(clazz._undefined===false){throw new Error("类已定义:"+name)}}}else{clazz=EG._newConst()}clazz._defineUnfinish=true;if(typeof(cfg)=="function"){loader.require(reqs,function(){var args=[];for(var i=0;i<arguments.length;i++){args.push(arguments[i])}args.push(clazz);cfg=cfg.apply(this,args);return EG._defineClass(cfg,clazz,name,loader)});return clazz}else{var extend=cfg.extend;var require=cfg.require;if(typeof(extend)=="string"){reqs.push(extend)}if(require){reqs=reqs.concat(require)}loader.require(reqs,function(){return EG._defineClass(cfg,clazz,name,loader)});return clazz}};EG.isInstanceof=function(obj,pClazz){if(obj==null){return false}return EG.isAssignableFrom(obj.getClass(),pClazz)};EG.isAssignableFrom=function(clazz,pClazz){if(clazz==pClazz){return true}while(clazz._superClass){clazz=clazz._superClass;if(clazz==pClazz){return true}}return false};EG.findClass=function(classNameExp){return EG.findClasses(classNameExp,false)[0]};EG.findClasses=function(classNameExp,returnObj,pClass,classes,packageName){classes=classes||((returnObj)?{}:[]);pClass=pClass||_EG_NS_;packageName=packageName||"";if(!classNameExp){return classes}var classEles=classNameExp.split(".");var classNamePattern=classEles.shift().replace("*",".*");for(var key in pClass){if((typeof(pClass[key])=="function"||typeof(pClass[key])=="object")&&new RegExp("^"+classNamePattern+"$","g").test(key)){var fullKey=packageName?(packageName+"."+key):key;if(classEles.length>0){EG.findClasses(classEles.join("."),returnObj,pClass[key],classes,fullKey)}else{if(returnObj){classes[fullKey]=(pClass[key])}else{classes.push(pClass[key])}}}}return classes};EG.findMethods=function(clazz,methodNameExp,rangeType,returnObj){var ms=((returnObj)?{}:[]);rangeType=rangeType||"all";methodNameExp=methodNameExp.replace("*",".*");var f=function(range){for(var key in range){if(typeof(range[key])=="function"&&new RegExp("^"+methodNameExp+"$","g").test(key)){if(returnObj){ms[key]=range[key]}else{ms.push(range[key])}}}};if(rangeType=="all"||rangeType=="prototype"){f(clazz.prototype)}if(rangeType=="all"||rangeType=="static"){f(clazz)}return ms};EG.isAssignbleFrom=function(obj,klass){var sc=obj.getClass();while(sc=sc._superClass){if(sc==klass){return true}}return false};EG.put=function(key,data){var ks=key.split("."),obj=_EG_NS_;for(var i=0,il=ks.length;i<il;i++){if(i==(il-1)){obj[ks[i]]=data}else{if(!obj[ks[i]]){obj[ks[i]]={}}obj=obj[ks[i]]}}};EG.get=function(key){var ks=key.split("."),obj=_EG_NS_;for(var i=0,il=ks.length;i<il;i++){if(!obj[ks[i]]){return null}obj=obj[ks[i]]}return obj};EG._importCache={};EG.importPath=function(keys,flag){};EG.importPath_static=function(){return EG.importPath(arguments,2)};EG.importPath_class=function(){return EG.importPath(arguments,1)};EG.showSource=function(text){if(!EG.showSourceEle){EG.showSourceEle=EG.CE({tn:"div",style:"position:absolute;z-index:9999",cn:[{tn:"textarea",style:"width:500px;height:500px"},{tn:"button",innerHTML:"hide",onclick:function(){EG.hide(EG.showSourceEle)}}]});EG.getBody().appendChild(EG.showSourceEle)}EG.showSourceEle.childNodes[0].value=text;EG.show(EG.showSourceEle)}})();(function(){EG.define("EG.String",function(a){return{statics:{isString:function(b){return typeof(b)==="string"},n2e:function(b){return b!=null?b:""},isBlank:function(b){return(b==null||b===""||a.trim(b)==="")},isEmpty:function(b){return(b==null||b===""||a.trim(b)==="")},equals:function(e,c,b){return b==true?e.toUpperCase()===c.toUpperCase():e===c},startWith:function(g,f,c){if(g==null||f==null){return false}var b=f.length;if(g.length<b){return false}var e;if(c){for(e=0;e<b;e++){if(f.charAt(e).toLocaleLowerCase()!=g.charAt(e).toLocaleLowerCase()){return false}}}else{for(e=0;e<b;e++){if(f.charAt(e)!=g.charAt(e)){return false}}}return true},endWith:function(e,c,b){return(e!==null&&c!==null&&(b==true?e.toUpperCase().lastIndexOf(c.toUpperCase())>0:e.lastIndexOf(c)>=(e.length-c.length)))},trim:function(b){return b.replace(/(^\s*)|(\s*$)/g,"")},removeEnd:function(c,b){return(a.endWith(c,b))?c.substring(0,c.length-b.length):c},removeStart:function(c,b){return(a.startWith(c,b))?c.substring(b.length,c.length):c},replaceAll:function(e,c,b){return e.replace(new RegExp(c,"g"),b)}}}})})();(function(){EG.define("EG.Date",function(f){var h=1*1000;var e=60*1000;var c=60*e;var b=24*c;var g=7*b;var a=30*b;var j=12*a;return{statics:{format:function(o,n){if(arguments.length==1){n=arguments[0];o=new Date()}if(typeof(o)=="number"){o=new Date(o)}else{if(typeof(o)=="string"){return o}}var t="";var k=[];for(var q=0;q<n.length;q++){var r=n.charAt(q);if(t.length>0&&t.charAt(t.length-1)!=r){k.push(t);t=r}else{t+=r}}k.push(t);for(var q=0;q<k.length;q++){var m=k[q];var u=m;var l=false;if(m=="yyyy"||m=="yy"){u=o.getFullYear();if(m=="yy"){u=u.substr(2)}}else{if(m=="MM"||m=="M"){u=o.getMonth()+1;l=(m=="MM")}else{if(m=="dd"||m=="d"){u=o.getDate();l=(m=="dd")}else{if(m=="HH"||m=="H"){u=o.getHours();l=(m=="HH")}else{if(m=="mm"||m=="m"){u=o.getMinutes();l=(m=="mm")}else{if(m=="ss"||m=="s"){u=o.getSeconds();l=(m=="ss")}}}}}}if(l){if(u<10){u="0"+u}}k[q]=u}return k.join("")},d2s:function(m,q,l){if(!m){m=new Date()}var r=m.getFullYear();var p=m.getMonth()+1;p=p<10?("0"+p):p;var s=m.getDate();s=s<10?("0"+s):s;if(q==null){q=true}if(q){var o=m.getHours();o=o<10?("0"+o):o;var n=m.getMinutes();n=n<10?("0"+n):n;var k=m.getSeconds();k=k<10?("0"+k):k;if(l){return(r+"年"+p+"月"+s+"日"+o+" 小时:"+n+"分"+k+"秒")}else{return(r+"-"+p+"-"+s+" "+o+":"+n+":"+k)}}else{if(l){return(r+"年"+p+"月"+s+"日")}else{return(r+"-"+p+"-"+s)}}},s2d:function(l){var k=new Date();if(l.length==4){return new Date(l,k.getMonth(),k.getDay(),k.getHours(),k.getMinutes(),k.getSeconds())}else{if(l.length==7){return new Date(l.substring(0,4),l.substring(5,7)-1,k.getDay(),k.getHours(),k.getMinutes(),k.getSeconds())}else{if(l.length==10){return new Date(l.substring(0,4),l.substring(5,7)-1,l.substring(8,10),k.getHours(),k.getMinutes(),k.getSeconds())}else{if(l.length==13){return new Date(l.substring(0,4),l.substring(5,7)-1,l.substring(8,10),l.substring(11,13),k.getHours(),k.getMinutes(),k.getSeconds())}else{if(l.length==16){return new Date(l.substring(0,4),l.substring(5,7)-1,l.substring(8,10),l.substring(11,13),l.substring(14,16),k.getSeconds())}else{if(l.length==19){return new Date(l.substring(0,4),l.substring(5,7)-1,l.substring(8,10),l.substring(11,13),l.substring(14,16),l.substring(17,19))}else{alert("str:"+l);throw new Error("参数不完整")}}}}}}},compare:function(l,k){if(typeof(l)=="string"){l=EG.Date.s2d(l)}if(typeof(k)=="string"){k=EG.Date.s2d(k)}return l.getTime()-k.getTime()},l2d:function(k){var m=new Date();m.setTime(k);return m},l2s:function(k){return EG.Date.d2s(EG.Date.l2d(k))},getMonthday:function(l){if(l==null){l=EG.clone(this.curDate)}else{if(typeof(l)=="string"){l=EG.Date.s2d(l);l=new Date(d.getFullYear(),d.getMonth(),0)}}var k=l.getMonth();var n=l.getFullYear();if(k==1){return(((n%4==0)&&(n%100!=0)||(n%400==0))?29:28)}else{return(f.solarMonthday[k])}},solarMonthday:[31,28,31,30,31,30,31,31,30,31,30,31],sameDay:function(l,k){return l.getYear()==k.getYear()&&l.getMonth()==k.getMonth()&&l.getDate()==k.getDate()},toSimpleFmt:function(k){var m=k.split(":");var n=parseInt(m[0]);var l=parseInt(m[1]);if(n>=13){return"下午"+(n-12)+""+(l>0?(":"+l):"点")}else{return"上午"+(n)+(l>0?(":"+l):"点")}},space:function(l,k){if(k==null){k=new Date()}var p=EG.Date.compare(k,l);if(p<0){return"未来"}var o=[[j,"年"],[a,"月"],[g,"周"],[b,"日"],[c,"年"],[e,"分"],[h,"秒"]];var n="";for(var m=0;m<o.length;m++){if(p>o[m][0]){n=parseInt(p/o[m][0])+o[m][1];break}}return n+"前"},unit_second:h,unit_min:60*1000,unit_hour:60*e,unit_day:24*c,unit_week:7*b,unit_month:30*b,unit_year:12*a}}})})();(function(){var ME=EG.define("EG.Object",{statics:{extract:function(arr,kKey,vKey){var o={};for(var i=0,il=arr.length;i<il;i++){o[arr[i][kKey]]=vKey!=null?arr[i][vKey]:arr[i]}return o},$in:function(obj,arr){if(arr==null){return false}for(var i=0,il=arr.length;i<il;i++){if(ME.equals(arr[i],obj)){return true}}return false},isLit:function(obj){return obj.constructor==Object},equals:function(o1,o2){if(o1==null&&o2==null){return true}var ot=EG.getType(o1);if(ot!=EG.getType(o2)){return false}switch(ot){case"string":case"function":case"boolean":case"number":return(o1===o2);case"object":for(var k in o1){if(!ME.equals(o1[k],o2[k])){return false}}return true;case"array":if(o1.length!=o2.length){return false}for(var i=0,il=o1.length;i<il;i++){if(!ME.equals(o1[i],o2[i])){return false}}return true;default:throw new Error("EG.Object#equals:暂不支持类型"+ot)}},hasKey:function(obj,keys){if(!EG.Array.isArray(keys)){keys=[keys]}for(var i=0;i<keys.length;i++){var has=false;for(var k in obj){if(keys[i]===k){has=true;break}}if(!has){return false}}return true},delKey:function(obj,keys){if(!EG.Array.isArray(keys)){keys=[keys]}for(var i=0;i<keys.length;i++){var key=keys[i];if(key.indexOf(".")>=0){var wdks=[];for(var k in obj){if(EG.RegExp.match(k,key)){wdks.push(k)}}for(var j=0;j<wdks.length;j++){delete obj[wdks[j]]}}else{delete obj[key]}}},$eval:function(str){var o=null;if(str==null||str.trim()==""){return null}eval("o="+str);return o},fetchKeys:function(src,keys){var o={};for(var i=0;i<keys.length;i++){o[keys[i]]=src[keys[i]]}return o}}});EG.$in=ME.$in;EG.isLit=ME.isLit})();(function(){EG.define("EG.Array",function(a){EG.copy(a,{isArray:function(b){return b&&typeof b==="object"&&typeof b.length==="number"&&typeof b.splice==="function"&&!(b.propertyIsEnumerable("length"))},del:function(b,c){if(c==null){throw new Error("EG.Array#del:待删除坐标不能为null")}b.splice(c,1);return b},remove:function(b,e){for(var c=b.length;c>=0;c--){if(e===b[c]){a.del(b,c)}else{if(e instanceof Function){if(e(b[c])){a.del(b,c)}}}}return b},insert:function(b,e,c){b.splice(e,0,c);return b},first:function(b){return a.get(b,0)},last:function(b){return a.get(b,b.length-1)},get:function(b,c){if(typeof(c)=="number"){if(b.length==0){return null}if(c<0){c=b.length+c}return b[c]}else{if(typeof(c)=="string"){if(c=="n"){c="last"}return a[c](b)}else{throw new Error("EG.Array#get:参数错误"+c)}}},has:function(b,f){if(!b){return false}for(var e=0,c=b.length;e<c;e++){if(f===b[e]){return true}}return false},each:function(b,f){if(!b||!f){return null}var g=null;for(var e=0,c=b.length;e<c;e++){g=f.apply(b[e],[e,b[e]]);if(g===false){break}}},extract:function(b,f){var g=[];for(var e=0,c=b.length;e<c;e++){g.push(b[e][f])}return g},extract2Arrays:function(b,l,m){if(!m){m=[]}for(var h=0,f=b.length;h<f;h++){var e=[];for(var g=0;g<l.length;g++){var c=l[g];if(typeof(c)=="string"){e.push(b[h][c])}else{e.push(c.apply(this,[b[h]]))}}m.push(e)}return m},extract2Obj:function(c,g,b){var h={};for(var f=0,e=c.length;f<e;f++){h[c[f][g]]=b!=null?c[f][b]:c[f]}return h},clear:function(b){b.splice(0,b.length);return b},sub:function(e,g,c){var b=[];for(var f=g;f<c;f++){b.push(e[f])}return b},addAll:function(c,b){Array.prototype.push.apply(c,b);return c},getIdx:function(b,f){for(var e=0,c=b.length;e<c;e++){if(f===b[e]){return e}}return -1},fetch:function(c,b,l,k){var e=EG.isArray(l);var h=[];for(var g=0;g<c.length;g++){if(e){for(var f=0;f<l.length;f++){if(c[g][b]==l[f]){h.push(c[g])}}}else{if(c[g][b]==l){return k!=null?c[g][k]:c[g]}}}return e?h:null}});EG.each=a.each;EG.isArray=a.isArray})})();(function(){EG.define("EG.RegExp",{statics:{match:function(b,a){return new RegExp("^"+a+"$").test(b)}}})})();(function(){var a=EG.define("EG.Ajax",{statics:{JSONP_callbackers:{},getXMLHttpRequest:function(){var b=null;if(window.XMLHttpRequest){b=new XMLHttpRequest()}else{if(window.ActiveXObject){try{b=new ActiveXObject("Msxml2.XMLHTTP")}catch(e){try{b=new ActiveXObject("Microsoft.XMLHTTP")}catch(c){b=null}}}}return b},javaURLEncoding:function(b){return window.encodeURIComponent(b).replace(/!/g,"%21").replace(/\"/g,"%22").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/~/g,"%7E")},handleJSONP:function(c,b){var e=new Date().getTime()+"_"+Math.random()*1000;if(a.tag_JSONP){EG.DOM.remove(a.tag_JSONP)}a.tag_JSONP=EG.CE({pn:EG.getBody(),tn:"script",type:"text/javascript"});a.JSONP_callbackers[e]=function(){var f=EG.copy(b);delete f.url_jsonp;a.send.apply(this,[f])};a.tag_JSONP.src=c+'EG.Ajax.sendJSONPBack("'+e+'")';return},send:function(l){if(l==null){throw new Error("EG.Ajax.call#参数不能为空")}var j=l.url_jsonp;if(j){a.handleJSONP(j,l);return}var e=l.url,f=l.charset||"UTF-8",p=EG.Ajax.getXMLHttpRequest(),c=EG.unnull(l.method,"GET"),g=EG.unnull(l.async,true),r=EG.unnull(l.callback,""),b=l.erhandler,m=EG.unnull(l.content,""),o=l.timeout||30*1000,q=EG.unnull(l.contentType,"application/x-www-form-urlencoded"),n=l.xhrFields;if(g){p.onreadystatechange=function(){if(p.readyState==4){if(p.status==200){if(r){return r(p.responseText,p)}}else{if(b){return b(p.status,p)}else{throw new Error("EG.Ajax#send:"+p.status+": "+p.statusText+",\n["+c+"]"+e)}}}return null}}if(!EG.Browser.isIE()){p.timeout=o}p.open(c,e,g);if(n){for(var h in n){p[h]=n[h]}}if(q){p.setRequestHeader("Content-Type",q)}p.send(m);if(!g&&r){return r(p.responseText,p)}return},sendJSONPBack:function(c){var b=a.JSONP_callbackers[c];if(b){delete a.JSONP_callbackers[c];b()}}}});a.globeRequest=a.getXMLHttpRequest()})();(function(){EG.define("EG.Browser",[],function(f){f.Sys={};var g=navigator.appVersion;var a=navigator.userAgent;var b=navigator.userAgent.toLowerCase();var c;(c=b.match(/msie ([\d.]+)/))?f.Sys.ie=c[1]:(c=b.match(/firefox\/([\d.]+)/))?f.Sys.firefox=c[1]:(c=b.match(/chrome\/([\d.]+)/))?f.Sys.chrome=c[1]:(c=b.match(/opera.([\d.]+)/))?f.Sys.opera=c[1]:(c=b.match(/version\/([\d.]+).*safari/))?f.Sys.safari=c[1]:0;var e=!window.location.domain;f.Version={trident:a.indexOf("Trident")>-1,presto:a.indexOf("Presto")>-1,webkit:a.indexOf("AppleWebKit")>-1,gecko:a.indexOf("Gecko")>-1&&a.indexOf("KHTML")==-1,mobile:!!a.match(/.*Mobile.*/),ios:!!a.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),android:a.indexOf("Android")>-1||a.indexOf("Linux")>-1,iphone:a.indexOf("iPhone")>-1||a.indexOf("Mac")>-1,ipad:a.indexOf("iPad")>-1,webApp:a.indexOf("Safari")==-1};return{statics:{isIE:function(){return(!!window.ActiveXObject||"ActiveXObject" in window)},isIE6:function(){return f.isIE()&&f.getIEVersion()==6},isIE7:function(){return f.isIE().tooUp&&f.getIEVersion()==7},isIE8:function(){return f.isIE()&&f.getIEVersion()==8},getIEVersion:function(){if(f.Sys.ie){return parseInt(f.Sys.ie.substr(0,f.Sys.ie.indexOf(".")))}else{return -1}},isIE8Before:function(){return f.isIE()&&f.getVersion()<=8},isFirefox:function(){return f.Sys.firefox!=null},isChrome:function(){return f.Sys.chrome!=null},isSafari:function(){return f.Sys.safari!=null},isOpera:function(){return f.Sys.opera!=null},getVersion:function(){return f.Sys.ie||f.Sys.firefox||f.Sys.chrome||f.Sys.safari||f.Sys.opera},getDomainAddress:function(){return window.location.href.substr(0,window.location.href.indexOf("/",10))},isLocal:function(){return e}}}})})();(function(){var ME=EG.define("EG.Tools",{statics:{clear:function(key){if(window.localStorage){localStorage.removeItem(key)}},save:function(key,value){var s=ME.toJSON(value);if(window.localStorage){localStorage.setItem(key,s)}},query:function(key){if(!window.localStorage){return null}var s=localStorage.getItem(key);return EG.Object.$eval(s)},post2:function(url,params){var f=EG.CE({pn:EG.getBody(),tn:"form",action:url,method:"post",style:"display:none"});for(var x in params){EG.CE({pn:f,tn:"textarea",name:x,value:params[x]})}f.submit();return f},filterSpecChar:function(str){return EG.String.replaceAll(EG.String.replaceAll(str,"<","&lt;"),">","&gt;")},getDPI:function(){var arrDPI;if(window.screen.deviceXDPI!=undefined){arrDPI=[window.screen.deviceXDPI,window.screen.deviceYDPI]}else{var tmpNode=EG.CE({tn:"div",style:"width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden"});document.body.appendChild(tmpNode);arrDPI=[parseInt(tmpNode.offsetWidth),parseInt(tmpNode.offsetHeight)];tmpNode.parentNode.removeChild(tmpNode)}return arrDPI},getPerInchPix:function(){if(!ME.gDPIRate){var tmpNode=EG.CE({tn:"div",style:"width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden"});document.body.appendChild(tmpNode);ME.gDPIRate=[parseInt(tmpNode.offsetWidth),parseInt(tmpNode.offsetHeight)];tmpNode.parentNode.removeChild(tmpNode)}return ME.gDPIRate},isPressLeft:function(e){e=EG.Event.getEvent(e);if(e.which!=null){return e.which==1}else{return e.button==1}},isPressRight:function(e){e=EG.Event.getEvent(e);if(e.which!=null){return e.which==3}else{return e.button==2}},isPressCtrl:function(e){e=EG.Event.getEvent(e);return e.ctrlKey||e.keyCode==17},getParam:function(name){var params=EG.doc.location.search;params=params.substring(1);if(!params){return""}var paramArr=params.split("&");for(var i=0,il=paramArr.length;i<il;i++){if(paramArr[i].indexOf(name)!=-1){return paramArr[i].substring(paramArr[i].indexOf("=")+1)}}return null},getParams:function(){var params={};var strParams=EG.doc.location.search;strParams=strParams.substring(1);if(!strParams){return{}}var paramArr=strParams.split("&");for(var i=0,il=paramArr.length;i<il;i++){params[paramArr[i].substring(0,paramArr[i].indexOf("="))]=paramArr[i].substring(paramArr[i].indexOf("=")+1)}return params},toJSON:function(obj,as){if(as==null){as=[]}if(obj==null){return"null"}if(typeof(obj)=="object"){as.push(obj);var json=[];if(obj instanceof Array){for(var i=0;i<obj.length;i++){var o=obj[i];json[i]=(o!==null)?ME.toJSON(o,as):"null"}return"["+json.join(", ")+"]"}else{for(var key in obj){if(!obj.hasOwnProperty(key)){continue}var o=obj[key];if(typeof(o)=="object"&&EG.Array.has(as,o)){continue}json.push('"'+key+'" : '+((o!=null)?ME.toJSON(o,as):"null"))}return"{\n "+json.join(",\n ")+"\n}"}}else{if(typeof(obj)=="string"){return'"'+obj.replace(/\\/g,"\\\\").replace(/"/g,'\\"')+'"'}else{if(typeof(obj)=="boolean"){return obj}else{if(typeof(obj)=="function"){return obj}else{if(typeof(obj)=="number"){return obj}else{if(typeof(obj)=="regexp"){return obj}else{throw new Error("Engin#toJSON:不支持类型:"+typeof(obj))}}}}}}},toXMLDoc:function(text){var xmlDoc;try{xmlDoc=new ActiveXObject("Microsoft.XMLDOM");xmlDoc.async="false";xmlDoc.loadXML(text)}catch(e){try{parser=new DOMParser();xmlDoc=parser.parseFromString(text,"text/xml")}catch(e){alert(e.message)}}return xmlDoc},getScriptPath:EG.getScriptPath,getMousePos:function(evt,refer){var mp;evt=EG.Event.getEvent(evt);if(evt.pageX||evt.pageY){mp={x:evt.pageX,y:evt.pageY}}else{mp={x:evt.clientX+EG.getBody().scrollLeft-EG.getBody().clientLeft,y:evt.clientY+EG.getBody().scrollTop-EG.getBody().clientTop}}if(refer){var rp=ME.getElementPos(refer);mp={x:mp.x-rp.x,y:mp.y-rp.y}}return mp},getElementPos:function(ele,parent,isOffsetParent){if(isOffsetParent==null){isOffsetParent=true}if(!parent){parent=EG.getBody()}var t=ele.offsetTop;var l=ele.offsetLeft;while((ele=isOffsetParent?ele.offsetParent:ele.parentNode)!=parent&&ele!=null){t+=ele.offsetTop;l+=ele.offsetLeft}return{x:l,y:t}},debugObject:function(obj){var s="";for(var key in obj){s+=key+","}alert(s)},aop:function(expression,before,after){if(!before&&!after){return}var mType=-1;if(mType<0){mType=expression.indexOf("###")>0?2:-1}if(mType<0){mType=expression.indexOf("##")>0?1:-1}if(mType<0){mType=expression.indexOf("#")>0?0:-1}if(mType<0){throw new Error("EG#aop:表达式错误>"+expression)}var classNameExp=expression.substr(0,expression.indexOf("#"));var methodNameExp=expression.substr(expression.lastIndexOf("#")+1);var classes=EG.findClasses(classNameExp,true);var cms={};for(var className in classes){(function(){var clazz=classes[className];var f=function(num,tp){var ms=EG.findMethods(clazz,methodNameExp,tp,"all");for(var methodName in ms){cms[className+","+num+","+methodName]=ms[methodName]}};if(mType==2||mType==1){f(1,"static")}if(mType==2||mType==0){f(0,"prototype")}})()}for(var cmName in cms){(function(){var method=cms[cmName];var ks=cmName.split(",");var className=ks[0];var isProptype=(ks[1]=="0");var methodName=ks[2];var clazz=classes[className];if(isProptype){clazz.prototype[methodName]=function(){if(before){before.apply(this,[clazz,className,method,methodName,isProptype])}var r=method.apply(this,arguments);if(after){after.apply(this,[clazz,className,method,methodName,isProptype])}return r}}else{clazz[methodName]=function(){if(before){before(clazz,className,method,methodName,isProptype)}var s="";for(var i=0,il=arguments.length;i<il;i++){if(s!=0){s+=","}s+=("arguments["+i+"]")}var r=null;var p="r=new method("+s+");";eval(p);if(after){after(clazz,className,method,methodName,isProptype)}return r}}})()}},getText:function(textvalues,value){for(var i=0;i<textvalues.length;i++){if(textvalues[i][1]==value){return textvalues[i][0]}}return null},getEllipsis:function(txts,count,endChar){var t="";if(!endChar){endChar=""}if(txts.length>count){t=EG.Array.sub(txts,0,count).join(",");t+="...("+(txts.length)+endChar+")"}else{t=txts.join(",")}return t},convertBase64UrlToBlob:function(urlData){var bytes=window.atob(urlData.split(",")[1]);var ab=new ArrayBuffer(bytes.length);var ia=new Uint8Array(ab);for(var i=0;i<bytes.length;i++){ia[i]=bytes.charCodeAt(i)}return new Blob([ab],{type:"image/png"})},watch:function(expression){ME.aop(expression,ME.watch_f,null)},watch_f:function(clazz,className,method,methodName,isProptype){if(!window.watchCount){window.watchBox=EG.doc.createElement("div");var ss="position:absolute;z-index:9999;left:0px;top:0px;border:1px solid red;font-size:12px;max-height:500px;overflow:auto;background-color:white".split(";");for(var i=0,il=ss.length;i<il;i++){var sss=ss[i].split(":");watchBox.style[sss[0]]=sss[1]}EG.getBody().appendChild(watchBox);window.watchCount={};window.watchBoxs={}}var key=className+(isProptype?"#":"##")+methodName;if(!watchCount[key]){watchCount[key]=0;watchBoxs[key]=EG.doc.createElement("div");watchBox.appendChild(watchBoxs[key])}watchCount[key]++;watchBoxs[key].innerHTML=(key+":"+watchCount[key])}}});EG.toJSON=ME.toJSON})();(function(){EG.define("EG.Tpl",{constructor:function(content){this.setContent(content)},setContent:function(content){this.contentSrc=content;this.content=EG.String.replaceAll(this.contentSrc,"\\{([^}]+)\\}","'+EG.Tpl.fill(data,'$1')+'")},match:function(data){var s;eval("s='"+this.content+"'");return s},statics:{fill:function(data,key){return data[key]}}})})();(function(){EG.define("EG.Validate",{statics:{common_regex:{email:"([a-zA-Z0-9_\\.\\-])+\\@(([a-zA-Z0-9])+\\.)+([a-zA-Z0-9]{2,10})+",phone:"(1\\d{10})",qq:"[1-9]\\d*",tel:"(([0+]\\d{2,3}-)?(0\\d{2,3})-)(\\d{7,8})(-(\\d{3,}))?",idcardNo:"\\d{15}(\\d{3})?",password:"[0-9a-zA-Z]*",wordnum:"[0-9a-zA-Z_]*"},common_comment:{email:"xxx@域名",phone:"1开头的11位数字",qq:"非0开头的纯数字",tel:"国家(非必填)-区域(非必填)-电话-分机(非必填)",idcardNo:"15或18位数字",password:"字母(含大小写)和数字组合形式",wordnum:"字母(含大小写)和数字组合形式"},getTelRegex:function(b){if(b==null){b="-?"}return"(([0+]\\d{2,3}"+b+")?(0\\d{2,3})"+b+")(\\d{7,8})("+b+"(\\d{3,}))?"},isWordnum:function(b){return EG.RegExp.match(b,EG.Validate.common_regex.wordnum)?"":a.getComment("wordnum")},isEmail:function(b){return EG.RegExp.match(b,EG.Validate.common_regex.email)?"":a.getComment("email")},isPassword:function(b){return EG.RegExp.match(b,EG.Validate.common_regex.password)?"":a.getComment("password")},isPhone:function(b){return EG.RegExp.match(b,EG.Validate.common_regex.phone)?"":a.getComment("phone")},isQq:function(b){return EG.RegExp.match(b,EG.Validate.common_regex.qq)?"":a.getComment("qq")},isTel:function(c,b){return EG.RegExp.match(c,EG.Validate.getTelRegex(b))?"":a.getComment("tel")},isIdcardNo:function(c){var b=c.length;return EG.RegExp.match(c,EG.Validate.common_regex.idcardNo)?"":a.getComment("idcardNo")},$is:function(c,g){var b="is"+EG.Word.first2Uppercase(c);var e=EG.Validate[b];if(!e){throw new Error("EG.Validate:不支持函数"+b)}return e(g)},getComment:function(b){return EG.Validate.common_comment[b]}}});var a=EG.Validate})();(function(){EG.define("EG.Word",{statics:{first2Uppercase:function(a){return a.substring(0,1).toUpperCase()+a.substring(1)},first2LowerCase:function(a){return a.substring(0,1).toLowerCase()+a.substring(1)},numtochinese:function(a){if(typeof(a)=="Number"){a=a+""}for(i=a.length-1;i>=0;i--){a=a.replace(",","");a=a.replace(" ","")}a=a.replace("￥","");if(isNaN(a)){alert("请检查小写金额是否正确");return}part=String(a).split(".");newchar="";for(i=part[0].length-1;i>=0;i--){if(part[0].length>10){alert("位数过大，无法计算");return""}tmpnewchar="";perchar=part[0].charAt(i);switch(perchar){case"0":tmpnewchar="零"+tmpnewchar;break;case"1":tmpnewchar="壹"+tmpnewchar;break;case"2":tmpnewchar="贰"+tmpnewchar;break;case"3":tmpnewchar="叁"+tmpnewchar;break;case"4":tmpnewchar="肆"+tmpnewchar;break;case"5":tmpnewchar="伍"+tmpnewchar;break;case"6":tmpnewchar="陆"+tmpnewchar;break;case"7":tmpnewchar="柒"+tmpnewchar;break;case"8":tmpnewchar="捌"+tmpnewchar;break;case"9":tmpnewchar="玖"+tmpnewchar;break}switch(part[0].length-i-1){case 0:tmpnewchar=tmpnewchar+"元";break;case 1:if(perchar!=0){tmpnewchar=tmpnewchar+"拾"}break;case 2:if(perchar!=0){tmpnewchar=tmpnewchar+"佰"}break;case 3:if(perchar!=0){tmpnewchar=tmpnewchar+"仟"}break;case 4:tmpnewchar=tmpnewchar+"万";break;case 5:if(perchar!=0){tmpnewchar=tmpnewchar+"拾"}break;case 6:if(perchar!=0){tmpnewchar=tmpnewchar+"佰"}break;case 7:if(perchar!=0){tmpnewchar=tmpnewchar+"仟"}break;case 8:tmpnewchar=tmpnewchar+"亿";break;case 9:tmpnewchar=tmpnewchar+"拾";break}newchar=tmpnewchar+newchar}if(a.indexOf(".")!=-1){if(part[1].length>2){alert("小数点之后只能保留两位,系统将自动截段");part[1]=part[1].substr(0,2)}for(i=0;i<part[1].length;i++){tmpnewchar="";perchar=part[1].charAt(i);switch(perchar){case"0":tmpnewchar="零"+tmpnewchar;break;case"1":tmpnewchar="壹"+tmpnewchar;break;case"2":tmpnewchar="贰"+tmpnewchar;break;case"3":tmpnewchar="叁"+tmpnewchar;break;case"4":tmpnewchar="肆"+tmpnewchar;break;case"5":tmpnewchar="伍"+tmpnewchar;break;case"6":tmpnewchar="陆"+tmpnewchar;break;case"7":tmpnewchar="柒"+tmpnewchar;break;case"8":tmpnewchar="捌"+tmpnewchar;break;case"9":tmpnewchar="玖"+tmpnewchar;break}if(i==0){tmpnewchar=tmpnewchar+"角"}if(i==1){tmpnewchar=tmpnewchar+"分"}newchar=newchar+tmpnewchar}}while(newchar.search("零零")!=-1){newchar=newchar.replace("零零","零")}newchar=newchar.replace("零亿","亿");newchar=newchar.replace("亿万","亿");newchar=newchar.replace("零万","万");newchar=newchar.replace("零元","元");newchar=newchar.replace("零角","");newchar=newchar.replace("零分","");if(newchar.charAt(newchar.length-1)=="元"||newchar.charAt(newchar.length-1)=="角"){newchar=newchar+"整"}return newchar},intToChinese:function(f){f=f+"";var a=f.length-1;var c=["","十","百","千","万","十","百","千","亿","十","百","千","万","十","百","千","亿"];var b=["零","壹","贰","叁","肆","伍","陆","柒","捌","玖"];var e=f.replace(/([1-9]|0+)/g,function(l,h,g,k){var n=0;if(h[0]!="0"){n=a-g;if(g==0&&h[0]==1&&c[a-g]=="十"){return c[a-g]}return b[h[0]]+c[a-g]}else{var m=a-g;var j=a-g+h.length;if(Math.floor(j/4)-Math.floor(m/4)>0){n=m-m%4}if(n){return c[n]+b[h[0]]}else{if(g+h.length>=a){return""}else{return b[h[0]]}}}});e=EG.String.replaceAll(e,"\\.","点");return e}}})})();(function(){EG.define("EG.Promise",[],function(a){return{constructor:function(b){this.resolvesQueue=[];this.rejectQueue=[];this.status="pendding";this.promiseMain=b;return this},handle:function(b,f){var e=this;this.status=b;var c=null;if(this.status=="fullfilled"){c=this.resolvesQueue.shift()}else{if(this.status=="rejected"){c=this.rejectQueue.shift()}}setTimeout(function(){c.apply(e,[e,f])},0)},then:function(e,b){var c=this;this.resolvesQueue.push(e);this.rejectQueue.push(b);if(this.status=="pendding"){this.status=="fullfilled";if(!this.t){this.t=setTimeout(function(){c.promiseMain.apply(c,[c])},0)}}return this},resolve:function(b){this.handle("fullfilled",b)},reject:function(b){this.handle("rejected",b)}}})})();(function(){var ME=EG.define("EG.Event",{statics:{fireEvent:function(ele,action){action=EG.String.removeStart(action,"on");if(document.createEvent){var e=document.createEvent("HTMLEvents");e.initEvent(action,false,false);ele.dispatchEvent(e)}else{if(ele.fireEvent){ele.fireEvent("on"+action)}else{eval("ele."+action+"();")}}},_eventFns:{},_getEventFN:function(action){var eventKey="events_"+action;if(!ME._eventFns[eventKey]){ME._eventFns[eventKey]=function(e){e=ME.getEvent(e);var rs=null;var target=this;if(target[eventKey]==null){target=e.target}if(!target[eventKey]){return rs}for(var i=0,il=target[eventKey].length;i<il;i++){var rs=target[eventKey][i].apply(target["on"+action+"Src"]||target,[e]);if(rs!=null){return rs}}return rs}}return ME._eventFns[eventKey]},bindEvent:function(ele,action,handler,cap){action=EG.String.removeStart(action,"on");var eventKey="events_"+action;if(!ele[eventKey]){ele[eventKey]=[];ele[action+"_fn"]=ME._getEventFN(action);var useAttach=false;if(EG.Browser.isIE8()||EG.Browser.isIE8Before()){useAttach=true}if(useAttach){ele.attachEvent("on"+action,function(e){ele[action+"_fn"].apply(ele,[e])})}else{if(EG.Browser.isIE()){ele["on"+action]=ele[action+"_fn"]}else{if(ele.addEventListener){ele.addEventListener(action,ele[action+"_fn"],cap)}}}}ele[eventKey].push(handler)},hasEventHandle:function(ele,action,fn){return ele[action+"_fn"]&&EG.Array.has(ele[action+"_fn"],fn)},_fn:{false_return:function(){return false}},bindUnselect:function(ele){EG.css(ele,EG.Style.c.selectnone);if(EG.Browser.isIE()){ME.bindEvent(ele,"onselectstart",ME._fn.false_return)}},removeEvent:function(ele,action,handler){action=EG.String.removeStart(action,"on");var eventKey="events_"+action;if(!ele[eventKey]){return}EG.Array.remove(ele[eventKey],handler)},onload:function(fn){ME.bindEvent(window,"load",fn)},getEvent:function(e){return e||window.event},getTarget:function(e){return e?e.target:window.event.srcElement},stopPropagation:function(e){e=ME.getEvent(e);if(e&&e.stopPropagation){e.stopPropagation()}else{window.event.cancelBubble=true}if(e.preventDefault){e.preventDefault()}else{e.returnValue=false}},keycodes:{esc:27}}});EG.bindEvent=ME.bindEvent;EG.removeEvent=ME.removeEvent;EG.onload=ME.onload})();(function(){var a=false;if(a){if(!EG.Browser.isIE()&&typeof(HTMLElement)!="undefined"&&!window.opera){HTMLElement.prototype.__defineGetter__("outerHTML",function(){var c=this.attributes,f="<"+this.tagName;for(var e=0;e<c.length;e++){if(c[e].specified){f+="   "+c[e].name+'="'+c[e].value+'"'}}if(!this.canHaveChildren){return f+"   />"}return f+">"+this.innerHTML+"</"+this.tagName+">"});HTMLElement.prototype.__defineSetter__("outerHTML",function(e){var f=EG.doc.createElement("DIV");f.innerHTML=e;for(var c=0;c<f.childNodes.length;c++){this.parentNode.insertBefore(f.childNodes[c],this)}this.parentNode.removeChild(this)});HTMLElement.prototype.__defineGetter__("canHaveChildren",function(){return !/^(area|base|basefont|col|frame|hr|img|br|input|isindex|link|meta|param)$/.test(this.tagName.toLowerCase())})}}var b=EG.define("EG.DOM",{statics:{addChildren:function(j,l,e){var h=(!EG.isArray(l))?[l]:l;for(var g=0,f=h.length;g<f;g++){var k=h[g];if(EG.isLit(k)){k=EG.CE(k)}else{if(k.getElement){k=k.getElement()}}b.insertChild(j,k,e);e++}},insertAfter:function(e,f){var c=f.parentNode;if(c.lastChild==f){c.appendChild(e)}else{c.insertBefore(e,b.nextNode(f))}},insertBefore:function(e,f){var c=f.parentNode;c.insertBefore(e,f)},insertChild:function(c,e,f){if(!f){return c.appendChild(e)}if(typeof(f)=="number"){f=b.childNodes(c)[f]}if(f){c.insertBefore(e,f)}else{c.appendChild(e)}},getOuterHTML:function(c){if(c.parentNode){throw new Error("EG.DOM#getOuterHTML:不支持有父节点的节点")}return EG.CE({tn:"div",cn:[c]}).innerHTML},$:function(f,m){if(!m){m=EG.doc}if(b.isElement(f)){return f}if("string"==typeof(f)){var k=m.getElementById(f);if(k!=null){return k}else{if((k=m.getElementsByName(f))!=null&&k.length>0){return k}else{return null}}}else{if("object"==typeof(f)){var j=null,c=[];if(f.name){var p=f.ele||m;j=p.getElementsByName(f.name)}else{if(f.tn){var p=f.ele||m;j=p.getElementsByTagName(f.tn)}else{throw new Error("EG.DOM#$:name和tn不能同时为空")}}var n=f.idx;delete f.name;delete f.tn;delete f.ele;delete f.idx;for(var g=0,l=j.length;g<l;g++){var h=true;for(var o in f){if(EG.$in("name","tn","ele","idx")){continue}if(f[o]!=j[g][o]){h=false;break}}if(h){c.push(j[g])}}if(n!=null){return EG.Array.get(c,n)}return c}}},CEKeywords:["ele","element","tn","tagName","pn","parentNode","cn","childNodes","style"],CENicknames:{cls:"className"},NS_SVG:"http://www.w3.org/2000/svg",CSVG:function(c){return b.CE(c,{svg:true})},CVML:function(c){return b.CE(c,{vml:true})},CE:function(g,f){var l=f?f.svg:false;var t=f?f.vml:false;var c=f?f.setAtr:false;if(typeof(g)=="string"){return b.childNodes(EG.CE({tn:"div",innerHTML:g}))}var u=EG.unnull(g.ele,g.element),s=EG.unnull(g.tn,g.tagName),h=EG.unnull(g.pn,g.parentNode),q=EG.unnull(g.cn,g.childNodes),e=g.style;if(!u&&!s){throw new Error("EG.DOM#CE:标签名和ele不能同时为空")}if(s){if(s.toUpperCase()=="IFRAME"&&g.name&&EG.Browser.isIE6()){u=EG.doc.createElement("<iframe name='"+g.name+"'></iframe>")}else{if(l){u=EG.doc.createElementNS(b.NS_SVG,s)}else{if(t){u=EG.doc.createElement(s)}else{u=EG.doc.createElement(s)}}if(t){u.xmlns="urn:schemas-microsoft-com:vml";g.cls="vml"}}}for(var r in g){if(EG.$in(r,b.CEKeywords)){continue}var o=g[r];if(r=="name"){u.setAttribute("name",o);u.name=o}else{if(r.indexOf("$")>0){var n=r.indexOf("$"),j=r.substring(0,n),k=r.substring(n+1,r.length);if(!u[j]){u[j]={}}u[j][k]=o}else{if(r.indexOf("on")==0&&!EG.String.endWith(r,"Src")){if(o!=null){EG.bindEvent(u,r.substr(2).toLowerCase(),o)}}else{if(b.CENicknames[r]){r=b.CENicknames[r]}if(l){if(r=="innerText"){b.removeChilds(u);u.appendChild(document.createTextNode(o))}else{if(r=="DATA"){u.DATA=o}else{if(r=="className"){r="class"}u.setAttributeNS(null,r,o)}}}else{if(c){u.setAttribute(r,o)}u[r]=o}}}}}if(e){EG.css(u,e)}if(h){if(h.isItem){h.getElement().appendChild(u)}else{h.appendChild(u)}}if(q){if(typeof(q)=="string"){q=b.CE(q)}for(var m=0,p=q.length;m<p;m++){if(EG.isLit(q[m])){q[m]=EG.CE(q[m],f)}else{if(q[m].isItem){q[m]=q[m].getElement()}}u.appendChild(q[m])}}return u},isElement:function(c){return c&&c.nodeType==1},isNodeList:function(c){return c!=null&&c.nodeType==null&&c.length!=null&&typeof(c.length)=="number"&&c.item!=null},isTag:function(e,c){return e.tagName.toUpperCase()==c.toUpperCase()},has:function(f,c){var e=c.parentNode;while(e!=null){if(e==f){return true}e=e.parentNode}return false},remove:function(e){if(!EG.Array.isArray(e)){e=[e]}for(var c=0;c<e.length;c++){e[c].parentNode.removeChild(e[c])}},removeChilds:function(c){while(c.childNodes&&c.childNodes.length>0){c.removeChild(c.childNodes[0])}return c},preNode:function(c){return b._nextNode(c,-1)},nextNode:function(c){return b._nextNode(c,1)},_nextNode:function(g,h){if(!g||!g.parentNode){return null}var f=b.childNodes(g.parentNode);for(var e=0,c=f.length;e<c;e++){if(f[e]==g){return(e+h<c)?f[e+h]:null}}},childNode:function(h,j){if(typeof(j)=="string"){var g=j.split("_");for(var e=0,c=g.length;e<c;e++){if(g[e]!="n"){g[e]=parseInt(g[e])}h=EG.Array.get(b.childNodes(h),g[e])}return h}else{if(typeof(j)=="number"){var f=b.childNodes(h);return EG.Array.get(f,j)}}throw new Error("EG.DOM#childNode不支持索引类型:"+typeof(j))},childNodes:function(h){var f=[],g=h.childNodes;for(var e=0,c=g.length;e<c;e++){if(g[e].nodeType==1){f.push(g[e])}}return f},getValue:function(q,e){e=e||{};if(typeof(q)=="string"){q=b.$(q)}if(q==null){throw new Error("EG.DOM#getValue:未找到匹配元素")}var h=b.isNodeList(q);var n=(h?q[0]:q).tagName.toUpperCase();if(n=="INPUT"){var k=(h?q[0]:q).type.toUpperCase();if(k=="TEXT"||k=="PASSWORD"||k=="HIDDEN"||k=="FILE"){if(h){throw new Error("EG.DOM#getValue:暂不支持数组Element INPUT "+k)}return q.value}else{if(k=="RADIO"){if(h){for(var g=0,l=q.length;g<l;g++){if(q[g].checked==true){return q[g].value}}}else{return(q.checked==true)?q.value:null}}else{if(k=="CHECKBOX"){if(h){var o=[];for(var g=0,l=q.length;g<l;g++){if(q[g].checked==true){o.push(q[g].value)}}return o}else{return(q.checked==true)?q.value:null}}else{throw new Error("EG.DOM#getValue:不支持input "+k+"类型")}}}}else{if(n=="SELECT"){if(h){throw new Error("EG.DOM#getValue:暂不支持数组Element SELECT")}var f=e.getText!=null?e.getText:false;var j=e.ignoreEmpty!=null?e.ignoreEmpty:false;var c=q.options;var o=[];var p=q.multiple;for(var g=0,l=c.length;g<l;g++){if(c[g].selected==true){var m=null;if(j&&c[g].value==""){m=null}else{m=(f)?c[g].text:c[g].value}if(p){o.push(m)}else{return m}}}if(p){return o}else{return null}}else{if(n=="TEXTAREA"){if(h){var o=[];for(var g=0,l=q.length;g<l;g++){o.push(q.value)}return o}else{return q.value}}else{if(q.innerHTML!=null){if(h){var o=[];for(var g=0,l=q.length;g<l;g++){o.push(q.innerHTML)}return o}else{return q.innerHTML}}else{throw new Error(" EG.DOM#getValue:不支持其它类型值")}}}}return null},getValues:function(j){var m={};var k=b.$(j);var l=j.getElementsByTagName("INPUT");l.concat(j.getElementsByTagName("SELECT"));l.concat(j.getElementsByTagName("TEXTAREA"));for(var h=0,f=l.length;h<f;h++){if(k[h].id==""&&k[h].name==""){continue}var g=k[h].id!=""?k[h].id:k[h].name;m[g]=b.getValue(g)}return m},setValue:function(p,m,e){e=e||{};var h=e.fireOnchange!=null?e.fireOnchange:true;if(typeof(p)=="string"){p=EG.$(p)}if(p==null){throw new Error("EG.DOM#setValue未找到匹配元素")}var j=b.isNodeList(p);var n=(j?p[0]:p).tagName.toUpperCase();if(m==null){m=""}if(n=="INPUT"){var k=(j?p[0]:p).type.toUpperCase();if(k=="TEXT"||k=="PASSWORD"||k=="HIDDEN"){p.value=m}else{if(k=="RADIO"){if(j){for(var g=0,l=p.length;g<l;g++){if(p[g].value==m){p[g].checked=true;break}}}else{if(p.value==m){p.checked=true}}}else{if(k=="CHECKBOX"){var f=e.spliter||",";if(m instanceof Array){if(!j){throw new Error("EG.DOM#setValue:值为数组时,Element类型必须为数组")}for(var g=0,l=p.length;g<l;g++){if(EG.Array.has(m,p[g].value)){p[g].checked=true;if(h){EG.Event.fireEvent(p[g],"onchange")}}}}else{if(j){for(var g=0,l=p.length;g<l;g++){if(m==p[g].value){p[g].checked=true;if(h){EG.Event.fireEvent(p[g],"onchange")}}}}else{if(p.value==m){p.checked=true;if(h){EG.Event.fireEvent(p,"onchange")}}}}}else{throw new Error("Engin#setValue:不支持该input "+k+"类型")}}}}else{if(n=="SELECT"){var o=e.cmpText!=null?e.cmpText:false;var c=p.options;for(var g=0,l=c.length;g<l;g++){if((o&&c[g].text==m)||(!o&&c[g].value==m)){c[g].selected=true;if(h){EG.Event.fireEvent(p,"onchange")}return}}}else{if(n=="TEXTAREA"){p.value=m}else{if(p.innerHTML!=null){p.innerHTML=m}else{throw new Error(" EG.DOM#setValue:不支持对其它类型设值")}}}}},setValues:function(e){if(e!=null){for(var c in e){b.setValue(c,e[c])}}},removeOptions:function(c){c=EG.$(c);if(!b.isTag(c,"SELECT")){throw new Error("EG.DOM#removeOptions:待删除的对象非Select组件")}b.removeChilds(c)},addOptions:function(l,g,f,m,j,e){l=EG.$(l);m=m||1;f=f||0;for(var h=0,k=g.length;h<k;h++){var c=EG.CE({tn:"option",value:(typeof(m)=="function")?m(g[h]):g[h][m],innerHTML:(typeof(f)=="function")?f(g[h]):g[h][f]});if(j&&e){c[j]=(typeof(e)=="function")?e(g[h]):g[h][e]}l.options[l.options.length]=c}},getOption:function(h,j){var g=h.options;for(var f=0,c=g.length;f<c;f++){var e=g[f];if(e.value==j){return e}}},getSelectedOption:function(c){return c.options[c.options.selectedIndex]},removeOption:function(l,m){var f=null,c;if(typeof(m)=="string"){m={value:m}}if((c=m.idx)!=null){f="idx"}else{if((c=m.value)!=null){f="value"}else{if((c=m.text)!=null){f="text"}else{throw new Error("EG.DOM#removeOption:参数不正确")}}}var k=l.options;if(EG.isArray(c)){for(var h=k.length-1;h>=0;h--){for(var e=0,g=c.length;e<g;e++){if((f=="idx"&&h==c[e])||(f=="value"&&c[e]==k[h].value)||(f=="text"&&c[e]==k[h].text)){l.removeChild(k[h]);break}}}}else{for(var h=k.length-1;h>=0;h--){if((f=="idx"&&h==c)||(f=="value"&&c==k[h].value)||(f=="text"&&c==k[h].text)){l.removeChild(k[h])}}}},getTextvalues:function(j,g){g=g||{};var c=g.egnoreEmpty!=null?g.egnoreEmpty:true;var k=[];var h=j.options;for(var f=0,e=h.length;f<e;f++){if(c&&h[f].value==""){continue}k.push([h[f].text,h[f].value])}return k},selectBoxes:function(j,g,h){if(!g){g=true}var f=EG.doc.getElementsByName(j);for(var e=0,c=f.length;e<c;e++){if(h!=null){f[e].checked=(f[e].value=h)?g:!g}else{f[e].checked=g}}},removeAllRows:function(c){c=EG.$(c);if(c==null){return}b.removeChilds(c)},getActionFrame:function(){if(!b.actionFrame){b.actionFrame=EG.CE({pn:EG.getBody(),tn:"iframe",id:"actionFrame",name:"actionFrame",style:"display:none"})}return b.actionFrame},getIdx:function(f){var e=b.childNodes(f.parentNode);for(var c=0;c<e.length;c++){if(e[c]==f){return c}}throw new Error("未找到索引")},contains:document.contains?function(c){return document.contains(c)}:function(c){return document.documentElement.contains(c)}}});EG.CSVG=b.CSVG;EG.CVML=b.CVML;EG.$=b.$;EG.CE=b.CE;EG.getValue=b.getValue;EG.setValue=b.setValue}());(function(){EG.define("EG.Style",[],function(ME){var getComputedStyle=window.getComputedStyle||(EG.doc&&EG.doc.defaultView&&EG.doc.defaultView.getComputedStyle);return{statics:{c:{dv:"display:inline-block;vertical-align:middle;",selectnone:"-moz-user-select:none;-webkit-user-select:none;user-select:none;",selectauto:"-moz-user-select:auto;-webkit-user-select:auto;user-select:auto;"},debugSize:function(ele){if(ele.getElement){ele=ele.getElement()}alert(EG.toJSON(EG.getSize(ele)))},parseTransform:function(transform){var m={};var regex=new RegExp("([a-zA-Z0-9]+)\\(([^)]*)\\)","ig");if(!transform){return m}var as=transform.match(regex);if(!as){return m}for(var i=0;i<as.length;i++){var a=as[i];var k=a.replace(regex,"$1");var vs=null;var ss=a.replace(regex,"$2");if(EG.Browser.isIE()){ss=ss.replace(new RegExp(" ","ig"),",")}eval("vs=["+ss+"]");m[k]=vs}return m},size2Num:function(size,refferNum){if(typeof(size)=="string"){if(EG.String.endWith(size,"%")){if(refferNum==null){Error("EG.Style#size2Num:百分比时参考尺寸不能为空.")}return parseInt(refferNum*parseInt(size.substr(0,size.length-1))/100)}else{if(EG.String.endWith(size,"px")){return parseInt(size.substr(0,size.length-2))}else{if(EG.String.endWith(size,"vh")){return parseInt(size.substr(0,size.length-2))*Style.vh}else{if(EG.String.endWith(size,"vw")){return parseInt(size.substr(0,size.length-2))*Style.vw}else{if(size=="thin"){return 1}else{if(size=="medium"){return 3}else{if(size=="thick"){return 5}else{if(size=="auto"){return 0}else{if(size==""){return 0}else{throw new Error("EG.Style#size2Num:暂不支持数值转换."+size)}}}}}}}}}}else{if(typeof(size)=="number"){return size}else{throw new Error("EG.Style#size2Num:参数类型无法识别."+typeof(size))}}},getSize:function(ele,css){if(ele.getElement){ele=ele.getElement()}var cs=Style.current(ele);var size={offsetWidth:ele.offsetWidth,offsetHeight:ele.offsetHeight,clientWidth:css?cs.width:ele.clientWidth,clientHeight:css?cs.height:ele.clientHeight,borderLeft:(cs.borderLeftStyle!="none")?(Style.size2Num(cs.borderLeftWidth)):0,borderTop:(cs.borderTopStyle!="none")?(Style.size2Num(cs.borderTopWidth)):0,borderRight:(cs.borderRightStyle!="none")?(Style.size2Num(cs.borderRightWidth)):0,borderBottom:(cs.borderBottomStyle!="none")?(Style.size2Num(cs.borderBottomWidth)):0,paddingLeft:(Style.size2Num(cs.paddingLeft)),paddingTop:(Style.size2Num(cs.paddingTop)),paddingRight:(Style.size2Num(cs.paddingRight)),paddingBottom:(Style.size2Num(cs.paddingBottom))};size.marginLeft=(Style.size2Num(cs.marginLeft));size.marginTop=(Style.size2Num(cs.marginTop));size.marginRight=(Style.size2Num(cs.marginRight));size.marginBottom=(Style.size2Num(cs.marginBottom));size.vScrollWidth=size.offsetWidth-size.clientWidth-size.borderLeft-size.borderRight;size.hScrollWidth=size.offsetHeight-size.clientHeight-size.borderTop-size.borderBottom;size.innerWidth=size.clientWidth-size.paddingLeft-size.paddingRight;size.innerHeight=size.clientHeight-size.paddingTop-size.paddingBottom;size.outerWidth=size.offsetWidth+size.marginLeft+size.marginRight;size.outerHeight=size.offsetHeight+size.marginTop+size.marginBottom;return size},getInnerTop:function(s){return Style.getInnerOut(s,"Top")},getInnerBottom:function(s){return Style.getInnerOut(s,"Bottom")},getInnerLeft:function(s){return Style.getInnerOut(s,"Left")},getInnerRight:function(s){return Style.getInnerOut(s,"Right")},getInnerOut:function(s,type){return s["margin"+type]+s["padding"+type]+s["border"+type]},num2Size:function(num){if(typeof(num)=="number"){return num+"px"}else{if(typeof(num)=="string"){return num}else{throw new Error("EG.Style#num2Size:参数类型无法识别."+typeof(num))}}},create:function(styles){var ss="";for(var key in styles){ss+=(key+"{"+styles[key]+"}\n")}if(Style.element.styleSheet){Style.element.styleSheet.cssText+=ss}else{Style.element.appendChild(EG.doc.createTextNode(ss))}},createSheet:function(url){var css=EG.CE({tn:"link",rel:"stylesheet",rev:"stylesheet",type:"text/css",media:"screen",href:url});EG.doc.getElementsByTagName("head")[0].appendChild(css);return css},current:function(ele){if(getComputedStyle){return getComputedStyle(ele)}else{return ele.currentStyle}},parse:function(style){var m={};var ses=style.split(";");for(var i=0;i<ses.length;i++){var sp=ses[i];if(sp==""){continue}var sess=sp.split(":");m[sess[0]]=sess[1]}return m},css:function(ele,style){if(ele.getElement){ele=ele.getElement()}if(!ele.cacheStyle){ele.cacheStyle={}}var m={};if(typeof(style)=="string"){m=ME.parse(style)}else{if(typeof(style)=="object"){m=style}else{throw new Error("EG.Style#set:style类型错误")}}var clearSize=false;for(var k in m){var v=m[k];if(k=="float"){k=("cssFloat" in ele.style)?"cssFloat":"styleFloat"}else{if(k=="vertical-align"){k="verticalAlign"}}if(EG.Browser.isIE8()){var ks=k.split("-");if(ks.length>1){k=ks[0]+ks[1].substr(0,1).toUpperCase()+ks[1].substr(1)}}ele.style[k]=v;if(ele.cacheStyle[k]!==v){ele.cacheStyle[k]=v;if(k=="width"||k=="height"||k.indexOf("margin")==0||k.indexOf("padding"==0)){clearSize=true}}}if(clearSize){ele.egSize=null}},isHide:function(ele,inherit){if(!inherit){var cs=Style.current(ele);return cs.display=="none"}else{var p=ele;while(p){if(p==EG.getBody()){return false}var cs=Style.current(p);if(cs.display=="none"||cs.visible=="none"){return true}p=p.parentNode}return true}},show:function(){Style.displays(arguments,true)},hide:function(){Style.displays(arguments,false)},visibles:function(eles,visible){for(var i=0,il=eles.length;i<il;i++){Style.visible(eles[i],visible)}},visible:function(ele,visible){ele.style.visibility=visible?"visible":"hidden"},displays:function(eles,display){for(var i=0,il=eles.length;i<il;i++){Style.display(eles[i],display)}},display:function(ele,display){if(typeof(ele)=="string"){ele=EG.$(ele)}if(ele.getElement){ele=ele.getElement()}if(typeof(display)=="boolean"){var cs=Style.current(ele);var cd=ele.style.display||(cs?cs.display:"");if(!display){if(cd!="none"){if(EG.DOM.contains(ele)){ele.oDisplay=cd}ele.style.display="none"}}else{if(cd=="none"){ele.style.display=ele.oDisplay||""}}}else{if(typeof(display)=="string"){ele.style.display=display}else{throw new Error("EG.Style#display:不支持"+display)}}},centerChilds:function(pn,horizontal){var cns=pn.childNodes;if(cns.length>0){if(horizontal){var mw=0;for(var i=0;i<cns.length;i++){var cn=cns[i];var s=EG.getSize(cn);if(i==0){mw+=s.outerWidth-s.marginLeft-s.marginRight}else{var sl=EG.getSize(cns[i-1]);var lm=Math.max(sl.marginRight,s.marginLeft);mw+=lm+s.outerWidth-s.marginRight-s.marginLeft}}var m=parseInt((EG.getSize(pn).innerWidth-mw)/2);EG.css(cns[0],"margin-left:"+m+"px");EG.css(cns[cns.length-1],"margin-right:"+m+"px")}else{alert("暂时不支持")}}},middleChilds:function(pn,horizontal){var cns=pn.childNodes;var ps=EG.getSize(pn);if(cns.length>0){if(horizontal){for(var i=0;i<cns.length;i++){var cn=cns[i];var s=EG.getSize(cn);var m=parseInt((ps.innerHeight-(s.outerHeight-s.marginTop-s.marginBottom))/2);EG.css(cn,"margin-top:"+m+"px;margin-bottom:"+m+"px")}}else{alert("暂不支持")}}},topChilds:function(pn,horizontal){var cns=pn.childNodes;var ps=EG.getSize(pn);if(cns.length>0){if(horizontal){for(var i=0;i<cns.length;i++){var cn=cns[i];var s=EG.getSize(cn);var m=parseInt((ps.innerHeight-(s.outerHeight-s.marginTop-s.marginBottom)));EG.css(cn,"margin-top:"+0+"px;margin-bottom:"+m+"px")}}else{alert("暂不支持")}}},bottomChilds:function(pn,horizontal){var cns=pn.childNodes;var ps=EG.getSize(pn);if(cns.length>0){if(horizontal){for(var i=0;i<cns.length;i++){var cn=cns[i];var s=EG.getSize(cn);var m=parseInt((ps.innerHeight-(s.outerHeight-s.marginTop-s.marginBottom)));EG.css(cn,"margin-top:"+m+"px;margin-bottom:"+0+"px")}}else{alert("暂不支持")}}},center:function(ele,ct){var l=Math.abs(parseInt((ct||ele.parentNode).clientWidth-EG.getSize(ele).outerWidth)/2);ele.style.position="absolute";ele.style.left=l+"px"},middle:function(ele,ct){var l=Math.abs(parseInt((ct||ele.parentNode).clientHeight-EG.getSize(ele).outerHeight)/2);ele.style.position="absolute";ele.style.top=l+"px"},left:function(ele,ct){ele.style.position="absolute";ele.style.left="0px"},right:function(ele,ct){ele.style.position="absolute";ele.style.right="0px"},top:function(ele,ct){ele.style.position="absolute";ele.style.top="0px"},bottom:function(ele,ct){ele.style.position="absolute";ele.style.bottom="0px"},full:function(ele,ct){if(!ct){ct=ele.parentNode}if(!ct){throw new Error("EG.Style#full:父元素不能为空")}ele.style.width=ct.clientWidth+"px";if(ct==EG.getBody()){ele.style.height=Math.max((EG.doc.documentElement?EG.doc.documentElement.clientHeight:EG.getBody().clientHeight),EG.getBody().clientHeight)+"px"}else{ele.style.height=ct.clientHeight+"px"}},moveTo:function(ele,pos){if(pos.x){Style.css(ele,"left:"+pos.x+"px")}if(pos.y){Style.css(ele,"top:"+pos.y+"px")}},capcity:function(ele,val){if(EG.doc.all){ele.style.filter="alpha(opacity="+parseInt(val)+")"}else{ele.style.opacity=parseInt(val)/100}},fade:function(ele,start,end,callback,speed){if(!speed){speed=20}var _a=(start>end)?-5:5;Style.capcity(ele,start+_a);if(start==end){if(!callback){return}return callback()}window.setTimeout(function(){Style.fade(ele,start+_a,end,callback,speed)},speed);return null},setCls:function(ele,cls,clsPre){if(cls==null){return}clsPre=clsPre?(clsPre+"-"):"";if(!EG.isArray(cls)){cls=[cls]}if(cls.length==0){return}var s="";for(var i=0;i<cls.length;i++){if(i!=0){s+=" "}s+=clsPre+cls[i]}ele.className=s},addCls:function(ele,cls){var cn=ele.className||"";var clss=cn.split(" ");if(EG.Array.has(clss,cls)){return}for(var i=clss.length;i>=0;i--){if(clss[i]==""){EG.Array.del(clss,i)}}clss.push(cls);ele.className=clss.join(" ")},removeCls:function(ele,cls){var cn=ele.className||"";var clss=cn.split(" ");if(!EG.Array.has(clss,cls)){return}EG.Array.remove(clss,cls);ele.className=clss.join(" ")}}}});var Style=EG.Style;Style.vh=window.screen.height/100;Style.vw=window.screen.width/100;EG.getSize=Style.getSize;EG.debugSize=Style.debugSize;EG.css=Style.css;EG.hide=Style.hide;EG.show=Style.show;EG.setCls=Style.setCls;EG.onload(function(){Style.element=EG.doc.createElement("style");Style.element.type="text/css";EG.doc.getElementsByTagName("HEAD").item(0).appendChild(Style.element);if(EG.Browser.isIE6()){Style.c.dv+="*display:inline;zoom:1;"}})})();(function(){EG.define("EG.$Q",{constructor:function(a){return new EG.$Q.prototype._init(a)},_init:function(a){this.ele=EG.$(a);return this},val:function(a){if(arguments.length==0){return EG.DOM.getValue(this.ele)}else{return EG.DOM.setValue(this.ele,a)}},show:function(){EG.show(this.ele);return this},hide:function(){EG.hide(this.ele);return this},center:function(a){EG.Style.center(this.ele,a);return this},hasClass:function(a){return this.ele.className.indexOf(a)>=0},removeChilds:function(){return EG.DOM.removeChilds(this.ele)},appendChild:function(a){this.ele.appendChild(a);return this},innerHTML:function(a){this.ele.innerHTML=a;return this},getChild:function(a){return this.ele.childNodes[a]},css:function(a){EG.css(this.ele,a);return this}});(function(){var b=["click","dblclick","mouseover","mouseout"];for(var a=0;a<b.length;a++){EG.$Q.prototype[b[a]]=function(c){var e=this;EG.bindEvent(this.ele,function(){c.apply(e)});return this}}})();EG.$Q.prototype._init.prototype=EG.$Q.prototype})();(function(){EG.define("EG.MMVC",{statics:{mmvcPath:"/mmvc/",getPath:function(uri){if(uri==null){uri=EG.MMVC.mmvcPath}return{call:uri+"jsonrpc/call",connect:uri+"push/connect",moditor:uri+"utils/profile",upload:uri+"upload",download:uri+"download"}},call:function(cfg){cfg=cfg||{};var muti=cfg.muti,rpc=cfg.rpc,method=cfg.method,params=EG.unnull(cfg.params,[]),exHandler=cfg.exHandler,cb=cfg.callback,httpMethod=EG.unnull(cfg.httpMethod,"POST"),mutiReturn=cfg.mutiReturn||"map",useRandom=EG.unnull(cfg.useRandom,true),erhandler=null;if(!muti&&EG.String.isEmpty(rpc)){throw new Error("EG#call:rpc不能为空")}if(!muti&&EG.String.isEmpty(method)){throw new Error("EG#call:method不能为空")}var url=cfg.callPath||EG.MMVC.getPath().call;var strParams="";if(muti){strParams=(mutiReturn!="map"?"mutiReturn="+mutiReturn+"&":"")+"muti="+EG.Ajax.javaURLEncoding(EG.Tools.toJSON(muti))}else{strParams="rpc="+rpc+"&method="+method+"&params="+EG.Ajax.javaURLEncoding(EG.Tools.toJSON(params))}var content=null;if(useRandom){strParams+="&random="+new Date().getTime()+"-"+Math.random()*1000}if(httpMethod=="GET"){url+="?"+strParams}else{content=strParams}var handleEx=function(ex){if(EG.MMVC.exClassHandlers[ex.exClass]){return EG.MMVC.exClassHandlers[ex.exClass](ex)}else{if(exHandler){return exHandler(ex)}else{if(EG.MMVC.defExHandler){return EG.MMVC.defExHandler(ex)}else{throw new Error(ex.exMsg)}}}};var callback=function(resText,req){var obj=null;if(resText!=null&&resText!=""){eval("obj="+resText+";")}if(obj!=null){if(!EG.isArray(obj)){throw new Error("结构非数组:"+resText)}if(obj[0]===0){return cb(obj[1])}else{return handleEx(new EG.MMVC.Exception(obj[1]["exClass"],obj[1]["exMsg"]))}}else{return cb(obj)}};return EG.Ajax.send({url:url,method:httpMethod,content:content,callback:callback,erhandler:erhandler})},exClassHandlers:{},defExHandler:null,connect:function(cfg){cfg=cfg||{};var actionFrame=cfg.actionFrame;if(!actionFrame){actionFrame=EG.DOM.getActionFrame()}actionFrame.src=EG.MMVC.getPath().connect},download:function(cfg){var actionFrame=cfg.actionFrame;var policy=cfg.policy;var params=cfg.params||{};var ps="";for(var key in params){ps+="&"+key+"="+EG.Ajax.javaURLEncoding(params[key])}if(!actionFrame){actionFrame=EG.DOM.getActionFrame()}actionFrame.src=EG.MMVC.getPath().download+"?policy="+policy+ps}}});EG.MMVC.Exception=function(exClass,exMsg){this.exClass=exClass;this.exMsg=exMsg};EG.call=EG.MMVC.call})();(function(){EG.define("EG.Anim",function(){return{statics:{add:function(c,a){if(!EG.Array.isArray(a)){a=[a]}if(!c.anims){c.anims=[]}for(var b=0;b<a.length;b++){if(EG.Array.has(c.anims,a[b])){continue}c.anims.push(a[b])}},remove:function(c,a){if(a==null&&c.anims!=null){a=c.anims}if(!EG.Array.isArray(a)){a=[a]}if(!c.anims){c.anims=[]}for(var b=0;b<a.length;b++){EG.Array.remove(c.anims,a[b])}},stop:function(a){a.fn_stopAnima.apply(a)},beforePlay:function(b){var a=EG.Style.current(b);b.oldOpacity=b.style.opacity||(a?a.opacity:1);EG.css(b,"opacity:0")},play:function(c){if(!c.oldOpacity){EG.Anim.beforePlay(c)}for(var b=0;b<c.anims.length;b++){EG.Style.addCls(c,c.anims[b])}if(!c.fn_stopAnima){c.fn_stopAnima=function(){for(var g=0;g<this.anims.length;g++){EG.Style.removeCls(this,this.anims[g])}};var a={WebkitAnimation:"webkitAnimationEnd",OAnimation:"oAnimationEnd",msAnimation:"MSAnimationEnd",animation:"animationend"};if(!Modernizr){return}var e=a[Modernizr.prefixed("animation")];EG.Event.bindEvent(c,e,c.fn_stopAnima)}var f=c.oldOpacity||1;EG.css(c,"opacity:"+f)},moveout:function(b,c,a){},movein:function(){},move:function(j,b,k,g,e,a,h){if(!e){e=1}if(!a){a=25}var f=e/a;if(h==null){h=0}h+=k/a;if(h>k){h=k}if(h<k){if(!EG.Array.isArray(j)){j=[j]}for(var c=0;c<j.length;c++){EG.css(j[c],"margin-"+b+":"+(-h)+"px")}window.setTimeout(function(){EG.Anim.move(j,b,k,g,e,a,h)},f)}else{if(!g){return}return g()}},rotate:function(f,j,e,a,h,g,c){var b;if(!h){h=25}if(!c){c=new Date().getTime();c+=j}if(new Date().getTime()>=c){alert("OVER");return}b=1000/h;if(g==null){g=0}g+=e;g=g%360;EG.css(f,"-webkit-transform: rotate(-"+g+"deg);-moz-transform: rotate(-"+g+"deg);-ms-transform: rotate(-"+g+"deg);-o-transform: rotate(-"+g+"deg);transform: rotate(-"+g+"deg);");window.setTimeout(function(){EG.Anim.rotate(f,j,e,a,h,g,c)},b)}}}})})();(function(){EG.define("EG.UI",{statics:{skin:"default",getSkinPath:function(a){return EG.basePath+"/skins/"+EG.UI.skin+"/"+a},sheet:null,setSkin:function(a){if(!EG.UI.sheet){EG.UI.sheet=EG.Style.createSheet("")}EG.UI.sheet.href=EG.basePath+"/skins/"+a+"/ui.css";EG.UI.skin=a},GITEMIDX:0,GITEMS:{}}});EG.RESIZING=false;EG.onload(function(){})})();(function(){EG.define("EG.ui.Item",[],function(a){return{config:{renderTo:null,id:null,width:null,height:null,style:null,cls:null,region:null,hidden:false,animate:null},constructor:function(b){this.initItem(b);this.build();this.getElement().item=this;if(this.id){this.getElement().id=this.id}a.setStyle(this);this.afterBuild();this.setAnimate(this.animate);a.doRenderTo(this);if(this.hidden){EG.hide(this.getElement())}},setAnimate:function(b){this.animate=b;if(this.animate){var c=this.getElement();EG.Anim.remove(c);EG.Anim.add(c,this.animate.split(" "))}},afterBuild:function(){},getStyleEle:function(){return this.getElement()},getElement:function(){return this.element},render:function(){},renderOuter:function(){a.fit(this);this.saveOuterSize()},saveOuterSize:function(){var b=EG.Style.current(this.getElement()).overflow;EG.css(this.getElement(),"overflow:hidden;");this._outerSize=this.getSize();EG.css(this.getElement(),"overflow:"+b+";")},initItem:function(b){this.initConfig(b);this.oWidth=this.width;this.oHeight=this.height;this.pItem=null},getOWidth:function(){return this.oWidth},getOHeight:function(){return this.oHeight},getSize:function(){return EG.getSize(this.getElement())},getInnerHeight:function(){return EG.getSize(this.getElement()).innerHeight},getInnerWidth:function(){return EG.getSize(this.getElement()).innerWidth},build:function(){this.element=EG.CE({tn:"div",item:this})},destroy:function(){},setHidden:function(b){this.hidden=b;if(this.hidden){EG.hide(this.getElement())}else{EG.show(this.getElement())}},isItem:true,statics:{itemClasses:{},regist:function(b,c){a.itemClasses[b]=c},create:function(e,b){if(!e){e="panel"}var c=EG._alias[e];if(!c){throw new Error("EG.ui.Item#create:不支持类型:"+e)}return new c(b)},setWidth:function(b,c){a.setSize(b,c,"width")},setHeight:function(c,b){a.setSize(c,b,"height")},setStyle:function(b){if(b.cls){EG.setCls(b.getStyleEle(),b.cls)}if(b.style){EG.css(b.getStyleEle(),b.style)}},initItem:function(b){if(b.renderTo){if(b.element.parentNode==null){if(a.isItem(b.renderTo)){b.renderTo.addItem(b)}else{if(EG.DOM.isElement(b.renderTo)){b.renderTo.appendChild(b.element)}}}b.render()}},setSize:function(b,e,c){if(e!=null){if(typeof(e)=="number"){EG.css(b,c+":"+e+"px")}else{EG.css(b,c+":"+e)}}else{EG.css(b,c+":100%")}},fit:function(f){if(a.isItem(f)){f={item:f}}var m=f.item,c=f.element;if(!c){if(m){c=m.element}else{throw new Error("fit:没有element")}}var b=f.sSize||EG.getSize(c);var j=f.dSize;if(!j){if(m&&(m.width!=null||m.height!=null)){j={width:m.width,height:m.height}}else{j={width:EG.unnull(f.width,"100%"),height:EG.unnull(f.height,"100%")}}}var g=f.pSize||EG.getSize(c.parentNode);if(g.width==null&&g.innerWidth!=null){g.width=g.innerWidth}if(g.height==null&&g.innerHeight!=null){g.height=g.innerHeight}var k=f.type||"all",l,e;if(j.width!=null&&j.width!="auto"&&(k=="all"||k=="width")){l=EG.Style.size2Num(j.width,g.width)-(b.outerWidth-b.innerWidth-b.vScrollWidth);if(l<0){l=0}EG.css(c,{width:l+"px"})}if(j.height!=null&&j.height!="auto"&&(k=="all"||k=="height")){e=EG.Style.size2Num(j.height,g.height)-(b.outerHeight-b.innerHeight-b.hScrollWidth);if(e<0){e=0}EG.css(c,{height:e+"px"})}return{width:l,height:e,pSize:g}},pack:function(f){var k=f.item;var h=f.pEle||(k?k.getElement():null);var c=f.cEle;var b=f.width;var j=f.height;var g=f.pSize||EG.getSize(h);if(c&&(b==null||j==null)){var l=EG.getSize(c);if(b==null){b=l.outerWidth}if(j==null){j=l.outerHeight}}var e={};if(b!=null){b=(g.paddingLeft+b+g.paddingRight);EG.css(h,"width:"+b+"px");e.width=b}if(j!=null){j=(g.paddingTop+j+g.paddingBottom);EG.css(h,"height:"+j+"px");e.height=j}return e},isItem:function(b){return b&&typeof(b.getElement)=="function"},doRenderTo:function(b){if(b.renderTo){EG.$(b.renderTo).appendChild(b.getElement());b.render()}},afterExtend:function(b){(function(){var c=b.prototype.render;if(c){b.prototype.render=function(){if(!EG.DOM.contains(this.getElement())){return}if(EG.Style.isHide(this.getElement().parentNode,true)){return}c.apply(this,arguments)}}})()},fixBody:function(c){var b=c.parentNode;if(b==EG.getBody()){EG.css(EG.getBody(),"margin:0px;padding:0px;width:100%;height:100%");EG.css(c,"position:absolute;left:0px;top:0px;")}},$:function(b){return EG.$(b).item},getPath:function(c){var b=c.getClass()._className;while((c=c.pItem)){b=c.getClass()._className+"("+(c.id?c.id:"")+")>"+b}return b}}}})})();(function(){EG.define("EG.ui.Container",["EG.ui.Item"],function(a,b){return{extend:a,config:{layout:"default",itemsConfig:[],cnConfig:[],innerHTML:null,isRenderingOuter:false},constructor:function(c){this.items=[];this.callSuper([c])},afterBuild:function(){this.setLayout(this.layout,false);if(this.itemsConfig.length>0){this.addItem(this.itemsConfig,false)}else{if(this.cnConfig.length>0){this.addChildren(this.cnConfig,null,false)}else{if(this.innerHTML){this.getItemContainer().innerHTML=this.innerHTML}}}},getItemContainer:function(){return this.element},addItem:function(j,k,c){if(typeof(k)=="undefined"){k=true}var g=(!EG.isArray(j))?[j]:j;var f=[];for(var h=0,e=g.length;h<e;h++){j=g[h];if(EG.isLit(j)){j=a.create(j.xtype,j)}j.pItem=this;if(c>=0){EG.Array.insert(this.items,c,j)}else{this.items.push(j)}if(this.layoutManager.addOnLayout){continue}EG.DOM.addChildren(this.getItemContainer(),j.getElement(),c);f.push(j)}if(k){if(!this.isPItemAuto()){this.doLayout()}}return f.length>1?f:f[0]},removeItem:function(c,e){if(e==null){e=true}if(typeof(c)=="number"){c=EG.Array.get(this.items,c)}this.getItemContainer().removeChild(c.getElement());EG.Array.remove(this.items,c);c.pItem=null;if(e){if(!this.isPItemAuto()){this.doLayout()}}},addChildren:function(f,c,e){if(e==null){e=true}EG.DOM.addChildren(this.getItemContainer(),f,c);if(e){this.doLayout()}},render:function(){EG.ui.Item.fixBody(this.element);a.fit(this);if(this.animate){EG.Anim.beforePlay(this.getElement())}if(this.items.length>0||this.layoutManager.force){this.doLayout()}if(this.animate){EG.Anim.play(this.getElement())}},renderOuter:function(){this.isRenderingOuter=true;a.fit(this);if(this.isAutoWidth()||this.isAutoHeight()){if(this.items.length>0||this.layoutManager.force){this.doLayout()}}this.saveOuterSize();this.isRenderingOuter=false},getItem:function(c){if(typeof(c)=="number"){return this.items[c]}else{if(this.layoutManager){return this.layoutManager.getItem(c)}else{throw new Error("无法获取Item,参数不对或布局器不存在")}}},setLayout:function(c,e){if(e==null){e=true}this.layout=c;this.layoutManager=EG.ui.Layout.create(this.layout,this);if(e){this.layoutManager.doLayout()}},doLayout:function(){var c=EG.Style.current(this.element).overflow;EG.css(this.element,"overflow:hidden");this.layoutManager.doLayout();EG.css(this.element,"overflow:"+c)},isPItemAuto:function(){if(this.pItem){var c=this.pItem.isAutoWidth();var e=this.pItem.isAutoWidth();if(!c&&!e&&(this.isAutoWidth()||this.isAutoHeight())){this.pItem.doLayout();return true}else{if(c||e){var f=this.pItem;while(f.pItem){if(!f.pItem.isAutoWidth()&&!f.pItem.isAutoHeight()){break}else{f=f.pItem}}f.doLayout();return true}}}return false},getItemLeft:function(){return this.getItem({region:"left"})},getItemCenter:function(){return this.getItem({region:"center"})},getItemRight:function(){return this.getItem({region:"right"})},getItemTop:function(){return this.getItem({region:"top"})},getItemBottom:function(){return this.getItem({region:"bottom"})},isAutoWidth:function(){return this.getOWidth()=="auto"},isAutoHeight:function(){return this.getOHeight()=="auto"},setInnerWidth:function(e,c){c=EG.n2d(c,true);EG.ui.Container.autoSize(this.getItemContainer(),e,"width");if(c){this.width=this.getSize().outerWidth}},setInnerHeight:function(c,e){e=EG.n2d(e,true);EG.ui.Container.autoSize(this.getItemContainer(),c,"height");if(e){this.height=this.getSize().outerHeight}},getInnerHeight:function(){return EG.getSize(this.getItemContainer()).innerHeight},getInnerWidth:function(){return EG.getSize(this.getItemContainer()).innerWidth},destroy:function(){for(var c=0;c<this.items.length;c++){this.items[c].destroy()}},clear:function(){for(var c=0;c<this.items.length;c++){if(this.items[c].isContainer){this.items[c].clear()}this.items[c].destroy()}EG.DOM.removeChilds(this.getItemContainer());EG.Array.clear(this.items)},isContainer:true,statics:{autoSize:function(f,c,e){var g=f.isItem?f.getElement():f;EG.css(g,(e=="width"?"width":"height")+":"+c+"px")},afterExtend:function(c){(function(){var e=c.prototype.doLayout;if(e){c.prototype.doLayout=function(){if(!EG.DOM.contains(this.getElement())){return}e.apply(this,arguments)}}})()}}}})})();(function(){EG.ui.Component=function(){};EG.ui.Component.addChildren=function(e,f){var c=(!EG.isArray(f))?[f]:f;for(var b=0,a=c.length;b<a;b++){f=c[b];if(EG.isLit(f)){f=EG.CE(f)}e.getElement().appendChild(f)}}})();(function(){EG.define("EG.ui.Drag",["EG.ui.Item"],function(a,b){return{config:{target:null,handle:null,parent:null,moveTarget:false,beforeStartDrag:null,afterEndDrag:null,sycHandleSize:false,cursoron:true},constructor:function(c){b.load();this.initConfig(c);this.handles=[];if(this.handle||this.target){this.bindHandle(this.handle||this.target)}this.bindParent(this.parent||EG.getBody())},bindHandle:function(g){this.handles.push(g);g=(!EG.isArray(g))?[g]:g;for(var e=0,c=g.length;e<c;e++){var f=g[e];if(f.getElement){f=f.getElement}EG.CE({ele:f,style:"cursor:move",onmousedown:b._events.handle.onmousedown,me:this})}},bindParent:function(c){this.parent=c},statics:{load:function(){if(!b.loaded){b.dragGhost=EG.CE({pn:EG.getBody(),tn:"div",cls:"eg_drager"});EG.bindEvent(EG.getBody(),"onselectstart",b._events.parent.onselectstart);EG.bindEvent(EG.getBody(),"onmouseup",b._events.parent.onmouseup);EG.bindEvent(EG.getBody(),"onmousemove",b._events.parent.onmousemove);b.loaded=true;EG.hide(b.dragGhost)}},isDraging:function(){return b.cur!=null},startDrag:function(f){b.cur=this.me;var g=this;while(true){if(EG.Array.has(b.cur.handles,g)){break}else{g=this.parentNode;if(g==null){break}}}b.selectable=false;if(g==null){throw new Error("发生预料外的错误->未找到拖拽节点")}b.curHandle=g;var c=b.cur.sycHandleSize?g:b.cur.target;b.refPos=EG.Tools.getMousePos(f,c);b.refPos_w=EG.Tools.getMousePos(f);b.showDragGhost(f)},draging:function(g){if(!b.isDraging()){return}if(!EG.Tools.isPressLeft(g)){b.endDrag(g);return}var h=EG.Tools.getMousePos(g);var f=h.y;var c=h.x;if(!b.cur.cursoron){f+=10;c+=10}else{f=f-b.refPos.y;c=c-b.refPos.x}EG.css(b.dragGhost,"top:"+f+"px;left:"+c+"px;");if(b.cur.beforeStartDrag){b.cur.beforeStartDrag.apply(b.cur,[g])}},endDrag:function(k){if(!b.cur){return}if(EG.Style.isHide(b.dragGhost)){return}var f=EG.Tools.getMousePos(k);var g=EG.Style.current(b.dragGhost);var j=g.top=="auto"?"0":g.top;var h=g.left=="auto"?"0":g.left;if(b.cur.moveTarget&&b.cur.parent!=EG.getBody()){var l=EG.Style.current(b.cur.target||b.cur.moveTarget);var m=l.top=="auto"?"0":l.top;var c=l.left=="auto"?"0":l.left;j=(parseInt(EG.String.removeEnd(m,"px"))+f.y-b.refPos_w.y)+"px";h=(parseInt(EG.String.removeEnd(c,"px"))+f.x-b.refPos_w.x)+"px"}if(b.cur.moveTarget){EG.css(b.cur.target,"position:absolute;top:"+j+";left:"+h+";")}EG.hide(b.dragGhost);b.selectable=true;if(b.cur.afterEndDrag){b.cur.afterEndDrag.apply(this,[k])}b.cur=null},changeSelectable:function(c){if(!c){EG.Style.css(EG.getBody(),EG.Style.c.selectnone)}else{EG.Style.css(EG.getBody(),EG.Style.c.selectauto)}b.selectable=c},_events:{handle:{onmousedown:function(f){var c=this;b.changeSelectable(false);b.dragThread=window.setTimeout(function(){b.startDrag.apply(c,[f])},100);return true}},parent:{onselectstart:function(c){return b.selectable},onmousemove:function(c){b.draging(c)},onmouseup:function(c){if(b.dragThread){window.clearTimeout(b.dragThread);b.dragThread=null}if(b.cur){b.endDrag(c)}b.changeSelectable(true)}}},bindDrag:function(c){new EG.ui.Drag(c)},showDragGhost:function(h){h=EG.Event.getEvent(h);var k=EG.Tools.getMousePos(h);var g=k.y-b.refPos.y;var f=k.x-b.refPos.x;EG.css(b.dragGhost,"top:"+g+"px;left:"+f+"px;");EG.show(b.dragGhost);var j=b.cur.sycHandleSize?b.curHandle:b.cur.target;var c=EG.getSize(j);a.fit({element:b.dragGhost,dSize:{width:c.outerWidth,height:c.outerHeight}})},hideDragGhost:function(){EG.hide(b.dragGhost)}}}})})();(function(){EG.define("EG.ui.Pop",["EG.ui.Container","EG.ui.Item"],function(a,b,c){return{extend:a,config:{cls:"eg_pop",lock:false,posFix:true,innerHTML:null,target:null,parent:null,afterOpen:null,expandOuter:true,onClose:null},constructor:function(e){var f=this;this.callSuper([e]);EG.hide(this.element)},build:function(){this.element=EG.CE({tn:"div",cls:this.cls+"-outer",item:this,cn:[this.dLocker=EG.CE({tn:"div",cls:this.cls+"-locker",item:this}),this.dPop=EG.CE({tn:"div",cls:this.cls,item:this})]})},getStyleEle:function(){return this.dPop},getItemContainer:function(){return this.dPop},open:function(){EG.DOM.insertAfter(this.element,this.element.parentNode.lastChild);EG.show(this.element);this.render();if(this.afterOpen){this.afterOpen.apply(this)}},close:function(){if(this.onClose){var e=this.onClose.apply(this.onCloseSrc||this,[]);if(e===false){return}}EG.hide(this.element)},setAnimate:function(e){this.animate=e;if(this.animate){var f=this.dPop;EG.Anim.remove(f);EG.Anim.add(f,this.animate.split(" "))}},render:function(){var f=this.parent||this.getElement().parentNode;var e=EG.getSize(f);if(this.expandOuter){b.fit({element:this.element,dSize:{width:e.innerWidth,height:e.innerHeight}})}b.fit({element:this.dPop,dSize:{width:this.width,height:this.height}});if(this.items.length>0){this.doLayout()}if(this.animate){EG.Anim.beforePlay(this.dPop)}if(this.lock){this.fullLock()}if(this.posFix){this.doPosFix()}if(this.animate){EG.Anim.play(this.dPop)}},fullLock:function(){b.fit({element:this.dLocker,dSize:{width:"100%",height:"100%"},pSize:EG.getSize(this.getElement())})},doPosFix:function(){EG.Style.center(this.dPop,this.getElement().parentNode);EG.Style.middle(this.dPop,this.getElement().parentNode)},moveTo:function(e){EG.Style.moveTo(this.dPop,e)},isOpen:function(){return !EG.Style.isHide(this.element)}}})})();(function(){EG.define("EG.ui.Button",["EG.ui.Item"],function(a,b){return{alias:"button",extend:a,config:{text:"",click:null,mouseover:null,mouseout:null,icon:null,cls:"eg_button",iconAble:true,menuConfig:null,iconOnly:false,textStyle:null,tabIndex:0,renderSizeAble:false,menuStyle:null,titleStyle:null,disable:false},constructor:function(c){this.callSuper([c]);if(!this.icon||!this.iconAble){EG.hide(this.dIcon)}else{this.dIcon.className=this.cls+"-icon icon_"+this.icon}this.buildMenu();if(this.textStyle){this.setTextStyle(this.textStyle)}this.setDisable(this.disable)},build:function(){var c=this;var e=this.getClass();if(this.iconOnly){this.element=EG.CE({tn:"div",cls:this.cls,item:this,onclick:e._events.element.onclick,cn:[this.dIcon=EG.CE({tn:"a",title:this.text})]})}else{this.element=EG.CE({tn:"div",cls:this.cls,item:this,onclick:e._events.element.onclick,onmouseover:e._events.element.onmouseover,onmouseout:e._events.element.onmouseout,cn:[this.dOuter=EG.CE({tn:"div",cls:this.cls+"-outer",item:this,cn:[this.dIcon=EG.CE({tn:"div"}),this.dTitle=EG.CE({tn:"div",cls:this.cls+"-title",innerHTML:this.text,style:EG.unnull(this.titleStyle,"")}),this.dMulti=EG.CE({tn:"div",cls:this.cls+"-multi",onclick:this.showMenu,onclickSrc:this,item:this})],onmouseover:e._events.outer.onmouseover,onmouseout:e._events.outer.onmouseout})]})}if(typeof(this.tabIndex)=="number"){this.element.tabIndex=this.tabIndex}},doClick:function(){if(this.disable){return}if(this.click){this.click.apply(this["clickSrc"]||this)}},setTextStyle:function(c){this.textStyle=c;EG.css(this.dTitle,this.textStyle)},setText:function(c){this.text=c;this.dTitle.innerHTML=c},setMenu:function(c){this.menuConfig=c;if(this.dMenus){EG.DOM.remove(this.dMenus)}if(this.dMulti){EG.DOM.remove(this.dMulti)}this.buildMenu()},buildMenu:function(){if(this.menuConfig&&this.menuConfig.length>0){this.dMenus=EG.CE({pn:this.element,tn:"div",cls:this.cls+"-menu",style:"position:absolute;z-index:1;overflow:hidden;"});for(var e=0,c=this.menuConfig.length;e<c;e++){var f=this.menuConfig[e];EG.CE({pn:this.dMenus,tn:"a",idx:e,innerHTML:f.text,href:"javascript:void(0)",cls:this.cls+"-mi",item:this,onclick:b._events.aMenuEle.onclick})}}else{if(this.dMulti){EG.hide(this.dMulti)}}if(this.dMenus){EG.hide(this.dMenus);if(this.menuStyle){EG.css(this.dMenus,this.menuStyle)}}},render:function(){if(this.renderSizeAble){a.fit(this);var e=EG.getSize(this.element);a.fit({element:this.dOuter,pSize:e,dSize:{width:"100%",height:"100%"}});a.fit({element:this.dIcon,pSize:EG.getSize(this.dOuter),dSize:{height:"100%"}});a.fit({element:this.dTitle,pSize:e,dSize:{height:"100%"}});a.fit({element:this.dMulti,pSize:e,dSize:{height:"100%"}});EG.css(this.dTitle,"line-height:"+EG.getSize(this.dTitle).innerHeight+"px")}if(this.dMenus){var c=EG.getSize(this.element);a.fit({element:this.dMenus,sSize:c,dSize:{width:c.outerWidth}})}},setDisable:function(c){this.disable=c;if(c){if(this.dOuter){this.dOuter.className=this.cls+"-disable"}}else{if(this.dOuter){this.dOuter.className=this.cls+"-outer"}}},renderOuter:function(){if(this.renderSizeAble){a.fit(this)}this._outerSize=this.getSize()},showMenu:function(c){if(this.dMenus){EG.show(this.dMenus)}if(c){EG.Event.stopPropagation(c)}},statics:{_events:{element:{onclick:function(c){this.item.doClick();EG.Event.stopPropagation(c)},onmouseout:function(f){var c=this.item;if(c.disable){return}if(!c.dMenus){return}if(c.outThread!=null){return}c.outThread=setTimeout(function(){EG.hide(c.dMenus)},10);EG.Event.stopPropagation(f)},onmouseover:function(f){var c=this.item;if(c.disable){return}if(!c.dMenus){return}if(c.outThread!=null){clearTimeout(c.outThread);c.outThread=null}EG.Event.stopPropagation(f)}},outer:{onmouseover:function(){var c=this.item;if(c.disable){return}this.className=c.cls+"-outer "+c.cls+"-on"},onmouseout:function(){var c=this.item;if(c.disable){return}this.className=c.cls+"-outer"}},aMenuEle:{onclick:function(g){var c=this.item;if(c.disable){return}var f=c.menuConfig[this.idx];f.click.apply(f.clickSrc||c);EG.hide(c.dMenus);EG.Event.stopPropagation(g)}}}}}})})();(function(){EG.define("EG.ui.Dialog",["EG.ui.Pop","EG.ui.Drag","EG.ui.Button","EG.ui.Item"],function(f,b,a,c,e){return{alias:"dialog",extend:f,config:{cls:"eg_dialog",title:null,btnsConfig:null,zIndex:null,bodyStyle:null,headStyle:null,footStyle:null,closeable:true,fullable:false,dragable:false},constructor:function(g){this.callSuper([g]);if(this.title){this.setTitle(this.title)}if(this.btnsConfig){this.setButtons(this.btnsConfig)}this.setFullable(this.fullable);this.setCloseable(this.closeable);this.setDragable(this.dragable)},build:function(){var g=this;this.callSuper("build");this.dHead=EG.CE({pn:this.dPop,tn:"div",cls:this.cls+"-head",cn:[this.dTitle=EG.CE({tn:"div",cls:this.cls+"-title"}),EG.CE({tn:"div",cls:this.cls+"-trBtns",cn:[this.dFuller=EG.CE({tn:"a",cls:this.cls+"-fuller",me:this,onmouseup:e._events.dFuller.onmouseup,onmousedown:e._events.dFuller.onmousedown}),this.dCloser=EG.CE({tn:"a",cls:this.cls+"-closer",me:this,onmouseup:e._events.dCloser.onmouseup,onmousedown:e._events.dCloser.onmousedown})]})]});this.dBody=EG.CE({pn:this.dPop,tn:"div",cls:this.cls+"-body"});this.dFoot=EG.CE({pn:this.dPop,tn:"div",cls:this.cls+"-foot"});if(this.bodyStyle){EG.css(this.dBody,this.bodyStyle)}if(this.headStyle){EG.css(this.dHead,this.headStyle)}if(this.footStyle){EG.css(this.dFoot,this.footStyle)}if(this.zIndex!=null){EG.css(this.element,"z-index:"+this.zIndex)}},getItemContainer:function(){return this.dBody},setFullable:function(g){if(g){EG.show(this.dFuller)}else{EG.hide(this.dFuller)}},setDragable:function(g){this.dragable=g;if(!this.drag){this.drag=new b({target:this.dPop,parent:this.dLocker,handle:this.dHead,moveTarget:true})}},setCloseable:function(g){if(g){EG.show(this.dCloser)}else{EG.hide(this.dCloser)}},setButtons:function(k){EG.DOM.removeChilds(this.dFoot);if(k==null){return}for(var j=0,g=k.length;j<g;j++){var h=EG.isLit(k[j])?new a(k[j]):k[j];this.dFoot.appendChild(h.getElement())}},setTitle:function(g){EG.setValue(this.dTitle,g)},setInnerWidth:function(g){c.pack({pEle:this.dBody,width:g});c.pack({pEle:this.dPop,width:EG.getSize(this.dBody).outerWidth})},setInnerHeight:function(g){c.pack({pEle:this.dBody,height:g});c.pack({pEle:this.dPop,height:EG.getSize(this.dBody).outerHeight+EG.getSize(this.dHead).outerHeight+EG.getSize(this.dFoot).outerHeight})},getInnerHeight:function(){if(typeof(this.oHeight)=="number"){return EG.getSize(this.dPop).innerHeight-EG.getSize(this.dHead).outerHeight-EG.getSize(this.dFoot).outerHeight}else{return this.oHeight}},full:function(){if(!this._fulled){this.oParent=this.parent;this.oParentNode=this.getElement().parentNode;this._oWidth=this.width;this._oHeight=this.height;EG.getBody().appendChild(this.getElement());this.width="100%";this.height="100%";this.render();this._fulled=true}else{this.parent=this.oParent;this.width=this._oWidth;this.height=this._oHeight;this.oParentNode.appendChild(this.getElement());this.render();this._fulled=false}},render:function(){var j=this.parent||this.getElement().parentNode;var h=EG.getSize(j);c.fit({element:this.element,dSize:{width:h.innerWidth,height:h.innerHeight},pSize:h});EG.css(this.element,"top:0px;left:0px");c.fit({element:this.dPop,dSize:{width:this.width,height:this.height}});var g=this.isAutoHeight();if(!g){c.fit({element:this.dBody,dSize:{height:(EG.getSize(this.dPop).innerHeight-EG.getSize(this.dHead).outerHeight-EG.getSize(this.dFoot).outerHeight)}})}var k=this.isAutoWidth();if(!k){c.fit({element:this.dBody,dSize:{width:"100%"}})}if(this.items.length>0){this.doLayout()}h=EG.getSize(this.dPop);c.fit({element:this.dHead,dSize:{width:h.innerWidth}});c.fit({element:this.dFoot,dSize:{width:h.innerWidth}});EG.css(this.dBody,"position:relative;");if(this.animate){EG.Anim.beforePlay(this.dPop)}if(this.lock){this.fullLock()}if(this.posFix){this.doPosFix()}if(this.animate){EG.Anim.play(this.dPop)}},statics:{_events:{dCloser:{onmousedown:function(g){EG.Event.stopPropagation(g)},onmouseup:function(h){var g=this.me;g.close();EG.Event.stopPropagation(h)}},dFuller:{onmousedown:function(g){EG.Event.stopPropagation(g)},onmouseup:function(h){var g=this.me;g.full();EG.Event.stopPropagation(h)}}}}}})})();(function(){EG.define("EG.ui.Tip",["EG.ui.Item"],function(a,b){EG.onload(function(){var e;var c=function(){if(!e){e=new b()}};EG.Tip={open:function(){c();e.open.apply(e,arguments)},error:function(){c();e.error.apply(e,arguments)},close:function(){c();e.close.apply(e,arguments)},info:function(){c();e.info.apply(e,arguments)},message:function(){c();e.message.apply(e,arguments)}}});return{extend:a,config:{lock:false,cls:"eg_tip"},constructor:function(c){this.callSuper([c])},build:function(){var c=this;this.element=EG.CE({pn:EG.getBody(),tn:"div",style:"border:1px solid red;border-radius:2px;background:#FFFFC0;cursor:pointer;position:absolute;z-index:2",cn:[this.dIcon=EG.CE({tn:"div",style:EG.Style.c.dv}),this.dMessage=EG.CE({tn:"div",style:EG.Style.c.dv+";padding:5px"})],onclick:function(f){if(c.currentClick!=null){c.currentClick.apply(this,[f]);c.currentClick=null;EG.Event.stopPropagation(f)}EG.hide(this)}});EG.hide(this.element)},close:function(){EG.hide(this.element)},open:function(){EG.show(this.element)},error:function(e,c){this.message({message:e,target:c,type:"error"})},info:function(e,c){this.message({message:e,target:c,type:"info"})},message:function(h){h=h||{};var p=h.message,j=h.target,k=h.target,l=h.pos,c=h.style,e=h.animate,n=h.click;var m=h.autoclose||false;var g=h.closetime;if(g){m=true}this.dMessage.innerHTML=p;EG.show(this.element);EG.css(this.dMessage,"width:100px");if(c){EG.css(this.element,c)}else{if(!l&&j){l=EG.Tools.getElementPos(j);var f=EG.getSize(j);var o=EG.getSize(this.dMessage);if((l.x+f.clientWidth+o.clientWidth)>screen.width){l.x=l.x+f.clientWidth-o.clientWidth;l.y=l.y+f.clientHeight}else{l.x=l.x+f.clientWidth}}EG.css(this.element,"left:"+l.x+"px;top:"+l.y+"px")}if(e){EG.Anim.remove(this.element);EG.Anim.add(this.element,e.split(" "))}EG.show(this.element)}}})})();(function(){EG.define("EG.ui.Locker",["EG.ui.Dialog","EG.ui.Button"],function(b,a,c){EG.onload(function(){EG.Locker=new c({width:Math.min(500,document.documentElement.clientWidth),height:Math.min(120,document.documentElement.clientHeight),renderTo:EG.getBody()})});return{extend:b,config:{cls:"eg_locker",title:"提示",zIndex:2},constructor:function(e){this.callSuper([e]);EG.css(this.dBody,"text-align:center;overflow:auto")},build:function(){this.callSuper("build");var e=this.dBody;this.dType=EG.CE({pn:e,tn:"div",cls:this.cls+"-type"});this.dWait=EG.CE({pn:e,tn:"div",cls:this.cls+"-wait",innerHTML:"正在加载..."})},lock:function(e){if(e){this.open()}else{if(this.t!=null){clearTimeout(this.t);this.t=null}this.close()}},wait:function(e){this.message({message:e,closeable:false})},confirm:function(g){if(typeof(g)=="string"){g={message:g};if(arguments.length>1){g.yes_callback=arguments[1]}if(arguments.length>2){g.no_callback=arguments[2]}}var k=g.message,m=g.yes_title||"确定",f=g.no_title||"取消",j=g.yes_callback,h=g.no_callback||function(){EG.Locker.lock(false)};var e,l;g.message=EG.CE({tn:"div",cn:[{tn:"div",innerHTML:k},{tn:"div",style:"margin-top:0.5rem;",cn:[e=new a({text:m,click:j,cls:"eg_button_small"}),l=new a({text:f,click:h,cls:"eg_button_small",style:"margin-left:2rem;"})]}],onkeydown:function(n){n=EG.Event.getEvent(n);if(n.keyCode==32||n.keyCode==13){e.doClick()}else{if(n.keyCode==27){l.doClick()}}}});g.reqFocus=true;g.reqFocusElement=g.message;this.message(g)},message:function(g){g=g||{};var k=g.type;if(k){EG.setCls(this.dType,["type","type-"+k],this.cls);EG.show(this.dType)}else{EG.hide(this.dType)}EG.setCls(this.dWait,["wait","fontM"],this.cls);var o=typeof(g)=="string"?g:EG.unnull(g.message,"请稍等...");if(typeof(o)=="string"){this.dWait.innerHTML=o}else{EG.DOM.removeChilds(this.dWait);this.dWait.appendChild(o)}var e=g.closeable!=null?g.closeable:true;var l=g.autoclose||false;var f=g.closetime;if(f){l=true}var m=g.callback;if(!e||l){this.setCloseable(false)}else{this.setCloseable(true)}this.open();var j=g.reqFocus;if(j){var n=g.reqFocusElement;EG.CE({ele:n,tabIndex:1});n.focus()}if(l){if(f==null){f=1200}var h=this;if(this.t!=null){clearTimeout(this.t);this.t=null}this.t=setTimeout(function(){h.t=null;h.close();if(m){m()}},f)}}}})})();(function(){EG.ui.Option=function(b){this.btns=b.btns;this.pop=new EG.ui.Pop({closeable:false});this.element=EG.CE({tn:"div",style:"padding:10px"});this.pop.getElement().appendChild(this.element);if(this.btns){this.setBtns(this.btns)}document.body.appendChild(this.element)};EG.ui.Option.prototype={};var a=EG.ui.Option;EG.ui.Option.prototype.setTextactions=function(b){for(var e=0,c=b.length;e<c;e++){}};EG.ui.Option.prototype.open=function(){this.pop.open()};EG.ui.Option.prototype.close=function(){this.pop.close()};EG.ui.Option.prototype.setBtns=function(e){for(var c=0,b=e.length;c<b;c++){this.element.appendChild(e[c].getElement())}}})();(function(){EG.define("EG.ui.Layout",[],function(a){return{statics:{layoutManagers:{},create:function(c,b){if(EG.String.isString(c)){c={type:c}}var e=EG.ui.layout[EG.Word.first2Uppercase(c.type)+"Layout"];if(!e){throw new Error("EG.ui.Layout#create:该布局器无法识别:"+c.type)}return new e(c,b)},regist:function(c,b){EG.ui.Layout.layoutManagers[c]=b},sizeDesc:function(b){var c={};c.ow=b.getOWidth();c.oh=b.getOHeight();c.isWA=b.isContainer&&b.isAutoWidth();c.isHA=b.isContainer&&b.isAutoHeight();c.isWP=(typeof(c.ow)=="string"&&c.ow.lastIndexOf("%")>=0);c.isHP=(typeof(c.oh)=="string"&&c.oh.lastIndexOf("%")>=0);c.isWE=c.ow==null;c.isHE=c.oh==null;c.isWN=(typeof(c.ow)=="number");c.isHN=(typeof(c.ow)=="number");return c},renderItems:function(f){for(var c=0,b=f.items.length;c<b;c++){var e=f.items[c];e.render()}}},addOnLayout:false,doLayout:function(){},hideItems:function(){var b=[];for(var c=0;c<this.items.length;c++){if(!this.items[c].hidden){b.push(this.items[c])}}this.items=b},renderItemsOuter:function(f){for(var c=0,b=this.items.length;c<b;c++){var e=this.items[c];if(f||(e.isContainer&&(e.isAutoHeight()||e.isAutoWidth()))){e.renderOuter()}}}}})})();(function(){EG.define("EG.ui.layout.DefaultLayout",["EG.ui.Layout","EG.ui.Item"],function(b,a,c){return{extend:b,config:{align:null,verticalAlign:null,horizontal:true},constructor:function(f,e){this.container=e;this.initConfig(f);this.force=this.align||this.verticalAlign},doLayout:function(){this.init();if(this.isCtWA||this.isCtHA){this.renderItemsOuter(true);this.autoSize()}if(!this.container.isRenderingOuter){b.renderItems(this);if(this.align=="center"){EG.Style.centerChilds(this.container.getItemContainer(),this.horizontal)}if(this.verticalAlign=="middle"){EG.Style.middleChilds(this.container.getItemContainer(),this.horizontal)}else{if(this.verticalAlign=="top"){EG.Style.topChilds(this.container.getItemContainer(),this.horizontal)}else{if(this.verticalAlign=="bottom"){EG.Style.bottomChilds(this.container.getItemContainer(),this.horizontal)}}}for(var g=0,e=this.items.length;g<e;g++){var j=this.items[g];if(j.region){var f=j.region.split[";"];for(var h in f){if(h=="center"){EG.Style.center(j.getElement())}else{if(h=="middle"){EG.Style.middle(j.getElement())}else{EG.css(j.getElement(),"position:absolute;"+h+"0px;")}}}}}}},init:function(){this.items=this.container.items;this.hideItems();this.isCtWA=this.container.isAutoWidth();this.isCtHA=this.container.isAutoHeight();this.hasItemWA=false;this.hasItemHA=false;for(var f=0,e=this.items.length;f<e;f++){var g=this.items[f];var h=b.sizeDesc(g);this.hasItemWA=this.hasItemWA||h.isWA;this.hasItemHA=this.hasItemHA||h.isHA;g.width=g.oWidth;g.height=g.oHeight;EG.css(g.getElement(),"width:auto;height:auto")}},autoSize:function(){var n=-1,g=-1,k=-1,f=-1;for(var o=0,m=this.items.length;o<m;o++){var u=this.items[o];try{var j=EG.Tools.getElementPos(u.getElement());var v=u.getSize();n=n==-1?(j.x-v.marginLeft):Math.min(j.x-v.marginLeft,n);g=g==-1?(j.x-v.marginLeft+v.outerWidth):Math.max(j.x-v.marginLeft+v.outerWidth,g);k=k==-1?(j.y-v.marginTop):Math.min(j.y-v.marginTop,k);f=f==-1?(j.y-v.marginTop+v.outerHeight):Math.max(j.y-v.marginTop+v.outerHeight,f)}catch(r){alert(a.getPath(u)+":"+u.getElement());throw r}}var t=Math.max(0,g-n);var q=Math.max(0,f-k);if(this.isCtWA){this.container.setInnerWidth(t)}if(this.isCtHA){this.container.setInnerHeight(q)}}}})})();(function(){EG.define("EG.ui.layout.BorderLayout",["EG.ui.Layout"],function(a,c){var b=EG.Style.size2Num;return{extend:a,constructor:function(f,e){this.container=e},getItem:function(f){for(var g=0,e=this.container.items.length;g<e;g++){if(this.container.items[g].region==f.region){return this.container.items[g]}}return null},doLayout:function(){this.init();if(this.isCtWA||this.isCtHA||this.hasItemHA||this.hasItemWA){if(!this.isCtWA){this.setItemWidth(this.ctW)}if(!this.isCtHA){this.setItemHeight(this.ctH)}this.renderItemsOuter();if(this.isCtWA){this.setCtWidth()}if(this.isCtHA){this.setCtHeight()}}if(!this.container.isRenderingOuter){this.setItemWidth(this.ctW,true);this.setItemHeight(this.ctH,true);a.renderItems(this)}},setCtWidth:function(){this.ctW=0;if(this.refMW){for(var e=0;e<this.mItems.length;e++){this.ctW+=this.getItemWidth(this.mItems[e])}}if(this.itemT&&this.itemT.getOWidth()!=null){this.ctW=Math.max(this.ctW,this.getItemWidth(this.itemT))}if(this.itemB&&this.itemB.getOWidth()!=null){this.ctW=Math.max(this.ctW,this.getItemWidth(this.itemB))}this.container.setInnerWidth(this.ctW)},setCtHeight:function(){this.ctH=0;if(this.refMH){for(var e=0;e<this.mItems.length;e++){var f=this.getItemHeight(this.mItems[e]);if(f==null){continue}this.ctH=Math.max(f,this.ctH)}}if(this.itemT&&this.itemT.getOHeight()!=null){this.ctH+=this.getItemHeight(this.itemT)}if(this.itemB&&this.itemB.getOHeight()!=null){this.ctH+=this.getItemHeight(this.itemT)}this.container.setInnerHeight(this.ctH)},getItemWidth:function(e){return e.isContainer&&e.isAutoWidth()?e._outerSize.outerWidth:e.getOWidth()},getItemHeight:function(e){return e.isContainer&&e.isAutoHeight()?e._outerSize.outerHeight:e.getOHeight()},setItemWidth:function(f,j){if(this.mItems.length>0){var h=this.setItemWidth_FN(this.itemL,f,j);var g=this.setItemWidth_FN(this.itemC,f,j);var e=this.setItemWidth_FN(this.itemR,f,j);if(h==-2||g==-2||e==-2){}else{if(g<0){if(h<0&&e<0){g=f/3;h=f/3;e=f/3}else{if(h>=0&&e>=0){g=f-h-e}else{if(h<0&&e<0){var l=f-e;g=0.5*l;h=0.5*l}else{if(e<0){var l=f-h;g=0.5*l;e=0.5*l}}}}}else{if(h<0&&e<0){var l=f-g;h=0.5*l;e=0.5*l}else{if(h<0){h=f-g-e}else{if(e<0){e=f-g-h}}}}}h=parseInt(h);g=parseInt(g);e=parseInt(e);if(this.itemL&&h>=0){this.itemL.width=h}if(this.itemC&&g>=0){this.itemC.width=g;var k=h>=0?h:0;EG.css(this.itemC,"left:"+k+"px")}if(this.itemR&&e>=0){this.itemR.width=e}}this.setItemWidth_FN2(this.itemT,f,j);this.setItemWidth_FN2(this.itemB,f,j)},setItemWidth_FN:function(h,f,j){if(!h){return 0}var g=h.getOWidth();var e=-1;if(g==null){e=-1}else{if(g=="auto"){e=j?h._outerSize.outerWidth:-2}else{if(typeof(g)=="string"&&g.lastIndexOf("%")>=0){if(f>0){e=b(g,f)}}else{if(typeof(g)=="number"){e=g}}}}return e},setItemWidth_FN2:function(f,e,g){if(!f||e<0||f.getOWidth()=="auto"){return}f.width=e},setItemHeight:function(e,f){var l=-1;if(this.mItems.length>0){for(var k=0;k<this.mItems.length;k++){var o=this.mItems[k];var m=o.getOHeight();if(m==null){continue}if(o.isContainer&&o.isAutoHeight()){if(f){m=o._outerSize.outerHeight}else{l=-2;break}}else{m=b(m,this.ctH)}l=Math.max(l,m)}}else{l=0}var n=this.setItemHeight_FN(this.itemB,e,f);var g=this.setItemHeight_FN(this.itemT,e,f);if(l==-2||n==-2||g==-2){}else{if(l==-1){if(g<0&&n<0){l=e/3;g=e/3;n=e/3}else{if(g>=0&&n>=0){l=e-n-g}else{if(g<0){var j=e-n;l=j*0.5;g=j*0.5}else{if(n<0){var j=e-g;l=j*0.5;n=j*0.5}}}}}else{if(g<0&&n<0){var j=e-l;g=j*0.5;n=j*0.5}else{if(g<0){g=e-l-n}else{if(n<0){n=e-l-g}}}}}l=parseInt(l);n=parseInt(n);g=parseInt(g);if(this.itemT&&g>=0){this.itemT.height=g}if(this.itemB&&n>=0){this.itemB.height=n}if(this.mItems.length>0&&l>=0){for(var k=0;k<this.mItems.length;k++){var o=this.mItems[k];o.height=l;EG.css(o,"top:"+g+"px")}}},setItemHeight_FN:function(j,f,k){if(!j){return 0}var g=j.getOHeight();var e=-1;if(g==null){e=-1}else{if(g=="auto"){e=k?j._outerSize.outerHeight:-2}else{if(typeof(g)=="string"&&g.lastIndexOf("%")>=0){if(f>0){e=b(g,f)}}else{if(typeof(g)=="number"){e=g}}}}return e},init:function(){this.items=this.container.items;this.hideItems();this.ctEle=this.container.getItemContainer();EG.css(this.ctEle,"position:absolute");this.itemT=this.itemB=this.itemC=this.itemL=this.itemR=null;this.isCtWA=this.container.isAutoWidth();this.isCtHA=this.container.isAutoHeight();this.hasItemWA=false;this.hasItemHA=false;this.hasMidWA=false;this.hasMidHA=false;this.hasMidWE=false;this.hasMidHE=false;this.hasMidWP=false;this.hasMidHP=false;this.hasMidWN=false;this.hasMidHN=false;this.ctW=this.container.getInnerWidth();this.ctH=this.container.getInnerHeight();this.mItems=[];for(var f=0,e=this.items.length;f<e;f++){var h=this.items[f];if(!h.region){var g="";while(h.pItem!=null){g+=">"+h.pItem.getClass()._className+"["+EG.Array.getIdx(h.pItem.items,h)+"]["+h.cls+"]["+h.pItem.layout+"]";h=h.pItem}alert(g);throw new Error("BorderLayout#doLayout:无region信息")}var j=a.sizeDesc(h);var k=false;this.hasItemWA=this.hasItemWA||j.isWA;this.hasItemHA=this.hasItemHA||j.isHA;if(h.region=="top"){this.itemT=h}else{if(h.region=="bottom"){this.itemB=h}else{if(h.region=="center"){this.itemC=h;k=true}else{if(h.region=="left"){this.itemL=h;k=true}else{if(h.region=="right"){this.itemR=h;k=true}else{throw new Error("BorderLayout#doLayout:暂不支持"+h.region)}}}}}if(k){this.mItems.push(h);this.hasMidWA=this.hasMidWA||j.isWA;this.hasMidHA=this.hasMidHA||j.isHA;this.hasMidWE=this.hasMidWE||j.isWE;this.hasMidHE=this.hasMidHE||j.isHE;this.hasMidWP=this.hasMidWP||j.isWP;this.hasMidWP=this.hasMidWP||j.isHP;this.hasMidWN=this.hasMidWN||j.isWN;this.hasMidHN=this.hasMidHN||j.isHN}if(h.region=="center"){EG.css(h,"position:absolute;");if(h.setCollapseAble){h.setCollapseAble(false)}}else{EG.css(h,"position:absolute;"+h.region+":0px");if(h.collapseAble&&h.setCollapseBehavior){h.setCollapseBehavior(h.region)}}h.width=h.oWidth;h.height=h.oHeight;EG.css(h.getElement(),"width:auto;height:auto")}this.refMW=false;this.refMH=false;if(this.mItems.length>0){this.refMW=this.isCtWA&&(!this.hasMidWE&&!this.hasMidWP);this.refMH=this.isCtHA&&(this.hasMidHN||this.hasMidHA)}}}})})();(function(){EG.define("EG.ui.layout.TableLayout",["EG.ui.Layout"],function(a,b){return{extend:a,config:{maxRow:0,maxCol:0,cellSpacing:0,cellPadding:0,border:0,style:null},constructor:function(e,c){this.container=c;this.initConfig(e);this.addOnLayout=true},getItem:function(c){return EG.Array.get(c)},fixPos:function(){for(var e=0,c=this.items.length;e<c;e++){var f=this.items[e].pos||[e,0];if(!EG.isArray(f)){throw new Error("EG.ui.layout#fixPos:item的pos类型需为数组")}if(f.length==0){f=[e,0]}else{if(f.length==1){f=[f[0],0]}else{if(f.length==2){}else{throw new Error("EG.ui.layout#fixPos:item的pos长度最大为2")}}}this.maxRow=Math.max(f[0]+1,this.maxRow);this.maxCol=Math.max(f[1]+1,this.maxCol);this.items[e].pos=f}},resetTable:function(){EG.DOM.removeAllRows(this.tbody);for(var e=0;e<this.maxRow;e++){var f=EG.CE({pn:this.tbody,tn:"tr"});for(var g=0;g<this.maxCol;g++){EG.CE({pn:f,tn:"td"})}}},putItems:function(){for(var e=0,c=this.items.length;e<c;e++){var g=this.items[e];var j=g.pos;var f=g.getElement();var h=this.tbody.childNodes[j[0]].childNodes[j[1]];if(g.vAlign){EG.css(h,"vertical-align:"+g.vAlign)}if(EG.Style.current(f).position=="absolute"){EG.css(f,"position:relative")}h.appendChild(f)}},autoSpan:function(){var q=this.tbody.childNodes;for(var m=0,o=q.length;m<o;m++){var n=q[m].childNodes;var c=0;var f=n.length;for(var l=f-1;l>=0;l--){if(n[l].innerHTML==""){c++;q[m].removeChild(n[l])}else{break}}if(n.length==0){continue}if(c>0){var h=n[n.length-1];h.colSpan=c+1}var g=parseInt(100/f);for(var l=0,e=n.length;l<e;l++){if(l==e-1){n[l].width=parseInt(((c>0)?(g*(c+1)):g))+"%"}else{n[l].width=g+"%"}}}},doLayout:function(){this.items=this.container.items;this.hideItems();var e=this.container.getItemContainer();if(!this.table){this.table=EG.CE({tn:"table",style:"width:100%;table-layout:fixed;"+(this.style?this.style:""),cellPadding:this.cellPadding,cellSpacing:this.cellSpacing,border:this.border});this.tbody=EG.CE({pn:this.table,tn:"tbody",style:"width:100%;"})}e.appendChild(this.table);this.fixPos();this.resetTable();this.putItems();this.autoSpan();for(var f=0,c=this.items.length;f<c;f++){var g=this.items[f];g.render()}this.autoSize()},autoSize:function(){var f=this.container.isAutoWidth();var c=this.container.isAutoHeight();if(f||c){var e=EG.getSize(this.table);if(f){this.container.setInnerWidth(e.outerWidth)}if(c){this.container.setInnerHeight(e.outerHeight)}}}}})})();(function(){EG.define("EG.ui.layout.LineLayout",["EG.ui.Layout"],function(b,c){return{extend:b,config:{direct:"H",align:null},constructor:function(f,e){this.container=e;this.initConfig(f);if(!this.align){this.align=this.direct=="H"?"left":"top"}},getItem:function(e){return EG.Array.get(e)},doLayout:function(){this.init();if(this.isCtWA||this.isCtHA||this.hasItemHA||this.hasItemWA){this.setItemSize();this.renderItemsOuter();if(this.isCtWA||this.isCtHA){this.autoSize()}}if(!this.container.isRenderingOuter){this.setItemSize(true);var g=0;for(var f=0,e=this.items.length;f<e;f++){var h=this.items[f];h.render();if(this.direct=="H"){EG.css(h,"position:absolute;"+this.align+":"+g+"px");g+=h.getSize().outerWidth}else{EG.css(h,"position:absolute;"+this.align+":"+g+"px");g+=h.getSize().outerHeight}}}},setItemSize:function(f){var j=0,e=0,p=0,n=0;var g={};if(!f){if(this.direct=="H"){if(this.isCtWA&&this.hasItemWA){return}}else{if(this.isCtHA&&this.hasItemHA){return}}}for(var k=0,h=this.items.length;k<h;k++){var o=this.items[k];var m=b.sizeDesc(o);g[k]=m;if(this.direct=="H"){if(!this.isCtWA&&m.isWP){o.width=a(o.getOWidth(),this.ctW)}}else{if(!this.isCtHA&&m.isHP){o.height=a(o.getOHeight(),this.ctH)}}if(this.direct=="H"){if(this.isCtHA){if(m.isHA){if(f){j=Math.max(j,o._outerSize.outerHeight)}else{return}}else{if(m.isHN){j=Math.max(j,o.height)}}}}else{if(this.isCtWA){if(m.isWA){if(f){j=Math.max(j,o._outerSize.outerWidth)}else{return}}else{if(m.isWN){j=Math.max(j,o.width)}}}}if(this.direct=="H"){if(!m.isWE){if(m.isWA){o.render();n+=o.getSize().outerWidth}else{n+=o.width}}else{e++}}else{if(!m.isHE){if(m.isHA){o.render();n+=o.getSize().outerHeight}else{n+=o.height}}else{e++}}}if(this.direct=="H"){if(!this.isCtHA){j=this.ctH}if(e){p=parseInt((this.ctW-n)/e)}}else{if(!this.isCtWA){j=this.ctW}if(e){p=parseInt((this.ctH-n)/e)}}for(var k=0,h=this.items.length;k<h;k++){var o=this.items[k];var m=g[k];if(this.direct=="H"){o.height=j;if(m.isWE){o.width=p}}else{o.width=j;if(m.isHE){o.height=p}}}},autoSize:function(){var j=0,f=0;for(var g=0,e=this.items.length;g<e;g++){var k=this.items[g];if(this.direct=="V"){if(this.isCtHA){j+=this.getItemHeight(k)}if(this.isCtWA){f=Math.max(f,this.getItemWidth(k))}}else{if(this.isCtHA){j=Math.max(j,this.getItemHeight(k))}if(this.isCtWA){f+=this.getItemWidth(k)}}}if(this.isCtWA){this.container.setInnerWidth(f);this.ctW=f}if(this.isCtHA){this.container.setInnerHeight(j);this.ctH=j}},getItemWidth:function(e){return e.isContainer&&e.isAutoWidth()?e._outerSize.outerWidth:e.getOWidth()},getItemHeight:function(e){return e.isContainer&&e.isAutoHeight()?e._outerSize.outerHeight:e.getOHeight()},init:function(){this.items=this.container.items;this.hideItems();var g=EG.Style.current(this.container.getItemContainer()).position;if(g!="absolute"&&g!="relative"){EG.css(this.container.getItemContainer(),"position:relative")}this.isCtWA=this.container.isAutoWidth();this.isCtHA=this.container.isAutoHeight();this.hasItemWA=false;this.hasItemHA=false;this.hasItemWN=false;this.hasItemHN=false;this.hasItemWE=false;this.hasItemHE=false;this.hasItemWP=false;this.hasItemHP=false;this.ctW=this.isCtWA?-1:this.container.getInnerWidth();this.ctH=this.isCtHA?-1:this.container.getInnerHeight();for(var f=0,e=this.items.length;f<e;f++){var h=this.items[f];var j=b.sizeDesc(h);this.hasItemWA=this.hasItemWA||j.isWA;this.hasItemHA=this.hasItemHA||j.isHA;this.hasItemWN=this.hasItemWN||j.isWN;this.hasItemHN=this.hasItemHN||j.isHN;this.hasItemWE=this.hasItemWE||j.isWE;this.hasItemHE=this.hasItemHE||j.isHE;this.hasItemWP=this.hasItemWP||j.isWP;this.hasItemHP=this.hasItemHP||j.isHP;if(this.direct=="H"){if(this.isCtWA&&(j.isWE||j.isWP)){throw new Error("EG.ui.layout.LineLayout:父组件宽度自动时，子组件宽度不能同为空||百分比")}}else{if(this.isCtHA&&(j.isHE||j.isHP)){throw new Error("EG.ui.layout.LineLayout:父组件高度自动时，子组件高度不能同为空||百分比")}}h.width=h.oWidth;h.height=h.oHeight;EG.css(h.getElement(),"width:auto;height:auto")}if(this.direct=="H"){if(this.isCtHA&&(!this.hasItemHN&&!this.hasItemHA)){throw new Error("EG.ui.layout.LineLayout:横向时,父组件高度为自动时,子组件高度不能既没有固定也没有自动")}}else{if(this.isCtWA&&(!this.hasItemWN&&!this.hasItemWA)){throw new Error("EG.ui.layout.LineLayout:纵向时,父组件宽度为自动时,子组件宽度不能既没有固定也没有自动")}}}}});var a=EG.Style.size2Num})();(function(){EG.define("EG.ui.Panel",["EG.ui.Container"],function(a,b){return{alias:"panel",extend:a,constructor:function(c){this.callSuper([c])}}})})();(function(){EG.define("EG.ui.XPanel",["EG.ui.Panel","EG.ui.Item"],function(c,a,b){return{alias:"xPanel",extend:c,config:{cls:"eg_xpanel",showTitle:true,showExpand:true,showBorder:true,title:null,collapseBehavior:"top",collapseAble:false,collapsed:false,barConfig:null,bodyStyle:null,headStyle:null},constructor:function(e){this.callSuper([e]);this.setCollapseAble(this.collapseAble)},build:function(){var e=this;this.element=EG.CE({tn:"div",cn:[this.dHead=EG.CE({tn:"div",cls:this.cls+"-dHead",cn:[this.dCollapse=EG.CE({tn:"div",cls:this.cls+"-dCollapse "+this.cls+"-dCollapse-"+this.collapseBehavior,onclick:function(){var f=EG.Style.isHide(e.dBody);e.collapse(!f)}}),this.dTitle=EG.CE({tn:"div",cls:this.cls+"-dTitle"})]}),this.dBody=EG.CE({tn:"div",cls:this.cls+"-dBody"})]});if(this.title){this.setTitle(this.title)}if(this.barConfig){this.setBar(this.barConfig)}if(this.bodyStyle){EG.css(this.dBody,this.bodyStyle)}if(this.headStyle){EG.css(this.dHead,this.headStyle)}},setBar:function(e){if(!this.dBar){this.dBar=new c({cls:this.cls+"-dBar"});this.dHead.appendChild(this.dBar.getElement())}this.dBar.clear();this.dBar.addItem(e)},render:function(){if(this.element.parentNode==EG.getBody()){EG.css(EG.getBody(),"margin:0px;padding:0px;width:100%;height:100%");EG.css(this.element,"position:absolute;left:0px;top:0px;")}a.fit(this);var f={width:"100%"};if(this.collapsed){f.height="100%";EG.hide(this.dBody)}else{EG.show(this.dBody)}EG.hide(this.dHead);a.fit({element:this.dHead,dSize:f});EG.show(this.dHead);if(!this.collapsed){if(this.dBar){this.dBar.width=Math.floor(EG.getSize(this.dHead).innerWidth-EG.getSize(this.dTitle).outerWidth-EG.getSize(this.dCollapse).outerWidth)-1;this.dBar.height=parseInt(EG.getSize(this.dHead).innerHeight);this.dBar.render()}}if(!this.collapsed){var e=EG.getSize(this.dHead);a.fit({element:this.dBody,dSize:{width:"100%",height:(EG.getSize(this.element).innerHeight-e.outerHeight)}});if(this.items.length>0){this.doLayout()}}else{}},getOHeight:function(){return this.oHeight},getOWidth:function(){if(this.collapsed){var e=EG.getSize(this.element);return e.outerWidth-e.innerWidth+EG.getSize(this.dCollapse).outerWidth}else{return this.oWidth}},getItemContainer:function(){return this.dBody},setCollapseAble:function(e){this.collapseAble=e;if(!this.collapseAble){EG.hide(this.dCollapse)}},setCollapseBehavior:function(e){this.collapseBehavior=e;this.refreshCollapseCln(this.collapsed)},refreshCollapseCln:function(g){var e=this.collapseBehavior;var f=false;if(g){switch(this.collapseBehavior){case"top":e="bottom";break;case"right":e="left";f=true;break;case"bottom":e="top";break;case"left":e="right";f=true;break}}EG.setCls(this.dCollapse,["dCollapse","dCollapse-"+e],this.cls);if(f){EG.css(this.dTitle,"writing-mode:lr-tb");if(this.dBar){EG.hide(this.dBar)}}else{EG.css(this.dTitle,"writing-mode:;");if(this.dBar){EG.show(this.dBar)}}},collapse:function(f){this.refreshCollapseCln(f);if(f){var e=EG.getSize(this.dBody);EG.hide(this.dBody);this._oWidth=this.oWidth;this._oHeight=this.oHeight;if(EG.$in(this.collapseBehavior,["top","bottom"])){this.oHeight=this.height-e.outerHeight}else{if(EG.$in(this.collapseBehavior,["left","right"])){this.oWidth=20;EG.css(this.dHead,"overflow:hidden;writing-mode:lr-tb;")}}}else{this.oWidth=this._oWidth;this.oHeight=this._oHeight;EG.css(this.dHead,"height:auto;")}this.collapsed=f;if(this.pItem){this.pItem.render()}},setTitle:function(e){this.title=e;this.dTitle.innerHTML=this.title},setInnerHeight:function(f){a.pack({pEle:this.dBody,height:f});var e=a.pack({pEle:this.element,height:EG.getSize(this.dBody).outerHeight+EG.getSize(this.dHead).outerHeight});this.height=e.height},getInnerHeight:function(){return EG.getSize(this.element).innerHeight-EG.getSize(this.dHead).outerHeight}}})})();(function(){EG.define("EG.ui.TabPanel",["EG.ui.Container","EG.ui.Item","EG.ui.Tab","EG.ui.Panel",],function(b,c,a,f,e){return{alias:"tabPanel",extend:b,config:{runOnSelectOnBuild:true,cls:"eg_tabPanel",direct:"top",isShowTabs:true,tabsStyle:null,tabWidthLR:90},constructor:function(g){this.curIdx=-1;this.panels=[];this.tabs=[];this.callSuper([g])},afterBuild:function(){if(this.itemsConfig&&this.itemsConfig.length>0){this.addItem(this.itemsConfig,true);this.select(0)}},build:function(){this.element=EG.CE({tn:"div",cn:[this.dPanels=EG.CE({tn:"div",cls:this.cls+"-panels"+this.direct}),this.dTabs=EG.CE({tn:"div",cls:this.cls+"-tabs"+this.direct})]});if(!this.isShowTabs){this.hideTabs(false)}},showTabs:function(g){if(g==null){g=true}EG.show(this.dTabs);if(g){this.render()}},hideTabs:function(g){if(g==null){g=true}EG.hide(this.dTabs);if(g){this.render()}},render:function(){c.fixBody(this.element);c.fit(this);var h=EG.getSize(this.element);var g={outerHeight:0,outerWidth:0};var j=EG.getSize(this.dPanels);if(this.direct=="top"||this.direct=="bottom"){if(this.showTabs){c.fit({element:this.dTabs,dSize:{width:"100%"},pSize:{width:h.innerWidth},type:"width"});EG.css(this.dTabs,"position:absolute;"+this.direct+":0px;left:0px");g=EG.getSize(this.dTabs)}var k=g.outerHeight-(this.direct=="top"?j.borderTop:j.borderBottom);c.fit({element:this.dPanels,pSize:{width:h.innerWidth,height:h.innerHeight-g.outerHeight}});EG.css(this.dPanels,"position:absolute;"+this.direct+":"+k+"px;left:0px")}else{if(this.direct=="left"||this.direct=="right"){if(this.showTabs){c.fit({element:this.dTabs,dSize:{height:h.innerHeight},pSize:h,type:"height"});EG.css(this.dTabs,"position:absolute;"+this.direct+":0px;top:0px");g=EG.getSize(this.dTabs)}var k=g.outerWidth-(this.direct=="left"?j.borderLeft:j.borderRight);c.fit({element:this.dPanels,pSize:{width:h.innerWidth-k,height:h.innerHeight}});EG.css(this.dPanels,"position:absolute;"+this.direct+":"+k+"px;top:0px")}}this.doLayout()},addItem:function(o,h,k){if(h==null){h=true}if(typeof(k)=="undefined"){k=-1}var l=this.items.length==0;var n=(!EG.isArray(o))?[o]:o;var j,g,p;for(var m=0,q=n.length;m<q;m++){o=n[m];if(o.panel["width"]==null){o.panel["width"]="100%"}if(o.panel["height"]==null){o.panel["height"]="100%"}o.tab["clsPre"]=this.cls+"-tabs"+this.direct;o.panel["className"]=this.cls+"-panels-panel";j=new a(this,o.tab);g=new f(o.panel);this.tabs.push(j);this.panels.push(g);j.pItem=this;g.pItem=this;this.dTabs.appendChild(j.getElement());this.dPanels.appendChild(g.getElement());this.items.push(g);if(m==0){p=j}}if(l&&h&&EG.DOM.contains(this.element)){this.render()}if(k!==null){this.select(k)}return j},close:function(g){var j;if(g instanceof a){j=g;g=this.getTabIdx(j)}else{j=EG.Array.get(this.tabs,g)}var l=j.getPanel();var h=(g==this.curIdx);var k=this.tabs[this.curIdx];j.destroy();l.destroy();this.dTabs.removeChild(j.element);this.dPanels.removeChild(l.getElement());EG.Array.del(this.items,g);EG.Array.del(this.tabs,g);EG.Array.del(this.panels,g);this.curIdx=EG.Array.getIdx(this.tabs,k);if(!h){return}if(g>=this.panels.length){g=this.panels.length-1}if(g>=0){this.select(g)}},closeAll:function(){for(var g=this.tabs.length-1;g>=0;g--){this.close(g)}},doSelect:function(n,j){var g=-1;for(var m=0,h=this.panels.length;m<h;m++){var o=this.panels[m];var k=this.tabs[m];if(k!=n){EG.setCls(k.element,"tab",this.cls+"-tabs"+this.direct);EG.hide(o.getElement())}else{g=m}}EG.setCls(this.tabs[g].element,["tab","selected"],this.cls+"-tabs"+this.direct);EG.show(this.panels[g].getElement());if(!j){this.panels[g].render()}this.curIdx=g},select:function(g){var h=(g instanceof a)?g:EG.Array.get(this.tabs,g);h.select()},getTabs:function(){return this.tabs},getPanels:function(){return this.panels},getPanel:function(g){return EG.Array.get(this.panels,g)},getPanelIdx:function(g){return EG.Array.getIdx(this.panels,g)},getTab:function(g){return EG.Array.get(this.tabs,g)},getTabIdx:function(h){for(var g=0;g<this.tabs.length;g++){if(this.tabs[g]==h){return g}}},getSelectedIdx:function(){return this.curIdx},getSelectedTab:function(){return this.getTab(this.curIdx)},getLength:function(){return this.items.length},doLayout:function(){if(!this.element.parentNode){return}var g=this.getSelectedIdx();if(g<0){return}var h=this.panels[g];if(h==null){return}h.pSize=EG.getSize(this.dPanels);h.render()},destroy:function(){for(var g=0;g<this.tabs.length;g++){this.tabs[g].destroy()}for(var g=0;g<this.panels.length;g++){this.panels[g].destroy()}this.tabs=null;this.panels=null;this.items=null}}})})();(function(){EG.define("EG.ui.Tab",["EG.ui.Item"],function(a,b){return{alias:"tab",extend:a,config:{title:"选项卡",closeable:false,onclick:null,onselect:null,onclose:null,afterselect:null,clsPre:null,tabIndex:false,onfocus:null},constructor:function(e,c){this.pTabPanel=e;this.callSuper([c]);this.setCloseable(this.closeable)},build:function(){var c=this;this.element=EG.CE({tn:"div",cls:this.clsPre+"-tab "+this.clsPre+"-selected",item:this,cn:[this.dTitle=EG.CE({tn:"div",cls:this.pTabPanel.cls+"-tabs-tab-title",innerHTML:this.title}),this.dCloser=EG.CE({tn:"a",cls:this.pTabPanel.cls+"-tabs-tab-closer",item:this,onclick:b._events.dCloser.onclick,onmouseover:b._events.dCloser.onmouseover,onmouseout:b._events.dCloser.onmouseout})],onclick:b._events.element.onclick,onkeydown:function(f){f=EG.Event.getEvent(f);if(f.keyCode==13){c.doClick()}}});if(typeof(this.tabIndex)=="number"){this.element.tabIndex=this.tabIndex}},setCloseable:function(c){this.closeable=c;if(this.closeable){EG.show(this.dCloser)}else{EG.hide(this.dCloser)}},doClick:function(){if(this.onclick){this.onclick.apply(this)}this.select()},setTitle:function(c){this.dTitle.innerHTML=c},getIdx:function(){return this.pTabPanel.getTabIdx(this)},select:function(){if(this.onselect){this.onselect.apply(this)}this.pTabPanel.doSelect(this);if(this.afterselect){this.afterselect.apply(this)}},isSelected:function(){return this.pTabPanel.curIdx==this.getIdx()},close:function(){if(this.onclose){this.onclose.apply(this)}this.pTabPanel.close(this)},getPanel:function(){var c=this.getIdx();return this.pTabPanel.getPanel(c)},statics:{_events:{element:{onclick:function(f){var c=this.item;c.doClick();EG.Event.stopPropagation(f)}},dCloser:{onclick:function(f){var c=this.item;c.close();EG.Event.stopPropagation(f)},onmouseover:function(){var c=this.item;EG.setCls(this,["tabs-tab-closer","tabs-tab-closer_on"],c.pTabPanel.cls)},onmouseout:function(){var c=this.item;EG.setCls(this,["tabs-tab-closer"],c.pTabPanel.cls)}}}}}})})();(function(){EG.define("EG.ui.Tree",["EG.ui.Item","EG.ui.TreeNode"],function(a,c,b){return{alias:"tree",extend:a,config:{rootTitle:"根目录",multiple:false,usebox:false,dragable:false,cls:"eg_tree",ondrag:null,deSelectable:true,onclick:null,nodesConfig:null},constructor:function(e){b.load();this.callSuper([e]);if(this.multiple){this.usebox=true}this.rootNode=new c({title:this.rootTitle,root:true,onclick:this.onclick,onclickSrc:this.onclickSrc},this);this.rootNode.setClassNamePre(this.cls+"-");if(!this.dragable){EG.hide(this.rootNode.dInsert)}EG.CE({pn:this.element,ele:this.rootNode.getElement(),style:"margin-left:0px"});if(!this.box){EG.hide(this.rootNode.box.getElement())}this.reset();if(this.nodesConfig){this.rootNode.setNodes(this.nodesConfig)}},setNodes:function(e,g){if(g==null){g=this.rootNode}for(var f=0;f<e.length;f++){g.add(new c(e[f]))}},build:function(){this.element=EG.CE({tn:"div",cls:this.cls,unselectable:"on",item:this,onmousemove:function(f){f=EG.Event.getEvent(f);if(b.curDrag!=null&&b.curDrag.style.display!="none"){EG.css(b.curDrag,"top:"+(f.clientY+10)+"px;left:"+(f.clientX+10)+"px;")}},onselectstart:function(){return false}})},reset:function(){this.treeNodes=[];if(this.rootNode.selected){this.rootNode.blur()}this.selectedNode=null;this.selectedNodes=[];EG.DOM.removeChilds(this.rootNode.dChildNodes)},render:function(){a.fit(this)},add:function(e){c.prototype.add.apply(this.rootNode,arguments)},appendChild:function(e){e.tree=this;this.childNodes.push(e)},getSelected:function(){return this.multiple?this.selectedNodes:this.selectedNode},setSelected:function(h){if(h==null){h=[]}else{if(!EG.Array.isArray(h)){h=[h]}}for(var f=0;f<this.treeNodes.length;f++){var k=this.treeNodes[f];var g=false;for(var e=0;e<h.length;e++){if(!g&&k.value===h[e]){g=true;break}}k.select(g)}},setValue:function(e){this.setSelected(e)},getElement:function(){return this.element},getRootNode:function(){return this.rootNode},removeAll:function(){this.rootNode.removeChilds()},registNode:function(e){this.treeNodes.push(e);e.tree=this;e.setClassNamePre(this.cls+"-")},ungistNode:function(e){EG.Array.remove(this.treeNodes,e)},expandAll:function(){for(var f=0,e=this.treeNodes.length;f<e;f++){this.treeNodes[f].expand()}},collapseAll:function(){for(var f=0,e=this.treeNodes.length;f<e;f++){this.treeNodes[f].collapse()}},expandLv:function(e){this.collapseAll();var g=function(j,k){j.expand();if(k>=e){return}k++;if(j.childNodes.length>0){for(var h=0,f=j.childNodes.length;h<f;h++){g(j.childNodes[h],(k))}}};g(this.getRootNode(),1)},deSelect:function(){if(this.multiple){if(!this.selectedNodes){return}for(var e=this.selectedNodes.length-1;e>=0;e--){this.selectedNodes[e].select(false)}}else{if(!this.selectedNode){return}this.selectedNode.select(false)}},expandChain:function(e){this.collapseAll();while((e=e.parentNode)!=null){e.expand()}},getValue:function(){var f=tree.getSelected();if(this.multiple){var g=[];for(var e=0;e<f.length;e++){g.push(f[e].value)}return g}else{return f.value}},statics:{dragThread:null,curXY:null,curDrag:null,isDraging:function(){return b.curDrag!=null&&!EG.Style.isHide(b.curDrag)},loaded:false,load:function(){if(b.loaded){return}EG.bindEvent(EG.doc,"mouseup",function(){if(b.dragThread!=null){window.clearTimeout(b.dragThread)}if(b.curDrag){EG.hide(b.curDrag)}});b.loaded=true}}}})})();(function(){EG.define("EG.ui.TreeNode",["EG.ui.Item","EG.ui.Box","EG.ui.Tree"],function(b,a,e,c){return{alias:"treeNode",extend:b,config:{title:"节点",value:null,showbox:false,root:false,onclick:null,ondrag:null,tree:null,cls:"eg_tree",titleStyle:null},constructor:function(g,f){this.callSuper([g]);this.tree=f||this.tree;this.parentNode=null;this.childNodes=[];this.ondrag=this.ondrag||(this.tree!=null?this.tree.ondrag:null);var h=this;this.box=new a({showText:false,style:"display:inline",onclick:function(){h.select(!h.selected)}});this.dNode.appendChild(this.box.getElement());EG.css(this.box.getElement().childNodes[0],"margin-left:3px")},statics:{_events:{dExpandBtn:{onclick:function(){var f=this.me;f.changeCollapsed()}},dTitle:{ondblclick:function(){var f=this.me;f.changeCollapsed()},onclick:function(){var f=this.me;f.select(f.tree.deSelectable?!f.box.selected:true);if(f.onclick){f.onclick.apply(f.onclickSrc||f)}},onmouseup:function(){var f=this.me;if(e.isDraging()){e.curDrag.node.moveto("in",f)}},onmousedown:function(h){var g=this.me;if(!g.tree.dragable||g==g.tree.rootNode){return}h=EG.Event.getEvent(h);e.curXY=[h.clientX,h.clientY];var f=g;e.dragThread=window.setTimeout(function(){if(e.curDrag==null){e.curDrag=EG.CE({pn:EG.getBody(),tn:"div",cls:"eg_drager"})}e.curDrag.node=f;e.curDrag.innerHTML=f.title;EG.show(e.curDrag);EG.css(e.curDrag,"top:"+(e.curXY[1]+10)+"px;left:"+(e.curXY[0]+10)+"px;")},300)}},dInsert:{onmouseover:function(){var f=this.me;if(f==f.tree.rootNode){return}if(e.isDraging()){EG.css(this,"background-color:red;margin-left:7px;")}},onmouseout:function(){EG.css(this,"background-color:;margin-left:;")},onmouseup:function(){var f=this.me;if(f==f.tree.rootNode){return}if(e.isDraging()){e.curDrag.node.moveto("after",f)}}}}},build:function(){this.element=EG.CE({tn:"div",cn:[this.dNode=EG.CE({tn:"div",cn:[this.dExpandBtn=EG.CE({tn:"div",me:this,onclick:c._events.dExpandBtn.onclick}),this.dTitle=EG.CE({tn:"div",innerHTML:this.title,me:this,ondblclick:c._events.dTitle.ondblclick,onclick:c._events.dTitle.onclick,onmouseup:c._events.dTitle.onmouseup,onmousedown:c._events.dTitle.onmousedown})]}),this.dChildNodes=EG.CE({tn:"div"}),this.dInsert=EG.CE({tn:"div",me:this,onmouseover:c._events.dInsert.onmouseover,onmouseout:c._events.dInsert.onmouseout,onmouseup:c._events.dInsert.onmouseup})]});if(this.titleStyle){EG.css(this.dTitle,this.titleStyle)}},doClick:function(){EG.Event.fireEvent(this.dTitle,"click")},setNodes:function(f,h){this.removeChilds(false);for(var g=0;g<f.length;g++){var j=f[g];if(EG.Object.isLit(j)){j=new c(j)}this.add(j);if(j.nodes){j.setNodes(j.nodes)}}if(h){this.render()}},setTitle:function(f){this.title=f;this.dTitle.innerHTML=f},setValue:function(f){this.value=f},getValue:function(){return this.value},select:function(f){if(f){this.focus();this.box.select(true);if(this.tree.multiple){if(!EG.Array.has(this.tree.selectedNodes,this)){this.tree.selectedNodes.push(this)}}else{if(this.tree.selectedNode!=null&&this.tree.selectedNode!=this){this.tree.selectedNode.select(false)}this.tree.selectedNode=this}}else{this.blur();this.box.select(false);if(this.tree.multiple){EG.Array.remove(this.tree.selectedNodes,this)}else{this.tree.selectedNode=null}}this.selected=f},setClassNamePre:function(f){this.element.className=f+"node";this.dNode.className=f+"node-dNode";this.dExpandBtn.className=f+"node-dNode-dExpandBtn";this.dTitle.className=f+"node-dNode-dTitle";this.dChildNodes.className=f+"node-dChildNodes";this.dInsert.className=f+"node-dInsert"},blur:function(){EG.Style.removeCls(this.dTitle,this.cls+"-node-dNode-selected")},focus:function(){EG.Style.addCls(this.dTitle,this.cls+"-node-dNode-selected")},isCollapsed:function(){return EG.Style.isHide(this.dChildNodes)},changeCollapsed:function(){if(this.isCollapsed()){this.expand()}else{this.collapse()}},expand:function(h){EG.show(this.dChildNodes);if(h){var j=[];var g=this;while(g.parentNode){j.push(g);g=g.parentNode}for(var f=j.length-1;f>=0;f--){j[f].expand()}}this.refreshCollapseElement()},collapse:function(){EG.hide(this.dChildNodes);this.refreshCollapseElement()},preNode:function(){var g=this.parentNode.childNodes;for(var f=0;f<g.length;f++){if(g[f]==this){return(f==0)?null:g[f-1]}}return null},nextNode:function(){var g=this.parentNode.childNodes;for(var f=0;f<g.length;f++){if(g[f]==this){return(f>=g.length-1)?null:g[f+1]}}return null},add:function(h,f){if(EG.Object.isLit(h)){h=new c(h)}if(f==null){f=this.childNodes.length-1;this.childNodes.push(h);this.dChildNodes.appendChild(h.getElement())}else{this.childNodes=EG.Array.insert(this.childNodes,f+1,h);if(this.dChildNodes.childNodes.length>f+1){var g=this.dChildNodes.childNodes[f+1];this.dChildNodes.insertBefore(h.getElement(),g)}else{this.dChildNodes.appendChild(h.getElement())}}this.tree.registNode(h);h.parentNode=this;this.refreshCollapseElement();h.refreshCollapseElement();if(this.childNodes.length>1){this.childNodes[this.childNodes.length-2].refreshCollapseElement()}if(!this.tree.multiple){EG.hide(h.box.getElement())}},remove:function(g){if(g==null){g=true}this.removeChilds();var f=this.preNode();this.tree.ungistNode(this);EG.Array.remove(this.parentNode.childNodes,this);this.parentNode.dChildNodes.removeChild(this.getElement());if(g){this.parentNode.refreshCollapseElement();if(f){f.refreshCollapseElement()}}},removeChilds:function(g){if(g==null){g=true}for(var f=this.childNodes.length-1;f>=0;f--){this.childNodes[f].remove(false)}if(g){this.refreshCollapseElement()}},refreshCollapseElement:function(){EG.DOM.removeChilds(this.dExpandBtn);var g,f;if(this.isLeaf()){g=this.cls+"-node-l-"+(this.isLast()?"l":"t");f=this.cls+"-node-file"}else{if(this.isCollapsed()){g=this.cls+"-node-l-"+(this.isLast()?"lPlus":"tPlus");f=this.cls+"-node-folder"}else{g=this.cls+"-node-l-"+(this.isLast()?"lMinus":"tMinus");f=this.cls+"-node-openfolder";if(!this.isLast()){EG.setCls(this.dChildNodes,["node-dChildNodes","node-bgLine"],this.cls)}else{EG.setCls(this.dChildNodes,["node-dChildNodes"],this.cls)}}}if(!this.isRoot()){EG.CE({pn:this.dExpandBtn,tn:"div",cls:g})}EG.CE({pn:this.dExpandBtn,tn:"div",cls:f});if(!this.isLast()){EG.setCls(this.dInsert,["node-dInsert","node-bgLine"],this.cls)}else{EG.setCls(this.dInsert,["node-dInsert"],this.cls)}if(this.parentNode&&this.parentNode.isRoot()){EG.css(this.element,"margin-left:0px")}else{EG.css(this.element,"margin-left:;")}},isLast:function(){if(this.parentNode==null){return true}return this.parentNode.childNodes[this.parentNode.childNodes.length-1]==this},isFirst:function(){return this.parentNode.childNodes[0]==this},isLeaf:function(){return this.childNodes.length==0},isRoot:function(){return this.root},getIdx:function(){var h=this.parentNode.childNodes;for(var g=0,f=h.length;g<f;g++){if(h[g]==this){return g}}},moveto:function(k,j){if(this==j){return}if(this.ondrag){if(this.ondrag.apply(this["ondragSrc"]||this,[this,k,j])===false){return}}var m=this.parentNode;var g=this.preNode(this);var h=this.nextNode(this);var o=null;var n=-1;var p=this.getIdx();if(k=="after"){n=j.getIdx();o=j.parentNode}else{if(k=="in"){o=j}}var l=false;var f=o;while((f=f.parentNode)&&f!=null&&f!=this.tree.rootNode){if(f==this){l=true;break}}if(l){throw new Error("不能移动到子节点")}EG.Array.remove(m.childNodes,this);if(n<0){o.add(this)}else{if(m==o){if(p<n){n--}}o.add(this,n)}m.refreshCollapseElement();if(g){g.refreshCollapseElement()}if(h){h.refreshCollapseElement()}if(this.parentNode.isRoot()){EG.css(this.element,"margin-left:0px")}this.refreshCollapseElement()}}})})();(function(){EG.define("EG.ui.Table",["EG.ui.Item","EG.ui.Dialog","EG.ui.Form","EG.ui.Button"],function(e,b,c,a,f){return{extend:e,config:{cls:"eg_table",title:null,rowCount:3,colCount:3,afterSelect:null},constructor:function(g){this.callSuper([g])},rebindTable:function(k){var n=this;this.element.replaceChild(k,this.dTable);this.dTable=k;this.dBody=k.getElementsByTagName("tbody")[0];EG.Event.bindEvent(this.dTable,ondragstart,function(){n.refreshSelect()});EG.Event.bindEvent(this.dTable,onmouseup,function(){n.selecting=false;if(n.afterSelect){n.afterSelect.apply(n)}});var g=this.dBody.childNodes;for(var l=0;l<g.length;l++){var m=g[l].childNodes;for(var h=0;h<m.length;h++){var p=m[h];var o=p.getAttribute("pos").split(",");EG.CE({ele:p,r:o[0],c:o[1],table:this,onmousedown:this.tdEvent.onmousedown,onmouseover:this.tdEvent.onmouseover,oncontextmenu:this.tdEvent.oncontextmenu})}}},build:function(){var g=this;this.element=EG.CE({tn:"div",cls:this.cls,cn:[this.dTable=EG.CE({tn:"table",cls:this.cls+"-table",border:1,cn:[this.dBody=EG.CE({tn:"tbody"})]}),this.dSelectTop=EG.CE({tn:"div",cls:this.cls+"-selectTop"}),this.dSelectRight=EG.CE({tn:"div",cls:this.cls+"-selectRight"}),this.dSelectBottom=EG.CE({tn:"div",cls:this.cls+"-selectBottom"}),this.dSelectLeft=EG.CE({tn:"div",cls:this.cls+"-selectLeft"})],ondragstart:function(){g.refreshSelect()},onmouseup:function(){g.selecting=false;if(g.afterSelect){g.afterSelect.apply(g)}}});EG.hide(this.dSelectTop,this.dSelectRight,this.dSelectBottom,this.dSelectLeft);this.buildRowCol(this.rowCount,this.colCount);this.dMenu=EG.CE({pn:this.getElement(),cls:this.cls+"-dMenu",tn:"div",cn:[{tn:"a",innerHTML:"属性",onclick:function(){g.openPFCol();EG.hide(g.dMenu)}},{tn:"a",innerHTML:"合并",onclick:function(){g.merge();EG.hide(g.dMenu)}},{tn:"a",innerHTML:"取消合并",onclick:function(){g.broke();EG.hide(g.dMenu)}},{tn:"a",innerHTML:"左侧插入一列",onclick:function(){EG.hide(g.dMenu)}},{tn:"a",innerHTML:"右侧插入一列",onclick:function(){EG.hide(g.dMenu)}},{tn:"a",innerHTML:"上方插入一行",onclick:function(){EG.hide(g.dMenu)}},{tn:"a",innerHTML:"下方插入一行",onclick:function(){EG.hide(g.dMenu)}},{tn:"a",innerHTML:"删除所在列",onclick:function(){EG.hide(g.dMenu)}},{tn:"a",innerHTML:"删除所在行",onclick:function(){EG.hide(g.dMenu)}},{tn:"a",innerHTML:"清除",onclick:function(){EG.hide(g.dMenu)}},{tn:"a",innerHTML:"表格属性",onclick:function(){g.openPFTable();EG.hide(g.dMenu)}}]});EG.hide(this.dMenu)},tdEvent:{onmousedown:function(h){var g=this.table;h=EG.Event.getEvent(h);if(h.button!=0){return}EG.hide(g.dMenu);g.selecting=true;g.startTd=this;g.endTd=this;EG.show(g.dSelectTop,g.dSelectRight,g.dSelectBottom,g.dSelectLeft);g.refreshSelect()},onmouseover:function(h){var g=this.table;if(!g.selecting){return}g.endTd=this;g.refreshSelect()},oncontextmenu:function(g){var h=this.table;if(!h.startTd){return}h.showMenu(g,h.node)}},showMenu:function(h){var g=EG.Tools.getMousePos(h,this.getElement());EG.css(this.dMenu,{top:g.y+"px",left:g.x+"px"});EG.show(this.dMenu);EG.Event.stopPropagation(h)},refreshSelect:function(j){if(!j&&!this.selecting){return}else{if(!this.endTd){return}}var g={x:this.startTd.offsetLeft-this.dTable.offsetLeft,y:this.startTd.offsetTop-this.dTable.offsetTop};var n={x:this.endTd.offsetLeft-this.dTable.offsetLeft,y:this.endTd.offsetTop-this.dTable.offsetTop};var p,o,q,m;var k=Math.min(g.x,n.x);var r=Math.min(g.y,n.y);var q=Math.max((g.x+this.startTd.clientWidth),(n.x+this.endTd.clientWidth))-k;var m=Math.max((g.y+this.startTd.clientHeight),(n.y+this.endTd.clientHeight))-r;EG.css(this.dSelectTop,{left:k+"px",top:r+"px",width:q+"px"});EG.css(this.dSelectRight,{left:(k+q)+"px",top:r+"px",height:m+"px"});EG.css(this.dSelectBottom,{left:k+"px",top:(r+m)+"px",width:q+"px"});EG.css(this.dSelectLeft,{left:k+"px",top:r+"px",height:m+"px"})},clearSelect:function(){EG.hide(this.dSelectTop,this.dSelectRight,this.dSelectBottom,this.dSelectLeft)},merge:function(){if(this.startTd==this.endTd){return}var h=Math.min(this.startTd.r,this.endTd.r);var o=Math.min(this.startTd.c,this.endTd.c);var g=Math.max(this.startTd.r,this.endTd.r);var n=Math.max(this.startTd.c,this.endTd.c);var l=null;var r=[];for(var q=h;q<=g;q++){var p=this.dBody.childNodes[q].childNodes;for(var m=0;m<p.length;m++){var k=p[m];if(k.c==o&&k.r==h){l=k}else{if(k.c>=o&&k.c<=n){r.push(k);if((k.colSpan&&k.colSpan>1)||(k.rowSpan&&k.rowSpan>1)){EG.Locker.message("待合并中有已合并的单元格");return}}}}}for(var q=0;q<r.length;q++){r[q].parentNode.removeChild(r[q])}l.colSpan=n-o+1;l.rowSpan=g-h+1;this.startTd=l;this.endTd=l;this.refreshSelect(true);this.collect()},collect:function(){var g=this.dBody.childNodes;var h=false;for(var l=g.length-1;l>=0;l--){if(g[l].childNodes.length==0){this.dBody.removeChild(g[l]);h=true}}g=this.dBody.childNodes;for(var l=0;l<g.length;l++){var m=g[l].childNodes;g[l]=l;for(var k=0;k<m.length;k++){m[k].r=l;if(m.length==1){m[k].rowSpan=""}}}},broke:function(){var y=Math.min(this.startTd.r,this.endTd.r);var s=Math.min(this.startTd.c,this.endTd.c);var v=Math.max(this.startTd.r,this.endTd.r);var r=Math.max(this.startTd.c,this.endTd.c);var l=null;var n=[];for(var w=y;w<=v;w++){var x=this.dBody.childNodes[w].childNodes;for(var u=0;u<x.length;u++){var p=x[u];if(p.c>=s&&p.c<=r){if((p.colSpan&&p.colSpan>1)||(p.rowSpan&&p.rowSpan>1)){n.push(p)}}}}for(var w=0;w<n.length;w++){var B=n[w];var o=B.rowSpan?B.rowSpan:1;var h=B.colSpan?B.colSpan:1;var z=B.r;var m=B.c;var A=B.parentNode;for(var u=0;u<=o-1;u++){var g=A;for(var t=0;t<u;t++){g=g.nextSibling}var x=g.childNodes;var q=null;for(var t=0;t<x.length;t++){if(x[w].c>=m){if(w>=1){q=x[w-1]}else{q=null}}}for(var t=m;t<=(m+h-1);t++){if(t==m&&u==0){}else{var p=EG.CE({pn:g,tn:"td",r:g.r,c:t,table:this,onmousedown:this.tdEvent.onmousedown,onmouseover:this.tdEvent.onmouseover,oncontextmenu:this.tdEvent.oncontextmenu});if(q){EG.DOM.insertAfter(p,q)}else{g.appendChild(p)}q=p}}}B.rowSpan=1;B.colSpan=1}},getTd:function(g,h){return this.dBody.childNodes[g].childNodes[h]},buildRowCol:function(g,l){this.rowCount=g;this.colCount=l;EG.DOM.removeChilds(this.dBody);for(var k=0;k<this.rowCount;k++){var m=EG.CE({pn:this.dBody,tn:"tr",r:k});for(var h=0;h<this.colCount;h++){var n=EG.CE({pn:m,tn:"td",r:k,c:h,table:this,onmousedown:this.tdEvent.onmousedown,onmouseover:this.tdEvent.onmouseover,oncontextmenu:this.tdEvent.oncontextmenu});n.setAttribute("pos",k+","+h)}}},inserCol:function(g,h){},inserRow:function(h,g){},deleteRow:function(g){},deleteCol:function(g){},openPFCol:function(g){if(!this.pfCol){this.buildPFCol()}if(!this.startTd){return}this.colForm.setData(this.getColStyle(this.startTd));this.pfCol.open()},openPFTable:function(g){if(!this.pfTable){this.buildPFTable()}this.tableForm.setData(this.getTableStyle(this.dTable));this.pfTable.open()},getColStyle:function(l){var g={};var k=f.sKey;var j=l.style;if(j){for(var h=0;h<k.length;h++){if(j[k[h]]!=null){g["td_"+k[h]]=j[k[h]]}}}g.td_inner=l.itemCfg?"":l.innerHTML;return g},getTableStyle:function(j){var g={};var h=j.style;if(h){if(h.borderColor!=null){g["table_border-color"]=h.borderColor}if(h.borderWidth!=null){g["table_border-width"]=h.borderWidth}}if(j.cellSpacing!=null){g.table_cellSpacing=j.cellSpacing}if(j.cellPadding!=null){g.table_cellPadding=j.cellPadding}return g},setColStyle:function(m,h,k){var j={};var l=f.sKey;for(var g=0;g<l.length;g++){j[l[g]]=h["td_"+l[g]]}EG.css(m,j);m.innerHTML=EG.unnull(h.td_inner,"");this.refreshSelect(true)},setTableStyle:function(h,g,j){if(!EG.isBlank(g["table_border-color"])){EG.css(h,"border-color:"+g["table_border-color"])}if(!EG.isBlank(g["table_border-width"])){EG.css(h,"border-width:"+g["table_border-width"])}if(!EG.isBlank(g.table_cellSpacing)){h.cellSpacing=g.table_cellSpacing}if(!EG.isBlank(g.table_cellPadding)){h.cellPadding=g.table_cellPadding}this.refreshSelect(true)},btn_events:{col:{save:function(){var h=this.table;if(!h.startTd){return}var g=h.colForm.getData();h.setColStyle(h.startTd,g)},cancel:function(){var g=this.table;g.pfCol.close()}},table:{save:function(){var h=this.table;var g=h.tableForm.getData();h.setTableStyle(h.dTable,g)},cancel:function(){var g=this.table;g.pfTable.close()}}},buildPFCol:function(){this.pfCol=new b({title:"单元格",layout:"border",width:300,height:"auto",lock:true,items:[this.colForm=new c({region:"center",layout:"table",height:"auto",width:"100%",items:[{xtype:"formItem",pos:[1,0],title:"内容",name:"td_inner",type:"textarea",height:40},{xtype:"formItem",pos:[2,0],title:"对齐",name:"td_text-align",type:"boxGroup",textvalues:[["左","left"],["中","center"],["右","right"],["无",""]]},{xtype:"formItem",pos:[3,0],title:"宽度",name:"td_width",type:"text"},{xtype:"formItem",pos:[3,1],title:"高度",name:"td_height",type:"text"},{xtype:"formItem",pos:[4,0],title:"字号",name:"td_font-size",type:"text"},{xtype:"formItem",pos:[4,1],title:"颜色",name:"td_color",type:"text"},{xtype:"formItem",pos:[5,0],title:"加重",name:"td_font-weight",type:"boxGroup",textvalues:[["是","bolder"],["否",""]]},{xtype:"formItem",pos:[5,1],title:"倾斜",name:"td_font-style",type:"boxGroup",textvalues:[["是","italic"],["否",""]]}]})],btns:[new a({text:"保存",click:this.btn_events.col.save,cls:"eg_button_small",table:this}),new a({text:"取消",click:this.btn_events.col.cancel,cls:"eg_button_small",table:this})],renderTo:this.getElement()})},buildPFTable:function(){this.pfTable=new b({title:"表格",layout:"border",width:300,height:"auto",lock:true,items:[this.tableForm=new c({region:"center",layout:"table",height:"auto",width:"100%",items:[{pos:[1,0],title:"边色",name:"table_border-color",type:"text"},{pos:[1,1],title:"边宽",name:"table_border-width",type:"text"},{pos:[2,0],title:"缝隙",name:"table_cellSpacing",type:"text"},{pos:[2,1],title:"间距",name:"table_cellPadding",type:"text"}]})],btns:[new a({text:"保存",click:this.btn_events.table.save,cls:"eg_button_small",table:this}),new a({text:"取消",click:this.btn_events.table.cancel,cls:"eg_button_small",table:this})],renderTo:this.getElement()})},render:function(){e.fit({element:this.element});if(this.pfCol){this.pfCol.render()}},statics:{sKey:["text-align","width","height","font-size","color","font-weight","font-style"]}}})})();(function(){EG.define("EG.ui.Box",["EG.ui.Item"],function(a,b){return{extend:a,config:{text:"",value:"",cls:"eg_box",onselect:null,onclick:null,afterselect:null,selected:false,showText:true},constructor:function(c){this.callSuper([c]);if(!this.showText){EG.hide(this.dText);EG.css(this.dBox,"display:block;")}},build:function(){this.element=EG.CE({tn:"div",cls:this.cls,item:this,onclick:b._events.element.onclick,cn:[this.dBox=EG.CE({tn:"a",cls:this.cls+"-b"}),this.dText=EG.CE({tn:"div",cls:this.cls+"-text",innerHTML:this.text})]})},select:function(e,c){if(c==null){c=true}if(c&&this.onselect){if(this.onselect.apply(this,[e])===false){return}}this.selected=e;EG.setCls(this.dBox,["b",this.selected?"select":"unselect"],this.cls);if(c&&this.afterselect){this.afterselect.apply(this,[e])}},deSelect:function(){this.selected=null;EG.setCls(this.dBox,["b","unselect"],this.cls)},setValue:function(c){this.value=c},getValue:function(){return this.value},render:function(){a.fit(this);var e=EG.getSize(this.element).innerHeight;var c=parseInt((e-EG.getSize(this.dBox).outerHeight)/2);EG.css(this.dBox,"margin-top:"+c+"px;margin-bottom:"+c+"px");EG.css(this.dText,"line-height:"+e+"px;height:"+e+"px")},statics:{_events:{element:{onclick:function(){var c=this.item;if(c.onclick){c.onclick.apply(c)}else{c.select(!c.selected)}}}}}}})})();(function(){EG.define("EG.ui.BoxGroup",["EG.ui.Item"],function(a,b){return{alias:"box",extend:a,config:{multiple:false,onselect:null,onclick:null,onchange:null,afterselect:null,textvalues:[],cls:"eg_boxgroup",defValue:null,boxHeight:null,boxStyle:null},constructor:function(c){this.callSuper([c]);this.boxes=[];this.onchangeEvents=[];if(this.onchange!=null){this.bindOnchange(this.onchange)}this.buildBoxes();EG.ui.FormItem.bindValidate.apply(this,[]);if(this.defValue){this.setValue(this.defValue)}},build:function(){this.element=EG.CE({tn:"div",cls:this.cls})},buildBoxes:function(){var g=this;for(var f=0,c=this.textvalues.length;f<c;f++){var e=this.textvalues[f];this.add({text:e[0],value:e[1],style:this.boxStyle,onclick:b._events.box.onclick,group:this,onselect:g.onselect,afterselect:g.afterselect})}},setValue:function(f,e){if(e==null){e=true}var c=this.getValue();if(this.multiple){EG.Array.each(this.boxes,function(){this.select(EG.Array.has(f,this.value))})}else{EG.Array.each(this.boxes,function(){this.select(this.value===f)})}if(!EG.Object.equals(c,f)&&e){this.doOnChange(f,c)}},getValue:function(){var e=[];for(var f=0,c=this.boxes.length;f<c;f++){var g=this.boxes[f];if(this.multiple){if(g.selected){e.push(g.value)}}else{if(g.selected){return g.value}}}if(this.multiple){return e}return null},getText:function(){var g=[];for(var e=0,c=this.boxes.length;e<c;e++){var f=this.boxes[e];if(this.multiple){if(f.selected){g.push(f.text)}}else{if(f.selected){return f.text}}}if(this.multiple){return g}return null},getSelectedBox:function(){var e=[];for(var f=0,c=this.boxes.length;f<c;f++){var g=this.boxes[f];if(this.multiple){if(g.selected){e.push(g)}}else{if(g.selected){return g}}}if(this.multiple){return e}return null},add:function(c){if(EG.isLit(c)){c=new EG.ui.Box(c)}c.pItem=this;this.boxes.push(c);this.element.appendChild(c.getElement())},getBoxes:function(){return this.boxes},bindOnchange:function(c){this.onchangeEvents.push(c)},doOnChange:function(f,c){for(var e=0;e<this.onchangeEvents.length;e++){this.onchangeEvents[e].apply(this,[f,c])}},render:function(){a.fit(this);var e=EG.getSize(this.element).innerHeight;for(var c=0;c<this.boxes.length;c++){this.boxes[c].height=this.boxHeight!=null?this.boxHeight:e;this.boxes[c].render()}},setTextvalues:function(c){EG.Array.clear(this.boxes);this.textvalues=c;EG.DOM.removeChilds(this.element);this.buildBoxes()},getTitle:function(n,f,k){var l=k.textvalues||[];if(!EG.isArray(n)){n=[n]}var h=[];for(var c=0,g=n.length;c<g;c++){var o=n[c];for(var e=0,m=l.length;e<m;e++){if(l[e][1]==o){h.push(l[e][0]);break}}}return h.join(",")},statics:{_events:{box:{onclick:function(){var g=this.group;var e=this.group.getValue();var j=false;if(g.multiple){this.select(!this.selected);j=true}else{if(g.onclick){g.onclick()}else{if(!this.selected){j=true}for(var f=0,c=g.boxes.length;f<c;f++){if(this!=g.boxes[f]){g.boxes[f].select(false)}}this.select(true)}}var h=this.group.getValue();if(j){for(var f=0;f<g.onchangeEvents.length;f++){g.onchangeEvents[f].apply(g,[h,e])}}}}}}}})})();(function(){EG.define("EG.ui.Date",["EG.ui.Item"],function(a,b){return{alias:"date",extend:a,config:{fmt:"YMDHMS",maxLength:10,dateFmt:null,cls:"eg_date",onkeydown:null,onkeyup:null,placeholder:null},constructor:function(c){this.callSuper([c]);if(this.dateFmt==null){if(this.fmt=="Y"){this.dateFmt="yyyy";this.maxLength=4}else{if(this.fmt=="YMD"){this.dateFmt="yyyy-MM-dd";this.maxLength=10}else{if(this.fmt=="YMDHMS"){this.dateFmt="yyyy-MM-dd HH:mm:ss";this.maxLength=20}else{throw new Error("暂不支持时间格式"+this.fmt)}}}}},build:function(){this.element=EG.CE({tn:"div",cls:this.cls,cn:[this.input=EG.CE({tn:"input",cls:this.cls+"-input",type:"text",maxLength:this.maxLength,length:this.maxLength,placeholder:this.placeholder||" ",item:this,onclick:b._events.input.onclick})]});EG.ui.FormItem.bindValidate.apply(this,[]);if(this.onkeyup){EG.Event.bindEvent(this.input,"onkeyup",this.onkeyup)}},setValue:function(c){EG.setValue(this.input,c)},getValue:function(){return EG.getValue(this.input)},render:function(){a.fit(this);a.fit({element:this.input,dSize:{width:"100%",height:"100%"},pSize:EG.getSize(this.element)})},statics:{_events:{input:{onclick:function(){var c=this.item;new WdatePicker({dateFmt:c.dateFmt,skin:"ext"})}}}}}})})();(function(){EG.define("EG.ui.Editor",["EG.ui.Item"],function(Item,ME){return{alias:"editor",extend:Item,config:{pluginGroupName:"def",cls:"eg_editor",uploadPolicy:null,imgUploadPolicy:null,uploadAction:null,imgUploadAction:null,onUploaded:null,imgOnUploaded:null,uploadHandleType:null,deleteUpload:null,parent:null,imgPickers:null,uploadOnPaste:false,readLineHeight:22},constructor:function(cfg){this.initItem(cfg);this.pluginGroup=ME.pluginGroups[this.pluginGroupName];if(typeof(this.onUploaded)=="string"){this.onUploaded=ME.defOnUploaded}this.dToolbarButtons=null;this.dIframe=null;this.dSource=null;this.dCache=null;this.element=EG.CE({tn:"div",cls:this.cls,cn:[this.dMenuPanels=EG.CE({tn:"div"}),this.dToolbarButtons=EG.CE({tn:"div",cls:this.cls+"-toolbar"}),this.dHtml=EG.CE({tn:"div",cls:this.cls+"-dHtml",cn:[this.frame=EG.CE({tn:"iframe",frameBorder:"0",designMode:"on",style:"height:100%",item:this})]}),this.dCache=EG.CE({tn:"div",style:"display:none"})]});EG.css(this.element,this.style);this.initIframe();this.buildPlugins()},render:function(){Item.fit(this);var size=EG.getSize(this.element);Item.fit({element:this.dToolbarButtons,dSize:{width:"100%"},pSize:size,type:"height"});var tbSize=EG.getSize(this.dToolbarButtons);Item.fit({element:this.dHtml,dSize:{width:"100%",height:size.innerHeight-tbSize.outerHeight},pSize:size});for(var i=0,il=this.plugins.length;i<il;i++){this.plugins[i].render()}},plugins:[],pluginsMap:{},clickHandlers:[],dblclickHandlers:[],buildPlugins:function(){this.buildInParents=[];for(var i=0,il=this.pluginGroup.length;i<il;i++){var name=this.pluginGroup[i];var plugin=new ME.pluginsMap[name](this);this.plugins.push(plugin);this.pluginsMap[name]=plugin;var toolbarButton=plugin.getToolbarButton();if(toolbarButton){this.dToolbarButtons.appendChild(toolbarButton.getElement())}if(plugin.getMenuPanel){var menuPanel=plugin.getMenuPanel();if(menuPanel){if(plugin.buildInParent&&this.parent){var pn=(this.parent.getElement)?this.parent.getElement():this.parent;pn.appendChild(menuPanel.getElement());this.buildInParents.push(menuPanel)}else{this.dMenuPanels.appendChild(menuPanel.getElement())}}}}},getPlugin:function(name){return this.pluginsMap[name]},initIframe:function(){EG.bindEvent(this.frame,"onload",ME._events.iframe.load);if(!EG.Browser.isIE8()){this.frame.src="about:blank"}},setContent:function(content){this.cacheHTML=content;if(this.loaded){this.doc.body.innerHTML=content}},setValue:function(value){var ct=EG.CE({tn:"div",innerHTML:value});var tns=["img","a"];for(var i=0;i<tns.length;i++){var els=ct.getElementsByTagName(tns[i]);for(var j=0;j<els.length;j++){var el=els[j];var uri;var attName="";if(el.hasAttribute("srcUri")){attName="src";uri=el.getAttribute("srcUri")}else{if(el.hasAttribute("hrefUri")){attName="href";uri=el.getAttribute("hrefUri")}else{continue}}el.setAttribute(attName,uri)}}this.setContent(value)},getContent:function(){if(!this.loaded||!this.doc){return EG.n2d(this.cacheHTML,"")}return this.doc.body.innerHTML},getValue:function(){return this.getContent()},focus:function(){if(EG.Browser.isChrome()){this.doc.body.focus()}else{if(EG.Browser.isIE()){this.frame.contentWindow.focus()}else{this.frame.focus()}}},hideMenus:function(){for(var i=0,il=this.plugins.length;i<il;i++){var plugin=this.plugins[i];if(!plugin.getMenuPanel){continue}var menuPanel=plugin.getMenuPanel();if(menuPanel){menuPanel.close()}}},htmlexec:function(type,para){this.focus();if(!para){if(EG.Browser.isIE()){this.doc.execCommand(type,false)}else{this.doc.execCommand(type,false,false)}}else{this.doc.execCommand(type,false,para)}this.focus()},pasteHTML:function(html){this.focus();if(EG.Browser.isIE()){this.getSelection().createRange().pasteHTML(html)}else{this.doc.execCommand("InsertHtml",false,html)}},getSelection:function(){if(EG.Browser.isIE()){return this.doc.selection}return this.doc.getSelection()},addResOnUploaded:function(cfg){var me=this;var ct=cfg.ct;var r=cfg.r;var type=r.type;var file=r.file;var doDelete=r.doDelete;var el=EG.CE({tn:"div",cls:this.cls+"-uploadLabel",style:EG.Style.c.dv+";min-width:40px;max-width:200px",onmouseover:ME._events.dUploadLabel.onmouseover,onmouseout:ME._events.dUploadLabel.onmouseout,cn:[{tn:"div",onclick:function(){var p;if(type=="image"){p=me.getPlugin("image");p.showMenuPanel();p.setImageForm({src:file.path})}else{if(type=="video"){p=me.getPlugin("video");p.showMenuPanel();p.setVideoForm(file)}else{if(type=="zip"){p=me.getPlugin("zip");p.showMenuPanel();p.setZipForm(file)}}}},innerHTML:file.name,style:EG.Style.c.dv+";cursor:pointer"},{tn:"a",cls:this.cls+"-uploadLabel-closer",onclick:function(){var ma=this;doDelete(function(){ma.parentNode.parentNode.removeChild(ma.parentNode)})}}]});ct.appendChild(el)},destroy:function(){for(var i=0;i<this.buildInParents.length;i++){this.buildInParents[i].destroy();EG.DOM.remove(this.buildInParents[i].getElement())}},execute:function(name,args){var p=this.getPlugin(name);p.execute(args)},statics:{def:{fontSize:"14px"},pluginsMap:{},pluginGroups:{def:["bold","italic","underline","fontname","fontsize","color","textalign","list","indent","image","code","full","program"],simple:["bold","italic","underline","fontname","fontsize","color","textalign","list","indent"]},registPlugin:function(name,plugin){ME.pluginsMap[name]=plugin},_events:{iframe:{load:function(){var me=this.item;me.doc=this.contentDocument||this.contentWindow.document;var fn1=function(){if(me.loaded){return}me.doc.body.designMode="on";me.doc.body.contentEditable=true;me.doc.body.style.fontSize=ME.def.fontSize;EG.css(me.doc.body,"line-height:1.5;margin:0; padding:8px 8px;font-size:"+ME.def.fontSize);if(me.cacheHTML!=null){me.doc.body.innerHTML=me.cacheHTML||"<p>&nbsp;</p>"}else{me.doc.body.innerHTML="<p>&nbsp;</p>"}EG.bindEvent(me.doc.body,"blur",function(e){me.cacheHTML=me.getContent()});me.loaded=true};if(EG.Browser.isIE8()){me.doc.designMode="on";EG.bindEvent(me.doc,"onreadystatechange",function(){if(me.doc.body){fn1()}})}else{fn1()}me.doc.editor=me;me.doc.onclick=function(){me.hideMenus()};EG.bindEvent(me.doc,"onpaste",function(e){if(me.uploadOnPaste){if(e.clipboardData.items){var ele=e.clipboardData.items;for(var i=0;i<ele.length;++i){if(ele[i].kind=="file"&&ele[i].type.indexOf("image/")!==-1){var blob=ele[i].getAsFile();var content=new window.FormData(EG.CE({tn:"form",enctype:"multipart/form-data",encoding:"multipart/form-data"}));content.append("uploadfile",blob);EG.Ajax.send({method:"POST",url:me.imgUploadAction,content:content,contentType:false,callback:function(data){var result;eval("result="+data+";");if(result[0]==0){me.pasteHTML("<img src='"+result[1]["path"]+"'>")}}})}}}}return false});if(EG.Browser.isIE()){EG.bindEvent(this,"beforedeactivate",function(){var range=me.doc.selection.createRange();if(range.getBookmark){this.ieSelectionBookmark=range.getBookmark()}});EG.bindEvent(this,"activate",function(){if(this.ieSelectionBookmark){var range=me.doc.body.createTextRange();range.moveToBookmark(this.ieSelectionBookmark);range.select();this.ieSelectionBookmark=null}})}EG.bindEvent(me.doc,"keydown",function(e){if(e==null){e=window.event}var code=e.keyCode;if(e.ctrlKey){}else{if(e.shiftKey){}else{switch(code){case 13:}}}});EG.bindEvent(me.doc,"click",function(e){for(var i=0,il=me.clickHandlers.length;i<il;i++){me.clickHandlers[i](e)}});EG.bindEvent(me.doc,"dblclick",function(e){for(var i=0,il=me.dblclickHandlers.length;i<il;i++){me.dblclickHandlers[i](e)}})}},dUploadLabel:{onmouseover:function(){EG.show(this.childNodes[1])},onmouseout:function(){EG.hide(this.childNodes[1])}},dUploadName:{}}}}});var Editor=EG.ui.Editor})();(function(){EG.define("EG.ui.editor.Plugin",{config:{buildInParent:false},getToolbarButton:function(){return this.toolbarButton},getMenuPanel:function(){return this.menuPanel},render:function(){}})})();(function(){EG.define("EG.ui.editor.ToolbarButton",{extend:"EG.ui.Item",config:{type:null,cls:null},constructor:function(b,a){this.initItem(a);this.editor=b;this.button=EG.CE({tn:"a",cls:b.cls+"-toolbar-"+this.cls,href:"javascript:void(0)"});if(a.click){EG.Event.bindEvent(this.button,"onclick",a.click)}this.element=this.button}})})();(function(){EG.define("EG.ui.editor.plugin.Bold",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"bold",click:function(){c.execute()},cls:"bold"})},execute:function(){this.editor.htmlexec("Bold")}});var a=EG.ui.editor.plugin.Bold;EG.ui.Editor.registPlugin("bold",a)})();(function(){EG.define("EG.ui.editor.plugin.Code",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.codeModel=false;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"code",click:function(){c.codeModel=!c.codeModel;c.execute(c.codeModel)},mouseover:function(){},cls:"code"});this.editPanel=EG.CE({pn:this.editor.dHtml,tn:"textarea",style:"width:100%;background:#FEFEFE;border:0px;margin:0px;padding:0px"});EG.hide(this.editPanel)},execute:function(f){if(f){EG.setValue(this.editPanel,this.editor.getContent());EG.css(this.editPanel,"height:"+this.editor.frame.clientHeight+"px;width:"+this.editor.frame.clientWidth+"px");EG.show(this.editPanel);EG.hide(this.editor.frame);var e=this.editor.dToolbarButtons.childNodes;for(var c=0,b=e.length;c<b;c++){if(e[c]!=this.toolbarButton.getElement()){EG.hide(e[c])}}}else{this.editor.setContent(EG.getValue(this.editPanel));EG.hide(this.editPanel);EG.show(this.editor.frame);var e=this.editor.dToolbarButtons.childNodes;for(var c=0,b=e.length;c<b;c++){EG.show(e[c])}}}});var a=EG.ui.editor.plugin.Code;EG.ui.Editor.registPlugin("code",a)})();(function(){EG.define("EG.ui.editor.plugin.Color",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"color",click:function(){c.editor.hideMenus();var e=c.toolbarButton.getElement();var f=EG.Tools.getElementPos(e,c.editor.getElement());f.y=e.clientHeight;EG.Style.moveTo(c.menuPanel.dPop,f);c.menuPanel.open()},mouseover:function(){},cls:"color"});this.buildMenuPanel()},execute:function(b){this.editor.htmlexec("ForeColor",b)},buildMenuPanel:function(){this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});var n=this;var g;var b=EG.CE({tn:"table",cellSpacing:0,cellPadding:0,style:"float:left;border:0px;margin:0px",cn:[g=EG.CE({tn:"tbody"})]});var c=5;for(var f=0;f<16;f++){if(f%4==0){continue}var m=EG.CE({pn:g,tn:"tr"});for(var e=0;e<30;e++){var l=e%c;if(e%4==0){continue}var k=Math.floor(e/c)*3;var h=k+3;(function(){var j=a.wc((a.cnum[h]*l+a.cnum[k]*(c-l)),(a.cnum[h+1]*l+a.cnum[k+1]*(c-l)),(a.cnum[h+2]*l+a.cnum[k+2]*(c-l)),f);EG.CE({pn:m,tn:"td",cn:[{tn:"div",cls:n.editor.cls+"-toolbar-color-box",style:"background-color:"+j,onclick:function(){n.execute(j);n.menuPanel.close()},onmouseover:function(){EG.setCls(this,["toolbar-color-box","toolbar-color-boxOn"],n.editor.cls)},onmouseout:function(){EG.setCls(this,["toolbar-color-box"],n.editor.cls)}}]})})()}}this.menuPanel.addChildren(b)},statics:{hexch:["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F"],cnum:[1,0,0,1,1,0,0,1,0,0,1,1,0,0,1,1,0,1,1,0,0],colors:["#FF0000","#00FF00","#0000FF","#FFFF00","#00FFFF","#FF00FF"],toHex:function(e){var c,b,e=Math.round(e);b=e%16;c=Math.floor((e/16))%16;return(a.hexch[c]+a.hexch[b])},wc:function(f,e,c,h){f=((f*16+f)*3*(15-h)+128*h)/15;e=((e*16+e)*3*(15-h)+128*h)/15;c=((c*16+c)*3*(15-h)+128*h)/15;return"#"+a.toHex(f)+a.toHex(e)+a.toHex(c)}}});var a=EG.ui.editor.plugin.Color;EG.ui.Editor.registPlugin("color",a)})();(function(){EG.define("EG.ui.editor.plugin.Fontname",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"fontname",text:"字体",click:function(){c.editor.hideMenus();var e=c.toolbarButton.getElement();var f=EG.Tools.getElementPos(e,c.editor.getElement());f.y=e.clientHeight;EG.Style.moveTo(c.menuPanel.dPop,f);c.menuPanel.open()},mouseover:function(){},style:"font-size:12px",cls:"fontname"});this.buildMenuPanel()},execute:function(b){this.editor.htmlexec("Fontname",b)},buildMenuPanel:function(){this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});var e=this;var f=EG.CE({tn:"div",style:""});for(var c=0,b=a.fontnames.length;c<b;c++){(function(){var g=a.fontnames[c];EG.CE({pn:f,tn:"a",cls:e.editor.cls+"-toolbar-fontname-box",style:"font-family:"+g,innerHTML:g,onclick:function(){e.execute(g);e.menuPanel.close()}})})()}this.menuPanel.addChildren(f)},statics:{fontnames:["微软雅黑","宋体","黑体","楷体_GB2312","隶书","幼圆","Arial","Arial Narrow","Arial Black","Comic Sans MS","Courier","System","Times New Roman"]}});var a=EG.ui.editor.plugin.Fontname;EG.ui.Editor.registPlugin("fontname",a)})();(function(){EG.define("EG.ui.editor.plugin.Fontsize",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"fontsize",click:function(){c.editor.hideMenus();var e=c.toolbarButton.getElement();var f=EG.Tools.getElementPos(e,c.editor.getElement());f.y=e.clientHeight;EG.Style.moveTo(c.menuPanel.dPop,f);c.menuPanel.open()},mouseover:function(){},cls:"fontsize"});this.buildMenuPanel()},execute:function(b){this.editor.htmlexec("FontSize",b)},buildMenuPanel:function(){this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});var e=this;var f=EG.CE({tn:"div",style:""});for(var c=0,b=a.fontsizes.length;c<b;c++){(function(){var g=a.fontsizes[c];EG.CE({pn:f,tn:"a",cls:e.editor.cls+"-toolbar-fontsize-box",style:"font-size:"+(g+10)+"px",innerHTML:g+"号",onclick:function(){e.execute(g);e.menuPanel.close()}})})()}this.menuPanel.addChildren(f)},statics:{fontsizes:[1,2,3,4,5,6,7]}});var a=EG.ui.editor.plugin.Fontsize;EG.ui.Editor.registPlugin("fontsize",a)})();(function(){EG.define("EG.ui.editor.plugin.Full",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"full",click:function(){c.execute()},cls:"full"})},execute:function(){if(!this.fulled){if(!this.d){this.d=new EG.ui.Dialog({lock:true,closeable:false,height:"100%",width:"100%",renderTo:EG.getBody(),id:"dddd",btns:[{text:"关闭",click:function(){me.d.close()}}]});this.d.open();this.d.dBody.appendChild(this.editor.getElement());this.editor.width="100%";this.editor.height="100%";this.editor.render()}this.d.open()}else{this.d.close()}}});var a=EG.ui.editor.plugin.Full;EG.ui.Editor.registPlugin("full",a)})();(function(){EG.define("EG.ui.editor.plugin.Image",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.buildInParent=true;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"image",click:function(){if(c.resPicker){c.resPicker.close()}c.showMenuPanel()},mouseover:function(){},cls:"image"});this.uploadPolicy=this.editor.imgUploadPolicy||this.editor.uploadPolicy;this.uploadAction=this.editor.imgUploadAction||this.editor.uploadAction;this.onUploaded=this.editor.imgOnUploaded||this.editor.onUploaded;this.buildMenuPanel();this.editor.dblclickHandlers.push(function(f){if(f.target&&f.target.tagName.toUpperCase()=="IMG"){c.selectedImg=f.target;c.setImageForm(f.target,true);c.showMenuPanel()}});this.editor.clickHandlers.push(function(f){if(f.target&&f.target.tagName.toUpperCase()=="IMG"){}else{c.selectedImg=null}})},showMenuPanel:function(){this.editor.hideMenus();this.menuPanel.open();this.editor.curMenuPanel=this.menuPanel;this.render()},execute:function(e){var b;var c=this;if(typeof(e)=="string"){e={src:e}}if(this.selectedImg){b=this.selectedImg;this.setImageAtrs(b,e)}else{b=EG.CE({tn:"img",onload:function(){var f=EG.DOM.getOuterHTML(this);c.editor.pasteHTML(f)}});this.setImageAtrs(b,e);b.src=e.src}},render:function(){this.imgForm.render()},getUploadAction:function(b){return EG.MMVC.getPath().upload+"/?uploadPolicy="+this.uploadPolicy+"&type="+b},beforeUpload:function(){return true},buildMenuPanel:function(){var e=this;var g=this.editor.parent||this.editor;if(g.getElement){g=g.getElement()}var j=this.editor.imgPickers;this.menuPanel=new EG.ui.Dialog({closeable:true,lock:true,posFix:true,title:"图片设置",fullable:true,parent:g,width:350,height:"auto",layout:{type:"line",direct:"V"},items:[this.imgForm=new EG.ui.Form({width:350,height:"auto",labelWidth:50,layout:"default",items:[{xtype:"tabPanel",width:"100%",height:120,items:[{tab:{title:"图片",style:"width:60px"},panel:{layout:"table",items:[{xtype:"formItem",pos:[0,0],title:"图片",name:"imgShowArea",type:"label",length:15,notnull:true,height:40},{xtype:"button",pos:[0,1],text:"选择",hidden:j?false:true,click:function(){e.resPicker.open()}},{xtype:"formItem",pos:[1,0],title:"路径",name:"imgSrc",type:"text",style:"overflow:hidden"},{xtype:"formItem",pos:[2,0],title:"宽",name:"imgWidth",type:"text",length:5,style:"width:40px",after:"px"},{xtype:"formItem",pos:[2,1],title:"高",name:"imgHeight",type:"text",length:5,style:"width:40px",after:"px"},{xtype:"formItem",pos:[3,0],title:"隐藏",name:"imgSrcUri",type:"text",hidden:true}]}},{tab:{title:"浮动",style:"width:60px"},tabStyle:"width:100",panel:{layout:"table",items:[{xtype:"formItem",pos:[0,0],title:"环绕",name:"imgFloat",type:"select",textvalues:[["不环绕",""],["向左","left"],["向右","right"]]},{xtype:"formItem",pos:[1,0],title:"左距",name:"imgMarginLeft",type:"text",length:10,after:"px"},{xtype:"formItem",pos:[1,1],title:"右距",name:"imgMarginRight",type:"text",length:10,after:"px"},{xtype:"formItem",pos:[2,0],title:"上距",name:"imgMarginTop",type:"text",length:10,after:"px"},{xtype:"formItem",pos:[2,1],title:"下距",name:"imgMarginBottom",type:"text",length:10,after:"px"}]}}]}]})],btns:[{text:"确定",cls:"eg_button_small",click:function(){e.doInsertImage();e.menuPanel.close()}},{text:"取消",cls:"eg_button_small",click:function(){e.menuPanel.close()},style:"margin-left:10px"}]});if(j){this.resPicker_opened=false;var h=j.types;var f=[];this.tpm={};for(var b=0;b<h.length;b++){var c=h[b];f.push([c.title,c.type]);this.tpm[c.type]=c}this.resPicker=new EG.ui.Dialog({renderTo:g,title:"选择图片",width:"100%",height:"auto",style:"margin:10px;z-index:2;",lock:true,posFix:true,layout:{type:"line",direct:"V"},items:[{xtype:"panel",height:EG.ui.FormItem._config.height,layout:"line",items:[this.sltCg=new EG.ui.FormItem({xtype:"formItem",title:"分类",type:"select",textvalues:f,width:200,onchange:function(k){e.loadPickerFiles(k)}}),{xtype:"button",text:"刷新",cls:"eg_button_small",onclick:function(){e.loadPickerFiles(e.sltCg.getValue())}}]},this.rpm=new EG.ui.Panel({xtype:"panel",style:"margin:3px;border:1px solid gray;overflow:auto",height:450}),this.rpf=new EG.ui.Panel({xtype:"panel",style:"margin:3px",height:30})],btns:[{xtype:"button",text:"选择",cls:"eg_button_small",click:function(){e.doPickerSelect()}}],afterOpen:function(){if(!e.resPicker_opened){var k=e.sltCg.getValue();if(k){e.loadPickerFiles(k)}}e.resPicker_opened=true}})}if(this.uploadPolicy||this.uploadAction){this.imgForm.items[0].addItem({tab:{title:"上传",style:"width:60px"},panel:{layout:"table",items:[{xtype:"formItem",title:"上传",name:"imgUpload",type:"upload",height:50,style:"overflow:hidden",autoupload:true,action:this.uploadAction||this.getUploadAction("image"),callback:function(k){var l=k.file?k.file:k;e.setImageForm({src:l.path});EG.Locker.lock(false);if(e.onUploaded){e.onUploaded.apply(e.editor,[k])}},exts:["JPEG","JPG","PNG","GIF","BMP"],showWait:true,beforeUpload:function(){return e.beforeUpload("img")}}]}})}},doPickerSelect:function(){var c=this.tpm[this.sltCg.getValue()];var b=c.getSelectedUri();if(b==null){EG.Locker.message("请选择文件");return}this.imgForm.getFormItem("imgSrc").setValue(b.src);this.imgForm.getFormItem("imgShowArea").setValue("<img src='"+b.src+"' height='40' />");this.imgForm.getFormItem("imgSrcUri").setValue(b.uri);this.resPicker.close()},loadPickerFiles:function(b){var c=this.tpm[b];c.load(this.rpm)},setFiles:function(b){this.rpm.clear();this.rpf.getElement().innerHTML=""+b.length+"个文件"},doInsertImage:function(c){var b={};if(arguments.length==0){c=this.imgForm.getData()}b.src=c.imgSrc;if(b.src==""){EG.Locker.message("请选择图片或上传新图片");return}b.width=c.imgWidth;b.height=c.imgHeight;b.style={};if(c.imgFloat!=""){EG.css(b,"float:"+c.imgFloat)}if(c.imgMarginLeft!=""){EG.css(b,"marginLeft:"+c.imgMarginLeft)}if(c.imgMarginRight!=""){EG.css(b,"marginRight:"+c.imgMarginRight)}if(c.imgMarginTop!=""){EG.css(b,"marginTop:"+c.imgMarginTop)}if(c.imgMarginBottom!=""){EG.css(b,"marginBottom:"+c.imgMarginBottom)}b.srcUri=this.imgForm.getFormItem("imgSrcUri").getValue();this.execute(b)},setImageAtrs:function(b,c){if(c.style){EG.css(b,c.style)}if(c.width==null||c.width==""){b.removeAttribute("width")}else{b.width=c.width}if(c.height==null||c.height==""){b.removeAttribute("height")}else{b.height=c.height}if(c.srcUri!=null&&c.srcUri!=""){b.setAttribute("srcUri",c.srcUri)}},setImageFormByImg:function(b){var c={src:b.src,style:b.style};this.setImageForm(c)},setImageForm:function(e){this.imgForm.items[0].getTab(0).select();var g=EG.CE({tn:"img"});var c=this;g.onload=function(){var h=this.width,j=this.height;c.imgForm.getFormItem("imgWidth").setValue(this.width);c.imgForm.getFormItem("imgHeight").setValue(this.height);if(this.width>this.height){this.height=j/(h/100);this.width=100}else{this.width=h/(j/40);this.height=40}};g.style.cursor="pointer";g.ondblclick=function(){window.open(this.src)};g.title="双击查看图片";g.src=e.src;if(e.style){if(e.style["float"]){this.imgForm.getFormItem("imgFloat").setValue(e.style["float"].toLowerCase())}if(e.style.marginLeft){this.imgForm.getFormItem("imgMarginLeft").setValue(e.style.marginLeft.toLowerCase())}if(e.style.marginRight){this.imgForm.getFormItem("imgMarginRight").setValue(e.style.marginRight.toLowerCase())}if(e.style.marginTop){this.imgForm.getFormItem("imgMarginTop").setValue(e.style.marginTop.toLowerCase())}if(e.style.marginBottom){this.imgForm.getFormItem("imgMarginBottom").setValue(e.style.marginBottom.toLowerCase())}}if(e.width!=null){this.imgForm.getFormItem("imgWidth").setValue(e.width)}if(e.height!=null){this.imgForm.getFormItem("imgHeight").setValue(e.height)}this.imgForm.getFormItem("imgShowArea").setValue("");this.imgForm.getFormItem("imgShowArea").prop.getElement().appendChild(g);var f=e.src;var b=EG.Browser.getDomainAddress();if(f.indexOf(b)==0){f=f.substr(b.length)}this.imgForm.getFormItem("imgSrc").setValue(f)}});var a=EG.ui.editor.plugin.Image;EG.ui.Editor.registPlugin("image",a)})();(function(){EG.define("EG.ui.editor.plugin.Indent",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"indent",click:function(){c.editor.hideMenus();var e=c.toolbarButton.getElement();var f=EG.Tools.getElementPos(e,c.editor.getElement());f.y=e.clientHeight;EG.Style.moveTo(c.menuPanel.dPop,f);c.menuPanel.open()},mouseover:function(){},cls:"indent"});this.buildMenuPanel()},execute:function(b){this.editor.htmlexec(b)},buildMenuPanel:function(){this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});var e=this;var f=EG.CE({tn:"div",style:""});for(var c=0,b=a.indents.length;c<b;c++){(function(){var g=a.indents[c];EG.CE({pn:f,tn:"a",cls:e.editor.cls+"-toolbar-indent-box",innerHTML:g[0],onclick:function(){e.execute(g[1]);e.menuPanel.close()}})})()}this.menuPanel.addChildren(f)},statics:{indents:[["增加缩进","Indent"],["减少缩进","Outdent"]]}});var a=EG.ui.editor.plugin.Indent;EG.ui.Editor.registPlugin("indent",a)})();(function(){EG.define("EG.ui.editor.plugin.Italic",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"italic",click:function(){c.execute()},cls:"italic"})},execute:function(){this.editor.htmlexec("Italic")}});var a=EG.ui.editor.plugin.Italic;EG.ui.Editor.registPlugin("italic",a)})();(function(){EG.define("EG.ui.editor.plugin.List",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"list",click:function(){c.editor.hideMenus();var e=c.toolbarButton.getElement();var f=EG.Tools.getElementPos(e,c.editor.getElement());f.y=e.clientHeight;EG.Style.moveTo(c.menuPanel.dPop,f);c.menuPanel.open()},mouseover:function(){},cls:"list"});this.buildMenuPanel()},execute:function(b){this.editor.htmlexec("Insert"+b+"List")},buildMenuPanel:function(){this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});var e=this;var f=EG.CE({tn:"div",style:""});for(var c=0,b=a.lists.length;c<b;c++){(function(){var g=a.lists[c];EG.CE({pn:f,tn:"a",cls:e.editor.cls+"-toolbar-list-box",innerHTML:g[0],onclick:function(){e.execute(g[1]);e.menuPanel.close()}})})()}this.menuPanel.addChildren(f)},statics:{lists:[["数字列表","Ordered"],["符号列表","Unordered"]]}});var a=EG.ui.editor.plugin.List;EG.ui.Editor.registPlugin("list",a)})();(function(){EG.define("EG.ui.editor.plugin.Program",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"program",click:function(){c.menuPanel.open()},cls:"program"});this.buildMenuPanel()},buildMenuPanel:function(){var b=this;var c=this.editor.parent||this.editor;if(c.getElement){c=c.getElement()}this.menuPanel=new EG.ui.Dialog({closeable:true,lock:true,posFix:true,title:"代码编辑",fullable:true,parent:c,width:350,height:"auto",layout:{type:"line",direct:"V"},items:[this.fiText=new EG.ui.FormItem({xtype:"formItem",title:"",showLeft:false,type:"textarea",length:15,notnull:true,height:150})],btns:[{text:"确定",cls:"eg_button_small",click:function(){b.doInsertProgram()}},{text:"取消",cls:"eg_button_small",click:function(){b.menuPanel.close()},style:"margin-left:10px"}]})},doInsertProgram:function(){var b=this.fiText.getValue();b=EG.String.trim(b);b=EG.Tools.filterSpecChar(b);if(!EG.String.startWith(b,"<pre ")){b="<pre class='"+this.editor.cls+"-toolbar-program-code'>"+b}if(!EG.String.endWith(b,"</pre>")){b=b+"</pre>"}this.editor.pasteHTML(b)}});var a=EG.ui.editor.plugin.Program;EG.ui.Editor.registPlugin("program",a)})();(function(){EG.define("EG.ui.editor.plugin.Textalign",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"textalign",click:function(){c.editor.hideMenus();var e=c.toolbarButton.getElement();var f=EG.Tools.getElementPos(e,c.editor.getElement());f.y=e.clientHeight;EG.Style.moveTo(c.menuPanel.dPop,f);c.menuPanel.open()},mouseover:function(){},cls:"textalign"});this.buildMenuPanel()},execute:function(b){this.editor.htmlexec("Justify"+b)},buildMenuPanel:function(){this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});var e=this;var f=EG.CE({tn:"div",style:""});for(var c=0,b=a.fontaligns.length;c<b;c++){(function(){var g=a.fontaligns[c];EG.CE({pn:f,tn:"a",cls:e.editor.cls+"-toolbar-textalign-box",innerHTML:g[0],onclick:function(){e.execute(g[1]);e.menuPanel.close()}})})()}this.menuPanel.addChildren(f)},statics:{fontaligns:[["左对齐","Left"],["居中对齐","Center"],["右对齐","Right"]]}});var a=EG.ui.editor.plugin.Textalign;EG.ui.Editor.registPlugin("textalign",a)})();(function(){EG.define("EG.ui.editor.plugin.Underline",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"underline",click:function(){c.execute()},cls:"underline"})},execute:function(){this.editor.htmlexec("Underline")}});var a=EG.ui.editor.plugin.Underline;EG.ui.Editor.registPlugin("underline",a)})();(function(){EG.define("EG.ui.Upload",["EG.ui.Item","EG.ui.Button"],function(b,a,c){return{alias:"upload",extend:b,config:{button:null,action:null,callback:null,beforeUpload:null,exts:null,showWait:false,showFilename:true,showPath:true,autoupload:false,onselect:null,filename:null,subSelf:true,cls:"eg_upload",showProcess:false,name:null,paramsConfig:null,btnText:"选择",btnCls:null,btnStyle:""},constructor:function(e){this.initItem(e);this.idx=EG.UI.GITEMIDX++;EG.UI.GITEMS[this.idx]=this;this.filename=this.name||this.filename||("file"+this.idx);EG.DOM.getActionFrame();if(this.subSelf){this.element=EG.CE({tn:"form",cls:this.cls,method:"post",encoding:"multipart/form-data",target:"actionFrame"})}else{this.element=EG.CE({tn:"div",cls:this.cls})}this.setAction(this.action);EG.CE({ele:this.element,item:this,cn:[this.dPath=EG.CE({tn:"div",cls:this.cls+"-dPath",style:"display:none;height:22px;"}),this.dFileName=EG.CE({tn:"div",cls:this.cls+"-dFileName",style:"display:none",cn:[]}),this.dFileinput=EG.CE({tn:"div",cls:this.cls+"-dFileinput",style:"position:relative",cn:[this.dSelectBtn=new a({text:this.btnText,click:function(){},style:"margin-left:2px"+this.btnStyle}),this.fileinput=EG.CE({tn:"input",cls:this.cls+"-fileinput",name:this.filename,type:"file",style:"cursor:pointer;position:absolute;left:0px;top:0px;opacity:0;filter:alpha(opacity=0);"})]}),this.dHiddenParams=EG.CE({tn:"div",style:"display:none"})]});var f=this;EG.bindEvent(this.fileinput,"onchange",function(){var h=EG.getValue(f.fileinput);if(h){var j=h.lastIndexOf("/");if(j==-1){j=h.lastIndexOf("\\")}var g=h.substr(j+1);EG.setValue(f.dFileName,g);if(f.showFilename){EG.show(f.dFileName)}if(f.onselect){f.onselect.apply(f,[h,g])}if(f.autoupload){f.submit()}}else{EG.hide(f.dFileName)}});if(!this.autoupload&&this.showBtn){if(!this.button){this.button=new a({text:this.btnText,click:function(){f.submit()},style:"veritical-align:middle;margin-left:10px"}).getElement()}this.element.appendChild(this.button)}EG.css(this.element,this.style);if(this.paramsConfig){this.setParams(this.paramsConfig)}},setAction:function(e){if(this.callback){e+="&callback=parent.EG.UI.GITEMS["+this.idx+"].onUploaded&onerror=parent.EG.UI.GITEMS["+this.idx+"].onError"}if(this.showProcess){e+="&callbackProcess=parent.EG.UI.GITEMS["+this.idx+"].onProcess"}this.action=e;if(this.subSelf){EG.CE({ele:this.element,action:e})}},setValue:function(e){EG.setValue(this.dPath,e)},getValue:function(){return EG.getValue(this.dPath)},getElement:function(){return this.element},setParams:function(f){for(var e in f){this.setParam(e,f[e])}},setParam:function(e,h){var f=this.dHiddenParams.childNodes;var g=null;for(var j in f){if(j.name=="name"){g=j;break}}if(g==null){g=EG.CE({pn:this.dHiddenParams,tn:"input",type:"hidden",name:e,value:h})}g.value=h},removeParam:function(e){var f=this.dHiddenParams.childNodes;var g=null;for(var h in f){if(h.name=="name"){g=h;break}}if(g!=null){EG.DOM.remove(g)}},submit:function(){if(EG.getValue(this.fileinput)==""){EG.Locker.message("请选择文件上传");return}if(!this.checkExt()){EG.Locker.message("上传类型需为"+this.exts+"的一种");return}if(this.beforeUpload&&!this.beforeUpload.apply(this)){return}if(this.showWait){EG.Locker.wait("正在上传文件,请稍后")}this.element.submit()},checkExt:function(){if(!this.exts){return true}var f=this.getExt();for(var e=0;e<this.exts.length;e++){if(this.exts[e].toUpperCase()===f.toUpperCase()){return true}}return false},getExt:function(){var e=EG.getValue(this.fileinput);return(e.substr(e.length-5)).substr((e.substr(e.length-5)).indexOf(".")+1)},onUploaded:function(e){if(this.showPath){EG.show(this.dPath)}EG.setValue(this.dPath,e.path);if(this.showWait){EG.Locker.lock(false)}if(typeof(this.callback)=="string"){if(this.callback=="showImg"){if(e.path==null){throw new Error("上传的返回值中不带path")}EG.setValue(this.dPath,e.path);EG.DOM.removeChilds(this.dPreview);EG.CE({pn:this.dPreview,tn:"img",width:"50",height:"30",src:e.path,style:"cursor:pointer",onclick:function(){window.open(this.src)}})}}else{this.callback.apply(this,arguments)}},onError:function(e){EG.Locker.message(e.exMessage)},onProcess:function(e,f){EG.Locker.message("已上传:"+parseInt(e/1024)+"K"+parseInt((e/f*100))+"%")},render:function(){b.fit(this)},destroy:function(){EG.UI.GITEMS[this.idx]=null},statics:{Callback:{showImg:"showImg"}}}})})();(function(){EG.define("EG.ui.Text",["EG.ui.Item","EG.ui.FormItem"],function(a,c,b){return{alias:"text",extend:a,config:{length:null,cls:"eg_text",onkeydown:null,onkeyup:null,inputStyle:null,dataType:null,placeholder:null},constructor:function(e){this.callSuper([e]);if(this.length!=null){this.input.maxLength=this.length}},build:function(){this.element=EG.CE({tn:"div",cls:this.cls,item:this,style:"position:relative;",cn:[this.input=EG.CE({tn:"input",type:"text",cls:this.cls+"-input",item:this,style:"position:absolute;"+EG.unnull(this.inputStyle,""),placeholder:this.placeholder||" "})]});if(this.dataType){this.dDataType=EG.CE({pn:this.element,tn:"div",style:"position:absolute;right:0px;bottom:0px;width:10px;height:10px",cls:this.dataType})}c.bindValidate.apply(this,[]);if(this.onkeydown){EG.Event.bindEvent(this.input,"onkeydown",this.onkeydown)}if(this.onkeyup){EG.Event.bindEvent(this.input,"onkeyup",this.onkeyup)}},setValue:function(e){EG.setValue(this.input,e)},getValue:function(){var e=EG.getValue(this.input);if(this.dataType&&this.dataType=="num"){e=parseFloat(e)}else{if(this.dataType&&this.dataType=="bool"){e=(e==="true")}}return e},render:function(){a.fit(this);var e=EG.getSize(this.element);a.fit({element:this.input,pSize:e});EG.css(this.input,"line-height:"+EG.getSize(this.input).innerHeight+"px")}}})})();(function(){EG.define("EG.ui.Textarea",["EG.ui.Item","EG.ui.FormItem"],function(a,c,b){return{alias:"textarea",extend:a,config:{length:null,style:null,cls:"eg_textarea",readLineHeight:22},constructor:function(e){this.initItem(e);this.element=EG.CE({tn:"div",cls:this.cls,item:this,cn:[this.input=EG.CE({tn:"textarea",cls:this.cls+"-input"})]});if(this.length!=null){this.input.maxLength=this.length}c.bindValidate.apply(this,[]);EG.css(this.element,this.style)},setValue:function(e){EG.setValue(this.input,e)},getValue:function(){return EG.getValue(this.input)},render:function(){a.fit(this);a.fit({element:this.input,pSize:EG.getSize(this.element)})}}})})();(function(){EG.define("EG.ui.Password",["EG.ui.Item","EG.ui.FormItem"],function(a,c,b){return{alias:"password",extend:a,config:{length:null,cls:"eg_password",inputStyle:null},constructor:function(e){this.initItem(e);this.element=EG.CE({tn:"div",cls:this.cls,cn:[this.input=EG.CE({tn:"input",type:"password",cls:this.cls+"-input",item:this,style:EG.unnull(this.inputStyle,"")})]});c.bindValidate.apply(this,[]);if(this.length!=null){this.input.maxLength=this.length}},setValue:function(e){EG.setValue(this.input,e)},getValue:function(){return EG.getValue(this.input)},render:function(){a.fit(this);var e=EG.getSize(this.element);a.fit({element:this.input,pSize:e});EG.css(this.input,"line-height:"+EG.getSize(this.input).innerHeight+"px")}}})})();(function(){EG.define("EG.ui.Label",["EG.ui.Item"],function(a,b){return{alias:"label",extend:a,config:{title:"",width:null,height:null,style:null,onclick:null,onclickSrc:null},constructor:function(c){this.initItem(c);this.element=EG.CE({tn:"div",innerHTML:this.title,item:this});if(this.onclick){EG.CE({ele:this.element,onclick:this.onclick,onclickSrc:this.onclickSrc})}a.setWidth(this.element,this.width);a.setHeight(this.element,this.height);EG.css(this.element,this.style);if(this.style){var e=EG.Style.parse(this.style);if(e["line-height"]!=null){this.render_lineHeight=e["line-height"]}}},setValue:function(c){if(typeof(c)=="undefined"){return}this.value=c;EG.setValue(this.element,c)},getValue:function(){return this.value},render:function(){a.fit(this);var c=EG.getSize(this.element);EG.css(this.element,"line-height:"+(this.render_lineHeight||c.innerHeight)+"px")}}})})();(function(){EG.define("EG.ui.Code",["EG.ui.Item"],function(a,b){return{alias:"code",extend:a,config:{mode:null,theme:"monokai"},constructor:function(c){this.callSuper([c])},build:function(){this.callSuper("build");ace.require("ace/ext/language_tools");this.editor=ace.edit(this.element);this.editor.setOptions({enableBasicAutocompletion:true,enableSnippets:true,enableLiveAutocompletion:true});this.editor.setTheme("ace/theme/"+this.theme);if(this.mode){this.editor.getSession().setMode("ace/mode/"+this.mode)}},setValue:function(c){if(c==null){c=""}this.editor.setValue(c)},getValue:function(){console.log(this.editor.getValue());return this.editor.getValue()},render:function(){a.fit(this)}}})})();(function(){EG.define("EG.ui.Select",["EG.ui.Item"],function(a,b){return{alias:"select",extend:a,config:{onchange:null,textvalues:[],cls:"eg_select",edit:false,onchangeOnbuild:false,multi:false,tabIndex:0,useEmpty:true},constructor:function(c){this.vs=[];this.callSuper([c])},build:function(){var c=this;var e=EG.clone(this.textvalues);this.textvalues=[];this.builded=false;this.onchangeEvents=[];if(this.onchange!=null){this.bindOnchange(this.onchange)}this.element=EG.CE({tn:"div",cls:this.cls,item:this,tabIndex:this.tabIndex,onmouseover:b._events.element.onmouseover,onmouseout:b._events.element.onmouseout,onkeyup:b._events.element.onkeyup,cn:[this.input=EG.CE({tn:"div",cls:this.cls+"-input",style:"overflow:hidden",item:this,cn:[this.iptText=EG.CE({tn:"input",cls:this.cls+"-iptText",style:"overflow:hidden"}),this.dArrow=EG.CE({tn:"div",cls:this.cls+"-arrow"})],onclick:b._events.input.onclick}),this.dOpts=EG.CE({tn:"div",cls:this.cls+"-opts"})]});if(!this.edit){this.iptText.readOnly=true}EG.hide(this.dOpts);this.iptText.value="";this.setOptions(e);this.builded=true},showOptions:function(){EG.show(this.dOpts);var e=this.getValue();var g=this.dOpts.childNodes;for(var f=0,c=g.length;f<c;f++){if(e===g[f].value){EG.setCls(g[f],["opt","opt-selected"],this.cls)}else{EG.setCls(g[f],"opt",this.cls)}}},setValue:function(j,h){if(this.multi){this.setValues(j,h);return}if(h==null){h=true}if(j==null){return}var f,k;for(var g=0,c=this.textvalues.length;g<c;g++){f=this.textvalues[g];if(f[1]===j){k=f[0];break}}if(k==null){this.iptText.value="";return}var e=this.getValue();this.iptText.value=k;this.iptText.v=j;if(e!==j&&h){if(!this.builded&&!this.onchangeOnbuild){return}this.doOnChange(j,e)}},refreshValues:function(f){var c=this.getValue();var g=this.dOpts.childNodes;EG.Array.clear(this.vs);var j=[];for(var e=0;e<g.length;e++){if(g[e].childNodes[0].checked){this.vs.push(g[e].value);j.push(g[e].childNodes[1].innerHTML)}}this.iptText.value=j.join(",");var h=this.getValue();if(f){if(!this.builded&&!this.onchangeOnbuild){return}this.doOnChange(h,c)}},setValues:function(m,g){var e=this.getValue();var k=this.dOpts.childNodes;var l=[];EG.Array.clear(this.vs);for(var c=0;c<m.length;c++){for(var f=0;f<k.length;f++){if(k[f].value==m[c]){var h=k[f].childNodes[0];h.checked=true;EG.setCls(h,["opt-b","opt-b-select"],this.cls);l.push(k[f].childNodes[1].innerHTML)}}this.vs.push(m[c])}this.iptText.value=l.join(",");if(g){if(!this.builded&&!this.onchangeOnbuild){return}this.doOnChange(this.vs,e)}},doOnChange:function(f,c){for(var e=0;e<this.onchangeEvents.length;e++){this.onchangeEvents[e].apply(this,[f,c])}},getValue:function(){if(this.multi){return this.vs}return this.iptText.v},getSelectedIdx:function(){var c=this.getValue();for(var e=0;e<this.textvalues.length;e++){if(this.textvalues[e][1]==c){return e}}return -1},setOption:function(c,e){this.textvalues[c]=e;EG.CE({ele:this.dOpts.childNodes[c],value:e[1],innerHTML:e[0]});if(this.getSelectedIdx()==c){this.iptText.value=e[0];this.iptText.v=e[1]}},setOptions:function(c,e){this.removeOptions();if(this.useEmpty){this.addOption(["请选择",""],false)}this.addOptions(c,false);if(this.multi){this.refreshValues(e)}else{if(this.textvalues&&this.textvalues.length>0){this.setValue(this.textvalues[0][1],e)}}this.render()},removeOptions:function(){EG.Array.clear(this.textvalues);EG.DOM.removeChilds(this.dOpts);this.setValue("",false)},bindOnchange:function(c){this.onchangeEvents.push(c)},addOptions:function(e,g){if(g==null){g=true}for(var f=0,c=e.length;f<c;f++){this.addOption(e[f],false)}if(g){if(this.textvalues&&this.textvalues.length>0){this.setValue(this.textvalues[0][1],true)}}},addOption:function(c,e){if(e==null){e=true}var f=EG.CE({pn:this.dOpts,tn:"div",cls:this.cls+"-opt",value:c[1],item:this,onmouseover:b._events.option.onmouseover,onmouseout:b._events.option.onmouseout,onclick:b._events.option.onclick});if(this.multi){EG.CE({pn:f,tn:"a",cls:this.cls+"-opt-b "+this.cls+"-opt-b-unselect"})}EG.CE({pn:f,tn:"div",innerHTML:c[0],style:EG.Style.c.dv,item:this});this.textvalues.push(c);if(e){this.setValue(c[1],true)}},removeOption:function(c,f){if(f==null){f=true}var h=this.getSelectedIdx();EG.Array.del(this.textvalues,c);this.dOpts.removeChild(this.dOpts.childNodes[c]);if(h==c){this.iptText.value="";if(this.textvalues.length==0){return}var g=Math.min(c,this.textvalues.length-1);var e=this.textvalues[g][1];this.setValue(e,f)}},getTextByValue:function(e){for(var c=0;c<this.textvalues.length;c++){if(this.textvalues[c][1]==e){return this.textvalues[c][0]}}return null},getText:function(c){return EG.getValue(this.iptText,{getText:true,ignoreEmpty:c})},getTitle:function(h,g,c){for(var f=0,e=this.textvalues.length;f<e;f++){if(this.textvalues[f][1]===h){return this.textvalues[f][0]}}return null},destroy:function(){},render:function(){a.fit(this);a.fit({element:this.input,pSize:EG.getSize(this.element)});var e=EG.getSize(this.input);EG.css(this.dOpts,"width:"+e.outerWidth+"px");var c=EG.getSize(this.dArrow);EG.css(this.dArrow,"line-height:"+e.innerHeight+"px;height:"+e.innerHeight+"px");var f=EG.getSize(this.input);a.fit({element:this.iptText,dSize:{width:e.innerWidth-c.outerWidth,height:"100%"},pSize:EG.getSize(this.input)});EG.css(this.iptText,"line-height:"+EG.getSize(this.iptText).innerHeight+"px;")},statics:{_events:{option:{onmouseover:function(){var c=this.item;EG.setCls(this,["opt","opt-over"],c.cls)},onmouseout:function(){var e=this.item;var c=e.getValue();if(this.value==c){EG.setCls(this,["opt","opt-selected"],e.cls)}else{EG.setCls(this,"opt",e.cls)}},onclick:function(h){var g=this.item;if(!g.multi){EG.hide(g.dOpts);g.setValue(this.value)}else{var f=this.childNodes[0];f.checked=!f.checked;EG.setCls(f,["opt-b",f.checked?"opt-b-select":"opt-b-unselect"],g.cls);var c=true;if(EG.Tools.isPressCtrl(h)){c=false}g.refreshValues(c)}EG.Event.stopPropagation(h)}},element:{onmouseout:function(f){var c=this.item;if(c.outThread!=null){return}c.outThread=setTimeout(function(){EG.hide(c.dOpts)},10);EG.Event.stopPropagation(f)},onmouseover:function(f){var c=this.item;if(c.outThread!=null){clearTimeout(c.outThread);c.outThread=null}EG.Event.stopPropagation(f)},onkeyup:function(f){var c=this.item;if(EG.Tools.isPressCtrl(f)){c.refreshValues(true)}}},input:{onclick:function(){var c=this.item;if(EG.Style.isHide(c.dOpts)){c.showOptions()}else{EG.hide(c.dOpts)}}}}}}})})();(function(){EG.define("EG.ui.SelectArea",["EG.ui.Item","EG.ui.Button"],function(b,a,c){return{alias:"selectArea",extend:b,config:{onchange:null,textvalues:[],cls:"eg_selectArea",edit:false},constructor:function(e){this.callSuper([e]);if(this.textvalues){this.setTextvalues(this.textvalues)}},build:function(){var e=this;this.element=EG.CE({tn:"div",cls:this.cls,cn:[this.srcSlt=EG.CE({tn:"div",cls:this.cls+"-slts"}),this.dMid=EG.CE({tn:"div",cls:this.cls+"-dMid",cn:[new a({text:"添加 &gt;",click:function(){e.move(true)},style:"display:block;margin:10px;"}),new a({text:"&lt; 删除",click:function(){e.move()},style:"display:block;margin:10px;"})],style:"width:80px;"}),this.destSlt=EG.CE({tn:"div",cls:this.cls+"-slts"})]});EG.Event.bindUnselect(this.element)},setTextvalues:function(e){EG.DOM.removeChilds(this.srcSlt);EG.DOM.removeChilds(this.destSlt);this.textvalues=e;this.addOptions(this.srcSlt,this.textvalues)},move:function(j){var o=this.destSlt;var m=this.srcSlt;if(j){o=this.srcSlt;m=this.destSlt}var l=this._getValues(o).concat(this._getValues(m,true));var e=this._getTextvalues(m);var n=[],k=[];for(var g=0;g<this.textvalues.length;g++){var h=this.textvalues[g];if(EG.Array.has(l,h[1])){n.push(h)}else{k.push(h)}}EG.DOM.removeChilds(o);EG.DOM.removeChilds(m);this.addOptions(m,n);this.addOptions(o,k);if(this.onchange){this.onchange.apply(this,[l,this.getValue()])}},_getValues:function(h,g){var f=h.childNodes;var j=[];for(var e=0;e<f.length;e++){if(g||f[e].selected){j.push(f[e].value)}}return j},_selectOpt:function(f,e){if(e==null){e=true}f.selected=e;f.className=e?this.cls+"-slted":this.cls+"-unslt"},_getIdx:function(g){var f=g.paren.childNodes;for(var e=0;e<f.length;e++){if(f[e]==cn){return e}}throw new Error("未找到索引")},addOptions:function(h,j){var g=this;for(var f=0;f<j.length;f++){var e=j[f];EG.CE({pn:h,tn:"div",innerHTML:e[0],cls:this.cls+"-unslt",value:e[1],onclick:function(o){o=EG.Event.getEvent(o);if(o.shiftKey){if(h.lastIdx!=null){var k=EG.DOM.getIdx(this);var m=Math.min(h.lastIdx,k),p=Math.max(h.lastIdx,k);var n=this.parentNode.childNodes;for(var l=0;l<n.length;l++){g._selectOpt(n[l],false)}for(var l=m;l<=p;l++){g._selectOpt(n[l],true)}}else{h.lastIdx=EG.DOM.getIdx(this);g._selectOpt(this,!this.selected)}}else{if(!o.ctrlKey){var n=this.parentNode.childNodes;for(var l=0;l<n.length;l++){g._selectOpt(n[l],false)}}h.lastIdx=EG.DOM.getIdx(this);g._selectOpt(this,!this.selected)}}})}},clear:function(){EG.DOM.removeChilds(this.destSlt);EG.DOM.removeChilds(this.srcSlt)},clearSourceOptions:function(){EG.DOM.removeChilds(this.srcSlt)},clearSelectedOptions:function(){EG.DOM.removeChilds(this.destSlt)},addSourceOptions:function(e){this.addOptions(this.srcSlt,e)},addSelectedOptions:function(e){this.addOptions(this.destSlt,e)},reset:function(){EG.DOM.removeChilds(this.srcSlt);EG.DOM.removeChilds(this.destSlt);this.addOptions(this.srcSlt,this.textvalues)},setValue:function(g){this.reset();var f=this.srcSlt.childNodes;for(var e=0;e<f.length;e++){if(EG.Array.has(g,f[e].value)){this._selectOpt(f[e],true)}}this.move(true)},getValue:function(){return this._getValues(this.destSlt,true)},getText:function(){var g=[];var f=this.destSlt.childNodes;for(var e=0;e<f.length;e++){g.push(f[e].innerHTML)}return g},_getTextvalues:function(g){var h=[];var f=g.childNodes;for(var e=0;e<f.length;e++){h.push([f[e].innerHTML,f[e].value])}return h},getSelectedTextvalues:function(){return this._getTextvalues(this.destSlt)},render:function(){b.fit(this);var f=EG.getSize(this.element);var g=EG.getSize(this.dMid).outerWidth;var e=(f.innerWidth-g)/2;b.fit({element:this.dMid,dSize:{height:"100%"},pSize:f});var h=EG.getSize(this.srcSlt);b.fit({element:this.srcSlt,dSize:{width:e,height:"100%"},pSize:f});b.fit({element:this.destSlt,dSize:{width:e,height:"100%"},pSize:f})},getTitle:function(n,m,e){if(n==null){n=[]}var h=this.textvalues;var l="";for(var k=0,f=h.length;k<f;k++){for(var g=0;g<n.length;g++){if(h[k][1]==n[g]){l+=(l!=""?",":"")+h[k][0];break}}}return l}}})})();(function(){EG.define("EG.ui.SelectExpand",["EG.ui.Item"],function(a,b){return{extend:a,config:{onchange:null,textvalues:[],cls:"eg_selectExpand",edit:false,onchangeOnbuild:false,multi:false,useEmpty:false},constructor:function(c){this.callSuper([c])},build:function(){var c=this;var e=EG.clone(this.textvalues);this.textvalues=[];this.builded=false;this.onchangeEvents=[];if(this.onchange!=null){this.bindOnchange(this.onchange)}this.element=EG.CE({tn:"div",cls:this.cls,item:this,cn:[this.dOpts=EG.CE({tn:"div",cls:this.cls+"-opts"})]});this.setOptions(e);this.builded=true},setValue:function(k,g){if(k===null){return}if(g==null){g=true}var e=this.getValue();var j=this.dOpts.childNodes;for(var f=0,c=j.length;f<c;f++){var h=this.multi?EG.Array.has(k,j[f].value):k===j[f].value;this.selectOpt(j[f],h)}if(e!==k&&g){if(!this.builded&&!this.onchangeOnbuild){return}this.doOnChange(k,e)}},refreshValues:function(f){var c=this.getValue();var g=this.dOpts.childNodes;EG.Array.clear(this.vs);var j=[];for(var e=0;e<g.length;e++){if(g[e].childNodes[0].checked){this.vs.push(g[e].value);j.push(g[e].childNodes[1].innerHTML)}}this.iptText.value=j.join(",");var h=this.getValue();if(f){if(!this.builded&&!this.onchangeOnBuild){return}this.doOnChange(h,c)}},selectOpt:function(c,e){if(e){EG.Style.addCls(c,this.cls+"-opt-selected");c.selected=true}else{c.selected=false;EG.Style.removeCls(c,this.cls+"-opt-selected")}},doOnChange:function(f,c){for(var e=0;e<this.onchangeEvents.length;e++){this.onchangeEvents[e].apply(this,[f,c])}},getValue:function(){var f=[];var e=this.dOpts.childNodes;for(var c=0;c<e.length;c++){if(e[c].selected){f.push(e[c].value)}}return this.multi?f:(f.length>0?f[0]:null)},getSelectedIdx:function(){var e=this.getValue();if(this.multi){var c=[];for(var f=0;f<e.length;f++){for(var g=0;g<this.textvalues.length;g++){if(this.textvalues[g][1]===e[f]){c.push(g)}}}return c}else{for(var g=0;g<this.textvalues.length;g++){if(this.textvalues[g][1]===e){return g}}return -1}},setOption:function(c,e){this.textvalues[c]=e;EG.CE({ele:this.dOpts.childNodes[c],value:e[1],innerHTML:e[0]})},setOptions:function(c,e){this.removeOptions();if(this.useEmpty){this.addOption(["全部",""],false)}this.addOptions(c,false);if(this.multi){this.refreshValues(e)}else{if(this.textvalues&&this.textvalues.length>0){this.setValue(this.textvalues[0][1],e)}}this.render()},removeOptions:function(){EG.Array.clear(this.textvalues);EG.DOM.removeChilds(this.dOpts);this.setValue("",false)},bindOnchange:function(c){this.onchangeEvents.push(c)},addOptions:function(e,g){if(g==null){g=true}for(var f=0,c=e.length;f<c;f++){this.addOption(e[f],false)}if(g){if(this.textvalues&&this.textvalues.length>0){this.setValue(this.textvalues[0][1],true)}}},addOption:function(c,e){if(e==null){e=true}var f=EG.CE({pn:this.dOpts,tn:"div",cls:this.cls+"-opt",style:EG.Style.c.dv,value:c[1],innerHTML:c[0],item:this,onmouseover:b._events.option.onmouseover,onmouseout:b._events.option.onmouseout,onclick:b._events.option.onclick});this.textvalues.push(c);if(e){this.setValue(c[1],true)}},removeOption:function(c,f){if(f==null){f=true}var h=this.getSelectedIdx();EG.Array.del(this.textvalues,c);this.dOpts.removeChild(this.dOpts.childNodes[c]);if(h==c){var g=Math.min(c,this.textvalues.length-1);var e=this.textvalues[g][1];this.setValue(e,f)}},getTextByValue:function(e){for(var c=0;c<this.textvalues.length;c++){if(this.textvalues[c][1]==e){return this.textvalues[c][0]}}return null},getText:function(c){return EG.getValue(this.iptText,{getText:true,ignoreEmpty:c})},getTitle:function(h,g,c){for(var f=0,e=this.textvalues.length;f<e;f++){if(this.textvalues[f][1]===h){return this.textvalues[f][0]}}return null},destroy:function(){},render:function(){a.fit(this);a.fit({element:this.dOpts,pSize:EG.getSize(this.element)});var c=EG.getSize(this.dOpts);var g=this.dOpts.childNodes;for(var f=0;f<g.length;f++){var e=g[f];a.fit({element:e,pSize:c,dSize:{height:"100%"}});var h=EG.getSize(e);EG.css(e,"line-height:"+h.innerHeight+"px")}},statics:{_events:{option:{onmouseover:function(){var c=this.item;EG.Style.setCls(this,["opt","opt-over"],c.cls)},onmouseout:function(){var c=this.item;EG.Style.removeCls(this,c.cls+"-opt-over");if(this.selected){EG.Style.addCls(this,c.cls+"-opt-selected")}},onclick:function(j){var h=this.item;var c=h.getValue();if(!h.multi){var g=h.dOpts.childNodes;for(var f=0;f<g.length;f++){h.selectOpt(g[f],g[f]==this)}}else{h.selectOpt(this,!this.selected)}if(!EG.Tools.isPressCtrl(j)){h.doOnChange(h.getValue(),c)}EG.Event.stopPropagation(j)}}}}}})})();(function(){EG.define("EG.ui.Grid",["EG.ui.Item"],function(Item,ME){return{alias:"grid",extend:Item,config:{cls:"eg_grid",rowClasses:["row-a","row-b"],changeRowClassAble:true,selectRowOnclick:true,selectSingleOnclick:false,boxAble:true,seqAble:true,colSelectAble:false,colOrderAble:false,colAdjAble:false,gridFixed:false,columns:null,rowEvents:{},colEvents:{},remotingCallback:null,showHead:true,showFoot:true,renderTo:null,pageSize:30,toolbar:"first pre stat next last size",cellInnerStyle:null,onOrder:null},constructor:function(cfg){ME.load();this.callSuper([cfg])},initItem:function(cfg){this.callSuper("initItem",[cfg]);this._currentPage=0;this._dataSize=0;this._pageCount=0;this._segment=-1;this.colOptAble=false},_ef_row:{changeRow_mouseover:function(e){this.grid.overRow(this,true);if(this.grid.fn_row_mouseover){this.grid.fn_row_mouseover.apply(this,[e])}},changeRow_mouseout:function(e){this.grid.overRow(this,false);if(this.grid.fn_row_mouseout){this.grid.fn_row_mouseout.apply(this,[e])}},selectRow_click:function(e){e=EG.Event.getEvent(e);var cns=this.parentNode.childNodes;if(e.shiftKey&&this.grid.lastIdx!=null){var sIdx=Math.min(this.grid.lastIdx,this.index),bIdx=Math.max(this.grid.lastIdx,this.index);cns=this.parentNode.childNodes;for(var i=0;i<cns.length;i++){this.grid.selectRow(cns[i],false)}for(var i=sIdx;i<=bIdx;i++){this.grid.selectRow(cns[i],true)}}else{this.grid.lastIdx=this.selected?null:this.index;if(!e.ctrlKey){for(var i=0;i<cns.length;i++){if(cns[i]!=this){this.grid.selectRow(cns[i],false)}}}this.grid.selectRow(this,!this.selected)}if(this.grid.clickFn){this.grid.clickFn.apply(this,[e])}}},build:function(){this.element=EG.CE({tn:"div",cn:[this.dMain=EG.CE({tn:"div",style:"width:100%;overflow:hidden;position:relative;"}),this.dFoot=EG.CE({tn:"div",cls:this.cls+"-foot"})],onkeydown:function(){},onkeyup:function(){}});EG.Event.bindUnselect(this.element);this.buildHead();this.buildBody();this.buildFixBody();this.buildFixHead();this.initHead();this.buildToolBar();if(!this.showHead){EG.hide(this.dHead,this.dFixHead)}if(!this.showFoot){EG.hide(this.dFoot)}if(this.changeRowClassAble){this.fn_row_mouseover=this.rowEvents.onmouseover;this.fn_row_mouseout=this.rowEvents.onmouseout;this.rowEvents.onmouseover=(this._ef_row.changeRow_mouseover);this.rowEvents.onmouseout=(this._ef_row.changeRow_mouseout)}if(this.selectRowOnclick){this.clickFn=this.rowEvents.onclick;this.rowEvents.onclick=(this._ef_row.selectRow_click)}if(this.colAdjAble){this.colAdjRulerL=EG.CE({pn:this.element,tn:"div",cls:this.cls+"-adjRuler"});this.colAdjRulerR=EG.CE({pn:this.element,tn:"div",cls:this.cls+"-adjRuler"});EG.hide(this.colAdjRulerL,this.colAdjRulerR)}if(this.colOptAble){this.buildColOpt()}},_fn_selectBox:function(){this.grid.selectRow(this.row,!this.selected)},getTr:function(i){return this.tbBody.childNodes[i]},setData:function(data){var me=this;this.data=data;this.boxes=[];EG.DOM.removeChilds(this.tbBody);EG.DOM.removeChilds(this.tbFixBody);var globalRowNumber=0;var i,j,key;for(i=0;i<this.pageSize;i++){var d=this.data[i];if(!d&&!this.gridFixed){break}var row=EG.CE({pn:this.tbBody,tn:"tr",index:i,grid:this,data:d});var rowFix=EG.CE({pn:this.tbFixBody,tn:"tr",index:i,grid:this,data:d});for(key in me.rowEvents){EG.Event.bindEvent(row,key,me.rowEvents[key])}if(this.rowClasses&&this.rowClasses.length){var rowClassName=this.cls+"-"+this.rowClasses[i%this.pageSize%this.rowClasses.length];EG.setCls(row,rowClassName);EG.setCls(rowFix,rowClassName)}globalRowNumber=this._currentPage*this.pageSize+i;if(this.boxAble){var box=new EG.ui.Box({showText:false,row:row,grid:this,onclick:this._fn_selectBox});EG.CE({pn:rowFix,tn:"td",cls:(this.cls+"-fixCol"),style:"width:30px",cn:[{tn:"div",cls:this.cls+"-fixBodyCellInner",cn:[box]}]});row.box=box}if(this.seqAble){EG.CE({pn:rowFix,tn:"td",cls:this.cls+"-fixCol",style:"text-align:center;vertical-align:middle;width:30px",cn:[{tn:"div",cls:this.cls+"-fixBodyCellInner",innerHTML:(globalRowNumber+1)}]})}for(j=0;j<this.columns.length;j++){var column=this.columns[j];var textlength=column.textlength,textlengthEnd=column.textlengthEnd||"",handle=column.handle,field=column.field,fieldClass=column.fieldClass||"txtcenter",width=column.width,fix=column.fix,showCode=EG.n2d(column.showCode,false);var col,cellInner;col=EG.CE({tn:"td",cls:this.cls+"-bodyCol",cn:[cellInner=EG.CE({tn:"div",cls:this.cls+"-bodyCellInner"})]});if(this.cellInnerStyle){EG.css(cellInner,this.cellInnerStyle)}if(fieldClass){col.className+=(" "+fieldClass)}for(key in this.colEvents){EG.Event.bindEvent(col,key,this.colEvents[key])}var ihtml;if(d){ihtml=null;if(handle){var hr=handle.apply(this,[this.data[i],i,globalRowNumber,column]);if(hr==null){hr=""}if(hr.nodeType){cellInner.appendChild(hr)}else{if(typeof(hr)=="object"&&hr.length!=null){for(var x=0;x<hr.length;x++){if(EG.ui.Item.isItem(hr[x])){cellInner.appendChild(hr[x].getElement())}else{cellInner.appendChild(hr[x])}}}else{cellInner.innerHTML=showCode?EG.Tools.filterSpecChar(hr):hr}}}else{if(this.data[i][field]!=null||!isNaN(this.data[i][field])){ihtml=this.data[i][field];if(ihtml&&textlength!=null&&ihtml.length>textlength){ihtml=ihtml.substr(0,textlength)+textlengthEnd}cellInner.innerHTML=showCode?EG.Tools.filterSpecChar(ihtml):ihtml}else{cellInner.innerHTML=showCode?EG.Tools.filterSpecChar(field):""}}}else{if(this.gridFixed){cellInner.innerHTML="&nbsp;"}}if(fix){rowFix.appendChild(col);EG.setCls(col,"fixCol",this.cls);EG.setCls(cellInner,"fixBodyCellInner",this.cls)}else{row.appendChild(col)}if(width){EG.css(col,"width:"+(column.outerWidth)+"px");EG.css(cellInner,"width:"+(width)+"px")}}}this.fitFixSize()},buildHead:function(){this.dHead=EG.CE({pn:this.dMain,cls:this.cls+"-head",tn:"div",style:"overflow:hidden;",cn:[this.tHead=EG.CE({tn:"table",border:0,cellPadding:0,cellSpacing:0,style:"table-layout:fixed;",cn:[this.tbHead=EG.CE({tn:"tbody"})]})]})},buildBody:function(){var me=this;this.dBody=EG.CE({pn:this.dMain,cls:this.cls+"-body",tn:"div",style:"overflow:auto;",onscroll:function(){me.dHead.scrollLeft=this.scrollLeft;me.dFixBody.style.top=((-this.scrollTop)+me.dFixHead.clientHeight)+"px"},cn:[this.tBody=EG.CE({tn:"table",border:0,cellPadding:0,cellSpacing:0,style:"table-layout:fixed;",cn:[this.tbBody=EG.CE({tn:"tbody"})]})]})},buildFixHead:function(){this.dFixHead=EG.CE({pn:this.dMain,cls:this.cls+"-fixHead",tn:"div",style:"position:absolute;left:0px;top:0px",cn:[{tn:"table",style:"table-layout:fixed;",border:0,cellPadding:0,cellSpacing:0,cn:[this.tbFixHead=EG.CE({tn:"tbody"})]}]})},buildFixBody:function(){this.dFixBody=EG.CE({pn:this.dMain,cls:this.cls+"-fixBody",tn:"div",style:"position:absolute;overflow:hidden;left:0px;top:0px",cn:[{tn:"table",style:"table-layout:fixed;",border:0,cellPadding:0,cellSpacing:0,cn:[this.tbFixBody=EG.CE({tn:"tbody"})]}]})},render:function(){Item.fit(this);var size=EG.getSize(this.element);var mainHeadsize=EG.getSize(this.dHead);var footHeight=this.showFoot?EG.getSize(this.dFoot).innerHeight:0;var mainHeight=size.innerHeight-footHeight;var mainBodyHeight=mainHeight-mainHeadsize.outerHeight;var pSize=EG.getSize(this.element.parentNode);EG.css(this.dMain,"height:"+mainHeight+"px;width:"+size.innerWidth+"px");EG.css(this.dBody,"height:"+mainBodyHeight+"px");var tableWidth=0;for(var i=0;i<this.columns.length;i++){var column=this.columns[i];var width=column.width;EG.css(column.dHeadInner,"width:"+width+"px");var w=EG.getSize(column.dHeadInner).outerWidth;EG.css(column.tdHead,"width:"+w+"px");column.outerWidth=w;tableWidth+=w}EG.css(this.tHead,"width:"+(tableWidth+ME.appendHeadWidth)+"px");this.fitFixSize()},fitFixSize:function(){var i,il;if(this.tbFixBody.childNodes.length>0){var fixBodyTr=this.tbFixBody.childNodes[0];for(i=0,il=fixBodyTr.childNodes.length;i<il;i++){var cSize=EG.getSize(fixBodyTr.childNodes[i]);EG.css(this.tbFixHead.childNodes[0].childNodes[i],"width:"+cSize.innerWidth+"px")}for(i=0,il=this.tbBody.childNodes.length;i<il;i++){if(this.tbBody.childNodes[i].childNodes.length==0||this.tbFixBody.childNodes[i].childNodes.length==0){continue}var td=this.tbBody.childNodes[i].childNodes[0];var tdFix=this.tbFixBody.childNodes[i].childNodes[0];var s=EG.getSize(td);var sFix=EG.getSize(tdFix);if(sFix.innerHeight>s.innerHeight){EG.css(td,"height:"+sFix.innerHeight+"px")}else{EG.css(tdFix,"height:"+s.innerHeight+"px")}}}var fixHeadsize=EG.getSize(this.dFixHead);EG.css(this.dBody,"margin-left:"+fixHeadsize.outerWidth+"px");EG.css(this.dHead,"margin-left:"+fixHeadsize.outerWidth+"px");this.dFixBody.style.top=((-this.dBody.scrollTop)+this.dFixHead.clientHeight)+"px"},initHead:function(){var me=this;var hrFix=EG.CE({pn:this.tbFixHead,tn:"tr",cls:me.cls+"-head"});var hr=EG.CE({pn:this.tbHead,tn:"tr",cls:me.cls+"-head"});if(this.boxAble){EG.CE({pn:hrFix,tn:"td",cls:this.cls+"-headCol",style:"text-align:center;",cn:[{tn:"div",cls:this.cls+"-fixHeadCellInner",style:"",cn:[this.boxHead=new EG.ui.Box({showText:false,onselect:function(){me.selectAllBox(!this.selected)}})]}]})}if(this.seqAble){EG.CE({pn:hrFix,tn:"td",cls:this.cls+"-headCol",style:"text-align:center;",cn:[{tn:"div",cls:this.cls+"-fixHeadCellInner",style:"width:12px",innerHTML:"&nbsp;"}]})}for(var i=0;i<this.columns.length;i++){var column=this.columns[i];var header=column.header,headerEvent=column.headerEvent,headerStyle=column.headerStyle,width=column.width,fix=column.fix,order=column.order,field=column.field;var col=EG.CE({pn:fix?hrFix:hr,tn:"td",cls:me.cls+"-headCol",style:"white-space:nowrap",me:this});col.dContent=EG.CE({pn:col,tn:"div",cls:me.cls+"-headCellInner",me:this});if(typeof(header)=="string"){col.dContent.innerHTML=header}else{col.dContent.appendChild(header)}if(me.colOrderAble&&order){col.orderName=typeof(order)=="boolean"?field:order;EG.Event.bindEvent(col,"click",ME._events.head.onclick)}if(this.colAdjAble){col.dAdj=EG.CE({pn:col,tn:"div",innerHTML:"&nbsp;",className:me.cls+"-head_adj",ondblclick:function(){alert("待实现，双击调整宽度")},onmousedown:function(){me.startAdjColWidth(this.parentNode)}})}column.tdHead=col;column.dHeadInner=col.dContent}EG.CE({pn:hr,tn:"td",cls:this.cls+"-headCol",cn:[{tn:"div",cls:this.cls+"-headCellInner",style:"width:"+ME.appendHeadWidth+"px"}]})},overRow:function(tr,over){if(typeof(tr)=="number"){tr=this.tbBody.childNodes[tr]}if(tr.selected){return}if(over){if(!tr.oldClass){tr.oldClass=tr.className}EG.setCls(tr,"row-over",this.cls)}else{EG.setCls(tr,tr.oldClass)}},selectRow:function(tr,selected,single){if(single==null){single=this.selectSingleOnclick}if(typeof(tr)=="number"){tr=this.tbBody.childNodes[tr]}tr.selected=selected;if(tr.selected){if(!tr.oldClass){tr.oldClass=tr.className}EG.setCls(tr,"row-selected",this.cls)}else{EG.setCls(tr,tr.oldClass)}if(this.boxAble){tr.box.select(tr.selected)}if(single){var cns=tr.parentNode.childNodes;for(var i=0;i<cns.length;i++){if(cns[i]!=tr&&cns[i].selected){cns[i].selected=false;cns[i].box.select(false);EG.setCls(cns[i],cns[i].oldClass)}}}},selectAllBox:function(selected){var trs=this.tbBody.childNodes;for(var i=0,l=trs.length;i<l;i++){this.selectRow(trs[i],selected)}},getSelectIdx:function(){var sd=[];var trs=this.tbBody.childNodes;for(var i=0,l=trs.length;i<l;i++){if(trs[i].selected){sd.push(i)}}return sd},getSelectData:function(key){var sd=[];var trs=this.tbBody.childNodes;for(var i=0,l=trs.length;i<l;i++){if(trs[i].selected){var d=trs[i]["data"];sd.push(key?d[key]:d)}}return sd},setDataSize:function(dataSize){this._dataSize=dataSize;this._pageCount=Math.ceil(this._dataSize/this.pageSize);if(this._currentPage+1>this._pageCount){this._currentPage=this._pageCount-1}if(this._currentPage<0){this._currentPage=0}for(var i=0;i<ME.handler.gridChangedAction.length;i++){if(this[ME.handler.gridChangedAction[i]]&&this[ME.handler.gridChangedAction[i]] instanceof Function){this[ME.handler.gridChangedAction[i]]()}}},fixColumn:function(colIdx){if(typeof(colIdx)=="string"){colIdx=this.getColumnIdx(colIdx)}var cns=this.tbBody.childNodes;for(var i=cns.length-1;i>=0;i++){EG.CE({pn:this.tbFixBody,tn:"tr",cn:cns[i].childNodes[colIdx]})}EG.CE({pn:this.tbFixHead,tn:"tr",cn:this.tbHead.childNodes[0].childNodes[colIdx]})},getColumnIdx:function(header){var startIdx=0;if(this.boxAble){startIdx++}if(this.seqAble){startIdx++}for(var i=0;i<this.columns.length;i++){if(header==this.columns[i]["header"]){return i+startIdx}}throw new Error("EG.ui.Grid#getColumnIdx:未找到对应列")},getData:function(){return this.data},go:function(pageIdx){if(pageIdx>this._pageCount-1){pageIdx=this._pageCount-1}if(pageIdx<0){pageIdx=0}this._currentPage=pageIdx;if(this.remotingCallback){this.remotingCallback.apply(this["remotingCallbackSrc"]||this,[pageIdx,this])}if(this.boxAble){this.boxHead.select(false,true)}},prePage:function(){this.go(this._currentPage-1)},nextPage:function(){this.go(this._currentPage+1)},firstPage:function(){this.go(0)},lastPage:function(){this.go(this._pageCount-1)},curPage:function(){this.go(this._currentPage)},buildColOpt:function(){this.dColOtp=EG.CE({pn:EG.getBody(),tn:"div",cls:"pagingGrid_dColOpt",style:"display:none",onmouseleave:function(){EG.hide(this)}});var overFn=function(){this.style.backgroundColor="white"};var outFn=function(){this.style.backgroundColor=""};if(this.colOrderAble){EG.CE({pn:this.dColOtp,tn:"div",innerHTML:"正序",cls:"ele",onclick:function(){},onmouseover:overFn,onmouseout:outFn});EG.CE({pn:this.dColOtp,tn:"div",innerHTML:"倒序",cls:"ele",onclick:function(){},onmouseover:overFn,onmouseout:outFn})}if(this.colSelectAble){EG.CE({pn:this.dColOtp,tn:"div",grid:this,innerHTML:"列",cls:"ele",onmouseenter:function(){var p=EG.Tools.getElementPos(this);EG.css(this.grid.dColSelect,{top:p.y+"px",left:(p.x+this.grid.dColOtp.clientWidth-20)+"px",display:""})},onmouseover:overFn,onmouseout:outFn});this.dColSelect=EG.CE({pn:EG.getBody(),tn:"div",cls:"pagingGrid_dColSelect",style:"display:none",onmouseleave:function(){EG.hide(this)}});for(var i=0,l=this.columns.length;i<l;i++){var column=this.columns[i];var b=new EG.ui.Box({title:column.header,onselect:function(){}});this.dColSelect.appendChild(b.getElement())}}},buildToolBar:function(){var tools=this.toolbar.split(/\s+/);for(var i=0;i<tools.length;i++){var method=ME.handler.toolsMap[tools[i]];var tool;if(!method){continue}tool=eval("this."+method+"()");this.dFoot.appendChild(tool)}},getOptions:function(){return EG.CE({tn:"a",href:"javascript:void(0)",innerHTML:"选择",onclick:function(){alert("1")}})},getFirstPage:function(){var me=this;return EG.CE({tn:"a",href:"javascript:void(0)",cls:this.cls+"-firstPage",onclick:function(){me.firstPage();return false}})},getPrePage:function(){var me=this;return EG.CE({tn:"a",href:"javascript:void(0)",cls:this.cls+"-prePage",onclick:function(){me.prePage();return false}})},getNextPage:function(){var me=this;return EG.CE({tn:"a",href:"javascript:void(0)",cls:this.cls+"-nextPage",onclick:function(){me.nextPage();return false}})},getLastPage:function(){var me=this;return EG.CE({tn:"a",href:"javascript:void(0)",cls:this.cls+"-lastPage",onclick:function(){me.lastPage();return false}})},getState:function(){var me=this;var span=EG.CE({tn:"span",cls:this.cls+"-state",cn:[{tn:"a",cls:this.cls+"-gotoPage",onclick:function(){me.go(parseInt(me.currentPageValue.value)-1)}},{tn:"span",innerHTML:"第"},this.currentPageValue=EG.CE({tn:"input",type:"text",size:2,style:""}),{tn:"span",innerHTML:"/"},this.pageCountValue=EG.CE({tn:"span",innerHTML:this._pageCount}),{tn:"span",innerHTML:"页"}],style:""});return span},getRecordSize:function(){var me=this;return EG.CE({tn:"span",cls:this.cls+"-recordSize",cn:[{tn:"span",innerHTML:"每页"},this.sPageSize=EG.CE({tn:"span",innerHTML:this.pageSize,onclick:function(e){e=EG.Event.getEvent(e);if(e.ctrlKey){var ps=prompt("RS");if(ps){me.pageSize=parseInt(ps);me.go(0)}}}}),{tn:"span",innerHTML:"条",style:"margin-right:5px"},{tn:"span",innerHTML:"共"},this.sizeValue=EG.CE({tn:"span",innerHTML:this._dataSize}),{tn:"span",innerHTML:"条"}]})},_changeState:function(){EG.setValue(this.currentPageValue,this._currentPage+1);this.pageCountValue.innerHTML=this._pageCount;this.sizeValue.innerHTML=this._dataSize;this.sPageSize.innerHTML=this.pageSize},dispose:function(){for(var p in this){if(this[p] instanceof Function){this[p]=function(){alert("The object has been released.")}}else{this[p]=null}}},startAdjColWidth:function(td){ME.adjAim=td;var me=td.me;var p=EG.Tools.getElementPos(ME.adjAim,me.getElement(),false);var h=(me.dHead.clientHeight+me.dBody.clientHeight)+"px";EG.css(me.colAdjRulerL,{top:p.y+"px",left:p.x+"px",height:h});EG.css(me.colAdjRulerR,{top:p.y+"px",left:(p.x+td.offsetWidth)+"px",height:h});EG.show(me.colAdjRulerL,me.colAdjRulerR);ME.adjIng=true},endAdjColWidth:function(){EG.hide(ME.colAdjRulerL,ME.colAdjRulerR);ME.adjAim.style.width=parseInt(EG.String.removeEnd(ME.colAdjRulerR.style.left,"px"))-parseInt(EG.String.removeEnd(ME.colAdjRulerL.style.left,"px"))+"px";ME.adjIng=false},statics:{_events:{head:{onclick:function(){var me=this.me;if(me.curOrderCol&&me.curOrderCol!=this){EG.Style.removeCls(me.curOrderCol.dContent,me.cls+"-head_order_asc");EG.Style.removeCls(me.curOrderCol.dContent,me.cls+"-head_order_desc");me.curOrderDesc="";me.curOrderName="";me.curOrderCol=null}me.curOrderCol=this;me.curOrderName=this.orderName;if(me.curOrderDesc=="desc"){me.curOrderDesc="asc"}else{if(me.curOrderDesc=="asc"){me.curOrderDesc="";me.curOrderName="";me.curOrderCol=null}else{me.curOrderDesc="desc"}}EG.Style.removeCls(this.dContent,me.cls+"-head_order_asc");EG.Style.removeCls(this.dContent,me.cls+"-head_order_desc");if(me.curOrderDesc){EG.Style.addCls(this.dContent,me.cls+"-head_order_"+me.curOrderDesc)}if(me.onOrder){me.onOrder.apply(me.onOrderSrc||me,[this.orderName,me.curOrderDesc])}}}},loaded:false,load:function(){if(ME.loaded){return}ME.loaded=true},handler:{grids:[],count:0,toolsMap:{option:"getOptions",pre:"getPrePage",next:"getNextPage",first:"getFirstPage",last:"getLastPage",stat:"getState",size:"getRecordSize",skip:"getSkip"},gridChangedAction:["_changeState"]},adjAim:null,adjIng:false,appendHeadWidth:1000}}})})();EG.define("EG.ui.InputGrid",["EG.ui.Item"],function(a,b){return{extend:a,alias:"inputGrid",config:{cls:"eg_inputgrid",columnsConfig:null,afterRowDelete:null,afterRowAdd:null,seqAble:true,addAble:true,readLineHeight:22,onClickAdd:null,onClickDel:null,seq_ig_name:null,onvalidate:null},constructor:function(c){this.setPropValueOnRead=true;this.callSuper([c])},build:function(){if(this.seqAble){EG.Array.insert(this.columnsConfig,0,{ig_name:(this.seq_ig_name||"idx"),ig_title:"序号",xtype:"label",width:100,height:30})}this.element=EG.CE({tn:"div"});if(this.addAble){this.element.appendChild(this.dBar=EG.CE({tn:"div",cn:[new EG.ui.Button({text:"添加",click:this.onClickAdd||this.addRow,clickSrc:this}),new EG.ui.Button({text:"删除",click:this.onClickDel||this.deleteRow,clickSrc:this})]}))}this.element.appendChild(this.dMain=EG.CE({tn:"div",cls:this.cls+"-main",cn:[this.table=EG.CE({tn:"table",cn:[this.thead=EG.CE({tn:"thead",cn:[{tn:"tr",cls:this.cls+"-head"}]}),this.tbody=EG.CE({tn:"tbody"}),this.tfoot=EG.CE({tn:"tfoot"})]})]}));this.buildHead()},buildHead:function(){var e=this.thead.childNodes[0];for(var f=0;f<this.columnsConfig.length;f++){var c=this.columnsConfig[f];var g=!!c.hidden;EG.CE({pn:e,tn:"td",innerHTML:g?"":c.ig_title,style:"width:"+c.width+"px"})}},addRow:function(){var l=this;var n=this.cls+"-row";var g=this.cls+"-rowSelected";var k=EG.CE({pn:this.tbody,tn:"tr",cls:n,onclick:function(){if(l.selectedRow!=null){EG.Style.removeCls(l.selectedRow,g)}l.selectedRow=this;EG.Style.addCls(this,g)}});for(var f=0;f<this.columnsConfig.length;f++){var c=this.columnsConfig[f];var j=c.typeCfg||c;var h=!!j.hidden;var m=c.xtype instanceof Function?new c.xtype(j):a.create(c.xtype,j);var e=EG.CE({pn:k,tn:"td",cn:[m]});if(h){EG.css(e,"overflow:hidden")}e.item=m;e.ig_name=c.ig_name;m.hidden=h;m.pItem=this;m.width=c.width;m.height=c.height;m.render();EG.css(m,"position:relative")}if(this.afterRowAdd){this.afterRowAdd.apply(this,[k,this])}if(this.seqAble){this.refreshSeq()}return k},getCellPos:function(h){var c=this.tbody.childNodes;for(var f=0;f<c.length;f++){var g=c[f].childNodes;for(var e=0;e<g.length;e++){var k=g[e];if(k.item==h){return[f,e]}}}return null},refreshSeq:function(){var c=this.tbody.childNodes;for(var e=0;e<c.length;e++){var f=c[e].childNodes[0];f.item.setValue(e+1)}},deleteRow:function(){if(!this.selectedRow){return}EG.DOM.remove(this.selectedRow);if(this.afterRowDelete){this.afterRowDelete.apply(this,[this.selectedRow,this])}if(this.seqAble){this.refreshSeq()}this.selectedRow=null},setValue:function(l,g){if(l==null){return}EG.DOM.removeChilds(this.tbody);var k=l;for(var f=0;f<k.length;f++){var n=k[f];var h=this.addRow();var e=h.childNodes;for(var c=0;c<e.length;c++){var m=e[c].item;m.setValue(n[e[c].ig_name],n)}}},setRowValue:function(g,k){var h;if(typeof(g)!="number"){h=g.parentNode.parentNode;g=EG.DOM.getIdx(h)}h=this.tbody.childNodes[g];var e=h.childNodes;var l={};for(var c=0;c<e.length;c++){var f=e[c].item;f.setValue(k[e[c].ig_name])}},getRowValue:function(g){var h;if(typeof(g)!="number"){h=g.parentNode.parentNode;g=EG.DOM.getIdx(h)}h=this.tbody.childNodes[g];var e=h.childNodes;var k={};for(var c=0;c<e.length;c++){var f=e[c].item;k[e[c].ig_name]=f.getValue()}return k},getValue:function(){var c=this.tbody.childNodes;var l=[];for(var f=0;f<c.length;f++){var g=c[f].childNodes;var k={};for(var e=0;e<g.length;e++){var h=g[e].item;k[g[e].ig_name]=h.getValue()}l.push(k)}return l},removeAll:function(){EG.DOM.removeAllRows(this.tbody)},getColumnItem:function(e,h){if(typeof(e)!="number"){var f=e.parentNode.parentNode;e=EG.DOM.getIdx(f)}if(typeof(h)=="string"){for(var c=0;c<this.columnsConfig.length;c++){var g=this.columnsConfig[c];if(g.ig_name==h){h=c;break}}if(typeof(h)=="string"){throw new Error("未找到对应列:"+h)}}return this.tbody.childNodes[e].childNodes[h].item},render:function(){a.fit(this);var g=EG.getSize(this.element);var k=this.addAble?EG.getSize(this.dBar):{outerHeight:0};var m=g.innerHeight-k.outerHeight;EG.css(this.dMain,"height:"+m+"px");var c=this.tbody.childNodes;for(var f=0;f<c.length;f++){var h=c[f].childNodes;for(var e=0;e<h.length;e++){var l=h[e].item;l.render();EG.css(l,"position:relative")}}},getTitle:function(q,k){var f,g;if(!q){return""}var e=this.columnsConfig;var u=EG.CE({tn:"table",cls:"eg_inputgrid-read"});var m=EG.CE({pn:u,tn:"thead"});var l=EG.CE({pn:u,tn:"tbody"});var p=EG.CE({pn:m,tn:"tr"});for(g=0;g<e.length;g++){f=e[g];EG.CE({pn:p,tn:"td",width:f.width,innerHTML:f.ig_title})}for(var h=0;h<q.length;h++){var s=q[h];var o=EG.CE({pn:l,tn:"tr"});for(g=0;g<e.length;g++){f=e[g];var n=f.xtype||f.type;var t=this.tbody.childNodes[h].childNodes[g].item;var c=(t&&t.getTitle)?t.getTitle(s[f.ig_name],s):s[f.ig_name];if(c==null){c=""}if(this.seqAble){if(g==0){c=(h+1)}}if(EG.DOM.isElement(c)){EG.CE({pn:o,tn:"td",cn:[c]})}else{EG.CE({pn:o,tn:"td",innerHTML:c})}}}return u},validate:function(){var l=this.getValue();if(this.unnull){if(!l||l.length==0){return"不能为空"}}if(l){for(var g=0;g<l.length;g++){var e=l[g];for(var f=0;f<this.columnsConfig.length;f++){var k=this.columnsConfig[f];if(k.unnull){var c=e[k.ig_name];if(c!==0&&!c){return k.ig_title+"不能为空"}}}}}if(this.onvalidate){var h=this.onvalidate(l);if(h){return h}}}}});(function(){EG.define("EG.ui.Form",["EG.ui.Container"],function(a,b){return{alias:"form",extend:a,config:{editable:true,labelWidth:40,validateAble:true,layout:"table",realForm:false,action:null,isUpload:false,target:null,useTopCls:false},build:function(){if(this.isUpload){this.realForm=true}this.element=EG.CE({tn:this.realForm?"form":"div"});if(this.isUpload){this.element.encoding="multipart/form-data"}if(this.realForm){this.element.method="POST"}},constructor:function(c){c=b.filterConfig(c,c);this.callSuper([c])},getFormItem:function(c){return EG.ui.Form.getFormItem(this,c)},getFormItems:function(){return EG.ui.Form.getFormItems(this)},_displayFormItems:function(f,e){for(var c=0;c<f.length;c++){var g=this.getFormItem(f[c]);if(g==null){continue}g.setHidden(!e)}this.render()},showFormItems:function(){this._displayFormItems(arguments,true)},hideFormItems:function(){this._displayFormItems(arguments,false)},getData:function(){var h={};var f=this.getFormItems();for(var e=0,c=f.length;e<c;e++){var g=f[e];var j=g.getProp();if(j&&j.onGetData){j.onGetData(h,g.name,this)}else{h[g.name]=g.getValue()}}return h},setData:function(h,g){if(h==null){throw new Error("EG.ui.Form.prototype#setData:data不能为null")}var f=this.getFormItems();if(!g){g={}}var l=EG.n2d(g.setDefVal,false);var j=EG.n2d(g.ignoreUnExist,false);for(var e=0,c=f.length;e<c;e++){var k=f[e];if(j&&!EG.Object.hasKey(h,k.name)){continue}k.setValue(h[k.name],h);if(l){k.defValue=h[k.name]}}},submit:function(){var e=this.getFormItems();for(var c=0;c<e.length;c++){var f=e[c];f.setSubmitValue(f.getValue())}this.element.action=this.action;if(this.target){this.element.target=this.target}this.element.submit()},reset:function(){var f=this.getFormItems();for(var e=0,c=f.length;e<c;e++){var g=f[e];if(g.defValue!=null){g.setValue(g.defValue)}else{g.setValue(null)}}},clearData:function(){var f=this.getFormItems();for(var e=0,c=f.length;e<c;e++){var g=f[e];g.setValue("",{})}},validate:function(j){var o=true;var l=this.getFormItems();var k=null;var f=null;var e=null;for(var g=0,m=l.length;g<m;g++){var p=l[g];if(p.hidden){continue}if(p.editable){var c=p.validate();o=(c&&o);if(!o){if(!e){e=p}var h=p;var n=null;while(k==null&&(h=h.pItem)!=null){if(h==this){break}if(h.xtype=="tabPanel"){k=h;f=h.getPanelIdx(n);e=p;break}n=h}if(!j){break}}}}if(k!=null){k.select(f)}if(e){EG.Tip.error(e.vErrorMsg,e.rDiv)}return o},setEditable:function(f){var g=this.getFormItems();for(var e=0,c=g.length;e<c;e++){var h=g[e];h.setEditable(f)}},statics:{getFormItem:function(c,f){for(var g=0,e=c.items.length;g<e;g++){var h=c.items[g];if(h instanceof EG.ui.FormItem){if(h.name==f){return h}}else{if(h.isContainer){var j=EG.ui.Form.getFormItem(h,f);if(j){return j}}}}return null},getFormItems:function(e,c){if(!c){c=[]}for(var g=0,f=e.items.length;g<f;g++){var h=e.items[g];if(h instanceof EG.ui.FormItem){c.push(h)}else{if(h.isContainer){EG.ui.Form.getFormItems(h,c)}}}return c},filterConfig:function(h,c){var l=c.labelWidth;var k=c.cls;var f=c.itemHeight;var e=h.xtype=="tabPanel";var j=h.items;if(!j){return h}for(var g=0;g<j.length;g++){var m=j[g];if(!EG.isLit(m)){continue}if(e){m.panel["xtype"]="panel";this.filterConfig(m.panel,c)}else{if(!m.xtype){m.xtype="formItem"}if(m.xtype=="formItem"){if(m.labelWidth==null&&l!=null){m.labelWidth=l}if(!m.cls&&k&&c.useTopCls){m.cls=k+"-item"}if(m.height==null&&f!=null){m.height=f}}else{b.filterConfig(m,c)}}}return h}}}})})();(function(){EG.define("EG.ui.Fieldset",["EG.ui.Container","EG.ui.Item"],function(a,b,c){return{alias:"fieldset",extend:a,config:{cls:"eg_fieldset",showTitle:true,showExpand:true,showBorder:true,layout:"table",bodyPadding:null,title:null,bodyStyle:null},constructor:function(e){var f=this;this.callSuper([e]);if(!this.showTitle){EG.Style.isHide(this.dTitle)}if(!this.showExpand){EG.Style.isHide(this.dCollapse)}if(!this.showBorder){EG.setCls(this.dBody,"dBody-noborder",f.cls)}if(this.bodyPadding!=null){EG.css(this.dBody,"padding:"+this.bodyPadding+"px")}},build:function(){this.element=EG.CE({tn:"fieldset",cls:this.cls,cn:[this.legend=EG.CE({tn:"legend",cls:this.cls+"-legend",cn:[this.dCollapse=EG.CE({tn:"div",cls:this.cls+"-dCollapse "+this.cls+"-dCollapse-show",onclick:function(){var f=EG.Style.isHide(me.dBody);var e=me.cls+"-dCollapse "+me.cls+"-dCollapse-";if(f){EG.show(me.dBody.getElement());EG.setCls(this,"hide",me.cls)}else{EG.hide(me.dBody.getElement());EG.setCls(this,"show",me.cls)}}}),this.dTitle=EG.CE({tn:"div",cls:this.cls+"-dTitle",innerHTML:this.title})]}),this.dBody=EG.CE({tn:"div",cls:this.cls+"-dBody"})]});if(this.bodyStyle){EG.css(this.dBody,this.bodyStyle)}},getItemContainer:function(){return this.dBody},setInnerHeight:function(e){b.pack({pEle:this.dBody,height:e});b.pack({pEle:this.element,height:EG.getSize(this.dBody).outerHeight+EG.getSize(this.legend).outerHeight})},render:function(){b.fit(this);var e=EG.getSize(this.element);b.fit({element:this.dBody,pSize:e,dSize:{width:"100%",height:e.innerHeight-EG.getSize(this.legend).outerHeight}});if(this.items.length>0){this.doLayout()}}}})})();(function(){EG.define("EG.ui.FormItem",["EG.ui.Item"],function(a,b){return{extend:a,alias:"formItem",config:{pos:null,editable:true,name:null,title:null,type:null,typeClass:null,pre:null,after:null,cls:"eg_form-item",labelWidth:60,width:"100%",height:28,validateConfig:null,unnull:false,defValue:null,typeCfg:null,showLeft:true,readLineHeight:22,readStyle:null,showCode:false,labelStyle:null,handle:null,readClick:null,setRead:null,vldType:null},constructor:function(c){this.cacheValue=null;this.prop=null;this.cfg=c;this.callSuper([c]);this.setEditable(this.editable,true);this.setValidate(this.validateConfig);if(this.defValue!=null){this.setValue(this.defValue)}},build:function(){var c=this;this.element=EG.CE({tn:"div",cls:this.cls,cn:[this.lDiv=EG.CE({tn:"div",cls:this.cls+"-dL",item:this,style:EG.unnull(this.labelStyle,""),cn:[this.dStar=EG.CE({tn:"div",cls:this.cls+"-star",item:this,innerHTML:" * "}),this.dTitle=EG.CE({tn:"div",cls:this.cls+"-title",item:this,innerHTML:this.title})]}),this.rDiv=EG.CE({tn:"div",cls:this.cls+"-dR",cn:[this.dPre=EG.CE({tn:"div",cls:this.cls+"-pre",item:this,style:EG.Style.c.dv}),this.dProp=EG.CE({tn:"div",cls:this.cls+"-prop",item:this,cn:[this.elementRead=EG.CE({tn:"div",item:this,cls:this.cls+"-read"})],style:EG.Style.c.dv}),this.dAfter=EG.CE({tn:"div",cls:this.cls+"-after",item:this,style:EG.Style.c.dv}),this.dError=EG.CE({tn:"div",cls:this.cls+"-error",item:this,style:EG.Style.c.dv})]})]});EG.hide(this.dPre,this.dAfter,this.dError);if(this.readStyle){EG.css(this.elementRead,this.readStyle)}if(this.readClick){this.elementRead.onclickSrc=this;EG.Event.bindEvent(this.elementRead,"onclick",this.readClick);EG.css(this.elementRead,"text-decoration:underline;color:blue")}if(this.type=="upload"){this.iptSub=EG.CE({pn:this.rDiv,tn:"input",type:"hidden",name:this.name,style:"display:none"})}if(this.after){this._renderAfter=false;if(typeof(this.after)=="string"){this.dAfter.innerHTML=this.after}else{if(EG.DOM.isElement(this.after)){this.dAfter.appendChild(this.after)}else{if(this.after.getElement){this.dAfter.appendChild(this.after.getElement());this._renderAfter=true}else{if(typeof(this.after)=="function"){this.after.apply(this,[this.dAfter])}}}}EG.show(this.dAfter)}if(this.pre){this._renderPre=false;if(typeof(this.pre)=="string"){this.dPre.innerHTML=this.pre}else{if(EG.DOM.isElement(this.pre)){this.dPre.appendChild(this.pre)}else{if(this.pre.getElement){this.dPre.appendChild(this.pre.getElement());this._renderPre=true}else{if(typeof(this.pre)=="function"){this.pre.apply(this,[this.dPre])}}}}EG.show(this.dPre)}this.readPropClass();this.buildProp();EG.bindEvent(this.rDiv,"mouseover",function(){if(c.vErrorMsg){EG.Tip.error(c.vErrorMsg,this)}});EG.bindEvent(this.rDiv,"mouseout",function(f){EG.Tip.close();EG.Event.stopPropagation(f)})},setSubmitValue:function(c){if(this.iptSub){this.iptSub.value=c}},render:function(){var h;a.fit(this);if(!this.unnull||!this.editable){EG.hide(this.dStar)}else{EG.show(this.dStar)}var e=EG.getSize(this.element);var g;if(this.showLeft){a.fit({element:this.lDiv,dSize:{width:this.labelWidth,height:"100%"},pSize:e});var j=EG.getSize(this.lDiv).innerHeight;EG.css(this.lDiv,"line-height:"+j+"px");EG.show(this.lDiv);g=EG.getSize(this.lDiv).outerWidth}else{EG.hide(this.lDiv);g=0}a.fit({element:this.rDiv,dSize:{width:e.innerWidth-g,height:"100%"},pSize:e});var f=EG.getSize(this.rDiv);var c=f.innerWidth;if(this.after){if(this._renderAfter){this.after.render()}h=EG.getSize(this.dAfter);c=c-h.outerWidth;EG.css(this.dAfter,"width:"+h.innerWidth+"px")}if(this.pre){if(this._renderPre){this.pre.render()}h=EG.getSize(this.dPre).outerWidth;c=c-h.outerWidth;EG.css(this.dPre,"width:"+h.innerWidth+"px")}a.fit({element:this.dProp,dSize:{width:c,height:"100%"},pSize:f});if(this.editable){if(this.prop&&this.prop.render){if(this.prop.width==null){this.prop.width=c}this.prop.height=f.innerHeight;this.prop.render()}}else{a.fit({element:this.elementRead,pSize:f});h=EG.getSize(this.elementRead);EG.css(this.elementRead,"line-height:"+(EG.unnull(this.readLineHeight,h.innerHeight))+"px");if(this.prop.afterRenderRead){this.prop.afterRenderRead()}}},renderOuter:function(){a.fit(this);this.saveOuterSize();this._outerSize=this.getSize()},setEditable:function(c,e){if(this.editable==c&&e!=true){return}this.editable=c;if(this.editable){EG.hide(this.elementRead);EG.show(this.propElement)}else{EG.hide(this.dStar,this.propElement);EG.show(this.elementRead);EG.Style.removeCls(this.lDiv,this.cls+"-error")}if(this.cacheValue||this.cacheData){this.setValue(this.cacheValue,this.cacheData)}this.render()},setUnnull:function(e,c){if(c==null){c=true}this.unnull=e;if(c){this.render()}},readPropClass:function(){if(!this.typeClass){if(typeof(this.type)=="string"){this.typeClass=EG._defaultLoader.find(this.type);if(!this.typeClass){var c="EG.ui."+EG.Word.first2Uppercase(this.type);this.typeClass=EG._defaultLoader.find(c)}}else{if(typeof(this.type)=="function"){this.typeClass=this.type}}}if(!this.typeClass){throw new Error("无法识别类型"+this.type)}},changeProp:function(e,c){if(typeof(e)=="function"){this.typeClass=e}else{this.typeClass=null}this.typeCfg=c;this.type=e;EG.DOM.remove(this.propElement);this.readPropClass();this.buildProp()},buildProp:function(){var c=this.typeCfg?this.typeCfg:this.cfg;c.formItem=this;c.unnull=this.unnull;this.prop=new this.typeClass(c);this.prop.formItem=this;this.propElement=this.prop.getElement();this.dProp.appendChild(this.propElement)},getForm:function(){var c=this.pItem;while(c&&!(c instanceof EG.ui.Form)){c=c.pItem}return(c instanceof EG.ui.Form)?c:null},setValue:function(g,f){g=this.setValueBefore(g,f);this.cacheData=f;this.cacheValue=g;var c=this.typeCfg||this.cfg;if(this.editable||this.prop.setPropValueOnRead){this.prop.setValue(this.cacheValue,f)}if(!this.editable){var e=this.prop.getTitle?this.prop.getTitle(g,f,c):g;if(this.setRead||this.prop.setRead){(this.setRead||this.prop.setRead).apply(this,[this.elementRead,g,f,c])}else{if(EG.DOM.isElement(e)){EG.DOM.removeChilds(this.elementRead);this.elementRead.appendChild(e)}else{EG.setValue(this.elementRead,e)}}}},setValueBefore:function(e,c){return e},getValue:function(){var c=null;if(this.editable){c=this.prop.getValue()}else{c=this.cacheValue}c=this.getValueAfter(c);return c},getValueAfter:function(c){return c},getProp:function(){if(this.prop){return EG.unnull(this.prop.prop||this.prop)}},setValidate:function(c){this.validateConfig=c},validate:function(){this.vErrorMsg=null;EG.Style.removeCls(this.lDiv,this.cls+"-error");if(this.prop.validate){var f=this.prop.validate();this.vErrorMsg=f?(this.title+f):null}else{var g=this.prop.getValue();if(g==null||(typeof(g)=="string"&&g==="")){if(this.unnull==true){this.vErrorMsg=this.title+"不能为空"}}else{if(!this.vErrorMsg&&typeof(g)=="string"){var c=this.minLength;if(c!=null&&g.length<c){this.vErrorMsg=this.title+"最小长度为"+c+"个字符"}if(!this.vErrorMsg){var e=this.vldType;if(e){if(typeof(e)=="string"){this.vErrorMsg=EG.Validate.$is(e,g)}else{if(typeof(e)=="function"){this.vErrorMsg=e(g)}else{if(!this.vldTypeObj){this.vldTypeObj=new e.type(e)}this.vErrorMsg=this.vldTypeObj.validate(g)}}}}}}}if(this.vErrorMsg){this.onError()}return !this.vErrorMsg},onError:function(){EG.Style.addCls(this.lDiv,this.cls+"-error");if(this.prop.onError){this.prop.onError()}},format:function(){if(!this.vldType||!this.vldType.type){return}if(!this.vldTypeObj){this.vldTypeObj=new this.vldType.type(this.vldType)}var c=this.vldTypeObj.format(this.getValue());this.setValue(c)},destroy:function(){if(this.prop&&this.prop.destroy){this.prop.destroy()}},onGetData:null,statics:{bindValidate:function(){var f=this;this.vError=false;var g=function(){if(f._tValidate){clearTimeout(f._tValidate);f._tValidate=null}f._tValidate=setTimeout(function(){if(f.formItem){f.formItem.format();f.formItem.validate()}},500)};var e=function(h){h=EG.Event.getEvent(h);if(37<=h.keyCode&&h.keyCode<=40){return}g()};var c=this.getClass()._className;if(EG.$in(c,["EG.ui.Text","EG.ui.Password","EG.ui.Textarea","EG.ui.Date"])){EG.bindEvent(this.input,"keyup",e);EG.bindEvent(this.input,"blur",e)}else{if(c=="EG.ui.Select"){this.bindOnchange(g);EG.bindEvent(this.prop.input,"blur",e)}else{if(c=="EG.ui.BoxGroup"){this.bindOnchange(g)}}}},validate:function(){var f=this.getForm();if(f&&!f.validateAble){return true}var g=this.prop.getValue();this.vError=false;this.v_msg="";if(EG.String.isEmpty(g)){if(this.unnull==true){this.vError=true;this.v_msg=this.formItem.title+"不能为空"}}else{var c=this.minLength;if(c!=null&&g.length<c){this.vError=true;this.v_msg=this.formItem.title+"最小长度为"+c+"个字符"}if(!this.vError){var e=this.vldType;if(e){if(typeof(e)=="string"){this.vError=!(EG.Validate.$is(e,g));if(this.vError){this.v_msg=this.formItem.title+"格式应为"+EG.Validate.getComment(e)}else{this.v_msg=""}}else{if(typeof(e)=="function"){this.vError=!(e(g))}else{if(!this.vldTypeObj){this.vldTypeObj=new e.type(e)}this.vError=!this.vldTypeObj.validate(g);if(this.vError){this.v_msg=this.vldTypeObj.getMessage()}}}}}}if(this.vError&&!this.v_msg){this.v_msg="请输入正确的"+this.formItem.title}if(this.onError){this.onError()}return !this.v_msg}}}})})();(function(){EG.define("EG.ui.form.Prop",["EG.ui.Item"],function(a,b){return{extend:a,getElement:function(){return this.prop.getElement()},setValue:function(e,c){this.prop.setValue(e,c)},getValue:function(){return this.prop.getValue()},getTitle:null,statics:{validate:function(){var f=this.getForm();if(f&&!f.validateAble){return true}var g=this.prop.getValue();this.vError=false;this.v_msg="";if(EG.String.isBlank(g)){if(this.unnull==true){this.vError=true;this.v_msg=this.formItem.title+"不能为空"}}else{var c=this.minLength;if(c!=null&&g.length<c){this.vError=true;this.v_msg=this.formItem.title+"最小长度为"+c+"个字符"}if(!this.vError){var e=this.vldType;if(e){if(typeof(e)=="string"){this.vError=!(EG.Validate.$is(e,g));if(this.vError){this.v_msg=this.formItem.title+"格式应为"+EG.Validate.getComment(e)}else{this.v_msg=""}}else{if(typeof(e)=="function"){this.vError=!(e(g))}else{if(!this.vldTypeObj){this.vldTypeObj=new e.type(e)}this.vError=!this.vldTypeObj.validate(g);if(this.vError){this.v_msg=this.vldTypeObj.getMessage()}}}}}}if(this.vError&&!this.v_msg){this.v_msg="请输入正确的"+this.formItem.title}if(this.onError){this.onError()}return !this.v_msg},onError:function(){if(this.vError){EG.setCls(this.prop.input,"error",this.prop.cls)}else{EG.setCls(this.prop.input,"input",this.prop.cls)}}}}})})();(function(){EG.define("EG.ui.form.prop.Box",["EG.ui.form.Prop","EG.ui.BoxGroup"],function(b,c,a){return{extend:b,config:{type:"boxGroup"},constructor:function(f){try{this.prop=new c(f)}catch(g){alert(g.message)}},validate:function(){var e=this.getForm();if(e&&!e.validateAble){return true}var f=this.prop.getValue();this.vError=false;if(typeof(f)=="number"){}else{if((this.prop.multiple&&f.length==0)||(!this.prop.multiple&&EG.String.isBlank(f))){if(this.unnull==true){this.vError=true;this.v_msg=this.formItem.title+"不能为空"}}}if(this.vError&&!this.v_msg){this.v_msg="请选择正确的"+this.formItem.title}if(this.vError){EG.setCls(this.prop.element,"error",this.prop.cls)}else{EG.setCls(this.prop.element,"input",this.prop.cls)}return !this.vError},getTitle:function(o,g,l){var m=l.textvalues||[];if(!EG.isArray(o)){o=[o]}var k=[];for(var e=0,h=o.length;e<h;e++){var p=o[e];for(var f=0,n=m.length;f<n;f++){if(m[f][1]===p){k.push(m[f][0]);break}}}return k.join(",")}}})})();(function(){EG.define("EG.ui.form.prop.Date",["EG.ui.form.Prop","EG.ui.Date"],function(c,a,b){return{extend:c,config:{type:"date",unnull:false,minLength:null,width:"100%",height:20,vldType:null},constructor:function(e){this.initConfig(e);this.prop=new a(e);c.bindValidate.apply(this,[e])},validate:function(){return c.validate.apply(this)},onError:function(){return c.onError.apply(this)}}})})();(function(){EG.define("EG.ui.form.prop.Editor",["EG.ui.form.Prop","EG.ui.Editor"],function(c,a,b){return{extend:c,config:{readLineHeight:22},constructor:function(e){this.prop=new a(e)}}})})();(function(){EG.define("EG.ui.form.prop.Label",["EG.ui.form.Prop","EG.ui.Label"],function(b,c,a){return{extend:b,constructor:function(e){this.prop=new c(e)}}})})();(function(){EG.define("EG.ui.form.prop.Password",["EG.ui.form.Prop","EG.ui.Password"],function(c,a,b){return{extend:c,config:{type:"password",unnull:false,minLength:null,width:"100%",height:20,vldType:null},constructor:function(e){this.initConfig(e);this.prop=new a(e);c.bindValidate.apply(this,[e])},validate:function(){return c.validate.apply(this)},onError:function(){return c.onError.apply(this)}}})})();(function(){EG.define("EG.ui.form.prop.Select",["EG.ui.form.Prop","EG.ui.Select"],function(b,c,a){return{extend:b,config:{type:"select",unnull:false},constructor:function(e){this.initConfig(e);this.prop=new c(e);b.bindValidate.apply(this,[e])},validate:function(){var e=this.getForm();if(e&&!e.validateAble){return true}var f=this.prop.getValue();this.vError=false;this.v_msg="";if(EG.String.isBlank(f+"")){if(this.unnull==true){this.vError=true;this.v_msg=this.formItem.title+"不能为空"}}if(this.vError&&!this.v_msg){this.v_msg="请选择正确的"+this.cfg.title}if(this.onError){this.onError()}return !this.vError},onError:function(){if(this.vError){EG.setCls(this.prop.input,"error",this.prop.cls)}else{EG.setCls(this.prop.input,"input",this.prop.cls)}},getTitle:function(k,j,e){var g=e.textvalues||[];for(var h=0,f=g.length;h<f;h++){if(g[h][1]==k){return g[h][0]}}return null}}})})();(function(){EG.define("EG.ui.form.prop.SelectArea",["EG.ui.form.Prop","EG.ui.SelectArea"],function(b,c,a){return{extend:b,config:{type:"SelectArea",unnull:false},constructor:function(f){var g=this;this.initConfig(f);var e=f.onchange;f.onchange=function(){g.validate();if(e){e.apply(this,arguments)}};this.prop=new c(f);b.bindValidate.apply(this,[f])},validate:function(){this.vError=false;this.v_msg="";var e=this.prop.getValue();if(this.unnull==true&&(!e||e.length==0)){this.v_msg=this.formItem.title+"不能为空";this.vError=true}if(this.onError){this.onError()}return !this.vError},onError:function(){if(this.vError){EG.setCls(this.prop.destSlt,"error",this.prop.cls)}else{EG.setCls(this.prop.destSlt,"slts",this.prop.cls)}}}})})();(function(){EG.define("EG.ui.form.prop.Text",["EG.ui.form.Prop","EG.ui.Text"],function(c,a,b){return{extend:c,config:{type:"text",unnull:false,minLength:null,width:"100%",height:20,vldType:null},constructor:function(e){this.initConfig(e);this.prop=new a(e);c.bindValidate.apply(this,[e])},validate:function(){return c.validate.apply(this)},onError:function(){return c.onError.apply(this)}}})})();(function(){EG.define("EG.ui.form.prop.Textarea",["EG.ui.form.Prop","EG.ui.Textarea"],function(c,a,b){return{extend:c,config:{type:"textarea",unnull:false,minLength:null,width:"100%",height:40,vldType:null,readLineHeight:22},constructor:function(e){this.initConfig(e);this.prop=new a(e);c.bindValidate.apply(this,[e])},validate:function(){return c.validate.apply(this)},onError:function(){return c.onError.apply(this)},setRead:function(e,h){if(!h){h=""}h=EG.String.replaceAll(h," ","&nbsp;");var g=h.split("\n");for(var f=0;f<g.length;f++){g[f]=("<span>"+g[f]+"</span>")}h=g.join("<br/>");h=EG.String.replaceAll(h,"\t","<div style='margin-left:4em;display:inline-block;'></div>");EG.setValue(e,h)}}})})();(function(){EG.define("EG.ui.form.prop.Upload",["EG.ui.form.Prop","EG.ui.Upload"],function(c,a,b){return{extend:c,constructor:function(e){this.prop=new a(e)}}})})();(function(){EG.define("EG.ui.Menu",["EG.ui.Item"],function(a,b){return{extend:a,config:{direct:"H",autoExpand:false,autoExpandLv:0,itemsConfig:null,pop:false,verticalAlign:null,isContextMenu:false,contextElement:null},constructor:function(c){this.callSuper([c]);if(this.contextElement){this.attachContext(this.contextElement)}},afterBuild:function(){this.items=[];if(this.itemsConfig!=null&&this.itemsConfig.length>0){this.addItem(this.itemsConfig,false)}},build:function(){var c=this;this.element=EG.CE({tn:"div"});if(this.isContextMenu){EG.css(this.element,"position:absolute");EG.hide(this.element)}},attachContext:function(e){var c=this;this.contextElement=e;EG.Event.bindEvent(this.contextElement,"oncontextmenu",function(f){f=EG.Event.getEvent(f);var g=EG.Tools.getMousePos(f,c.contextElement);EG.Style.moveTo(c.element,g);if(c.onOpen){if(c.onOpen.apply(c,[f])===false){return}}EG.show(c.element);EG.Event.stopPropagation(f);return false});EG.Event.bindEvent(this.contextElement,"onclick",function(f){EG.hide(c.element)})},hide:function(){EG.hide(this.element)},addItem:function(h,c){var f=(!EG.isArray(h))?[h]:h;for(var g=0,e=f.length;g<e;g++){h=f[g];h.menu=this;if(EG.isLit(h)){h=a.create(h.xtype,h)}if(c>=0){EG.Array.insert(this.items,c,h)}else{this.items.push(h)}EG.DOM.addChildren(this.element,h.getElement(),c);h.pItem=this}this.render()},removeItem:function(c,e){if(e==null){e=true}this.element.removeChild(c.getElement());EG.Array.remove(this.items,c);c.pItem=null;if(e){}},clear:function(e){if(e==null){e=true}for(var c=0;c<this.items.length;c++){if(this.items[c].isContainer){this.items[c].clear()}}EG.DOM.removeChilds(this.getItemContainer());EG.Array.clear(this.items);if(e){}},render:function(){a.fit(this);var f=this.getSize();if(this.direct=="H"){for(var c=0;c<this.items.length;c++){var e=this.items[c];EG.css(e.getElement(),EG.Style.c.dv);e.render()}}else{for(var c=0;c<this.items.length;c++){var e=this.items[c];EG.css(e.getElement(),"display:block;");e.render()}}if(this.verticalAlign=="middle"){EG.Style.middleChilds(this.element,this.direct)}else{if(this.verticalAlign=="top"){EG.Style.topChilds(this.element,this.direct)}else{if(this.verticalAlign=="bottom"){EG.Style.bottomChilds(this.element,this.direct)}}}},clear:function(){EG.Array.clear(this.items);EG.DOM.removeChilds(this.element)}}})})();(function(){EG.define("EG.ui.MenuItem",["EG.ui.Item"],function(a,b){return{extend:a,config:{cls:"eg_menuItem",layoutDirect:null,menuDirect:null,text:null,menuWidth:null,menuHeight:null,itemsConfig:null,menu:null,click:null,fade:true,showMuti:true,mutiDirect:"right",showMutiOnEmpty:false,selectedAble:true},constructor:function(c){this.callSuper([c]);if(this.text){this.setText(this.text)}if(this.menu){this.setMenu(this.menu)}this.items=[];if(this.itemsConfig!=null&&this.itemsConfig.length>0){this.addItem(this.itemsConfig)}},build:function(){var c=this;this.element=EG.CE({tn:"div",cls:this.cls,item:this,cn:[this.dOuter=EG.CE({tn:"div",cls:this.cls+"-outer",item:this,cn:[this.dText=EG.CE({tn:"div",cls:this.cls+"-text",item:this}),this.dMulti=EG.CE({tn:"div",cls:this.cls+"-multi",item:this})]}),this.dMenus=EG.CE({tn:"div",cls:this.cls+"-menu",item:this,style:"display:none;"})]});if(this.mutiDirect=="right"){EG.DOM.insertAfter(this.dMulti,this.dText)}else{if(this.mutiDirect=="left"){EG.DOM.insertBefore(this.dMulti,this.dText)}}EG.CE({ele:this.dOuter,onmouseover:b._events.element.onmouseover,onmouseoverSrc:this,onmouseout:b._events.element.onmouseout,onmouseoutSrc:this,onclick:b._events.element.onclick,onclickSrc:this});EG.CE({ele:this.dMenus,onmouseover:b._events.element.onmouseover,onmouseoverSrc:this,onmouseout:b._events.element.onmouseout,onmouseoutSrc:this});EG.CE({ele:this.dText,onclick:this.expandClick,onclickSrc:this});EG.CE({ele:this.dMulti,onclick:this.expandClick,onclickSrc:this});EG.hide(this.dMenus);if(!this.showMuti){EG.hide(this.dMulti)}},setMenu:function(c){this.menu=c;EG.css(this.dMenus,(this.menu.pop?"position:absolute;z-index:1;":""))},setText:function(c){this.text=c;if(typeof(c)=="string"){this.dText.innerHTML=c}else{var e=c;if(!EG.Array.isArray(e)){e=[e]}EG.CE({ele:this.dText,cn:e})}},expandClick:function(){if(!this.menu.pop){if(EG.Style.current(this.dMenus).display=="none"){this.showChildMenus()}else{this.hideChildMenus()}}if(this.click){this.click.apply(this)}},addItem:function(h,c){var f=(!EG.isArray(h))?[h]:h;for(var g=0,e=f.length;g<e;g++){h=f[g];h.menu=this.menu;if(EG.isLit(h)){h=a.create(h.xtype,h)}h.pItem=this;if(c>=0){EG.Array.insert(this.items,c,h)}else{this.items.push(h)}EG.DOM.addChildren(this.dMenus,h.getElement(),c)}this.render()},removeItem:function(c,e){if(e==null){e=true}this.dMenus.removeChild(c.getElement());EG.Array.remove(this.items,c);c.pItem=null;if(e){}},clear:function(e){if(e==null){e=true}for(var c=0;c<this.items.length;c++){if(this.items[c].isContainer){this.items[c].clear()}}EG.DOM.removeChilds(this.getItemContainer());EG.Array.clear(this.items);if(e){}},render:function(){if(this.showMuti&&(this.showMutiOnEmpty||(this.items!=null&&this.items.length>0))){EG.show(this.dMulti)}else{EG.hide(this.dMulti)}if(this.hidden){EG.hide(this.getElement());return}else{EG.show(this.getElement())}if(this.rendered){return}if(this.width){a.fit({element:this.element,width:this.width,type:"width"});a.fit({element:this.dOuter,width:"100%",type:"width"});a.fit({element:this.dText,width:EG.getSize(this.dOuter).innerWidth,type:"width"})}this.rendered=true},renderMenus:function(){if(!this.menu.pop){if((this.pItem&&this.pItem.layoutDirect||this.menu&&this.menu.direct)=="H"){EG.css(this.dMenus,EG.Style.c.dv);EG.css(this.dText,EG.Style.c.dv)}else{EG.css(this.dMenus,"display:block;");EG.css(this.dText,"display:block;")}}var f=this.getSize();if(this.layoutDirect=="H"){for(var c=0;c<this.items.length;c++){var e=this.items[c];EG.css(e.getElement(),EG.Style.c.dv);if(this.menuHeight!=null){e.height=this.menuHeight}e.render()}}else{for(var c=0;c<this.items.length;c++){var e=this.items[c];EG.css(e.getElement(),"display:block;");if(this.menuWidth!=null){e.width=this.menuWidth}e.render()}}},showChildMenus:function(){if(this.items.length==0){return}if(this.menu.pop){var g=EG.Tools.getElementPos(this.element,this.element.offsetParent);var f=EG.Tools.getElementPos(this.dOuter,this.element.offsetParent);var e=EG.Style.getSize(this.dOuter);if(this.menuDirect=="B"){g.y=g.y+e.innerHeight+e.borderBottom+e.borderTop}else{if(this.menuDirect=="R"){var c=e.innerWidth+e.borderLeft+e.borderRight;if(EG.Style.current(this.getElement().parentNode).position=="absolute"){g.y=f.y;g.x=c}else{g.x=g.x+c;g.y=f.y}}else{throw new Error("暂不支持")}}EG.css(this.dMenus,"top:"+g.y+"px;left:"+g.x+"px;");if(this.menuWidth!=null){EG.css(this.dMenus,"width:"+this.menuWidth+"px")}if(this.menuHeight!=null){EG.css(this.dMenus,"height:"+this.menuHeight+"px")}}EG.show(this.dMenus);if(this.fade){EG.Style.fade(this.dMenus,0,90,null,10)}this.renderMenus()},hideChildMenus:function(){if(this.menu.pop){for(var c=0;c<b.w4h.length;c++){EG.hide(b.w4h[c].dMenus)}this.hiding=false}else{EG.hide(this.dMenus)}},addHideQue:function(){if(!EG.Array.has(b.w4h,this)){b.w4h.push(this);var c=this;while((c=c.pItem)!=null){if(c.getClass()._className!="EG.ui.MenuItem"){break}b.w4h.push(c)}}},removeHideQue:function(){EG.Array.remove(b.w4h,this);var c=this;while((c=c.pItem)!=null){if(c.getClass()._className!="EG.ui.MenuItem"){break}EG.Array.remove(b.w4h,c)}this.showChildMenus()},refreshCls:function(c){c=c?EG.Word.first2Uppercase(c):"";EG.Style.setCls(this.dOuter,this.cls+"-outer"+EG.n2d(c,""));EG.Style.setCls(this.dText,this.cls+"-text"+EG.n2d(c,""))},statics:{_events:{element:{onmouseover:function(c){this.refreshCls("on");if(this.menu.pop){this.removeHideQue()}EG.Event.stopPropagation(c)},onmouseout:function(f){var c=this;if(this.pItem.selectedItem==this){this.refreshCls("selected")}else{this.refreshCls()}if(this.menu.pop){this.addHideQue();if(!this.hiding){this.hiding=true;setTimeout(function(){c.hideChildMenus()},100)}}EG.Event.stopPropagation(f)},onclick:function(c){if(this.pItem.selectedItem){this.pItem.selectedItem.refreshCls()}if(this.selectedAble){this.pItem.selectedItem=this;this.refreshCls("selected")}}}},hiding:false,w4h:[]}}})})();(function(){EG.define("EG.ui.Calendar",["EG.ui.Item"],function(b,c){return{extend:b,config:{cls:"eg_calander",mode:"M",cellSpacing:0,cellPadding:0,border:0,date:null,scale:15},constructor:function(e){c.load();this.callSuper([e]);this.setDate(this.date||new Date())},build:function(){var e=this;this.p=new EG.ui.Panel({layout:"border",items:[this.pTop=new EG.ui.Panel({region:"top",height:30,cls:this.cls+"-top",layout:{type:"default",verticalAlign:true},cn:[this.dPre=EG.CE({tn:"div",cls:this.cls+"-top-pre",onclick:function(){e.goPre()}}),this.dCur=EG.CE({tn:"div",cls:this.cls+"-top-cur",onclick:function(){e.choose()},cn:[this.dChooserYear=EG.CE({tn:"div",onclick:this.chooseYear,onclickSrc:this,style:EG.Style.c.dv}),this.dChooserMonth=EG.CE({tn:"div",onclick:this.chooseMonth,onclickSrc:this,style:EG.Style.c.dv+"margin-left:10px"})]}),this.dPre=EG.CE({tn:"div",cls:this.cls+"-top-next",onclick:function(){e.goNext()}})]}),this.pLeft=new EG.ui.Panel({region:"left",cls:this.cls+"-left",layout:"border",width:70,items:[this.pLeftHead=new EG.ui.Panel({region:"top",cls:this.cls+"-left-head",height:30}),this.pLeftBody=new EG.ui.Panel({region:"center",cls:this.cls+"-left-body",cn:[this.leftBodyTable=EG.CE({tn:"table",cls:this.cls+"-left-table",item:this,cellPadding:this.cellPadding,cellSpacing:this.cellSpacing,border:this.border,cn:[this.leftBodyTableBody=EG.CE({tn:"tbody"})]})]})]}),this.pMain=new EG.ui.Panel({region:"center",layout:"border",cls:this.cls+"-main",items:[this.pMainHead=new EG.ui.Panel({region:"top",cls:this.cls+"-main-head",height:30,cn:[this.mainHeadTable=EG.CE({tn:"table",cls:this.cls+"-main-head-table",item:this,cellPadding:this.cellPadding,cellSpacing:this.cellSpacing,border:this.border,cn:[this.mainHeadTableBody=EG.CE({tn:"tbody",cn:[{tn:"tr"}]})]})]}),this.pMainBody=new EG.ui.Panel({region:"center",cls:this.cls+"-main-body",cn:[this.mainBodyTable=EG.CE({tn:"table",cls:this.cls+"-main-body-table",style:"",item:this,border:0,cellPadding:0,cellSpacing:0,cn:[this.mainBodyTableBody=EG.CE({tn:"tbody"})]})]})]})]});EG.DOM.addChildren(this.p.getElement(),this.dChooser=EG.CE({tn:"div",cls:this.cls+"-chooser",style:"position:absolute;"}));EG.hide(this.dChooser);EG.bindEvent(this.pMainBody.getElement(),"onscroll",function(){e.pLeftBody.getElement().scrollTop=this.scrollTop});this.element=this.p.getElement()},setDate:function(e){e=this.filterDate(e);this.curDate=e;this.draw()},filterDate:function(e){if(typeof(e)=="string"){e=EG.Date.s2d(e)}return e},getPreDate:function(){},getNextDate:function(){},goPre:function(){this.setDate(this.getPreDate())},goNext:function(){this.setDate(this.getNextDate())},choose:function(){},getDate:function(){return this.curDate},refresh:function(){this.setDate(this.curDate)},draw:function(){},go2:function(e){},render:function(){this.p.width=this.width;this.p.height=this.height;this.p.render()},statics:{_events:{},load:function(){if(c.loaded){return}EG.bindEvent(EG.getBody(),"onclick",function(){if(c.chooser&&!EG.Style.isHide(c.chooser)){EG.Style.hide(c.chooser)}});c.loaded=true},weeks:["日","一","二","三","四","五","六"],create:function(e){if(e.mode=="M"){return new EG.ui.calander.Month(e)}else{if(e.mode=="W"){return new EG.ui.calander.Week(e)}else{if(e.mode=="D"){return new EG.ui.calander.Day(e)}}}}}}});var a=EG.ui.Calendar})();(function(){EG.define("EG.ui.calendar.Month",["EG.ui.Item","EG.ui.Calendar"],function(c,f,e){return{extend:f,config:{},constructor:function(g){this.callSuper([g])},build:function(){var l=this;this.callSuper("build");EG.CE({ele:this.dCur,cn:[this.dChooserYear=EG.CE({tn:"div",onclick:this.chooseYear,onclickSrc:this,style:EG.Style.c.dv}),this.dChooserMonth=EG.CE({tn:"div",onclick:this.chooseMonth,onclickSrc:this,style:EG.Style.c.dv+"margin-left:10px"})]});this.pLeft.setHidden(true);for(var h=0;h<7;h++){EG.CE({pn:this.mainHeadTableBody.childNodes[0],tn:"td",innerHTML:"周"+a.weeks[h]})}var k=function(){if(l.lastSelected){EG.Style.removeCls(l.lastSelected,"selected")}l.lastSelected=this;EG.Style.addCls(l.lastSelected,"selected")};for(var h=0;h<6;h++){var m=EG.CE({pn:this.mainBodyTableBody,tn:"tr"});for(var g=0;g<7;g++){EG.CE({pn:m,tn:"td",onclick:k})}}},draw:function(){this.callSuper("draw");this.dChooserYear.innerHTML=this.curDate.getFullYear()+"年";this.dChooserMonth.innerHTML=(this.curDate.getMonth()+1)+"月";var g=EG.Date.getMonthday(this.curDate);var m=EG.clone(this.curDate);m.setDate(1);var j=m.getDay();var l=this.mainBodyTableBody.getElementsByTagName("td");var n=EG.clone(this.curDate);n.setDate(1);for(var k=0;k<l.length;k++){l[k].innerHTML="";var o=EG.Date.l2d(n.getTime()+EG.Date.unit_day*(k-j));var h=k<j?-1:0;if(h==0){if(this.curDate.getMonth()!=o.getMonth()){h=1}}this.drawDay(l[k],o,h)}},drawDay:function(j,h,g){j.innerHTML="<span style='color:"+(g!=0?"gray":"black")+"'>"+h.getDate()+"</span>"},render:function(){this.callSuper("render");c.fit({element:this.mainHeadTable});c.fit({element:this.mainHeadTableBody});c.fit({element:this.mainBodyTable});c.fit({element:this.mainBodyTableBody});var l=EG.getSize(this.mainBodyTable);var q=this.mainBodyTableBody.childNodes.length;var o=parseInt(l.innerHeight/q);var r=parseInt(l.innerWidth/7);var s=l.innerHeight%q;var m=l.innerWidth%7;var j=EG.getSize(this.mainHeadTableBody.childNodes[0].childNodes[0]);var k=EG.getSize(this.mainBodyTableBody.childNodes[0].childNodes[0]);for(var p=0;p<7;p++){var h=(r-(j.outerWidth-j.innerWidth));var n=(r-(k.outerWidth-k.innerWidth));if(p==6&&m>0){h+=m;n+=m}EG.css(this.mainHeadTableBody.childNodes[0].childNodes[p],"width:"+h+"px");EG.css(this.mainBodyTableBody.childNodes[0].childNodes[p],"width:"+n+"px")}for(var p=0;p<q;p++){var g=(o-(k.outerHeight-k.innerHeight));if(p==q-1&&s>0){g+=s}EG.css(this.mainBodyTableBody.childNodes[p].childNodes[0],"height:"+g+"px")}},getPreDate:function(){var g=EG.clone(this.curDate);if(g.getMonth==0){g.setMonth(11);g.setYear(g.getFullYear()-1)}else{g.setMonth(g.getMonth()-1)}return g},getNextDate:function(){var g=EG.clone(this.curDate);if(g.getMonth==11){g.setMonth(0);g.setYear(g.getFullYear()+1)}else{g.setMonth(g.getMonth()+1)}this.setDate(g);return g},getLeft:function(g){var h=g.offsetLeft;if(g.offsetParent!=null){h+=this.getLeft(g.offsetParent)}return h},chooseYear:function(k,j){var h=this;if(j==null){j=0}var l=EG.Tools.getElementPos(this.dChooserYear,this.element);l.y=this.dChooserYear.clientHeight;EG.DOM.removeChilds(this.dChooser);EG.CE({ele:this.dChooser,cn:[{tn:"div",style:EG.Style.c.dv},{tn:"div",style:EG.Style.c.dv}]});for(var g=5;g>=1;g--){EG.CE({pn:this.dChooser.childNodes[0],tn:"a",innerHTML:(this.curDate.getFullYear()-g+j*10),style:"display:block",item:this,onclick:e._events.aYear.click})}for(var g=0;g<5;g++){EG.CE({pn:this.dChooser.childNodes[1],tn:"a",innerHTML:(this.curDate.getFullYear()+g+j*10),style:"display:block",item:this,onclick:e._events.aYear.click})}EG.CE({pn:this.dChooser.childNodes[0],tn:"a",innerHTML:"<-",style:"display:block",item:this,onclick:function(){h.chooseYear(null,j-1)}});EG.CE({pn:this.dChooser.childNodes[1],tn:"a",innerHTML:"->",style:"display:block",item:this,onclick:function(){h.chooseYear(null,j+1)}});e.chooser=this.dChooser;EG.show(this.dChooser);EG.css(this.dChooser,"top:"+l.y+"px;left:"+((l.x-EG.getSize(this.dChooser.childNodes[0]).outerWidth/2))+"px");EG.Event.stopPropagation(k)},chooseMonth:function(h){var j=EG.Tools.getElementPos(this.dChooserMonth,this.element);j.y=this.dChooserMonth.clientHeight;EG.DOM.removeChilds(this.dChooser);EG.CE({ele:this.dChooser,cn:[{tn:"div",style:EG.Style.c.dv},{tn:"div",style:EG.Style.c.dv}]});for(var g=1;g<=6;g++){EG.CE({pn:this.dChooser.childNodes[0],tn:"a",innerHTML:g,style:"display:block",item:this,onclick:e._events.aMonth.click})}for(var g=7;g<=12;g++){EG.CE({pn:this.dChooser.childNodes[1],tn:"a",innerHTML:g,style:"display:block",item:this,onclick:e._events.aMonth.click})}e.chooser=this.dChooser;EG.show(this.dChooser);EG.css(this.dChooser,"top:"+j.y+"px;left:"+((j.x-EG.getSize(this.dChooser.childNodes[1]).outerWidth/2))+"px");EG.Event.stopPropagation(h)},statics:{_events:{aYear:{click:function(){var g=EG.clone(this.item.curDate);g.setYear(parseInt(this.innerHTML));this.item.setDate(g);EG.hide(this.item.dChooser)}},aMonth:{click:function(){var g=EG.clone(this.item.curDate);g.setMonth(parseInt(this.innerHTML)-1);this.item.setDate(g);EG.hide(this.item.dChooser)}}}}}});var b=EG.ui.calendar.Month;var a=EG.ui.Calendar})();(function(){EG.define("EG.ui.calendar.Week",{extend:"EG.ui.Calendar",config:{},constructor:function(c){this.callSuper([c])},build:function(){var l=this;this.callSuper("build");EG.CE({ele:this.dCur,cn:[this.dChooserYear=EG.CE({tn:"div",onclick:this.chooseYear,onclickSrc:this,style:EG.Style.c.dv}),this.dChooserMonth=EG.CE({tn:"div",onclick:this.chooseMonth,onclickSrc:this,style:EG.Style.c.dv+"margin-left:10px"})]});var k=24*60/this.scale;var o=new Date();o.setMinutes(0);o.setHours(0);o.setSeconds(0);for(var f=0;f<k;f++){var g=parseInt((f*this.scale)/60);var c=(f*this.scale)%60;EG.CE({pn:this.leftBodyTableBody,tn:"tr",cn:[{tn:"td",style:"height:25px;line-height:25px;",innerHTML:(g+":"+c)}]});var n=EG.CE({pn:this.mainBodyTableBody,tn:"tr"});for(var e=0;e<7;e++){EG.CE({pn:n,tn:"td",style:"height:25px;line-height:25px;"})}}for(var f=0;f<7;f++){EG.CE({pn:this.mainHeadTableBody.childNodes[0],tn:"td",innerHTML:"周"+a.weeks[f]})}EG.CE({pn:this.mainHeadTableBody.childNodes[0],tn:"td"})},draw:function(){this.callSuper("draw");this.dChooserYear.innerHTML=this.curDate.getFullYear()+"年";this.dChooserMonth.innerHTML=(this.curDate.getMonth()+1)+"月"},drawCol:function(f,e,c){},addBlock:function(j,k,m){if(EG.Date.sameDay(j,k)){var h=j.getDay();var f=this.mainHeadTableBody.childNodes[0].childNodes[h];var n=EG.getSize(f);var c=n.innerWidth;var e=f.offsetLeft;var g=this.mainBodyTableBody.childNodes[this.getRowIdx(j)].offsetTop;var l=this.mainBodyTableBody.childNodes[this.getRowIdx(k)].offsetTop-g;alert(g+","+e+","+c+","+l);this.pMainBody.getElement().appendChild(m);Item.fit({element:m,pSize:{width:c,height:l}});EG.css(m,"position:absolute;top:"+g+"px;left:"+e+"px");EG.bindEvent(m,"onmouseover",function(){});EG.bindEvent(m,"onmouseout",function(){});EG.bindEvent(m,"onclick",function(){})}else{}},getRowIdx:function(c){return(c.getHours()*60+c.getMinutes())/this.scale},render:function(){this.callSuper("render");Item.fit({element:this.leftBodyTable});Item.fit({element:this.leftBodyTableBody});Item.fit({element:this.mainHeadTable});Item.fit({element:this.mainHeadTableBody});Item.fit({element:this.mainBodyTable});Item.fit({element:this.mainBodyTableBody});var n=EG.getSize(this.mainBodyTableBody);var l=EG.getSize(this.mainHeadTableBody);var e=n.innerWidth%7;var h=EG.getSize(this.mainBodyTableBody.childNodes[0].childNodes[0]);var j=parseInt(n.innerWidth/7)-(h.outerWidth-h.innerWidth);var m=this.mainBodyTableBody.childNodes[0].childNodes;var k=EG.getSize(this.mainHeadTableBody.childNodes[0].childNodes[0]);var c=parseInt(n.innerWidth/7)-(k.outerWidth-k.innerWidth);var f=this.mainHeadTableBody.childNodes[0].childNodes;for(var g=0;g<7;g++){EG.css(m[g],"width:"+(j+(g==6?e:0))+"px");EG.css(f[g],"width:"+(c+(g==6?e:0))+"px")}EG.getSize(f[7],"width:"+this.mainHeadTable.innerWidth-7*c+"px")},statics:{_events:{aYear:{click:function(){var c=EG.clone(this.item.curDate);c.setYear(parseInt(this.innerHTML));this.item.setDate(c)}},aMonth:{click:function(){var c=EG.clone(this.item.curDate);c.setMonth(parseInt(this.innerHTML)-1);this.item.setDate(c)}}}}});var b=EG.ui.calendar.Week;var a=EG.ui.Calendar})();(function(){EG.define("EG.ui.Slide",["EG.ui.Item"],function(a,b){return{alias:"slide",extend:a,config:{onchange:null,cls:"eg_slide",range_start:0,range_end:100,doubleEnable:false,showScale:false,showTip:false,tipStyle:null,onSlideUp:null},constructor:function(c){this.callSuper([c])},build:function(){this.element=EG.CE({tn:"div",cls:this.cls,item:this,cn:[this.el_tip=EG.CE({tn:"div",cls:this.cls+"-tip",style:"position:absolute;",item:this,innerHTML:this.range_start||" "}),this.el_background=EG.CE({tn:"div",cls:this.cls+"-background",style:"position:absolute;",item:this}),this.el_block=EG.CE({tn:"div",cls:this.cls+"-block",style:"position:absolute"})],onmousemove:b._events.element.onmousemove,onmouseup:b._events.element.onmouseup});if(this.tipStyle){EG.css(this.el_tip,this.tipStyle)}this.builded=true},setValue:function(g){this._value=g;var f=this.value2Size(g);EG.css(this.el_block,"margin-left:"+f+"px");if(this.showTip){var e=EG.getSize(this.el_block);var c=EG.getSize(this.el_tip);EG.css(this.el_tip,"margin-left:"+(f+(e.clientWidth/2-c.clientWidth/2))+"px");this.el_tip.innerHTML=g}},getValue:function(){return this._value},destroy:function(){},size2Value:function(f){var c=this.el_background.clientWidth;var e=this.range_start+(f/c)*(this.range_end-this.range_start);if(!this.doubleEnable){e=parseInt(e)}if(e<this.range_start){e=this.range_start}else{if(e>this.range_end){e=this.range_end}}return e},value2Size:function(f){var c=this.el_background.clientWidth;var e=f/(this.range_end-this.range_start);return c*e},render:function(){a.fit(this);var f=EG.getSize(this.getElement());var g=f.innerHeight/2;a.fit({element:this.el_background,pSize:f,dSize:{width:f.innerWidth-g*2,height:g}});EG.css(this.el_background,"margin-top:"+(g/2)+"px;border-radius:"+g/2+"px;margin-left:"+g+"px");var e=EG.getSize(this.el_block);EG.css(this.el_block,{width:f.innerHeight-(e.borderLeft+e.borderRight)+"px",height:f.innerHeight-(e.borderTop+e.borderBottom)+"px","border-radius":g+"px"});e=EG.getSize(this.el_block);if(this.showTip){EG.show(this.el_tip);EG.css(this.el_tip,"width:"+g+"px;height:"+g+"px;margin-top:-"+g+"px;line-height:"+g+"px");var c=EG.getSize(this.el_tip);EG.css(this.el_tip,"margin-left:"+(e.clientWidth/2-c.clientWidth/2)+"px")}else{EG.hide(this.el_tip)}},statics:{_events:{element:{onmousemove:function(j){var k=this.item;if(EG.Tools.isPressLeft(j)){if(!b._startMousePos){b._startMousePos=EG.Tools.getMousePos(j,this);b._startBlockSize=EG.getSize(k.el_block)}var l=EG.Tools.getMousePos(j,this);var g=l.x-b._startMousePos.x+b._startBlockSize.marginLeft;if(g<0){g=0}var n=EG.getSize(k.element);var f=EG.getSize(k.el_block);if(g>=(n.innerWidth-f.clientWidth)){g=n.innerWidth-f.clientWidth}EG.css(k.el_block,"margin-left:"+g+"px");var h=EG.getSize(k.el_background);var c=(g+f.clientWidth/2)-h.marginLeft;var m=k.size2Value(c);k.setValue(m)}EG.Event.stopPropagation(j)},onmouseup:function(g){var c=this.item;if(EG.Tools.isPressLeft(g)){var f=c.getValue();if(c.onSlideUp){c.onSlideUp.apply(this,[f])}}b._startPos=null}}}}}})})();(function(){EG.define("EG.ui.Switch",["EG.ui.Item"],function(a,b){return{alias:"switch",extend:a,config:{onchange:null,cls:"eg_switch",range_start:0,range_end:100,doubleEnable:false,textvalues:[],openValue:null,closeValue:null,openText:"打开",closeText:"关闭",showText:false,marginSpacing:3,textStyle:null},constructor:function(c){var e=c.textvalues;if(e){if(this.openValue==null){this.openValue=e[0][1]}if(this.closeValue==null){this.closeValue=e[1][1]}if(this.openText==null){this.openText=e[0][0]}if(this.closeText==null){this.closeText=e[1][0]}}this.callSuper([c])},build:function(){var c=this;this.element=EG.CE({tn:"div",cls:this.cls,item:this,cn:[this.el_background=EG.CE({tn:"div",cls:this.cls+"-background-close",style:"position:absolute;",item:this}),this.el_block=EG.CE({tn:"div",cls:this.cls+"-block",style:"position:absolute",onclick:function(){var e=c.getOppositeValue(c.getValue());c.setValue(e)}}),this.el_text=EG.CE({tn:"div",cls:this.cls+"-text",style:"position:absolute;text-align:center;",onclick:function(){}})]});if(this.textStyle){EG.css(this.el_text,this.textStyle)}this.builded=true},getOppositeValue:function(c){return c==this.openValue?this.closeValue:this.openValue},setValue:function(c){this._value=c;this.render()},getValue:function(){return this._value},destroy:function(){},doSwitch:function(){this.setValue(this.getOppositeValue(this.getValue()))},getTitle:function(f,e,c){if(f==this.openValue){return this.openText}else{if(f==this.closeValue){return this.closeText}}return""},render:function(){var h=this.getValue();a.fit(this);var f=EG.getSize(this.element);EG.Style.setCls(this.el_background,this.cls+"-background-"+(h==this.openValue?"open":"close"));a.fit({element:this.el_background,pSize:f});EG.css(this.el_background,"border-radius:"+f.innerHeight/2+"px");var g=EG.getSize(this.el_background);EG.css(this.el_block,{width:(f.innerHeight-this.marginSpacing*2)+"px",height:(f.innerHeight-this.marginSpacing*2)+"px","border-radius":f.innerHeight/2+"px","margin-top":this.marginSpacing+"px","margin-left":this.marginSpacing+"px",});var e=EG.getSize(this.el_block);EG.css(this.el_block,"margin-top:"+this.marginSpacing+"px;");var j=g.innerWidth-e.clientWidth;if(h==this.openValue){EG.css(this.el_block,"margin-left:"+(j-this.marginSpacing)+"px")}else{EG.css(this.el_block,"margin-left:"+(this.marginSpacing)+"px")}var c=(g.innerWidth-e.clientWidth-this.marginSpacing*2);EG.css(this.el_text,"width:"+c+"px;height:"+f.innerHeight+"px;line-height:"+f.innerHeight+"px");if(this.showText){EG.hide(this.el_text)}else{EG.show(this.el_text)}if(h==this.openValue){EG.CE({ele:this.el_text,cls:this.cls+"-text-open",style:"margin-left:"+(this.marginSpacing)+"px",innerHTML:this.openText})}else{EG.CE({ele:this.el_text,cls:this.cls+"-text-close",style:"margin-left:"+(e.clientWidth+this.marginSpacing*2)+"px",innerHTML:this.closeText})}}}})})();(function(){EG.define("EG.ui.JSONView",["EG.ui.Item"],function(a,b){return{alias:"jsonView",extend:a,constructor:function(c){this.callSuper([c])},build:function(){this.element=EG.CE({tn:"div"})},setValue:function(c){this.value=c;this.element.innerHTML=this.getObjHtml(c)},getValue:function(){return this.value},getIndentSpace:function(c){var f="";for(var e=0;e<c*4;e++){f+="&nbsp;"}return f},getObjHtml:function(h,e,s){if(e==null){e=0}if(s==null){s=false}var j="color:#00AA00";var r="color:#0033FF";var m="color:#AA00AA";var o="color:#007777";var q="color:#CC0000";var l="color:#AA00AA";var p="color:#0033FF";var c=EG.getType(h);var n="";if(h==null){n='<span style="'+m+'">null</span>'}else{if(typeof(h)=="string"){n='<span style="'+o+'">"'+h+'"</span>'}else{if(typeof(h)=="number"){n='<span style="'+l+'">'+h+"</span>"}else{if(typeof(h)=="boolean"){n='<span style="'+p+'">'+h+"</span>"}else{if(h instanceof Object){if(EG.Array.isArray(h)){n='<span style="'+r+'">[</span><br/>';for(var g=0;g<h.length;g++){if(g>0){n+=",<br/>"}n+=this.getIndentSpace(e+1)+this.getObjHtml(h[g],e+1)}n+="<br/>";n+='<span style="'+r+'">'+this.getIndentSpace(e)+"]</span>"}else{n='<span style="display-line:block;'+j+'">{</span><br/>';var g=0;for(var f in h){if(g>0){n+=",<br/>"}n+=this.getIndentSpace(e+1)+'<span style="'+q+'">"'+f+'"</span>:'+this.getObjHtml(h[f],e+1,true);g++}n+="<br/>";n+='<span style="'+j+'">'+this.getIndentSpace(e)+"}</span>"}}else{n="#δ֪��ʽgetObjHtml#"}}}}}return n},render:function(){a.fit(this)}}})})();(function(){EG.define("EG.Math",function(a){return{statics:{a2r:function(b){return(b/360)*2*Math.PI},r2a:function(b){return(b/(2*Math.PI))*360},getLength:function(c,b){return Math.sqrt(Math.pow(c[0]-b[0],2)+Math.pow(c[1]-b[1],2)+Math.pow(c[2]-b[2],2))},includedAngel:function(f,e){var c=Math.sqrt(Math.pow(f[0],2)+Math.pow(f[1],2)+Math.pow(f[2],2));var b=Math.sqrt(Math.pow(e[0],2)+Math.pow(e[1],2)+Math.pow(e[2],2));return Math.acos((f[0]*e[0]+f[1]*e[1]+f[2]*e[2])/(c*b))},vec_cross:function(b,c){return[b[1]*c[2]-c[1]*b[2],b[2]*c[0]-c[2]*b[0],b[0]*c[1]-c[0]*b[1]]},vec_dot:function(){},vec_normal:function(b,c){return a.vec_cross(b,c)},triggle_normal:function(g,e,b){var f=[e[0]-g[0],e[1]-g[1],e[2]-g[2]];var c=[b[0]-g[0],b[1]-g[1],b[2]-g[2]];return a.vec_normal(f,c)},triggle_area:function(k,h,f){var j=[h[0]-k[0],h[1]-k[1],h[2]-k[2]];var g=[f[0]-k[0],f[1]-k[1],f[2]-k[2]];var b=0;var e=0;var c=[j[1]*g[2]-g[1]*j[2],g[0]*j[2]-j[0]*g[2],j[0]*g[1]-g[0]*j[1]];b=Math.sqrt(c[0]*c[0]+c[1]*c[1]+c[2]*c[2]);e=b/2;return e},in_triggle:function(l,j,g,b){var k=a.triggle_area(l,j,g);var h=a.triggle_area(b,j,g);var f=a.triggle_area(l,b,g);var e=a.triggle_area(l,j,b);var c=h+f+e;if(Math.abs(c-k)<k/100){return true}return false},interset_point:function(s,r,w,j){var u=[];var m,l,k,p,o,n,h,g,f,e,c,b,q,v;m=s[0];l=s[1];k=s[2];p=r[0];o=r[1];n=r[2];h=w[0];g=w[1];f=w[2];e=j[0];c=j[1];b=j[2];v=h*m+g*l+f*k;if(v==0){u=null}else{q=((p-e)*m+(o-c)*l+(n-b)*k)/v;u[0]=e+h*q;u[1]=c+g*q;u[2]=b+f*q}return u}}}})})();EG.define("EG.math.Vec2",function(a){return{statics:{getLinePoint:function(g,b,f){var e=a.getLength(g,b);var c=[(g[0]-b[0])/e,(g[1]-b[1])/e];return[g[0]+c[0]*f,g[1]+c[1]*f]},getLength:function(c,b){return Math.sqrt(Math.pow(c[0]-b[0],2)+Math.pow(c[1]-b[1],2))}}}});EG.define("EG.math.Vec3",[],function(a){return{constructor:function(c,b){this.start=c;this.end=b},statics:{add:function(e,c,b){b[0]=e[0]+c[0];b[1]=e[1]+c[1];b[2]=e[2]+c[2];return b},getLength:function(c,b){return Math.sqrt(Math.pow(c[0]-b[0],2)+Math.pow(c[1]-b[1],2)+Math.pow(c[2]-b[2],2))},getLinePoint:function(g,b,f){var e=a.getLength(g,b);var c=[(g[0]-b[0])/e,(g[1]-b[1])/e,(g[2]-b[2])/e];return[g[0]+c[0]*f,g[1]+c[1]*f,g[2]+c[2]*f]}}}});(function(){EG.define("EG.math.Mat2",[],function(a){return{statics:{create:function(){return new Float32Array(4)},identity:function(b){b[0]=1;b[1]=0;b[2]=0;b[3]=0;b[4]=1;b[5]=0;b[6]=0;b[7]=0;b[8]=1;return b},multiply:function(m,l,k){var g=m[0],f=m[1],o=m[2],n=m[3],j=l[0],h=l[1],e=l[2],c=l[3];k[0]=g*j+f*h;k[1]=f*h+g*c;k[2]=o*e+n*j;k[3]=n*c+o*h;return k},multiply_point:function(c,b){return[b[0]*c[0]+b[1]*c[1],b[2]*c[0]+b[3]*c[1]]},rotate:function(c,b,j){var e=Math.sin(j);var g=Math.cos(j);var f=[g,-e,e,g];var h=[c[0]-b[0],c[1]-b[1]];h=a.multiply_point(h,f);return[h[0]+b[0],h[1]+b[1]]}}}})})();(function(){EG.define("EG.math.Mat3",function(a){return{statics:{create:function(){return new Float32Array(9)},identity:function(b){b[0]=1;b[1]=0;b[2]=0;b[3]=0;b[4]=1;b[5]=0;b[6]=0;b[7]=0;b[8]=1;return b},multiply:function(v,u,s){var y=v[0],x=v[1],w=v[2],j=v[3],h=v[4],g=v[5],p=v[6],o=v[7],n=v[8],m=u[0],l=u[1],k=u[2],t=u[3],r=u[4],q=u[5],f=u[6],e=u[7],c=u[8];s[0]=m*y+l*j+k*p;s[1]=m*x+l*h+k*o;s[2]=m*w+l*g+k*n;s[3]=t*y+r*j+q*p;s[4]=t*x+r*h+q*o;s[5]=t*w+r*g+q*n;s[6]=f*y+e*j+c*p;s[7]=f*x+e*h+c*o;s[8]=f*w+e*g+c*n;return s},multiply_point:function(b,k){var f=k[0],e=k[1],c=k[2],n=k[3],m=k[4],l=k[5],j=k[6],h=k[7],g=k[8];return[f*b[0]+e*b[1]+c*b[2],n*b[0]+m*b[1]+l*b[2],j*b[0]+h*b[1]+g*b[2]]},multiply2:function(v,u,s){var y=v[0],x=v[1],w=v[2],j=v[3],h=v[4],g=v[5],p=v[6],o=v[7],n=v[8],m=u[0],l=u[1],k=u[2],t=u[3],r=u[4],q=u[5],f=u[6],e=u[7],c=u[8];s[0]=y*m+x*t+w*f;s[1]=y*l+x*r+w*e;s[2]=y*k+x*q+w*c;s[3]=j*m+h*t+g*f;s[4]=j*l+h*r+g*e;s[5]=j*k+h*q+g*c;s[6]=p*m+o*t+n*f;s[7]=p*l+o*r+n*e;s[8]=p*k+o*q+n*c;return s},transpose:function(e,c){if(!c||e===c){var g=e[1],f=e[2],b=e[5];e[1]=e[3];e[2]=e[6];e[3]=g;e[5]=e[7];e[6]=f;e[7]=b;return e}c[0]=e[0];c[1]=e[3];c[2]=e[6];c[3]=e[1];c[4]=e[4];c[5]=e[7];c[6]=e[2];c[7]=e[5];c[8]=e[8];return c}}}})})();(function(){EG.define("EG.math.Mat4",function(a){return{statics:{create:function(){return new Float32Array(16)},toPoint:function(b){return[b[0],b[5],b[10],b[15]]},getPoint:function(c){var b=a.create();b[0]=c[0];b[5]=c[1];b[10]=c[2];b[15]=c.length>3?c[3]:1;return b},identity:function(b){b[0]=1;b[1]=0;b[2]=0;b[3]=0;b[4]=0;b[5]=1;b[6]=0;b[7]=0;b[8]=0;b[9]=0;b[10]=1;b[11]=0;b[12]=0;b[13]=0;b[14]=0;b[15]=1;return b},multiplyColumn:function(b,f){var g=b[0]*f[0]+b[4]*f[1]+b[8]*f[2]+b[12]*f[3];var h=b[1]*f[0]+b[5]*f[1]+b[9]*f[2]+b[13]*f[3];var c=b[2]*f[0]+b[6]*f[1]+b[10]*f[2]+b[14]*f[3];var e=b[3]*f[0]+b[7]*f[1]+b[11]*f[2]+b[15]*f[3];return[g,h,c,e]},multiply:function(G,D,C){var K=G[0],J=G[1],H=G[2],E=G[3],l=G[4],k=G[5],j=G[6],h=G[7],x=G[8],w=G[9],v=G[10],u=G[11],M=G[12],L=G[13],I=G[14],F=G[15],s=D[0],q=D[1],o=D[2],m=D[3],B=D[4],A=D[5],z=D[6],y=D[7],g=D[8],f=D[9],e=D[10],c=D[11],t=D[12],r=D[13],p=D[14],n=D[15];C[0]=s*K+q*l+o*x+m*M;C[1]=s*J+q*k+o*w+m*L;C[2]=s*H+q*j+o*v+m*I;C[3]=s*E+q*h+o*u+m*F;C[4]=B*K+A*l+z*x+y*M;C[5]=B*J+A*k+z*w+y*L;C[6]=B*H+A*j+z*v+y*I;C[7]=B*E+A*h+z*u+y*F;C[8]=g*K+f*l+e*x+c*M;C[9]=g*J+f*k+e*w+c*L;C[10]=g*H+f*j+e*v+c*I;C[11]=g*E+f*h+e*u+c*F;C[12]=t*K+r*l+p*x+n*M;C[13]=t*J+r*k+p*w+n*L;C[14]=t*H+r*j+p*v+n*I;C[15]=t*E+r*h+p*u+n*F;return C},scale:function(b,c,e){e[0]=b[0]*c[0];e[1]=b[1]*c[0];e[2]=b[2]*c[0];e[3]=b[3]*c[0];e[4]=b[4]*c[1];e[5]=b[5]*c[1];e[6]=b[6]*c[1];e[7]=b[7]*c[1];e[8]=b[8]*c[2];e[9]=b[9]*c[2];e[10]=b[10]*c[2];e[11]=b[11]*c[2];e[12]=b[12];e[13]=b[13];e[14]=b[14];e[15]=b[15];return e},translate:function(b,c,e){e[0]=b[0];e[1]=b[1];e[2]=b[2];e[3]=b[3];e[4]=b[4];e[5]=b[5];e[6]=b[6];e[7]=b[7];e[8]=b[8];e[9]=b[9];e[10]=b[10];e[11]=b[11];e[12]=b[0]*c[0]+b[4]*c[1]+b[8]*c[2]+b[12];e[13]=b[1]*c[0]+b[5]*c[1]+b[9]*c[2]+b[13];e[14]=b[2]*c[0]+b[6]*c[1]+b[10]*c[2]+b[14];e[15]=b[3]*c[0]+b[7]*c[1]+b[11]*c[2]+b[15];return e},rotateY:function(b,g){var c=Math.sin(g),f=Math.cos(g);var e=[f,0,c,0,1,0,-c,0,f];return a.multiply3(b,e)},rotateX:function(b,g){var c=Math.sin(g),f=Math.cos(g);var e=[1,0,0,0,f,-c,0,c,f];return a.multiply3(b,e)},rotateZ:function(b,g){var c=Math.sin(g),f=Math.cos(g);var e=[f,-c,0,c,f,0,0,0,1];return a.multiply3(b,e)},multiply3:function(e,g){var j=g[0],h=g[1],f=g[2],c=g[3],b=g[4],n=g[5],m=g[6],l=g[7],k=g[8];return[e[0]*j+e[1]*h+e[2]*f,e[0]*c+e[1]*b+e[2]*n,e[0]*m+e[1]*l+e[2]*k]},add3:function(c,b){return[c[0]+b[0]+b[3]+b[6],c[1]+b[4]+b[7]+b[1],c[2]+b[8]+b[2]+b[5]]},rotate:function(ac,ab,b,F){var W=Math.sqrt(b[0]*b[0]+b[1]*b[1]+b[2]*b[2]);if(!W){return null}var H=b[0],E=b[1],C=b[2];if(W!=1){W=1/W;H*=W;E*=W;C*=W}var ae=Math.sin(ab),ad=Math.cos(ab),aa=1-ad,Z=ac[0],Y=ac[1],X=ac[2],V=ac[3],U=ac[4],T=ac[5],S=ac[6],R=ac[7],Q=ac[8],P=ac[9],N=ac[10],M=ac[11],L=H*H*aa+ad,K=E*H*aa+C*ae,J=C*H*aa-E*ae,I=H*E*aa-C*ae,G=E*E*aa+ad,D=C*E*aa+H*ae,B=H*C*aa+E*ae,c=E*C*aa-H*ae,O=C*C*aa+ad;if(ab){if(ac!=F){F[12]=ac[12];F[13]=ac[13];F[14]=ac[14];F[15]=ac[15]}}else{F=ac}F[0]=Z*L+U*K+Q*J;F[1]=Y*L+T*K+P*J;F[2]=X*L+S*K+N*J;F[3]=V*L+R*K+M*J;F[4]=Z*I+U*G+Q*D;F[5]=Y*I+T*G+P*D;F[6]=X*I+S*G+N*D;F[7]=V*I+R*G+M*D;F[8]=Z*B+U*c+Q*O;F[9]=Y*B+T*c+P*O;F[10]=X*B+S*c+N*O;F[11]=V*B+R*c+M*O;return F},lookAt:function(w,x,m,k){var q=w[0],o=w[1],j=w[2],A=m[0],z=m[1],y=m[2],h=x[0],g=x[1],f=x[2];if(q==h&&o==g&&j==f){return this.identity(k)}var v,u,t,e,c,b,r,p,n,s;r=q-h;p=o-g;n=j-f;s=1/Math.sqrt(r*r+p*p+n*n);r*=s;p*=s;n*=s;v=z*n-y*p;u=y*r-A*n;t=A*p-z*r;s=Math.sqrt(v*v+u*u+t*t);if(!s){v=0;u=0;t=0}else{s=1/s;v*=s;u*=s;t*=s}e=p*t-n*u;c=n*v-r*t;b=r*u-p*v;s=Math.sqrt(e*e+c*c+b*b);if(!s){e=0;c=0;b=0}else{s=1/s;e*=s;c*=s;b*=s}k[0]=v;k[1]=e;k[2]=r;k[3]=0;k[4]=u;k[5]=c;k[6]=p;k[7]=0;k[8]=t;k[9]=b;k[10]=n;k[11]=0;k[12]=-(v*q+u*o+t*j);k[13]=-(e*q+c*o+b*j);k[14]=-(r*q+p*o+n*j);k[15]=1;return k},perspective:function(g,f,j,h,n){var o=j*Math.tan(g*Math.PI/360);var e=o*f;var m=e*2,l=o*2,k=h-j;n[0]=j*2/m;n[1]=0;n[2]=0;n[3]=0;n[4]=0;n[5]=j*2/l;n[6]=0;n[7]=0;n[8]=0;n[9]=0;n[10]=-(h+j)/k;n[11]=-1;n[12]=0;n[13]=0;n[14]=-(h*j*2)/k;n[15]=0;return n},transpose:function(c,b){b[0]=c[0];b[1]=c[4];b[2]=c[8];b[3]=c[12];b[4]=c[1];b[5]=c[5];b[6]=c[9];b[7]=c[13];b[8]=c[2];b[9]=c[6];b[10]=c[10];b[11]=c[14];b[12]=c[3];b[13]=c[7];b[14]=c[11];b[15]=c[15];return b},inverse:function(ab,G){var ag=ab[0],af=ab[1],ae=ab[2],ad=ab[3],ac=ab[4],aa=ab[5],Z=ab[6],Y=ab[7],X=ab[8],W=ab[9],V=ab[10],U=ab[11],T=ab[12],S=ab[13],R=ab[14],Q=ab[15],O=ag*aa-af*ac,M=ag*Z-ae*ac,L=ag*Y-ad*ac,K=af*Z-ae*aa,J=af*Y-ad*aa,I=ae*Y-ad*Z,H=X*S-W*T,F=X*R-V*T,E=X*Q-U*T,D=W*R-V*S,P=W*Q-U*S,N=V*Q-U*R,C=1/(O*N-M*P+L*D+K*E-J*F+I*H);G[0]=(aa*N-Z*P+Y*D)*C;G[1]=(-af*N+ae*P-ad*D)*C;G[2]=(S*I-R*J+Q*K)*C;G[3]=(-W*I+V*J-U*K)*C;G[4]=(-ac*N+Z*E-Y*F)*C;G[5]=(ag*N-ae*E+ad*F)*C;G[6]=(-T*I+R*L-Q*M)*C;G[7]=(X*I-V*L+U*M)*C;G[8]=(ac*P-aa*E+Y*H)*C;G[9]=(-ag*P+af*E-ad*H)*C;G[10]=(T*J-S*L+Q*O)*C;G[11]=(-X*J+W*L-U*O)*C;G[12]=(-ac*D+aa*F-Z*H)*C;G[13]=(ag*D-af*F+ae*H)*C;G[14]=(-T*K+S*M-R*O)*C;G[15]=(X*K-W*M+V*O)*C;return G},toInverseMat3:function(q,o){var g=q[0],f=q[1],e=q[2],s=q[4],r=q[5],p=q[6],l=q[8],k=q[9],j=q[10],h=j*r-p*k,c=-j*s+p*l,n=k*s-r*l,m=g*h+f*c+e*n,b;if(!m){return null}b=1/m;if(!o){o=mat3.create()}o[0]=h*b;o[1]=(-j*f+e*k)*b;o[2]=(p*f-e*r)*b;o[3]=c*b;o[4]=(j*g-e*l)*b;o[5]=(-p*g+e*s)*b;o[6]=n*b;o[7]=(-k*g+f*l)*b;o[8]=(r*g-f*s)*b;return o}}}})})();(function(){EG.define("EG.G",{statics:{type:""}})})();(function(){EG.define("EG.G.SVG",{statics:{getMousePos:function(b,f,e){var c=f.getScreenCTM();var a=e.createSVGPoint();a.x=b.clientX;a.y=b.clientY;a=a.matrixTransform(c.inverse());return a}}})})();(function(){EG.define("EG.GL",function(a){return{statics:{getContext:function(b){var h=["webgl","experimental-webgl"];var f=null;for(var c=0;c<h.length;c++){try{f=b.getContext(h[c])}catch(g){}if(f){break}}if(f){f.viewportWidth=b.width;f.viewportHeight=b.height}else{alert("Failed to create Webgl.context!")}return f},SHADER_TYPE:{vertex:"vertex",fragment:"fragment"},sharpTypes:{},registSharp:function(c,b){a.sharpTypes[c]=b},createSharp:function(b){var c=a.sharpTypes[b.type];if(!c){c=EG._defaultLoader.find(b.type)}return new c(b)},createShader:function(f,e,b){var c=f.createShader(b);f.shaderSource(c,e);f.compileShader(c);if(!f.getShaderParameter(c,f.COMPILE_STATUS)){throw new Error("WARN:"+f.getShaderInfoLog(c))}return c},createTexture:function(e,c,f){var b=new Image();b.onload=function(){var g=e.createTexture();e.bindTexture(e.TEXTURE_2D,g);e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,b);e.generateMipmap(e.TEXTURE_2D);e.bindTexture(e.TEXTURE_2D,null);f.apply(this,[g])};b.src=c},createVBO:function(e,b){var c=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,c);e.bufferData(e.ARRAY_BUFFER,new Float32Array(b),e.STATIC_DRAW);e.bindBuffer(e.ARRAY_BUFFER,null);return c},hex2Rgb:function(e){e=e.toLowerCase();var c=[];for(var b=1;b<7;b+=2){c.push(parseInt("0x"+e.slice(b,b+2))/255)}return c}}}})})();(function(){EG.define("EG.gl.Camera",["EG.math.Vec2","EG.math.Vec3","EG.math.Mat2","EG.math.Mat3","EG.math.Mat4"],function(f,c,e,b,a){return{config:{cone:60,near:0.1,far:500,zoomRate:0.1},constructor:function(g){this.initConfig(g);this.init()},init:function(){if(!this.position){this.position=[10,10,10]}if(!this.center){this.center=[0,0,0]}if(!this.normal){this.normal=[0,1,0]}this.setPosition(this.position);this.setCenter(this.center);this.setNormal(this.normal)},setNormal:function(g){this.normal[0]=g[0];this.normal[1]=g[1];this.normal[2]=g[2];this.prepare()},setPosition:function(g){this.position[0]=g[0];this.position[1]=g[1];this.position[2]=g[2];this.prepare()},setCenter:function(g){this.center[0]=g[0];this.center[1]=g[1];this.center[2]=g[2];this.prepare()},prepare:function(){if(!this.center||!this.position||!this.scene){return}if(!this.directLine){this.directLine=new EG.gl.Line()}this.directLine.setStart(this.position);this.directLine.setEnd(this.center);if(!this.vMatrix){this.vMatrix=a.identity(a.create());this.pMatrix=a.identity(a.create())}a.lookAt(this.position,this.center,this.normal,this.vMatrix);this.scene.gl.uniformMatrix4fv(this.scene.vs.uModelViewMatrix,false,this.vMatrix);a.perspective(this.cone,this.scene.rateWH,this.near,this.far,this.pMatrix);this.scene.gl.uniformMatrix4fv(this.scene.vs.uProjectionMatrix,false,this.pMatrix);var g=a.identity(a.create());a.inverse(g,g);this.scene.gl.uniformMatrix4fv(this.scene.vs.uNormalMatrix,false,g);if(!this.direction||!this.scene){return}this.scene.vs.setCameraPosition(this.position)},setScene:function(g){this.scene=g;this.prepare()},getPointByNear2DPos:function(j,g){if(g==null){g=false}if(!g){}var h=this.directLine.clone()},getVerticalLine:function(g,h){},getNearPlane:function(){},getFarPlane:function(){},getDirectLine:function(){return this.directLine},planeMoveUp:function(){this.planeMove(Math.PI*-1)},planeMoveLeft:function(){this.planeMove(Math.PI*0.5)},planeMoveDown:function(){this.planeMove(Math.PI*0)},planeMoveRight:function(){this.planeMove(Math.PI*1.5)},planeMove:function(h){var l=this;var p=[this.position[0],this.position[2]];var n=[this.center[0],this.center[2]];var g=f.getLength(p,n)*0.1;var q=f.getLinePoint(p,n,g);var r=e.rotate(q,p,h);var v=[r[0]-p[0],r[1]-p[1]];var o=v[0];var k=40/2;var u=v[0]/k;var t=v[1]/k;if(this.tMove!=null){if(this.planeMove_angle==h){this.planeMove_speed=Math.min(this.planeMove_speed*1.01,4)}window.clearTimeout(this.tMove);this.tMove=null}this.planeMove_angle=h;if(this.planeMove_speed==null){this.planeMove_speed=1}var j=0;var m=function(){if(j>k){l.planeMove_speed=1;l.tMove=null;return}j++;l.setPosition([l.position[0]+u*l.planeMove_speed,l.position[1],l.position[2]+t*l.planeMove_speed]);l.setCenter([l.center[0]+u*l.planeMove_speed,l.center[1],l.center[2]+t*l.planeMove_speed]);l.tMove=setTimeout(m,10)};m()},zoom:function(g){var h=c.add(this.position,[(this.position[0]-this.center[0])*g,(this.position[1]-this.center[1])*g,(this.position[2]-this.center[2])*g],[]);this.setPosition(h)},rotateY:function(j){var h=[this.position[0]-this.center[0],this.position[1]-this.center[1],this.position[2]-this.center[2]];var g=a.rotateY(h,j);this.setPosition([g[0]+this.center[0],g[1]+this.center[1],g[2]+this.center[2]])},clone:function(){return new ME({position:this.position,center:this.center,normal:this.normal,cone:this.cone,far:this.far})}}})})();(function(){var a=["precision mediump float;","uniform sampler2D uTexture;","uniform bool uUseTexture		;","uniform bool uDecriptTexture	;","varying vec4 vPosition;","varying vec4 vColor;","varying vec2 vTextureCoord;","varying vec3 vTransformedNormal;","varying float fLightWeightning;","void main()","{","	if (!uUseTexture) {","		gl_FragColor  = vColor;","	}else{","		vec4 smpColor = texture2D(uTexture, vTextureCoord);","		if(uDecriptTexture){","			smpColor.brg = smpColor.rgb;","			smpColor.r = 1.0 - smpColor.r;","			smpColor.g = 1.0 - smpColor.g;","			smpColor.b = 1.0 - smpColor.b;","		}","		vec3 nColor =  smpColor.rgb * fLightWeightning;","		gl_FragColor  = vec4(nColor,1.0);//vec4(diffuse+specular,smpColor.a);//*vColor;","	}","}"].join("\n");EG.define("EG.gl.FragmentShader",{constructor:function(c,b){this.gl=c;this.program=b;this.shader=EG.GL.createShader(c,a,c.FRAGMENT_SHADER);c.attachShader(b,this.shader)},init:function(){this.uTexture=this._getU("uTexture");this.uUseTexture=this._getU("uUseTexture");this.uDecriptTexture=this._getU("uDecriptTexture")},_getU:function(b){return this.gl.getUniformLocation(this.program,b)},_getA:function(b){return this.gl.getAttribLocation(this.program,b)},setUseTexture:function(b){this.gl.uniform1i(this.uUseTexture,b)},setDecriptTexture:function(b){this.gl.uniform1i(this.uDecriptTexture,b)}})})();(function(){EG.define("EG.GL",function(a){return{statics:{getContext:function(b){var h=["webgl","experimental-webgl"];var f=null;for(var c=0;c<h.length;c++){try{f=b.getContext(h[c])}catch(g){}if(f){break}}if(f){f.viewportWidth=b.width;f.viewportHeight=b.height}else{alert("Failed to create Webgl.context!")}return f},SHADER_TYPE:{vertex:"vertex",fragment:"fragment"},sharpTypes:{},registSharp:function(c,b){a.sharpTypes[c]=b},createSharp:function(b){var c=a.sharpTypes[b.type];if(!c){c=EG._defaultLoader.find(b.type)}return new c(b)},createShader:function(f,e,b){var c=f.createShader(b);f.shaderSource(c,e);f.compileShader(c);if(!f.getShaderParameter(c,f.COMPILE_STATUS)){throw new Error("WARN:"+f.getShaderInfoLog(c))}return c},createTexture:function(e,c,f){var b=new Image();b.onload=function(){var g=e.createTexture();e.bindTexture(e.TEXTURE_2D,g);e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,b);e.generateMipmap(e.TEXTURE_2D);e.bindTexture(e.TEXTURE_2D,null);f.apply(this,[g])};b.src=c},createVBO:function(e,b){var c=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,c);e.bufferData(e.ARRAY_BUFFER,new Float32Array(b),e.STATIC_DRAW);e.bindBuffer(e.ARRAY_BUFFER,null);return c},hex2Rgb:function(e){e=e.toLowerCase();var c=[];for(var b=1;b<7;b+=2){c.push(parseInt("0x"+e.slice(b,b+2))/255)}return c}}}})})();(function(){EG.define("EG.gl.ObjParser",[],function(a){var b=function(f,c){for(var e=0;e<c.length;e++){f.push(parseFloat(c[e]))}return f};return{statics:{parse:function(c){var g=c.obj;var f=c.mtl;var e={};if(g){EG.copy(e,a.parseObj(g),true)}if(f){EG.copy(e,a.parseMtl(f),true)}return e},parseObj:function(B){var g,v;var D=[];var k=null;var f="";var N=[];var o=[];var t=[];var n=function(c){if(g&&g.mesh[0].f.length==0&&g.name==null){return g}if(c){g={name:null,mesh:[]};u(true);D.push(g)}return g};var u=function(c){if(v==null||c){v={f:[],v:[],vn:[],vt:[],usemtl:null};g.mesh.push(v)}return v};g=n(true);var F=B.split("\n");var w=[];var T=-1;var M=null;var A="TRIANGLES";for(var S=0;S<F.length;S++){var P=F[S].trim();if(P==""){continue}if(P.indexOf("#")==0){if(P.indexOf("# object ")==0){k=P.substring("# object ".length)}continue}var Q=P.split(/\s+/);var U=Q.shift();if(U=="mtllib"){if(Q.length<1){w.push(S+"行mtllib值为空");continue}f=Q[0]}else{if(U=="usemtl"){if(v.usemtl&&Q[0]!=v.usemtl){u(true)}v.usemtl=Q[0]}else{if(U=="v"){if(Q.length<3){w.push(S+"行v数量小于3");continue}N.push(Q[0]);N.push(Q[1]);N.push(Q[2])}else{if(U=="vt"){if(Q.length<2){w.push(S+"行vt数量小于2");continue}o.push(Q[0]);o.push(Q[1])}else{if(U=="vn"){if(Q.length<3){w.push(S+"行vn数量小于3");continue}t.push(Q[0]);t.push(Q[1]);t.push(Q[2])}else{if(U=="g"){}else{if(U=="s"){}else{if(U=="o"){n(true);g.name=Q[0]}else{if(U=="f"){var O=[];for(var R=0;R<Q.length;R++){var C={};var E=Q[R].split("/");if(E.length==0){throw new Error("f长度不支持["+E.length+"]"+Q[R])}var m=E[0],l,h;if(E.length==1){l=m;h=m}else{if(E.length==2){l=m;h=null}else{l=E[1];h=E[2]}}if(m!=null&&N.length>0){var L=(parseInt(m)-1)*3;var s=L+0;var q=L+1;var p=L+2;if(N.length<=s||N.length<=q||N.length<=p){throw new Error("发现未知")}v.v.push(N[s]);v.v.push(N[q]);v.v.push(N[p])}if(l!=null&&o.length>0){var K=(parseInt(l)-1)*2;var e=K+0;var V=K+1;if(o.length<=e||o.length<=V){throw new Error("发现未知")}v.vt.push(o[e]);v.vt.push(o[V])}if(h!=null&&t.length>0){var J=(parseInt(h)-1)*3;var I=J+0;var H=J+1;var G=J+2;if(t.length<=I||t.length<=H||t.length<=G){throw new Error("发现未知")}v.vn.push(t[I]);v.vn.push(t[H]);v.vn.push(t[G])}if(v.fCount==null){v.fCount=0}else{v.fCount++}v.f.push(v.fCount)}}}}}}}}}}}if(w.length>0){console.log("OBJ解析错误:"+w)}return{name:k,o:D,mtllib:f}},parseMtl:function(q){var g=q.split("\n");var o=[];var l={};var e={};for(var h=0;h<g.length;h++){var c=g[h].trim();if(c==""){continue}if(c.indexOf("#")==0){continue}var p=c.split(/\s+/);var n=p.shift();if(n=="newmtl"){e={};l[p[0]]=e}else{if(n=="Tf"||n=="Ka"||n=="Kd"||n=="Ks"||n=="Ke"){var j=[];for(var f=0;f<p.length;f++){j.push(parseFloat(p[f]))}e[n]=j}else{if(n=="Ns"||n=="Ni"||n=="d"||n=="Tr"||n=="illum"||n=="map_Ka"||n=="map_Kd"){e[n]=c.substr(n.length+1)}}}}if(o.length>0){console.log("MTL解析错误:"+o)}return l}}}})})();(function(){EG.define("EG.gl.ObjParserTest",[],function(a){var b=function(f,c){for(var e=0;e<c.length;e++){f.push(parseFloat(c[e]))}return f};return{statics:{parse:function(c){var g=c.obj;var f=c.mtl;var e={};if(g){EG.copy(e,a.parseObj(g),true)}if(f){EG.copy(e,a.parseMtl(f),true)}return e},parseObj:function(B){var g,v;var D=[];var k=null;var f="";var N=[];var o=[];var t=[];var n=function(c){if(g&&g.mesh[0].f.length==0&&g.name==null){return g}if(c){g={name:null,mesh:[]};u(true);D.push(g)}return g};var u=function(c){if(v==null||c){v={f:[],v:[],vn:[],vt:[],usemtl:null};g.mesh.push(v)}return v};g=n(true);var F=B.split("\n");var w=[];var T=-1;var M=null;var A="TRIANGLES";for(var S=0;S<F.length;S++){var P=F[S].trim();if(P==""){continue}if(P.indexOf("#")==0){if(P.indexOf("# object ")==0){k=P.substring("# object ".length)}continue}var Q=P.split(/\s+/);var U=Q.shift();if(U=="mtllib"){if(Q.length<1){w.push(S+"行mtllib值为空");continue}f=Q[0]}else{if(U=="usemtl"){if(v.usemtl&&Q[0]!=v.usemtl){u(true)}v.usemtl=Q[0]}else{if(U=="v"){if(Q.length<3){w.push(S+"行v数量小于3");continue}N.push(Q[0]);N.push(Q[1]);N.push(Q[2])}else{if(U=="vt"){if(Q.length<2){w.push(S+"行vt数量小于2");continue}o.push(Q[0]);o.push(Q[1])}else{if(U=="vn"){if(Q.length<3){w.push(S+"行vn数量小于3");continue}t.push(Q[0]);t.push(Q[1]);t.push(Q[2])}else{if(U=="g"){}else{if(U=="s"){}else{if(U=="o"){n(true);g.name=Q[0]}else{if(U=="f"){var O=[];for(var R=0;R<Q.length;R++){var C={};var E=Q[R].split("/");if(E.length==0){throw new Error("f长度不支持["+E.length+"]"+Q[R])}var m=E[0],l,h;if(E.length==1){l=m;h=m}else{if(E.length==2){l=m;h=null}else{l=E[1];h=E[2]}}if(m!=null&&N.length>0){var L=(parseInt(m)-1)*3;var s=L+0;var q=L+1;var p=L+2;if(N.length<=s||N.length<=q||N.length<=p){throw new Error("发现未知")}v.v.push(N[s]);v.v.push(N[q]);v.v.push(N[p])}if(l!=null&&o.length>0){var K=(parseInt(l)-1)*2;var e=K+0;var V=K+1;if(o.length<=e||o.length<=V){throw new Error("发现未知")}v.vt.push(o[e]);v.vt.push(o[V])}if(h!=null&&t.length>0){var J=(parseInt(h)-1)*3;var I=J+0;var H=J+1;var G=J+2;if(t.length<=I||t.length<=H||t.length<=G){throw new Error("发现未知")}v.vn.push(t[I]);v.vn.push(t[H]);v.vn.push(t[G])}if(v.fCount==null){v.fCount=0}else{v.fCount++}v.f.push(v.fCount)}}}}}}}}}}}if(w.length>0){console.log("OBJ解析错误:"+w)}return{name:k,o:D,mtllib:f}},parseMtl:function(q){var g=q.split("\n");var o=[];var l={};var e={};for(var h=0;h<g.length;h++){var c=g[h].trim();if(c==""){continue}if(c.indexOf("#")==0){continue}var p=c.split(/\s+/);var n=p.shift();if(n=="newmtl"){e={};l[p[0]]=e}else{if(n=="Tf"||n=="Ka"||n=="Kd"||n=="Ks"||n=="Ke"){var j=[];for(var f=0;f<p.length;f++){j.push(parseFloat(p[f]))}e[n]=j}else{if(n=="Ns"||n=="Ni"||n=="d"||n=="Tr"||n=="illum"||n=="map_Ka"||n=="map_Kd"){e[n]=c.substr(n.length+1)}}}}if(o.length>0){console.log("MTL解析错误:"+o)}return l}}}})})();(function(){EG.define("EG.gl.Oper",["EG.math.Mat4"],function(a,b){return{config:{},constructor:function(c){this.scene=c.scene;this.element=this.scene.getElement();this.operType=null;EG.CE({ele:this.scene.getElement(),onmousewheel:this.onmousewheel,onmousewheelSrc:this,onmousedown:this.onmousedown,onmousedownSrc:this,onmousemove:this.onmousemove,onmousemoveSrc:this,onmouseup:this.onmouseup,onmouseupSrc:this,onmouseout:this.onmouseout,onmouseoutSrc:this,onkeydown:this.onkeydown,onkeydownSrc:this,onkeyup:this.onkeyup,onkeyupSrc:this})},onmousewheel:function(g){var f=this.scene;var c=this.scene.camera;if(g.wheelDelta>0){c.zoom(f.zoomInRate)}else{c.zoom(f.zoomOutRate)}},onmousedown:function(m){var l=this.scene;var k=this.scene.camera;this.startPoint=EG.Tools.getMousePos(m,this.element);if(m.ctrlKey){if(EG.Tools.isPressLeft(m)){this.operType="ROTATE"}}if(!this.operType){}else{if(this.operType=="ADD_ITEM"){if(l.w4adds){var f=l.getPickupRay(this.startPoint);var c=l.getCrossXZ(f);var j=null;while((j=l.w4adds.shift())!=null){j.setPosition(c);l.addItem(j)}}}else{if(this.operType=="REMOVE_ITEM"){var f=l.getPickupRay(this.startPoint);var g=l.pickup(f);for(var h=0;h<g.length;h++){l.removeItem(g[h])}}else{if(this.operType=="MOVE_ITEM"){var f=l.getPickupRay(this.startPoint);var g=l.pickup(f);if(g&&g.length>0){l.pickupItems=g}}else{if(this.operType=="ROTATE_ITEM"){var f=l.getPickupRay(this.startPoint);var g=l.pickup(f);if(g&&g.length>0){l.pickupItems=g}}}}}}},onmousemove:function(k){var h=this.scene;var l=this.scene.camera;if(!this.operType){}else{if(this.operType=="ROTATE"){var f=EG.Tools.getMousePos(k,this.element);var n=f.x-this.startPoint.x;var c=(n/this.scene.width)*Math.PI*0.6;l.rotateY(-c);this.startPoint.x=f.x;this.startPoint.y=f.y}else{if(this.operType=="ROTATE_ITEM"){if(EG.Tools.isPressLeft(k)&&h.pickupItems&&h.pickupItems.length>0){var f=EG.Tools.getMousePos(k,this.element);var n=f.x-this.startPoint.x;var c=(n/this.scene.width)*Math.PI*0.6;for(var g=0;g<h.pickupItems.length;g++){var o=h.pickupItems[g];h.pickupItems[g].rotateY(c)}this.startPoint.x=f.x;this.startPoint.y=f.y}}else{if(this.operType=="MOVE_ITEM"){if(EG.Tools.isPressLeft(k)&&h.pickupItems&&h.pickupItems.length>0){var f=EG.Tools.getMousePos(k,this.element);var m=h.getPickupRay(f);var j=h.getCrossXZ(m);for(var g=0;g<h.pickupItems.length;g++){var o=h.pickupItems[g];o.setPosition([j[0],o.position[1],j[2],])}}}else{if(this.operType=="ROTATE_ITEM"){}}}}}},onmouseup:function(c){if(this.operType=="MOVE_ITEM"||this.operType=="ROTATE_ITEM"){}else{if(this.operType=="ROTATE"){this.operType=""}}},onmouseout:function(){if(!this.operType){return}this.startCameraPosition=null},onkeydown:function(j){var f=this.scene.camera;if(!this.operType){var h,c,k,g;if((h=(j.keyCode==87||j.keyCode==38))||(c=(j.keyCode==83||j.keyCode==40))||(k=(j.keyCode==65||j.keyCode==37))||(g=(j.keyCode==68||j.keyCode==39))){if(k){f.planeMoveLeft()}else{if(c){f.planeMoveDown()}else{if(g){f.planeMoveRight()}else{f.planeMoveUp()}}}}else{}}},onkeyup:function(c){if(!this.operType){return}},isRotate:function(c){return EG.Tools.isPressRight(c)&&c.ctrlKey}}})})();(function(){EG.define("EG.gl.PointLight",[],function(b,a,c){return{config:{position:null,intensity:null,},constructor:function(e){this.initConfig(e)},setScene:function(e){this.scene=e;this.prepare()},prepare:function(){}}})})();(function(){EG.define("EG.gl.Renderer",{config:{fps:100},constructor:function(a){this.initConfig(a);this.running=false;this.setFps(this.fps)},setFps:function(a){this.fps=a;this.timeout=1000/this.fps},bindScene:function(a){this.scene=a},start:function(){var b=this;this.running=true;var a=function(){if(!b.running){return}b.scene.render();setTimeout(a,b.timeout)};a()},stop:function(){this.running=false}})})();(function(){EG.define("EG.gl.Scene",["EG.math.Mat4","EG.Math",],function(b,a,c){return{config:{width:null,height:null,gl:null,showCoordinate:true,zoomAble:true,zoomInRate:-0.1,zoomOutRate:0.1,changeLookinAble:true,pickupAble:true},constructor:function(e){this.initConfig(e);this.rateWH=this.width/this.height;this.items=[];this.lights=[];this.shaderVars={};this.images=[];this.build();this.oper=new EG.gl.Oper({scene:this})},setLoader:function(e){this.loader=e},build:function(){var f=this;this.element=EG.CE({tn:"canvas",tabIndex:1000,width:this.width,height:this.height});this.gl=this.element.getContext("webgl")||this.element.getContext("experimental-webgl");var g=this.gl;g.enable(g.DEPTH_TEST);g.enable(g.CULL_FACE);g.blendFunc(g.SRC_ALPHA,g.ONE);this.program=g.createProgram();var e=this.program;this.vs=new EG.gl.VertexShader(g,e);this.fs=new EG.gl.FragmentShader(g,e);g.linkProgram(this.program);if(g.getProgramParameter(this.program,g.LINK_STATUS)){g.useProgram(this.program)}else{throw new Error("WARN:"+g.getProgramInfoLog(this.program))}this.vs.init();this.fs.init();this.coordinate=new EG.gl.Coordinate();this._add(this.coordinate);this.coordinate.init()},getElement:function(){return this.element},_add:function(e){e.scene=this;e.gl=this.gl;e.shaderVars=this.shaderVars;e.init()},addItem:function(e){this._add(e);this.items.push(e)},addLight:function(e){this.lights.push(e)},removeItem:function(e){EG.Array.remove(this.items,e)},bindCamera:function(e){this.camera=e},loadImage:function(g){var f=this;var e=this.images[g];if(e==null){e=EG.CE({tn:"img",onload:function(){f.images[g]=this}});e.src=g;return null}else{return e}},setPickupAble:function(e){this.pickupAble=e},getPickupRay:function(v){var j=[this.element.clientWidth/2,this.element.clientHeight/2];var o=(v.x-j[0])/j[0];var m=(j[1]-v.y)/j[1];var k=0.499;var q=1;var l=[o,m,k,q];var t=b.inverse(this.camera.pMatrix,[]);var e=b.multiplyColumn(t,l);var s=e[0]/e[3];var r=e[1]/e[3];var p=e[2]/e[3];var u=1;var f=[s,r,p,u];var g=b.inverse(this.camera.vMatrix,[]);var h=b.multiplyColumn(g,f);var n=new EG.gl.Line({start:this.camera.position,end:h});return n},getCrossXZ:function(n){var g=n.start[0],m=n.start[1],k=n.start[2],f=n.end[0],l=n.end[1],j=n.end[2];var e=f-((g-f)*l)/(m-l);var h=j-((k-j)*l)/(m-l);return[e,0,h]},pickup:function(e){var f=[];for(var l=0;l<this.items.length;l++){var n=this.items[l];if(!(n instanceof EG.gl.Cube)&&!(n instanceof EG.gl.Geometry)){continue}var m=n.getBoundingBox();var k=null;for(var g=0;g<m.length;g++){var h=m[g];k=this.getCrossTriggle(h[0],h[1],h[2],e);if(k!=null){break}}if(k!=null){f.push(n);break}}return f},getCrossTriggle:function(k,j,g,e){var h=a.triggle_normal(k,j,g);var f=a.interset_point(h,k,e.getDirect(),e.start);if(f==null){return null}if(!a.in_triggle(k,j,g,f)){return null}return f},render:function(){var h=this.gl;h.clearColor(0,0,0,1);h.clearDepth(1);h.clear(h.COLOR_BUFFER_BIT|h.DEPTH_BUFFER_BIT);var f=this.camera;var j=this.vs;if(this.showCoordinate){this.coordinate.render()}for(var e=0;e<this.items.length;e++){this.items[e].render()}var g=h.getError();if(g){console.log(g)}h.flush()},prepareAdd:function(f){if(!this.w4adds){this.w4adds=[]}var e=(!EG.isArray(f))?[f]:f;EG.Array.addAll(this.w4adds,e)}}})})();(function(){EG.define("EG.gl.Sharp",{config:{name:null,title:null,useCoord:false},constructor:function(a){this.initConfig(a)},setScene:function(a){this.scene=a;this.gl=a.gl},createShader:function(b,a){return EG.GL.createShader(this.gl,b,a)},bindShader:function(b,a){this.program=EG.GL.createProgram(this.gl,b,a)},createTexture:function(a,b){return EG.GL.createTexture(this.gl,a,b)},createVBO:function(b,a){return EG.GL.createVBO(this.gl,a)},setVBO:function(c,a,b){var f=this.gl;var g=this.scene.vs;f.bindBuffer(f.ARRAY_BUFFER,f.createBuffer());f.bufferData(f.ARRAY_BUFFER,new Float32Array(c),f.STATIC_DRAW);var e=g[a];f.vertexAttribPointer(e,b,f.FLOAT,false,0,0);f.enableVertexAttribArray(e)},setVBOs:function(){for(var b=0;b<arguments.length;b++){var a=arguments[b];this.setVBO(a.vertex,a.location,a.count)}},setMatrix:function(){var f=this.gl;var c=this.scene.camera;var g=this.scene.vs;var a=new EG.gl.Matrix();var b=a.identity(a.create());var e=a.identity(a.create());a.lookAt(c.position,c.center,c.normal,b);f.uniformMatrix4fv(g.uModelViewMatrix,false,b);a.perspective(c.cone,this.scene.rateWH,c.near,c.far,e);f.uniformMatrix4fv(g.uProjectionMatrix,false,e)},render:function(){},init:function(){},getBoundingBox:function(){return null},rotateY:function(){}})})();(function(){EG.define("EG.gl.SpotLight",[],function(b,a,c){return{config:{position:null,direction:null,intensity:null,},constructor:function(e){this.initConfig(e)},setScene:function(e){this.scene=e;this.prepare()},prepare:function(){if(!this.direction||!this.scene){return}var e=this.scene.fs;var g=this.scene.vs;var f=this.gl;g.setLightDirection(this.direction)}}})})();(function(){var a=["attribute vec3 aPosition;","attribute vec4 aColor;","attribute vec2 aTextureCoord;","attribute vec3 aNormal;","uniform mat4 uModelViewMatrix;","uniform mat4 uProjectionMatrix;","uniform mat4 uNormalMatrix;","uniform bool uUseTexture;","uniform bool uUseAColor;","uniform vec3 uLightPosition;","uniform vec3 uLightDirection;","uniform vec3 uAmbientLightColor;","uniform vec3 uDiffuseLightColor;","uniform vec3 uSpecularLightColor;","uniform vec3 uCameraPosition;","varying vec4 vPosition;","varying vec4 vColor;","varying vec2 vTextureCoord;","varying vec3 vTransformedNormal;","varying float fLightWeightning;","const float shininess = 1.0;","void main()","{","	vPosition 		= uModelViewMatrix * vec4(aPosition, 1.0);","	gl_Position 	= uProjectionMatrix * vPosition;","	vec3  invLight   = normalize(uNormalMatrix * vec4(uLightDirection,0)).xyz;","	vec3  invEye     = normalize(uNormalMatrix * vec4(uCameraPosition,0)).xyz;","	vec3  halfLE     = normalize(invLight + invEye);","	float diffuse    = clamp(dot(aNormal, invLight), 0.0, 1.0) * 0.9;","	float specular   = pow(clamp(dot(aNormal, halfLE), 0.0, 1.0), 70.0);","	fLightWeightning = diffuse + specular;","	if (uUseTexture) {","		vColor= vec4(1,1,1,1);","		vTextureCoord= aTextureCoord;","	}else{","		if(uUseAColor){","			vColor=aColor;","		}else{","			vColor=vec4(uDiffuseLightColor,1);","		}","	}","}"].join("\n");EG.define("EG.gl.VertexShader",{constructor:function(c,b){this.gl=c;this.program=b;this.shader=EG.GL.createShader(c,a,c.VERTEX_SHADER);c.attachShader(b,this.shader)},init:function(){this.aPosition=this._getA("aPosition");this.aColor=this._getA("aColor");this.aTextureCoord=this._getA("aTextureCoord");this.aNormal=this._getA("aNormal");this.uModelViewMatrix=this._getU("uModelViewMatrix");this.uProjectionMatrix=this._getU("uProjectionMatrix");this.uNormalMatrix=this._getU("uNormalMatrix");this.uUseTexture=this._getU("uUseTexture");this.uUseAColor=this._getU("uUseAColor");this.uLightPosition=this._getU("uLightPosition");this.uLightDirection=this._getU("uLightDirection");this.uAmbientLightColor=this._getU("uAmbientLightColor");this.uDiffuseLightColor=this._getU("uDiffuseLightColor");this.uSpecularLightColor=this._getU("uSpecularLightColor");this.uCameraPosition=this._getU("uCameraPosition");this.aDebug=this._getA("aDebug")},_getU:function(b){return this.gl.getUniformLocation(this.program,b)},_getA:function(b){return this.gl.getAttribLocation(this.program,b)},setUseTexture:function(b){this.gl.uniform1i(this.uUseTexture,b)},setUseAColor:function(b){this.gl.uniform1i(this.uUseAColor,b)},setLightPosition:function(b){this.gl.uniform3fv(this.uLightPosition,b)},setLightDirection:function(b){this.gl.uniform3fv(this.uLightDirection,b)},setDiffuseLightColor:function(b){this.gl.uniform3fv(this.uDiffuseLightColor,b)},setAmbientLightColor:function(b){this.gl.uniform3fv(this.uAmbientLightColor,b)},setSpecularLightColor:function(b){this.gl.uniform3fv(this.uSpecularLightColor,b)},setCameraPosition:function(b){this.gl.uniform3fv(this.uCameraPosition,b)},})})();(function(){EG.define("EG.gl.Coordinate",function(a){EG.GL.registSharp("coordinate",a);return{extend:"EG.gl.Sharp",config:{useCoord:false},constructor:function(b){this.callSuper([b])},init:function(){},setPosition:function(b){this.position=b},setColor:function(b){this.color=b},getRenderVertexPosition:function(){return[0,0,0,999999,0,0,0,0,0,0,999999,0,0,0,0,0,0,999999]},getRenderIndices:function(){return[0,2,1,0,3,2,4,6,5,4,7,6,8,10,9,8,11,10,12,14,13,12,15,14,16,18,17,16,19,18,20,21,22,20,22,23]},getRenderVertexColor:function(){return[1,0,0,1,1,0,0,1,0,1,0,1,0,1,0,1,0,0,1,1,0,0,1,1]},render:function(){var c=this.gl;var b=this.scene.fs;var e=this.scene.vs;e.setUseAColor(true);e.setUseTexture(false);b.setUseTexture(false);c.disableVertexAttribArray(e.aTextureCoord);this.setVBOs({vertex:this.getRenderVertexPosition(),location:"aPosition",count:3},{vertex:this.getRenderVertexColor(),location:"aColor",count:4});c.drawArrays(c.LINES,0,6)}}})})();(function(){EG.define("EG.gl.Cube",["EG.math.Mat4"],function(a,b){EG.GL.registSharp("cube",b);return{extend:"EG.gl.Sharp",config:{position:null,size:null,color:null,texture:null,textureRepeated:1,rotatedY:0,},constructor:function(c){this.callSuper([c]);this.surface={};if(c.position){this.setPosition(c.position)}if(c.size){this.setSize(c.size)}if(c.rotatedY){this.rotateY(c.rotatedY)}},init:function(){},setSize:function(c){this.size=c;this.prepareVertex()},setPosition:function(c){this.position=c;this.prepareVertex()},fillSurface:function(c){for(var e in c){this.surface[e]=c[e]||{};var f=this.surface[e];if(f.image){this.scene.loadImage(f.image,function(){})}}},prepareVertex:function(){if(!this.size||!this.position){return}var f=this.size[0]/2;var c=this.size[1]/2;var r=this.size[2]/2;var p=this.position[0];var n=this.position[1];var m=this.position[2];var j=[+f,+c,+r];var q=[+f,-c,+r];var l=[-f,-c,+r];var h=[-f,+c,+r];var g=[+f,+c,-r];var o=[+f,-c,-r];var k=[-f,-c,-r];var e=[-f,+c,-r];if(this.rotateY!=0){j=a.rotateY(j,this.rotatedY);q=a.rotateY(q,this.rotatedY);l=a.rotateY(l,this.rotatedY);h=a.rotateY(h,this.rotatedY);g=a.rotateY(g,this.rotatedY);o=a.rotateY(o,this.rotatedY);k=a.rotateY(k,this.rotatedY);e=a.rotateY(e,this.rotatedY)}j=[p+j[0],n+j[1],m+j[2]];q=[p+q[0],n+q[1],m+q[2]];l=[p+l[0],n+l[1],m+l[2]];h=[p+h[0],n+h[1],m+h[2]];g=[p+g[0],n+g[1],m+g[2]];o=[p+o[0],n+o[1],m+o[2]];k=[p+k[0],n+k[1],m+k[2]];e=[p+e[0],n+e[1],m+e[2]];this.vertexPosition=[].concat(j,q,l,h,g,o,q,j,e,k,o,g,h,l,k,e,g,j,h,e,q,o,k,l)},getRenderVertexPosition:function(){return this.vertexPosition},getRenderTextureCoord:function(){var c=1*this.textureRepeated;return[c,0,c,c,0,c,0,0,c,0,c,c,0,c,0,0,c,0,c,c,0,c,0,0,c,0,c,c,0,c,0,0,c,0,c,c,0,c,0,0,c,0,c,c,0,c,0,0]},getRenderIndices:function(){return[0,2,1,0,3,2,4,6,5,4,7,6,8,10,9,8,11,10,12,14,13,12,15,14,16,18,17,16,19,18,20,21,22,20,22,23]},getRenderVertexColor:function(){var g="#FFFFFF";var l=1;var n=["front","right","back","left","top","bottom"];var j=[];for(var h=0;h<n.length;h++){var f=n[h];var m=this.surface[f]||{};var e=EG.GL.hex2Rgb(m.color||g).concat(m.opacity||l);j=j.concat(e,e,e,e)}return j},render:function(){var g=this;var k=this.gl;var c=this.scene.fs;var l=this.scene.vs;var e=this.getRenderVertexPosition();if(e==null){return}l.setUseTexture(false);c.setUseTexture(false);k.disableVertexAttribArray(l.aTextureCoord);k.bindBuffer(k.ARRAY_BUFFER,k.createBuffer());k.bufferData(k.ARRAY_BUFFER,new Float32Array(e),k.STATIC_DRAW);k.vertexAttribPointer(l.aPosition,3,k.FLOAT,false,0,0);k.enableVertexAttribArray(l.aPosition);k.bindBuffer(k.ARRAY_BUFFER,k.createBuffer());k.bufferData(k.ARRAY_BUFFER,new Float32Array(this.getRenderVertexColor()),k.STATIC_DRAW);k.vertexAttribPointer(l.aColor,4,k.FLOAT,false,0,0);k.enableVertexAttribArray(l.aColor);if(this.texture){var h=this.scene.loadImage(this.texture);if(h!=null){l.setUseTexture(true);c.setUseTexture(true);k.enableVertexAttribArray(l.aTextureCoord);this.setVBOs({vertex:this.getRenderTextureCoord(),location:"aTextureCoord",count:2});if(!h.texture){var f=k.createTexture();k.bindTexture(k.TEXTURE_2D,f);k.texParameteri(k.TEXTURE_2D,k.TEXTURE_MIN_FILTER,k.LINEAR);k.texImage2D(k.TEXTURE_2D,0,k.RGB,k.RGB,k.UNSIGNED_BYTE,h);k.bindTexture(k.TEXTURE_2D,null);h.texture=f}k.activeTexture(k.TEXTURE0);k.bindTexture(k.TEXTURE_2D,h.texture)}}var j=[0,2,1,0,3,2,4,6,5,4,7,6,8,10,9,8,11,10,];k.bindBuffer(k.ELEMENT_ARRAY_BUFFER,k.createBuffer());k.bufferData(k.ELEMENT_ARRAY_BUFFER,new Uint8Array(j),k.STATIC_DRAW);k.drawElements(k.TRIANGLES,j.length,k.UNSIGNED_BYTE,0);k.bindBuffer(k.ELEMENT_ARRAY_BUFFER,null);j=[12,14,13,12,15,14,16,18,17,16,19,18,20,21,22,20,22,23];k.bindBuffer(k.ELEMENT_ARRAY_BUFFER,k.createBuffer());k.bufferData(k.ELEMENT_ARRAY_BUFFER,new Uint8Array(j),k.STATIC_DRAW);k.drawElements(k.TRIANGLES,j.length,k.UNSIGNED_BYTE,0);k.bindBuffer(k.ELEMENT_ARRAY_BUFFER,null)},getBoundingBox:function(){var f=this.getRenderVertexPosition();var e=[];for(var c=0;c<f.length;){e.push([[f[c+0],f[c+1],f[c+2]],[f[c+3],f[c+4],f[c+5]],[f[c+6],f[c+7],f[c+8]]]);c+=9}return e},rotateY:function(e,c){this.rotatedY+=e;if(this.rotatedY>360){this.rotatedY=this.rotatedY-360}this.prepareVertex()}}})})();(function(){EG.define("EG.gl.Geometry",["EG.math.Mat4"],function(a,b){EG.GL.registSharp("geometry",b);return{extend:"EG.gl.Sharp",config:{position:null,size:null,color:null,texture:null,rotatedY:0,scale:1,decriptTexture:false},constructor:function(c){this.callSuper([c])},load:function(){var e=this;if(this.obj&&typeof(this.obj)=="string"){this.loaded="loading";if(!this.obj_basePath){var c=this.obj.lastIndexOf("/");this.obj_basePath=c>0?this.obj.substr(0,c):""}this.scene.loader.require([{path:this.obj,type:"text"},],function(h){var j=EG.gl.ObjParser.parseObj(h.item);e.obj_model=j;if(j.mtllib){var g=j.mtllib;if(e.obj_basePath){g=e.obj_basePath+"/"+j.mtllib}var f=g.lastIndexOf("/");e.mtl_basePath=f>0?g.substr(0,f):"";e.scene.loader.require([{path:g,type:"text"},],function(k){e.obj_mtl=EG.gl.ObjParser.parseMtl(k.item);e.loaded="loaded"})}else{e.loaded="loaded"}e.prepare()})}else{this.loaded="loaded"}},setPosition:function(c){this.position=c;this.prepare()},setVertices:function(c){this.vertices=c;this.prepare()},init:function(){this.prepare()},prepare:function(){if(!this.scene||!this.position||!this.obj_model){return}var o=this.scene.gl;this.boundingBox=[];for(var g=0;g<this.obj_model.o.length;g++){var c=this.obj_model.o[g].mesh;for(var h=0;h<c.length;h++){var e=c[h];var f=e.v.length/3;var p=[];for(var n=0;n<f;n++){var t=e.v[n*3+0];var r=e.v[n*3+1];var q=e.v[n*3+2];if(this.rotateY!=0){var s=a.rotateY([t,r,q],this.rotatedY);t=s[0];r=s[1];q=s[2]}if(this.scale!=1){t=t*this.scale;r=r*this.scale;q=q*this.scale}t+=this.position[0];r+=this.position[1];q+=this.position[2];p.push(t);p.push(r);p.push(q)}e.v_final=p;for(var n=0;n<p.length;){this.boundingBox.push([[p[h+0],p[h+1],p[h+2]],[p[h+3],p[h+4],p[h+5]],[p[h+6],p[h+7],p[h+8]]]);n+=9}e.buf_v=o.createBuffer();o.bindBuffer(o.ARRAY_BUFFER,e.buf_v);o.bufferData(o.ARRAY_BUFFER,new Float32Array(e.v_final),o.STATIC_DRAW);if(!e.buffered){e.buf_vt=o.createBuffer();o.bindBuffer(o.ARRAY_BUFFER,e.buf_vt);o.bufferData(o.ARRAY_BUFFER,new Float32Array(e.vt),o.STATIC_DRAW);o.bindBuffer(o.ARRAY_BUFFER,null);e.buf_vn=o.createBuffer();o.bindBuffer(o.ARRAY_BUFFER,e.buf_vn);o.bufferData(o.ARRAY_BUFFER,new Float32Array(e.vn),o.STATIC_DRAW);o.bindBuffer(o.ARRAY_BUFFER,null);e.buf_f=o.createBuffer();o.bindBuffer(o.ELEMENT_ARRAY_BUFFER,e.buf_f);o.bufferData(o.ELEMENT_ARRAY_BUFFER,new Uint16Array(e.f),o.STATIC_DRAW);o.bindBuffer(o.ELEMENT_ARRAY_BUFFER,null);e.buffered=true}}}this.prepared=true},getVertices:function(){return this.vertices},setUvs:function(c){this.uvs=c},getUvs:function(){return this.uvs},setFaces:function(c){this.faces=c},getFaces:function(){return this.faces},setColors:function(c){this.colors=c},getColors:function(){return this.colors},getBoundingBox:function(){return this.boundingBox},render:function(){if(!this.loaded){this.load()}if(this.loaded!="loaded"){return}if(!this.prepared){return}for(var e=0;e<this.obj_model.o.length;e++){var f=this.obj_model.o[e].mesh;for(var c=0;c<f.length;c++){this.render_mesh(f[c])}}},render_mesh:function(v){var s=this;var o=this.scene.fs;var u=this.scene.vs;var n=this.gl;var t=this.scene.loader;n.bindBuffer(n.ARRAY_BUFFER,v.buf_v);n.enableVertexAttribArray(u.aPosition);n.vertexAttribPointer(u.aPosition,3,n.FLOAT,false,0,0);n.bindBuffer(n.ARRAY_BUFFER,v.buf_vn);n.enableVertexAttribArray(u.aNormal);n.vertexAttribPointer(u.aNormal,3,n.FLOAT,false,0,0);n.bindBuffer(n.ARRAY_BUFFER,v.buf_vt);n.vertexAttribPointer(u.aTextureCoord,2,n.FLOAT,false,0,0);n.enableVertexAttribArray(u.aTextureCoord);o.setDecriptTexture(!!this.decriptTexture);u.setUseAColor(false);n.disableVertexAttribArray(u.aColor);var f=this.scene.lights;if(f.length>0){var g=f[0];u.setLightPosition(g.position)}var m=false;if(v.usemtl){var r=this.obj_mtl[v.usemtl];u.setAmbientLightColor(r.Ka);u.setDiffuseLightColor(r.Kd);u.setSpecularLightColor(r.Ks);m=!!r.map_Kd;if(m){if(r&&r.map_Kd){var c=r.map_Kd;if(this.mtl_basePath){c=this.mtl_basePath+"/"+c}var e={type:"img",path:EG.Ajax.javaURLEncoding(c)};var l=false;var j=t.find(e);if(!j&&t.failds){for(var h=0;h<t.failds.length;h++){if(t.failds[h]==c){l=true;break}}}if(j){if(!j.texture){var q=n.createTexture();n.bindTexture(n.TEXTURE_2D,q);n.pixelStorei(n.UNPACK_FLIP_Y_WEBGL,true);n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,j.item);n.generateMipmap(n.TEXTURE_2D);n.bindTexture(n.TEXTURE_2D,null);j.texture=q}n.activeTexture(n.TEXTURE0);n.bindTexture(n.TEXTURE_2D,j.texture)}else{if(!l){t.require([e],function(){},function(k){if(!t.failds){t.failds=[]}t.failds.push(c)})}}}else{console.log("未发现:"+v.usemtl+":"+JSON.stringify(this.mtllib))}}else{}}u.setUseTexture(m);o.setUseTexture(m);if(!m){n.disableVertexAttribArray(u.aTextureCoord)}else{n.enableVertexAttribArray(u.aTextureCoord)}n.drawArrays(n.TRIANGLES,0,v.f.length)},rotateY:function(e,c){this.rotatedY+=e;if(this.rotatedY>360){this.rotatedY=this.rotatedY-360}this.prepare()}}})})();(function(){EG.define("EG.gl.GeometryTest",["EG.math.Mat4"],function(a,b){EG.GL.registSharp("geometry",b);return{extend:"EG.gl.Sharp",config:{position:null,size:null,color:null,texture:null,rotatedY:0,scale:1,decriptTexture:false},constructor:function(c){this.callSuper([c])},load:function(){var e=this;if(this.obj&&typeof(this.obj)=="string"){this.loaded="loading";if(!this.obj_basePath){var c=this.obj.lastIndexOf("/");this.obj_basePath=c>0?this.obj.substr(0,c):""}this.scene.loader.require([{path:this.obj,type:"text"},],function(h){var j=EG.gl.ObjParserTest.parseObj(h.item);e.obj_model=j;if(j.mtllib){var g=j.mtllib;if(e.obj_basePath){g=e.obj_basePath+"/"+j.mtllib}var f=g.lastIndexOf("/");e.mtl_basePath=f>0?g.substr(0,f):"";e.scene.loader.require([{path:g,type:"text"},],function(k){e.obj_mtl=EG.gl.ObjParserTest.parseMtl(k.item);e.loaded="loaded"})}else{e.loaded="loaded"}e.prepare()})}else{this.loaded="loaded"}},setPosition:function(c){this.position=c;this.prepare()},setVertices:function(c){this.vertices=c;this.prepare()},init:function(){this.prepare()},prepare:function(){if(!this.scene||!this.position||!this.obj_model){return}var o=this.scene.gl;this.boundingBox=[];for(var g=0;g<this.obj_model.o.length;g++){var c=this.obj_model.o[g].mesh;for(var h=0;h<c.length;h++){var e=c[h];var f=e.v.length/3;var p=[];for(var n=0;n<f;n++){var t=e.v[n*3+0];var r=e.v[n*3+1];var q=e.v[n*3+2];if(this.rotateY!=0){var s=a.rotateY([t,r,q],this.rotatedY);t=s[0];r=s[1];q=s[2]}if(this.scale!=1){t=t*this.scale;r=r*this.scale;q=q*this.scale}t+=this.position[0];r+=this.position[1];q+=this.position[2];p.push(t);p.push(r);p.push(q)}e.v_final=p;for(var n=0;n<p.length;){this.boundingBox.push([[p[h+0],p[h+1],p[h+2]],[p[h+3],p[h+4],p[h+5]],[p[h+6],p[h+7],p[h+8]]]);n+=9}e.buf_v=o.createBuffer();o.bindBuffer(o.ARRAY_BUFFER,e.buf_v);o.bufferData(o.ARRAY_BUFFER,new Float32Array(e.v_final),o.STATIC_DRAW);if(!e.buffered){e.buf_vt=o.createBuffer();o.bindBuffer(o.ARRAY_BUFFER,e.buf_vt);o.bufferData(o.ARRAY_BUFFER,new Float32Array(e.vt),o.STATIC_DRAW);o.bindBuffer(o.ARRAY_BUFFER,null);e.buf_vn=o.createBuffer();o.bindBuffer(o.ARRAY_BUFFER,e.buf_vn);o.bufferData(o.ARRAY_BUFFER,new Float32Array(e.vn),o.STATIC_DRAW);o.bindBuffer(o.ARRAY_BUFFER,null);e.buf_f=o.createBuffer();o.bindBuffer(o.ELEMENT_ARRAY_BUFFER,e.buf_f);o.bufferData(o.ELEMENT_ARRAY_BUFFER,new Uint16Array(e.f),o.STATIC_DRAW);o.bindBuffer(o.ELEMENT_ARRAY_BUFFER,null);e.buffered=true}}}this.prepared=true},getVertices:function(){return this.vertices},setUvs:function(c){this.uvs=c},getUvs:function(){return this.uvs},setFaces:function(c){this.faces=c},getFaces:function(){return this.faces},setColors:function(c){this.colors=c},getColors:function(){return this.colors},getBoundingBox:function(){return this.boundingBox},render:function(){if(!this.loaded){this.load()}if(this.loaded!="loaded"){return}if(!this.prepared){return}for(var e=0;e<this.obj_model.o.length;e++){var f=this.obj_model.o[e].mesh;for(var c=0;c<f.length;c++){this.render_mesh(f[c])}}},render_mesh:function(v){var s=this;var o=this.scene.fs;var u=this.scene.vs;var n=this.gl;var t=this.scene.loader;n.bindBuffer(n.ARRAY_BUFFER,v.buf_v);n.enableVertexAttribArray(u.aPosition);n.vertexAttribPointer(u.aPosition,3,n.FLOAT,false,0,0);n.bindBuffer(n.ARRAY_BUFFER,v.buf_vn);n.enableVertexAttribArray(u.aNormal);n.vertexAttribPointer(u.aNormal,3,n.FLOAT,false,0,0);n.bindBuffer(n.ARRAY_BUFFER,v.buf_vt);n.vertexAttribPointer(u.aTextureCoord,2,n.FLOAT,false,0,0);n.enableVertexAttribArray(u.aTextureCoord);o.setDecriptTexture(!!this.decriptTexture);u.setUseAColor(false);n.disableVertexAttribArray(u.aColor);var f=this.scene.lights;if(f.length>0){var g=f[0];u.setLightPosition(g.position)}var m=false;if(v.usemtl){var r=this.obj_mtl[v.usemtl];u.setAmbientLightColor(r.Ka);u.setDiffuseLightColor(r.Kd);u.setSpecularLightColor(r.Ks);m=!!r.map_Kd;if(m){if(r&&r.map_Kd){var c=r.map_Kd;if(this.mtl_basePath){c=this.mtl_basePath+"/"+c}var e={type:"img",path:EG.Ajax.javaURLEncoding(c)};var l=false;var j=t.find(e);if(!j&&t.failds){for(var h=0;h<t.failds.length;h++){if(t.failds[h]==c){l=true;break}}}if(j){if(!j.texture){var q=n.createTexture();n.bindTexture(n.TEXTURE_2D,q);n.pixelStorei(n.UNPACK_FLIP_Y_WEBGL,true);n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,j.item);n.generateMipmap(n.TEXTURE_2D);n.bindTexture(n.TEXTURE_2D,null);j.texture=q}n.activeTexture(n.TEXTURE0);n.bindTexture(n.TEXTURE_2D,j.texture)}else{if(!l){t.require([e],function(){},function(k){if(!t.failds){t.failds=[]}t.failds.push(c)})}}}else{console.log("未发现:"+v.usemtl+":"+JSON.stringify(this.mtllib))}}else{}}u.setUseTexture(m);o.setUseTexture(m);if(!m){n.disableVertexAttribArray(u.aTextureCoord)}else{n.enableVertexAttribArray(u.aTextureCoord)}n.drawArrays(n.TRIANGLES,0,v.f.length)},rotateY:function(e,c){this.rotatedY+=e;if(this.rotatedY>360){this.rotatedY=this.rotatedY-360}this.prepare()}}})})();(function(){EG.define("EG.gl.Line",["EG.math.Vec3"],function(a,b){EG.GL.registSharp("line",b);return{extend:"EG.gl.Sharp",config:{color:null,start:null,end:null},constructor:function(c){this.callSuper([c]);if(c==null){c={}}this.verticals=[];if(c.verticals){this.addVerticals(c.verticals)}if(c.start){this.setStart(c.start)}if(c.end){this.setEnd(c.end)}},getProjectionAngle:function(c){if(c==null){c="Y"}if(this.isVertical(c)){return Math.PI/2}else{return Math.atan((this.start[1]-this.end[1])/Math.sqrt(Math.pow(this.start[0]-this.end[0],2)+Math.pow(this.start[2]-this.end[2],2)))}},isVertical:function(c){if(c==null){c="Y"}if(c=="Y"){return(this.start.x-this.end.x==0)&&(this.start.z-this.end.z==0)}else{if(c=="X"){return(this.start.y-this.end.y==0)&&(this.start.z-this.end.z==0)}else{if(c=="Z"){return(this.start.x-this.end.x==0)&&(this.start.y-this.end.y==0)}}}},getPoint:function(e,c){if(c==null){c=true}return a.getLinePoint(c?this.start:this.end,c?this.end:this.start,e)},addVerticals:function(c){EG.Array.addAll(this.verticals,c);this.cacheVetexPos=null},getRenderVertexPosition:function(){if(!this.cacheVetexPos){this.cacheVetexPos=[];if(this.start){EG.Array.addAll(this.cacheVetexPos,this.start)}EG.Array.addAll(this.cacheVetexPos,this.verticals);if(this.end){EG.Array.addAll(this.cacheVetexPos,this.end)}}return this.cacheVetexPos},getRenderVertexColor:function(){var f="#FFFFFF";var j=1;if(!this.vcs){var h=[];var e=EG.GL.hex2Rgb(this.color||f).concat(this.opacity||j);var k=this.getRenderVertexPosition().length/6;for(var g=0;g<k;g++){EG.Array.addAll(h,e);EG.Array.addAll(h,e)}this.vcs=h}return this.vcs},render:function(){var f=this;var h=this.gl;var e=this.scene.fs;var k=this.scene.vs;k.setUseAColor(true);k.setUseTexture(false);e.setUseTexture(false);h.disableVertexAttribArray(k.aTextureCoord);var g=this.getRenderVertexPosition();var j=this.getRenderVertexColor();this.setVBOs({vertex:g,location:"aPosition",count:3},{vertex:j,location:"aColor",count:4});h.drawArrays(h.LINES,0,g.length/3)},setStart:function(c){this.start=c;this.cacheVetexPos=null},setEnd:function(c){this.end=c;this.cacheVetexPos=null},getLength:function(){return a.getLength(this.start,this.end)},rotate:function(c,e){},getDirect:function(){return[this.end[0]-this.start[0],this.end[1]-this.start[1],this.end[2]-this.start[2]]},clone:function(){return new b({start:EG.Array.addAll([],this.start),end:EG.Array.addAll([],this.end),color:this.color,verticals:EG.Array.addAll([],this.verticals)})}}})})();(function(){EG.define("EG.gl.Plane",function(a){EG.GL.registSharp("plane",a);return{extend:"EG.gl.Sharp",config:{vetexs:null,size:null,color:null},constructor:function(b){this.callSuper([b]);this.surface={}},init:function(){},setSize:function(b){this.size=b},setRenderVertexPosition:function(b,f,e,c){EG.Array.addAll(this.vetexs,[b,f,e,c])},getCross:function(b){},getPoint:function(b){var c=null;return c},getRenderVertexPosition:function(){return this.vetexs},getRenderTextureCoord:function(){return[0,0,1,0,1,1,0,1]},getRenderIndices:function(){return[0,1,2,0,2,3]},getRenderVertexColor:function(){var c="#FFFFFF";var f=1;var e=[];var b=EG.gl.hex2Rgb(this.color||c).concat(this.opacity||f);e=e.concat(b,b,b,b);return e},render:function(){var c=this;var g=this.gl;var b=this.scene.fs;var h=this.scene.vs;h.setUseTexture(false);b.setUseTexture(false);g.disableVertexAttribArray(h.aTextureCoord);this.setVBOs({vertex:this.getRenderVertexPosition(),location:"aPosition",count:3},{vertex:this.getRenderVertexColor(),location:"aColor",count:4});if(this.texture){var e=this.scene.loadImage(this.texture);if(e!=null){h.setUseTexture(true);b.setUseTexture(true);g.enableVertexAttribArray(h.aTextureCoord);this.setVBOs({vertex:this.getRenderTextureCoord(),location:"aTextureCoord",count:2});g.activeTexture(g.TEXTURE0);g.bindTexture(g.TEXTURE_2D,g.createTexture());g.texParameteri(g.TEXTURE_2D,g.TEXTURE_MIN_FILTER,g.LINEAR);g.texImage2D(g.TEXTURE_2D,0,g.RGB,g.RGB,g.UNSIGNED_BYTE,e);g.uniform1i(b.uTexture,0)}}var f=this.getRenderIndices();g.bindBuffer(g.ELEMENT_ARRAY_BUFFER,g.createBuffer());g.bufferData(g.ELEMENT_ARRAY_BUFFER,new Uint8Array(f),g.STATIC_DRAW);g.drawElements(g.TRIANgl.S,f.length,g.UNSIGNED_BYTE,0);g.bindBuffer(g.ELEMENT_ARRAY_BUFFER,null)}}})})();(function(){EG.define("EG.gl.Segment",{extend:"EG.gl.Sharp",config:{},constructor:function(a){this.callSuper([a])},setPosition:function(a){this.position=a},setColor:function(a){this.color=a},render:function(){var j=this.scene.gl;var l=this.scene.camera;var t=["attribute vec3 position;","uniform   mat4 mvpMatrix;","attribute vec4 color;","varying   vec4 vColor;","void main(void){","    vColor = color;","    gl_Position = mvpMatrix * vec4(position, 1.0);","}"].join("\n");var s=EG.GL.createShader(j,t,j.VERTEX_SHADER);var k=["precision mediump float;","varying vec4 vColor;","void main(void){","    gl_FragColor = vColor;","}"].join("\n");var q=EG.GL.createShader(j,k,j.FRAGMENT_SHADER);var c=EG.GL.createProgram(j,s,q);var a=new Array(2);a[0]=j.getAttribLocation(c,"position");a[1]=j.getAttribLocation(c,"color");var f=new Array(2);f[0]=3;f[1]=4;var h=EG.GL.createVBO(j,this.position);var g=EG.GL.createVBO(j,this.color);EG.GL.setVBO(j,[h,g],a,f);var e=new EG.gl.Matrix();var b=e.identity(e.create());var r=e.identity(e.create());var p=e.identity(e.create());var o=e.identity(e.create());e.lookAt(l.position,l.center,l.normal,r);e.perspective(90,this.scene.width/this.scene.height,0.1,100,p);e.multiply(p,r,o);e.multiply(o,b,o);var n=j.getUniformLocation(c,"mvpMatrix");j.uniformMatrix4fv(n,false,o);j.drawArrays(j.TRIANgl.S,0,3);j.flush()}})})();(function(){EG.define("EG.gl.Triangle",{extend:"EG.gl.Sharp",config:{position:null,color:null},constructor:function(a){this.callSuper([a])},setPosition:function(a){this.position=a},setColor:function(a){this.color=a},render:function(){var h=this.scene.gl;var k=this.scene.camera;var r=EG.GL.createShader(h,["attribute vec3 position;","uniform   mat4 mvpMatrix;","attribute vec4 color;","varying   vec4 vColor;","void main(void){","    vColor = color;","    gl_Position = mvpMatrix * vec4(position, 1.0);","}"].join("\n"),h.VERTEX_SHADER);var p=EG.GL.createShader(h,["precision mediump float;","varying vec4 vColor;","void main(void){","    gl_FragColor = vColor;","}"].join("\n"),h.FRAGMENT_SHADER);var c=EG.GL.createProgram(h,r,p);var a=new Array(2);a[0]=h.getAttribLocation(c,"position");a[1]=h.getAttribLocation(c,"color");var f=new Array(2);f[0]=3;f[1]=4;var j=EG.GL.createVBO(h,this.position);var g=EG.GL.createVBO(h,this.color);EG.GL.setVBO(h,[j,g],a,f);var e=new EG.gl.Matrix();var b=e.identity(e.create());var q=e.identity(e.create());var o=e.identity(e.create());var n=e.identity(e.create());e.lookAt(k.position,k.center,k.normal,q);e.perspective(90,this.scene.width/this.scene.height,0.1,100,o);e.multiply(o,q,n);e.multiply(n,b,n);var l=h.getUniformLocation(c,"mvpMatrix");h.uniformMatrix4fv(l,false,n);h.drawArrays(h.TRIANgl.S,0,3);h.flush()}})})();(function(){EG.define("EG.FC",{statics:{makeSvg:function(a){return EG.CSVG({pn:a,tn:"svg",width:"100%",height:"100%"})},getXY:function(c){var b;if(EG.DOM.isElement(c)){b=c.getAttribute("transform")}else{if(typeof(c)=="string"){b=c}else{throw new Error("EG.FC.getXY:参数类型错误"+typeof(c))}}if(!b){return null}var a=EG.Style.parseTransform(b)["translate"];if(!a){return null}return{x:a[0],y:a[1]}}}})})();(function(){EG.define("EG.fc.Node",{config:{appId:null,type:"node",width:80,height:40,x:0,y:0,style:null,rx:10,ry:10,text:"",indent:"",cls:"eg_fc_node",editor:null,crossPadding:4,click:null,textColor:"#000000",textSize:"14px",boxBackground:null,boxBorderWidth:1,boxBorderColor:"#DDDDDD",boxOpacoty:0.8,afterMove:null},constructor:function(a){this.initConfig(a);this.build();this.setTextColor(this.textColor);this.setTextSize(this.textSize);this.setBoxBackground(this.boxBackground);this.setBoxBorderColor(this.boxBorderColor);this.setBoxBorderWidth(this.boxBorderWidth);this.setBoxOpacity(this.boxOpacity);this.moveTo({x:this.x,y:this.y});this.sizeTo({width:this.width,height:this.height});if(!this.indent){this.indent=this.type+(EG.fc.Node.count++)}if(this.editor){this.editor.addNode(this)}},statics:{count:0,_events:{onmousedown:function(a){var b=this.me;if(b.editor&&b.editor.editable){if(EG.Tools.isPressRight(a)){b.editor.showMenu(a,b)}else{if(b.editor.oper.type=="w4connect"){b.editor.startConnect(a,b)}else{b.editor.selectNodes([b]);b.editor.startMoveNodes(a,b)}}}else{if(b.click){b.click.apply(this.clickSrc||b,[a])}}EG.Event.stopPropagation(a)},onmouseup:function(a){var b=this.me;if(b.editor.editable){if(b.editor.oper.type=="connect"){b.editor.afterConnect(a,b);EG.Event.stopPropagation(a)}else{if(b.editor.oper.type=="moveConnectPoint"){b.editor.afterMoveConnectPoint(a,b);EG.Event.stopPropagation(a)}}}}}},build:function(){var a=EG.fc.Node;this.element=EG.CSVG({tn:"g",cls:this.cls,onmousedown:a._events.onmousedown,onmouseup:a._events.onmouseup});this.element.me=this;this.buildBox();this.buildText()},buildBox:function(){this.eBox=EG.CSVG({pn:this.element,tn:"rect",rx:this.rx,ry:this.ry})},buildText:function(){this.eText=EG.CSVG({pn:this.element,tn:"text",innerText:this.text,"text-anchor":"middle",x:(this.width)/2,y:24,style:this.textStyle})},getElement:function(){return this.element},changeTo:function(a){this.moveTo(a);this.sizeTo(a)},moveTo:function(a){EG.CSVG({ele:this.element,transform:"translate("+a.x+","+a.y+")"});if(this.afterMove){this.afterMove.apply(this,[a])}},sizeTo:function(a){EG.CSVG({ele:this.eBox,width:a.width,height:a.height});EG.CSVG({ele:this.eText,x:a.width/2,y:24})},getSize:function(){return{width:parseInt(this.eBox.getAttribute("width")),height:parseInt(this.eBox.getAttribute("height"))}},getPos:function(){return EG.FC.getXY(this.element)},getCrossPoint:function(c){var g=this.getPos();var e=this.getSize();var f;var a={kER:0.1,yER:5};var b=new EG.fc.Line({x1:g.x,y1:g.y-this.crossPadding,x2:g.x+e.width,y2:g.y-this.crossPadding,id:"top"});f=b.getCrossPoint(c,a);if(f!=null){return f}b=new EG.fc.Line({x1:g.x,y1:g.y+e.height+this.crossPadding,x2:g.x+e.width,y2:g.y+e.height+this.crossPadding,id:"bottom"});f=b.getCrossPoint(c,a);if(f!=null){return f}b=new EG.fc.Line({x1:g.x-this.crossPadding,y1:g.y,x2:g.x-this.crossPadding,y2:g.y+e.height,id:"left"});f=b.getCrossPoint(c,a);if(f!=null){return f}b=new EG.fc.Line({x1:g.x+e.width+this.crossPadding,y1:g.y,x2:g.x+e.width+this.crossPadding,y2:g.y+e.height,id:"right"});f=b.getCrossPoint(c,a);return f},bindEditor:function(a){this.editor=a},setText:function(a){this.text=a;EG.CSVG({ele:this.eText,innerText:a})},setTextColor:function(a){if(a==null){a="#000000"}this.textColor=a;EG.css(this.eText,"fill:"+a)},setTextSize:function(a){if(a==null){a="14px"}this.textSize=a;EG.css(this.eText,"font-size:"+a)},setBoxOpacity:function(a){if(a==null){a=""}this.boxOpacity=a;EG.css(this.eBox,"opacity:"+this.boxOpacity)},setBoxBackground:function(a){if(a==null){a=""}this.boxBackground=a;EG.css(this.eBox,"fill:"+this.boxBackground)},setBoxBorderWidth:function(a){if(a==null){a=""}this.boxBorderWidth=a;EG.css(this.eBox,"strokeWidth:"+this.boxBorderWidth)},setBoxBorderColor:function(a){if(a==null){a=""}this.boxBorderColor=a;EG.css(this.eBox,"stroke:"+this.boxBorderColor)}})})();(function(){EG.define("EG.fc.Branch",{config:{width:80,height:40,x:0,y:0,style:null,rx:10,ry:10,text:"",cls:"eg_node",editor:null,strokeWidth:0},constructor:function(a){var b=this;this.initConfig(a);this.element=EG.CSVG({tn:"g",cls:this.cls,cn:[this.polygon=EG.CSVG({tn:"polygon",fill:"#78854F",stroke:"#03689A","stroke-width":this.strokeWidth}),this.eText=EG.CSVG({tn:"text",innerText:this.text,"text-anchor":"middle",fill:"#FFFFFF",x:(this.width)/2,y:24,style:"font-size:14px;color:white"})],onmousemove:function(){},onmousedown:function(c){if(!b.editor.oper.type){b.editor.selectNodes([b])}else{if(b.editor.oper.type=="w4connect"){b.editor.startConnect(c,b)}}EG.Event.stopPropagation(c)},onmouseover:function(){},onmouseup:function(c){if(b.editor.oper.type=="connect"){b.editor.afterConnect(c,b);EG.Event.stopPropagation(c)}}});this.moveTo({x:this.x,y:this.y});this.sizeTo({width:this.width,height:this.height})},getElement:function(){return this.element},changeTo:function(a){this.moveTo(a);this.sizeTo(a)},moveTo:function(a){EG.CSVG({ele:this.element,transform:"translate("+a.x+","+a.y+")"})},sizeTo:function(b){var e="";var a=b.width,c=b.height;e+=a/2+","+0;e+=" "+a+","+c/2;e+=" "+a/2+","+c;e+=" "+0+","+c/2;EG.CSVG({ele:this.polygon,points:e});EG.CSVG({ele:this.eText,x:b.width/2,y:24})},getSize:function(){var c=this.polygon.getAttribute("points").split(" ");var b=parseInt(c[1].split(",")[0]);var a=parseInt(c[2].split(",")[1]);return{width:b,height:a}},getPos:function(){return EG.FC.getXY(this.element)}})})();(function(){EG.define("EG.fc.Connector",[],function(a){return{config:{appId:null,type:"connector",indent:"",text:"",startNode:null,startX:null,startY:null,endNode:null,endX:null,endY:null,strokeWidth:1,cnSize:7,selected:false,breaks:[],selectedColor:"red",unselectColor:"#909090",selectedMarker:"#markerArrow-on",unselectMarker:"#markerArrow",sycPosByNode:true,textRelatePos:{x:0,y:0}},constructor:function(b){var c=this;this.initConfig(b);this.element=EG.CSVG({tn:"g",cursor:"move",style:"cursor:move",cn:[],oncontextmenu:function(e){c.editor.showMenu(e,c)}});this.maskLine=EG.CSVG({pn:this.element,tn:"polyline",stroke:"white","stroke-miterlimit":10,"stroke-width":7,fill:"none",onmousedown:function(e){if(c.editor&&c.editor.editable){c.editor.selectConnector(c);EG.Event.stopPropagation(e)}}});this.line=EG.CSVG({pn:this.element,tn:"polyline",style:"","stroke-width":this.strokeWidth,fill:"none",stroke:this.unselectColor,"stroke-dasharray":"2","marker-end":"url("+(!EG.Browser.isIE()?window.location:"")+this.unselectMarker+")",onmousedown:function(e){if(c.editor&&c.editor.editable){c.editor.selectConnector(c);EG.Event.stopPropagation(e)}}});if(this.text!==null){this.setText(this.text)}if(this.startNode){this.bindStartNode(this.startNode)}if(this.endNode){this.endTo(this.endNode)}if(!this.indent){this.indent=this.type+(EG.fc.Connector.count++)}if(this.breaks.length>0){}if(this.editor){this.editor.addConnector(this)}this.render()},setEndPos:function(b){this.endX=b.x;this.endY=b.y},endTo:function(b){this.endNode=b;this.line.removeAttribute("stroke-dasharray");this.render()},addBreak:function(){},bindStartNode:function(b){},bindEditor:function(b){this.editor=b;if(this.nodeText){this.nodeText.bindEditor(b)}},render:function(){var s=this;if(this.endNode&&this.sycPosByNode){var n=this.startNode.getPos();var t=this.endNode.getPos();var o=this.startNode.getSize();var u=this.endNode.getSize();var c=n.x+o.width/2;var l=n.y+o.height/2;var b=t.x+u.width/2;var k=t.y+u.height/2;var g={x1:c,y1:l,x2:this.breaks.length>0?this.breaks[0][0]:b,y2:this.breaks.length>0?this.breaks[0][1]:k};var p={x1:this.breaks.length>0?this.breaks[this.breaks.length-1][0]:c,y1:this.breaks.length>0?this.breaks[this.breaks.length-1][1]:l,x2:b,y2:k};var f=new EG.fc.Line(g);var m=new EG.fc.Line(p);var j=this.startNode.getCrossPoint(f);var q=this.endNode.getCrossPoint(m);this.startX=j?j.x:c;this.startY=j?j.y:l;this.endX=q?q.x:b;this.endY=q?q.y:k}var r=this.startX+","+this.startY+" "+this.breaks.join(" ")+" "+this.endX+","+this.endY;EG.CSVG({ele:this.line,points:r});EG.CSVG({ele:this.maskLine,points:r});if(this.nodeText){var h=this.textRelatePos;var e=this.nodeText.centerPos={x:(this.startX+this.endX)/2,y:(this.startY+this.endY)/2};this.nodeText.moveTo({x:e.x+h.x,y:e.y+h.y})}},buildBreaks:function(){var f=this;this.breakEles=[];this.tmpBreakEles=[];this.ps=[];this.ps.push([this.startX,this.startY]);this.ps=this.ps.concat(this.breaks);this.ps.push([this.endX,this.endY]);var e=function(j,h,l,k){return EG.CSVG({pn:f.element,tn:"rect",DATA:j,cursor:"move",width:f.cnSize,height:f.cnSize,fill:"black",stroke:"white","stroke-width":1,x:h,y:l,onmousedown:function(m){if(k){EG.Array.insert(f.breakEles,j,this);EG.Array.insert(f.breaks,j,[h,l]);EG.Array.remove(f.tmpBreakEles,this)}f.editor.startMoveConnectPoint(m,f,k?this.DATA+1:this.DATA,k);EG.Event.stopPropagation(m)}})};for(var c=0;c<this.ps.length;c++){var g=this.ps[c];this.breakEles.push(e(c,g[0]-f.cnSize/2,g[1]-f.cnSize/2));if(c+1<this.ps.length){var b=this.ps[c+1];this.tmpBreakEles.push(e(c,(g[0]+b[0])/2+-f.cnSize/2,(g[1]+b[1])/2-f.cnSize/2,true))}}},renderBreak:function(){},afterMove:function(f){if(f>0&&f<this.breaks.length+1){var b=this.breaks[f-1];var l=f==1?[this.startX,this.startY]:this.breaks[f-2];var k=f==this.breaks.length?[this.endX,this.endY]:this.breaks[f];var e=new EG.fc.Line({x1:l[0],y1:l[1],x2:b[0],y2:b[1]});var c=new EG.fc.Line({x1:b[0],y1:b[1],x2:k[0],y2:k[1]});var j=false;if(Math.abs(e.x2-e.x1)<2&&Math.abs(c.x2-c.x1)<2){j=true}else{var h=e.getK();var g=c.getK();if(Math.abs(h-g)<0.4){j=true}}if(j){EG.Array.del(this.breaks,f-1)}}this.sycPosByNode=true},beforeMove:function(){this.sycPosByNode=false;this.removeBreakEles()},removeBreakEles:function(){if(this.breakEles){for(var b=0;b<this.breakEles.length;b++){this.element.removeChild(this.breakEles[b])}this.breakEles=[];for(var b=0;b<this.tmpBreakEles.length;b++){this.element.removeChild(this.tmpBreakEles[b])}this.tmpBreakEles=[]}},select:function(b){this.selected=b;if(this.selected){if(!this.breakEles||this.breakEles.length==0){this.buildBreaks()}EG.CSVG({ele:this.line,stroke:this.selectedColor,"marker-end":"url("+(!EG.Browser.isIE()?window.location:"")+this.selectedMarker+")"})}else{this.removeBreakEles();EG.CSVG({ele:this.line,stroke:this.unselectColor,"marker-end":"url("+(!EG.Browser.isIE()?window.location:"")+this.unselectMarker+")"})}},getElement:function(){return this.element},setText:function(b){if(!this.nodeText){this.nodeText=new EG.fc.Text({connector:this,afterMove:a.event_afterMove,menuAble:false})}this.nodeText.setText(b);this.element.appendChild(this.nodeText.getElement());if(this.editor){this.nodeText.bindEditor(this.editor)}this.render()},statics:{count:0,event_afterMove:function(b){if(!this.centerPos){return}this.connector.textRelatePos={x:b.x-this.centerPos.x,y:b.y-this.centerPos.y,}}}}})})();(function(){EG.define("EG.fc.End",{extend:"EG.fc.Node",config:{type:"end",width:36,height:36,text:"结束",cls:"eg_node",boxBackground:"#FF0000",boxBorderColor:"#03689A"},constructor:function(a){this.callSuper([a])},buildBox:function(){this.eBox=EG.CSVG({pn:this.element,tn:"circle",cx:this.width/2,cy:this.width/2})},sizeTo:function(a){EG.CSVG({ele:this.eBox,r:a.width/2});EG.CSVG({ele:this.eText,x:a.width/2,y:24})},getSize:function(){return{width:parseInt(this.eBox.getAttribute("r"))*2,height:parseInt(this.eBox.getAttribute("r"))*2}}})})();(function(){EG.define("EG.fc.GhostNode",{config:{width:null,height:null,handleNode:null,editor:null},constructor:function(a){this.initConfig(a);this.element=EG.CSVG({tn:"rect",fill:"#F6F6F6",stroke:"black","fill-opacity":0.7,"stroke-dasharray":"2"});this.bindHandleNode(this.handleNode)},changeTo:function(a){this.moveTo(a);this.sizeTo(a)},moveTo:function(a){EG.CSVG({ele:this.element,x:a.x,y:a.y})},sizeTo:function(a){EG.CSVG({ele:this.element,width:a.width,height:a.height})},bindHandleNode:function(a){if(!a){return}this.handleNode=a;this.editor=this.handleNode.editor;this.editor.addGhostNode(this);this.moveTo(a.getPos());this.sizeTo(a.getSize())},getElement:function(){return this.element},getPos:function(){return{x:parseInt(this.element.getAttribute("x")),y:parseInt(this.element.getAttribute("y"))}},getSize:function(){return{width:parseInt(this.element.getAttribute("width")),height:parseInt(this.element.getAttribute("height"))}}})})();(function(){EG.define("EG.fc.HandleConnector",{config:{connector:null,editor:null,cnSize:5,width:null,height:null,strokeWidth:2},constructor:function(a){var b=this;this.points=[];this.initConfig(a);this.build();this.bindConnector(this.node)},build:function(){var a=this;this.element=EG.CSVG({tn:"g",cursor:"pointer",cn:[]});this.line=EG.CSVG({pn:this.element,tn:"polyline",cursor:"move","stroke-width":this.strokeWidth,stroke:"black","stroke-dasharray":"2",onmousedown:function(){}})},buildPoints:function(){this.points=[];var g=[];g.push({x:this.connector.startX,y:this.connector.startY});g.push({x:this.connector.endX,y:this.connector.endY});var e=[];for(var f=0;f<this.breaks.length;f++){var a=this.breaks[f];if(f>0){var c=this.connector.breaks[f-1];g.push({x:(a.x+c.x)/2,y:(a.y+c.y)/2})}g.push(a)}for(var f=0;f<this.ps.length;f++){(function(){var b=f;var h=EG.CSVG({pn:me.element,tn:"rect",width:me.cnSize,height:me.cnSize,cursor:"pointer",fill:"black",stroke:"white","stroke-width":1,onmousedown:function(j){me.editor.startMoveConnectPoint(j,me,b);EG.Event.stopPropagation(j)}});me.points.push(h)})()}},bindConnector:function(a){if(!a){return}this.connector=a;this.editor=this.connector.editor;this.editor.addHandleNode(this);this.breaks=a.breaks;this.buildPoints()},repaint:function(){var a=this.startX+","+this.startY+" "+this.breaks.join(" ")+" "+this.endX+","+this.endY;var a=this.startX+","+this.startY+" "+this.endX+","+this.endY;EG.CSVG({ele:this.line,points:a})},getElement:function(){return this.element}})})();(function(){EG.define("EG.fc.HandleNode",{config:{node:null,editor:null,padding:5,cnSize:5,width:null,height:null,strokeWidth:1},constructor:function(a){var b=this;this.initConfig(a);this.build();this.bindNode(this.node)},build:function(){var b=this;this.element=EG.CSVG({tn:"g",cursor:"pointer",cn:[this.box=EG.CSVG({tn:"rect",fill:"white","fill-opacity":0,stroke:"black","stroke-width":this.strokeWidth})],onmousedown:function(e){if(EG.Tools.isPressRight(e)){b.editor.showMenu(e,b.node)}else{b.editor.startMoveNodes(e,b);EG.Event.stopPropagation(e)}EG.Event.stopPropagation(e);return false}});var c=["nw","n","ne","w","e","sw","s","se"];for(var a=0;a<c.length;a++){(function(){var e=c[a];b["cn_"+e]=EG.CSVG({pn:b.element,tn:"rect",cursor:e+"-resize",width:b.cnSize,height:b.cnSize,fill:"black",stroke:"white","stroke-width":1,onmousedown:function(f){b.editor.startChangeSize(f,b,e);EG.Event.stopPropagation(f)}})})()}},moveTo:function(b){if(b==null){b=this.getPos()}this._moveTo(b);var a=(this.cnSize-this.strokeWidth)/2;this.node.moveTo({x:b.x+this.padding+a,y:b.y+this.padding+a})},_moveTo:function(a){EG.CSVG({ele:this.element,transform:"translate("+a.x+","+a.y+")"})},sizeTo:function(a){this._sizeTo(a);this.node.sizeTo({width:a.width-this.padding*2,height:a.height-this.padding*2})},_sizeTo:function(b){var a=(this.cnSize-this.strokeWidth)/2;EG.CSVG({ele:this.box,width:b.width-a*2,height:b.height-a*2,x:a,y:a});EG.CSVG({ele:this.cn_nw,x:0,y:0});EG.CSVG({ele:this.cn_n,x:b.width/2-a,y:0});EG.CSVG({ele:this.cn_ne,x:b.width-this.cnSize,y:0});EG.CSVG({ele:this.cn_w,x:0,y:b.height/2-a});EG.CSVG({ele:this.cn_e,x:b.width-this.cnSize,y:b.height/2-a});EG.CSVG({ele:this.cn_sw,x:0,y:b.height-this.cnSize});EG.CSVG({ele:this.cn_s,x:b.width/2-a,y:b.height-this.cnSize});EG.CSVG({ele:this.cn_se,x:b.width-this.cnSize,y:b.height-this.cnSize})},changeTo:function(a){this.moveTo(a);this.sizeTo(a)},bindNode:function(c){if(!c){return}this.node=c;this.editor=this.node.editor;this.editor.addHandleNode(this);var e=EG.FC.getXY(c.getElement());var b=(this.cnSize-this.strokeWidth)/2;this._moveTo({x:e.x-this.padding-b,y:e.y-this.padding-b});var a=c.getSize();this._sizeTo({width:a.width+this.padding*2+b*2,height:a.height+this.padding*2+b*2})},getSize:function(){var a=(this.cnSize-this.strokeWidth)/2;return{width:parseInt(this.box.getAttribute("width"))+a*2,height:parseInt(this.box.getAttribute("height"))+a*2}},getPos:function(){return EG.FC.getXY(this.element)},getElement:function(){return this.element}})})();(function(){EG.define("EG.fc.Line",{config:{x1:null,y1:null,x2:null,y2:null,id:null},constructor:function(a){this.initConfig(a)},getCrossPoint:function(b,a){if(this.getK()==b.getK()){return null}var e,c;if(this.isVertical()){e=this.x1;c=b.getY(e)}else{if(b.isVertical()){e=b.x1;c=this.getY(e)}else{e=((this.x1-this.x2)*(b.x1*b.y2-b.x2*b.y1)-(b.x1-b.x2)*(this.x1*this.y2-this.x2*this.y1))/((b.x1-b.x2)*(this.y1-this.y2)-(this.x1-this.x2)*(b.y1-b.y2));c=((this.y1-this.y2)*(b.x1*b.y2-b.x2*b.y1)-(this.x1*this.y2-this.x2*this.y1)*(b.y1-b.y2))/((this.y1-this.y2)*(b.x1-b.x2)-(this.x1-this.x2)*(b.y1-b.y2))}}var f={x:e,y:c};if(b.contains(f,a)&&this.contains(f,a)){return f}else{return null}},isVertical:function(a){if(!a){a=0}return Math.abs(this.x1-this.x2)<=a},getK:function(){return(this.y2-this.y1)/(this.x2-this.x1)},getD:function(){return this.y1-this.getK()*this.x1},getX:function(a){return(a-this.y1)/this.getK()+this.x1},getY:function(a){return this.getK()*(a-this.x1)+this.y1},contains:function(a,g){if(!g){g={}}var l=EG.n2d(g.yER,0);var h=EG.n2d(g.kER,0);var m=this.y1,c=this.y2;if(this.y1>this.y2){m=this.y2;c=this.y1}if(this.isVertical()){if(a.x!=this.x1){return false}var n=(a.y>=(m-l)&&a.y<=(c+l));return n}else{var b=this.x1,f=this.x2;if(this.x1>this.x2){b=this.x2;f=this.x1}if(a.x<b||a.x>f){return false}if(a.y<m||a.y>c){return false}var j=(a.y-this.y1)/(a.x-this.x1);var e=this.getK();if(Math.abs(j-e)<h){return true}return false}}})})();(function(){EG.define("EG.fc.Node",{config:{appId:null,type:"node",width:80,height:40,x:0,y:0,style:null,rx:10,ry:10,text:"",indent:"",cls:"eg_fc_node",editor:null,crossPadding:4,click:null,textColor:"#000000",textSize:"14px",boxBackground:null,boxBorderWidth:1,boxBorderColor:"#DDDDDD",boxOpacoty:0.8,afterMove:null},constructor:function(a){this.initConfig(a);this.build();this.setTextColor(this.textColor);this.setTextSize(this.textSize);this.setBoxBackground(this.boxBackground);this.setBoxBorderColor(this.boxBorderColor);this.setBoxBorderWidth(this.boxBorderWidth);this.setBoxOpacity(this.boxOpacity);this.moveTo({x:this.x,y:this.y});this.sizeTo({width:this.width,height:this.height});if(!this.indent){this.indent=this.type+(EG.fc.Node.count++)}if(this.editor){this.editor.addNode(this)}},statics:{count:0,_events:{onmousedown:function(a){var b=this.me;if(b.editor&&b.editor.editable){if(EG.Tools.isPressRight(a)){b.editor.showMenu(a,b)}else{if(b.editor.oper.type=="w4connect"){b.editor.startConnect(a,b)}else{b.editor.selectNodes([b]);b.editor.startMoveNodes(a,b)}}}else{if(b.click){b.click.apply(this.clickSrc||b,[a])}}EG.Event.stopPropagation(a)},onmouseup:function(a){var b=this.me;if(b.editor.editable){if(b.editor.oper.type=="connect"){b.editor.afterConnect(a,b);EG.Event.stopPropagation(a)}else{if(b.editor.oper.type=="moveConnectPoint"){b.editor.afterMoveConnectPoint(a,b);EG.Event.stopPropagation(a)}}}}}},build:function(){var a=EG.fc.Node;this.element=EG.CSVG({tn:"g",cls:this.cls,onmousedown:a._events.onmousedown,onmouseup:a._events.onmouseup});this.element.me=this;this.buildBox();this.buildText()},buildBox:function(){this.eBox=EG.CSVG({pn:this.element,tn:"rect",rx:this.rx,ry:this.ry})},buildText:function(){this.eText=EG.CSVG({pn:this.element,tn:"text",innerText:this.text,"text-anchor":"middle",x:(this.width)/2,y:24,style:this.textStyle})},getElement:function(){return this.element},changeTo:function(a){this.moveTo(a);this.sizeTo(a)},moveTo:function(a){EG.CSVG({ele:this.element,transform:"translate("+a.x+","+a.y+")"});if(this.afterMove){this.afterMove.apply(this,[a])}},sizeTo:function(a){EG.CSVG({ele:this.eBox,width:a.width,height:a.height});EG.CSVG({ele:this.eText,x:a.width/2,y:24})},getSize:function(){return{width:parseInt(this.eBox.getAttribute("width")),height:parseInt(this.eBox.getAttribute("height"))}},getPos:function(){return EG.FC.getXY(this.element)},getCrossPoint:function(c){var g=this.getPos();var e=this.getSize();var f;var a={kER:0.1,yER:5};var b=new EG.fc.Line({x1:g.x,y1:g.y-this.crossPadding,x2:g.x+e.width,y2:g.y-this.crossPadding,id:"top"});f=b.getCrossPoint(c,a);if(f!=null){return f}b=new EG.fc.Line({x1:g.x,y1:g.y+e.height+this.crossPadding,x2:g.x+e.width,y2:g.y+e.height+this.crossPadding,id:"bottom"});f=b.getCrossPoint(c,a);if(f!=null){return f}b=new EG.fc.Line({x1:g.x-this.crossPadding,y1:g.y,x2:g.x-this.crossPadding,y2:g.y+e.height,id:"left"});f=b.getCrossPoint(c,a);if(f!=null){return f}b=new EG.fc.Line({x1:g.x+e.width+this.crossPadding,y1:g.y,x2:g.x+e.width+this.crossPadding,y2:g.y+e.height,id:"right"});f=b.getCrossPoint(c,a);return f},bindEditor:function(a){this.editor=a},setText:function(a){this.text=a;EG.CSVG({ele:this.eText,innerText:a})},setTextColor:function(a){if(a==null){a="#000000"}this.textColor=a;EG.css(this.eText,"fill:"+a)},setTextSize:function(a){if(a==null){a="14px"}this.textSize=a;EG.css(this.eText,"font-size:"+a)},setBoxOpacity:function(a){if(a==null){a=""}this.boxOpacity=a;EG.css(this.eBox,"opacity:"+this.boxOpacity)},setBoxBackground:function(a){if(a==null){a=""}this.boxBackground=a;EG.css(this.eBox,"fill:"+this.boxBackground)},setBoxBorderWidth:function(a){if(a==null){a=""}this.boxBorderWidth=a;EG.css(this.eBox,"strokeWidth:"+this.boxBorderWidth)},setBoxBorderColor:function(a){if(a==null){a=""}this.boxBorderColor=a;EG.css(this.eBox,"stroke:"+this.boxBorderColor)}})})();(function(){EG.define("EG.fc.Start",{extend:"EG.fc.Node",config:{type:"start",width:36,height:36,text:"开始",cls:"eg_node",boxBackground:"#00FF00",boxBorderColor:"#03689A"},constructor:function(a){this.callSuper([a])},buildBox:function(){this.eBox=EG.CSVG({pn:this.element,tn:"circle",cx:this.width/2,cy:this.width/2})},sizeTo:function(a){EG.CSVG({ele:this.eBox,r:a.width/2});EG.CSVG({ele:this.eText,x:a.width/2,y:24})},getSize:function(){return{width:parseInt(this.eBox.getAttribute("r"))*2,height:parseInt(this.eBox.getAttribute("r"))*2}}})})();(function(){EG.define("EG.fc.Task",{extend:"EG.fc.Node",config:{type:"task",textColor:"#FFFFFF",boxBackground:"#2e5286"},constructor:function(a){this.callSuper([a])}})})();(function(){EG.define("EG.fc.Text",{extend:"EG.fc.Node",config:{type:"text",text:"",boxBorderWidth:0,boxBackground:"",boxOpacity:0},constructor:function(a){this.callSuper([a])}})})();(function(){EG.define("EG.fc.Editor",["EG.fc.Node","EG.fc.Connector"],function(b,a,c){return{extend:"EG.ui.Panel",config:{cls:"eg_fc_editor",layout:"border",dragWidth:80,dragHeight:40,showbtns:false,dNode:null,dConnector:null,lang:"SVG",onAfterDragIn:null,onMenuEdit:null,onSelectNodes:null,onClearHandle:null,zoomLv:1,canvasWidth:1000,canvasHeight:1000,editable:true,showToolbox:true,},constructor:function(e){var f=this;this.callSuper([e]);this.addItem([{region:"top",width:"100%",height:(this.showbtns?30:0)},{region:"left",width:40,hidden:!this.editable,style:"background-color:#d3d3d3;overflow:auto;text-align:center;vertical-align:top;cursor:pointer;padding:3px",cn:this.getToolboxItems()},{region:"center",style:"overflow:auto;background-color:white;"},{region:"bottom",style:"background-color:white;",height:30}]);this.pBar=this.getItemTop();this.pTool=this.getItemLeft();this.pMain=this.getItemCenter();this.pFoot=this.getItemBottom();this.pTool.getElement().onselectstart=function(){return false};this.pMain.getElement().oncontextmenu=function(g){g.preventDefault()};this.oper={};this.oper.type=null;f.num=0;this.svg=EG.CSVG({pn:this.pMain.getElement(),style:"user-select:none",tn:"svg",width:this.canvasWidth,height:this.canvasHeight,cn:[{tn:"defs",cn:[{tn:"marker",id:"markerArrow",markerUnits:"userSpaceOnUse",markerWidth:8,markerHeight:8,refX:9,refY:4,orient:"auto",cn:[{tn:"path",d:"M 0 0 L 8 4 L 0 8 z",stroke:"#909090",fill:"#909090"}]},{tn:"marker",id:"markerArrow-on",markerUnits:"userSpaceOnUse",markerWidth:8,markerHeight:8,refX:9,refY:4,orient:"auto",cn:[{tn:"path",d:"M 0 0 L 8 4 L 0 8 z",stroke:"red",fill:"red"}]}]},this.gCoodinate=EG.CSVG({id:"gCoodinate",tn:"g"}),this.gConnect=EG.CSVG({id:"gConnect",tn:"g"}),this.gNode=EG.CSVG({id:"gNode",tn:"g"}),this.gHandle=EG.CSVG({id:"gHandle",tn:"g"}),this.gGhost=EG.CSVG({id:"gGhost",tn:"g"})],onmouseup:function(g){if(!f.editable){return}if(f.oper.type=="selecting"){f.afterSelecting(g)}else{if(f.oper.type=="moveNodes"){f.afterMoveNodes(g)}else{if(f.oper.type=="moveConnectPoint"){f.afterMoveConnectPoint(g)}else{if(f.oper.type=="resize"){}else{if(f.oper.type=="connect"){f.afterConnect(g)}else{if(f.oper.type=="changeSize"){f.afterChangeSize(g)}else{if(f.oper.type=="dragIn"){f.afterDragIn(g)}else{}}}}}}}},ondblclick:function(g){if(!f.editable){return}EG.Event.stopPropagation(g)},onmousedown:function(g){if(!f.editable){f.clearHandle();return}EG.hide(f.dMenu);if(!f.oper.type){f.clearGhost();f.clearHandle();f.startSelecting(g)}else{if(f.oper.type=="selected"||f.oper.type=="selectedConnector"){f.oper.type=null;f.clearGhost();f.clearHandle();f.deSelectConnector()}}},onmousemove:function(g){if(!f.editable){return}var h=f.getMousePos(g);f.pFoot.getElement().innerHTML="["+(f.num++)+"]"+f.oper.type+":x:"+h.x+",y:"+h.y+",hd:"+f.handleNodes.length+",gh:"+f.ghostNodes.length+",cur:"+(f.curXY?f.curXY.x+"-"+f.curXY.y:"null")+",zoomLv:"+f.zoomLv;if(f.oper.type=="selecting"){f.doSelecting(g)}else{if(f.oper.type=="moveNodes"){f.doMoveNodes(g)}else{if(f.oper.type=="moveConnectPoint"){f.doMoveConnectPoint(g)}else{if(f.oper.type=="resize"){f.doResize()}else{if(f.oper.type=="connect"){f.doConnect(g)}else{if(f.oper.type=="changeSize"){f.doChangeSize(g)}else{if(f.oper.type=="dragIn"){f.doDragIn(g)}}}}}}}EG.Event.stopPropagation(g)},onselectstart:function(){if(!f.editable){return}return false}});this.svg.onselectstart=function(){return false};this.drawCoodinate();this.buildMenu();this.init()},buildMenu:function(){var e=this;this.dMenu=EG.CE({pn:this.getElement(),cls:this.cls+"-dMenu",tn:"div",cn:[{tn:"a",innerHTML:"编辑",onclick:function(){var f=e.dMenu.item;if(EG.isInstanceof(f,a)){e.onMenuEditConnector(f)}else{if(EG.isInstanceof(f,b)){e.onMenuEditNode(f)}}if(e.onMenuEdit){e.onMenuEdit.apply(e,[f])}EG.hide(e.dMenu)}},{tn:"a",innerHTML:"删除",onclick:function(){if(e.handleNodes!=null&&e.handleNodes.length>0){for(var f=e.handleNodes.length-1;f>=0;f--){if(e.handleNodes[f].node instanceof a){e.removeConnector(e.handleNodes[f].node)}else{e.removeNode(e.handleNodes[f].node)}}e.clearHandle()}else{var g=e.dMenu.item;if(g instanceof a){e.removeConnector(g)}else{e.removeNode(g,true)}}EG.hide(e.dMenu)}},{tn:"a",innerHTML:"复制",onclick:function(){}},{tn:"a",innerHTML:"移前",onclick:function(){}},{tn:"a",innerHTML:"移后",onclick:function(){}}],oncontextmenu:function(f){EG.Event.stopPropagation(f)}});this.dlgNode=this.buildNodeDialog();this.dlgConnector=this.buildConnectorDialog();EG.hide(this.dMenu)},onMenuEditNode:function(e){this.dlgNode.open();this.dlgNode.getItem(0).setData(e)},buildNodeDialog:function(){var e=this;var f=new EG.ui.Dialog({closeable:true,lock:true,title:"编辑",width:400,height:"auto",renderTo:this.getElement(),items:[{xtype:"form",height:"auto",items:[{title:"标识",name:"indent",type:"text"},{title:"文本",name:"text",type:"text"},{title:"类型",name:"type",type:"text",editable:false}]}],btns:[{text:"保存",cls:"eg_button_small",click:function(){var g=f.getItem(0).getData();var h=e.dMenu.item;h.setText(g.text);h.indent=(g.indent);f.close()}}]});return f},onMenuEditConnector:function(e){if(!this.dlgConnector){}this.dlgConnector.open();this.dlgConnector.getItem(0).setData(e)},buildConnectorDialog:function(){var e=this;var f=new EG.ui.Dialog({closeable:true,lock:true,title:"编辑",width:400,height:"auto",renderTo:this.getElement(),items:[{xtype:"form",height:"auto",items:[{title:"标识",name:"indent",type:"text"},{title:"文本",name:"text",type:"text"}]}],btns:[{text:"保存",cls:"eg_button_small",click:function(){var g=f.getItem(0).getData();var h=e.dMenu.item;h.setText(g.text);h.indent=(g.indent);f.close()}}]});return f},getToolboxItems:function(){var e=this;return[{tn:"div",cls:"eg_fc_select",onclick:function(){e.oper.type=null;EG.css(this,{"background-color":"blue"})}},{tn:"div",cls:"eg_fc_connect",onmousedown:function(f){e.oper.type="w4connect"}},{tn:"div",cls:"eg_fc_start",onmousedown:function(f){e.startDragIn(f,"start")}},{tn:"div",cls:"eg_fc_end",onmousedown:function(f){e.startDragIn(f,"end")}},{tn:"div",cls:"eg_fc_node",onmousedown:function(f){e.startDragIn(f,"task")}},{tn:"div",cls:"eg_fc_branch",onmousedown:function(f){e.startDragIn(f,"branch")}},{tn:"div",cls:"eg_fc_combine",onmousedown:function(f){e.startDragIn(f,"combine")}},{tn:"div",cls:"eg_fc_zoomout",onmousedown:function(f){e.zoom(1)}},{tn:"div",cls:"eg_fc_zoomin",onmousedown:function(f){e.zoom(-1)}}]},showMenu:function(k,j){if(j&&j.menuAble===false){return}var h=j.getElement();var g=EG.G.SVG.getMousePos(k,h,this.svg);var f=EG.FC.getXY(h)||{x:0,y:0};h=this.pMain.getElement();EG.css(this.dMenu,{top:(g.y+f.y-h.scrollTop)+"px",left:(g.x+f.x-h.scrollLeft)+"px"});this.dMenu.item=j;EG.show(this.dMenu)},init:function(){this.nodes=[];this.handleNodes=[];this.ghostNodes=[];this.connectors=[]},drawCoodinate:function(){var f=20,e=1000,j=1000;var k=e/f,m=j/f;var l="";for(var g=0;g<k;g++){l+=" M"+g*f+",0 L"+g*f+","+j}for(g=0;g<m;g++){l+=" M0,"+g*f+" L"+e+","+g*f}EG.CSVG({pn:this.gCoodinate,tn:"path",d:l,stroke:"#DADADA","stroke-width":"1px"})},addNode:function(e){e.bindEditor(this);this.nodes.push(e);this.gNode.appendChild(e.getElement())},removeNode:function(h,f){for(var g=this.connectors.length-1;g>=0;g--){var e=this.connectors[g];if(e.startNode==h||e.endNode==h){this.removeConnector(e)}}this.gNode.removeChild(h.getElement());EG.Array.remove(this.nodes,h);if(f){this.clearHandle()}},addHandleNode:function(e){e.editor=this;this.gHandle.appendChild(e.getElement());this.handleNodes.push(e)},addGhostNode:function(e){e.editor=this;this.gGhost.appendChild(e.getElement());this.ghostNodes.push(e)},addConnector:function(e){e.bindEditor(this);this.gConnect.appendChild(e.getElement());this.connectors.push(e)},removeConnector:function(e){this.gConnect.removeChild(e.getElement());EG.Array.remove(this.connectors,e)},addHandleConnector:function(e){e.editor=this;this.gHandle.appendChild(e.getElement())},doMovePoint:function(){},doResize:function(){},selectNodes:function(e){EG.hide(this.dMenu);if(e.length==0){return}this.oper.type="selected";this.selectedNodes=e;this.clearGhost();this.clearHandle();this.deSelectConnector();if(this.onSelectNodes){var f=this.onSelectNodes.apply(this,[e]);if(f===false){return}}for(var g=0;g<this.selectedNodes.length;g++){new EG.fc.HandleNode({node:this.selectedNodes[g]})}},selectConnector:function(e){EG.hide(this.dMenu);if(!e){return}this.oper.type="selectedConnector";this.clearGhost();this.clearHandle();this.deSelectConnector();this.curConnector=e;e.select(true)},deSelectConnector:function(){if(this.curConnector){this.curConnector.select(false);this.curConnector=null}},clearHandle:function(){if(this.onClearHandle){this.onClearHandle.apply(this,this.handleNodes)}this.handleNodes=[];EG.DOM.removeChilds(this.gHandle)},clearGhost:function(){this.ghostNodes=[];this.selectRect=null;EG.DOM.removeChilds(this.gGhost)},clearNodes:function(){this.nodes=[];EG.DOM.removeChilds(this.gNode)},clearConnectors:function(){this.connectors=[];EG.DOM.removeChilds(this.gConnect)},clear:function(){this.clearHandle();this.clearGhost();this.clearNodes();this.clearConnectors()},savePos:function(e,f){this.curXY=this.getMousePos(e)},startMoveConnectPoint:function(e,f,g){this.oper.type="moveConnectPoint";this.curConnector=f;this.curConnector.beforeMove();this.curPonitIdx=g},doMoveConnectPoint:function(e){var f=this.getMousePos(e);if(this.curPonitIdx==0){this.curConnector.startX=f.x;this.curConnector.startY=f.y}else{if(this.curPonitIdx==this.curConnector.breaks.length+1){this.curConnector.endX=f.x;this.curConnector.endY=f.y}else{this.curConnector.breaks[this.curPonitIdx-1]=[f.x,f.y]}}this.curConnector.render()},afterMoveConnectPoint:function(e,f){this.oper.type=null;if(f){if(this.curPonitIdx==0){this.curConnector.startNode=f}else{if(this.curPonitIdx==this.curConnector.breaks.length+1){this.curConnector.endNode=f}else{}}}this.curConnector.afterMove(this.curPonitIdx);this.curConnector.render();this.curConnector.select(true);this.curPonitIdx=-1},startConnect:function(e,g){this.oper.type="connect";var f=this.getMousePos(e);this.curConnector=new a({startNode:g,startX:f.x,startY:f.y,endX:f.x,endY:f.y});this.addConnector(this.curConnector)},doConnect:function(e){var f=this.getMousePos(e);this.curConnector.endX=f.x;this.curConnector.endY=f.y;this.curConnector.render()},afterConnect:function(e,f){this.oper.type="w4connect";if(f&&f!=this.curConnector.startNode){this.curConnector.endTo(f)}else{this.gConnect.removeChild(this.curConnector.getElement())}this.curConnector=null},startDragIn:function(e,f){this.oper.type="dragIn";this.dragInType=f;var g=this.getMousePos(e);this.dragInNode=EG.CSVG({tn:"rect",width:this.dragWidth,height:this.dragHeight,x:g.x-this.dragWidth,y:g.y-this.dragHeight,fill:"#F6F6F6",stroke:"black","fill-opacity":0.7,"stroke-dasharray":"2"});this.gGhost.appendChild(this.dragInNode)},doDragIn:function(e){var f=this.getMousePos(e);EG.CSVG({ele:this.dragInNode,x:f.x-this.dragWidth,y:f.y-this.dragHeight})},afterDragIn:function(e){this.oper.type=null;var f=null;switch(this.dragInType){case"start":f=new EG.fc.Start({text:"开始"});break;case"end":f=new EG.fc.End({text:"结束"});break;case"task":f=new EG.fc.Task({text:"节点"+(this.nodes.length==0?"":this.nodes.length+1)});break;case"branch":f=new EG.fc.Branch({text:"分支"+(this.nodes.length==0?"":this.nodes.length+1)});break;case"combine":break}f.moveTo({x:parseInt(this.dragInNode.getAttribute("x")),y:parseInt(this.dragInNode.getAttribute("y"))});this.addNode(f);this.clearGhost();if(this.onAfterDragIn){this.onAfterDragIn.apply(this,[f])}},startMoveNodes:function(e,g){this.oper.type="moveNodes";this.savePos(e);if(this.ghostNodes.length==0){for(var f=0;f<this.handleNodes.length;f++){var j=this.handleNodes[f];var h=new EG.fc.GhostNode({handleNode:j});h.o_p=h.getPos()}}},doMoveNodes:function(e){var g=this.getMousePos(e);for(var f=0;f<this.ghostNodes.length;f++){var h=this.ghostNodes[f];h.moveTo({x:g.x-this.curXY.x+h.o_p.x,y:g.y-this.curXY.y+h.o_p.y})}},afterMoveNodes:function(){this.oper.type=null;if(this.ghostNodes.length==0){return}var g=this.ghostNodes[0].handleNode.getPos();var s=this.ghostNodes[0].getPos();var o={x:s.x-g.x,y:s.y-g.y};var m=[];for(var l=0;l<this.ghostNodes.length;l++){var p=this.ghostNodes[l].handleNode.getPos();this.ghostNodes[l].handleNode.moveTo({x:p.x+o.x,y:p.y+o.y})}for(var l=0;l<this.connectors.length;l++){var h=this.connectors[l];var f=false;var r=false;for(var k=0;k<this.ghostNodes.length;k++){var e=this.ghostNodes[k].handleNode.node;if(h.startNode==e){f=true}else{if(h.endNode==e){r=true}else{if(f&&r){break}}}}if(!f&&!r){continue}if(f&&r){var q=h.breaks;for(var k=0;k<q.length;k++){var n=q[k];n[0]+=o.x;n[1]+=o.y}}h.render()}this.clearGhost()},startChangeSize:function(e,f,g){this.oper.type="changeSize";this.oper.changeSize={behavior:g,handleNode:f};var h=new EG.fc.GhostNode({handleNode:f});h.o_p=h.getPos();this.oper.changeSize.ghostNode=h},doChangeSize:function(n){var e=this.oper.changeSize.handleNode;var o=this.oper.changeSize.ghostNode;var h=this.getMousePos(n);var f=e.getPos();var q=e.getSize();var m=q.height;var k,j,l,g;switch(this.oper.changeSize.behavior){case"nw":f.x=f.x+q.width;f.y=f.y+q.height;break;case"ne":f.y=f.y+q.height;break;case"sw":f.x=f.x+q.width;break;case"se":break;case"n":f.y=f.y+q.height;break;case"s":break;case"w":f.x=f.x+q.width;break;case"e":break}switch(this.oper.changeSize.behavior){case"nw":k=Math.min(f.x,h.x);j=Math.min(f.y,h.y);l=Math.abs(f.x-(h.x));g=Math.abs(f.y-(h.y));break;case"ne":case"sw":case"se":k=Math.min(f.x,h.x);j=Math.min(f.y,h.y);l=Math.abs(f.x-(h.x));g=Math.abs(f.y-(h.y));break;case"n":case"s":k=h.x-130>f.x?Math.min(f.x,h.x):f.x;j=Math.min(f.y,h.y);l=q.width;g=Math.abs(f.y-(h.y));break;case"w":case"e":k=Math.min(f.x,h.x);j=Math.min(f.y,h.y);l=Math.abs(f.x-(h.x));g=q.height;break}o.changeTo({x:k,y:j,width:l,height:g})},afterChangeSize:function(){this.oper.type=null;var e=this.oper.changeSize.handleNode;var f=this.oper.changeSize.ghostNode;e.moveTo(f.getPos());e.sizeTo(f.getSize());this.renderRelateConnectors(e.node);this.clearGhost()},renderRelateConnectors:function(g){for(var f=0;f<this.connectors.length;f++){var e=this.connectors[f];if(e.startNode==g||e.endNode==g){e.render()}}},startSelecting:function(e){this.oper.type="selecting";this.savePos(e);if(!this.selectRect){this.selectRect=EG.DOM.CSVG({tn:"rect",stroke:"black",fill:"#F6F6F6",opacity:0.7,"stroke-width":"1","stroke-dasharray":"2"});this.gGhost.appendChild(this.selectRect)}},doSelecting:function(e){var f=this.getMousePos(e);EG.DOM.CSVG({ele:this.selectRect,x:Math.min(this.curXY.x,f.x),y:Math.min(this.curXY.y,f.y),width:Math.abs(this.curXY.x-f.x),height:Math.abs(this.curXY.y-f.y)})},afterSelecting:function(){this.oper.type=null;var q=parseInt(this.selectRect.getAttribute("x")),p=parseInt(this.selectRect.getAttribute("y")),r=parseInt(this.selectRect.getAttribute("width")),k=parseInt(this.selectRect.getAttribute("height"));EG.DOM.CSVG({ele:this.selectRect,x:-10,y:-10,width:0,height:0});var j=[];for(var g=0;g<this.nodes.length;g++){var e=this.nodes[g];var s=e.getPos();var h=e.getSize();var m=s.x,l=s.y,o=h.width,f=h.height;if(q<=m&&(q+r)>=(m+o)&&p<=l&&(p+k)>=(l+f)){j.push(e)}}if(j.length>0){this.selectNodes(j)}},createNode:function(e){var f;if(e.type=="start"){f=EG.fc.Start}else{if(e.type=="end"){f=EG.fc.End}else{if(e.type=="task"){f=EG.fc.Task}else{f=EG.Object.$eval(e.type)}}}e.editor=this;return new f(e)},createFlow:function(e){e.editor=this;return new a(e)},zoom:function(e){this.zoomLv+=e;if(this.zoomLv<0){this.zoomLv=0}EG.CSVG({ele:this.svg,viewBox:"0 0 "+(1+this.zoomLv*0.2)*this.canvasWidth+" "+(1+this.zoomLv*0.2)*this.canvasHeight})},getMousePos:function(f){var h=EG.Tools.getMousePos(f);var e=this.pMain.getElement();this.docPos=EG.Tools.getElementPos(e);var g=this.pMain.getElement();return{x:h.x-this.docPos.x+g.scrollLeft,y:h.y-this.docPos.y+g.scrollTop}}}})})();