(function(){if(typeof(_EG_NS_)=="undefined"){_EG_NS_=window}var EG=_EG_NS_.EG=window.EG=function(){};EG._className="EG";EG.debug=true;EG.onError=function(sMsg,sUrl,sLine){if(EG.debug){var errorMsg="<div style='text-align:left'><b>原因</b>:"+sMsg+"<br/>";if(sUrl!=null){errorMsg+="<b>行数</b>:"+sLine+"<br/>"}if(sLine!=null){errorMsg+="<b>URL</b>:"+sUrl+"<br/></div>"}if(EG.Locker){EG.Locker._lock=true;EG.Locker.message({message:errorMsg,closeable:true,force:true})}else{alert(errorMsg)}}};EG.doc=document;EG.body=null;EG.getBody=function(){if(!EG.body){EG.body=EG.doc.body}return EG.body};EG.head=null;EG.getHead=function(){if(!EG.head){EG.head=EG.doc.head}return EG.head};EG.unnull=function(){for(var i=0;i<arguments.length;i++){if(arguments[i]!=null){return arguments[i]}}return null};EG.n2d=function(o,d){if(o==null){return d}else{return o}};EG.f2o=function(o){if(o==null){return null}if(typeof(o)=="function"){return o()}else{return o}};EG.getScriptPath=function(scriptName){var js=EG.doc.scripts;var jsPath;for(var i=js.length;i>0;i--){if(js[i-1].src.indexOf(scriptName)>-1){jsPath=js[i-1].src.substring(0,js[i-1].src.lastIndexOf("/")+1);if(jsPath.indexOf("http://")>=0){jsPath=jsPath.substring(jsPath.indexOf("/",10))}return jsPath}}return null};var scriptPath="eg";EG.basePath=window._EG_BASEPATH_||EG.getScriptPath(scriptPath);EG.contextPath="";EG.copy=function(obj,props,override,clone){for(var key in props){if(override||typeof(obj[key])=="undefined"){if(clone){obj[key]=EG.clone(props[key])}else{obj[key]=props[key]}}}return obj};EG.getType=function(o){return Object.prototype.toString.call(o).slice(8,-1).toLowerCase()};var types={"undefined":1,"null":1,number:1,"boolean":1,string:1,array:2,"function":3,date:4};EG._clone=function(dObj,sObj,key){var val=sObj[key];var type=types[EG.getType(val)];if(1==type){dObj[key]=val}else{if(4==type){dObj[key]=new Date();dObj[key].setTime(val.getTime())}else{dObj[key]=EG.clone(val)}}};EG.clone=function(obj){if(obj==null){return null}if(typeof(obj)=="undefined"){throw new Error("EG.clone#待clone参数未定义")}var cObj,typeNum,i,il,fn_body,fn_args;var type=EG.getType(obj);typeNum=types[type];cObj=obj;if(!typeNum){if(type!="object"){}else{if(obj==null||obj.constructor==null){alert(obj)}if(obj.constructor!=Object){cObj=obj}else{cObj=new obj.constructor();for(i in obj){EG._clone(cObj,obj,i)}}}}else{if(2==typeNum){cObj=[];for(i=0,il=obj.length;i<il;i++){EG._clone(cObj,obj,i)}}else{if(3==typeNum){cObj=obj}else{if(4==typeNum){cObj=new Date();cObj.setTime(obj.getTime())}}}}return cObj};EG._markLoadPath=false;EG.Repository=function(container){if(!container){container={}}this.container=container};EG.Repository.prototype={put:function(key,data){var ks=key.split("."),obj=this.container;for(var i=0,il=ks.length;i<il;i++){if(i==(il-1)){obj[ks[i]]=data}else{if(!obj[ks[i]]){obj[ks[i]]={}}obj=obj[ks[i]]}}},get:function(key){var ks=key.split("."),obj=this.container;for(var i=0,il=ks.length;i<il;i++){if(!obj[ks[i]]){return null}obj=obj[ks[i]]}return obj},defineNamespace:function(namespace,fn){if(!fn){fn=EG._newConst()}var ss=namespace.split(".");var p=this.container;for(var i=0;i<ss.length;i++){if(!p[ss[i]]){if(i<ss.length-1){p[ss[i]]=EG._newConst();p[ss[i]]._undefined=true}else{p[ss[i]]=fn}}p=p[ss[i]]}return p}};EG._defaultRepository=new EG.Repository(_EG_NS_);EG.put=function(key,data){EG._defaultRepository.put(key,data)};EG.get=function(key){return EG._defaultRepository.get(key)};EG.Loader=function(cfg){if(!cfg){cfg={}}this.repository=cfg.repository||EG._defaultRepository};EG.Loader.prototype={_isLoader:true,load:function(cfg,callback,onError){var me=this;cfg=this.convert(cfg);var key=cfg.key||cfg.path;var type=cfg.type;var path=cfg.path;var r=this.find(key);if(r){return callback(r,cfg)}else{if(callback){me.onReady(cfg,function(c){callback(c,cfg)})}}var url=this.getUrl(type,key,path);if(type=="class"){if(!EG.Browser.isLocal()){EG.Ajax.send({url:url,method:"GET",callback:function(text){if(EG._markLoadPath){text=("//@ sourceURL="+url+"\n"+text)}eval(text)},erhandler:onError})}else{var el=EG.CE({tn:"script",type:"text/javascript",charset:"UTF-8",src:url});EG.getHead().appendChild(el)}}else{if(type=="text"){EG.Ajax.send({url:url,method:"GET",callback:function(text){var obj={path:url,item:text};me.repository.put(key,obj);me.doAfterReady(key,obj)},erhandler:onError})}else{if(type=="ele"){EG.Ajax.send({url:url,method:"GET",callback:function(text){var ele=EG.CE({tn:"div"});ele.innerHTML=text;var obj={path:url,item:ele.childNodes[0]};me.repository.put(key,obj);me.doAfterReady(key,obj)},erhandler:onError})}else{if(type=="img"){var img=EG.CE({tn:"img",onload:function(event){var obj={path:url,item:this};me.repository.put(key,obj);me.doAfterReady(key,obj)},onerror:function(){if(onError){onError.apply(this,arguments)}}});img.src=url}}}}},getUrl:function(type,key,path){if(type=="class"){return this.getClassUrl(key)}else{return path}},getClassUrl:function(className){var ss=className.split(".");ss.shift();var classFileName=ss[ss.length-1];ss.pop();return EG.basePath+"src/"+ss.join("/").toLowerCase()+"/"+classFileName+".js"},find:function(key){if(typeof(key)!="string"){key=key.key||key.path}var d=this.repository.get(key);if(d==null){return null}return d},isLoaded:function(cfg){var d=this.find(cfg);if(d==null||d._defineUnfinish){return false}return true},convert:function(cfg){if(typeof(cfg)=="string"){cfg={key:cfg,type:"class"}}return cfg},readyEvents:{},onReady:function(cfg,callback){cfg=this.convert(cfg);var key=cfg.key||cfg.path;var d=this.find(key);if(d){callback(d,cfg)}else{if(!this.readyEvents[key]){this.readyEvents[key]=[]}this.readyEvents[key].push(callback)}},doAfterReady:function(key,obj){if(this.readyEvents[key]){var ev;while((ev=this.readyEvents[key].shift())){ev(obj)}}},require:function(reqs,onFinish,onError){var me=this;if(!reqs.reqEd){reqs.reqEd=[]}if(reqs.length==0){var rs=[];for(var i=0;i<reqs.reqEd.length;i++){var ed=reqs.reqEd[i];var r=this.find(ed);if(r==null){throw new Error("未找到："+ed)}rs.push(r)}return onFinish.apply(this,rs)}var req=reqs.shift();reqs.reqEd.push(req);var fn=function(){if(me.isLoaded(req)){return me.require(reqs,onFinish,onError)}else{return me.load(req,function(){return me.require(reqs,onFinish,onError)},onError)}};var clazz=this.find(req);if(clazz!=null&&clazz._defineUnfinish){this.onReady(req,fn)}else{fn()}}};EG._defaultLoader=new EG.Loader();EG.onReady=function(){EG._defaultLoader.onReady.apply(EG._defaultLoader,arguments)};EG.nativeMethod={initConfig:function(cfg){if(cfg){for(var key in cfg){var cK=key+"Config";if(typeof(this[cK])!="undefined"){this[cK]=cfg[key]}else{this[key]=cfg[key]}}}},getSuperclass:function(){return this.getClass()._superClass},callSuper:function(name,args){var o;var fn=arguments.callee.caller;var sc=(fn._className?fn:fn._class)._superClass;if(!sc){throw new Error("没有父类")}var methodName,params;if(arguments.length==0){methodName="$constructor";params=[]}else{if(arguments.length==1){if(typeof(arguments[0])=="string"){methodName=arguments[0];params=[]}else{methodName="$constructor";params=arguments[0]}}else{methodName=arguments[0];params=arguments[1]}}if(methodName=="$constructor"){o=sc.apply(this,params)}else{o=sc.prototype[methodName].apply(this,params)}return o},getClass:function(){return this._class}};EG._newConst=function(){return function(){var c=arguments.callee;var args=arguments;if(c._config){EG.copy(this,EG.clone(c._config))}if(c._constructor){if(c._beforeConstructor){var me=this;return c._beforeConstructor.apply(this,[function(){return c._constructor.apply(me,args)}])}else{return c._constructor.apply(this,args)}}}};EG._alias={};EG._defineClass=function(cfg,clazz,name,loader){if(!cfg){cfg={}}var statics=cfg.statics,config=cfg.config||{},constructor=cfg.constructor,extend=cfg.extend,beforeConstructor=cfg.beforeConstructor;if(config){config=EG.clone(config)}if({}.constructor==constructor){constructor=null}if(constructor){constructor._class=clazz}clazz._constructor=constructor;clazz._config=config;clazz._beforeConstructor=beforeConstructor;if(name){clazz._className=name}if(extend){if(typeof(extend)=="string"){extend=loader.find(extend)}var f=function(){};f.prototype=extend.prototype;clazz.prototype=new f();clazz._superClass=extend;if(extend._config){EG.copy(config,EG.clone(extend._config),false)}}var prototyps=clazz.prototype;EG.copy(prototyps,EG.nativeMethod);prototyps._class=clazz;for(var key in cfg){if(key!="extend"&&key!="alias"&&key!="statics"&&key!="config"&&key!="constructor"&&key!="beforeConstructor"&&key!="require"){prototyps[key]=cfg[key];if(typeof(cfg[key])=="function"){prototyps[key]._class=clazz}}}if(statics){EG.copy(clazz,statics,true)}if(extend){if(extend.afterExtend){extend.afterExtend(clazz)}}clazz._defineUnfinish=false;delete clazz._defineUnfinish;if(cfg.alias){EG._alias[cfg.alias]=clazz}if(name){loader.doAfterReady(name,clazz)}return clazz};EG.define=function(){var name,reqs,cfg,loader;for(var i=0;i<arguments.length;i++){var arg=arguments[i];var type=EG.getType(arg);if(!name){if(type=="string"){name=arg;continue}}if(!reqs){if(type=="array"){reqs=arg;continue}}if(!cfg){if(type=="object"||type=="function"){cfg=arg;continue}}if(!loader){if(arg._isLoader){loader=arg;continue}}}loader=loader||EG._defaultLoader;reqs=reqs||[];var clazz;if(name){clazz=loader.find(name);if(!clazz){clazz=loader.repository.defineNamespace(name,clazz)}else{if(clazz._undefined===false){throw new Error("类已定义:"+name)}}}else{clazz=EG._newConst()}clazz._defineUnfinish=true;if(typeof(cfg)=="function"){loader.require(reqs,function(){var args=[];for(var i=0;i<arguments.length;i++){args.push(arguments[i])}args.push(clazz);cfg=cfg.apply(this,args);return EG._defineClass(cfg,clazz,name,loader)});return clazz}else{var extend=cfg.extend;var require=cfg.require;if(typeof(extend)=="string"){reqs.push(extend)}if(require){reqs=reqs.concat(require)}loader.require(reqs,function(){return EG._defineClass(cfg,clazz,name,loader)});return clazz}};EG.isInstanceof=function(obj,pClazz){if(obj==null){return false}return EG.isAssignableFrom(obj.getClass(),pClazz)};EG.isAssignableFrom=function(clazz,pClazz){if(clazz==pClazz){return true}while(clazz._superClass){clazz=clazz._superClass;if(clazz==pClazz){return true}}return false};EG.findClass=function(classNameExp){return EG.findClasses(classNameExp,false)[0]};EG.findClasses=function(classNameExp,returnObj,pClass,classes,packageName){classes=classes||((returnObj)?{}:[]);pClass=pClass||_EG_NS_;packageName=packageName||"";if(!classNameExp){return classes}var classEles=classNameExp.split(".");var classNamePattern=classEles.shift().replace("*",".*");for(var key in pClass){if((typeof(pClass[key])=="function"||typeof(pClass[key])=="object")&&new RegExp("^"+classNamePattern+"$","g").test(key)){var fullKey=packageName?(packageName+"."+key):key;if(classEles.length>0){EG.findClasses(classEles.join("."),returnObj,pClass[key],classes,fullKey)}else{if(returnObj){classes[fullKey]=(pClass[key])}else{classes.push(pClass[key])}}}}return classes};EG.findMethods=function(clazz,methodNameExp,rangeType,returnObj){var ms=((returnObj)?{}:[]);rangeType=rangeType||"all";methodNameExp=methodNameExp.replace("*",".*");var f=function(range){for(var key in range){if(typeof(range[key])=="function"&&new RegExp("^"+methodNameExp+"$","g").test(key)){if(returnObj){ms[key]=range[key]}else{ms.push(range[key])}}}};if(rangeType=="all"||rangeType=="prototype"){f(clazz.prototype)}if(rangeType=="all"||rangeType=="static"){f(clazz)}return ms};EG.isAssignbleFrom=function(obj,klass){var sc=obj.getClass();while(sc=sc._superClass){if(sc==klass){return true}}return false};EG.put=function(key,data){var ks=key.split("."),obj=_EG_NS_;for(var i=0,il=ks.length;i<il;i++){if(i==(il-1)){obj[ks[i]]=data}else{if(!obj[ks[i]]){obj[ks[i]]={}}obj=obj[ks[i]]}}};EG.get=function(key){var ks=key.split("."),obj=_EG_NS_;for(var i=0,il=ks.length;i<il;i++){if(!obj[ks[i]]){return null}obj=obj[ks[i]]}return obj};EG._importCache={};EG.importPath=function(keys,flag){};EG.importPath_static=function(){return EG.importPath(arguments,2)};EG.importPath_class=function(){return EG.importPath(arguments,1)};EG.showSource=function(text){if(!EG.showSourceEle){EG.showSourceEle=EG.CE({tn:"div",style:"position:absolute;z-index:9999",cn:[{tn:"textarea",style:"width:500px;height:500px"},{tn:"button",innerHTML:"hide",onclick:function(){EG.hide(EG.showSourceEle)}}]});EG.getBody().appendChild(EG.showSourceEle)}EG.showSourceEle.childNodes[0].value=text;EG.show(EG.showSourceEle)}})();(function(){EG.define("EG.String",function(a){return{statics:{isString:function(b){return typeof(b)==="string"},n2e:function(b){return b!=null?b:""},isBlank:function(b){return(b==null||b===""||a.trim(b)==="")},isEmpty:function(b){return(b==null||b===""||a.trim(b)==="")},equals:function(e,c,b){return b==true?e.toUpperCase()===c.toUpperCase():e===c},startWith:function(g,f,c){if(g==null||f==null){return false}var b=f.length;if(g.length<b){return false}var e;if(c){for(e=0;e<b;e++){if(f.charAt(e).toLocaleLowerCase()!=g.charAt(e).toLocaleLowerCase()){return false}}}else{for(e=0;e<b;e++){if(f.charAt(e)!=g.charAt(e)){return false}}}return true},endWith:function(e,c,b){return(e!==null&&c!==null&&(b==true?e.toUpperCase().lastIndexOf(c.toUpperCase())>0:e.lastIndexOf(c)>=(e.length-c.length)))},trim:function(b){return b.replace(/(^\s*)|(\s*$)/g,"")},removeEnd:function(c,b){return(a.endWith(c,b))?c.substring(0,c.length-b.length):c},removeStart:function(c,b){return(a.startWith(c,b))?c.substring(b.length,c.length):c},replaceAll:function(e,c,b){return e.replace(new RegExp(c,"g"),b)}}}})})();(function(){EG.define("EG.Date",function(f){var h=1*1000;var e=60*1000;var c=60*e;var b=24*c;var g=7*b;var a=30*b;var j=12*a;return{statics:{format:function(o,n){if(arguments.length==1){n=arguments[0];o=new Date()}if(typeof(o)=="number"){o=new Date(o)}else{if(typeof(o)=="string"){return o}}var t="";var k=[];for(var q=0;q<n.length;q++){var r=n.charAt(q);if(t.length>0&&t.charAt(t.length-1)!=r){k.push(t);t=r}else{t+=r}}k.push(t);for(var q=0;q<k.length;q++){var m=k[q];var u=m;var l=false;if(m=="yyyy"||m=="yy"){u=o.getFullYear();if(m=="yy"){u=u.substr(2)}}else{if(m=="MM"||m=="M"){u=o.getMonth()+1;l=(m=="MM")}else{if(m=="dd"||m=="d"){u=o.getDate();l=(m=="dd")}else{if(m=="HH"||m=="H"){u=o.getHours();l=(m=="HH")}else{if(m=="mm"||m=="m"){u=o.getMinutes();l=(m=="mm")}else{if(m=="ss"||m=="s"){u=o.getSeconds();l=(m=="ss")}}}}}}if(l){if(u<10){u="0"+u}}k[q]=u}return k.join("")},d2s:function(m,q,l){if(!m){m=new Date()}var r=m.getFullYear();var p=m.getMonth()+1;p=p<10?("0"+p):p;var s=m.getDate();s=s<10?("0"+s):s;if(q==null){q=true}if(q){var o=m.getHours();o=o<10?("0"+o):o;var n=m.getMinutes();n=n<10?("0"+n):n;var k=m.getSeconds();k=k<10?("0"+k):k;if(l){return(r+"年"+p+"月"+s+"日"+o+" 小时:"+n+"分"+k+"秒")}else{return(r+"-"+p+"-"+s+" "+o+":"+n+":"+k)}}else{if(l){return(r+"年"+p+"月"+s+"日")}else{return(r+"-"+p+"-"+s)}}},s2d:function(l){var k=new Date();if(l.length==4){return new Date(l,k.getMonth(),k.getDay(),k.getHours(),k.getMinutes(),k.getSeconds())}else{if(l.length==7){return new Date(l.substring(0,4),l.substring(5,7)-1,k.getDay(),k.getHours(),k.getMinutes(),k.getSeconds())}else{if(l.length==10){return new Date(l.substring(0,4),l.substring(5,7)-1,l.substring(8,10),k.getHours(),k.getMinutes(),k.getSeconds())}else{if(l.length==13){return new Date(l.substring(0,4),l.substring(5,7)-1,l.substring(8,10),l.substring(11,13),k.getHours(),k.getMinutes(),k.getSeconds())}else{if(l.length==16){return new Date(l.substring(0,4),l.substring(5,7)-1,l.substring(8,10),l.substring(11,13),l.substring(14,16),k.getSeconds())}else{if(l.length==19){return new Date(l.substring(0,4),l.substring(5,7)-1,l.substring(8,10),l.substring(11,13),l.substring(14,16),l.substring(17,19))}else{alert("str:"+l);throw new Error("参数不完整")}}}}}}},compare:function(l,k){if(typeof(l)=="string"){l=EG.Date.s2d(l)}if(typeof(k)=="string"){k=EG.Date.s2d(k)}return l.getTime()-k.getTime()},l2d:function(k){var m=new Date();m.setTime(k);return m},l2s:function(k){return EG.Date.d2s(EG.Date.l2d(k))},getMonthday:function(l){if(l==null){l=EG.clone(this.curDate)}else{if(typeof(l)=="string"){l=EG.Date.s2d(l);l=new Date(d.getFullYear(),d.getMonth(),0)}}var k=l.getMonth();var n=l.getFullYear();if(k==1){return(((n%4==0)&&(n%100!=0)||(n%400==0))?29:28)}else{return(f.solarMonthday[k])}},solarMonthday:[31,28,31,30,31,30,31,31,30,31,30,31],sameDay:function(l,k){return l.getYear()==k.getYear()&&l.getMonth()==k.getMonth()&&l.getDate()==k.getDate()},toSimpleFmt:function(k){var m=k.split(":");var n=parseInt(m[0]);var l=parseInt(m[1]);if(n>=13){return"下午"+(n-12)+""+(l>0?(":"+l):"点")}else{return"上午"+(n)+(l>0?(":"+l):"点")}},space:function(l,k){if(k==null){k=new Date()}var p=EG.Date.compare(k,l);if(p<0){return"未来"}var o=[[j,"年"],[a,"月"],[g,"周"],[b,"日"],[c,"年"],[e,"分"],[h,"秒"]];var n="";for(var m=0;m<o.length;m++){if(p>o[m][0]){n=parseInt(p/o[m][0])+o[m][1];break}}return n+"前"},unit_second:h,unit_min:60*1000,unit_hour:60*e,unit_day:24*c,unit_week:7*b,unit_month:30*b,unit_year:12*a}}})})();(function(){var ME=EG.define("EG.Object",{statics:{extract:function(arr,kKey,vKey){var o={};for(var i=0,il=arr.length;i<il;i++){o[arr[i][kKey]]=vKey!=null?arr[i][vKey]:arr[i]}return o},$in:function(obj,arr){if(arr==null){return false}for(var i=0,il=arr.length;i<il;i++){if(ME.equals(arr[i],obj)){return true}}return false},isLit:function(obj){return obj.constructor==Object},equals:function(o1,o2){if(o1==null&&o2==null){return true}var ot=EG.getType(o1);if(ot!=EG.getType(o2)){return false}switch(ot){case"string":case"function":case"boolean":case"number":return(o1===o2);case"object":for(var k in o1){if(!ME.equals(o1[k],o2[k])){return false}}return true;case"array":if(o1.length!=o2.length){return false}for(var i=0,il=o1.length;i<il;i++){if(!ME.equals(o1[i],o2[i])){return false}}return true;default:throw new Error("EG.Object#equals:暂不支持类型"+ot)}},hasKey:function(obj,keys){if(!EG.Array.isArray(keys)){keys=[keys]}for(var i=0;i<keys.length;i++){var has=false;for(var k in obj){if(keys[i]===k){has=true;break}}if(!has){return false}}return true},delKey:function(obj,keys){if(!EG.Array.isArray(keys)){keys=[keys]}for(var i=0;i<keys.length;i++){var key=keys[i];if(key.indexOf(".")>=0){var wdks=[];for(var k in obj){if(EG.RegExp.match(k,key)){wdks.push(k)}}for(var j=0;j<wdks.length;j++){delete obj[wdks[j]]}}else{delete obj[key]}}},$eval:function(str){var o=null;if(str==null||str.trim()==""){return null}eval("o="+str);return o},fetchKeys:function(src,keys){var o={};for(var i=0;i<keys.length;i++){o[keys[i]]=src[keys[i]]}return o}}});EG.$in=ME.$in;EG.isLit=ME.isLit})();(function(){EG.define("EG.Array",function(a){EG.copy(a,{isArray:function(b){return b&&typeof b==="object"&&typeof b.length==="number"&&typeof b.splice==="function"&&!(b.propertyIsEnumerable("length"))},del:function(b,c){if(c==null){throw new Error("EG.Array#del:待删除坐标不能为null")}b.splice(c,1);return b},remove:function(b,e){for(var c=b.length;c>=0;c--){if(e===b[c]){a.del(b,c)}else{if(e instanceof Function){if(e(b[c])){a.del(b,c)}}}}return b},insert:function(b,e,c){b.splice(e,0,c);return b},first:function(b){return a.get(b,0)},last:function(b){return a.get(b,b.length-1)},get:function(b,c){if(typeof(c)=="number"){if(b.length==0){return null}if(c<0){c=b.length+c}return b[c]}else{if(typeof(c)=="string"){if(c=="n"){c="last"}return a[c](b)}else{throw new Error("EG.Array#get:参数错误"+c)}}},has:function(b,f){if(!b){return false}for(var e=0,c=b.length;e<c;e++){if(f===b[e]){return true}}return false},each:function(b,f){if(!b||!f){return null}var g=null;for(var e=0,c=b.length;e<c;e++){g=f.apply(b[e],[e,b[e]]);if(g===false){break}}},extract:function(b,f){var g=[];for(var e=0,c=b.length;e<c;e++){g.push(b[e][f])}return g},extract2Arrays:function(b,l,m){if(!m){m=[]}for(var h=0,f=b.length;h<f;h++){var e=[];for(var g=0;g<l.length;g++){var c=l[g];if(typeof(c)=="string"){e.push(b[h][c])}else{e.push(c.apply(this,[b[h]]))}}m.push(e)}return m},extract2Obj:function(c,g,b){var h={};for(var f=0,e=c.length;f<e;f++){h[c[f][g]]=b!=null?c[f][b]:c[f]}return h},clear:function(b){b.splice(0,b.length);return b},sub:function(e,g,c){var b=[];for(var f=g;f<c;f++){b.push(e[f])}return b},addAll:function(c,b){Array.prototype.push.apply(c,b);return c},getIdx:function(b,f){for(var e=0,c=b.length;e<c;e++){if(f===b[e]){return e}}return -1},fetch:function(c,b,l,k){var e=EG.isArray(l);var h=[];for(var g=0;g<c.length;g++){if(e){for(var f=0;f<l.length;f++){if(c[g][b]==l[f]){h.push(c[g])}}}else{if(c[g][b]==l){return k!=null?c[g][k]:c[g]}}}return e?h:null}});EG.each=a.each;EG.isArray=a.isArray})})();(function(){EG.define("EG.RegExp",{statics:{match:function(b,a){return new RegExp("^"+a+"$").test(b)}}})})();(function(){var a=EG.define("EG.Ajax",{statics:{JSONP_callbackers:{},getXMLHttpRequest:function(){var b=null;if(window.XMLHttpRequest){b=new XMLHttpRequest()}else{if(window.ActiveXObject){try{b=new ActiveXObject("Msxml2.XMLHTTP")}catch(e){try{b=new ActiveXObject("Microsoft.XMLHTTP")}catch(c){b=null}}}}return b},javaURLEncoding:function(b){return window.encodeURIComponent(b).replace(/!/g,"%21").replace(/\"/g,"%22").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/~/g,"%7E")},handleJSONP:function(c,b){var e=new Date().getTime()+"_"+Math.random()*1000;if(a.tag_JSONP){EG.DOM.remove(a.tag_JSONP)}a.tag_JSONP=EG.CE({pn:EG.getBody(),tn:"script",type:"text/javascript"});a.JSONP_callbackers[e]=function(){var f=EG.copy(b);delete f.url_jsonp;a.send.apply(this,[f])};a.tag_JSONP.src=c+'EG.Ajax.sendJSONPBack("'+e+'")';return},send:function(l){if(l==null){throw new Error("EG.Ajax.call#参数不能为空")}var j=l.url_jsonp;if(j){a.handleJSONP(j,l);return}var e=l.url,f=l.charset||"UTF-8",p=EG.Ajax.getXMLHttpRequest(),c=EG.unnull(l.method,"GET"),g=EG.unnull(l.async,true),r=EG.unnull(l.callback,""),b=l.erhandler,m=EG.unnull(l.content,""),o=l.timeout||30*1000,q=EG.unnull(l.contentType,"application/x-www-form-urlencoded"),n=l.xhrFields;if(g){p.onreadystatechange=function(){if(p.readyState==4){if(p.status==200){if(r){return r(p.responseText,p)}}else{if(b){return b(p.status,p)}else{throw new Error("EG.Ajax#send:"+p.status+": "+p.statusText+",\n["+c+"]"+e)}}}return null}}if(!EG.Browser.isIE()){p.timeout=o}p.open(c,e,g);if(n){for(var h in n){p[h]=n[h]}}if(q){p.setRequestHeader("Content-Type",q)}p.send(m);if(!g&&r){return r(p.responseText,p)}return},sendJSONPBack:function(c){var b=a.JSONP_callbackers[c];if(b){delete a.JSONP_callbackers[c];b()}}}});a.globeRequest=a.getXMLHttpRequest()})();(function(){EG.define("EG.Browser",[],function(f){f.Sys={};var g=navigator.appVersion;var a=navigator.userAgent;var b=navigator.userAgent.toLowerCase();var c;(c=b.match(/msie ([\d.]+)/))?f.Sys.ie=c[1]:(c=b.match(/firefox\/([\d.]+)/))?f.Sys.firefox=c[1]:(c=b.match(/chrome\/([\d.]+)/))?f.Sys.chrome=c[1]:(c=b.match(/opera.([\d.]+)/))?f.Sys.opera=c[1]:(c=b.match(/version\/([\d.]+).*safari/))?f.Sys.safari=c[1]:0;var e=!window.location.domain;f.Version={trident:a.indexOf("Trident")>-1,presto:a.indexOf("Presto")>-1,webkit:a.indexOf("AppleWebKit")>-1,gecko:a.indexOf("Gecko")>-1&&a.indexOf("KHTML")==-1,mobile:!!a.match(/.*Mobile.*/),ios:!!a.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),android:a.indexOf("Android")>-1||a.indexOf("Linux")>-1,iphone:a.indexOf("iPhone")>-1||a.indexOf("Mac")>-1,ipad:a.indexOf("iPad")>-1,webApp:a.indexOf("Safari")==-1};return{statics:{isIE:function(){return(!!window.ActiveXObject||"ActiveXObject" in window)},isIE6:function(){return f.isIE()&&f.getIEVersion()==6},isIE7:function(){return f.isIE().tooUp&&f.getIEVersion()==7},isIE8:function(){return f.isIE()&&f.getIEVersion()==8},getIEVersion:function(){if(f.Sys.ie){return parseInt(f.Sys.ie.substr(0,f.Sys.ie.indexOf(".")))}else{return -1}},isIE8Before:function(){return f.isIE()&&f.getVersion()<=8},isFirefox:function(){return f.Sys.firefox!=null},isChrome:function(){return f.Sys.chrome!=null},isSafari:function(){return f.Sys.safari!=null},isOpera:function(){return f.Sys.opera!=null},getVersion:function(){return f.Sys.ie||f.Sys.firefox||f.Sys.chrome||f.Sys.safari||f.Sys.opera},getDomainAddress:function(){return window.location.href.substr(0,window.location.href.indexOf("/",10))},isLocal:function(){return e}}}})})();(function(){var ME=EG.define("EG.Tools",{statics:{clear:function(key){if(window.localStorage){localStorage.removeItem(key)}},save:function(key,value){var s=ME.toJSON(value);if(window.localStorage){localStorage.setItem(key,s)}},query:function(key){if(!window.localStorage){return null}var s=localStorage.getItem(key);return EG.Object.$eval(s)},post2:function(url,params){var f=EG.CE({pn:EG.getBody(),tn:"form",action:url,method:"post",style:"display:none"});for(var x in params){EG.CE({pn:f,tn:"textarea",name:x,value:params[x]})}f.submit();return f},filterSpecChar:function(str){return EG.String.replaceAll(EG.String.replaceAll(str,"<","&lt;"),">","&gt;")},getDPI:function(){var arrDPI;if(window.screen.deviceXDPI!=undefined){arrDPI=[window.screen.deviceXDPI,window.screen.deviceYDPI]}else{var tmpNode=EG.CE({tn:"div",style:"width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden"});document.body.appendChild(tmpNode);arrDPI=[parseInt(tmpNode.offsetWidth),parseInt(tmpNode.offsetHeight)];tmpNode.parentNode.removeChild(tmpNode)}return arrDPI},getPerInchPix:function(){if(!ME.gDPIRate){var tmpNode=EG.CE({tn:"div",style:"width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden"});document.body.appendChild(tmpNode);ME.gDPIRate=[parseInt(tmpNode.offsetWidth),parseInt(tmpNode.offsetHeight)];tmpNode.parentNode.removeChild(tmpNode)}return ME.gDPIRate},isPressLeft:function(e){e=EG.Event.getEvent(e);if(e.which!=null){return e.which==1}else{return e.button==1}},isPressRight:function(e){e=EG.Event.getEvent(e);if(e.which!=null){return e.which==3}else{return e.button==2}},isPressCtrl:function(e){e=EG.Event.getEvent(e);return e.ctrlKey||e.keyCode==17},getParam:function(name){var params=EG.doc.location.search;params=params.substring(1);if(!params){return""}var paramArr=params.split("&");for(var i=0,il=paramArr.length;i<il;i++){if(paramArr[i].indexOf(name)!=-1){return paramArr[i].substring(paramArr[i].indexOf("=")+1)}}return null},getParams:function(){var params={};var strParams=EG.doc.location.search;strParams=strParams.substring(1);if(!strParams){return{}}var paramArr=strParams.split("&");for(var i=0,il=paramArr.length;i<il;i++){params[paramArr[i].substring(0,paramArr[i].indexOf("="))]=paramArr[i].substring(paramArr[i].indexOf("=")+1)}return params},toJSON:function(obj,as){if(as==null){as=[]}if(obj==null){return"null"}if(typeof(obj)=="object"){as.push(obj);var json=[];if(obj instanceof Array){for(var i=0;i<obj.length;i++){var o=obj[i];json[i]=(o!==null)?ME.toJSON(o,as):"null"}return"["+json.join(", ")+"]"}else{for(var key in obj){if(!obj.hasOwnProperty(key)){continue}var o=obj[key];if(typeof(o)=="object"&&EG.Array.has(as,o)){continue}json.push('"'+key+'" : '+((o!=null)?ME.toJSON(o,as):"null"))}return"{\n "+json.join(",\n ")+"\n}"}}else{if(typeof(obj)=="string"){return'"'+obj.replace(/\\/g,"\\\\").replace(/"/g,'\\"')+'"'}else{if(typeof(obj)=="boolean"){return obj}else{if(typeof(obj)=="function"){return obj}else{if(typeof(obj)=="number"){return obj}else{if(typeof(obj)=="regexp"){return obj}else{throw new Error("Engin#toJSON:不支持类型:"+typeof(obj))}}}}}}},toXMLDoc:function(text){var xmlDoc;try{xmlDoc=new ActiveXObject("Microsoft.XMLDOM");xmlDoc.async="false";xmlDoc.loadXML(text)}catch(e){try{parser=new DOMParser();xmlDoc=parser.parseFromString(text,"text/xml")}catch(e){alert(e.message)}}return xmlDoc},getScriptPath:EG.getScriptPath,getMousePos:function(evt,refer){var mp;evt=EG.Event.getEvent(evt);if(evt.pageX||evt.pageY){mp={x:evt.pageX,y:evt.pageY}}else{mp={x:evt.clientX+EG.getBody().scrollLeft-EG.getBody().clientLeft,y:evt.clientY+EG.getBody().scrollTop-EG.getBody().clientTop}}if(refer){var rp=ME.getElementPos(refer);mp={x:mp.x-rp.x,y:mp.y-rp.y}}return mp},getElementPos:function(ele,parent,isOffsetParent){if(isOffsetParent==null){isOffsetParent=true}if(!parent){parent=EG.getBody()}var t=ele.offsetTop;var l=ele.offsetLeft;while((ele=isOffsetParent?ele.offsetParent:ele.parentNode)!=parent&&ele!=null){t+=ele.offsetTop;l+=ele.offsetLeft}return{x:l,y:t}},debugObject:function(obj){var s="";for(var key in obj){s+=key+","}alert(s)},aop:function(expression,before,after){if(!before&&!after){return}var mType=-1;if(mType<0){mType=expression.indexOf("###")>0?2:-1}if(mType<0){mType=expression.indexOf("##")>0?1:-1}if(mType<0){mType=expression.indexOf("#")>0?0:-1}if(mType<0){throw new Error("EG#aop:表达式错误>"+expression)}var classNameExp=expression.substr(0,expression.indexOf("#"));var methodNameExp=expression.substr(expression.lastIndexOf("#")+1);var classes=EG.findClasses(classNameExp,true);var cms={};for(var className in classes){(function(){var clazz=classes[className];var f=function(num,tp){var ms=EG.findMethods(clazz,methodNameExp,tp,"all");for(var methodName in ms){cms[className+","+num+","+methodName]=ms[methodName]}};if(mType==2||mType==1){f(1,"static")}if(mType==2||mType==0){f(0,"prototype")}})()}for(var cmName in cms){(function(){var method=cms[cmName];var ks=cmName.split(",");var className=ks[0];var isProptype=(ks[1]=="0");var methodName=ks[2];var clazz=classes[className];if(isProptype){clazz.prototype[methodName]=function(){if(before){before.apply(this,[clazz,className,method,methodName,isProptype])}var r=method.apply(this,arguments);if(after){after.apply(this,[clazz,className,method,methodName,isProptype])}return r}}else{clazz[methodName]=function(){if(before){before(clazz,className,method,methodName,isProptype)}var s="";for(var i=0,il=arguments.length;i<il;i++){if(s!=0){s+=","}s+=("arguments["+i+"]")}var r=null;var p="r=new method("+s+");";eval(p);if(after){after(clazz,className,method,methodName,isProptype)}return r}}})()}},getText:function(textvalues,value){for(var i=0;i<textvalues.length;i++){if(textvalues[i][1]==value){return textvalues[i][0]}}return null},getEllipsis:function(txts,count,endChar){var t="";if(!endChar){endChar=""}if(txts.length>count){t=EG.Array.sub(txts,0,count).join(",");t+="...("+(txts.length)+endChar+")"}else{t=txts.join(",")}return t},convertBase64UrlToBlob:function(urlData){var bytes=window.atob(urlData.split(",")[1]);var ab=new ArrayBuffer(bytes.length);var ia=new Uint8Array(ab);for(var i=0;i<bytes.length;i++){ia[i]=bytes.charCodeAt(i)}return new Blob([ab],{type:"image/png"})},watch:function(expression){ME.aop(expression,ME.watch_f,null)},watch_f:function(clazz,className,method,methodName,isProptype){if(!window.watchCount){window.watchBox=EG.doc.createElement("div");var ss="position:absolute;z-index:9999;left:0px;top:0px;border:1px solid red;font-size:12px;max-height:500px;overflow:auto;background-color:white".split(";");for(var i=0,il=ss.length;i<il;i++){var sss=ss[i].split(":");watchBox.style[sss[0]]=sss[1]}EG.getBody().appendChild(watchBox);window.watchCount={};window.watchBoxs={}}var key=className+(isProptype?"#":"##")+methodName;if(!watchCount[key]){watchCount[key]=0;watchBoxs[key]=EG.doc.createElement("div");watchBox.appendChild(watchBoxs[key])}watchCount[key]++;watchBoxs[key].innerHTML=(key+":"+watchCount[key])}}});EG.toJSON=ME.toJSON})();(function(){EG.define("EG.Tpl",{constructor:function(content){this.setContent(content)},setContent:function(content){this.contentSrc=content;this.content=EG.String.replaceAll(this.contentSrc,"\\{([^}]+)\\}","'+EG.Tpl.fill(data,'$1')+'")},match:function(data){var s;eval("s='"+this.content+"'");return s},statics:{fill:function(data,key){return data[key]}}})})();(function(){EG.define("EG.Validate",{statics:{common_regex:{email:"([a-zA-Z0-9_\\.\\-])+\\@(([a-zA-Z0-9])+\\.)+([a-zA-Z0-9]{2,10})+",phone:"(1\\d{10})",qq:"[1-9]\\d*",tel:"(([0+]\\d{2,3}-)?(0\\d{2,3})-)(\\d{7,8})(-(\\d{3,}))?",idcardNo:"\\d{15}(\\d{3})?",password:"[0-9a-zA-Z]*",wordnum:"[0-9a-zA-Z_]*"},common_comment:{email:"xxx@域名",phone:"1开头的11位数字",qq:"非0开头的纯数字",tel:"国家(非必填)-区域(非必填)-电话-分机(非必填)",idcardNo:"15或18位数字",password:"字母(含大小写)和数字组合形式",wordnum:"字母(含大小写)和数字组合形式"},getTelRegex:function(b){if(b==null){b="-?"}return"(([0+]\\d{2,3}"+b+")?(0\\d{2,3})"+b+")(\\d{7,8})("+b+"(\\d{3,}))?"},isWordnum:function(b){return EG.RegExp.match(b,EG.Validate.common_regex.wordnum)?"":a.getComment("wordnum")},isEmail:function(b){return EG.RegExp.match(b,EG.Validate.common_regex.email)?"":a.getComment("email")},isPassword:function(b){return EG.RegExp.match(b,EG.Validate.common_regex.password)?"":a.getComment("password")},isPhone:function(b){return EG.RegExp.match(b,EG.Validate.common_regex.phone)?"":a.getComment("phone")},isQq:function(b){return EG.RegExp.match(b,EG.Validate.common_regex.qq)?"":a.getComment("qq")},isTel:function(c,b){return EG.RegExp.match(c,EG.Validate.getTelRegex(b))?"":a.getComment("tel")},isIdcardNo:function(c){var b=c.length;return EG.RegExp.match(c,EG.Validate.common_regex.idcardNo)?"":a.getComment("idcardNo")},$is:function(c,g){var b="is"+EG.Word.first2Uppercase(c);var e=EG.Validate[b];if(!e){throw new Error("EG.Validate:不支持函数"+b)}return e(g)},getComment:function(b){return EG.Validate.common_comment[b]}}});var a=EG.Validate})();(function(){EG.define("EG.Word",{statics:{first2Uppercase:function(a){return a.substring(0,1).toUpperCase()+a.substring(1)},first2LowerCase:function(a){return a.substring(0,1).toLowerCase()+a.substring(1)},numtochinese:function(a){if(typeof(a)=="Number"){a=a+""}for(i=a.length-1;i>=0;i--){a=a.replace(",","");a=a.replace(" ","")}a=a.replace("￥","");if(isNaN(a)){alert("请检查小写金额是否正确");return}part=String(a).split(".");newchar="";for(i=part[0].length-1;i>=0;i--){if(part[0].length>10){alert("位数过大，无法计算");return""}tmpnewchar="";perchar=part[0].charAt(i);switch(perchar){case"0":tmpnewchar="零"+tmpnewchar;break;case"1":tmpnewchar="壹"+tmpnewchar;break;case"2":tmpnewchar="贰"+tmpnewchar;break;case"3":tmpnewchar="叁"+tmpnewchar;break;case"4":tmpnewchar="肆"+tmpnewchar;break;case"5":tmpnewchar="伍"+tmpnewchar;break;case"6":tmpnewchar="陆"+tmpnewchar;break;case"7":tmpnewchar="柒"+tmpnewchar;break;case"8":tmpnewchar="捌"+tmpnewchar;break;case"9":tmpnewchar="玖"+tmpnewchar;break}switch(part[0].length-i-1){case 0:tmpnewchar=tmpnewchar+"元";break;case 1:if(perchar!=0){tmpnewchar=tmpnewchar+"拾"}break;case 2:if(perchar!=0){tmpnewchar=tmpnewchar+"佰"}break;case 3:if(perchar!=0){tmpnewchar=tmpnewchar+"仟"}break;case 4:tmpnewchar=tmpnewchar+"万";break;case 5:if(perchar!=0){tmpnewchar=tmpnewchar+"拾"}break;case 6:if(perchar!=0){tmpnewchar=tmpnewchar+"佰"}break;case 7:if(perchar!=0){tmpnewchar=tmpnewchar+"仟"}break;case 8:tmpnewchar=tmpnewchar+"亿";break;case 9:tmpnewchar=tmpnewchar+"拾";break}newchar=tmpnewchar+newchar}if(a.indexOf(".")!=-1){if(part[1].length>2){alert("小数点之后只能保留两位,系统将自动截段");part[1]=part[1].substr(0,2)}for(i=0;i<part[1].length;i++){tmpnewchar="";perchar=part[1].charAt(i);switch(perchar){case"0":tmpnewchar="零"+tmpnewchar;break;case"1":tmpnewchar="壹"+tmpnewchar;break;case"2":tmpnewchar="贰"+tmpnewchar;break;case"3":tmpnewchar="叁"+tmpnewchar;break;case"4":tmpnewchar="肆"+tmpnewchar;break;case"5":tmpnewchar="伍"+tmpnewchar;break;case"6":tmpnewchar="陆"+tmpnewchar;break;case"7":tmpnewchar="柒"+tmpnewchar;break;case"8":tmpnewchar="捌"+tmpnewchar;break;case"9":tmpnewchar="玖"+tmpnewchar;break}if(i==0){tmpnewchar=tmpnewchar+"角"}if(i==1){tmpnewchar=tmpnewchar+"分"}newchar=newchar+tmpnewchar}}while(newchar.search("零零")!=-1){newchar=newchar.replace("零零","零")}newchar=newchar.replace("零亿","亿");newchar=newchar.replace("亿万","亿");newchar=newchar.replace("零万","万");newchar=newchar.replace("零元","元");newchar=newchar.replace("零角","");newchar=newchar.replace("零分","");if(newchar.charAt(newchar.length-1)=="元"||newchar.charAt(newchar.length-1)=="角"){newchar=newchar+"整"}return newchar},intToChinese:function(f){f=f+"";var a=f.length-1;var c=["","十","百","千","万","十","百","千","亿","十","百","千","万","十","百","千","亿"];var b=["零","壹","贰","叁","肆","伍","陆","柒","捌","玖"];var e=f.replace(/([1-9]|0+)/g,function(l,h,g,k){var n=0;if(h[0]!="0"){n=a-g;if(g==0&&h[0]==1&&c[a-g]=="十"){return c[a-g]}return b[h[0]]+c[a-g]}else{var m=a-g;var j=a-g+h.length;if(Math.floor(j/4)-Math.floor(m/4)>0){n=m-m%4}if(n){return c[n]+b[h[0]]}else{if(g+h.length>=a){return""}else{return b[h[0]]}}}});e=EG.String.replaceAll(e,"\\.","点");return e}}})})();(function(){EG.define("EG.Promise",[],function(a){return{constructor:function(b){this.resolvesQueue=[];this.rejectQueue=[];this.status="pendding";this.promiseMain=b;return this},handle:function(b,f){var e=this;this.status=b;var c=null;if(this.status=="fullfilled"){c=this.resolvesQueue.shift()}else{if(this.status=="rejected"){c=this.rejectQueue.shift()}}setTimeout(function(){c.apply(e,[e,f])},0)},then:function(e,b){var c=this;this.resolvesQueue.push(e);this.rejectQueue.push(b);if(this.status=="pendding"){this.status=="fullfilled";if(!this.t){this.t=setTimeout(function(){c.promiseMain.apply(c,[c])},0)}}return this},resolve:function(b){this.handle("fullfilled",b)},reject:function(b){this.handle("rejected",b)}}})})();(function(){var ME=EG.define("EG.Event",{statics:{fireEvent:function(ele,action){action=EG.String.removeStart(action,"on");if(document.createEvent){var e=document.createEvent("HTMLEvents");e.initEvent(action,false,false);ele.dispatchEvent(e)}else{if(ele.fireEvent){ele.fireEvent("on"+action)}else{eval("ele."+action+"();")}}},_eventFns:{},_getEventFN:function(action){var eventKey="events_"+action;if(!ME._eventFns[eventKey]){ME._eventFns[eventKey]=function(e){e=ME.getEvent(e);var rs=null;var target=this;if(target[eventKey]==null){target=e.target}if(!target[eventKey]){return rs}for(var i=0,il=target[eventKey].length;i<il;i++){var rs=target[eventKey][i].apply(target["on"+action+"Src"]||target,[e]);if(rs!=null){return rs}}return rs}}return ME._eventFns[eventKey]},bindEvent:function(ele,action,handler,cap){action=EG.String.removeStart(action,"on");var eventKey="events_"+action;if(!ele[eventKey]){ele[eventKey]=[];ele[action+"_fn"]=ME._getEventFN(action);var useAttach=false;if(EG.Browser.isIE8()||EG.Browser.isIE8Before()){useAttach=true}if(useAttach){ele.attachEvent("on"+action,function(e){ele[action+"_fn"].apply(ele,[e])})}else{if(EG.Browser.isIE()){ele["on"+action]=ele[action+"_fn"]}else{if(ele.addEventListener){ele.addEventListener(action,ele[action+"_fn"],cap)}}}}ele[eventKey].push(handler)},hasEventHandle:function(ele,action,fn){return ele[action+"_fn"]&&EG.Array.has(ele[action+"_fn"],fn)},_fn:{false_return:function(){return false}},bindUnselect:function(ele){EG.css(ele,EG.Style.c.selectnone);if(EG.Browser.isIE()){ME.bindEvent(ele,"onselectstart",ME._fn.false_return)}},removeEvent:function(ele,action,handler){action=EG.String.removeStart(action,"on");var eventKey="events_"+action;if(!ele[eventKey]){return}EG.Array.remove(ele[eventKey],handler)},onload:function(fn){ME.bindEvent(window,"load",fn)},getEvent:function(e){return e||window.event},getTarget:function(e){return e?e.target:window.event.srcElement},stopPropagation:function(e){e=ME.getEvent(e);if(e&&e.stopPropagation){e.stopPropagation()}else{window.event.cancelBubble=true}if(e.preventDefault){e.preventDefault()}else{e.returnValue=false}},keycodes:{esc:27}}});EG.bindEvent=ME.bindEvent;EG.removeEvent=ME.removeEvent;EG.onload=ME.onload})();(function(){var a=false;if(a){if(!EG.Browser.isIE()&&typeof(HTMLElement)!="undefined"&&!window.opera){HTMLElement.prototype.__defineGetter__("outerHTML",function(){var c=this.attributes,f="<"+this.tagName;for(var e=0;e<c.length;e++){if(c[e].specified){f+="   "+c[e].name+'="'+c[e].value+'"'}}if(!this.canHaveChildren){return f+"   />"}return f+">"+this.innerHTML+"</"+this.tagName+">"});HTMLElement.prototype.__defineSetter__("outerHTML",function(e){var f=EG.doc.createElement("DIV");f.innerHTML=e;for(var c=0;c<f.childNodes.length;c++){this.parentNode.insertBefore(f.childNodes[c],this)}this.parentNode.removeChild(this)});HTMLElement.prototype.__defineGetter__("canHaveChildren",function(){return !/^(area|base|basefont|col|frame|hr|img|br|input|isindex|link|meta|param)$/.test(this.tagName.toLowerCase())})}}var b=EG.define("EG.DOM",{statics:{addChildren:function(j,l,e){var h=(!EG.isArray(l))?[l]:l;for(var g=0,f=h.length;g<f;g++){var k=h[g];if(EG.isLit(k)){k=EG.CE(k)}else{if(k.getElement){k=k.getElement()}}b.insertChild(j,k,e);e++}},insertAfter:function(e,f){var c=f.parentNode;if(c.lastChild==f){c.appendChild(e)}else{c.insertBefore(e,b.nextNode(f))}},insertBefore:function(e,f){var c=f.parentNode;c.insertBefore(e,f)},insertChild:function(c,e,f){if(!f){return c.appendChild(e)}if(typeof(f)=="number"){f=b.childNodes(c)[f]}if(f){c.insertBefore(e,f)}else{c.appendChild(e)}},getOuterHTML:function(c){if(c.parentNode){throw new Error("EG.DOM#getOuterHTML:不支持有父节点的节点")}return EG.CE({tn:"div",cn:[c]}).innerHTML},$:function(f,m){if(!m){m=EG.doc}if(b.isElement(f)){return f}if("string"==typeof(f)){var k=m.getElementById(f);if(k!=null){return k}else{if((k=m.getElementsByName(f))!=null&&k.length>0){return k}else{return null}}}else{if("object"==typeof(f)){var j=null,c=[];if(f.name){var p=f.ele||m;j=p.getElementsByName(f.name)}else{if(f.tn){var p=f.ele||m;j=p.getElementsByTagName(f.tn)}else{throw new Error("EG.DOM#$:name和tn不能同时为空")}}var n=f.idx;delete f.name;delete f.tn;delete f.ele;delete f.idx;for(var g=0,l=j.length;g<l;g++){var h=true;for(var o in f){if(EG.$in("name","tn","ele","idx")){continue}if(f[o]!=j[g][o]){h=false;break}}if(h){c.push(j[g])}}if(n!=null){return EG.Array.get(c,n)}return c}}},CEKeywords:["ele","element","tn","tagName","pn","parentNode","cn","childNodes","style"],CENicknames:{cls:"className"},NS_SVG:"http://www.w3.org/2000/svg",CSVG:function(c){return b.CE(c,{svg:true})},CVML:function(c){return b.CE(c,{vml:true})},CE:function(g,f){var l=f?f.svg:false;var t=f?f.vml:false;var c=f?f.setAtr:false;if(typeof(g)=="string"){return b.childNodes(EG.CE({tn:"div",innerHTML:g}))}var u=EG.unnull(g.ele,g.element),s=EG.unnull(g.tn,g.tagName),h=EG.unnull(g.pn,g.parentNode),q=EG.unnull(g.cn,g.childNodes),e=g.style;if(!u&&!s){throw new Error("EG.DOM#CE:标签名和ele不能同时为空")}if(s){if(s.toUpperCase()=="IFRAME"&&g.name&&EG.Browser.isIE6()){u=EG.doc.createElement("<iframe name='"+g.name+"'></iframe>")}else{if(l){u=EG.doc.createElementNS(b.NS_SVG,s)}else{if(t){u=EG.doc.createElement(s)}else{u=EG.doc.createElement(s)}}if(t){u.xmlns="urn:schemas-microsoft-com:vml";g.cls="vml"}}}for(var r in g){if(EG.$in(r,b.CEKeywords)){continue}var o=g[r];if(r=="name"){u.setAttribute("name",o);u.name=o}else{if(r.indexOf("$")>0){var n=r.indexOf("$"),j=r.substring(0,n),k=r.substring(n+1,r.length);if(!u[j]){u[j]={}}u[j][k]=o}else{if(r.indexOf("on")==0&&!EG.String.endWith(r,"Src")){if(o!=null){EG.bindEvent(u,r.substr(2).toLowerCase(),o)}}else{if(b.CENicknames[r]){r=b.CENicknames[r]}if(l){if(r=="innerText"){b.removeChilds(u);u.appendChild(document.createTextNode(o))}else{if(r=="DATA"){u.DATA=o}else{if(r=="className"){r="class"}u.setAttributeNS(null,r,o)}}}else{if(c){u.setAttribute(r,o)}u[r]=o}}}}}if(e){EG.css(u,e)}if(h){if(h.isItem){h.getElement().appendChild(u)}else{h.appendChild(u)}}if(q){if(typeof(q)=="string"){q=b.CE(q)}for(var m=0,p=q.length;m<p;m++){if(EG.isLit(q[m])){q[m]=EG.CE(q[m],f)}else{if(q[m].isItem){q[m]=q[m].getElement()}}u.appendChild(q[m])}}return u},isElement:function(c){return c&&c.nodeType==1},isNodeList:function(c){return c!=null&&c.nodeType==null&&c.length!=null&&typeof(c.length)=="number"&&c.item!=null},isTag:function(e,c){return e.tagName.toUpperCase()==c.toUpperCase()},has:function(f,c){var e=c.parentNode;while(e!=null){if(e==f){return true}e=e.parentNode}return false},remove:function(e){if(!EG.Array.isArray(e)){e=[e]}for(var c=0;c<e.length;c++){e[c].parentNode.removeChild(e[c])}},removeChilds:function(c){while(c.childNodes&&c.childNodes.length>0){c.removeChild(c.childNodes[0])}return c},preNode:function(c){return b._nextNode(c,-1)},nextNode:function(c){return b._nextNode(c,1)},_nextNode:function(g,h){if(!g||!g.parentNode){return null}var f=b.childNodes(g.parentNode);for(var e=0,c=f.length;e<c;e++){if(f[e]==g){return(e+h<c)?f[e+h]:null}}},childNode:function(h,j){if(typeof(j)=="string"){var g=j.split("_");for(var e=0,c=g.length;e<c;e++){if(g[e]!="n"){g[e]=parseInt(g[e])}h=EG.Array.get(b.childNodes(h),g[e])}return h}else{if(typeof(j)=="number"){var f=b.childNodes(h);return EG.Array.get(f,j)}}throw new Error("EG.DOM#childNode不支持索引类型:"+typeof(j))},childNodes:function(h){var f=[],g=h.childNodes;for(var e=0,c=g.length;e<c;e++){if(g[e].nodeType==1){f.push(g[e])}}return f},getValue:function(q,e){e=e||{};if(typeof(q)=="string"){q=b.$(q)}if(q==null){throw new Error("EG.DOM#getValue:未找到匹配元素")}var h=b.isNodeList(q);var n=(h?q[0]:q).tagName.toUpperCase();if(n=="INPUT"){var k=(h?q[0]:q).type.toUpperCase();if(k=="TEXT"||k=="PASSWORD"||k=="HIDDEN"||k=="FILE"){if(h){throw new Error("EG.DOM#getValue:暂不支持数组Element INPUT "+k)}return q.value}else{if(k=="RADIO"){if(h){for(var g=0,l=q.length;g<l;g++){if(q[g].checked==true){return q[g].value}}}else{return(q.checked==true)?q.value:null}}else{if(k=="CHECKBOX"){if(h){var o=[];for(var g=0,l=q.length;g<l;g++){if(q[g].checked==true){o.push(q[g].value)}}return o}else{return(q.checked==true)?q.value:null}}else{throw new Error("EG.DOM#getValue:不支持input "+k+"类型")}}}}else{if(n=="SELECT"){if(h){throw new Error("EG.DOM#getValue:暂不支持数组Element SELECT")}var f=e.getText!=null?e.getText:false;var j=e.ignoreEmpty!=null?e.ignoreEmpty:false;var c=q.options;var o=[];var p=q.multiple;for(var g=0,l=c.length;g<l;g++){if(c[g].selected==true){var m=null;if(j&&c[g].value==""){m=null}else{m=(f)?c[g].text:c[g].value}if(p){o.push(m)}else{return m}}}if(p){return o}else{return null}}else{if(n=="TEXTAREA"){if(h){var o=[];for(var g=0,l=q.length;g<l;g++){o.push(q.value)}return o}else{return q.value}}else{if(q.innerHTML!=null){if(h){var o=[];for(var g=0,l=q.length;g<l;g++){o.push(q.innerHTML)}return o}else{return q.innerHTML}}else{throw new Error(" EG.DOM#getValue:不支持其它类型值")}}}}return null},getValues:function(j){var m={};var k=b.$(j);var l=j.getElementsByTagName("INPUT");l.concat(j.getElementsByTagName("SELECT"));l.concat(j.getElementsByTagName("TEXTAREA"));for(var h=0,f=l.length;h<f;h++){if(k[h].id==""&&k[h].name==""){continue}var g=k[h].id!=""?k[h].id:k[h].name;m[g]=b.getValue(g)}return m},setValue:function(p,m,e){e=e||{};var h=e.fireOnchange!=null?e.fireOnchange:true;if(typeof(p)=="string"){p=EG.$(p)}if(p==null){throw new Error("EG.DOM#setValue未找到匹配元素")}var j=b.isNodeList(p);var n=(j?p[0]:p).tagName.toUpperCase();if(m==null){m=""}if(n=="INPUT"){var k=(j?p[0]:p).type.toUpperCase();if(k=="TEXT"||k=="PASSWORD"||k=="HIDDEN"){p.value=m}else{if(k=="RADIO"){if(j){for(var g=0,l=p.length;g<l;g++){if(p[g].value==m){p[g].checked=true;break}}}else{if(p.value==m){p.checked=true}}}else{if(k=="CHECKBOX"){var f=e.spliter||",";if(m instanceof Array){if(!j){throw new Error("EG.DOM#setValue:值为数组时,Element类型必须为数组")}for(var g=0,l=p.length;g<l;g++){if(EG.Array.has(m,p[g].value)){p[g].checked=true;if(h){EG.Event.fireEvent(p[g],"onchange")}}}}else{if(j){for(var g=0,l=p.length;g<l;g++){if(m==p[g].value){p[g].checked=true;if(h){EG.Event.fireEvent(p[g],"onchange")}}}}else{if(p.value==m){p.checked=true;if(h){EG.Event.fireEvent(p,"onchange")}}}}}else{throw new Error("Engin#setValue:不支持该input "+k+"类型")}}}}else{if(n=="SELECT"){var o=e.cmpText!=null?e.cmpText:false;var c=p.options;for(var g=0,l=c.length;g<l;g++){if((o&&c[g].text==m)||(!o&&c[g].value==m)){c[g].selected=true;if(h){EG.Event.fireEvent(p,"onchange")}return}}}else{if(n=="TEXTAREA"){p.value=m}else{if(p.innerHTML!=null){p.innerHTML=m}else{throw new Error(" EG.DOM#setValue:不支持对其它类型设值")}}}}},setValues:function(e){if(e!=null){for(var c in e){b.setValue(c,e[c])}}},removeOptions:function(c){c=EG.$(c);if(!b.isTag(c,"SELECT")){throw new Error("EG.DOM#removeOptions:待删除的对象非Select组件")}b.removeChilds(c)},addOptions:function(l,g,f,m,j,e){l=EG.$(l);m=m||1;f=f||0;for(var h=0,k=g.length;h<k;h++){var c=EG.CE({tn:"option",value:(typeof(m)=="function")?m(g[h]):g[h][m],innerHTML:(typeof(f)=="function")?f(g[h]):g[h][f]});if(j&&e){c[j]=(typeof(e)=="function")?e(g[h]):g[h][e]}l.options[l.options.length]=c}},getOption:function(h,j){var g=h.options;for(var f=0,c=g.length;f<c;f++){var e=g[f];if(e.value==j){return e}}},getSelectedOption:function(c){return c.options[c.options.selectedIndex]},removeOption:function(l,m){var f=null,c;if(typeof(m)=="string"){m={value:m}}if((c=m.idx)!=null){f="idx"}else{if((c=m.value)!=null){f="value"}else{if((c=m.text)!=null){f="text"}else{throw new Error("EG.DOM#removeOption:参数不正确")}}}var k=l.options;if(EG.isArray(c)){for(var h=k.length-1;h>=0;h--){for(var e=0,g=c.length;e<g;e++){if((f=="idx"&&h==c[e])||(f=="value"&&c[e]==k[h].value)||(f=="text"&&c[e]==k[h].text)){l.removeChild(k[h]);break}}}}else{for(var h=k.length-1;h>=0;h--){if((f=="idx"&&h==c)||(f=="value"&&c==k[h].value)||(f=="text"&&c==k[h].text)){l.removeChild(k[h])}}}},getTextvalues:function(j,g){g=g||{};var c=g.egnoreEmpty!=null?g.egnoreEmpty:true;var k=[];var h=j.options;for(var f=0,e=h.length;f<e;f++){if(c&&h[f].value==""){continue}k.push([h[f].text,h[f].value])}return k},selectBoxes:function(j,g,h){if(!g){g=true}var f=EG.doc.getElementsByName(j);for(var e=0,c=f.length;e<c;e++){if(h!=null){f[e].checked=(f[e].value=h)?g:!g}else{f[e].checked=g}}},removeAllRows:function(c){c=EG.$(c);if(c==null){return}b.removeChilds(c)},getActionFrame:function(){if(!b.actionFrame){b.actionFrame=EG.CE({pn:EG.getBody(),tn:"iframe",id:"actionFrame",name:"actionFrame",style:"display:none"})}return b.actionFrame},getIdx:function(f){var e=b.childNodes(f.parentNode);for(var c=0;c<e.length;c++){if(e[c]==f){return c}}throw new Error("未找到索引")},contains:document.contains?function(c){return document.contains(c)}:function(c){return document.documentElement.contains(c)}}});EG.CSVG=b.CSVG;EG.CVML=b.CVML;EG.$=b.$;EG.CE=b.CE;EG.getValue=b.getValue;EG.setValue=b.setValue}());(function(){EG.define("EG.Style",[],function(ME){var getComputedStyle=window.getComputedStyle||(EG.doc&&EG.doc.defaultView&&EG.doc.defaultView.getComputedStyle);return{statics:{c:{dv:"display:inline-block;vertical-align:middle;",selectnone:"-moz-user-select:none;-webkit-user-select:none;user-select:none;",selectauto:"-moz-user-select:auto;-webkit-user-select:auto;user-select:auto;"},debugSize:function(ele){if(ele.getElement){ele=ele.getElement()}alert(EG.toJSON(EG.getSize(ele)))},parseTransform:function(transform){var m={};var regex=new RegExp("([a-zA-Z0-9]+)\\(([^)]*)\\)","ig");if(!transform){return m}var as=transform.match(regex);if(!as){return m}for(var i=0;i<as.length;i++){var a=as[i];var k=a.replace(regex,"$1");var vs=null;var ss=a.replace(regex,"$2");if(EG.Browser.isIE()){ss=ss.replace(new RegExp(" ","ig"),",")}eval("vs=["+ss+"]");m[k]=vs}return m},size2Num:function(size,refferNum){if(typeof(size)=="string"){if(EG.String.endWith(size,"%")){if(refferNum==null){Error("EG.Style#size2Num:百分比时参考尺寸不能为空.")}return parseInt(refferNum*parseInt(size.substr(0,size.length-1))/100)}else{if(EG.String.endWith(size,"px")){return parseInt(size.substr(0,size.length-2))}else{if(EG.String.endWith(size,"vh")){return parseInt(size.substr(0,size.length-2))*Style.vh}else{if(EG.String.endWith(size,"vw")){return parseInt(size.substr(0,size.length-2))*Style.vw}else{if(size=="thin"){return 1}else{if(size=="medium"){return 3}else{if(size=="thick"){return 5}else{if(size=="auto"){return 0}else{if(size==""){return 0}else{throw new Error("EG.Style#size2Num:暂不支持数值转换."+size)}}}}}}}}}}else{if(typeof(size)=="number"){return size}else{throw new Error("EG.Style#size2Num:参数类型无法识别."+typeof(size))}}},getSize:function(ele,css){if(ele.getElement){ele=ele.getElement()}var cs=Style.current(ele);var size={offsetWidth:ele.offsetWidth,offsetHeight:ele.offsetHeight,clientWidth:css?cs.width:ele.clientWidth,clientHeight:css?cs.height:ele.clientHeight,borderLeft:(cs.borderLeftStyle!="none")?(Style.size2Num(cs.borderLeftWidth)):0,borderTop:(cs.borderTopStyle!="none")?(Style.size2Num(cs.borderTopWidth)):0,borderRight:(cs.borderRightStyle!="none")?(Style.size2Num(cs.borderRightWidth)):0,borderBottom:(cs.borderBottomStyle!="none")?(Style.size2Num(cs.borderBottomWidth)):0,paddingLeft:(Style.size2Num(cs.paddingLeft)),paddingTop:(Style.size2Num(cs.paddingTop)),paddingRight:(Style.size2Num(cs.paddingRight)),paddingBottom:(Style.size2Num(cs.paddingBottom))};size.marginLeft=(Style.size2Num(cs.marginLeft));size.marginTop=(Style.size2Num(cs.marginTop));size.marginRight=(Style.size2Num(cs.marginRight));size.marginBottom=(Style.size2Num(cs.marginBottom));size.vScrollWidth=size.offsetWidth-size.clientWidth-size.borderLeft-size.borderRight;size.hScrollWidth=size.offsetHeight-size.clientHeight-size.borderTop-size.borderBottom;size.innerWidth=size.clientWidth-size.paddingLeft-size.paddingRight;size.innerHeight=size.clientHeight-size.paddingTop-size.paddingBottom;size.outerWidth=size.offsetWidth+size.marginLeft+size.marginRight;size.outerHeight=size.offsetHeight+size.marginTop+size.marginBottom;return size},getInnerTop:function(s){return Style.getInnerOut(s,"Top")},getInnerBottom:function(s){return Style.getInnerOut(s,"Bottom")},getInnerLeft:function(s){return Style.getInnerOut(s,"Left")},getInnerRight:function(s){return Style.getInnerOut(s,"Right")},getInnerOut:function(s,type){return s["margin"+type]+s["padding"+type]+s["border"+type]},num2Size:function(num){if(typeof(num)=="number"){return num+"px"}else{if(typeof(num)=="string"){return num}else{throw new Error("EG.Style#num2Size:参数类型无法识别."+typeof(num))}}},create:function(styles){var ss="";for(var key in styles){ss+=(key+"{"+styles[key]+"}\n")}if(Style.element.styleSheet){Style.element.styleSheet.cssText+=ss}else{Style.element.appendChild(EG.doc.createTextNode(ss))}},createSheet:function(url){var css=EG.CE({tn:"link",rel:"stylesheet",rev:"stylesheet",type:"text/css",media:"screen",href:url});EG.doc.getElementsByTagName("head")[0].appendChild(css);return css},current:function(ele){if(getComputedStyle){return getComputedStyle(ele)}else{return ele.currentStyle}},parse:function(style){var m={};var ses=style.split(";");for(var i=0;i<ses.length;i++){var sp=ses[i];if(sp==""){continue}var sess=sp.split(":");m[sess[0]]=sess[1]}return m},css:function(ele,style){if(ele.getElement){ele=ele.getElement()}if(!ele.cacheStyle){ele.cacheStyle={}}var m={};if(typeof(style)=="string"){m=ME.parse(style)}else{if(typeof(style)=="object"){m=style}else{throw new Error("EG.Style#set:style类型错误")}}var clearSize=false;for(var k in m){var v=m[k];if(k=="float"){k=("cssFloat" in ele.style)?"cssFloat":"styleFloat"}else{if(k=="vertical-align"){k="verticalAlign"}}if(EG.Browser.isIE8()){var ks=k.split("-");if(ks.length>1){k=ks[0]+ks[1].substr(0,1).toUpperCase()+ks[1].substr(1)}}ele.style[k]=v;if(ele.cacheStyle[k]!==v){ele.cacheStyle[k]=v;if(k=="width"||k=="height"||k.indexOf("margin")==0||k.indexOf("padding"==0)){clearSize=true}}}if(clearSize){ele.egSize=null}},isHide:function(ele,inherit){if(!inherit){var cs=Style.current(ele);return cs.display=="none"}else{var p=ele;while(p){if(p==EG.getBody()){return false}var cs=Style.current(p);if(cs.display=="none"||cs.visible=="none"){return true}p=p.parentNode}return true}},show:function(){Style.displays(arguments,true)},hide:function(){Style.displays(arguments,false)},visibles:function(eles,visible){for(var i=0,il=eles.length;i<il;i++){Style.visible(eles[i],visible)}},visible:function(ele,visible){ele.style.visibility=visible?"visible":"hidden"},displays:function(eles,display){for(var i=0,il=eles.length;i<il;i++){Style.display(eles[i],display)}},display:function(ele,display){if(typeof(ele)=="string"){ele=EG.$(ele)}if(ele.getElement){ele=ele.getElement()}if(typeof(display)=="boolean"){var cs=Style.current(ele);var cd=ele.style.display||(cs?cs.display:"");if(!display){if(cd!="none"){if(EG.DOM.contains(ele)){ele.oDisplay=cd}ele.style.display="none"}}else{if(cd=="none"){ele.style.display=ele.oDisplay||""}}}else{if(typeof(display)=="string"){ele.style.display=display}else{throw new Error("EG.Style#display:不支持"+display)}}},centerChilds:function(pn,horizontal){var cns=pn.childNodes;if(cns.length>0){if(horizontal){var mw=0;for(var i=0;i<cns.length;i++){var cn=cns[i];var s=EG.getSize(cn);if(i==0){mw+=s.outerWidth-s.marginLeft-s.marginRight}else{var sl=EG.getSize(cns[i-1]);var lm=Math.max(sl.marginRight,s.marginLeft);mw+=lm+s.outerWidth-s.marginRight-s.marginLeft}}var m=parseInt((EG.getSize(pn).innerWidth-mw)/2);EG.css(cns[0],"margin-left:"+m+"px");EG.css(cns[cns.length-1],"margin-right:"+m+"px")}else{alert("暂时不支持")}}},middleChilds:function(pn,horizontal){var cns=pn.childNodes;var ps=EG.getSize(pn);if(cns.length>0){if(horizontal){for(var i=0;i<cns.length;i++){var cn=cns[i];var s=EG.getSize(cn);var m=parseInt((ps.innerHeight-(s.outerHeight-s.marginTop-s.marginBottom))/2);EG.css(cn,"margin-top:"+m+"px;margin-bottom:"+m+"px")}}else{alert("暂不支持")}}},topChilds:function(pn,horizontal){var cns=pn.childNodes;var ps=EG.getSize(pn);if(cns.length>0){if(horizontal){for(var i=0;i<cns.length;i++){var cn=cns[i];var s=EG.getSize(cn);var m=parseInt((ps.innerHeight-(s.outerHeight-s.marginTop-s.marginBottom)));EG.css(cn,"margin-top:"+0+"px;margin-bottom:"+m+"px")}}else{alert("暂不支持")}}},bottomChilds:function(pn,horizontal){var cns=pn.childNodes;var ps=EG.getSize(pn);if(cns.length>0){if(horizontal){for(var i=0;i<cns.length;i++){var cn=cns[i];var s=EG.getSize(cn);var m=parseInt((ps.innerHeight-(s.outerHeight-s.marginTop-s.marginBottom)));EG.css(cn,"margin-top:"+m+"px;margin-bottom:"+0+"px")}}else{alert("暂不支持")}}},center:function(ele,ct){var l=Math.abs(parseInt((ct||ele.parentNode).clientWidth-EG.getSize(ele).outerWidth)/2);ele.style.position="absolute";ele.style.left=l+"px"},middle:function(ele,ct){var l=Math.abs(parseInt((ct||ele.parentNode).clientHeight-EG.getSize(ele).outerHeight)/2);ele.style.position="absolute";ele.style.top=l+"px"},left:function(ele,ct){ele.style.position="absolute";ele.style.left="0px"},right:function(ele,ct){ele.style.position="absolute";ele.style.right="0px"},top:function(ele,ct){ele.style.position="absolute";ele.style.top="0px"},bottom:function(ele,ct){ele.style.position="absolute";ele.style.bottom="0px"},full:function(ele,ct){if(!ct){ct=ele.parentNode}if(!ct){throw new Error("EG.Style#full:父元素不能为空")}ele.style.width=ct.clientWidth+"px";if(ct==EG.getBody()){ele.style.height=Math.max((EG.doc.documentElement?EG.doc.documentElement.clientHeight:EG.getBody().clientHeight),EG.getBody().clientHeight)+"px"}else{ele.style.height=ct.clientHeight+"px"}},moveTo:function(ele,pos){if(pos.x){Style.css(ele,"left:"+pos.x+"px")}if(pos.y){Style.css(ele,"top:"+pos.y+"px")}},capcity:function(ele,val){if(EG.doc.all){ele.style.filter="alpha(opacity="+parseInt(val)+")"}else{ele.style.opacity=parseInt(val)/100}},fade:function(ele,start,end,callback,speed){if(!speed){speed=20}var _a=(start>end)?-5:5;Style.capcity(ele,start+_a);if(start==end){if(!callback){return}return callback()}window.setTimeout(function(){Style.fade(ele,start+_a,end,callback,speed)},speed);return null},setCls:function(ele,cls,clsPre){if(cls==null){return}clsPre=clsPre?(clsPre+"-"):"";if(!EG.isArray(cls)){cls=[cls]}if(cls.length==0){return}var s="";for(var i=0;i<cls.length;i++){if(i!=0){s+=" "}s+=clsPre+cls[i]}ele.className=s},addCls:function(ele,cls){var cn=ele.className||"";var clss=cn.split(" ");if(EG.Array.has(clss,cls)){return}for(var i=clss.length;i>=0;i--){if(clss[i]==""){EG.Array.del(clss,i)}}clss.push(cls);ele.className=clss.join(" ")},removeCls:function(ele,cls){var cn=ele.className||"";var clss=cn.split(" ");if(!EG.Array.has(clss,cls)){return}EG.Array.remove(clss,cls);ele.className=clss.join(" ")}}}});var Style=EG.Style;Style.vh=window.screen.height/100;Style.vw=window.screen.width/100;EG.getSize=Style.getSize;EG.debugSize=Style.debugSize;EG.css=Style.css;EG.hide=Style.hide;EG.show=Style.show;EG.setCls=Style.setCls;EG.onload(function(){Style.element=EG.doc.createElement("style");Style.element.type="text/css";EG.doc.getElementsByTagName("HEAD").item(0).appendChild(Style.element);if(EG.Browser.isIE6()){Style.c.dv+="*display:inline;zoom:1;"}})})();(function(){EG.define("EG.$Q",{constructor:function(a){return new EG.$Q.prototype._init(a)},_init:function(a){this.ele=EG.$(a);return this},val:function(a){if(arguments.length==0){return EG.DOM.getValue(this.ele)}else{return EG.DOM.setValue(this.ele,a)}},show:function(){EG.show(this.ele);return this},hide:function(){EG.hide(this.ele);return this},center:function(a){EG.Style.center(this.ele,a);return this},hasClass:function(a){return this.ele.className.indexOf(a)>=0},removeChilds:function(){return EG.DOM.removeChilds(this.ele)},appendChild:function(a){this.ele.appendChild(a);return this},innerHTML:function(a){this.ele.innerHTML=a;return this},getChild:function(a){return this.ele.childNodes[a]},css:function(a){EG.css(this.ele,a);return this}});(function(){var b=["click","dblclick","mouseover","mouseout"];for(var a=0;a<b.length;a++){EG.$Q.prototype[b[a]]=function(c){var e=this;EG.bindEvent(this.ele,function(){c.apply(e)});return this}}})();EG.$Q.prototype._init.prototype=EG.$Q.prototype})();(function(){EG.define("EG.MMVC",{statics:{mmvcPath:"/mmvc/",getPath:function(uri){if(uri==null){uri=EG.MMVC.mmvcPath}return{call:uri+"jsonrpc/call",connect:uri+"push/connect",moditor:uri+"utils/profile",upload:uri+"upload",download:uri+"download"}},call:function(cfg){cfg=cfg||{};var muti=cfg.muti,rpc=cfg.rpc,method=cfg.method,params=EG.unnull(cfg.params,[]),exHandler=cfg.exHandler,cb=cfg.callback,httpMethod=EG.unnull(cfg.httpMethod,"POST"),mutiReturn=cfg.mutiReturn||"map",useRandom=EG.unnull(cfg.useRandom,true),erhandler=null;if(!muti&&EG.String.isEmpty(rpc)){throw new Error("EG#call:rpc不能为空")}if(!muti&&EG.String.isEmpty(method)){throw new Error("EG#call:method不能为空")}var url=cfg.callPath||EG.MMVC.getPath().call;var strParams="";if(muti){strParams=(mutiReturn!="map"?"mutiReturn="+mutiReturn+"&":"")+"muti="+EG.Ajax.javaURLEncoding(EG.Tools.toJSON(muti))}else{strParams="rpc="+rpc+"&method="+method+"&params="+EG.Ajax.javaURLEncoding(EG.Tools.toJSON(params))}var content=null;if(useRandom){strParams+="&random="+new Date().getTime()+"-"+Math.random()*1000}if(httpMethod=="GET"){url+="?"+strParams}else{content=strParams}var handleEx=function(ex){if(EG.MMVC.exClassHandlers[ex.exClass]){return EG.MMVC.exClassHandlers[ex.exClass](ex)}else{if(exHandler){return exHandler(ex)}else{if(EG.MMVC.defExHandler){return EG.MMVC.defExHandler(ex)}else{throw new Error(ex.exMsg)}}}};var callback=function(resText,req){var obj=null;if(resText!=null&&resText!=""){eval("obj="+resText+";")}if(obj!=null){if(!EG.isArray(obj)){throw new Error("结构非数组:"+resText)}if(obj[0]===0){return cb(obj[1])}else{return handleEx(new EG.MMVC.Exception(obj[1]["exClass"],obj[1]["exMsg"]))}}else{return cb(obj)}};return EG.Ajax.send({url:url,method:httpMethod,content:content,callback:callback,erhandler:erhandler})},exClassHandlers:{},defExHandler:null,connect:function(cfg){cfg=cfg||{};var actionFrame=cfg.actionFrame;if(!actionFrame){actionFrame=EG.DOM.getActionFrame()}actionFrame.src=EG.MMVC.getPath().connect},download:function(cfg){var actionFrame=cfg.actionFrame;var policy=cfg.policy;var params=cfg.params||{};var ps="";for(var key in params){ps+="&"+key+"="+EG.Ajax.javaURLEncoding(params[key])}if(!actionFrame){actionFrame=EG.DOM.getActionFrame()}actionFrame.src=EG.MMVC.getPath().download+"?policy="+policy+ps}}});EG.MMVC.Exception=function(exClass,exMsg){this.exClass=exClass;this.exMsg=exMsg};EG.call=EG.MMVC.call})();(function(){EG.define("EG.Anim",function(){return{statics:{add:function(c,a){if(!EG.Array.isArray(a)){a=[a]}if(!c.anims){c.anims=[]}for(var b=0;b<a.length;b++){if(EG.Array.has(c.anims,a[b])){continue}c.anims.push(a[b])}},remove:function(c,a){if(a==null&&c.anims!=null){a=c.anims}if(!EG.Array.isArray(a)){a=[a]}if(!c.anims){c.anims=[]}for(var b=0;b<a.length;b++){EG.Array.remove(c.anims,a[b])}},stop:function(a){a.fn_stopAnima.apply(a)},beforePlay:function(b){var a=EG.Style.current(b);b.oldOpacity=b.style.opacity||(a?a.opacity:1);EG.css(b,"opacity:0")},play:function(c){if(!c.oldOpacity){EG.Anim.beforePlay(c)}for(var b=0;b<c.anims.length;b++){EG.Style.addCls(c,c.anims[b])}if(!c.fn_stopAnima){c.fn_stopAnima=function(){for(var g=0;g<this.anims.length;g++){EG.Style.removeCls(this,this.anims[g])}};var a={WebkitAnimation:"webkitAnimationEnd",OAnimation:"oAnimationEnd",msAnimation:"MSAnimationEnd",animation:"animationend"};if(!Modernizr){return}var e=a[Modernizr.prefixed("animation")];EG.Event.bindEvent(c,e,c.fn_stopAnima)}var f=c.oldOpacity||1;EG.css(c,"opacity:"+f)},moveout:function(b,c,a){},movein:function(){},move:function(j,b,k,g,e,a,h){if(!e){e=1}if(!a){a=25}var f=e/a;if(h==null){h=0}h+=k/a;if(h>k){h=k}if(h<k){if(!EG.Array.isArray(j)){j=[j]}for(var c=0;c<j.length;c++){EG.css(j[c],"margin-"+b+":"+(-h)+"px")}window.setTimeout(function(){EG.Anim.move(j,b,k,g,e,a,h)},f)}else{if(!g){return}return g()}},rotate:function(f,j,e,a,h,g,c){var b;if(!h){h=25}if(!c){c=new Date().getTime();c+=j}if(new Date().getTime()>=c){alert("OVER");return}b=1000/h;if(g==null){g=0}g+=e;g=g%360;EG.css(f,"-webkit-transform: rotate(-"+g+"deg);-moz-transform: rotate(-"+g+"deg);-ms-transform: rotate(-"+g+"deg);-o-transform: rotate(-"+g+"deg);transform: rotate(-"+g+"deg);");window.setTimeout(function(){EG.Anim.rotate(f,j,e,a,h,g,c)},b)}}}})})();