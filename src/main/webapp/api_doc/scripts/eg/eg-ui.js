(function(){EG.define("EG.UI",{statics:{skin:"default",getSkinPath:function(a){return EG.basePath+"/skins/"+EG.UI.skin+"/"+a},sheet:null,setSkin:function(a){if(!EG.UI.sheet){EG.UI.sheet=EG.Style.createSheet("")}EG.UI.sheet.href=EG.basePath+"/skins/"+a+"/ui.css";EG.UI.skin=a},GITEMIDX:0,GITEMS:{}}});EG.RESIZING=false;EG.onload(function(){})})();(function(){EG.define("EG.ui.Item",[],function(a){return{config:{renderTo:null,id:null,width:null,height:null,style:null,cls:null,region:null,hidden:false,animate:null},constructor:function(b){this.initItem(b);this.build();this.getElement().item=this;if(this.id){this.getElement().id=this.id}a.setStyle(this);this.afterBuild();this.setAnimate(this.animate);a.doRenderTo(this);if(this.hidden){EG.hide(this.getElement())}},setAnimate:function(b){this.animate=b;if(this.animate){var c=this.getElement();EG.Anim.remove(c);EG.Anim.add(c,this.animate.split(" "))}},afterBuild:function(){},getStyleEle:function(){return this.getElement()},getElement:function(){return this.element},render:function(){},renderOuter:function(){a.fit(this);this.saveOuterSize()},saveOuterSize:function(){var b=EG.Style.current(this.getElement()).overflow;EG.css(this.getElement(),"overflow:hidden;");this._outerSize=this.getSize();EG.css(this.getElement(),"overflow:"+b+";")},initItem:function(b){this.initConfig(b);this.oWidth=this.width;this.oHeight=this.height;this.pItem=null},getOWidth:function(){return this.oWidth},getOHeight:function(){return this.oHeight},getSize:function(){return EG.getSize(this.getElement())},getInnerHeight:function(){return EG.getSize(this.getElement()).innerHeight},getInnerWidth:function(){return EG.getSize(this.getElement()).innerWidth},build:function(){this.element=EG.CE({tn:"div",item:this})},destroy:function(){},setHidden:function(b){this.hidden=b;if(this.hidden){EG.hide(this.getElement())}else{EG.show(this.getElement())}},isItem:true,statics:{itemClasses:{},regist:function(b,c){a.itemClasses[b]=c},create:function(d,b){if(!d){d="panel"}var c=EG._alias[d];if(!c){throw new Error("EG.ui.Item#create:不支持类型:"+d)}return new c(b)},setWidth:function(b,c){a.setSize(b,c,"width")},setHeight:function(c,b){a.setSize(c,b,"height")},setStyle:function(b){if(b.cls){EG.setCls(b.getStyleEle(),b.cls)}if(b.style){EG.css(b.getStyleEle(),b.style)}},initItem:function(b){if(b.renderTo){if(b.element.parentNode==null){if(a.isItem(b.renderTo)){b.renderTo.addItem(b)}else{if(EG.DOM.isElement(b.renderTo)){b.renderTo.appendChild(b.element)}}}b.render()}},setSize:function(b,d,c){if(d!=null){if(typeof(d)=="number"){EG.css(b,c+":"+d+"px")}else{EG.css(b,c+":"+d)}}else{EG.css(b,c+":100%")}},fit:function(e){if(a.isItem(e)){e={item:e}}var k=e.item,c=e.element;if(!c){if(k){c=k.element}else{throw new Error("fit:没有element")}}var b=e.sSize||EG.getSize(c);var g=e.dSize;if(!g){if(k&&(k.width!=null||k.height!=null)){g={width:k.width,height:k.height}}else{g={width:EG.unnull(e.width,"100%"),height:EG.unnull(e.height,"100%")}}}var f=e.pSize||EG.getSize(c.parentNode);if(f.width==null&&f.innerWidth!=null){f.width=f.innerWidth}if(f.height==null&&f.innerHeight!=null){f.height=f.innerHeight}var i=e.type||"all",j,d;if(g.width!=null&&g.width!="auto"&&(i=="all"||i=="width")){j=EG.Style.size2Num(g.width,f.width)-(b.outerWidth-b.innerWidth-b.vScrollWidth);if(j<0){j=0}EG.css(c,{width:j+"px"})}if(g.height!=null&&g.height!="auto"&&(i=="all"||i=="height")){d=EG.Style.size2Num(g.height,f.height)-(b.outerHeight-b.innerHeight-b.hScrollWidth);if(d<0){d=0}EG.css(c,{height:d+"px"})}return{width:j,height:d,pSize:f}},pack:function(e){var i=e.item;var g=e.pEle||(i?i.getElement():null);var c=e.cEle;var b=e.width;var h=e.height;var f=e.pSize||EG.getSize(g);if(c&&(b==null||h==null)){var j=EG.getSize(c);if(b==null){b=j.outerWidth}if(h==null){h=j.outerHeight}}var d={};if(b!=null){b=(f.paddingLeft+b+f.paddingRight);EG.css(g,"width:"+b+"px");d.width=b}if(h!=null){h=(f.paddingTop+h+f.paddingBottom);EG.css(g,"height:"+h+"px");d.height=h}return d},isItem:function(b){return b&&typeof(b.getElement)=="function"},doRenderTo:function(b){if(b.renderTo){EG.$(b.renderTo).appendChild(b.getElement());b.render()}},afterExtend:function(b){(function(){var c=b.prototype.render;if(c){b.prototype.render=function(){if(!EG.DOM.contains(this.getElement())){return}if(EG.Style.isHide(this.getElement().parentNode,true)){return}c.apply(this,arguments)}}})()},fixBody:function(c){var b=c.parentNode;if(b==EG.getBody()){EG.css(EG.getBody(),"margin:0px;padding:0px;width:100%;height:100%");EG.css(c,"position:absolute;left:0px;top:0px;")}},$:function(b){return EG.$(b).item},getPath:function(c){var b=c.getClass()._className;while((c=c.pItem)){b=c.getClass()._className+"("+(c.id?c.id:"")+")>"+b}return b}}}})})();(function(){EG.define("EG.ui.Container",["EG.ui.Item"],function(a,b){return{extend:a,config:{layout:"default",itemsConfig:[],cnConfig:[],innerHTML:null,isRenderingOuter:false},constructor:function(c){this.items=[];this.callSuper([c])},afterBuild:function(){this.setLayout(this.layout,false);if(this.itemsConfig.length>0){this.addItem(this.itemsConfig,false)}else{if(this.cnConfig.length>0){this.addChildren(this.cnConfig,null,false)}else{if(this.innerHTML){this.getItemContainer().innerHTML=this.innerHTML}}}},getItemContainer:function(){return this.element},addItem:function(h,j,c){if(typeof(j)=="undefined"){j=true}var f=(!EG.isArray(h))?[h]:h;var e=[];for(var g=0,d=f.length;g<d;g++){h=f[g];if(EG.isLit(h)){h=a.create(h.xtype,h)}h.pItem=this;if(c>=0){EG.Array.insert(this.items,c,h)}else{this.items.push(h)}if(this.layoutManager.addOnLayout){continue}EG.DOM.addChildren(this.getItemContainer(),h.getElement(),c);e.push(h)}if(j){if(!this.isPItemAuto()){this.doLayout()}}return e.length>1?e:e[0]},removeItem:function(c,d){if(d==null){d=true}if(typeof(c)=="number"){c=EG.Array.get(this.items,c)}this.getItemContainer().removeChild(c.getElement());EG.Array.remove(this.items,c);c.pItem=null;if(d){if(!this.isPItemAuto()){this.doLayout()}}},addChildren:function(e,c,d){if(d==null){d=true}EG.DOM.addChildren(this.getItemContainer(),e,c);if(d){this.doLayout()}},render:function(){EG.ui.Item.fixBody(this.element);a.fit(this);if(this.animate){EG.Anim.beforePlay(this.getElement())}if(this.items.length>0||this.layoutManager.force){this.doLayout()}if(this.animate){EG.Anim.play(this.getElement())}},renderOuter:function(){this.isRenderingOuter=true;a.fit(this);if(this.isAutoWidth()||this.isAutoHeight()){if(this.items.length>0||this.layoutManager.force){this.doLayout()}}this.saveOuterSize();this.isRenderingOuter=false},getItem:function(c){if(typeof(c)=="number"){return this.items[c]}else{if(this.layoutManager){return this.layoutManager.getItem(c)}else{throw new Error("无法获取Item,参数不对或布局器不存在")}}},setLayout:function(c,d){if(d==null){d=true}this.layout=c;this.layoutManager=EG.ui.Layout.create(this.layout,this);if(d){this.layoutManager.doLayout()}},doLayout:function(){var c=EG.Style.current(this.element).overflow;EG.css(this.element,"overflow:hidden");this.layoutManager.doLayout();EG.css(this.element,"overflow:"+c)},isPItemAuto:function(){if(this.pItem){var c=this.pItem.isAutoWidth();var d=this.pItem.isAutoWidth();if(!c&&!d&&(this.isAutoWidth()||this.isAutoHeight())){this.pItem.doLayout();return true}else{if(c||d){var e=this.pItem;while(e.pItem){if(!e.pItem.isAutoWidth()&&!e.pItem.isAutoHeight()){break}else{e=e.pItem}}e.doLayout();return true}}}return false},getItemLeft:function(){return this.getItem({region:"left"})},getItemCenter:function(){return this.getItem({region:"center"})},getItemRight:function(){return this.getItem({region:"right"})},getItemTop:function(){return this.getItem({region:"top"})},getItemBottom:function(){return this.getItem({region:"bottom"})},isAutoWidth:function(){return this.getOWidth()=="auto"},isAutoHeight:function(){return this.getOHeight()=="auto"},setInnerWidth:function(d,c){c=EG.n2d(c,true);EG.ui.Container.autoSize(this.getItemContainer(),d,"width");if(c){this.width=this.getSize().outerWidth}},setInnerHeight:function(c,d){d=EG.n2d(d,true);EG.ui.Container.autoSize(this.getItemContainer(),c,"height");if(d){this.height=this.getSize().outerHeight}},getInnerHeight:function(){return EG.getSize(this.getItemContainer()).innerHeight},getInnerWidth:function(){return EG.getSize(this.getItemContainer()).innerWidth},destroy:function(){for(var c=0;c<this.items.length;c++){this.items[c].destroy()}},clear:function(){for(var c=0;c<this.items.length;c++){if(this.items[c].isContainer){this.items[c].clear()}this.items[c].destroy()}EG.DOM.removeChilds(this.getItemContainer());EG.Array.clear(this.items)},isContainer:true,statics:{autoSize:function(e,c,d){var f=e.isItem?e.getElement():e;EG.css(f,(d=="width"?"width":"height")+":"+c+"px")},afterExtend:function(c){(function(){var d=c.prototype.doLayout;if(d){c.prototype.doLayout=function(){if(!EG.DOM.contains(this.getElement())){return}d.apply(this,arguments)}}})()}}}})})();(function(){EG.ui.Component=function(){};EG.ui.Component.addChildren=function(d,e){var c=(!EG.isArray(e))?[e]:e;for(var b=0,a=c.length;b<a;b++){e=c[b];if(EG.isLit(e)){e=EG.CE(e)}d.getElement().appendChild(e)}}})();(function(){EG.define("EG.ui.Drag",["EG.ui.Item"],function(a,b){return{config:{target:null,handle:null,parent:null,moveTarget:false,beforeStartDrag:null,afterEndDrag:null,sycHandleSize:false,cursoron:true},constructor:function(c){b.load();this.initConfig(c);this.handles=[];if(this.handle||this.target){this.bindHandle(this.handle||this.target)}this.bindParent(this.parent||EG.getBody())},bindHandle:function(f){this.handles.push(f);f=(!EG.isArray(f))?[f]:f;for(var d=0,c=f.length;d<c;d++){var e=f[d];if(e.getElement){e=e.getElement}EG.CE({ele:e,style:"cursor:move",onmousedown:b._events.handle.onmousedown,me:this})}},bindParent:function(c){this.parent=c},statics:{load:function(){if(!b.loaded){b.dragGhost=EG.CE({pn:EG.getBody(),tn:"div",cls:"eg_drager"});EG.bindEvent(EG.getBody(),"onselectstart",b._events.parent.onselectstart);EG.bindEvent(EG.getBody(),"onmouseup",b._events.parent.onmouseup);EG.bindEvent(EG.getBody(),"onmousemove",b._events.parent.onmousemove);b.loaded=true;EG.hide(b.dragGhost)}},isDraging:function(){return b.cur!=null},startDrag:function(d){b.cur=this.me;var f=this;while(true){if(EG.Array.has(b.cur.handles,f)){break}else{f=this.parentNode;if(f==null){break}}}b.selectable=false;if(f==null){throw new Error("发生预料外的错误->未找到拖拽节点")}b.curHandle=f;var c=b.cur.sycHandleSize?f:b.cur.target;b.refPos=EG.Tools.getMousePos(d,c);b.refPos_w=EG.Tools.getMousePos(d);b.showDragGhost(d)},draging:function(f){if(!b.isDraging()){return}if(!EG.Tools.isPressLeft(f)){b.endDrag(f);return}var g=EG.Tools.getMousePos(f);var d=g.y;var c=g.x;if(!b.cur.cursoron){d+=10;c+=10}else{d=d-b.refPos.y;c=c-b.refPos.x}EG.css(b.dragGhost,"top:"+d+"px;left:"+c+"px;");if(b.cur.beforeStartDrag){b.cur.beforeStartDrag.apply(b.cur,[f])}},endDrag:function(i){if(!b.cur){return}if(EG.Style.isHide(b.dragGhost)){return}var d=EG.Tools.getMousePos(i);var f=EG.Style.current(b.dragGhost);var h=f.top=="auto"?"0":f.top;var g=f.left=="auto"?"0":f.left;if(b.cur.moveTarget&&b.cur.parent!=EG.getBody()){var j=EG.Style.current(b.cur.target||b.cur.moveTarget);var k=j.top=="auto"?"0":j.top;var c=j.left=="auto"?"0":j.left;h=(parseInt(EG.String.removeEnd(k,"px"))+d.y-b.refPos_w.y)+"px";g=(parseInt(EG.String.removeEnd(c,"px"))+d.x-b.refPos_w.x)+"px"}if(b.cur.moveTarget){EG.css(b.cur.target,"position:absolute;top:"+h+";left:"+g+";")}EG.hide(b.dragGhost);b.selectable=true;if(b.cur.afterEndDrag){b.cur.afterEndDrag.apply(this,[i])}b.cur=null},changeSelectable:function(c){if(!c){EG.Style.css(EG.getBody(),EG.Style.c.selectnone)}else{EG.Style.css(EG.getBody(),EG.Style.c.selectauto)}b.selectable=c},_events:{handle:{onmousedown:function(d){var c=this;b.changeSelectable(false);b.dragThread=window.setTimeout(function(){b.startDrag.apply(c,[d])},100);return true}},parent:{onselectstart:function(c){return b.selectable},onmousemove:function(c){b.draging(c)},onmouseup:function(c){if(b.dragThread){window.clearTimeout(b.dragThread);b.dragThread=null}if(b.cur){b.endDrag(c)}b.changeSelectable(true)}}},bindDrag:function(c){new EG.ui.Drag(c)},showDragGhost:function(g){g=EG.Event.getEvent(g);var i=EG.Tools.getMousePos(g);var f=i.y-b.refPos.y;var d=i.x-b.refPos.x;EG.css(b.dragGhost,"top:"+f+"px;left:"+d+"px;");EG.show(b.dragGhost);var h=b.cur.sycHandleSize?b.curHandle:b.cur.target;var c=EG.getSize(h);a.fit({element:b.dragGhost,dSize:{width:c.outerWidth,height:c.outerHeight}})},hideDragGhost:function(){EG.hide(b.dragGhost)}}}})})();(function(){EG.define("EG.ui.Pop",["EG.ui.Container","EG.ui.Item"],function(a,b,c){return{extend:a,config:{cls:"eg_pop",lock:false,posFix:true,innerHTML:null,target:null,parent:null,afterOpen:null,expandOuter:true,onClose:null},constructor:function(d){var e=this;this.callSuper([d]);EG.hide(this.element)},build:function(){this.element=EG.CE({tn:"div",cls:this.cls+"-outer",item:this,cn:[this.dLocker=EG.CE({tn:"div",cls:this.cls+"-locker",item:this}),this.dPop=EG.CE({tn:"div",cls:this.cls,item:this})]})},getStyleEle:function(){return this.dPop},getItemContainer:function(){return this.dPop},open:function(){EG.DOM.insertAfter(this.element,this.element.parentNode.lastChild);EG.show(this.element);this.render();if(this.afterOpen){this.afterOpen.apply(this)}},close:function(){if(this.onClose){var d=this.onClose.apply(this.onCloseSrc||this,[]);if(d===false){return}}EG.hide(this.element)},setAnimate:function(d){this.animate=d;if(this.animate){var e=this.dPop;EG.Anim.remove(e);EG.Anim.add(e,this.animate.split(" "))}},render:function(){var e=this.parent||this.getElement().parentNode;var d=EG.getSize(e);if(this.expandOuter){b.fit({element:this.element,dSize:{width:d.innerWidth,height:d.innerHeight}})}b.fit({element:this.dPop,dSize:{width:this.width,height:this.height}});if(this.items.length>0){this.doLayout()}if(this.animate){EG.Anim.beforePlay(this.dPop)}if(this.lock){this.fullLock()}if(this.posFix){this.doPosFix()}if(this.animate){EG.Anim.play(this.dPop)}},fullLock:function(){b.fit({element:this.dLocker,dSize:{width:"100%",height:"100%"},pSize:EG.getSize(this.getElement())})},doPosFix:function(){EG.Style.center(this.dPop,this.getElement().parentNode);EG.Style.middle(this.dPop,this.getElement().parentNode)},moveTo:function(d){EG.Style.moveTo(this.dPop,d)},isOpen:function(){return !EG.Style.isHide(this.element)}}})})();(function(){EG.define("EG.ui.Button",["EG.ui.Item"],function(a,b){return{alias:"button",extend:a,config:{text:"",click:null,mouseover:null,mouseout:null,icon:null,cls:"eg_button",iconAble:true,menuConfig:null,iconOnly:false,textStyle:null,tabIndex:0,renderSizeAble:false,menuStyle:null,titleStyle:null,disable:false},constructor:function(c){this.callSuper([c]);if(!this.icon||!this.iconAble){EG.hide(this.dIcon)}else{this.dIcon.className=this.cls+"-icon icon_"+this.icon}this.buildMenu();if(this.textStyle){this.setTextStyle(this.textStyle)}this.setDisable(this.disable)},build:function(){var c=this;var d=this.getClass();if(this.iconOnly){this.element=EG.CE({tn:"div",cls:this.cls,item:this,onclick:d._events.element.onclick,cn:[this.dIcon=EG.CE({tn:"a",title:this.text})]})}else{this.element=EG.CE({tn:"div",cls:this.cls,item:this,onclick:d._events.element.onclick,onmouseover:d._events.element.onmouseover,onmouseout:d._events.element.onmouseout,cn:[this.dOuter=EG.CE({tn:"div",cls:this.cls+"-outer",item:this,cn:[this.dIcon=EG.CE({tn:"div"}),this.dTitle=EG.CE({tn:"div",cls:this.cls+"-title",innerHTML:this.text,style:EG.unnull(this.titleStyle,"")}),this.dMulti=EG.CE({tn:"div",cls:this.cls+"-multi",onclick:this.showMenu,onclickSrc:this,item:this})],onmouseover:d._events.outer.onmouseover,onmouseout:d._events.outer.onmouseout})]})}if(typeof(this.tabIndex)=="number"){this.element.tabIndex=this.tabIndex}},doClick:function(){if(this.disable){return}if(this.click){this.click.apply(this["clickSrc"]||this)}},setTextStyle:function(c){this.textStyle=c;EG.css(this.dTitle,this.textStyle)},setText:function(c){this.text=c;this.dTitle.innerHTML=c},setMenu:function(c){this.menuConfig=c;if(this.dMenus){EG.DOM.remove(this.dMenus)}if(this.dMulti){EG.DOM.remove(this.dMulti)}this.buildMenu()},buildMenu:function(){if(this.menuConfig&&this.menuConfig.length>0){this.dMenus=EG.CE({pn:this.element,tn:"div",cls:this.cls+"-menu",style:"position:absolute;z-index:1;overflow:hidden;"});for(var d=0,c=this.menuConfig.length;d<c;d++){var e=this.menuConfig[d];EG.CE({pn:this.dMenus,tn:"a",idx:d,innerHTML:e.text,href:"javascript:void(0)",cls:this.cls+"-mi",item:this,onclick:b._events.aMenuEle.onclick})}}else{if(this.dMulti){EG.hide(this.dMulti)}}if(this.dMenus){EG.hide(this.dMenus);if(this.menuStyle){EG.css(this.dMenus,this.menuStyle)}}},render:function(){if(this.renderSizeAble){a.fit(this);var d=EG.getSize(this.element);a.fit({element:this.dOuter,pSize:d,dSize:{width:"100%",height:"100%"}});a.fit({element:this.dIcon,pSize:EG.getSize(this.dOuter),dSize:{height:"100%"}});a.fit({element:this.dTitle,pSize:d,dSize:{height:"100%"}});a.fit({element:this.dMulti,pSize:d,dSize:{height:"100%"}});EG.css(this.dTitle,"line-height:"+EG.getSize(this.dTitle).innerHeight+"px")}if(this.dMenus){var c=EG.getSize(this.element);a.fit({element:this.dMenus,sSize:c,dSize:{width:c.outerWidth}})}},setDisable:function(c){this.disable=c;if(c){if(this.dOuter){this.dOuter.className=this.cls+"-disable"}}else{if(this.dOuter){this.dOuter.className=this.cls+"-outer"}}},renderOuter:function(){if(this.renderSizeAble){a.fit(this)}this._outerSize=this.getSize()},showMenu:function(c){if(this.dMenus){EG.show(this.dMenus)}if(c){EG.Event.stopPropagation(c)}},statics:{_events:{element:{onclick:function(c){this.item.doClick();EG.Event.stopPropagation(c)},onmouseout:function(d){var c=this.item;if(c.disable){return}if(!c.dMenus){return}if(c.outThread!=null){return}c.outThread=setTimeout(function(){EG.hide(c.dMenus)},10);EG.Event.stopPropagation(d)},onmouseover:function(d){var c=this.item;if(c.disable){return}if(!c.dMenus){return}if(c.outThread!=null){clearTimeout(c.outThread);c.outThread=null}EG.Event.stopPropagation(d)}},outer:{onmouseover:function(){var c=this.item;if(c.disable){return}this.className=c.cls+"-outer "+c.cls+"-on"},onmouseout:function(){var c=this.item;if(c.disable){return}this.className=c.cls+"-outer"}},aMenuEle:{onclick:function(f){var c=this.item;if(c.disable){return}var d=c.menuConfig[this.idx];d.click.apply(d.clickSrc||c);EG.hide(c.dMenus);EG.Event.stopPropagation(f)}}}}}})})();(function(){EG.define("EG.ui.Dialog",["EG.ui.Pop","EG.ui.Drag","EG.ui.Button","EG.ui.Item"],function(e,b,a,c,d){return{alias:"dialog",extend:e,config:{cls:"eg_dialog",title:null,btnsConfig:null,zIndex:null,bodyStyle:null,headStyle:null,footStyle:null,closeable:true,fullable:false,dragable:false},constructor:function(f){this.callSuper([f]);if(this.title){this.setTitle(this.title)}if(this.btnsConfig){this.setButtons(this.btnsConfig)}this.setFullable(this.fullable);this.setCloseable(this.closeable);this.setDragable(this.dragable)},build:function(){var f=this;this.callSuper("build");this.dHead=EG.CE({pn:this.dPop,tn:"div",cls:this.cls+"-head",cn:[this.dTitle=EG.CE({tn:"div",cls:this.cls+"-title"}),EG.CE({tn:"div",cls:this.cls+"-trBtns",cn:[this.dFuller=EG.CE({tn:"a",cls:this.cls+"-fuller",me:this,onmouseup:d._events.dFuller.onmouseup,onmousedown:d._events.dFuller.onmousedown}),this.dCloser=EG.CE({tn:"a",cls:this.cls+"-closer",me:this,onmouseup:d._events.dCloser.onmouseup,onmousedown:d._events.dCloser.onmousedown})]})]});this.dBody=EG.CE({pn:this.dPop,tn:"div",cls:this.cls+"-body"});this.dFoot=EG.CE({pn:this.dPop,tn:"div",cls:this.cls+"-foot"});if(this.bodyStyle){EG.css(this.dBody,this.bodyStyle)}if(this.headStyle){EG.css(this.dHead,this.headStyle)}if(this.footStyle){EG.css(this.dFoot,this.footStyle)}if(this.zIndex!=null){EG.css(this.element,"z-index:"+this.zIndex)}},getItemContainer:function(){return this.dBody},setFullable:function(f){if(f){EG.show(this.dFuller)}else{EG.hide(this.dFuller)}},setDragable:function(f){this.dragable=f;if(!this.drag){this.drag=new b({target:this.dPop,parent:this.dLocker,handle:this.dHead,moveTarget:true})}},setCloseable:function(f){if(f){EG.show(this.dCloser)}else{EG.hide(this.dCloser)}},setButtons:function(j){EG.DOM.removeChilds(this.dFoot);if(j==null){return}for(var h=0,f=j.length;h<f;h++){var g=EG.isLit(j[h])?new a(j[h]):j[h];this.dFoot.appendChild(g.getElement())}},setTitle:function(f){EG.setValue(this.dTitle,f)},setInnerWidth:function(f){c.pack({pEle:this.dBody,width:f});c.pack({pEle:this.dPop,width:EG.getSize(this.dBody).outerWidth})},setInnerHeight:function(f){c.pack({pEle:this.dBody,height:f});c.pack({pEle:this.dPop,height:EG.getSize(this.dBody).outerHeight+EG.getSize(this.dHead).outerHeight+EG.getSize(this.dFoot).outerHeight})},getInnerHeight:function(){if(typeof(this.oHeight)=="number"){return EG.getSize(this.dPop).innerHeight-EG.getSize(this.dHead).outerHeight-EG.getSize(this.dFoot).outerHeight}else{return this.oHeight}},full:function(){if(!this._fulled){this.oParent=this.parent;this.oParentNode=this.getElement().parentNode;this._oWidth=this.width;this._oHeight=this.height;EG.getBody().appendChild(this.getElement());this.width="100%";this.height="100%";this.render();this._fulled=true}else{this.parent=this.oParent;this.width=this._oWidth;this.height=this._oHeight;this.oParentNode.appendChild(this.getElement());this.render();this._fulled=false}},render:function(){var h=this.parent||this.getElement().parentNode;var g=EG.getSize(h);c.fit({element:this.element,dSize:{width:g.innerWidth,height:g.innerHeight},pSize:g});EG.css(this.element,"top:0px;left:0px");c.fit({element:this.dPop,dSize:{width:this.width,height:this.height}});var f=this.isAutoHeight();if(!f){c.fit({element:this.dBody,dSize:{height:(EG.getSize(this.dPop).innerHeight-EG.getSize(this.dHead).outerHeight-EG.getSize(this.dFoot).outerHeight)}})}var i=this.isAutoWidth();if(!i){c.fit({element:this.dBody,dSize:{width:"100%"}})}if(this.items.length>0){this.doLayout()}g=EG.getSize(this.dPop);c.fit({element:this.dHead,dSize:{width:g.innerWidth}});c.fit({element:this.dFoot,dSize:{width:g.innerWidth}});EG.css(this.dBody,"position:relative;");if(this.animate){EG.Anim.beforePlay(this.dPop)}if(this.lock){this.fullLock()}if(this.posFix){this.doPosFix()}if(this.animate){EG.Anim.play(this.dPop)}},statics:{_events:{dCloser:{onmousedown:function(f){EG.Event.stopPropagation(f)},onmouseup:function(g){var f=this.me;f.close();EG.Event.stopPropagation(g)}},dFuller:{onmousedown:function(f){EG.Event.stopPropagation(f)},onmouseup:function(g){var f=this.me;f.full();EG.Event.stopPropagation(g)}}}}}})})();(function(){EG.define("EG.ui.Tip",["EG.ui.Item"],function(a,b){EG.onload(function(){var d;var c=function(){if(!d){d=new b()}};EG.Tip={open:function(){c();d.open.apply(d,arguments)},error:function(){c();d.error.apply(d,arguments)},close:function(){c();d.close.apply(d,arguments)},info:function(){c();d.info.apply(d,arguments)},message:function(){c();d.message.apply(d,arguments)}}});return{extend:a,config:{lock:false,cls:"eg_tip"},constructor:function(c){this.callSuper([c])},build:function(){var c=this;this.element=EG.CE({pn:EG.getBody(),tn:"div",style:"border:1px solid red;border-radius:2px;background:#FFFFC0;cursor:pointer;position:absolute;z-index:2",cn:[this.dIcon=EG.CE({tn:"div",style:EG.Style.c.dv}),this.dMessage=EG.CE({tn:"div",style:EG.Style.c.dv+";padding:5px"})],onclick:function(d){if(c.currentClick!=null){c.currentClick.apply(this,[d]);c.currentClick=null;EG.Event.stopPropagation(d)}EG.hide(this)}});EG.hide(this.element)},close:function(){EG.hide(this.element)},open:function(){EG.show(this.element)},error:function(d,c){this.message({message:d,target:c,type:"error"})},info:function(d,c){this.message({message:d,target:c,type:"info"})},message:function(g){g=g||{};var n=g.message,h=g.target,i=g.target,j=g.pos,c=g.style,d=g.animate,l=g.click;var k=g.autoclose||false;var f=g.closetime;if(f){k=true}this.dMessage.innerHTML=n;EG.show(this.element);EG.css(this.dMessage,"width:100px");if(c){EG.css(this.element,c)}else{if(!j&&h){j=EG.Tools.getElementPos(h);var e=EG.getSize(h);var m=EG.getSize(this.dMessage);if((j.x+e.clientWidth+m.clientWidth)>screen.width){j.x=j.x+e.clientWidth-m.clientWidth;j.y=j.y+e.clientHeight}else{j.x=j.x+e.clientWidth}}EG.css(this.element,"left:"+j.x+"px;top:"+j.y+"px")}if(d){EG.Anim.remove(this.element);EG.Anim.add(this.element,d.split(" "))}EG.show(this.element)}}})})();(function(){EG.define("EG.ui.Locker",["EG.ui.Dialog","EG.ui.Button"],function(b,a,c){EG.onload(function(){EG.Locker=new c({width:Math.min(500,document.documentElement.clientWidth),height:Math.min(120,document.documentElement.clientHeight),renderTo:EG.getBody()})});return{extend:b,config:{cls:"eg_locker",title:"提示",zIndex:2},constructor:function(d){this.callSuper([d]);EG.css(this.dBody,"text-align:center;overflow:auto")},build:function(){this.callSuper("build");var d=this.dBody;this.dType=EG.CE({pn:d,tn:"div",cls:this.cls+"-type"});this.dWait=EG.CE({pn:d,tn:"div",cls:this.cls+"-wait",innerHTML:"正在加载..."})},lock:function(d){if(d){this.open()}else{if(this.t!=null){clearTimeout(this.t);this.t=null}this.close()}},wait:function(d){this.message({message:d,closeable:false})},confirm:function(f){if(typeof(f)=="string"){f={message:f};if(arguments.length>1){f.yes_callback=arguments[1]}if(arguments.length>2){f.no_callback=arguments[2]}}var i=f.message,k=f.yes_title||"确定",e=f.no_title||"取消",h=f.yes_callback,g=f.no_callback||function(){EG.Locker.lock(false)};var d,j;f.message=EG.CE({tn:"div",cn:[{tn:"div",innerHTML:i},{tn:"div",style:"margin-top:0.5rem;",cn:[d=new a({text:k,click:h,cls:"eg_button_small"}),j=new a({text:e,click:g,cls:"eg_button_small",style:"margin-left:2rem;"})]}],onkeydown:function(l){l=EG.Event.getEvent(l);if(l.keyCode==32||l.keyCode==13){d.doClick()}else{if(l.keyCode==27){j.doClick()}}}});f.reqFocus=true;f.reqFocusElement=f.message;this.message(f)},message:function(f){f=f||{};var i=f.type;if(i){EG.setCls(this.dType,["type","type-"+i],this.cls);EG.show(this.dType)}else{EG.hide(this.dType)}EG.setCls(this.dWait,["wait","fontM"],this.cls);var m=typeof(f)=="string"?f:EG.unnull(f.message,"请稍等...");if(typeof(m)=="string"){this.dWait.innerHTML=m}else{EG.DOM.removeChilds(this.dWait);this.dWait.appendChild(m)}var d=f.closeable!=null?f.closeable:true;var j=f.autoclose||false;var e=f.closetime;if(e){j=true}var k=f.callback;if(!d||j){this.setCloseable(false)}else{this.setCloseable(true)}this.open();var h=f.reqFocus;if(h){var l=f.reqFocusElement;EG.CE({ele:l,tabIndex:1});l.focus()}if(j){if(e==null){e=1200}var g=this;if(this.t!=null){clearTimeout(this.t);this.t=null}this.t=setTimeout(function(){g.t=null;g.close();if(k){k()}},e)}}}})})();(function(){EG.ui.Option=function(b){this.btns=b.btns;this.pop=new EG.ui.Pop({closeable:false});this.element=EG.CE({tn:"div",style:"padding:10px"});this.pop.getElement().appendChild(this.element);if(this.btns){this.setBtns(this.btns)}document.body.appendChild(this.element)};EG.ui.Option.prototype={};var a=EG.ui.Option;EG.ui.Option.prototype.setTextactions=function(b){for(var d=0,c=b.length;d<c;d++){}};EG.ui.Option.prototype.open=function(){this.pop.open()};EG.ui.Option.prototype.close=function(){this.pop.close()};EG.ui.Option.prototype.setBtns=function(d){for(var c=0,b=d.length;c<b;c++){this.element.appendChild(d[c].getElement())}}})();(function(){EG.define("EG.ui.Layout",[],function(a){return{statics:{layoutManagers:{},create:function(c,b){if(EG.String.isString(c)){c={type:c}}var d=EG.ui.layout[EG.Word.first2Uppercase(c.type)+"Layout"];if(!d){throw new Error("EG.ui.Layout#create:该布局器无法识别:"+c.type)}return new d(c,b)},regist:function(c,b){EG.ui.Layout.layoutManagers[c]=b},sizeDesc:function(b){var c={};c.ow=b.getOWidth();c.oh=b.getOHeight();c.isWA=b.isContainer&&b.isAutoWidth();c.isHA=b.isContainer&&b.isAutoHeight();c.isWP=(typeof(c.ow)=="string"&&c.ow.lastIndexOf("%")>=0);c.isHP=(typeof(c.oh)=="string"&&c.oh.lastIndexOf("%")>=0);c.isWE=c.ow==null;c.isHE=c.oh==null;c.isWN=(typeof(c.ow)=="number");c.isHN=(typeof(c.ow)=="number");return c},renderItems:function(e){for(var c=0,b=e.items.length;c<b;c++){var d=e.items[c];d.render()}}},addOnLayout:false,doLayout:function(){},hideItems:function(){var b=[];for(var c=0;c<this.items.length;c++){if(!this.items[c].hidden){b.push(this.items[c])}}this.items=b},renderItemsOuter:function(e){for(var c=0,b=this.items.length;c<b;c++){var d=this.items[c];if(e||(d.isContainer&&(d.isAutoHeight()||d.isAutoWidth()))){d.renderOuter()}}}}})})();(function(){EG.define("EG.ui.layout.DefaultLayout",["EG.ui.Layout","EG.ui.Item"],function(b,a,c){return{extend:b,config:{align:null,verticalAlign:null,horizontal:true},constructor:function(e,d){this.container=d;this.initConfig(e);this.force=this.align||this.verticalAlign},doLayout:function(){this.init();if(this.isCtWA||this.isCtHA){this.renderItemsOuter(true);this.autoSize()}if(!this.container.isRenderingOuter){b.renderItems(this);if(this.align=="center"){EG.Style.centerChilds(this.container.getItemContainer(),this.horizontal)}if(this.verticalAlign=="middle"){EG.Style.middleChilds(this.container.getItemContainer(),this.horizontal)}else{if(this.verticalAlign=="top"){EG.Style.topChilds(this.container.getItemContainer(),this.horizontal)}else{if(this.verticalAlign=="bottom"){EG.Style.bottomChilds(this.container.getItemContainer(),this.horizontal)}}}for(var f=0,d=this.items.length;f<d;f++){var h=this.items[f];if(h.region){var e=h.region.split[";"];for(var g in e){if(g=="center"){EG.Style.center(h.getElement())}else{if(g=="middle"){EG.Style.middle(h.getElement())}else{EG.css(h.getElement(),"position:absolute;"+g+"0px;")}}}}}}},init:function(){this.items=this.container.items;this.hideItems();this.isCtWA=this.container.isAutoWidth();this.isCtHA=this.container.isAutoHeight();this.hasItemWA=false;this.hasItemHA=false;for(var e=0,d=this.items.length;e<d;e++){var f=this.items[e];var g=b.sizeDesc(f);this.hasItemWA=this.hasItemWA||g.isWA;this.hasItemHA=this.hasItemHA||g.isHA;f.width=f.oWidth;f.height=f.oHeight;EG.css(f.getElement(),"width:auto;height:auto")}},autoSize:function(){var m=-1,f=-1,j=-1,d=-1;for(var n=0,k=this.items.length;n<k;n++){var t=this.items[n];try{var g=EG.Tools.getElementPos(t.getElement());var u=t.getSize();m=m==-1?(g.x-u.marginLeft):Math.min(g.x-u.marginLeft,m);f=f==-1?(g.x-u.marginLeft+u.outerWidth):Math.max(g.x-u.marginLeft+u.outerWidth,f);j=j==-1?(g.y-u.marginTop):Math.min(g.y-u.marginTop,j);d=d==-1?(g.y-u.marginTop+u.outerHeight):Math.max(g.y-u.marginTop+u.outerHeight,d)}catch(q){alert(a.getPath(t)+":"+t.getElement());throw q}}var r=Math.max(0,f-m);var o=Math.max(0,d-j);if(this.isCtWA){this.container.setInnerWidth(r)}if(this.isCtHA){this.container.setInnerHeight(o)}}}})})();(function(){EG.define("EG.ui.layout.BorderLayout",["EG.ui.Layout"],function(a,c){var b=EG.Style.size2Num;return{extend:a,constructor:function(e,d){this.container=d},getItem:function(e){for(var f=0,d=this.container.items.length;f<d;f++){if(this.container.items[f].region==e.region){return this.container.items[f]}}return null},doLayout:function(){this.init();if(this.isCtWA||this.isCtHA||this.hasItemHA||this.hasItemWA){if(!this.isCtWA){this.setItemWidth(this.ctW)}if(!this.isCtHA){this.setItemHeight(this.ctH)}this.renderItemsOuter();if(this.isCtWA){this.setCtWidth()}if(this.isCtHA){this.setCtHeight()}}if(!this.container.isRenderingOuter){this.setItemWidth(this.ctW,true);this.setItemHeight(this.ctH,true);a.renderItems(this)}},setCtWidth:function(){this.ctW=0;if(this.refMW){for(var d=0;d<this.mItems.length;d++){this.ctW+=this.getItemWidth(this.mItems[d])}}if(this.itemT&&this.itemT.getOWidth()!=null){this.ctW=Math.max(this.ctW,this.getItemWidth(this.itemT))}if(this.itemB&&this.itemB.getOWidth()!=null){this.ctW=Math.max(this.ctW,this.getItemWidth(this.itemB))}this.container.setInnerWidth(this.ctW)},setCtHeight:function(){this.ctH=0;if(this.refMH){for(var d=0;d<this.mItems.length;d++){var e=this.getItemHeight(this.mItems[d]);if(e==null){continue}this.ctH=Math.max(e,this.ctH)}}if(this.itemT&&this.itemT.getOHeight()!=null){this.ctH+=this.getItemHeight(this.itemT)}if(this.itemB&&this.itemB.getOHeight()!=null){this.ctH+=this.getItemHeight(this.itemT)}this.container.setInnerHeight(this.ctH)},getItemWidth:function(d){return d.isContainer&&d.isAutoWidth()?d._outerSize.outerWidth:d.getOWidth()},getItemHeight:function(d){return d.isContainer&&d.isAutoHeight()?d._outerSize.outerHeight:d.getOHeight()},setItemWidth:function(e,h){if(this.mItems.length>0){var g=this.setItemWidth_FN(this.itemL,e,h);var f=this.setItemWidth_FN(this.itemC,e,h);var d=this.setItemWidth_FN(this.itemR,e,h);if(g==-2||f==-2||d==-2){}else{if(f<0){if(g<0&&d<0){f=e/3;g=e/3;d=e/3}else{if(g>=0&&d>=0){f=e-g-d}else{if(g<0&&d<0){var j=e-d;f=0.5*j;g=0.5*j}else{if(d<0){var j=e-g;f=0.5*j;d=0.5*j}}}}}else{if(g<0&&d<0){var j=e-f;g=0.5*j;d=0.5*j}else{if(g<0){g=e-f-d}else{if(d<0){d=e-f-g}}}}}g=parseInt(g);f=parseInt(f);d=parseInt(d);if(this.itemL&&g>=0){this.itemL.width=g}if(this.itemC&&f>=0){this.itemC.width=f;var i=g>=0?g:0;EG.css(this.itemC,"left:"+i+"px")}if(this.itemR&&d>=0){this.itemR.width=d}}this.setItemWidth_FN2(this.itemT,e,h);this.setItemWidth_FN2(this.itemB,e,h)},setItemWidth_FN:function(g,e,h){if(!g){return 0}var f=g.getOWidth();var d=-1;if(f==null){d=-1}else{if(f=="auto"){d=h?g._outerSize.outerWidth:-2}else{if(typeof(f)=="string"&&f.lastIndexOf("%")>=0){if(e>0){d=b(f,e)}}else{if(typeof(f)=="number"){d=f}}}}return d},setItemWidth_FN2:function(e,d,f){if(!e||d<0||e.getOWidth()=="auto"){return}e.width=d},setItemHeight:function(d,e){var k=-1;if(this.mItems.length>0){for(var j=0;j<this.mItems.length;j++){var n=this.mItems[j];var l=n.getOHeight();if(l==null){continue}if(n.isContainer&&n.isAutoHeight()){if(e){l=n._outerSize.outerHeight}else{k=-2;break}}else{l=b(l,this.ctH)}k=Math.max(k,l)}}else{k=0}var m=this.setItemHeight_FN(this.itemB,d,e);var f=this.setItemHeight_FN(this.itemT,d,e);if(k==-2||m==-2||f==-2){}else{if(k==-1){if(f<0&&m<0){k=d/3;f=d/3;m=d/3}else{if(f>=0&&m>=0){k=d-m-f}else{if(f<0){var g=d-m;k=g*0.5;f=g*0.5}else{if(m<0){var g=d-f;k=g*0.5;m=g*0.5}}}}}else{if(f<0&&m<0){var g=d-k;f=g*0.5;m=g*0.5}else{if(f<0){f=d-k-m}else{if(m<0){m=d-k-f}}}}}k=parseInt(k);m=parseInt(m);f=parseInt(f);if(this.itemT&&f>=0){this.itemT.height=f}if(this.itemB&&m>=0){this.itemB.height=m}if(this.mItems.length>0&&k>=0){for(var j=0;j<this.mItems.length;j++){var n=this.mItems[j];n.height=k;EG.css(n,"top:"+f+"px")}}},setItemHeight_FN:function(g,e,i){if(!g){return 0}var f=g.getOHeight();var d=-1;if(f==null){d=-1}else{if(f=="auto"){d=i?g._outerSize.outerHeight:-2}else{if(typeof(f)=="string"&&f.lastIndexOf("%")>=0){if(e>0){d=b(f,e)}}else{if(typeof(f)=="number"){d=f}}}}return d},init:function(){this.items=this.container.items;this.hideItems();this.ctEle=this.container.getItemContainer();EG.css(this.ctEle,"position:absolute");this.itemT=this.itemB=this.itemC=this.itemL=this.itemR=null;this.isCtWA=this.container.isAutoWidth();this.isCtHA=this.container.isAutoHeight();this.hasItemWA=false;this.hasItemHA=false;this.hasMidWA=false;this.hasMidHA=false;this.hasMidWE=false;this.hasMidHE=false;this.hasMidWP=false;this.hasMidHP=false;this.hasMidWN=false;this.hasMidHN=false;this.ctW=this.container.getInnerWidth();this.ctH=this.container.getInnerHeight();this.mItems=[];for(var e=0,d=this.items.length;e<d;e++){var g=this.items[e];if(!g.region){var f="";while(g.pItem!=null){f+=">"+g.pItem.getClass()._className+"["+EG.Array.getIdx(g.pItem.items,g)+"]["+g.cls+"]["+g.pItem.layout+"]";g=g.pItem}alert(f);throw new Error("BorderLayout#doLayout:无region信息")}var h=a.sizeDesc(g);var j=false;this.hasItemWA=this.hasItemWA||h.isWA;this.hasItemHA=this.hasItemHA||h.isHA;if(g.region=="top"){this.itemT=g}else{if(g.region=="bottom"){this.itemB=g}else{if(g.region=="center"){this.itemC=g;j=true}else{if(g.region=="left"){this.itemL=g;j=true}else{if(g.region=="right"){this.itemR=g;j=true}else{throw new Error("BorderLayout#doLayout:暂不支持"+g.region)}}}}}if(j){this.mItems.push(g);this.hasMidWA=this.hasMidWA||h.isWA;this.hasMidHA=this.hasMidHA||h.isHA;this.hasMidWE=this.hasMidWE||h.isWE;this.hasMidHE=this.hasMidHE||h.isHE;this.hasMidWP=this.hasMidWP||h.isWP;this.hasMidWP=this.hasMidWP||h.isHP;this.hasMidWN=this.hasMidWN||h.isWN;this.hasMidHN=this.hasMidHN||h.isHN}if(g.region=="center"){EG.css(g,"position:absolute;");if(g.setCollapseAble){g.setCollapseAble(false)}}else{EG.css(g,"position:absolute;"+g.region+":0px");if(g.collapseAble&&g.setCollapseBehavior){g.setCollapseBehavior(g.region)}}g.width=g.oWidth;g.height=g.oHeight;EG.css(g.getElement(),"width:auto;height:auto")}this.refMW=false;this.refMH=false;if(this.mItems.length>0){this.refMW=this.isCtWA&&(!this.hasMidWE&&!this.hasMidWP);this.refMH=this.isCtHA&&(this.hasMidHN||this.hasMidHA)}}}})})();(function(){EG.define("EG.ui.layout.TableLayout",["EG.ui.Layout"],function(a,b){return{extend:a,config:{maxRow:0,maxCol:0,cellSpacing:0,cellPadding:0,border:0,style:null},constructor:function(d,c){this.container=c;this.initConfig(d);this.addOnLayout=true},getItem:function(c){return EG.Array.get(c)},fixPos:function(){for(var d=0,c=this.items.length;d<c;d++){var e=this.items[d].pos||[d,0];if(!EG.isArray(e)){throw new Error("EG.ui.layout#fixPos:item的pos类型需为数组")}if(e.length==0){e=[d,0]}else{if(e.length==1){e=[e[0],0]}else{if(e.length==2){}else{throw new Error("EG.ui.layout#fixPos:item的pos长度最大为2")}}}this.maxRow=Math.max(e[0]+1,this.maxRow);this.maxCol=Math.max(e[1]+1,this.maxCol);this.items[d].pos=e}},resetTable:function(){EG.DOM.removeAllRows(this.tbody);for(var d=0;d<this.maxRow;d++){var e=EG.CE({pn:this.tbody,tn:"tr"});for(var f=0;f<this.maxCol;f++){EG.CE({pn:e,tn:"td"})}}},putItems:function(){for(var d=0,c=this.items.length;d<c;d++){var f=this.items[d];var h=f.pos;var e=f.getElement();var g=this.tbody.childNodes[h[0]].childNodes[h[1]];if(f.vAlign){EG.css(g,"vertical-align:"+f.vAlign)}if(EG.Style.current(e).position=="absolute"){EG.css(e,"position:relative")}g.appendChild(e)}},autoSpan:function(){var n=this.tbody.childNodes;for(var i=0,m=n.length;i<m;i++){var l=n[i].childNodes;var c=0;var e=l.length;for(var h=e-1;h>=0;h--){if(l[h].innerHTML==""){c++;n[i].removeChild(l[h])}else{break}}if(l.length==0){continue}if(c>0){var g=l[l.length-1];g.colSpan=c+1}var f=parseInt(100/e);for(var h=0,d=l.length;h<d;h++){if(h==d-1){l[h].width=parseInt(((c>0)?(f*(c+1)):f))+"%"}else{l[h].width=f+"%"}}}},doLayout:function(){this.items=this.container.items;this.hideItems();var d=this.container.getItemContainer();if(!this.table){this.table=EG.CE({tn:"table",style:"width:100%;table-layout:fixed;"+(this.style?this.style:""),cellPadding:this.cellPadding,cellSpacing:this.cellSpacing,border:this.border});this.tbody=EG.CE({pn:this.table,tn:"tbody",style:"width:100%;"})}d.appendChild(this.table);this.fixPos();this.resetTable();this.putItems();this.autoSpan();for(var e=0,c=this.items.length;e<c;e++){var f=this.items[e];f.render()}this.autoSize()},autoSize:function(){var e=this.container.isAutoWidth();var c=this.container.isAutoHeight();if(e||c){var d=EG.getSize(this.table);if(e){this.container.setInnerWidth(d.outerWidth)}if(c){this.container.setInnerHeight(d.outerHeight)}}}}})})();(function(){EG.define("EG.ui.layout.LineLayout",["EG.ui.Layout"],function(b,c){return{extend:b,config:{direct:"H",align:null},constructor:function(e,d){this.container=d;this.initConfig(e);if(!this.align){this.align=this.direct=="H"?"left":"top"}},getItem:function(d){return EG.Array.get(d)},doLayout:function(){this.init();if(this.isCtWA||this.isCtHA||this.hasItemHA||this.hasItemWA){this.setItemSize();this.renderItemsOuter();if(this.isCtWA||this.isCtHA){this.autoSize()}}if(!this.container.isRenderingOuter){this.setItemSize(true);var f=0;for(var e=0,d=this.items.length;e<d;e++){var g=this.items[e];g.render();if(this.direct=="H"){EG.css(g,"position:absolute;"+this.align+":"+f+"px");f+=g.getSize().outerWidth}else{EG.css(g,"position:absolute;"+this.align+":"+f+"px");f+=g.getSize().outerHeight}}}},setItemSize:function(e){var h=0,d=0,o=0,m=0;var f={};if(!e){if(this.direct=="H"){if(this.isCtWA&&this.hasItemWA){return}}else{if(this.isCtHA&&this.hasItemHA){return}}}for(var j=0,g=this.items.length;j<g;j++){var n=this.items[j];var k=b.sizeDesc(n);f[j]=k;if(this.direct=="H"){if(!this.isCtWA&&k.isWP){n.width=a(n.getOWidth(),this.ctW)}}else{if(!this.isCtHA&&k.isHP){n.height=a(n.getOHeight(),this.ctH)}}if(this.direct=="H"){if(this.isCtHA){if(k.isHA){if(e){h=Math.max(h,n._outerSize.outerHeight)}else{return}}else{if(k.isHN){h=Math.max(h,n.height)}}}}else{if(this.isCtWA){if(k.isWA){if(e){h=Math.max(h,n._outerSize.outerWidth)}else{return}}else{if(k.isWN){h=Math.max(h,n.width)}}}}if(this.direct=="H"){if(!k.isWE){if(k.isWA){n.render();m+=n.getSize().outerWidth}else{m+=n.width}}else{d++}}else{if(!k.isHE){if(k.isHA){n.render();m+=n.getSize().outerHeight}else{m+=n.height}}else{d++}}}if(this.direct=="H"){if(!this.isCtHA){h=this.ctH}if(d){o=parseInt((this.ctW-m)/d)}}else{if(!this.isCtWA){h=this.ctW}if(d){o=parseInt((this.ctH-m)/d)}}for(var j=0,g=this.items.length;j<g;j++){var n=this.items[j];var k=f[j];if(this.direct=="H"){n.height=h;if(k.isWE){n.width=o}}else{n.width=h;if(k.isHE){n.height=o}}}},autoSize:function(){var g=0,e=0;for(var f=0,d=this.items.length;f<d;f++){var j=this.items[f];if(this.direct=="V"){if(this.isCtHA){g+=this.getItemHeight(j)}if(this.isCtWA){e=Math.max(e,this.getItemWidth(j))}}else{if(this.isCtHA){g=Math.max(g,this.getItemHeight(j))}if(this.isCtWA){e+=this.getItemWidth(j)}}}if(this.isCtWA){this.container.setInnerWidth(e);this.ctW=e}if(this.isCtHA){this.container.setInnerHeight(g);this.ctH=g}},getItemWidth:function(d){return d.isContainer&&d.isAutoWidth()?d._outerSize.outerWidth:d.getOWidth()},getItemHeight:function(d){return d.isContainer&&d.isAutoHeight()?d._outerSize.outerHeight:d.getOHeight()},init:function(){this.items=this.container.items;this.hideItems();var f=EG.Style.current(this.container.getItemContainer()).position;if(f!="absolute"&&f!="relative"){EG.css(this.container.getItemContainer(),"position:relative")}this.isCtWA=this.container.isAutoWidth();this.isCtHA=this.container.isAutoHeight();this.hasItemWA=false;this.hasItemHA=false;this.hasItemWN=false;this.hasItemHN=false;this.hasItemWE=false;this.hasItemHE=false;this.hasItemWP=false;this.hasItemHP=false;this.ctW=this.isCtWA?-1:this.container.getInnerWidth();this.ctH=this.isCtHA?-1:this.container.getInnerHeight();for(var e=0,d=this.items.length;e<d;e++){var g=this.items[e];var h=b.sizeDesc(g);this.hasItemWA=this.hasItemWA||h.isWA;this.hasItemHA=this.hasItemHA||h.isHA;this.hasItemWN=this.hasItemWN||h.isWN;this.hasItemHN=this.hasItemHN||h.isHN;this.hasItemWE=this.hasItemWE||h.isWE;this.hasItemHE=this.hasItemHE||h.isHE;this.hasItemWP=this.hasItemWP||h.isWP;this.hasItemHP=this.hasItemHP||h.isHP;if(this.direct=="H"){if(this.isCtWA&&(h.isWE||h.isWP)){throw new Error("EG.ui.layout.LineLayout:父组件宽度自动时，子组件宽度不能同为空||百分比")}}else{if(this.isCtHA&&(h.isHE||h.isHP)){throw new Error("EG.ui.layout.LineLayout:父组件高度自动时，子组件高度不能同为空||百分比")}}g.width=g.oWidth;g.height=g.oHeight;EG.css(g.getElement(),"width:auto;height:auto")}if(this.direct=="H"){if(this.isCtHA&&(!this.hasItemHN&&!this.hasItemHA)){throw new Error("EG.ui.layout.LineLayout:横向时,父组件高度为自动时,子组件高度不能既没有固定也没有自动")}}else{if(this.isCtWA&&(!this.hasItemWN&&!this.hasItemWA)){throw new Error("EG.ui.layout.LineLayout:纵向时,父组件宽度为自动时,子组件宽度不能既没有固定也没有自动")}}}}});var a=EG.Style.size2Num})();(function(){EG.define("EG.ui.Panel",["EG.ui.Container"],function(a,b){return{alias:"panel",extend:a,constructor:function(c){this.callSuper([c])}}})})();(function(){EG.define("EG.ui.XPanel",["EG.ui.Panel","EG.ui.Item"],function(c,a,b){return{alias:"xPanel",extend:c,config:{cls:"eg_xpanel",showTitle:true,showExpand:true,showBorder:true,title:null,collapseBehavior:"top",collapseAble:false,collapsed:false,barConfig:null,bodyStyle:null,headStyle:null},constructor:function(d){this.callSuper([d]);this.setCollapseAble(this.collapseAble)},build:function(){var d=this;this.element=EG.CE({tn:"div",cn:[this.dHead=EG.CE({tn:"div",cls:this.cls+"-dHead",cn:[this.dCollapse=EG.CE({tn:"div",cls:this.cls+"-dCollapse "+this.cls+"-dCollapse-"+this.collapseBehavior,onclick:function(){var e=EG.Style.isHide(d.dBody);d.collapse(!e)}}),this.dTitle=EG.CE({tn:"div",cls:this.cls+"-dTitle"})]}),this.dBody=EG.CE({tn:"div",cls:this.cls+"-dBody"})]});if(this.title){this.setTitle(this.title)}if(this.barConfig){this.setBar(this.barConfig)}if(this.bodyStyle){EG.css(this.dBody,this.bodyStyle)}if(this.headStyle){EG.css(this.dHead,this.headStyle)}},setBar:function(d){if(!this.dBar){this.dBar=new c({cls:this.cls+"-dBar"});this.dHead.appendChild(this.dBar.getElement())}this.dBar.clear();this.dBar.addItem(d)},render:function(){if(this.element.parentNode==EG.getBody()){EG.css(EG.getBody(),"margin:0px;padding:0px;width:100%;height:100%");EG.css(this.element,"position:absolute;left:0px;top:0px;")}a.fit(this);var e={width:"100%"};if(this.collapsed){e.height="100%";EG.hide(this.dBody)}else{EG.show(this.dBody)}EG.hide(this.dHead);a.fit({element:this.dHead,dSize:e});EG.show(this.dHead);if(!this.collapsed){if(this.dBar){this.dBar.width=Math.floor(EG.getSize(this.dHead).innerWidth-EG.getSize(this.dTitle).outerWidth-EG.getSize(this.dCollapse).outerWidth)-1;this.dBar.height=parseInt(EG.getSize(this.dHead).innerHeight);this.dBar.render()}}if(!this.collapsed){var d=EG.getSize(this.dHead);a.fit({element:this.dBody,dSize:{width:"100%",height:(EG.getSize(this.element).innerHeight-d.outerHeight)}});if(this.items.length>0){this.doLayout()}}else{}},getOHeight:function(){return this.oHeight},getOWidth:function(){if(this.collapsed){var d=EG.getSize(this.element);return d.outerWidth-d.innerWidth+EG.getSize(this.dCollapse).outerWidth}else{return this.oWidth}},getItemContainer:function(){return this.dBody},setCollapseAble:function(d){this.collapseAble=d;if(!this.collapseAble){EG.hide(this.dCollapse)}},setCollapseBehavior:function(d){this.collapseBehavior=d;this.refreshCollapseCln(this.collapsed)},refreshCollapseCln:function(f){var d=this.collapseBehavior;var e=false;if(f){switch(this.collapseBehavior){case"top":d="bottom";break;case"right":d="left";e=true;break;case"bottom":d="top";break;case"left":d="right";e=true;break}}EG.setCls(this.dCollapse,["dCollapse","dCollapse-"+d],this.cls);if(e){EG.css(this.dTitle,"writing-mode:lr-tb");if(this.dBar){EG.hide(this.dBar)}}else{EG.css(this.dTitle,"writing-mode:;");if(this.dBar){EG.show(this.dBar)}}},collapse:function(e){this.refreshCollapseCln(e);if(e){var d=EG.getSize(this.dBody);EG.hide(this.dBody);this._oWidth=this.oWidth;this._oHeight=this.oHeight;if(EG.$in(this.collapseBehavior,["top","bottom"])){this.oHeight=this.height-d.outerHeight}else{if(EG.$in(this.collapseBehavior,["left","right"])){this.oWidth=20;EG.css(this.dHead,"overflow:hidden;writing-mode:lr-tb;")}}}else{this.oWidth=this._oWidth;this.oHeight=this._oHeight;EG.css(this.dHead,"height:auto;")}this.collapsed=e;if(this.pItem){this.pItem.render()}},setTitle:function(d){this.title=d;this.dTitle.innerHTML=this.title},setInnerHeight:function(e){a.pack({pEle:this.dBody,height:e});var d=a.pack({pEle:this.element,height:EG.getSize(this.dBody).outerHeight+EG.getSize(this.dHead).outerHeight});this.height=d.height},getInnerHeight:function(){return EG.getSize(this.element).innerHeight-EG.getSize(this.dHead).outerHeight}}})})();(function(){EG.define("EG.ui.TabPanel",["EG.ui.Container","EG.ui.Item","EG.ui.Tab","EG.ui.Panel",],function(b,c,a,e,d){return{alias:"tabPanel",extend:b,config:{runOnSelectOnBuild:true,cls:"eg_tabPanel",direct:"top",isShowTabs:true,tabsStyle:null,tabWidthLR:90},constructor:function(f){this.curIdx=-1;this.panels=[];this.tabs=[];this.callSuper([f])},afterBuild:function(){if(this.itemsConfig&&this.itemsConfig.length>0){this.addItem(this.itemsConfig,true);this.select(0)}},build:function(){this.element=EG.CE({tn:"div",cn:[this.dPanels=EG.CE({tn:"div",cls:this.cls+"-panels"+this.direct}),this.dTabs=EG.CE({tn:"div",cls:this.cls+"-tabs"+this.direct})]});if(!this.isShowTabs){this.hideTabs(false)}},showTabs:function(f){if(f==null){f=true}EG.show(this.dTabs);if(f){this.render()}},hideTabs:function(f){if(f==null){f=true}EG.hide(this.dTabs);if(f){this.render()}},render:function(){c.fixBody(this.element);c.fit(this);var g=EG.getSize(this.element);var f={outerHeight:0,outerWidth:0};var h=EG.getSize(this.dPanels);if(this.direct=="top"||this.direct=="bottom"){if(this.showTabs){c.fit({element:this.dTabs,dSize:{width:"100%"},pSize:{width:g.innerWidth},type:"width"});EG.css(this.dTabs,"position:absolute;"+this.direct+":0px;left:0px");f=EG.getSize(this.dTabs)}var i=f.outerHeight-(this.direct=="top"?h.borderTop:h.borderBottom);c.fit({element:this.dPanels,pSize:{width:g.innerWidth,height:g.innerHeight-f.outerHeight}});EG.css(this.dPanels,"position:absolute;"+this.direct+":"+i+"px;left:0px")}else{if(this.direct=="left"||this.direct=="right"){if(this.showTabs){c.fit({element:this.dTabs,dSize:{height:g.innerHeight},pSize:g,type:"height"});EG.css(this.dTabs,"position:absolute;"+this.direct+":0px;top:0px");f=EG.getSize(this.dTabs)}var i=f.outerWidth-(this.direct=="left"?h.borderLeft:h.borderRight);c.fit({element:this.dPanels,pSize:{width:g.innerWidth-i,height:g.innerHeight}});EG.css(this.dPanels,"position:absolute;"+this.direct+":"+i+"px;top:0px")}}this.doLayout()},addItem:function(n,g,j){if(g==null){g=true}if(typeof(j)=="undefined"){j=-1}var k=this.items.length==0;var m=(!EG.isArray(n))?[n]:n;var h,f,o;for(var l=0,p=m.length;l<p;l++){n=m[l];if(n.panel["width"]==null){n.panel["width"]="100%"}if(n.panel["height"]==null){n.panel["height"]="100%"}n.tab["clsPre"]=this.cls+"-tabs"+this.direct;n.panel["className"]=this.cls+"-panels-panel";h=new a(this,n.tab);f=new e(n.panel);this.tabs.push(h);this.panels.push(f);h.pItem=this;f.pItem=this;this.dTabs.appendChild(h.getElement());this.dPanels.appendChild(f.getElement());this.items.push(f);if(l==0){o=h}}if(k&&g&&EG.DOM.contains(this.element)){this.render()}if(j!==null){this.select(j)}return h},close:function(f){var h;if(f instanceof a){h=f;f=this.getTabIdx(h)}else{h=EG.Array.get(this.tabs,f)}var j=h.getPanel();var g=(f==this.curIdx);var i=this.tabs[this.curIdx];h.destroy();j.destroy();this.dTabs.removeChild(h.element);this.dPanels.removeChild(j.getElement());EG.Array.del(this.items,f);EG.Array.del(this.tabs,f);EG.Array.del(this.panels,f);this.curIdx=EG.Array.getIdx(this.tabs,i);if(!g){return}if(f>=this.panels.length){f=this.panels.length-1}if(f>=0){this.select(f)}},closeAll:function(){for(var f=this.tabs.length-1;f>=0;f--){this.close(f)}},doSelect:function(m,h){var f=-1;for(var k=0,g=this.panels.length;k<g;k++){var n=this.panels[k];var j=this.tabs[k];if(j!=m){EG.setCls(j.element,"tab",this.cls+"-tabs"+this.direct);EG.hide(n.getElement())}else{f=k}}EG.setCls(this.tabs[f].element,["tab","selected"],this.cls+"-tabs"+this.direct);EG.show(this.panels[f].getElement());if(!h){this.panels[f].render()}this.curIdx=f},select:function(f){var g=(f instanceof a)?f:EG.Array.get(this.tabs,f);g.select()},getTabs:function(){return this.tabs},getPanels:function(){return this.panels},getPanel:function(f){return EG.Array.get(this.panels,f)},getPanelIdx:function(f){return EG.Array.getIdx(this.panels,f)},getTab:function(f){return EG.Array.get(this.tabs,f)},getTabIdx:function(g){for(var f=0;f<this.tabs.length;f++){if(this.tabs[f]==g){return f}}},getSelectedIdx:function(){return this.curIdx},getSelectedTab:function(){return this.getTab(this.curIdx)},getLength:function(){return this.items.length},doLayout:function(){if(!this.element.parentNode){return}var f=this.getSelectedIdx();if(f<0){return}var g=this.panels[f];if(g==null){return}g.pSize=EG.getSize(this.dPanels);g.render()},destroy:function(){for(var f=0;f<this.tabs.length;f++){this.tabs[f].destroy()}for(var f=0;f<this.panels.length;f++){this.panels[f].destroy()}this.tabs=null;this.panels=null;this.items=null}}})})();(function(){EG.define("EG.ui.Tab",["EG.ui.Item"],function(a,b){return{alias:"tab",extend:a,config:{title:"选项卡",closeable:false,onclick:null,onselect:null,onclose:null,afterselect:null,clsPre:null,tabIndex:false,onfocus:null},constructor:function(d,c){this.pTabPanel=d;this.callSuper([c]);this.setCloseable(this.closeable)},build:function(){var c=this;this.element=EG.CE({tn:"div",cls:this.clsPre+"-tab "+this.clsPre+"-selected",item:this,cn:[this.dTitle=EG.CE({tn:"div",cls:this.pTabPanel.cls+"-tabs-tab-title",innerHTML:this.title}),this.dCloser=EG.CE({tn:"a",cls:this.pTabPanel.cls+"-tabs-tab-closer",item:this,onclick:b._events.dCloser.onclick,onmouseover:b._events.dCloser.onmouseover,onmouseout:b._events.dCloser.onmouseout})],onclick:b._events.element.onclick,onkeydown:function(d){d=EG.Event.getEvent(d);if(d.keyCode==13){c.doClick()}}});if(typeof(this.tabIndex)=="number"){this.element.tabIndex=this.tabIndex}},setCloseable:function(c){this.closeable=c;if(this.closeable){EG.show(this.dCloser)}else{EG.hide(this.dCloser)}},doClick:function(){if(this.onclick){this.onclick.apply(this)}this.select()},setTitle:function(c){this.dTitle.innerHTML=c},getIdx:function(){return this.pTabPanel.getTabIdx(this)},select:function(){if(this.onselect){this.onselect.apply(this)}this.pTabPanel.doSelect(this);if(this.afterselect){this.afterselect.apply(this)}},isSelected:function(){return this.pTabPanel.curIdx==this.getIdx()},close:function(){if(this.onclose){this.onclose.apply(this)}this.pTabPanel.close(this)},getPanel:function(){var c=this.getIdx();return this.pTabPanel.getPanel(c)},statics:{_events:{element:{onclick:function(d){var c=this.item;c.doClick();EG.Event.stopPropagation(d)}},dCloser:{onclick:function(d){var c=this.item;c.close();EG.Event.stopPropagation(d)},onmouseover:function(){var c=this.item;EG.setCls(this,["tabs-tab-closer","tabs-tab-closer_on"],c.pTabPanel.cls)},onmouseout:function(){var c=this.item;EG.setCls(this,["tabs-tab-closer"],c.pTabPanel.cls)}}}}}})})();(function(){EG.define("EG.ui.Tree",["EG.ui.Item","EG.ui.TreeNode"],function(a,c,b){return{alias:"tree",extend:a,config:{rootTitle:"根目录",multiple:false,usebox:false,dragable:false,cls:"eg_tree",ondrag:null,deSelectable:true,onclick:null,nodesConfig:null},constructor:function(d){b.load();this.callSuper([d]);if(this.multiple){this.usebox=true}this.rootNode=new c({title:this.rootTitle,root:true,onclick:this.onclick,onclickSrc:this.onclickSrc},this);this.rootNode.setClassNamePre(this.cls+"-");if(!this.dragable){EG.hide(this.rootNode.dInsert)}EG.CE({pn:this.element,ele:this.rootNode.getElement(),style:"margin-left:0px"});if(!this.box){EG.hide(this.rootNode.box.getElement())}this.reset();if(this.nodesConfig){this.rootNode.setNodes(this.nodesConfig)}},setNodes:function(d,f){if(f==null){f=this.rootNode}for(var e=0;e<d.length;e++){f.add(new c(d[e]))}},build:function(){this.element=EG.CE({tn:"div",cls:this.cls,unselectable:"on",item:this,onmousemove:function(d){d=EG.Event.getEvent(d);if(b.curDrag!=null&&b.curDrag.style.display!="none"){EG.css(b.curDrag,"top:"+(d.clientY+10)+"px;left:"+(d.clientX+10)+"px;")}},onselectstart:function(){return false}})},reset:function(){this.treeNodes=[];if(this.rootNode.selected){this.rootNode.blur()}this.selectedNode=null;this.selectedNodes=[];EG.DOM.removeChilds(this.rootNode.dChildNodes)},render:function(){a.fit(this)},add:function(d){c.prototype.add.apply(this.rootNode,arguments)},appendChild:function(d){d.tree=this;this.childNodes.push(d)},getSelected:function(){return this.multiple?this.selectedNodes:this.selectedNode},setSelected:function(g){if(g==null){g=[]}else{if(!EG.Array.isArray(g)){g=[g]}}for(var e=0;e<this.treeNodes.length;e++){var h=this.treeNodes[e];var f=false;for(var d=0;d<g.length;d++){if(!f&&h.value===g[d]){f=true;break}}h.select(f)}},setValue:function(d){this.setSelected(d)},getElement:function(){return this.element},getRootNode:function(){return this.rootNode},removeAll:function(){this.rootNode.removeChilds()},registNode:function(d){this.treeNodes.push(d);d.tree=this;d.setClassNamePre(this.cls+"-")},ungistNode:function(d){EG.Array.remove(this.treeNodes,d)},expandAll:function(){for(var e=0,d=this.treeNodes.length;e<d;e++){this.treeNodes[e].expand()}},collapseAll:function(){for(var e=0,d=this.treeNodes.length;e<d;e++){this.treeNodes[e].collapse()}},expandLv:function(d){this.collapseAll();var e=function(h,j){h.expand();if(j>=d){return}j++;if(h.childNodes.length>0){for(var g=0,f=h.childNodes.length;g<f;g++){e(h.childNodes[g],(j))}}};e(this.getRootNode(),1)},deSelect:function(){if(this.multiple){if(!this.selectedNodes){return}for(var d=this.selectedNodes.length-1;d>=0;d--){this.selectedNodes[d].select(false)}}else{if(!this.selectedNode){return}this.selectedNode.select(false)}},expandChain:function(d){this.collapseAll();while((d=d.parentNode)!=null){d.expand()}},getValue:function(){var e=tree.getSelected();if(this.multiple){var f=[];for(var d=0;d<e.length;d++){f.push(e[d].value)}return f}else{return e.value}},statics:{dragThread:null,curXY:null,curDrag:null,isDraging:function(){return b.curDrag!=null&&!EG.Style.isHide(b.curDrag)},loaded:false,load:function(){if(b.loaded){return}EG.bindEvent(EG.doc,"mouseup",function(){if(b.dragThread!=null){window.clearTimeout(b.dragThread)}if(b.curDrag){EG.hide(b.curDrag)}});b.loaded=true}}}})})();(function(){EG.define("EG.ui.TreeNode",["EG.ui.Item","EG.ui.Box","EG.ui.Tree"],function(b,a,d,c){return{alias:"treeNode",extend:b,config:{title:"节点",value:null,showbox:false,root:false,onclick:null,ondrag:null,tree:null,cls:"eg_tree",titleStyle:null},constructor:function(f,e){this.callSuper([f]);this.tree=e||this.tree;this.parentNode=null;this.childNodes=[];this.ondrag=this.ondrag||(this.tree!=null?this.tree.ondrag:null);var g=this;this.box=new a({showText:false,style:"display:inline",onclick:function(){g.select(!g.selected)}});this.dNode.appendChild(this.box.getElement());EG.css(this.box.getElement().childNodes[0],"margin-left:3px")},statics:{_events:{dExpandBtn:{onclick:function(){var e=this.me;e.changeCollapsed()}},dTitle:{ondblclick:function(){var e=this.me;e.changeCollapsed()},onclick:function(){var e=this.me;e.select(e.tree.deSelectable?!e.box.selected:true);if(e.onclick){e.onclick.apply(e.onclickSrc||e)}},onmouseup:function(){var e=this.me;if(d.isDraging()){d.curDrag.node.moveto("in",e)}},onmousedown:function(g){var f=this.me;if(!f.tree.dragable||f==f.tree.rootNode){return}g=EG.Event.getEvent(g);d.curXY=[g.clientX,g.clientY];var e=f;d.dragThread=window.setTimeout(function(){if(d.curDrag==null){d.curDrag=EG.CE({pn:EG.getBody(),tn:"div",cls:"eg_drager"})}d.curDrag.node=e;d.curDrag.innerHTML=e.title;EG.show(d.curDrag);EG.css(d.curDrag,"top:"+(d.curXY[1]+10)+"px;left:"+(d.curXY[0]+10)+"px;")},300)}},dInsert:{onmouseover:function(){var e=this.me;if(e==e.tree.rootNode){return}if(d.isDraging()){EG.css(this,"background-color:red;margin-left:7px;")}},onmouseout:function(){EG.css(this,"background-color:;margin-left:;")},onmouseup:function(){var e=this.me;if(e==e.tree.rootNode){return}if(d.isDraging()){d.curDrag.node.moveto("after",e)}}}}},build:function(){this.element=EG.CE({tn:"div",cn:[this.dNode=EG.CE({tn:"div",cn:[this.dExpandBtn=EG.CE({tn:"div",me:this,onclick:c._events.dExpandBtn.onclick}),this.dTitle=EG.CE({tn:"div",innerHTML:this.title,me:this,ondblclick:c._events.dTitle.ondblclick,onclick:c._events.dTitle.onclick,onmouseup:c._events.dTitle.onmouseup,onmousedown:c._events.dTitle.onmousedown})]}),this.dChildNodes=EG.CE({tn:"div"}),this.dInsert=EG.CE({tn:"div",me:this,onmouseover:c._events.dInsert.onmouseover,onmouseout:c._events.dInsert.onmouseout,onmouseup:c._events.dInsert.onmouseup})]});if(this.titleStyle){EG.css(this.dTitle,this.titleStyle)}},doClick:function(){EG.Event.fireEvent(this.dTitle,"click")},setNodes:function(e,g){this.removeChilds(false);for(var f=0;f<e.length;f++){var h=e[f];if(EG.Object.isLit(h)){h=new c(h)}this.add(h);if(h.nodes){h.setNodes(h.nodes)}}if(g){this.render()}},setTitle:function(e){this.title=e;this.dTitle.innerHTML=e},setValue:function(e){this.value=e},getValue:function(){return this.value},select:function(e){if(e){this.focus();this.box.select(true);if(this.tree.multiple){if(!EG.Array.has(this.tree.selectedNodes,this)){this.tree.selectedNodes.push(this)}}else{if(this.tree.selectedNode!=null&&this.tree.selectedNode!=this){this.tree.selectedNode.select(false)}this.tree.selectedNode=this}}else{this.blur();this.box.select(false);if(this.tree.multiple){EG.Array.remove(this.tree.selectedNodes,this)}else{this.tree.selectedNode=null}}this.selected=e},setClassNamePre:function(e){this.element.className=e+"node";this.dNode.className=e+"node-dNode";this.dExpandBtn.className=e+"node-dNode-dExpandBtn";this.dTitle.className=e+"node-dNode-dTitle";this.dChildNodes.className=e+"node-dChildNodes";this.dInsert.className=e+"node-dInsert"},blur:function(){EG.Style.removeCls(this.dTitle,this.cls+"-node-dNode-selected")},focus:function(){EG.Style.addCls(this.dTitle,this.cls+"-node-dNode-selected")},isCollapsed:function(){return EG.Style.isHide(this.dChildNodes)},changeCollapsed:function(){if(this.isCollapsed()){this.expand()}else{this.collapse()}},expand:function(g){EG.show(this.dChildNodes);if(g){var h=[];var f=this;while(f.parentNode){h.push(f);f=f.parentNode}for(var e=h.length-1;e>=0;e--){h[e].expand()}}this.refreshCollapseElement()},collapse:function(){EG.hide(this.dChildNodes);this.refreshCollapseElement()},preNode:function(){var f=this.parentNode.childNodes;for(var e=0;e<f.length;e++){if(f[e]==this){return(e==0)?null:f[e-1]}}return null},nextNode:function(){var f=this.parentNode.childNodes;for(var e=0;e<f.length;e++){if(f[e]==this){return(e>=f.length-1)?null:f[e+1]}}return null},add:function(g,e){if(EG.Object.isLit(g)){g=new c(g)}if(e==null){e=this.childNodes.length-1;this.childNodes.push(g);this.dChildNodes.appendChild(g.getElement())}else{this.childNodes=EG.Array.insert(this.childNodes,e+1,g);if(this.dChildNodes.childNodes.length>e+1){var f=this.dChildNodes.childNodes[e+1];this.dChildNodes.insertBefore(g.getElement(),f)}else{this.dChildNodes.appendChild(g.getElement())}}this.tree.registNode(g);g.parentNode=this;this.refreshCollapseElement();g.refreshCollapseElement();if(this.childNodes.length>1){this.childNodes[this.childNodes.length-2].refreshCollapseElement()}if(!this.tree.multiple){EG.hide(g.box.getElement())}},remove:function(f){if(f==null){f=true}this.removeChilds();var e=this.preNode();this.tree.ungistNode(this);EG.Array.remove(this.parentNode.childNodes,this);this.parentNode.dChildNodes.removeChild(this.getElement());if(f){this.parentNode.refreshCollapseElement();if(e){e.refreshCollapseElement()}}},removeChilds:function(f){if(f==null){f=true}for(var e=this.childNodes.length-1;e>=0;e--){this.childNodes[e].remove(false)}if(f){this.refreshCollapseElement()}},refreshCollapseElement:function(){EG.DOM.removeChilds(this.dExpandBtn);var f,e;if(this.isLeaf()){f=this.cls+"-node-l-"+(this.isLast()?"l":"t");e=this.cls+"-node-file"}else{if(this.isCollapsed()){f=this.cls+"-node-l-"+(this.isLast()?"lPlus":"tPlus");e=this.cls+"-node-folder"}else{f=this.cls+"-node-l-"+(this.isLast()?"lMinus":"tMinus");e=this.cls+"-node-openfolder";if(!this.isLast()){EG.setCls(this.dChildNodes,["node-dChildNodes","node-bgLine"],this.cls)}else{EG.setCls(this.dChildNodes,["node-dChildNodes"],this.cls)}}}if(!this.isRoot()){EG.CE({pn:this.dExpandBtn,tn:"div",cls:f})}EG.CE({pn:this.dExpandBtn,tn:"div",cls:e});if(!this.isLast()){EG.setCls(this.dInsert,["node-dInsert","node-bgLine"],this.cls)}else{EG.setCls(this.dInsert,["node-dInsert"],this.cls)}if(this.parentNode&&this.parentNode.isRoot()){EG.css(this.element,"margin-left:0px")}else{EG.css(this.element,"margin-left:;")}},isLast:function(){if(this.parentNode==null){return true}return this.parentNode.childNodes[this.parentNode.childNodes.length-1]==this},isFirst:function(){return this.parentNode.childNodes[0]==this},isLeaf:function(){return this.childNodes.length==0},isRoot:function(){return this.root},getIdx:function(){var g=this.parentNode.childNodes;for(var f=0,e=g.length;f<e;f++){if(g[f]==this){return f}}},moveto:function(i,h){if(this==h){return}if(this.ondrag){if(this.ondrag.apply(this["ondragSrc"]||this,[this,i,h])===false){return}}var k=this.parentNode;var f=this.preNode(this);var g=this.nextNode(this);var m=null;var l=-1;var n=this.getIdx();if(i=="after"){l=h.getIdx();m=h.parentNode}else{if(i=="in"){m=h}}var j=false;var e=m;while((e=e.parentNode)&&e!=null&&e!=this.tree.rootNode){if(e==this){j=true;break}}if(j){throw new Error("不能移动到子节点")}EG.Array.remove(k.childNodes,this);if(l<0){m.add(this)}else{if(k==m){if(n<l){l--}}m.add(this,l)}k.refreshCollapseElement();if(f){f.refreshCollapseElement()}if(g){g.refreshCollapseElement()}if(this.parentNode.isRoot()){EG.css(this.element,"margin-left:0px")}this.refreshCollapseElement()}}})})();(function(){EG.define("EG.ui.Table",["EG.ui.Item","EG.ui.Dialog","EG.ui.Form","EG.ui.Button"],function(d,b,c,a,e){return{extend:d,config:{cls:"eg_table",title:null,rowCount:3,colCount:3,afterSelect:null},constructor:function(f){this.callSuper([f])},rebindTable:function(h){var m=this;this.element.replaceChild(h,this.dTable);this.dTable=h;this.dBody=h.getElementsByTagName("tbody")[0];EG.Event.bindEvent(this.dTable,ondragstart,function(){m.refreshSelect()});EG.Event.bindEvent(this.dTable,onmouseup,function(){m.selecting=false;if(m.afterSelect){m.afterSelect.apply(m)}});var f=this.dBody.childNodes;for(var k=0;k<f.length;k++){var l=f[k].childNodes;for(var g=0;g<l.length;g++){var o=l[g];var n=o.getAttribute("pos").split(",");EG.CE({ele:o,r:n[0],c:n[1],table:this,onmousedown:this.tdEvent.onmousedown,onmouseover:this.tdEvent.onmouseover,oncontextmenu:this.tdEvent.oncontextmenu})}}},build:function(){var f=this;this.element=EG.CE({tn:"div",cls:this.cls,cn:[this.dTable=EG.CE({tn:"table",cls:this.cls+"-table",border:1,cn:[this.dBody=EG.CE({tn:"tbody"})]}),this.dSelectTop=EG.CE({tn:"div",cls:this.cls+"-selectTop"}),this.dSelectRight=EG.CE({tn:"div",cls:this.cls+"-selectRight"}),this.dSelectBottom=EG.CE({tn:"div",cls:this.cls+"-selectBottom"}),this.dSelectLeft=EG.CE({tn:"div",cls:this.cls+"-selectLeft"})],ondragstart:function(){f.refreshSelect()},onmouseup:function(){f.selecting=false;if(f.afterSelect){f.afterSelect.apply(f)}}});EG.hide(this.dSelectTop,this.dSelectRight,this.dSelectBottom,this.dSelectLeft);this.buildRowCol(this.rowCount,this.colCount);this.dMenu=EG.CE({pn:this.getElement(),cls:this.cls+"-dMenu",tn:"div",cn:[{tn:"a",innerHTML:"属性",onclick:function(){f.openPFCol();EG.hide(f.dMenu)}},{tn:"a",innerHTML:"合并",onclick:function(){f.merge();EG.hide(f.dMenu)}},{tn:"a",innerHTML:"取消合并",onclick:function(){f.broke();EG.hide(f.dMenu)}},{tn:"a",innerHTML:"左侧插入一列",onclick:function(){EG.hide(f.dMenu)}},{tn:"a",innerHTML:"右侧插入一列",onclick:function(){EG.hide(f.dMenu)}},{tn:"a",innerHTML:"上方插入一行",onclick:function(){EG.hide(f.dMenu)}},{tn:"a",innerHTML:"下方插入一行",onclick:function(){EG.hide(f.dMenu)}},{tn:"a",innerHTML:"删除所在列",onclick:function(){EG.hide(f.dMenu)}},{tn:"a",innerHTML:"删除所在行",onclick:function(){EG.hide(f.dMenu)}},{tn:"a",innerHTML:"清除",onclick:function(){EG.hide(f.dMenu)}},{tn:"a",innerHTML:"表格属性",onclick:function(){f.openPFTable();EG.hide(f.dMenu)}}]});EG.hide(this.dMenu)},tdEvent:{onmousedown:function(g){var f=this.table;g=EG.Event.getEvent(g);if(g.button!=0){return}EG.hide(f.dMenu);f.selecting=true;f.startTd=this;f.endTd=this;EG.show(f.dSelectTop,f.dSelectRight,f.dSelectBottom,f.dSelectLeft);f.refreshSelect()},onmouseover:function(g){var f=this.table;if(!f.selecting){return}f.endTd=this;f.refreshSelect()},oncontextmenu:function(f){var g=this.table;if(!g.startTd){return}g.showMenu(f,g.node)}},showMenu:function(g){var f=EG.Tools.getMousePos(g,this.getElement());EG.css(this.dMenu,{top:f.y+"px",left:f.x+"px"});EG.show(this.dMenu);EG.Event.stopPropagation(g)},refreshSelect:function(g){if(!g&&!this.selecting){return}else{if(!this.endTd){return}}var f={x:this.startTd.offsetLeft-this.dTable.offsetLeft,y:this.startTd.offsetTop-this.dTable.offsetTop};var k={x:this.endTd.offsetLeft-this.dTable.offsetLeft,y:this.endTd.offsetTop-this.dTable.offsetTop};var n,m,o,j;var i=Math.min(f.x,k.x);var p=Math.min(f.y,k.y);var o=Math.max((f.x+this.startTd.clientWidth),(k.x+this.endTd.clientWidth))-i;var j=Math.max((f.y+this.startTd.clientHeight),(k.y+this.endTd.clientHeight))-p;EG.css(this.dSelectTop,{left:i+"px",top:p+"px",width:o+"px"});EG.css(this.dSelectRight,{left:(i+o)+"px",top:p+"px",height:j+"px"});EG.css(this.dSelectBottom,{left:i+"px",top:(p+j)+"px",width:o+"px"});EG.css(this.dSelectLeft,{left:i+"px",top:p+"px",height:j+"px"})},clearSelect:function(){EG.hide(this.dSelectTop,this.dSelectRight,this.dSelectBottom,this.dSelectLeft)},merge:function(){if(this.startTd==this.endTd){return}var g=Math.min(this.startTd.r,this.endTd.r);var n=Math.min(this.startTd.c,this.endTd.c);var f=Math.max(this.startTd.r,this.endTd.r);var m=Math.max(this.startTd.c,this.endTd.c);var k=null;var q=[];for(var p=g;p<=f;p++){var o=this.dBody.childNodes[p].childNodes;for(var l=0;l<o.length;l++){var h=o[l];if(h.c==n&&h.r==g){k=h}else{if(h.c>=n&&h.c<=m){q.push(h);if((h.colSpan&&h.colSpan>1)||(h.rowSpan&&h.rowSpan>1)){EG.Locker.message("待合并中有已合并的单元格");return}}}}}for(var p=0;p<q.length;p++){q[p].parentNode.removeChild(q[p])}k.colSpan=m-n+1;k.rowSpan=f-g+1;this.startTd=k;this.endTd=k;this.refreshSelect(true);this.collect()},collect:function(){var f=this.dBody.childNodes;var g=false;for(var k=f.length-1;k>=0;k--){if(f[k].childNodes.length==0){this.dBody.removeChild(f[k]);g=true}}f=this.dBody.childNodes;for(var k=0;k<f.length;k++){var l=f[k].childNodes;f[k]=k;for(var h=0;h<l.length;h++){l[h].r=k;if(l.length==1){l[h].rowSpan=""}}}},broke:function(){var x=Math.min(this.startTd.r,this.endTd.r);var r=Math.min(this.startTd.c,this.endTd.c);var u=Math.max(this.startTd.r,this.endTd.r);var q=Math.max(this.startTd.c,this.endTd.c);var h=null;var m=[];for(var v=x;v<=u;v++){var w=this.dBody.childNodes[v].childNodes;for(var t=0;t<w.length;t++){var o=w[t];if(o.c>=r&&o.c<=q){if((o.colSpan&&o.colSpan>1)||(o.rowSpan&&o.rowSpan>1)){m.push(o)}}}}for(var v=0;v<m.length;v++){var A=m[v];var n=A.rowSpan?A.rowSpan:1;var g=A.colSpan?A.colSpan:1;var y=A.r;var l=A.c;var z=A.parentNode;for(var t=0;t<=n-1;t++){var f=z;for(var s=0;s<t;s++){f=f.nextSibling}var w=f.childNodes;var p=null;for(var s=0;s<w.length;s++){if(w[v].c>=l){if(v>=1){p=w[v-1]}else{p=null}}}for(var s=l;s<=(l+g-1);s++){if(s==l&&t==0){}else{var o=EG.CE({pn:f,tn:"td",r:f.r,c:s,table:this,onmousedown:this.tdEvent.onmousedown,onmouseover:this.tdEvent.onmouseover,oncontextmenu:this.tdEvent.oncontextmenu});if(p){EG.DOM.insertAfter(o,p)}else{f.appendChild(o)}p=o}}}A.rowSpan=1;A.colSpan=1}},getTd:function(f,g){return this.dBody.childNodes[f].childNodes[g]},buildRowCol:function(f,k){this.rowCount=f;this.colCount=k;EG.DOM.removeChilds(this.dBody);for(var h=0;h<this.rowCount;h++){var l=EG.CE({pn:this.dBody,tn:"tr",r:h});for(var g=0;g<this.colCount;g++){var m=EG.CE({pn:l,tn:"td",r:h,c:g,table:this,onmousedown:this.tdEvent.onmousedown,onmouseover:this.tdEvent.onmouseover,oncontextmenu:this.tdEvent.oncontextmenu});m.setAttribute("pos",h+","+g)}}},inserCol:function(f,g){},inserRow:function(g,f){},deleteRow:function(f){},deleteCol:function(f){},openPFCol:function(f){if(!this.pfCol){this.buildPFCol()}if(!this.startTd){return}this.colForm.setData(this.getColStyle(this.startTd));this.pfCol.open()},openPFTable:function(f){if(!this.pfTable){this.buildPFTable()}this.tableForm.setData(this.getTableStyle(this.dTable));this.pfTable.open()},getColStyle:function(k){var f={};var j=e.sKey;var h=k.style;if(h){for(var g=0;g<j.length;g++){if(h[j[g]]!=null){f["td_"+j[g]]=h[j[g]]}}}f.td_inner=k.itemCfg?"":k.innerHTML;return f},getTableStyle:function(h){var f={};var g=h.style;if(g){if(g.borderColor!=null){f["table_border-color"]=g.borderColor}if(g.borderWidth!=null){f["table_border-width"]=g.borderWidth}}if(h.cellSpacing!=null){f.table_cellSpacing=h.cellSpacing}if(h.cellPadding!=null){f.table_cellPadding=h.cellPadding}return f},setColStyle:function(l,g,j){var h={};var k=e.sKey;for(var f=0;f<k.length;f++){h[k[f]]=g["td_"+k[f]]}EG.css(l,h);l.innerHTML=EG.unnull(g.td_inner,"");this.refreshSelect(true)},setTableStyle:function(g,f,h){if(!EG.isBlank(f["table_border-color"])){EG.css(g,"border-color:"+f["table_border-color"])}if(!EG.isBlank(f["table_border-width"])){EG.css(g,"border-width:"+f["table_border-width"])}if(!EG.isBlank(f.table_cellSpacing)){g.cellSpacing=f.table_cellSpacing}if(!EG.isBlank(f.table_cellPadding)){g.cellPadding=f.table_cellPadding}this.refreshSelect(true)},btn_events:{col:{save:function(){var g=this.table;if(!g.startTd){return}var f=g.colForm.getData();g.setColStyle(g.startTd,f)},cancel:function(){var f=this.table;f.pfCol.close()}},table:{save:function(){var g=this.table;var f=g.tableForm.getData();g.setTableStyle(g.dTable,f)},cancel:function(){var f=this.table;f.pfTable.close()}}},buildPFCol:function(){this.pfCol=new b({title:"单元格",layout:"border",width:300,height:"auto",lock:true,items:[this.colForm=new c({region:"center",layout:"table",height:"auto",width:"100%",items:[{xtype:"formItem",pos:[1,0],title:"内容",name:"td_inner",type:"textarea",height:40},{xtype:"formItem",pos:[2,0],title:"对齐",name:"td_text-align",type:"boxGroup",textvalues:[["左","left"],["中","center"],["右","right"],["无",""]]},{xtype:"formItem",pos:[3,0],title:"宽度",name:"td_width",type:"text"},{xtype:"formItem",pos:[3,1],title:"高度",name:"td_height",type:"text"},{xtype:"formItem",pos:[4,0],title:"字号",name:"td_font-size",type:"text"},{xtype:"formItem",pos:[4,1],title:"颜色",name:"td_color",type:"text"},{xtype:"formItem",pos:[5,0],title:"加重",name:"td_font-weight",type:"boxGroup",textvalues:[["是","bolder"],["否",""]]},{xtype:"formItem",pos:[5,1],title:"倾斜",name:"td_font-style",type:"boxGroup",textvalues:[["是","italic"],["否",""]]}]})],btns:[new a({text:"保存",click:this.btn_events.col.save,cls:"eg_button_small",table:this}),new a({text:"取消",click:this.btn_events.col.cancel,cls:"eg_button_small",table:this})],renderTo:this.getElement()})},buildPFTable:function(){this.pfTable=new b({title:"表格",layout:"border",width:300,height:"auto",lock:true,items:[this.tableForm=new c({region:"center",layout:"table",height:"auto",width:"100%",items:[{pos:[1,0],title:"边色",name:"table_border-color",type:"text"},{pos:[1,1],title:"边宽",name:"table_border-width",type:"text"},{pos:[2,0],title:"缝隙",name:"table_cellSpacing",type:"text"},{pos:[2,1],title:"间距",name:"table_cellPadding",type:"text"}]})],btns:[new a({text:"保存",click:this.btn_events.table.save,cls:"eg_button_small",table:this}),new a({text:"取消",click:this.btn_events.table.cancel,cls:"eg_button_small",table:this})],renderTo:this.getElement()})},render:function(){d.fit({element:this.element});if(this.pfCol){this.pfCol.render()}},statics:{sKey:["text-align","width","height","font-size","color","font-weight","font-style"]}}})})();(function(){EG.define("EG.ui.Box",["EG.ui.Item"],function(a,b){return{extend:a,config:{text:"",value:"",cls:"eg_box",onselect:null,onclick:null,afterselect:null,selected:false,showText:true},constructor:function(c){this.callSuper([c]);if(!this.showText){EG.hide(this.dText);EG.css(this.dBox,"display:block;")}},build:function(){this.element=EG.CE({tn:"div",cls:this.cls,item:this,onclick:b._events.element.onclick,cn:[this.dBox=EG.CE({tn:"a",cls:this.cls+"-b"}),this.dText=EG.CE({tn:"div",cls:this.cls+"-text",innerHTML:this.text})]})},select:function(d,c){if(c==null){c=true}if(c&&this.onselect){if(this.onselect.apply(this,[d])===false){return}}this.selected=d;EG.setCls(this.dBox,["b",this.selected?"select":"unselect"],this.cls);if(c&&this.afterselect){this.afterselect.apply(this,[d])}},deSelect:function(){this.selected=null;EG.setCls(this.dBox,["b","unselect"],this.cls)},setValue:function(c){this.value=c},getValue:function(){return this.value},render:function(){a.fit(this);var d=EG.getSize(this.element).innerHeight;var c=parseInt((d-EG.getSize(this.dBox).outerHeight)/2);EG.css(this.dBox,"margin-top:"+c+"px;margin-bottom:"+c+"px");EG.css(this.dText,"line-height:"+d+"px;height:"+d+"px")},statics:{_events:{element:{onclick:function(){var c=this.item;if(c.onclick){c.onclick.apply(c)}else{c.select(!c.selected)}}}}}}})})();(function(){EG.define("EG.ui.BoxGroup",["EG.ui.Item"],function(a,b){return{alias:"box",extend:a,config:{multiple:false,onselect:null,onclick:null,onchange:null,afterselect:null,textvalues:[],cls:"eg_boxgroup",defValue:null,boxHeight:null,boxStyle:null},constructor:function(c){this.callSuper([c]);this.boxes=[];this.onchangeEvents=[];if(this.onchange!=null){this.bindOnchange(this.onchange)}this.buildBoxes();EG.ui.FormItem.bindValidate.apply(this,[]);if(this.defValue){this.setValue(this.defValue)}},build:function(){this.element=EG.CE({tn:"div",cls:this.cls})},buildBoxes:function(){var f=this;for(var e=0,c=this.textvalues.length;e<c;e++){var d=this.textvalues[e];this.add({text:d[0],value:d[1],style:this.boxStyle,onclick:b._events.box.onclick,group:this,onselect:f.onselect,afterselect:f.afterselect})}},setValue:function(e,d){if(d==null){d=true}var c=this.getValue();if(this.multiple){EG.Array.each(this.boxes,function(){this.select(EG.Array.has(e,this.value))})}else{EG.Array.each(this.boxes,function(){this.select(this.value===e)})}if(!EG.Object.equals(c,e)&&d){this.doOnChange(e,c)}},getValue:function(){var d=[];for(var e=0,c=this.boxes.length;e<c;e++){var f=this.boxes[e];if(this.multiple){if(f.selected){d.push(f.value)}}else{if(f.selected){return f.value}}}if(this.multiple){return d}return null},getText:function(){var f=[];for(var d=0,c=this.boxes.length;d<c;d++){var e=this.boxes[d];if(this.multiple){if(e.selected){f.push(e.text)}}else{if(e.selected){return e.text}}}if(this.multiple){return f}return null},getSelectedBox:function(){var d=[];for(var e=0,c=this.boxes.length;e<c;e++){var f=this.boxes[e];if(this.multiple){if(f.selected){d.push(f)}}else{if(f.selected){return f}}}if(this.multiple){return d}return null},add:function(c){if(EG.isLit(c)){c=new EG.ui.Box(c)}c.pItem=this;this.boxes.push(c);this.element.appendChild(c.getElement())},getBoxes:function(){return this.boxes},bindOnchange:function(c){this.onchangeEvents.push(c)},doOnChange:function(e,c){for(var d=0;d<this.onchangeEvents.length;d++){this.onchangeEvents[d].apply(this,[e,c])}},render:function(){a.fit(this);var d=EG.getSize(this.element).innerHeight;for(var c=0;c<this.boxes.length;c++){this.boxes[c].height=this.boxHeight!=null?this.boxHeight:d;this.boxes[c].render()}},setTextvalues:function(c){EG.Array.clear(this.boxes);this.textvalues=c;EG.DOM.removeChilds(this.element);this.buildBoxes()},getTitle:function(m,e,h){var k=h.textvalues||[];if(!EG.isArray(m)){m=[m]}var g=[];for(var c=0,f=m.length;c<f;c++){var n=m[c];for(var d=0,l=k.length;d<l;d++){if(k[d][1]==n){g.push(k[d][0]);break}}}return g.join(",")},statics:{_events:{box:{onclick:function(){var f=this.group;var d=this.group.getValue();var h=false;if(f.multiple){this.select(!this.selected);h=true}else{if(f.onclick){f.onclick()}else{if(!this.selected){h=true}for(var e=0,c=f.boxes.length;e<c;e++){if(this!=f.boxes[e]){f.boxes[e].select(false)}}this.select(true)}}var g=this.group.getValue();if(h){for(var e=0;e<f.onchangeEvents.length;e++){f.onchangeEvents[e].apply(f,[g,d])}}}}}}}})})();(function(){EG.define("EG.ui.Date",["EG.ui.Item"],function(a,b){return{alias:"date",extend:a,config:{fmt:"YMDHMS",maxLength:10,dateFmt:null,cls:"eg_date",onkeydown:null,onkeyup:null,placeholder:null},constructor:function(c){this.callSuper([c]);if(this.dateFmt==null){if(this.fmt=="Y"){this.dateFmt="yyyy";this.maxLength=4}else{if(this.fmt=="YMD"){this.dateFmt="yyyy-MM-dd";this.maxLength=10}else{if(this.fmt=="YMDHMS"){this.dateFmt="yyyy-MM-dd HH:mm:ss";this.maxLength=20}else{throw new Error("暂不支持时间格式"+this.fmt)}}}}},build:function(){this.element=EG.CE({tn:"div",cls:this.cls,cn:[this.input=EG.CE({tn:"input",cls:this.cls+"-input",type:"text",maxLength:this.maxLength,length:this.maxLength,placeholder:this.placeholder||" ",item:this,onclick:b._events.input.onclick})]});EG.ui.FormItem.bindValidate.apply(this,[]);if(this.onkeyup){EG.Event.bindEvent(this.input,"onkeyup",this.onkeyup)}},setValue:function(c){EG.setValue(this.input,c)},getValue:function(){return EG.getValue(this.input)},render:function(){a.fit(this);a.fit({element:this.input,dSize:{width:"100%",height:"100%"},pSize:EG.getSize(this.element)})},statics:{_events:{input:{onclick:function(){var c=this.item;new WdatePicker({dateFmt:c.dateFmt,skin:"ext"})}}}}}})})();(function(){EG.define("EG.ui.Editor",["EG.ui.Item"],function(Item,ME){return{alias:"editor",extend:Item,config:{pluginGroupName:"def",cls:"eg_editor",uploadPolicy:null,imgUploadPolicy:null,uploadAction:null,imgUploadAction:null,onUploaded:null,imgOnUploaded:null,uploadHandleType:null,deleteUpload:null,parent:null,imgPickers:null,uploadOnPaste:false,readLineHeight:22},constructor:function(cfg){this.initItem(cfg);this.pluginGroup=ME.pluginGroups[this.pluginGroupName];if(typeof(this.onUploaded)=="string"){this.onUploaded=ME.defOnUploaded}this.dToolbarButtons=null;this.dIframe=null;this.dSource=null;this.dCache=null;this.element=EG.CE({tn:"div",cls:this.cls,cn:[this.dMenuPanels=EG.CE({tn:"div"}),this.dToolbarButtons=EG.CE({tn:"div",cls:this.cls+"-toolbar"}),this.dHtml=EG.CE({tn:"div",cls:this.cls+"-dHtml",cn:[this.frame=EG.CE({tn:"iframe",frameBorder:"0",designMode:"on",style:"height:100%",item:this})]}),this.dCache=EG.CE({tn:"div",style:"display:none"})]});EG.css(this.element,this.style);this.initIframe();this.buildPlugins()},render:function(){Item.fit(this);var size=EG.getSize(this.element);Item.fit({element:this.dToolbarButtons,dSize:{width:"100%"},pSize:size,type:"height"});var tbSize=EG.getSize(this.dToolbarButtons);Item.fit({element:this.dHtml,dSize:{width:"100%",height:size.innerHeight-tbSize.outerHeight},pSize:size});for(var i=0,il=this.plugins.length;i<il;i++){this.plugins[i].render()}},plugins:[],pluginsMap:{},clickHandlers:[],dblclickHandlers:[],buildPlugins:function(){this.buildInParents=[];for(var i=0,il=this.pluginGroup.length;i<il;i++){var name=this.pluginGroup[i];var plugin=new ME.pluginsMap[name](this);this.plugins.push(plugin);this.pluginsMap[name]=plugin;var toolbarButton=plugin.getToolbarButton();if(toolbarButton){this.dToolbarButtons.appendChild(toolbarButton.getElement())}if(plugin.getMenuPanel){var menuPanel=plugin.getMenuPanel();if(menuPanel){if(plugin.buildInParent&&this.parent){var pn=(this.parent.getElement)?this.parent.getElement():this.parent;pn.appendChild(menuPanel.getElement());this.buildInParents.push(menuPanel)}else{this.dMenuPanels.appendChild(menuPanel.getElement())}}}}},getPlugin:function(name){return this.pluginsMap[name]},initIframe:function(){EG.bindEvent(this.frame,"onload",ME._events.iframe.load);if(!EG.Browser.isIE8()){this.frame.src="about:blank"}},setContent:function(content){this.cacheHTML=content;if(this.loaded){this.doc.body.innerHTML=content}},setValue:function(value){var ct=EG.CE({tn:"div",innerHTML:value});var tns=["img","a"];for(var i=0;i<tns.length;i++){var els=ct.getElementsByTagName(tns[i]);for(var j=0;j<els.length;j++){var el=els[j];var uri;var attName="";if(el.hasAttribute("srcUri")){attName="src";uri=el.getAttribute("srcUri")}else{if(el.hasAttribute("hrefUri")){attName="href";uri=el.getAttribute("hrefUri")}else{continue}}el.setAttribute(attName,uri)}}this.setContent(value)},getContent:function(){if(!this.loaded||!this.doc){return EG.n2d(this.cacheHTML,"")}return this.doc.body.innerHTML},getValue:function(){return this.getContent()},focus:function(){if(EG.Browser.isChrome()){this.doc.body.focus()}else{if(EG.Browser.isIE()){this.frame.contentWindow.focus()}else{this.frame.focus()}}},hideMenus:function(){for(var i=0,il=this.plugins.length;i<il;i++){var plugin=this.plugins[i];if(!plugin.getMenuPanel){continue}var menuPanel=plugin.getMenuPanel();if(menuPanel){menuPanel.close()}}},htmlexec:function(type,para){this.focus();if(!para){if(EG.Browser.isIE()){this.doc.execCommand(type,false)}else{this.doc.execCommand(type,false,false)}}else{this.doc.execCommand(type,false,para)}this.focus()},pasteHTML:function(html){this.focus();if(EG.Browser.isIE()){this.getSelection().createRange().pasteHTML(html)}else{this.doc.execCommand("InsertHtml",false,html)}},getSelection:function(){if(EG.Browser.isIE()){return this.doc.selection}return this.doc.getSelection()},addResOnUploaded:function(cfg){var me=this;var ct=cfg.ct;var r=cfg.r;var type=r.type;var file=r.file;var doDelete=r.doDelete;var el=EG.CE({tn:"div",cls:this.cls+"-uploadLabel",style:EG.Style.c.dv+";min-width:40px;max-width:200px",onmouseover:ME._events.dUploadLabel.onmouseover,onmouseout:ME._events.dUploadLabel.onmouseout,cn:[{tn:"div",onclick:function(){var p;if(type=="image"){p=me.getPlugin("image");p.showMenuPanel();p.setImageForm({src:file.path})}else{if(type=="video"){p=me.getPlugin("video");p.showMenuPanel();p.setVideoForm(file)}else{if(type=="zip"){p=me.getPlugin("zip");p.showMenuPanel();p.setZipForm(file)}}}},innerHTML:file.name,style:EG.Style.c.dv+";cursor:pointer"},{tn:"a",cls:this.cls+"-uploadLabel-closer",onclick:function(){var ma=this;doDelete(function(){ma.parentNode.parentNode.removeChild(ma.parentNode)})}}]});ct.appendChild(el)},destroy:function(){for(var i=0;i<this.buildInParents.length;i++){this.buildInParents[i].destroy();EG.DOM.remove(this.buildInParents[i].getElement())}},execute:function(name,args){var p=this.getPlugin(name);p.execute(args)},statics:{def:{fontSize:"14px"},pluginsMap:{},pluginGroups:{def:["bold","italic","underline","fontname","fontsize","color","textalign","list","indent","image","code","full","program"],simple:["bold","italic","underline","fontname","fontsize","color","textalign","list","indent"]},registPlugin:function(name,plugin){ME.pluginsMap[name]=plugin},_events:{iframe:{load:function(){var me=this.item;me.doc=this.contentDocument||this.contentWindow.document;var fn1=function(){if(me.loaded){return}me.doc.body.designMode="on";me.doc.body.contentEditable=true;me.doc.body.style.fontSize=ME.def.fontSize;EG.css(me.doc.body,"line-height:1.5;margin:0; padding:8px 8px;font-size:"+ME.def.fontSize);if(me.cacheHTML!=null){me.doc.body.innerHTML=me.cacheHTML||"<p>&nbsp;</p>"}else{me.doc.body.innerHTML="<p>&nbsp;</p>"}EG.bindEvent(me.doc.body,"blur",function(e){me.cacheHTML=me.getContent()});me.loaded=true};if(EG.Browser.isIE8()){me.doc.designMode="on";EG.bindEvent(me.doc,"onreadystatechange",function(){if(me.doc.body){fn1()}})}else{fn1()}me.doc.editor=me;me.doc.onclick=function(){me.hideMenus()};EG.bindEvent(me.doc,"onpaste",function(e){if(me.uploadOnPaste){if(e.clipboardData.items){var ele=e.clipboardData.items;for(var i=0;i<ele.length;++i){if(ele[i].kind=="file"&&ele[i].type.indexOf("image/")!==-1){var blob=ele[i].getAsFile();var content=new window.FormData(EG.CE({tn:"form",enctype:"multipart/form-data",encoding:"multipart/form-data"}));content.append("uploadfile",blob);EG.Ajax.send({method:"POST",url:me.imgUploadAction,content:content,contentType:false,callback:function(data){var result;eval("result="+data+";");if(result[0]==0){me.pasteHTML("<img src='"+result[1]["path"]+"'>")}}})}}}}return false});if(EG.Browser.isIE()){EG.bindEvent(this,"beforedeactivate",function(){var range=me.doc.selection.createRange();if(range.getBookmark){this.ieSelectionBookmark=range.getBookmark()}});EG.bindEvent(this,"activate",function(){if(this.ieSelectionBookmark){var range=me.doc.body.createTextRange();range.moveToBookmark(this.ieSelectionBookmark);range.select();this.ieSelectionBookmark=null}})}EG.bindEvent(me.doc,"keydown",function(e){if(e==null){e=window.event}var code=e.keyCode;if(e.ctrlKey){}else{if(e.shiftKey){}else{switch(code){case 13:}}}});EG.bindEvent(me.doc,"click",function(e){for(var i=0,il=me.clickHandlers.length;i<il;i++){me.clickHandlers[i](e)}});EG.bindEvent(me.doc,"dblclick",function(e){for(var i=0,il=me.dblclickHandlers.length;i<il;i++){me.dblclickHandlers[i](e)}})}},dUploadLabel:{onmouseover:function(){EG.show(this.childNodes[1])},onmouseout:function(){EG.hide(this.childNodes[1])}},dUploadName:{}}}}});var Editor=EG.ui.Editor})();(function(){EG.define("EG.ui.editor.Plugin",{config:{buildInParent:false},getToolbarButton:function(){return this.toolbarButton},getMenuPanel:function(){return this.menuPanel},render:function(){}})})();(function(){EG.define("EG.ui.editor.ToolbarButton",{extend:"EG.ui.Item",config:{type:null,cls:null},constructor:function(b,a){this.initItem(a);this.editor=b;this.button=EG.CE({tn:"a",cls:b.cls+"-toolbar-"+this.cls,href:"javascript:void(0)"});if(a.click){EG.Event.bindEvent(this.button,"onclick",a.click)}this.element=this.button}})})();(function(){EG.define("EG.ui.editor.plugin.Bold",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"bold",click:function(){c.execute()},cls:"bold"})},execute:function(){this.editor.htmlexec("Bold")}});var a=EG.ui.editor.plugin.Bold;EG.ui.Editor.registPlugin("bold",a)})();(function(){EG.define("EG.ui.editor.plugin.Code",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.codeModel=false;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"code",click:function(){c.codeModel=!c.codeModel;c.execute(c.codeModel)},mouseover:function(){},cls:"code"});this.editPanel=EG.CE({pn:this.editor.dHtml,tn:"textarea",style:"width:100%;background:#FEFEFE;border:0px;margin:0px;padding:0px"});EG.hide(this.editPanel)},execute:function(e){if(e){EG.setValue(this.editPanel,this.editor.getContent());EG.css(this.editPanel,"height:"+this.editor.frame.clientHeight+"px;width:"+this.editor.frame.clientWidth+"px");EG.show(this.editPanel);EG.hide(this.editor.frame);var d=this.editor.dToolbarButtons.childNodes;for(var c=0,b=d.length;c<b;c++){if(d[c]!=this.toolbarButton.getElement()){EG.hide(d[c])}}}else{this.editor.setContent(EG.getValue(this.editPanel));EG.hide(this.editPanel);EG.show(this.editor.frame);var d=this.editor.dToolbarButtons.childNodes;for(var c=0,b=d.length;c<b;c++){EG.show(d[c])}}}});var a=EG.ui.editor.plugin.Code;EG.ui.Editor.registPlugin("code",a)})();(function(){EG.define("EG.ui.editor.plugin.Color",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"color",click:function(){c.editor.hideMenus();var d=c.toolbarButton.getElement();var e=EG.Tools.getElementPos(d,c.editor.getElement());e.y=d.clientHeight;EG.Style.moveTo(c.menuPanel.dPop,e);c.menuPanel.open()},mouseover:function(){},cls:"color"});this.buildMenuPanel()},execute:function(b){this.editor.htmlexec("ForeColor",b)},buildMenuPanel:function(){this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});var m=this;var f;var b=EG.CE({tn:"table",cellSpacing:0,cellPadding:0,style:"float:left;border:0px;margin:0px",cn:[f=EG.CE({tn:"tbody"})]});var c=5;for(var e=0;e<16;e++){if(e%4==0){continue}var l=EG.CE({pn:f,tn:"tr"});for(var d=0;d<30;d++){var k=d%c;if(d%4==0){continue}var h=Math.floor(d/c)*3;var g=h+3;(function(){var i=a.wc((a.cnum[g]*k+a.cnum[h]*(c-k)),(a.cnum[g+1]*k+a.cnum[h+1]*(c-k)),(a.cnum[g+2]*k+a.cnum[h+2]*(c-k)),e);EG.CE({pn:l,tn:"td",cn:[{tn:"div",cls:m.editor.cls+"-toolbar-color-box",style:"background-color:"+i,onclick:function(){m.execute(i);m.menuPanel.close()},onmouseover:function(){EG.setCls(this,["toolbar-color-box","toolbar-color-boxOn"],m.editor.cls)},onmouseout:function(){EG.setCls(this,["toolbar-color-box"],m.editor.cls)}}]})})()}}this.menuPanel.addChildren(b)},statics:{hexch:["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F"],cnum:[1,0,0,1,1,0,0,1,0,0,1,1,0,0,1,1,0,1,1,0,0],colors:["#FF0000","#00FF00","#0000FF","#FFFF00","#00FFFF","#FF00FF"],toHex:function(d){var c,b,d=Math.round(d);b=d%16;c=Math.floor((d/16))%16;return(a.hexch[c]+a.hexch[b])},wc:function(e,d,c,f){e=((e*16+e)*3*(15-f)+128*f)/15;d=((d*16+d)*3*(15-f)+128*f)/15;c=((c*16+c)*3*(15-f)+128*f)/15;return"#"+a.toHex(e)+a.toHex(d)+a.toHex(c)}}});var a=EG.ui.editor.plugin.Color;EG.ui.Editor.registPlugin("color",a)})();(function(){EG.define("EG.ui.editor.plugin.Fontname",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"fontname",text:"字体",click:function(){c.editor.hideMenus();var d=c.toolbarButton.getElement();var e=EG.Tools.getElementPos(d,c.editor.getElement());e.y=d.clientHeight;EG.Style.moveTo(c.menuPanel.dPop,e);c.menuPanel.open()},mouseover:function(){},style:"font-size:12px",cls:"fontname"});this.buildMenuPanel()},execute:function(b){this.editor.htmlexec("Fontname",b)},buildMenuPanel:function(){this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});var e=this;var f=EG.CE({tn:"div",style:""});for(var c=0,b=a.fontnames.length;c<b;c++){(function(){var d=a.fontnames[c];EG.CE({pn:f,tn:"a",cls:e.editor.cls+"-toolbar-fontname-box",style:"font-family:"+d,innerHTML:d,onclick:function(){e.execute(d);e.menuPanel.close()}})})()}this.menuPanel.addChildren(f)},statics:{fontnames:["微软雅黑","宋体","黑体","楷体_GB2312","隶书","幼圆","Arial","Arial Narrow","Arial Black","Comic Sans MS","Courier","System","Times New Roman"]}});var a=EG.ui.editor.plugin.Fontname;EG.ui.Editor.registPlugin("fontname",a)})();(function(){EG.define("EG.ui.editor.plugin.Fontsize",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"fontsize",click:function(){c.editor.hideMenus();var d=c.toolbarButton.getElement();var e=EG.Tools.getElementPos(d,c.editor.getElement());e.y=d.clientHeight;EG.Style.moveTo(c.menuPanel.dPop,e);c.menuPanel.open()},mouseover:function(){},cls:"fontsize"});this.buildMenuPanel()},execute:function(b){this.editor.htmlexec("FontSize",b)},buildMenuPanel:function(){this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});var e=this;var f=EG.CE({tn:"div",style:""});for(var c=0,b=a.fontsizes.length;c<b;c++){(function(){var d=a.fontsizes[c];EG.CE({pn:f,tn:"a",cls:e.editor.cls+"-toolbar-fontsize-box",style:"font-size:"+(d+10)+"px",innerHTML:d+"号",onclick:function(){e.execute(d);e.menuPanel.close()}})})()}this.menuPanel.addChildren(f)},statics:{fontsizes:[1,2,3,4,5,6,7]}});var a=EG.ui.editor.plugin.Fontsize;EG.ui.Editor.registPlugin("fontsize",a)})();(function(){EG.define("EG.ui.editor.plugin.Full",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"full",click:function(){c.execute()},cls:"full"})},execute:function(){if(!this.fulled){if(!this.d){this.d=new EG.ui.Dialog({lock:true,closeable:false,height:"100%",width:"100%",renderTo:EG.getBody(),id:"dddd",btns:[{text:"关闭",click:function(){me.d.close()}}]});this.d.open();this.d.dBody.appendChild(this.editor.getElement());this.editor.width="100%";this.editor.height="100%";this.editor.render()}this.d.open()}else{this.d.close()}}});var a=EG.ui.editor.plugin.Full;EG.ui.Editor.registPlugin("full",a)})();(function(){EG.define("EG.ui.editor.plugin.Image",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.buildInParent=true;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"image",click:function(){if(c.resPicker){c.resPicker.close()}c.showMenuPanel()},mouseover:function(){},cls:"image"});this.uploadPolicy=this.editor.imgUploadPolicy||this.editor.uploadPolicy;this.uploadAction=this.editor.imgUploadAction||this.editor.uploadAction;this.onUploaded=this.editor.imgOnUploaded||this.editor.onUploaded;this.buildMenuPanel();this.editor.dblclickHandlers.push(function(d){if(d.target&&d.target.tagName.toUpperCase()=="IMG"){c.selectedImg=d.target;c.setImageForm(d.target,true);c.showMenuPanel()}});this.editor.clickHandlers.push(function(d){if(d.target&&d.target.tagName.toUpperCase()=="IMG"){}else{c.selectedImg=null}})},showMenuPanel:function(){this.editor.hideMenus();this.menuPanel.open();this.editor.curMenuPanel=this.menuPanel;this.render()},execute:function(d){var b;var c=this;if(typeof(d)=="string"){d={src:d}}if(this.selectedImg){b=this.selectedImg;this.setImageAtrs(b,d)}else{b=EG.CE({tn:"img",onload:function(){var e=EG.DOM.getOuterHTML(this);c.editor.pasteHTML(e)}});this.setImageAtrs(b,d);b.src=d.src}},render:function(){this.imgForm.render()},getUploadAction:function(b){return EG.MMVC.getPath().upload+"/?uploadPolicy="+this.uploadPolicy+"&type="+b},beforeUpload:function(){return true},buildMenuPanel:function(){var d=this;var f=this.editor.parent||this.editor;if(f.getElement){f=f.getElement()}var h=this.editor.imgPickers;this.menuPanel=new EG.ui.Dialog({closeable:true,lock:true,posFix:true,title:"图片设置",fullable:true,parent:f,width:350,height:"auto",layout:{type:"line",direct:"V"},items:[this.imgForm=new EG.ui.Form({width:350,height:"auto",labelWidth:50,layout:"default",items:[{xtype:"tabPanel",width:"100%",height:120,items:[{tab:{title:"图片",style:"width:60px"},panel:{layout:"table",items:[{xtype:"formItem",pos:[0,0],title:"图片",name:"imgShowArea",type:"label",length:15,notnull:true,height:40},{xtype:"button",pos:[0,1],text:"选择",hidden:h?false:true,click:function(){d.resPicker.open()}},{xtype:"formItem",pos:[1,0],title:"路径",name:"imgSrc",type:"text",style:"overflow:hidden"},{xtype:"formItem",pos:[2,0],title:"宽",name:"imgWidth",type:"text",length:5,style:"width:40px",after:"px"},{xtype:"formItem",pos:[2,1],title:"高",name:"imgHeight",type:"text",length:5,style:"width:40px",after:"px"},{xtype:"formItem",pos:[3,0],title:"隐藏",name:"imgSrcUri",type:"text",hidden:true}]}},{tab:{title:"浮动",style:"width:60px"},tabStyle:"width:100",panel:{layout:"table",items:[{xtype:"formItem",pos:[0,0],title:"环绕",name:"imgFloat",type:"select",textvalues:[["不环绕",""],["向左","left"],["向右","right"]]},{xtype:"formItem",pos:[1,0],title:"左距",name:"imgMarginLeft",type:"text",length:10,after:"px"},{xtype:"formItem",pos:[1,1],title:"右距",name:"imgMarginRight",type:"text",length:10,after:"px"},{xtype:"formItem",pos:[2,0],title:"上距",name:"imgMarginTop",type:"text",length:10,after:"px"},{xtype:"formItem",pos:[2,1],title:"下距",name:"imgMarginBottom",type:"text",length:10,after:"px"}]}}]}]})],btns:[{text:"确定",cls:"eg_button_small",click:function(){d.doInsertImage();d.menuPanel.close()}},{text:"取消",cls:"eg_button_small",click:function(){d.menuPanel.close()},style:"margin-left:10px"}]});if(h){this.resPicker_opened=false;var g=h.types;var e=[];this.tpm={};for(var b=0;b<g.length;b++){var c=g[b];e.push([c.title,c.type]);this.tpm[c.type]=c}this.resPicker=new EG.ui.Dialog({renderTo:f,title:"选择图片",width:"100%",height:"auto",style:"margin:10px;z-index:2;",lock:true,posFix:true,layout:{type:"line",direct:"V"},items:[{xtype:"panel",height:EG.ui.FormItem._config.height,layout:"line",items:[this.sltCg=new EG.ui.FormItem({xtype:"formItem",title:"分类",type:"select",textvalues:e,width:200,onchange:function(i){d.loadPickerFiles(i)}}),{xtype:"button",text:"刷新",cls:"eg_button_small",onclick:function(){d.loadPickerFiles(d.sltCg.getValue())}}]},this.rpm=new EG.ui.Panel({xtype:"panel",style:"margin:3px;border:1px solid gray;overflow:auto",height:450}),this.rpf=new EG.ui.Panel({xtype:"panel",style:"margin:3px",height:30})],btns:[{xtype:"button",text:"选择",cls:"eg_button_small",click:function(){d.doPickerSelect()}}],afterOpen:function(){if(!d.resPicker_opened){var i=d.sltCg.getValue();if(i){d.loadPickerFiles(i)}}d.resPicker_opened=true}})}if(this.uploadPolicy||this.uploadAction){this.imgForm.items[0].addItem({tab:{title:"上传",style:"width:60px"},panel:{layout:"table",items:[{xtype:"formItem",title:"上传",name:"imgUpload",type:"upload",height:50,style:"overflow:hidden",autoupload:true,action:this.uploadAction||this.getUploadAction("image"),callback:function(i){var j=i.file?i.file:i;d.setImageForm({src:j.path});EG.Locker.lock(false);if(d.onUploaded){d.onUploaded.apply(d.editor,[i])}},exts:["JPEG","JPG","PNG","GIF","BMP"],showWait:true,beforeUpload:function(){return d.beforeUpload("img")}}]}})}},doPickerSelect:function(){var c=this.tpm[this.sltCg.getValue()];var b=c.getSelectedUri();if(b==null){EG.Locker.message("请选择文件");return}this.imgForm.getFormItem("imgSrc").setValue(b.src);this.imgForm.getFormItem("imgShowArea").setValue("<img src='"+b.src+"' height='40' />");this.imgForm.getFormItem("imgSrcUri").setValue(b.uri);this.resPicker.close()},loadPickerFiles:function(b){var c=this.tpm[b];c.load(this.rpm)},setFiles:function(b){this.rpm.clear();this.rpf.getElement().innerHTML=""+b.length+"个文件"},doInsertImage:function(c){var b={};if(arguments.length==0){c=this.imgForm.getData()}b.src=c.imgSrc;if(b.src==""){EG.Locker.message("请选择图片或上传新图片");return}b.width=c.imgWidth;b.height=c.imgHeight;b.style={};if(c.imgFloat!=""){EG.css(b,"float:"+c.imgFloat)}if(c.imgMarginLeft!=""){EG.css(b,"marginLeft:"+c.imgMarginLeft)}if(c.imgMarginRight!=""){EG.css(b,"marginRight:"+c.imgMarginRight)}if(c.imgMarginTop!=""){EG.css(b,"marginTop:"+c.imgMarginTop)}if(c.imgMarginBottom!=""){EG.css(b,"marginBottom:"+c.imgMarginBottom)}b.srcUri=this.imgForm.getFormItem("imgSrcUri").getValue();this.execute(b)},setImageAtrs:function(b,c){if(c.style){EG.css(b,c.style)}if(c.width==null||c.width==""){b.removeAttribute("width")}else{b.width=c.width}if(c.height==null||c.height==""){b.removeAttribute("height")}else{b.height=c.height}if(c.srcUri!=null&&c.srcUri!=""){b.setAttribute("srcUri",c.srcUri)}},setImageFormByImg:function(b){var c={src:b.src,style:b.style};this.setImageForm(c)},setImageForm:function(d){this.imgForm.items[0].getTab(0).select();var f=EG.CE({tn:"img"});var c=this;f.onload=function(){var g=this.width,h=this.height;c.imgForm.getFormItem("imgWidth").setValue(this.width);c.imgForm.getFormItem("imgHeight").setValue(this.height);if(this.width>this.height){this.height=h/(g/100);this.width=100}else{this.width=g/(h/40);this.height=40}};f.style.cursor="pointer";f.ondblclick=function(){window.open(this.src)};f.title="双击查看图片";f.src=d.src;if(d.style){if(d.style["float"]){this.imgForm.getFormItem("imgFloat").setValue(d.style["float"].toLowerCase())}if(d.style.marginLeft){this.imgForm.getFormItem("imgMarginLeft").setValue(d.style.marginLeft.toLowerCase())}if(d.style.marginRight){this.imgForm.getFormItem("imgMarginRight").setValue(d.style.marginRight.toLowerCase())}if(d.style.marginTop){this.imgForm.getFormItem("imgMarginTop").setValue(d.style.marginTop.toLowerCase())}if(d.style.marginBottom){this.imgForm.getFormItem("imgMarginBottom").setValue(d.style.marginBottom.toLowerCase())}}if(d.width!=null){this.imgForm.getFormItem("imgWidth").setValue(d.width)}if(d.height!=null){this.imgForm.getFormItem("imgHeight").setValue(d.height)}this.imgForm.getFormItem("imgShowArea").setValue("");this.imgForm.getFormItem("imgShowArea").prop.getElement().appendChild(f);var e=d.src;var b=EG.Browser.getDomainAddress();if(e.indexOf(b)==0){e=e.substr(b.length)}this.imgForm.getFormItem("imgSrc").setValue(e)}});var a=EG.ui.editor.plugin.Image;EG.ui.Editor.registPlugin("image",a)})();(function(){EG.define("EG.ui.editor.plugin.Indent",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"indent",click:function(){c.editor.hideMenus();var d=c.toolbarButton.getElement();var e=EG.Tools.getElementPos(d,c.editor.getElement());e.y=d.clientHeight;EG.Style.moveTo(c.menuPanel.dPop,e);c.menuPanel.open()},mouseover:function(){},cls:"indent"});this.buildMenuPanel()},execute:function(b){this.editor.htmlexec(b)},buildMenuPanel:function(){this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});var e=this;var f=EG.CE({tn:"div",style:""});for(var c=0,b=a.indents.length;c<b;c++){(function(){var d=a.indents[c];EG.CE({pn:f,tn:"a",cls:e.editor.cls+"-toolbar-indent-box",innerHTML:d[0],onclick:function(){e.execute(d[1]);e.menuPanel.close()}})})()}this.menuPanel.addChildren(f)},statics:{indents:[["增加缩进","Indent"],["减少缩进","Outdent"]]}});var a=EG.ui.editor.plugin.Indent;EG.ui.Editor.registPlugin("indent",a)})();(function(){EG.define("EG.ui.editor.plugin.Italic",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"italic",click:function(){c.execute()},cls:"italic"})},execute:function(){this.editor.htmlexec("Italic")}});var a=EG.ui.editor.plugin.Italic;EG.ui.Editor.registPlugin("italic",a)})();(function(){EG.define("EG.ui.editor.plugin.List",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"list",click:function(){c.editor.hideMenus();var d=c.toolbarButton.getElement();var e=EG.Tools.getElementPos(d,c.editor.getElement());e.y=d.clientHeight;EG.Style.moveTo(c.menuPanel.dPop,e);c.menuPanel.open()},mouseover:function(){},cls:"list"});this.buildMenuPanel()},execute:function(b){this.editor.htmlexec("Insert"+b+"List")},buildMenuPanel:function(){this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});var e=this;var f=EG.CE({tn:"div",style:""});for(var c=0,b=a.lists.length;c<b;c++){(function(){var d=a.lists[c];EG.CE({pn:f,tn:"a",cls:e.editor.cls+"-toolbar-list-box",innerHTML:d[0],onclick:function(){e.execute(d[1]);e.menuPanel.close()}})})()}this.menuPanel.addChildren(f)},statics:{lists:[["数字列表","Ordered"],["符号列表","Unordered"]]}});var a=EG.ui.editor.plugin.List;EG.ui.Editor.registPlugin("list",a)})();(function(){EG.define("EG.ui.editor.plugin.Program",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"program",click:function(){c.menuPanel.open()},cls:"program"});this.buildMenuPanel()},buildMenuPanel:function(){var b=this;var c=this.editor.parent||this.editor;if(c.getElement){c=c.getElement()}this.menuPanel=new EG.ui.Dialog({closeable:true,lock:true,posFix:true,title:"代码编辑",fullable:true,parent:c,width:350,height:"auto",layout:{type:"line",direct:"V"},items:[this.fiText=new EG.ui.FormItem({xtype:"formItem",title:"",showLeft:false,type:"textarea",length:15,notnull:true,height:150})],btns:[{text:"确定",cls:"eg_button_small",click:function(){b.doInsertProgram()}},{text:"取消",cls:"eg_button_small",click:function(){b.menuPanel.close()},style:"margin-left:10px"}]})},doInsertProgram:function(){var b=this.fiText.getValue();b=EG.String.trim(b);b=EG.Tools.filterSpecChar(b);if(!EG.String.startWith(b,"<pre ")){b="<pre class='"+this.editor.cls+"-toolbar-program-code'>"+b}if(!EG.String.endWith(b,"</pre>")){b=b+"</pre>"}this.editor.pasteHTML(b)}});var a=EG.ui.editor.plugin.Program;EG.ui.Editor.registPlugin("program",a)})();(function(){EG.define("EG.ui.editor.plugin.Textalign",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"textalign",click:function(){c.editor.hideMenus();var d=c.toolbarButton.getElement();var e=EG.Tools.getElementPos(d,c.editor.getElement());e.y=d.clientHeight;EG.Style.moveTo(c.menuPanel.dPop,e);c.menuPanel.open()},mouseover:function(){},cls:"textalign"});this.buildMenuPanel()},execute:function(b){this.editor.htmlexec("Justify"+b)},buildMenuPanel:function(){this.menuPanel=new EG.ui.Pop({closeable:true,lock:false,posFix:false,cls:"eg_pop_blank"});var e=this;var f=EG.CE({tn:"div",style:""});for(var c=0,b=a.fontaligns.length;c<b;c++){(function(){var d=a.fontaligns[c];EG.CE({pn:f,tn:"a",cls:e.editor.cls+"-toolbar-textalign-box",innerHTML:d[0],onclick:function(){e.execute(d[1]);e.menuPanel.close()}})})()}this.menuPanel.addChildren(f)},statics:{fontaligns:[["左对齐","Left"],["居中对齐","Center"],["右对齐","Right"]]}});var a=EG.ui.editor.plugin.Textalign;EG.ui.Editor.registPlugin("textalign",a)})();(function(){EG.define("EG.ui.editor.plugin.Underline",{extend:"EG.ui.editor.Plugin",constructor:function(b){this.editor=b;var c=this;this.toolbarButton=new EG.ui.editor.ToolbarButton(b,{type:"underline",click:function(){c.execute()},cls:"underline"})},execute:function(){this.editor.htmlexec("Underline")}});var a=EG.ui.editor.plugin.Underline;EG.ui.Editor.registPlugin("underline",a)})();(function(){EG.define("EG.ui.Upload",["EG.ui.Item","EG.ui.Button"],function(b,a,c){return{alias:"upload",extend:b,config:{button:null,action:null,callback:null,beforeUpload:null,exts:null,showWait:false,showFilename:true,showPath:true,autoupload:false,onselect:null,filename:null,subSelf:true,cls:"eg_upload",showProcess:false,name:null,paramsConfig:null,btnText:"选择",btnCls:null,btnStyle:""},constructor:function(d){this.initItem(d);this.idx=EG.UI.GITEMIDX++;EG.UI.GITEMS[this.idx]=this;this.filename=this.name||this.filename||("file"+this.idx);EG.DOM.getActionFrame();if(this.subSelf){this.element=EG.CE({tn:"form",cls:this.cls,method:"post",encoding:"multipart/form-data",target:"actionFrame"})}else{this.element=EG.CE({tn:"div",cls:this.cls})}this.setAction(this.action);EG.CE({ele:this.element,item:this,cn:[this.dPath=EG.CE({tn:"div",cls:this.cls+"-dPath",style:"display:none;height:22px;"}),this.dFileName=EG.CE({tn:"div",cls:this.cls+"-dFileName",style:"display:none",cn:[]}),this.dFileinput=EG.CE({tn:"div",cls:this.cls+"-dFileinput",style:"position:relative",cn:[this.dSelectBtn=new a({text:this.btnText,click:function(){},style:"margin-left:2px"+this.btnStyle}),this.fileinput=EG.CE({tn:"input",cls:this.cls+"-fileinput",name:this.filename,type:"file",style:"cursor:pointer;position:absolute;left:0px;top:0px;opacity:0;filter:alpha(opacity=0);"})]}),this.dHiddenParams=EG.CE({tn:"div",style:"display:none"})]});var e=this;EG.bindEvent(this.fileinput,"onchange",function(){var g=EG.getValue(e.fileinput);if(g){var h=g.lastIndexOf("/");if(h==-1){h=g.lastIndexOf("\\")}var f=g.substr(h+1);EG.setValue(e.dFileName,f);if(e.showFilename){EG.show(e.dFileName)}if(e.onselect){e.onselect.apply(e,[g,f])}if(e.autoupload){e.submit()}}else{EG.hide(e.dFileName)}});if(!this.autoupload&&this.showBtn){if(!this.button){this.button=new a({text:this.btnText,click:function(){e.submit()},style:"veritical-align:middle;margin-left:10px"}).getElement()}this.element.appendChild(this.button)}EG.css(this.element,this.style);if(this.paramsConfig){this.setParams(this.paramsConfig)}},setAction:function(d){if(this.callback){d+="&callback=parent.EG.UI.GITEMS["+this.idx+"].onUploaded&onerror=parent.EG.UI.GITEMS["+this.idx+"].onError"}if(this.showProcess){d+="&callbackProcess=parent.EG.UI.GITEMS["+this.idx+"].onProcess"}this.action=d;if(this.subSelf){EG.CE({ele:this.element,action:d})}},setValue:function(d){EG.setValue(this.dPath,d)},getValue:function(){return EG.getValue(this.dPath)},getElement:function(){return this.element},setParams:function(e){for(var d in e){this.setParam(d,e[d])}},setParam:function(d,g){var e=this.dHiddenParams.childNodes;var f=null;for(var h in e){if(h.name=="name"){f=h;break}}if(f==null){f=EG.CE({pn:this.dHiddenParams,tn:"input",type:"hidden",name:d,value:g})}f.value=g},removeParam:function(d){var e=this.dHiddenParams.childNodes;var f=null;for(var g in e){if(g.name=="name"){f=g;break}}if(f!=null){EG.DOM.remove(f)}},submit:function(){if(EG.getValue(this.fileinput)==""){EG.Locker.message("请选择文件上传");return}if(!this.checkExt()){EG.Locker.message("上传类型需为"+this.exts+"的一种");return}if(this.beforeUpload&&!this.beforeUpload.apply(this)){return}if(this.showWait){EG.Locker.wait("正在上传文件,请稍后")}this.element.submit()},checkExt:function(){if(!this.exts){return true}var e=this.getExt();for(var d=0;d<this.exts.length;d++){if(this.exts[d].toUpperCase()===e.toUpperCase()){return true}}return false},getExt:function(){var d=EG.getValue(this.fileinput);return(d.substr(d.length-5)).substr((d.substr(d.length-5)).indexOf(".")+1)},onUploaded:function(d){if(this.showPath){EG.show(this.dPath)}EG.setValue(this.dPath,d.path);if(this.showWait){EG.Locker.lock(false)}if(typeof(this.callback)=="string"){if(this.callback=="showImg"){if(d.path==null){throw new Error("上传的返回值中不带path")}EG.setValue(this.dPath,d.path);EG.DOM.removeChilds(this.dPreview);EG.CE({pn:this.dPreview,tn:"img",width:"50",height:"30",src:d.path,style:"cursor:pointer",onclick:function(){window.open(this.src)}})}}else{this.callback.apply(this,arguments)}},onError:function(d){EG.Locker.message(d.exMessage)},onProcess:function(d,e){EG.Locker.message("已上传:"+parseInt(d/1024)+"K"+parseInt((d/e*100))+"%")},render:function(){b.fit(this)},destroy:function(){EG.UI.GITEMS[this.idx]=null},statics:{Callback:{showImg:"showImg"}}}})})();(function(){EG.define("EG.ui.Text",["EG.ui.Item","EG.ui.FormItem"],function(a,c,b){return{alias:"text",extend:a,config:{length:null,cls:"eg_text",onkeydown:null,onkeyup:null,inputStyle:null,dataType:null,placeholder:null},constructor:function(d){this.callSuper([d]);if(this.length!=null){this.input.maxLength=this.length}},build:function(){this.element=EG.CE({tn:"div",cls:this.cls,item:this,style:"position:relative;",cn:[this.input=EG.CE({tn:"input",type:"text",cls:this.cls+"-input",item:this,style:"position:absolute;"+EG.unnull(this.inputStyle,""),placeholder:this.placeholder||" "})]});if(this.dataType){this.dDataType=EG.CE({pn:this.element,tn:"div",style:"position:absolute;right:0px;bottom:0px;width:10px;height:10px",cls:this.dataType})}c.bindValidate.apply(this,[]);if(this.onkeydown){EG.Event.bindEvent(this.input,"onkeydown",this.onkeydown)}if(this.onkeyup){EG.Event.bindEvent(this.input,"onkeyup",this.onkeyup)}},setValue:function(d){EG.setValue(this.input,d)},getValue:function(){var d=EG.getValue(this.input);if(this.dataType&&this.dataType=="num"){d=parseFloat(d)}else{if(this.dataType&&this.dataType=="bool"){d=(d==="true")}}return d},render:function(){a.fit(this);var d=EG.getSize(this.element);a.fit({element:this.input,pSize:d});EG.css(this.input,"line-height:"+EG.getSize(this.input).innerHeight+"px")}}})})();(function(){EG.define("EG.ui.Textarea",["EG.ui.Item","EG.ui.FormItem"],function(a,c,b){return{alias:"textarea",extend:a,config:{length:null,style:null,cls:"eg_textarea",readLineHeight:22},constructor:function(d){this.initItem(d);this.element=EG.CE({tn:"div",cls:this.cls,item:this,cn:[this.input=EG.CE({tn:"textarea",cls:this.cls+"-input"})]});if(this.length!=null){this.input.maxLength=this.length}c.bindValidate.apply(this,[]);EG.css(this.element,this.style)},setValue:function(d){EG.setValue(this.input,d)},getValue:function(){return EG.getValue(this.input)},render:function(){a.fit(this);a.fit({element:this.input,pSize:EG.getSize(this.element)})}}})})();(function(){EG.define("EG.ui.Password",["EG.ui.Item","EG.ui.FormItem"],function(a,c,b){return{alias:"password",extend:a,config:{length:null,cls:"eg_password",inputStyle:null},constructor:function(d){this.initItem(d);this.element=EG.CE({tn:"div",cls:this.cls,cn:[this.input=EG.CE({tn:"input",type:"password",cls:this.cls+"-input",item:this,style:EG.unnull(this.inputStyle,"")})]});c.bindValidate.apply(this,[]);if(this.length!=null){this.input.maxLength=this.length}},setValue:function(d){EG.setValue(this.input,d)},getValue:function(){return EG.getValue(this.input)},render:function(){a.fit(this);var d=EG.getSize(this.element);a.fit({element:this.input,pSize:d});EG.css(this.input,"line-height:"+EG.getSize(this.input).innerHeight+"px")}}})})();(function(){EG.define("EG.ui.Label",["EG.ui.Item"],function(a,b){return{alias:"label",extend:a,config:{title:"",width:null,height:null,style:null,onclick:null,onclickSrc:null},constructor:function(c){this.initItem(c);this.element=EG.CE({tn:"div",innerHTML:this.title,item:this});if(this.onclick){EG.CE({ele:this.element,onclick:this.onclick,onclickSrc:this.onclickSrc})}a.setWidth(this.element,this.width);a.setHeight(this.element,this.height);EG.css(this.element,this.style);if(this.style){var d=EG.Style.parse(this.style);if(d["line-height"]!=null){this.render_lineHeight=d["line-height"]}}},setValue:function(c){if(typeof(c)=="undefined"){return}this.value=c;EG.setValue(this.element,c)},getValue:function(){return this.value},render:function(){a.fit(this);var c=EG.getSize(this.element);EG.css(this.element,"line-height:"+(this.render_lineHeight||c.innerHeight)+"px")}}})})();(function(){EG.define("EG.ui.Code",["EG.ui.Item"],function(a,b){return{alias:"code",extend:a,config:{mode:null,theme:"monokai"},constructor:function(c){this.callSuper([c])},build:function(){this.callSuper("build");ace.require("ace/ext/language_tools");this.editor=ace.edit(this.element);this.editor.setOptions({enableBasicAutocompletion:true,enableSnippets:true,enableLiveAutocompletion:true});this.editor.setTheme("ace/theme/"+this.theme);if(this.mode){this.editor.getSession().setMode("ace/mode/"+this.mode)}},setValue:function(c){if(c==null){c=""}this.editor.setValue(c)},getValue:function(){console.log(this.editor.getValue());return this.editor.getValue()},render:function(){a.fit(this)}}})})();(function(){EG.define("EG.ui.Select",["EG.ui.Item"],function(a,b){return{alias:"select",extend:a,config:{onchange:null,textvalues:[],cls:"eg_select",edit:false,onchangeOnbuild:false,multi:false,tabIndex:0,useEmpty:true},constructor:function(c){this.vs=[];this.callSuper([c])},build:function(){var c=this;var d=EG.clone(this.textvalues);this.textvalues=[];this.builded=false;this.onchangeEvents=[];if(this.onchange!=null){this.bindOnchange(this.onchange)}this.element=EG.CE({tn:"div",cls:this.cls,item:this,tabIndex:this.tabIndex,onmouseover:b._events.element.onmouseover,onmouseout:b._events.element.onmouseout,onkeyup:b._events.element.onkeyup,cn:[this.input=EG.CE({tn:"div",cls:this.cls+"-input",style:"overflow:hidden",item:this,cn:[this.iptText=EG.CE({tn:"input",cls:this.cls+"-iptText",style:"overflow:hidden"}),this.dArrow=EG.CE({tn:"div",cls:this.cls+"-arrow"})],onclick:b._events.input.onclick}),this.dOpts=EG.CE({tn:"div",cls:this.cls+"-opts"})]});if(!this.edit){this.iptText.readOnly=true}EG.hide(this.dOpts);this.iptText.value="";this.setOptions(d);this.builded=true},showOptions:function(){EG.show(this.dOpts);var d=this.getValue();var f=this.dOpts.childNodes;for(var e=0,c=f.length;e<c;e++){if(d===f[e].value){EG.setCls(f[e],["opt","opt-selected"],this.cls)}else{EG.setCls(f[e],"opt",this.cls)}}},setValue:function(h,g){if(this.multi){this.setValues(h,g);return}if(g==null){g=true}if(h==null){return}var e,j;for(var f=0,c=this.textvalues.length;f<c;f++){e=this.textvalues[f];if(e[1]===h){j=e[0];break}}if(j==null){this.iptText.value="";return}var d=this.getValue();this.iptText.value=j;this.iptText.v=h;if(d!==h&&g){if(!this.builded&&!this.onchangeOnbuild){return}this.doOnChange(h,d)}},refreshValues:function(e){var c=this.getValue();var f=this.dOpts.childNodes;EG.Array.clear(this.vs);var h=[];for(var d=0;d<f.length;d++){if(f[d].childNodes[0].checked){this.vs.push(f[d].value);h.push(f[d].childNodes[1].innerHTML)}}this.iptText.value=h.join(",");var g=this.getValue();if(e){if(!this.builded&&!this.onchangeOnbuild){return}this.doOnChange(g,c)}},setValues:function(l,f){var d=this.getValue();var h=this.dOpts.childNodes;var k=[];EG.Array.clear(this.vs);for(var c=0;c<l.length;c++){for(var e=0;e<h.length;e++){if(h[e].value==l[c]){var g=h[e].childNodes[0];g.checked=true;EG.setCls(g,["opt-b","opt-b-select"],this.cls);k.push(h[e].childNodes[1].innerHTML)}}this.vs.push(l[c])}this.iptText.value=k.join(",");if(f){if(!this.builded&&!this.onchangeOnbuild){return}this.doOnChange(this.vs,d)}},doOnChange:function(e,c){for(var d=0;d<this.onchangeEvents.length;d++){this.onchangeEvents[d].apply(this,[e,c])}},getValue:function(){if(this.multi){return this.vs}return this.iptText.v},getSelectedIdx:function(){var c=this.getValue();for(var d=0;d<this.textvalues.length;d++){if(this.textvalues[d][1]==c){return d}}return -1},setOption:function(c,d){this.textvalues[c]=d;EG.CE({ele:this.dOpts.childNodes[c],value:d[1],innerHTML:d[0]});if(this.getSelectedIdx()==c){this.iptText.value=d[0];this.iptText.v=d[1]}},setOptions:function(c,d){this.removeOptions();if(this.useEmpty){this.addOption(["请选择",""],false)}this.addOptions(c,false);if(this.multi){this.refreshValues(d)}else{if(this.textvalues&&this.textvalues.length>0){this.setValue(this.textvalues[0][1],d)}}this.render()},removeOptions:function(){EG.Array.clear(this.textvalues);EG.DOM.removeChilds(this.dOpts);this.setValue("",false)},bindOnchange:function(c){this.onchangeEvents.push(c)},addOptions:function(d,f){if(f==null){f=true}for(var e=0,c=d.length;e<c;e++){this.addOption(d[e],false)}if(f){if(this.textvalues&&this.textvalues.length>0){this.setValue(this.textvalues[0][1],true)}}},addOption:function(c,e){if(e==null){e=true}var f=EG.CE({pn:this.dOpts,tn:"div",cls:this.cls+"-opt",value:c[1],item:this,onmouseover:b._events.option.onmouseover,onmouseout:b._events.option.onmouseout,onclick:b._events.option.onclick});if(this.multi){EG.CE({pn:f,tn:"a",cls:this.cls+"-opt-b "+this.cls+"-opt-b-unselect"})}EG.CE({pn:f,tn:"div",innerHTML:c[0],style:EG.Style.c.dv,item:this});this.textvalues.push(c);if(e){this.setValue(c[1],true)}},removeOption:function(c,e){if(e==null){e=true}var g=this.getSelectedIdx();EG.Array.del(this.textvalues,c);this.dOpts.removeChild(this.dOpts.childNodes[c]);if(g==c){this.iptText.value="";if(this.textvalues.length==0){return}var f=Math.min(c,this.textvalues.length-1);var d=this.textvalues[f][1];this.setValue(d,e)}},getTextByValue:function(d){for(var c=0;c<this.textvalues.length;c++){if(this.textvalues[c][1]==d){return this.textvalues[c][0]}}return null},getText:function(c){return EG.getValue(this.iptText,{getText:true,ignoreEmpty:c})},getTitle:function(g,f,c){for(var e=0,d=this.textvalues.length;e<d;e++){if(this.textvalues[e][1]===g){return this.textvalues[e][0]}}return null},destroy:function(){},render:function(){a.fit(this);a.fit({element:this.input,pSize:EG.getSize(this.element)});var d=EG.getSize(this.input);EG.css(this.dOpts,"width:"+d.outerWidth+"px");var c=EG.getSize(this.dArrow);EG.css(this.dArrow,"line-height:"+d.innerHeight+"px;height:"+d.innerHeight+"px");var e=EG.getSize(this.input);a.fit({element:this.iptText,dSize:{width:d.innerWidth-c.outerWidth,height:"100%"},pSize:EG.getSize(this.input)});EG.css(this.iptText,"line-height:"+EG.getSize(this.iptText).innerHeight+"px;")},statics:{_events:{option:{onmouseover:function(){var c=this.item;EG.setCls(this,["opt","opt-over"],c.cls)},onmouseout:function(){var d=this.item;var c=d.getValue();if(this.value==c){EG.setCls(this,["opt","opt-selected"],d.cls)}else{EG.setCls(this,"opt",d.cls)}},onclick:function(g){var f=this.item;if(!f.multi){EG.hide(f.dOpts);f.setValue(this.value)}else{var d=this.childNodes[0];d.checked=!d.checked;EG.setCls(d,["opt-b",d.checked?"opt-b-select":"opt-b-unselect"],f.cls);var c=true;if(EG.Tools.isPressCtrl(g)){c=false}f.refreshValues(c)}EG.Event.stopPropagation(g)}},element:{onmouseout:function(d){var c=this.item;if(c.outThread!=null){return}c.outThread=setTimeout(function(){EG.hide(c.dOpts)},10);EG.Event.stopPropagation(d)},onmouseover:function(d){var c=this.item;if(c.outThread!=null){clearTimeout(c.outThread);c.outThread=null}EG.Event.stopPropagation(d)},onkeyup:function(d){var c=this.item;if(EG.Tools.isPressCtrl(d)){c.refreshValues(true)}}},input:{onclick:function(){var c=this.item;if(EG.Style.isHide(c.dOpts)){c.showOptions()}else{EG.hide(c.dOpts)}}}}}}})})();(function(){EG.define("EG.ui.SelectArea",["EG.ui.Item","EG.ui.Button"],function(b,a,c){return{alias:"selectArea",extend:b,config:{onchange:null,textvalues:[],cls:"eg_selectArea",edit:false},constructor:function(d){this.callSuper([d]);if(this.textvalues){this.setTextvalues(this.textvalues)}},build:function(){var d=this;this.element=EG.CE({tn:"div",cls:this.cls,cn:[this.srcSlt=EG.CE({tn:"div",cls:this.cls+"-slts"}),this.dMid=EG.CE({tn:"div",cls:this.cls+"-dMid",cn:[new a({text:"添加 &gt;",click:function(){d.move(true)},style:"display:block;margin:10px;"}),new a({text:"&lt; 删除",click:function(){d.move()},style:"display:block;margin:10px;"})],style:"width:80px;"}),this.destSlt=EG.CE({tn:"div",cls:this.cls+"-slts"})]});EG.Event.bindUnselect(this.element)},setTextvalues:function(d){EG.DOM.removeChilds(this.srcSlt);EG.DOM.removeChilds(this.destSlt);this.textvalues=d;this.addOptions(this.srcSlt,this.textvalues)},move:function(h){var n=this.destSlt;var l=this.srcSlt;if(h){n=this.srcSlt;l=this.destSlt}var k=this._getValues(n).concat(this._getValues(l,true));var d=this._getTextvalues(l);var m=[],j=[];for(var e=0;e<this.textvalues.length;e++){var g=this.textvalues[e];if(EG.Array.has(k,g[1])){m.push(g)}else{j.push(g)}}EG.DOM.removeChilds(n);EG.DOM.removeChilds(l);this.addOptions(l,m);this.addOptions(n,j);if(this.onchange){this.onchange.apply(this,[k,this.getValue()])}},_getValues:function(g,f){var e=g.childNodes;var h=[];for(var d=0;d<e.length;d++){if(f||e[d].selected){h.push(e[d].value)}}return h},_selectOpt:function(e,d){if(d==null){d=true}e.selected=d;e.className=d?this.cls+"-slted":this.cls+"-unslt"},_getIdx:function(f){var e=f.paren.childNodes;for(var d=0;d<e.length;d++){if(e[d]==cn){return d}}throw new Error("未找到索引")},addOptions:function(g,h){var f=this;for(var e=0;e<h.length;e++){var d=h[e];EG.CE({pn:g,tn:"div",innerHTML:d[0],cls:this.cls+"-unslt",value:d[1],onclick:function(n){n=EG.Event.getEvent(n);if(n.shiftKey){if(g.lastIdx!=null){var i=EG.DOM.getIdx(this);var l=Math.min(g.lastIdx,i),o=Math.max(g.lastIdx,i);var m=this.parentNode.childNodes;for(var k=0;k<m.length;k++){f._selectOpt(m[k],false)}for(var k=l;k<=o;k++){f._selectOpt(m[k],true)}}else{g.lastIdx=EG.DOM.getIdx(this);f._selectOpt(this,!this.selected)}}else{if(!n.ctrlKey){var m=this.parentNode.childNodes;for(var k=0;k<m.length;k++){f._selectOpt(m[k],false)}}g.lastIdx=EG.DOM.getIdx(this);f._selectOpt(this,!this.selected)}}})}},clear:function(){EG.DOM.removeChilds(this.destSlt);EG.DOM.removeChilds(this.srcSlt)},clearSourceOptions:function(){EG.DOM.removeChilds(this.srcSlt)},clearSelectedOptions:function(){EG.DOM.removeChilds(this.destSlt)},addSourceOptions:function(d){this.addOptions(this.srcSlt,d)},addSelectedOptions:function(d){this.addOptions(this.destSlt,d)},reset:function(){EG.DOM.removeChilds(this.srcSlt);EG.DOM.removeChilds(this.destSlt);this.addOptions(this.srcSlt,this.textvalues)},setValue:function(f){this.reset();var e=this.srcSlt.childNodes;for(var d=0;d<e.length;d++){if(EG.Array.has(f,e[d].value)){this._selectOpt(e[d],true)}}this.move(true)},getValue:function(){return this._getValues(this.destSlt,true)},getText:function(){var f=[];var e=this.destSlt.childNodes;for(var d=0;d<e.length;d++){f.push(e[d].innerHTML)}return f},_getTextvalues:function(f){var g=[];var e=f.childNodes;for(var d=0;d<e.length;d++){g.push([e[d].innerHTML,e[d].value])}return g},getSelectedTextvalues:function(){return this._getTextvalues(this.destSlt)},render:function(){b.fit(this);var e=EG.getSize(this.element);var f=EG.getSize(this.dMid).outerWidth;var d=(e.innerWidth-f)/2;b.fit({element:this.dMid,dSize:{height:"100%"},pSize:e});var g=EG.getSize(this.srcSlt);b.fit({element:this.srcSlt,dSize:{width:d,height:"100%"},pSize:e});b.fit({element:this.destSlt,dSize:{width:d,height:"100%"},pSize:e})},getTitle:function(m,l,d){if(m==null){m=[]}var g=this.textvalues;var k="";for(var h=0,e=g.length;h<e;h++){for(var f=0;f<m.length;f++){if(g[h][1]==m[f]){k+=(k!=""?",":"")+g[h][0];break}}}return k}}})})();(function(){EG.define("EG.ui.SelectExpand",["EG.ui.Item"],function(a,b){return{extend:a,config:{onchange:null,textvalues:[],cls:"eg_selectExpand",edit:false,onchangeOnbuild:false,multi:false,useEmpty:false},constructor:function(c){this.callSuper([c])},build:function(){var c=this;var d=EG.clone(this.textvalues);this.textvalues=[];this.builded=false;this.onchangeEvents=[];if(this.onchange!=null){this.bindOnchange(this.onchange)}this.element=EG.CE({tn:"div",cls:this.cls,item:this,cn:[this.dOpts=EG.CE({tn:"div",cls:this.cls+"-opts"})]});this.setOptions(d);this.builded=true},setValue:function(j,f){if(j===null){return}if(f==null){f=true}var d=this.getValue();var h=this.dOpts.childNodes;for(var e=0,c=h.length;e<c;e++){var g=this.multi?EG.Array.has(j,h[e].value):j===h[e].value;this.selectOpt(h[e],g)}if(d!==j&&f){if(!this.builded&&!this.onchangeOnbuild){return}this.doOnChange(j,d)}},refreshValues:function(e){var c=this.getValue();var f=this.dOpts.childNodes;EG.Array.clear(this.vs);var h=[];for(var d=0;d<f.length;d++){if(f[d].childNodes[0].checked){this.vs.push(f[d].value);h.push(f[d].childNodes[1].innerHTML)}}this.iptText.value=h.join(",");var g=this.getValue();if(e){if(!this.builded&&!this.onchangeOnBuild){return}this.doOnChange(g,c)}},selectOpt:function(c,d){if(d){EG.Style.addCls(c,this.cls+"-opt-selected");c.selected=true}else{c.selected=false;EG.Style.removeCls(c,this.cls+"-opt-selected")}},doOnChange:function(e,c){for(var d=0;d<this.onchangeEvents.length;d++){this.onchangeEvents[d].apply(this,[e,c])}},getValue:function(){var e=[];var d=this.dOpts.childNodes;for(var c=0;c<d.length;c++){if(d[c].selected){e.push(d[c].value)}}return this.multi?e:(e.length>0?e[0]:null)},getSelectedIdx:function(){var d=this.getValue();if(this.multi){var c=[];for(var e=0;e<d.length;e++){for(var f=0;f<this.textvalues.length;f++){if(this.textvalues[f][1]===d[e]){c.push(f)}}}return c}else{for(var f=0;f<this.textvalues.length;f++){if(this.textvalues[f][1]===d){return f}}return -1}},setOption:function(c,d){this.textvalues[c]=d;EG.CE({ele:this.dOpts.childNodes[c],value:d[1],innerHTML:d[0]})},setOptions:function(c,d){this.removeOptions();if(this.useEmpty){this.addOption(["全部",""],false)}this.addOptions(c,false);if(this.multi){this.refreshValues(d)}else{if(this.textvalues&&this.textvalues.length>0){this.setValue(this.textvalues[0][1],d)}}this.render()},removeOptions:function(){EG.Array.clear(this.textvalues);EG.DOM.removeChilds(this.dOpts);this.setValue("",false)},bindOnchange:function(c){this.onchangeEvents.push(c)},addOptions:function(d,f){if(f==null){f=true}for(var e=0,c=d.length;e<c;e++){this.addOption(d[e],false)}if(f){if(this.textvalues&&this.textvalues.length>0){this.setValue(this.textvalues[0][1],true)}}},addOption:function(c,e){if(e==null){e=true}var f=EG.CE({pn:this.dOpts,tn:"div",cls:this.cls+"-opt",style:EG.Style.c.dv,value:c[1],innerHTML:c[0],item:this,onmouseover:b._events.option.onmouseover,onmouseout:b._events.option.onmouseout,onclick:b._events.option.onclick});this.textvalues.push(c);if(e){this.setValue(c[1],true)}},removeOption:function(c,e){if(e==null){e=true}var g=this.getSelectedIdx();EG.Array.del(this.textvalues,c);this.dOpts.removeChild(this.dOpts.childNodes[c]);if(g==c){var f=Math.min(c,this.textvalues.length-1);var d=this.textvalues[f][1];this.setValue(d,e)}},getTextByValue:function(d){for(var c=0;c<this.textvalues.length;c++){if(this.textvalues[c][1]==d){return this.textvalues[c][0]}}return null},getText:function(c){return EG.getValue(this.iptText,{getText:true,ignoreEmpty:c})},getTitle:function(g,f,c){for(var e=0,d=this.textvalues.length;e<d;e++){if(this.textvalues[e][1]===g){return this.textvalues[e][0]}}return null},destroy:function(){},render:function(){a.fit(this);a.fit({element:this.dOpts,pSize:EG.getSize(this.element)});var c=EG.getSize(this.dOpts);var f=this.dOpts.childNodes;for(var e=0;e<f.length;e++){var d=f[e];a.fit({element:d,pSize:c,dSize:{height:"100%"}});var g=EG.getSize(d);EG.css(d,"line-height:"+g.innerHeight+"px")}},statics:{_events:{option:{onmouseover:function(){var c=this.item;EG.Style.setCls(this,["opt","opt-over"],c.cls)},onmouseout:function(){var c=this.item;EG.Style.removeCls(this,c.cls+"-opt-over");if(this.selected){EG.Style.addCls(this,c.cls+"-opt-selected")}},onclick:function(h){var g=this.item;var c=g.getValue();if(!g.multi){var f=g.dOpts.childNodes;for(var d=0;d<f.length;d++){g.selectOpt(f[d],f[d]==this)}}else{g.selectOpt(this,!this.selected)}if(!EG.Tools.isPressCtrl(h)){g.doOnChange(g.getValue(),c)}EG.Event.stopPropagation(h)}}}}}})})();(function(){EG.define("EG.ui.Grid",["EG.ui.Item"],function(Item,ME){return{alias:"grid",extend:Item,config:{cls:"eg_grid",rowClasses:["row-a","row-b"],changeRowClassAble:true,selectRowOnclick:true,selectSingleOnclick:false,boxAble:true,seqAble:true,colSelectAble:false,colOrderAble:false,colAdjAble:false,gridFixed:false,columns:null,rowEvents:{},colEvents:{},remotingCallback:null,showHead:true,showFoot:true,renderTo:null,pageSize:30,toolbar:"first pre stat next last size",cellInnerStyle:null,onOrder:null},constructor:function(cfg){ME.load();this.callSuper([cfg])},initItem:function(cfg){this.callSuper("initItem",[cfg]);this._currentPage=0;this._dataSize=0;this._pageCount=0;this._segment=-1;this.colOptAble=false},_ef_row:{changeRow_mouseover:function(e){this.grid.overRow(this,true);if(this.grid.fn_row_mouseover){this.grid.fn_row_mouseover.apply(this,[e])}},changeRow_mouseout:function(e){this.grid.overRow(this,false);if(this.grid.fn_row_mouseout){this.grid.fn_row_mouseout.apply(this,[e])}},selectRow_click:function(e){e=EG.Event.getEvent(e);var cns=this.parentNode.childNodes;if(e.shiftKey&&this.grid.lastIdx!=null){var sIdx=Math.min(this.grid.lastIdx,this.index),bIdx=Math.max(this.grid.lastIdx,this.index);cns=this.parentNode.childNodes;for(var i=0;i<cns.length;i++){this.grid.selectRow(cns[i],false)}for(var i=sIdx;i<=bIdx;i++){this.grid.selectRow(cns[i],true)}}else{this.grid.lastIdx=this.selected?null:this.index;if(!e.ctrlKey){for(var i=0;i<cns.length;i++){if(cns[i]!=this){this.grid.selectRow(cns[i],false)}}}this.grid.selectRow(this,!this.selected)}if(this.grid.clickFn){this.grid.clickFn.apply(this,[e])}}},build:function(){this.element=EG.CE({tn:"div",cn:[this.dMain=EG.CE({tn:"div",style:"width:100%;overflow:hidden;position:relative;"}),this.dFoot=EG.CE({tn:"div",cls:this.cls+"-foot"})],onkeydown:function(){},onkeyup:function(){}});EG.Event.bindUnselect(this.element);this.buildHead();this.buildBody();this.buildFixBody();this.buildFixHead();this.initHead();this.buildToolBar();if(!this.showHead){EG.hide(this.dHead,this.dFixHead)}if(!this.showFoot){EG.hide(this.dFoot)}if(this.changeRowClassAble){this.fn_row_mouseover=this.rowEvents.onmouseover;this.fn_row_mouseout=this.rowEvents.onmouseout;this.rowEvents.onmouseover=(this._ef_row.changeRow_mouseover);this.rowEvents.onmouseout=(this._ef_row.changeRow_mouseout)}if(this.selectRowOnclick){this.clickFn=this.rowEvents.onclick;this.rowEvents.onclick=(this._ef_row.selectRow_click)}if(this.colAdjAble){this.colAdjRulerL=EG.CE({pn:this.element,tn:"div",cls:this.cls+"-adjRuler"});this.colAdjRulerR=EG.CE({pn:this.element,tn:"div",cls:this.cls+"-adjRuler"});EG.hide(this.colAdjRulerL,this.colAdjRulerR)}if(this.colOptAble){this.buildColOpt()}},_fn_selectBox:function(){this.grid.selectRow(this.row,!this.selected)},getTr:function(i){return this.tbBody.childNodes[i]},setData:function(data){var me=this;this.data=data;this.boxes=[];EG.DOM.removeChilds(this.tbBody);EG.DOM.removeChilds(this.tbFixBody);var globalRowNumber=0;var i,j,key;for(i=0;i<this.pageSize;i++){var d=this.data[i];if(!d&&!this.gridFixed){break}var row=EG.CE({pn:this.tbBody,tn:"tr",index:i,grid:this,data:d});var rowFix=EG.CE({pn:this.tbFixBody,tn:"tr",index:i,grid:this,data:d});for(key in me.rowEvents){EG.Event.bindEvent(row,key,me.rowEvents[key])}if(this.rowClasses&&this.rowClasses.length){var rowClassName=this.cls+"-"+this.rowClasses[i%this.pageSize%this.rowClasses.length];EG.setCls(row,rowClassName);EG.setCls(rowFix,rowClassName)}globalRowNumber=this._currentPage*this.pageSize+i;if(this.boxAble){var box=new EG.ui.Box({showText:false,row:row,grid:this,onclick:this._fn_selectBox});EG.CE({pn:rowFix,tn:"td",cls:(this.cls+"-fixCol"),style:"width:30px",cn:[{tn:"div",cls:this.cls+"-fixBodyCellInner",cn:[box]}]});row.box=box}if(this.seqAble){EG.CE({pn:rowFix,tn:"td",cls:this.cls+"-fixCol",style:"text-align:center;vertical-align:middle;width:30px",cn:[{tn:"div",cls:this.cls+"-fixBodyCellInner",innerHTML:(globalRowNumber+1)}]})}for(j=0;j<this.columns.length;j++){var column=this.columns[j];var textlength=column.textlength,textlengthEnd=column.textlengthEnd||"",handle=column.handle,field=column.field,fieldClass=column.fieldClass||"txtcenter",width=column.width,fix=column.fix,showCode=EG.n2d(column.showCode,false);var col,cellInner;col=EG.CE({tn:"td",cls:this.cls+"-bodyCol",cn:[cellInner=EG.CE({tn:"div",cls:this.cls+"-bodyCellInner"})]});if(this.cellInnerStyle){EG.css(cellInner,this.cellInnerStyle)}if(fieldClass){col.className+=(" "+fieldClass)}for(key in this.colEvents){EG.Event.bindEvent(col,key,this.colEvents[key])}var ihtml;if(d){ihtml=null;if(handle){var hr=handle.apply(this,[this.data[i],i,globalRowNumber,column]);if(hr==null){hr=""}if(hr.nodeType){cellInner.appendChild(hr)}else{if(typeof(hr)=="object"&&hr.length!=null){for(var x=0;x<hr.length;x++){if(EG.ui.Item.isItem(hr[x])){cellInner.appendChild(hr[x].getElement())}else{cellInner.appendChild(hr[x])}}}else{cellInner.innerHTML=showCode?EG.Tools.filterSpecChar(hr):hr}}}else{if(this.data[i][field]!=null||!isNaN(this.data[i][field])){ihtml=this.data[i][field];if(ihtml&&textlength!=null&&ihtml.length>textlength){ihtml=ihtml.substr(0,textlength)+textlengthEnd}cellInner.innerHTML=showCode?EG.Tools.filterSpecChar(ihtml):ihtml}else{cellInner.innerHTML=showCode?EG.Tools.filterSpecChar(field):""}}}else{if(this.gridFixed){cellInner.innerHTML="&nbsp;"}}if(fix){rowFix.appendChild(col);EG.setCls(col,"fixCol",this.cls);EG.setCls(cellInner,"fixBodyCellInner",this.cls)}else{row.appendChild(col)}if(width){EG.css(col,"width:"+(column.outerWidth)+"px");EG.css(cellInner,"width:"+(width)+"px")}}}this.fitFixSize()},buildHead:function(){this.dHead=EG.CE({pn:this.dMain,cls:this.cls+"-head",tn:"div",style:"overflow:hidden;",cn:[this.tHead=EG.CE({tn:"table",border:0,cellPadding:0,cellSpacing:0,style:"table-layout:fixed;",cn:[this.tbHead=EG.CE({tn:"tbody"})]})]})},buildBody:function(){var me=this;this.dBody=EG.CE({pn:this.dMain,cls:this.cls+"-body",tn:"div",style:"overflow:auto;",onscroll:function(){me.dHead.scrollLeft=this.scrollLeft;me.dFixBody.style.top=((-this.scrollTop)+me.dFixHead.clientHeight)+"px"},cn:[this.tBody=EG.CE({tn:"table",border:0,cellPadding:0,cellSpacing:0,style:"table-layout:fixed;",cn:[this.tbBody=EG.CE({tn:"tbody"})]})]})},buildFixHead:function(){this.dFixHead=EG.CE({pn:this.dMain,cls:this.cls+"-fixHead",tn:"div",style:"position:absolute;left:0px;top:0px",cn:[{tn:"table",style:"table-layout:fixed;",border:0,cellPadding:0,cellSpacing:0,cn:[this.tbFixHead=EG.CE({tn:"tbody"})]}]})},buildFixBody:function(){this.dFixBody=EG.CE({pn:this.dMain,cls:this.cls+"-fixBody",tn:"div",style:"position:absolute;overflow:hidden;left:0px;top:0px",cn:[{tn:"table",style:"table-layout:fixed;",border:0,cellPadding:0,cellSpacing:0,cn:[this.tbFixBody=EG.CE({tn:"tbody"})]}]})},render:function(){Item.fit(this);var size=EG.getSize(this.element);var mainHeadsize=EG.getSize(this.dHead);var footHeight=this.showFoot?EG.getSize(this.dFoot).innerHeight:0;var mainHeight=size.innerHeight-footHeight;var mainBodyHeight=mainHeight-mainHeadsize.outerHeight;var pSize=EG.getSize(this.element.parentNode);EG.css(this.dMain,"height:"+mainHeight+"px;width:"+size.innerWidth+"px");EG.css(this.dBody,"height:"+mainBodyHeight+"px");var tableWidth=0;for(var i=0;i<this.columns.length;i++){var column=this.columns[i];var width=column.width;EG.css(column.dHeadInner,"width:"+width+"px");var w=EG.getSize(column.dHeadInner).outerWidth;EG.css(column.tdHead,"width:"+w+"px");column.outerWidth=w;tableWidth+=w}EG.css(this.tHead,"width:"+(tableWidth+ME.appendHeadWidth)+"px");this.fitFixSize()},fitFixSize:function(){var i,il;if(this.tbFixBody.childNodes.length>0){var fixBodyTr=this.tbFixBody.childNodes[0];for(i=0,il=fixBodyTr.childNodes.length;i<il;i++){var cSize=EG.getSize(fixBodyTr.childNodes[i]);EG.css(this.tbFixHead.childNodes[0].childNodes[i],"width:"+cSize.innerWidth+"px")}for(i=0,il=this.tbBody.childNodes.length;i<il;i++){if(this.tbBody.childNodes[i].childNodes.length==0||this.tbFixBody.childNodes[i].childNodes.length==0){continue}var td=this.tbBody.childNodes[i].childNodes[0];var tdFix=this.tbFixBody.childNodes[i].childNodes[0];var s=EG.getSize(td);var sFix=EG.getSize(tdFix);if(sFix.innerHeight>s.innerHeight){EG.css(td,"height:"+sFix.innerHeight+"px")}else{EG.css(tdFix,"height:"+s.innerHeight+"px")}}}var fixHeadsize=EG.getSize(this.dFixHead);EG.css(this.dBody,"margin-left:"+fixHeadsize.outerWidth+"px");EG.css(this.dHead,"margin-left:"+fixHeadsize.outerWidth+"px");this.dFixBody.style.top=((-this.dBody.scrollTop)+this.dFixHead.clientHeight)+"px"},initHead:function(){var me=this;var hrFix=EG.CE({pn:this.tbFixHead,tn:"tr",cls:me.cls+"-head"});var hr=EG.CE({pn:this.tbHead,tn:"tr",cls:me.cls+"-head"});if(this.boxAble){EG.CE({pn:hrFix,tn:"td",cls:this.cls+"-headCol",style:"text-align:center;",cn:[{tn:"div",cls:this.cls+"-fixHeadCellInner",style:"",cn:[this.boxHead=new EG.ui.Box({showText:false,onselect:function(){me.selectAllBox(!this.selected)}})]}]})}if(this.seqAble){EG.CE({pn:hrFix,tn:"td",cls:this.cls+"-headCol",style:"text-align:center;",cn:[{tn:"div",cls:this.cls+"-fixHeadCellInner",style:"width:12px",innerHTML:"&nbsp;"}]})}for(var i=0;i<this.columns.length;i++){var column=this.columns[i];var header=column.header,headerEvent=column.headerEvent,headerStyle=column.headerStyle,width=column.width,fix=column.fix,order=column.order,field=column.field;var col=EG.CE({pn:fix?hrFix:hr,tn:"td",cls:me.cls+"-headCol",style:"white-space:nowrap",me:this});col.dContent=EG.CE({pn:col,tn:"div",cls:me.cls+"-headCellInner",me:this});if(typeof(header)=="string"){col.dContent.innerHTML=header}else{col.dContent.appendChild(header)}if(me.colOrderAble&&order){col.orderName=typeof(order)=="boolean"?field:order;EG.Event.bindEvent(col,"click",ME._events.head.onclick)}if(this.colAdjAble){col.dAdj=EG.CE({pn:col,tn:"div",innerHTML:"&nbsp;",className:me.cls+"-head_adj",ondblclick:function(){alert("待实现，双击调整宽度")},onmousedown:function(){me.startAdjColWidth(this.parentNode)}})}column.tdHead=col;column.dHeadInner=col.dContent}EG.CE({pn:hr,tn:"td",cls:this.cls+"-headCol",cn:[{tn:"div",cls:this.cls+"-headCellInner",style:"width:"+ME.appendHeadWidth+"px"}]})},overRow:function(tr,over){if(typeof(tr)=="number"){tr=this.tbBody.childNodes[tr]}if(tr.selected){return}if(over){if(!tr.oldClass){tr.oldClass=tr.className}EG.setCls(tr,"row-over",this.cls)}else{EG.setCls(tr,tr.oldClass)}},selectRow:function(tr,selected,single){if(single==null){single=this.selectSingleOnclick}if(typeof(tr)=="number"){tr=this.tbBody.childNodes[tr]}tr.selected=selected;if(tr.selected){if(!tr.oldClass){tr.oldClass=tr.className}EG.setCls(tr,"row-selected",this.cls)}else{EG.setCls(tr,tr.oldClass)}if(this.boxAble){tr.box.select(tr.selected)}if(single){var cns=tr.parentNode.childNodes;for(var i=0;i<cns.length;i++){if(cns[i]!=tr&&cns[i].selected){cns[i].selected=false;cns[i].box.select(false);EG.setCls(cns[i],cns[i].oldClass)}}}},selectAllBox:function(selected){var trs=this.tbBody.childNodes;for(var i=0,l=trs.length;i<l;i++){this.selectRow(trs[i],selected)}},getSelectIdx:function(){var sd=[];var trs=this.tbBody.childNodes;for(var i=0,l=trs.length;i<l;i++){if(trs[i].selected){sd.push(i)}}return sd},getSelectData:function(key){var sd=[];var trs=this.tbBody.childNodes;for(var i=0,l=trs.length;i<l;i++){if(trs[i].selected){var d=trs[i]["data"];sd.push(key?d[key]:d)}}return sd},setDataSize:function(dataSize){this._dataSize=dataSize;this._pageCount=Math.ceil(this._dataSize/this.pageSize);if(this._currentPage+1>this._pageCount){this._currentPage=this._pageCount-1}if(this._currentPage<0){this._currentPage=0}for(var i=0;i<ME.handler.gridChangedAction.length;i++){if(this[ME.handler.gridChangedAction[i]]&&this[ME.handler.gridChangedAction[i]] instanceof Function){this[ME.handler.gridChangedAction[i]]()}}},fixColumn:function(colIdx){if(typeof(colIdx)=="string"){colIdx=this.getColumnIdx(colIdx)}var cns=this.tbBody.childNodes;for(var i=cns.length-1;i>=0;i++){EG.CE({pn:this.tbFixBody,tn:"tr",cn:cns[i].childNodes[colIdx]})}EG.CE({pn:this.tbFixHead,tn:"tr",cn:this.tbHead.childNodes[0].childNodes[colIdx]})},getColumnIdx:function(header){var startIdx=0;if(this.boxAble){startIdx++}if(this.seqAble){startIdx++}for(var i=0;i<this.columns.length;i++){if(header==this.columns[i]["header"]){return i+startIdx}}throw new Error("EG.ui.Grid#getColumnIdx:未找到对应列")},getData:function(){return this.data},go:function(pageIdx){if(pageIdx>this._pageCount-1){pageIdx=this._pageCount-1}if(pageIdx<0){pageIdx=0}this._currentPage=pageIdx;if(this.remotingCallback){this.remotingCallback.apply(this["remotingCallbackSrc"]||this,[pageIdx,this])}if(this.boxAble){this.boxHead.select(false,true)}},prePage:function(){this.go(this._currentPage-1)},nextPage:function(){this.go(this._currentPage+1)},firstPage:function(){this.go(0)},lastPage:function(){this.go(this._pageCount-1)},curPage:function(){this.go(this._currentPage)},buildColOpt:function(){this.dColOtp=EG.CE({pn:EG.getBody(),tn:"div",cls:"pagingGrid_dColOpt",style:"display:none",onmouseleave:function(){EG.hide(this)}});var overFn=function(){this.style.backgroundColor="white"};var outFn=function(){this.style.backgroundColor=""};if(this.colOrderAble){EG.CE({pn:this.dColOtp,tn:"div",innerHTML:"正序",cls:"ele",onclick:function(){},onmouseover:overFn,onmouseout:outFn});EG.CE({pn:this.dColOtp,tn:"div",innerHTML:"倒序",cls:"ele",onclick:function(){},onmouseover:overFn,onmouseout:outFn})}if(this.colSelectAble){EG.CE({pn:this.dColOtp,tn:"div",grid:this,innerHTML:"列",cls:"ele",onmouseenter:function(){var p=EG.Tools.getElementPos(this);EG.css(this.grid.dColSelect,{top:p.y+"px",left:(p.x+this.grid.dColOtp.clientWidth-20)+"px",display:""})},onmouseover:overFn,onmouseout:outFn});this.dColSelect=EG.CE({pn:EG.getBody(),tn:"div",cls:"pagingGrid_dColSelect",style:"display:none",onmouseleave:function(){EG.hide(this)}});for(var i=0,l=this.columns.length;i<l;i++){var column=this.columns[i];var b=new EG.ui.Box({title:column.header,onselect:function(){}});this.dColSelect.appendChild(b.getElement())}}},buildToolBar:function(){var tools=this.toolbar.split(/\s+/);for(var i=0;i<tools.length;i++){var method=ME.handler.toolsMap[tools[i]];var tool;if(!method){continue}tool=eval("this."+method+"()");this.dFoot.appendChild(tool)}},getOptions:function(){return EG.CE({tn:"a",href:"javascript:void(0)",innerHTML:"选择",onclick:function(){alert("1")}})},getFirstPage:function(){var me=this;return EG.CE({tn:"a",href:"javascript:void(0)",cls:this.cls+"-firstPage",onclick:function(){me.firstPage();return false}})},getPrePage:function(){var me=this;return EG.CE({tn:"a",href:"javascript:void(0)",cls:this.cls+"-prePage",onclick:function(){me.prePage();return false}})},getNextPage:function(){var me=this;return EG.CE({tn:"a",href:"javascript:void(0)",cls:this.cls+"-nextPage",onclick:function(){me.nextPage();return false}})},getLastPage:function(){var me=this;return EG.CE({tn:"a",href:"javascript:void(0)",cls:this.cls+"-lastPage",onclick:function(){me.lastPage();return false}})},getState:function(){var me=this;var span=EG.CE({tn:"span",cls:this.cls+"-state",cn:[{tn:"a",cls:this.cls+"-gotoPage",onclick:function(){me.go(parseInt(me.currentPageValue.value)-1)}},{tn:"span",innerHTML:"第"},this.currentPageValue=EG.CE({tn:"input",type:"text",size:2,style:""}),{tn:"span",innerHTML:"/"},this.pageCountValue=EG.CE({tn:"span",innerHTML:this._pageCount}),{tn:"span",innerHTML:"页"}],style:""});return span},getRecordSize:function(){var me=this;return EG.CE({tn:"span",cls:this.cls+"-recordSize",cn:[{tn:"span",innerHTML:"每页"},this.sPageSize=EG.CE({tn:"span",innerHTML:this.pageSize,onclick:function(e){e=EG.Event.getEvent(e);if(e.ctrlKey){var ps=prompt("RS");if(ps){me.pageSize=parseInt(ps);me.go(0)}}}}),{tn:"span",innerHTML:"条",style:"margin-right:5px"},{tn:"span",innerHTML:"共"},this.sizeValue=EG.CE({tn:"span",innerHTML:this._dataSize}),{tn:"span",innerHTML:"条"}]})},_changeState:function(){EG.setValue(this.currentPageValue,this._currentPage+1);this.pageCountValue.innerHTML=this._pageCount;this.sizeValue.innerHTML=this._dataSize;this.sPageSize.innerHTML=this.pageSize},dispose:function(){for(var p in this){if(this[p] instanceof Function){this[p]=function(){alert("The object has been released.")}}else{this[p]=null}}},startAdjColWidth:function(td){ME.adjAim=td;var me=td.me;var p=EG.Tools.getElementPos(ME.adjAim,me.getElement(),false);var h=(me.dHead.clientHeight+me.dBody.clientHeight)+"px";EG.css(me.colAdjRulerL,{top:p.y+"px",left:p.x+"px",height:h});EG.css(me.colAdjRulerR,{top:p.y+"px",left:(p.x+td.offsetWidth)+"px",height:h});EG.show(me.colAdjRulerL,me.colAdjRulerR);ME.adjIng=true},endAdjColWidth:function(){EG.hide(ME.colAdjRulerL,ME.colAdjRulerR);ME.adjAim.style.width=parseInt(EG.String.removeEnd(ME.colAdjRulerR.style.left,"px"))-parseInt(EG.String.removeEnd(ME.colAdjRulerL.style.left,"px"))+"px";ME.adjIng=false},statics:{_events:{head:{onclick:function(){var me=this.me;if(me.curOrderCol&&me.curOrderCol!=this){EG.Style.removeCls(me.curOrderCol.dContent,me.cls+"-head_order_asc");EG.Style.removeCls(me.curOrderCol.dContent,me.cls+"-head_order_desc");me.curOrderDesc="";me.curOrderName="";me.curOrderCol=null}me.curOrderCol=this;me.curOrderName=this.orderName;if(me.curOrderDesc=="desc"){me.curOrderDesc="asc"}else{if(me.curOrderDesc=="asc"){me.curOrderDesc="";me.curOrderName="";me.curOrderCol=null}else{me.curOrderDesc="desc"}}EG.Style.removeCls(this.dContent,me.cls+"-head_order_asc");EG.Style.removeCls(this.dContent,me.cls+"-head_order_desc");if(me.curOrderDesc){EG.Style.addCls(this.dContent,me.cls+"-head_order_"+me.curOrderDesc)}if(me.onOrder){me.onOrder.apply(me.onOrderSrc||me,[this.orderName,me.curOrderDesc])}}}},loaded:false,load:function(){if(ME.loaded){return}ME.loaded=true},handler:{grids:[],count:0,toolsMap:{option:"getOptions",pre:"getPrePage",next:"getNextPage",first:"getFirstPage",last:"getLastPage",stat:"getState",size:"getRecordSize",skip:"getSkip"},gridChangedAction:["_changeState"]},adjAim:null,adjIng:false,appendHeadWidth:1000}}})})();EG.define("EG.ui.InputGrid",["EG.ui.Item"],function(a,b){return{extend:a,alias:"inputGrid",config:{cls:"eg_inputgrid",columnsConfig:null,afterRowDelete:null,afterRowAdd:null,seqAble:true,addAble:true,readLineHeight:22,onClickAdd:null,onClickDel:null,seq_ig_name:null,onvalidate:null},constructor:function(c){this.setPropValueOnRead=true;this.callSuper([c])},build:function(){if(this.seqAble){EG.Array.insert(this.columnsConfig,0,{ig_name:(this.seq_ig_name||"idx"),ig_title:"序号",xtype:"label",width:100,height:30})}this.element=EG.CE({tn:"div"});if(this.addAble){this.element.appendChild(this.dBar=EG.CE({tn:"div",cn:[new EG.ui.Button({text:"添加",click:this.onClickAdd||this.addRow,clickSrc:this}),new EG.ui.Button({text:"删除",click:this.onClickDel||this.deleteRow,clickSrc:this})]}))}this.element.appendChild(this.dMain=EG.CE({tn:"div",cls:this.cls+"-main",cn:[this.table=EG.CE({tn:"table",cn:[this.thead=EG.CE({tn:"thead",cn:[{tn:"tr",cls:this.cls+"-head"}]}),this.tbody=EG.CE({tn:"tbody"}),this.tfoot=EG.CE({tn:"tfoot"})]})]}));this.buildHead()},buildHead:function(){var d=this.thead.childNodes[0];for(var e=0;e<this.columnsConfig.length;e++){var c=this.columnsConfig[e];var f=!!c.hidden;EG.CE({pn:d,tn:"td",innerHTML:f?"":c.ig_title,style:"width:"+c.width+"px"})}},addRow:function(){var k=this;var m=this.cls+"-row";var f=this.cls+"-rowSelected";var j=EG.CE({pn:this.tbody,tn:"tr",cls:m,onclick:function(){if(k.selectedRow!=null){EG.Style.removeCls(k.selectedRow,f)}k.selectedRow=this;EG.Style.addCls(this,f)}});for(var e=0;e<this.columnsConfig.length;e++){var c=this.columnsConfig[e];var h=c.typeCfg||c;var g=!!h.hidden;var l=c.xtype instanceof Function?new c.xtype(h):a.create(c.xtype,h);var d=EG.CE({pn:j,tn:"td",cn:[l]});if(g){EG.css(d,"overflow:hidden")}d.item=l;d.ig_name=c.ig_name;l.hidden=g;l.pItem=this;l.width=c.width;l.height=c.height;l.render();EG.css(l,"position:relative")}if(this.afterRowAdd){this.afterRowAdd.apply(this,[j,this])}if(this.seqAble){this.refreshSeq()}return j},getCellPos:function(g){var c=this.tbody.childNodes;for(var e=0;e<c.length;e++){var f=c[e].childNodes;for(var d=0;d<f.length;d++){var h=f[d];if(h.item==g){return[e,d]}}}return null},refreshSeq:function(){var c=this.tbody.childNodes;for(var d=0;d<c.length;d++){var e=c[d].childNodes[0];e.item.setValue(d+1)}},deleteRow:function(){if(!this.selectedRow){return}EG.DOM.remove(this.selectedRow);if(this.afterRowDelete){this.afterRowDelete.apply(this,[this.selectedRow,this])}if(this.seqAble){this.refreshSeq()}this.selectedRow=null},setValue:function(l,g){if(l==null){return}EG.DOM.removeChilds(this.tbody);var k=l;for(var f=0;f<k.length;f++){var n=k[f];var h=this.addRow();var e=h.childNodes;for(var c=0;c<e.length;c++){var m=e[c].item;m.setValue(n[e[c].ig_name],n)}}},setRowValue:function(f,h){var g;if(typeof(f)!="number"){g=f.parentNode.parentNode;f=EG.DOM.getIdx(g)}g=this.tbody.childNodes[f];var d=g.childNodes;var i={};for(var c=0;c<d.length;c++){var e=d[c].item;e.setValue(h[d[c].ig_name])}},getRowValue:function(f){var g;if(typeof(f)!="number"){g=f.parentNode.parentNode;f=EG.DOM.getIdx(g)}g=this.tbody.childNodes[f];var d=g.childNodes;var h={};for(var c=0;c<d.length;c++){var e=d[c].item;h[d[c].ig_name]=e.getValue()}return h},getValue:function(){var c=this.tbody.childNodes;var l=[];for(var f=0;f<c.length;f++){var g=c[f].childNodes;var k={};for(var e=0;e<g.length;e++){var h=g[e].item;k[g[e].ig_name]=h.getValue()}l.push(k)}return l},removeAll:function(){EG.DOM.removeAllRows(this.tbody)},getColumnItem:function(d,g){if(typeof(d)!="number"){var e=d.parentNode.parentNode;d=EG.DOM.getIdx(e)}if(typeof(g)=="string"){for(var c=0;c<this.columnsConfig.length;c++){var f=this.columnsConfig[c];if(f.ig_name==g){g=c;break}}if(typeof(g)=="string"){throw new Error("未找到对应列:"+g)}}return this.tbody.childNodes[d].childNodes[g].item},render:function(){a.fit(this);var f=EG.getSize(this.element);var h=this.addAble?EG.getSize(this.dBar):{outerHeight:0};var l=f.innerHeight-h.outerHeight;EG.css(this.dMain,"height:"+l+"px");var c=this.tbody.childNodes;for(var e=0;e<c.length;e++){var g=c[e].childNodes;for(var d=0;d<g.length;d++){var k=g[d].item;k.render();EG.css(k,"position:relative")}}},getTitle:function(p,h){var e,f;if(!p){return""}var d=this.columnsConfig;var t=EG.CE({tn:"table",cls:"eg_inputgrid-read"});var l=EG.CE({pn:t,tn:"thead"});var k=EG.CE({pn:t,tn:"tbody"});var o=EG.CE({pn:l,tn:"tr"});for(f=0;f<d.length;f++){e=d[f];EG.CE({pn:o,tn:"td",width:e.width,innerHTML:e.ig_title})}for(var g=0;g<p.length;g++){var q=p[g];var n=EG.CE({pn:k,tn:"tr"});for(f=0;f<d.length;f++){e=d[f];var m=e.xtype||e.type;var s=this.tbody.childNodes[g].childNodes[f].item;var c=(s&&s.getTitle)?s.getTitle(q[e.ig_name],q):q[e.ig_name];if(c==null){c=""}if(this.seqAble){if(f==0){c=(g+1)}}if(EG.DOM.isElement(c)){EG.CE({pn:n,tn:"td",cn:[c]})}else{EG.CE({pn:n,tn:"td",innerHTML:c})}}}return t},validate:function(){var k=this.getValue();if(this.unnull){if(!k||k.length==0){return"不能为空"}}if(k){for(var f=0;f<k.length;f++){var d=k[f];for(var e=0;e<this.columnsConfig.length;e++){var h=this.columnsConfig[e];if(h.unnull){var c=d[h.ig_name];if(c!==0&&!c){return h.ig_title+"不能为空"}}}}}if(this.onvalidate){var g=this.onvalidate(k);if(g){return g}}}}});(function(){EG.define("EG.ui.Form",["EG.ui.Container"],function(a,b){return{alias:"form",extend:a,config:{editable:true,labelWidth:40,validateAble:true,layout:"table",realForm:false,action:null,isUpload:false,target:null,useTopCls:false},build:function(){if(this.isUpload){this.realForm=true}this.element=EG.CE({tn:this.realForm?"form":"div"});if(this.isUpload){this.element.encoding="multipart/form-data"}if(this.realForm){this.element.method="POST"}},constructor:function(c){c=b.filterConfig(c,c);this.callSuper([c])},getFormItem:function(c){return EG.ui.Form.getFormItem(this,c)},getFormItems:function(){return EG.ui.Form.getFormItems(this)},_displayFormItems:function(e,d){for(var c=0;c<e.length;c++){var f=this.getFormItem(e[c]);if(f==null){continue}f.setHidden(!d)}this.render()},showFormItems:function(){this._displayFormItems(arguments,true)},hideFormItems:function(){this._displayFormItems(arguments,false)},getData:function(){var g={};var e=this.getFormItems();for(var d=0,c=e.length;d<c;d++){var f=e[d];var h=f.getProp();if(h&&h.onGetData){h.onGetData(g,f.name,this)}else{g[f.name]=f.getValue()}}return g},setData:function(g,f){if(g==null){throw new Error("EG.ui.Form.prototype#setData:data不能为null")}var e=this.getFormItems();if(!f){f={}}var k=EG.n2d(f.setDefVal,false);var h=EG.n2d(f.ignoreUnExist,false);for(var d=0,c=e.length;d<c;d++){var j=e[d];if(h&&!EG.Object.hasKey(g,j.name)){continue}j.setValue(g[j.name],g);if(k){j.defValue=g[j.name]}}},submit:function(){var d=this.getFormItems();for(var c=0;c<d.length;c++){var e=d[c];e.setSubmitValue(e.getValue())}this.element.action=this.action;if(this.target){this.element.target=this.target}this.element.submit()},reset:function(){var e=this.getFormItems();for(var d=0,c=e.length;d<c;d++){var f=e[d];if(f.defValue!=null){f.setValue(f.defValue)}else{f.setValue(null)}}},clearData:function(){var e=this.getFormItems();for(var d=0,c=e.length;d<c;d++){var f=e[d];f.setValue("",{})}},validate:function(h){var n=true;var k=this.getFormItems();var j=null;var e=null;var d=null;for(var f=0,l=k.length;f<l;f++){var o=k[f];if(o.hidden){continue}if(o.editable){var c=o.validate();n=(c&&n);if(!n){if(!d){d=o}var g=o;var m=null;while(j==null&&(g=g.pItem)!=null){if(g==this){break}if(g.xtype=="tabPanel"){j=g;e=g.getPanelIdx(m);d=o;break}m=g}if(!h){break}}}}if(j!=null){j.select(e)}if(d){EG.Tip.error(d.vErrorMsg,d.rDiv)}return n},setEditable:function(e){var f=this.getFormItems();for(var d=0,c=f.length;d<c;d++){var g=f[d];g.setEditable(e)}},statics:{getFormItem:function(c,e){for(var f=0,d=c.items.length;f<d;f++){var g=c.items[f];if(g instanceof EG.ui.FormItem){if(g.name==e){return g}}else{if(g.isContainer){var h=EG.ui.Form.getFormItem(g,e);if(h){return h}}}}return null},getFormItems:function(d,c){if(!c){c=[]}for(var f=0,e=d.items.length;f<e;f++){var g=d.items[f];if(g instanceof EG.ui.FormItem){c.push(g)}else{if(g.isContainer){EG.ui.Form.getFormItems(g,c)}}}return c},filterConfig:function(g,c){var k=c.labelWidth;var j=c.cls;var e=c.itemHeight;var d=g.xtype=="tabPanel";var h=g.items;if(!h){return g}for(var f=0;f<h.length;f++){var l=h[f];if(!EG.isLit(l)){continue}if(d){l.panel["xtype"]="panel";this.filterConfig(l.panel,c)}else{if(!l.xtype){l.xtype="formItem"}if(l.xtype=="formItem"){if(l.labelWidth==null&&k!=null){l.labelWidth=k}if(!l.cls&&j&&c.useTopCls){l.cls=j+"-item"}if(l.height==null&&e!=null){l.height=e}}else{b.filterConfig(l,c)}}}return g}}}})})();(function(){EG.define("EG.ui.Fieldset",["EG.ui.Container","EG.ui.Item"],function(a,b,c){return{alias:"fieldset",extend:a,config:{cls:"eg_fieldset",showTitle:true,showExpand:true,showBorder:true,layout:"table",bodyPadding:null,title:null,bodyStyle:null},constructor:function(d){var e=this;this.callSuper([d]);if(!this.showTitle){EG.Style.isHide(this.dTitle)}if(!this.showExpand){EG.Style.isHide(this.dCollapse)}if(!this.showBorder){EG.setCls(this.dBody,"dBody-noborder",e.cls)}if(this.bodyPadding!=null){EG.css(this.dBody,"padding:"+this.bodyPadding+"px")}},build:function(){this.element=EG.CE({tn:"fieldset",cls:this.cls,cn:[this.legend=EG.CE({tn:"legend",cls:this.cls+"-legend",cn:[this.dCollapse=EG.CE({tn:"div",cls:this.cls+"-dCollapse "+this.cls+"-dCollapse-show",onclick:function(){var f=EG.Style.isHide(me.dBody);var e=me.cls+"-dCollapse "+me.cls+"-dCollapse-";if(f){EG.show(me.dBody.getElement());EG.setCls(this,"hide",me.cls)}else{EG.hide(me.dBody.getElement());EG.setCls(this,"show",me.cls)}}}),this.dTitle=EG.CE({tn:"div",cls:this.cls+"-dTitle",innerHTML:this.title})]}),this.dBody=EG.CE({tn:"div",cls:this.cls+"-dBody"})]});if(this.bodyStyle){EG.css(this.dBody,this.bodyStyle)}},getItemContainer:function(){return this.dBody},setInnerHeight:function(d){b.pack({pEle:this.dBody,height:d});b.pack({pEle:this.element,height:EG.getSize(this.dBody).outerHeight+EG.getSize(this.legend).outerHeight})},render:function(){b.fit(this);var d=EG.getSize(this.element);b.fit({element:this.dBody,pSize:d,dSize:{width:"100%",height:d.innerHeight-EG.getSize(this.legend).outerHeight}});if(this.items.length>0){this.doLayout()}}}})})();(function(){EG.define("EG.ui.FormItem",["EG.ui.Item"],function(a,b){return{extend:a,alias:"formItem",config:{pos:null,editable:true,name:null,title:null,type:null,typeClass:null,pre:null,after:null,cls:"eg_form-item",labelWidth:60,width:"100%",height:28,validateConfig:null,unnull:false,defValue:null,typeCfg:null,showLeft:true,readLineHeight:22,readStyle:null,showCode:false,labelStyle:null,handle:null,readClick:null,setRead:null,vldType:null},constructor:function(c){this.cacheValue=null;this.prop=null;this.cfg=c;this.callSuper([c]);this.setEditable(this.editable,true);this.setValidate(this.validateConfig);if(this.defValue!=null){this.setValue(this.defValue)}},build:function(){var c=this;this.element=EG.CE({tn:"div",cls:this.cls,cn:[this.lDiv=EG.CE({tn:"div",cls:this.cls+"-dL",item:this,style:EG.unnull(this.labelStyle,""),cn:[this.dStar=EG.CE({tn:"div",cls:this.cls+"-star",item:this,innerHTML:" * "}),this.dTitle=EG.CE({tn:"div",cls:this.cls+"-title",item:this,innerHTML:this.title})]}),this.rDiv=EG.CE({tn:"div",cls:this.cls+"-dR",cn:[this.dPre=EG.CE({tn:"div",cls:this.cls+"-pre",item:this,style:EG.Style.c.dv}),this.dProp=EG.CE({tn:"div",cls:this.cls+"-prop",item:this,cn:[this.elementRead=EG.CE({tn:"div",item:this,cls:this.cls+"-read"})],style:EG.Style.c.dv}),this.dAfter=EG.CE({tn:"div",cls:this.cls+"-after",item:this,style:EG.Style.c.dv}),this.dError=EG.CE({tn:"div",cls:this.cls+"-error",item:this,style:EG.Style.c.dv})]})]});EG.hide(this.dPre,this.dAfter,this.dError);if(this.readStyle){EG.css(this.elementRead,this.readStyle)}if(this.readClick){this.elementRead.onclickSrc=this;EG.Event.bindEvent(this.elementRead,"onclick",this.readClick);EG.css(this.elementRead,"text-decoration:underline;color:blue")}if(this.type=="upload"){this.iptSub=EG.CE({pn:this.rDiv,tn:"input",type:"hidden",name:this.name,style:"display:none"})}if(this.after){this._renderAfter=false;if(typeof(this.after)=="string"){this.dAfter.innerHTML=this.after}else{if(EG.DOM.isElement(this.after)){this.dAfter.appendChild(this.after)}else{if(this.after.getElement){this.dAfter.appendChild(this.after.getElement());this._renderAfter=true}else{if(typeof(this.after)=="function"){this.after.apply(this,[this.dAfter])}}}}EG.show(this.dAfter)}if(this.pre){this._renderPre=false;if(typeof(this.pre)=="string"){this.dPre.innerHTML=this.pre}else{if(EG.DOM.isElement(this.pre)){this.dPre.appendChild(this.pre)}else{if(this.pre.getElement){this.dPre.appendChild(this.pre.getElement());this._renderPre=true}else{if(typeof(this.pre)=="function"){this.pre.apply(this,[this.dPre])}}}}EG.show(this.dPre)}this.readPropClass();this.buildProp();EG.bindEvent(this.rDiv,"mouseover",function(){if(c.vErrorMsg){EG.Tip.error(c.vErrorMsg,this)}});EG.bindEvent(this.rDiv,"mouseout",function(d){EG.Tip.close();EG.Event.stopPropagation(d)})},setSubmitValue:function(c){if(this.iptSub){this.iptSub.value=c}},render:function(){var g;a.fit(this);if(!this.unnull||!this.editable){EG.hide(this.dStar)}else{EG.show(this.dStar)}var d=EG.getSize(this.element);var f;if(this.showLeft){a.fit({element:this.lDiv,dSize:{width:this.labelWidth,height:"100%"},pSize:d});var h=EG.getSize(this.lDiv).innerHeight;EG.css(this.lDiv,"line-height:"+h+"px");EG.show(this.lDiv);f=EG.getSize(this.lDiv).outerWidth}else{EG.hide(this.lDiv);f=0}a.fit({element:this.rDiv,dSize:{width:d.innerWidth-f,height:"100%"},pSize:d});var e=EG.getSize(this.rDiv);var c=e.innerWidth;if(this.after){if(this._renderAfter){this.after.render()}g=EG.getSize(this.dAfter);c=c-g.outerWidth;EG.css(this.dAfter,"width:"+g.innerWidth+"px")}if(this.pre){if(this._renderPre){this.pre.render()}g=EG.getSize(this.dPre).outerWidth;c=c-g.outerWidth;EG.css(this.dPre,"width:"+g.innerWidth+"px")}a.fit({element:this.dProp,dSize:{width:c,height:"100%"},pSize:e});if(this.editable){if(this.prop&&this.prop.render){if(this.prop.width==null){this.prop.width=c}this.prop.height=e.innerHeight;this.prop.render()}}else{a.fit({element:this.elementRead,pSize:e});g=EG.getSize(this.elementRead);EG.css(this.elementRead,"line-height:"+(EG.unnull(this.readLineHeight,g.innerHeight))+"px");if(this.prop.afterRenderRead){this.prop.afterRenderRead()}}},renderOuter:function(){a.fit(this);this.saveOuterSize();this._outerSize=this.getSize()},setEditable:function(c,d){if(this.editable==c&&d!=true){return}this.editable=c;if(this.editable){EG.hide(this.elementRead);EG.show(this.propElement)}else{EG.hide(this.dStar,this.propElement);EG.show(this.elementRead);EG.Style.removeCls(this.lDiv,this.cls+"-error")}if(this.cacheValue||this.cacheData){this.setValue(this.cacheValue,this.cacheData)}this.render()},setUnnull:function(d,c){if(c==null){c=true}this.unnull=d;if(c){this.render()}},readPropClass:function(){if(!this.typeClass){if(typeof(this.type)=="string"){this.typeClass=EG._defaultLoader.find(this.type);if(!this.typeClass){var c="EG.ui."+EG.Word.first2Uppercase(this.type);this.typeClass=EG._defaultLoader.find(c)}}else{if(typeof(this.type)=="function"){this.typeClass=this.type}}}if(!this.typeClass){throw new Error("无法识别类型"+this.type)}},changeProp:function(d,c){if(typeof(d)=="function"){this.typeClass=d}else{this.typeClass=null}this.typeCfg=c;this.type=d;EG.DOM.remove(this.propElement);this.readPropClass();this.buildProp()},buildProp:function(){var c=this.typeCfg?this.typeCfg:this.cfg;c.formItem=this;c.unnull=this.unnull;this.prop=new this.typeClass(c);this.prop.formItem=this;this.propElement=this.prop.getElement();this.dProp.appendChild(this.propElement)},getForm:function(){var c=this.pItem;while(c&&!(c instanceof EG.ui.Form)){c=c.pItem}return(c instanceof EG.ui.Form)?c:null},setValue:function(f,e){f=this.setValueBefore(f,e);this.cacheData=e;this.cacheValue=f;var c=this.typeCfg||this.cfg;if(this.editable||this.prop.setPropValueOnRead){this.prop.setValue(this.cacheValue,e)}if(!this.editable){var d=this.prop.getTitle?this.prop.getTitle(f,e,c):f;if(this.setRead||this.prop.setRead){(this.setRead||this.prop.setRead).apply(this,[this.elementRead,f,e,c])}else{if(EG.DOM.isElement(d)){EG.DOM.removeChilds(this.elementRead);this.elementRead.appendChild(d)}else{EG.setValue(this.elementRead,d)}}}},setValueBefore:function(d,c){return d},getValue:function(){var c=null;if(this.editable){c=this.prop.getValue()}else{c=this.cacheValue}c=this.getValueAfter(c);return c},getValueAfter:function(c){return c},getProp:function(){if(this.prop){return EG.unnull(this.prop.prop||this.prop)}},setValidate:function(c){this.validateConfig=c},validate:function(){this.vErrorMsg=null;EG.Style.removeCls(this.lDiv,this.cls+"-error");if(this.prop.validate){var e=this.prop.validate();this.vErrorMsg=e?(this.title+e):null}else{var f=this.prop.getValue();if(f==null||(typeof(f)=="string"&&f==="")){if(this.unnull==true){this.vErrorMsg=this.title+"不能为空"}}else{if(!this.vErrorMsg&&typeof(f)=="string"){var c=this.minLength;if(c!=null&&f.length<c){this.vErrorMsg=this.title+"最小长度为"+c+"个字符"}if(!this.vErrorMsg){var d=this.vldType;if(d){if(typeof(d)=="string"){this.vErrorMsg=EG.Validate.$is(d,f)}else{if(typeof(d)=="function"){this.vErrorMsg=d(f)}else{if(!this.vldTypeObj){this.vldTypeObj=new d.type(d)}this.vErrorMsg=this.vldTypeObj.validate(f)}}}}}}}if(this.vErrorMsg){this.onError()}return !this.vErrorMsg},onError:function(){EG.Style.addCls(this.lDiv,this.cls+"-error");if(this.prop.onError){this.prop.onError()}},format:function(){if(!this.vldType||!this.vldType.type){return}if(!this.vldTypeObj){this.vldTypeObj=new this.vldType.type(this.vldType)}var c=this.vldTypeObj.format(this.getValue());this.setValue(c)},destroy:function(){if(this.prop&&this.prop.destroy){this.prop.destroy()}},onGetData:null,statics:{bindValidate:function(){var e=this;this.vError=false;var f=function(){if(e._tValidate){clearTimeout(e._tValidate);e._tValidate=null}e._tValidate=setTimeout(function(){if(e.formItem){e.formItem.format();e.formItem.validate()}},500)};var d=function(g){g=EG.Event.getEvent(g);if(37<=g.keyCode&&g.keyCode<=40){return}f()};var c=this.getClass()._className;if(EG.$in(c,["EG.ui.Text","EG.ui.Password","EG.ui.Textarea","EG.ui.Date"])){EG.bindEvent(this.input,"keyup",d);EG.bindEvent(this.input,"blur",d)}else{if(c=="EG.ui.Select"){this.bindOnchange(f);EG.bindEvent(this.prop.input,"blur",d)}else{if(c=="EG.ui.BoxGroup"){this.bindOnchange(f)}}}},validate:function(){var e=this.getForm();if(e&&!e.validateAble){return true}var f=this.prop.getValue();this.vError=false;this.v_msg="";if(EG.String.isEmpty(f)){if(this.unnull==true){this.vError=true;this.v_msg=this.formItem.title+"不能为空"}}else{var c=this.minLength;if(c!=null&&f.length<c){this.vError=true;this.v_msg=this.formItem.title+"最小长度为"+c+"个字符"}if(!this.vError){var d=this.vldType;if(d){if(typeof(d)=="string"){this.vError=!(EG.Validate.$is(d,f));if(this.vError){this.v_msg=this.formItem.title+"格式应为"+EG.Validate.getComment(d)}else{this.v_msg=""}}else{if(typeof(d)=="function"){this.vError=!(d(f))}else{if(!this.vldTypeObj){this.vldTypeObj=new d.type(d)}this.vError=!this.vldTypeObj.validate(f);if(this.vError){this.v_msg=this.vldTypeObj.getMessage()}}}}}}if(this.vError&&!this.v_msg){this.v_msg="请输入正确的"+this.formItem.title}if(this.onError){this.onError()}return !this.v_msg}}}})})();(function(){EG.define("EG.ui.form.Prop",["EG.ui.Item"],function(a,b){return{extend:a,getElement:function(){return this.prop.getElement()},setValue:function(d,c){this.prop.setValue(d,c)},getValue:function(){return this.prop.getValue()},getTitle:null,statics:{validate:function(){var e=this.getForm();if(e&&!e.validateAble){return true}var f=this.prop.getValue();this.vError=false;this.v_msg="";if(EG.String.isBlank(f)){if(this.unnull==true){this.vError=true;this.v_msg=this.formItem.title+"不能为空"}}else{var c=this.minLength;if(c!=null&&f.length<c){this.vError=true;this.v_msg=this.formItem.title+"最小长度为"+c+"个字符"}if(!this.vError){var d=this.vldType;if(d){if(typeof(d)=="string"){this.vError=!(EG.Validate.$is(d,f));if(this.vError){this.v_msg=this.formItem.title+"格式应为"+EG.Validate.getComment(d)}else{this.v_msg=""}}else{if(typeof(d)=="function"){this.vError=!(d(f))}else{if(!this.vldTypeObj){this.vldTypeObj=new d.type(d)}this.vError=!this.vldTypeObj.validate(f);if(this.vError){this.v_msg=this.vldTypeObj.getMessage()}}}}}}if(this.vError&&!this.v_msg){this.v_msg="请输入正确的"+this.formItem.title}if(this.onError){this.onError()}return !this.v_msg},onError:function(){if(this.vError){EG.setCls(this.prop.input,"error",this.prop.cls)}else{EG.setCls(this.prop.input,"input",this.prop.cls)}}}}})})();(function(){EG.define("EG.ui.form.prop.Box",["EG.ui.form.Prop","EG.ui.BoxGroup"],function(b,c,a){return{extend:b,config:{type:"boxGroup"},constructor:function(d){try{this.prop=new c(d)}catch(f){alert(f.message)}},validate:function(){var d=this.getForm();if(d&&!d.validateAble){return true}var e=this.prop.getValue();this.vError=false;if(typeof(e)=="number"){}else{if((this.prop.multiple&&e.length==0)||(!this.prop.multiple&&EG.String.isBlank(e))){if(this.unnull==true){this.vError=true;this.v_msg=this.formItem.title+"不能为空"}}}if(this.vError&&!this.v_msg){this.v_msg="请选择正确的"+this.formItem.title}if(this.vError){EG.setCls(this.prop.element,"error",this.prop.cls)}else{EG.setCls(this.prop.element,"input",this.prop.cls)}return !this.vError},getTitle:function(n,f,k){var l=k.textvalues||[];if(!EG.isArray(n)){n=[n]}var h=[];for(var d=0,g=n.length;d<g;d++){var o=n[d];for(var e=0,m=l.length;e<m;e++){if(l[e][1]===o){h.push(l[e][0]);break}}}return h.join(",")}}})})();(function(){EG.define("EG.ui.form.prop.Date",["EG.ui.form.Prop","EG.ui.Date"],function(c,a,b){return{extend:c,config:{type:"date",unnull:false,minLength:null,width:"100%",height:20,vldType:null},constructor:function(d){this.initConfig(d);this.prop=new a(d);c.bindValidate.apply(this,[d])},validate:function(){return c.validate.apply(this)},onError:function(){return c.onError.apply(this)}}})})();(function(){EG.define("EG.ui.form.prop.Editor",["EG.ui.form.Prop","EG.ui.Editor"],function(c,a,b){return{extend:c,config:{readLineHeight:22},constructor:function(d){this.prop=new a(d)}}})})();(function(){EG.define("EG.ui.form.prop.Label",["EG.ui.form.Prop","EG.ui.Label"],function(b,c,a){return{extend:b,constructor:function(d){this.prop=new c(d)}}})})();(function(){EG.define("EG.ui.form.prop.Password",["EG.ui.form.Prop","EG.ui.Password"],function(c,a,b){return{extend:c,config:{type:"password",unnull:false,minLength:null,width:"100%",height:20,vldType:null},constructor:function(d){this.initConfig(d);this.prop=new a(d);c.bindValidate.apply(this,[d])},validate:function(){return c.validate.apply(this)},onError:function(){return c.onError.apply(this)}}})})();(function(){EG.define("EG.ui.form.prop.Select",["EG.ui.form.Prop","EG.ui.Select"],function(b,c,a){return{extend:b,config:{type:"select",unnull:false},constructor:function(d){this.initConfig(d);this.prop=new c(d);b.bindValidate.apply(this,[d])},validate:function(){var d=this.getForm();if(d&&!d.validateAble){return true}var e=this.prop.getValue();this.vError=false;this.v_msg="";if(EG.String.isBlank(e+"")){if(this.unnull==true){this.vError=true;this.v_msg=this.formItem.title+"不能为空"}}if(this.vError&&!this.v_msg){this.v_msg="请选择正确的"+this.cfg.title}if(this.onError){this.onError()}return !this.vError},onError:function(){if(this.vError){EG.setCls(this.prop.input,"error",this.prop.cls)}else{EG.setCls(this.prop.input,"input",this.prop.cls)}},getTitle:function(j,h,d){var f=d.textvalues||[];for(var g=0,e=f.length;g<e;g++){if(f[g][1]==j){return f[g][0]}}return null}}})})();(function(){EG.define("EG.ui.form.prop.SelectArea",["EG.ui.form.Prop","EG.ui.SelectArea"],function(b,c,a){return{extend:b,config:{type:"SelectArea",unnull:false},constructor:function(e){var f=this;this.initConfig(e);var d=e.onchange;e.onchange=function(){f.validate();if(d){d.apply(this,arguments)}};this.prop=new c(e);b.bindValidate.apply(this,[e])},validate:function(){this.vError=false;this.v_msg="";var d=this.prop.getValue();if(this.unnull==true&&(!d||d.length==0)){this.v_msg=this.formItem.title+"不能为空";this.vError=true}if(this.onError){this.onError()}return !this.vError},onError:function(){if(this.vError){EG.setCls(this.prop.destSlt,"error",this.prop.cls)}else{EG.setCls(this.prop.destSlt,"slts",this.prop.cls)}}}})})();(function(){EG.define("EG.ui.form.prop.Text",["EG.ui.form.Prop","EG.ui.Text"],function(c,a,b){return{extend:c,config:{type:"text",unnull:false,minLength:null,width:"100%",height:20,vldType:null},constructor:function(d){this.initConfig(d);this.prop=new a(d);c.bindValidate.apply(this,[d])},validate:function(){return c.validate.apply(this)},onError:function(){return c.onError.apply(this)}}})})();(function(){EG.define("EG.ui.form.prop.Textarea",["EG.ui.form.Prop","EG.ui.Textarea"],function(c,a,b){return{extend:c,config:{type:"textarea",unnull:false,minLength:null,width:"100%",height:40,vldType:null,readLineHeight:22},constructor:function(d){this.initConfig(d);this.prop=new a(d);c.bindValidate.apply(this,[d])},validate:function(){return c.validate.apply(this)},onError:function(){return c.onError.apply(this)},setRead:function(d,g){if(!g){g=""}g=EG.String.replaceAll(g," ","&nbsp;");var f=g.split("\n");for(var e=0;e<f.length;e++){f[e]=("<span>"+f[e]+"</span>")}g=f.join("<br/>");g=EG.String.replaceAll(g,"\t","<div style='margin-left:4em;display:inline-block;'></div>");EG.setValue(d,g)}}})})();(function(){EG.define("EG.ui.form.prop.Upload",["EG.ui.form.Prop","EG.ui.Upload"],function(c,a,b){return{extend:c,constructor:function(d){this.prop=new a(d)}}})})();(function(){EG.define("EG.ui.Menu",["EG.ui.Item"],function(a,b){return{extend:a,config:{direct:"H",autoExpand:false,autoExpandLv:0,itemsConfig:null,pop:false,verticalAlign:null,isContextMenu:false,contextElement:null},constructor:function(c){this.callSuper([c]);if(this.contextElement){this.attachContext(this.contextElement)}},afterBuild:function(){this.items=[];if(this.itemsConfig!=null&&this.itemsConfig.length>0){this.addItem(this.itemsConfig,false)}},build:function(){var c=this;this.element=EG.CE({tn:"div"});if(this.isContextMenu){EG.css(this.element,"position:absolute");EG.hide(this.element)}},attachContext:function(d){var c=this;this.contextElement=d;EG.Event.bindEvent(this.contextElement,"oncontextmenu",function(f){f=EG.Event.getEvent(f);var g=EG.Tools.getMousePos(f,c.contextElement);EG.Style.moveTo(c.element,g);if(c.onOpen){if(c.onOpen.apply(c,[f])===false){return}}EG.show(c.element);EG.Event.stopPropagation(f);return false});EG.Event.bindEvent(this.contextElement,"onclick",function(f){EG.hide(c.element)})},hide:function(){EG.hide(this.element)},addItem:function(g,c){var e=(!EG.isArray(g))?[g]:g;for(var f=0,d=e.length;f<d;f++){g=e[f];g.menu=this;if(EG.isLit(g)){g=a.create(g.xtype,g)}if(c>=0){EG.Array.insert(this.items,c,g)}else{this.items.push(g)}EG.DOM.addChildren(this.element,g.getElement(),c);g.pItem=this}this.render()},removeItem:function(c,d){if(d==null){d=true}this.element.removeChild(c.getElement());EG.Array.remove(this.items,c);c.pItem=null;if(d){}},clear:function(d){if(d==null){d=true}for(var c=0;c<this.items.length;c++){if(this.items[c].isContainer){this.items[c].clear()}}EG.DOM.removeChilds(this.getItemContainer());EG.Array.clear(this.items);if(d){}},render:function(){a.fit(this);var e=this.getSize();if(this.direct=="H"){for(var c=0;c<this.items.length;c++){var d=this.items[c];EG.css(d.getElement(),EG.Style.c.dv);d.render()}}else{for(var c=0;c<this.items.length;c++){var d=this.items[c];EG.css(d.getElement(),"display:block;");d.render()}}if(this.verticalAlign=="middle"){EG.Style.middleChilds(this.element,this.direct)}else{if(this.verticalAlign=="top"){EG.Style.topChilds(this.element,this.direct)}else{if(this.verticalAlign=="bottom"){EG.Style.bottomChilds(this.element,this.direct)}}}},clear:function(){EG.Array.clear(this.items);EG.DOM.removeChilds(this.element)}}})})();(function(){EG.define("EG.ui.MenuItem",["EG.ui.Item"],function(a,b){return{extend:a,config:{cls:"eg_menuItem",layoutDirect:null,menuDirect:null,text:null,menuWidth:null,menuHeight:null,itemsConfig:null,menu:null,click:null,fade:true,showMuti:true,mutiDirect:"right",showMutiOnEmpty:false,selectedAble:true},constructor:function(c){this.callSuper([c]);if(this.text){this.setText(this.text)}if(this.menu){this.setMenu(this.menu)}this.items=[];if(this.itemsConfig!=null&&this.itemsConfig.length>0){this.addItem(this.itemsConfig)}},build:function(){var c=this;this.element=EG.CE({tn:"div",cls:this.cls,item:this,cn:[this.dOuter=EG.CE({tn:"div",cls:this.cls+"-outer",item:this,cn:[this.dText=EG.CE({tn:"div",cls:this.cls+"-text",item:this}),this.dMulti=EG.CE({tn:"div",cls:this.cls+"-multi",item:this})]}),this.dMenus=EG.CE({tn:"div",cls:this.cls+"-menu",item:this,style:"display:none;"})]});if(this.mutiDirect=="right"){EG.DOM.insertAfter(this.dMulti,this.dText)}else{if(this.mutiDirect=="left"){EG.DOM.insertBefore(this.dMulti,this.dText)}}EG.CE({ele:this.dOuter,onmouseover:b._events.element.onmouseover,onmouseoverSrc:this,onmouseout:b._events.element.onmouseout,onmouseoutSrc:this,onclick:b._events.element.onclick,onclickSrc:this});EG.CE({ele:this.dMenus,onmouseover:b._events.element.onmouseover,onmouseoverSrc:this,onmouseout:b._events.element.onmouseout,onmouseoutSrc:this});EG.CE({ele:this.dText,onclick:this.expandClick,onclickSrc:this});EG.CE({ele:this.dMulti,onclick:this.expandClick,onclickSrc:this});EG.hide(this.dMenus);if(!this.showMuti){EG.hide(this.dMulti)}},setMenu:function(c){this.menu=c;EG.css(this.dMenus,(this.menu.pop?"position:absolute;z-index:1;":""))},setText:function(c){this.text=c;if(typeof(c)=="string"){this.dText.innerHTML=c}else{var d=c;if(!EG.Array.isArray(d)){d=[d]}EG.CE({ele:this.dText,cn:d})}},expandClick:function(){if(!this.menu.pop){if(EG.Style.current(this.dMenus).display=="none"){this.showChildMenus()}else{this.hideChildMenus()}}if(this.click){this.click.apply(this)}},addItem:function(g,c){var e=(!EG.isArray(g))?[g]:g;for(var f=0,d=e.length;f<d;f++){g=e[f];g.menu=this.menu;if(EG.isLit(g)){g=a.create(g.xtype,g)}g.pItem=this;if(c>=0){EG.Array.insert(this.items,c,g)}else{this.items.push(g)}EG.DOM.addChildren(this.dMenus,g.getElement(),c)}this.render()},removeItem:function(c,d){if(d==null){d=true}this.dMenus.removeChild(c.getElement());EG.Array.remove(this.items,c);c.pItem=null;if(d){}},clear:function(d){if(d==null){d=true}for(var c=0;c<this.items.length;c++){if(this.items[c].isContainer){this.items[c].clear()}}EG.DOM.removeChilds(this.getItemContainer());EG.Array.clear(this.items);if(d){}},render:function(){if(this.showMuti&&(this.showMutiOnEmpty||(this.items!=null&&this.items.length>0))){EG.show(this.dMulti)}else{EG.hide(this.dMulti)}if(this.hidden){EG.hide(this.getElement());return}else{EG.show(this.getElement())}if(this.rendered){return}if(this.width){a.fit({element:this.element,width:this.width,type:"width"});a.fit({element:this.dOuter,width:"100%",type:"width"});a.fit({element:this.dText,width:EG.getSize(this.dOuter).innerWidth,type:"width"})}this.rendered=true},renderMenus:function(){if(!this.menu.pop){if((this.pItem&&this.pItem.layoutDirect||this.menu&&this.menu.direct)=="H"){EG.css(this.dMenus,EG.Style.c.dv);EG.css(this.dText,EG.Style.c.dv)}else{EG.css(this.dMenus,"display:block;");EG.css(this.dText,"display:block;")}}var e=this.getSize();if(this.layoutDirect=="H"){for(var c=0;c<this.items.length;c++){var d=this.items[c];EG.css(d.getElement(),EG.Style.c.dv);if(this.menuHeight!=null){d.height=this.menuHeight}d.render()}}else{for(var c=0;c<this.items.length;c++){var d=this.items[c];EG.css(d.getElement(),"display:block;");if(this.menuWidth!=null){d.width=this.menuWidth}d.render()}}},showChildMenus:function(){if(this.items.length==0){return}if(this.menu.pop){var f=EG.Tools.getElementPos(this.element,this.element.offsetParent);var e=EG.Tools.getElementPos(this.dOuter,this.element.offsetParent);var d=EG.Style.getSize(this.dOuter);if(this.menuDirect=="B"){f.y=f.y+d.innerHeight+d.borderBottom+d.borderTop}else{if(this.menuDirect=="R"){var c=d.innerWidth+d.borderLeft+d.borderRight;if(EG.Style.current(this.getElement().parentNode).position=="absolute"){f.y=e.y;f.x=c}else{f.x=f.x+c;f.y=e.y}}else{throw new Error("暂不支持")}}EG.css(this.dMenus,"top:"+f.y+"px;left:"+f.x+"px;");if(this.menuWidth!=null){EG.css(this.dMenus,"width:"+this.menuWidth+"px")}if(this.menuHeight!=null){EG.css(this.dMenus,"height:"+this.menuHeight+"px")}}EG.show(this.dMenus);if(this.fade){EG.Style.fade(this.dMenus,0,90,null,10)}this.renderMenus()},hideChildMenus:function(){if(this.menu.pop){for(var c=0;c<b.w4h.length;c++){EG.hide(b.w4h[c].dMenus)}this.hiding=false}else{EG.hide(this.dMenus)}},addHideQue:function(){if(!EG.Array.has(b.w4h,this)){b.w4h.push(this);var c=this;while((c=c.pItem)!=null){if(c.getClass()._className!="EG.ui.MenuItem"){break}b.w4h.push(c)}}},removeHideQue:function(){EG.Array.remove(b.w4h,this);var c=this;while((c=c.pItem)!=null){if(c.getClass()._className!="EG.ui.MenuItem"){break}EG.Array.remove(b.w4h,c)}this.showChildMenus()},refreshCls:function(c){c=c?EG.Word.first2Uppercase(c):"";EG.Style.setCls(this.dOuter,this.cls+"-outer"+EG.n2d(c,""));EG.Style.setCls(this.dText,this.cls+"-text"+EG.n2d(c,""))},statics:{_events:{element:{onmouseover:function(c){this.refreshCls("on");if(this.menu.pop){this.removeHideQue()}EG.Event.stopPropagation(c)},onmouseout:function(d){var c=this;if(this.pItem.selectedItem==this){this.refreshCls("selected")}else{this.refreshCls()}if(this.menu.pop){this.addHideQue();if(!this.hiding){this.hiding=true;setTimeout(function(){c.hideChildMenus()},100)}}EG.Event.stopPropagation(d)},onclick:function(c){if(this.pItem.selectedItem){this.pItem.selectedItem.refreshCls()}if(this.selectedAble){this.pItem.selectedItem=this;this.refreshCls("selected")}}}},hiding:false,w4h:[]}}})})();(function(){EG.define("EG.ui.Calendar",["EG.ui.Item"],function(b,c){return{extend:b,config:{cls:"eg_calander",mode:"M",cellSpacing:0,cellPadding:0,border:0,date:null,scale:15},constructor:function(d){c.load();this.callSuper([d]);this.setDate(this.date||new Date())},build:function(){var d=this;this.p=new EG.ui.Panel({layout:"border",items:[this.pTop=new EG.ui.Panel({region:"top",height:30,cls:this.cls+"-top",layout:{type:"default",verticalAlign:true},cn:[this.dPre=EG.CE({tn:"div",cls:this.cls+"-top-pre",onclick:function(){d.goPre()}}),this.dCur=EG.CE({tn:"div",cls:this.cls+"-top-cur",onclick:function(){d.choose()},cn:[this.dChooserYear=EG.CE({tn:"div",onclick:this.chooseYear,onclickSrc:this,style:EG.Style.c.dv}),this.dChooserMonth=EG.CE({tn:"div",onclick:this.chooseMonth,onclickSrc:this,style:EG.Style.c.dv+"margin-left:10px"})]}),this.dPre=EG.CE({tn:"div",cls:this.cls+"-top-next",onclick:function(){d.goNext()}})]}),this.pLeft=new EG.ui.Panel({region:"left",cls:this.cls+"-left",layout:"border",width:70,items:[this.pLeftHead=new EG.ui.Panel({region:"top",cls:this.cls+"-left-head",height:30}),this.pLeftBody=new EG.ui.Panel({region:"center",cls:this.cls+"-left-body",cn:[this.leftBodyTable=EG.CE({tn:"table",cls:this.cls+"-left-table",item:this,cellPadding:this.cellPadding,cellSpacing:this.cellSpacing,border:this.border,cn:[this.leftBodyTableBody=EG.CE({tn:"tbody"})]})]})]}),this.pMain=new EG.ui.Panel({region:"center",layout:"border",cls:this.cls+"-main",items:[this.pMainHead=new EG.ui.Panel({region:"top",cls:this.cls+"-main-head",height:30,cn:[this.mainHeadTable=EG.CE({tn:"table",cls:this.cls+"-main-head-table",item:this,cellPadding:this.cellPadding,cellSpacing:this.cellSpacing,border:this.border,cn:[this.mainHeadTableBody=EG.CE({tn:"tbody",cn:[{tn:"tr"}]})]})]}),this.pMainBody=new EG.ui.Panel({region:"center",cls:this.cls+"-main-body",cn:[this.mainBodyTable=EG.CE({tn:"table",cls:this.cls+"-main-body-table",style:"",item:this,border:0,cellPadding:0,cellSpacing:0,cn:[this.mainBodyTableBody=EG.CE({tn:"tbody"})]})]})]})]});EG.DOM.addChildren(this.p.getElement(),this.dChooser=EG.CE({tn:"div",cls:this.cls+"-chooser",style:"position:absolute;"}));EG.hide(this.dChooser);EG.bindEvent(this.pMainBody.getElement(),"onscroll",function(){d.pLeftBody.getElement().scrollTop=this.scrollTop});this.element=this.p.getElement()},setDate:function(d){d=this.filterDate(d);this.curDate=d;this.draw()},filterDate:function(d){if(typeof(d)=="string"){d=EG.Date.s2d(d)}return d},getPreDate:function(){},getNextDate:function(){},goPre:function(){this.setDate(this.getPreDate())},goNext:function(){this.setDate(this.getNextDate())},choose:function(){},getDate:function(){return this.curDate},refresh:function(){this.setDate(this.curDate)},draw:function(){},go2:function(d){},render:function(){this.p.width=this.width;this.p.height=this.height;this.p.render()},statics:{_events:{},load:function(){if(c.loaded){return}EG.bindEvent(EG.getBody(),"onclick",function(){if(c.chooser&&!EG.Style.isHide(c.chooser)){EG.Style.hide(c.chooser)}});c.loaded=true},weeks:["日","一","二","三","四","五","六"],create:function(d){if(d.mode=="M"){return new EG.ui.calander.Month(d)}else{if(d.mode=="W"){return new EG.ui.calander.Week(d)}else{if(d.mode=="D"){return new EG.ui.calander.Day(d)}}}}}}});var a=EG.ui.Calendar})();(function(){EG.define("EG.ui.calendar.Month",["EG.ui.Item","EG.ui.Calendar"],function(c,e,d){return{extend:e,config:{},constructor:function(f){this.callSuper([f])},build:function(){var k=this;this.callSuper("build");EG.CE({ele:this.dCur,cn:[this.dChooserYear=EG.CE({tn:"div",onclick:this.chooseYear,onclickSrc:this,style:EG.Style.c.dv}),this.dChooserMonth=EG.CE({tn:"div",onclick:this.chooseMonth,onclickSrc:this,style:EG.Style.c.dv+"margin-left:10px"})]});this.pLeft.setHidden(true);for(var g=0;g<7;g++){EG.CE({pn:this.mainHeadTableBody.childNodes[0],tn:"td",innerHTML:"周"+a.weeks[g]})}var h=function(){if(k.lastSelected){EG.Style.removeCls(k.lastSelected,"selected")}k.lastSelected=this;EG.Style.addCls(k.lastSelected,"selected")};for(var g=0;g<6;g++){var l=EG.CE({pn:this.mainBodyTableBody,tn:"tr"});for(var f=0;f<7;f++){EG.CE({pn:l,tn:"td",onclick:h})}}},draw:function(){this.callSuper("draw");this.dChooserYear.innerHTML=this.curDate.getFullYear()+"年";this.dChooserMonth.innerHTML=(this.curDate.getMonth()+1)+"月";var f=EG.Date.getMonthday(this.curDate);var l=EG.clone(this.curDate);l.setDate(1);var h=l.getDay();var k=this.mainBodyTableBody.getElementsByTagName("td");var m=EG.clone(this.curDate);m.setDate(1);for(var j=0;j<k.length;j++){k[j].innerHTML="";var n=EG.Date.l2d(m.getTime()+EG.Date.unit_day*(j-h));var g=j<h?-1:0;if(g==0){if(this.curDate.getMonth()!=n.getMonth()){g=1}}this.drawDay(k[j],n,g)}},drawDay:function(h,g,f){h.innerHTML="<span style='color:"+(f!=0?"gray":"black")+"'>"+g.getDate()+"</span>"},render:function(){this.callSuper("render");c.fit({element:this.mainHeadTable});c.fit({element:this.mainHeadTableBody});c.fit({element:this.mainBodyTable});c.fit({element:this.mainBodyTableBody});var k=EG.getSize(this.mainBodyTable);var p=this.mainBodyTableBody.childNodes.length;var n=parseInt(k.innerHeight/p);var q=parseInt(k.innerWidth/7);var r=k.innerHeight%p;var l=k.innerWidth%7;var h=EG.getSize(this.mainHeadTableBody.childNodes[0].childNodes[0]);var j=EG.getSize(this.mainBodyTableBody.childNodes[0].childNodes[0]);for(var o=0;o<7;o++){var g=(q-(h.outerWidth-h.innerWidth));var m=(q-(j.outerWidth-j.innerWidth));if(o==6&&l>0){g+=l;m+=l}EG.css(this.mainHeadTableBody.childNodes[0].childNodes[o],"width:"+g+"px");EG.css(this.mainBodyTableBody.childNodes[0].childNodes[o],"width:"+m+"px")}for(var o=0;o<p;o++){var f=(n-(j.outerHeight-j.innerHeight));if(o==p-1&&r>0){f+=r}EG.css(this.mainBodyTableBody.childNodes[o].childNodes[0],"height:"+f+"px")}},getPreDate:function(){var f=EG.clone(this.curDate);if(f.getMonth==0){f.setMonth(11);f.setYear(f.getFullYear()-1)}else{f.setMonth(f.getMonth()-1)}return f},getNextDate:function(){var f=EG.clone(this.curDate);if(f.getMonth==11){f.setMonth(0);f.setYear(f.getFullYear()+1)}else{f.setMonth(f.getMonth()+1)}this.setDate(f);return f},getLeft:function(f){var g=f.offsetLeft;if(f.offsetParent!=null){g+=this.getLeft(f.offsetParent)}return g},chooseYear:function(j,h){var g=this;if(h==null){h=0}var k=EG.Tools.getElementPos(this.dChooserYear,this.element);k.y=this.dChooserYear.clientHeight;EG.DOM.removeChilds(this.dChooser);EG.CE({ele:this.dChooser,cn:[{tn:"div",style:EG.Style.c.dv},{tn:"div",style:EG.Style.c.dv}]});for(var f=5;f>=1;f--){EG.CE({pn:this.dChooser.childNodes[0],tn:"a",innerHTML:(this.curDate.getFullYear()-f+h*10),style:"display:block",item:this,onclick:d._events.aYear.click})}for(var f=0;f<5;f++){EG.CE({pn:this.dChooser.childNodes[1],tn:"a",innerHTML:(this.curDate.getFullYear()+f+h*10),style:"display:block",item:this,onclick:d._events.aYear.click})}EG.CE({pn:this.dChooser.childNodes[0],tn:"a",innerHTML:"<-",style:"display:block",item:this,onclick:function(){g.chooseYear(null,h-1)}});EG.CE({pn:this.dChooser.childNodes[1],tn:"a",innerHTML:"->",style:"display:block",item:this,onclick:function(){g.chooseYear(null,h+1)}});d.chooser=this.dChooser;EG.show(this.dChooser);EG.css(this.dChooser,"top:"+k.y+"px;left:"+((k.x-EG.getSize(this.dChooser.childNodes[0]).outerWidth/2))+"px");EG.Event.stopPropagation(j)},chooseMonth:function(g){var h=EG.Tools.getElementPos(this.dChooserMonth,this.element);h.y=this.dChooserMonth.clientHeight;EG.DOM.removeChilds(this.dChooser);EG.CE({ele:this.dChooser,cn:[{tn:"div",style:EG.Style.c.dv},{tn:"div",style:EG.Style.c.dv}]});for(var f=1;f<=6;f++){EG.CE({pn:this.dChooser.childNodes[0],tn:"a",innerHTML:f,style:"display:block",item:this,onclick:d._events.aMonth.click})}for(var f=7;f<=12;f++){EG.CE({pn:this.dChooser.childNodes[1],tn:"a",innerHTML:f,style:"display:block",item:this,onclick:d._events.aMonth.click})}d.chooser=this.dChooser;EG.show(this.dChooser);EG.css(this.dChooser,"top:"+h.y+"px;left:"+((h.x-EG.getSize(this.dChooser.childNodes[1]).outerWidth/2))+"px");EG.Event.stopPropagation(g)},statics:{_events:{aYear:{click:function(){var f=EG.clone(this.item.curDate);f.setYear(parseInt(this.innerHTML));this.item.setDate(f);EG.hide(this.item.dChooser)}},aMonth:{click:function(){var f=EG.clone(this.item.curDate);f.setMonth(parseInt(this.innerHTML)-1);this.item.setDate(f);EG.hide(this.item.dChooser)}}}}}});var b=EG.ui.calendar.Month;var a=EG.ui.Calendar})();(function(){EG.define("EG.ui.calendar.Week",{extend:"EG.ui.Calendar",config:{},constructor:function(c){this.callSuper([c])},build:function(){var l=this;this.callSuper("build");EG.CE({ele:this.dCur,cn:[this.dChooserYear=EG.CE({tn:"div",onclick:this.chooseYear,onclickSrc:this,style:EG.Style.c.dv}),this.dChooserMonth=EG.CE({tn:"div",onclick:this.chooseMonth,onclickSrc:this,style:EG.Style.c.dv+"margin-left:10px"})]});var k=24*60/this.scale;var o=new Date();o.setMinutes(0);o.setHours(0);o.setSeconds(0);for(var f=0;f<k;f++){var g=parseInt((f*this.scale)/60);var c=(f*this.scale)%60;EG.CE({pn:this.leftBodyTableBody,tn:"tr",cn:[{tn:"td",style:"height:25px;line-height:25px;",innerHTML:(g+":"+c)}]});var n=EG.CE({pn:this.mainBodyTableBody,tn:"tr"});for(var e=0;e<7;e++){EG.CE({pn:n,tn:"td",style:"height:25px;line-height:25px;"})}}for(var f=0;f<7;f++){EG.CE({pn:this.mainHeadTableBody.childNodes[0],tn:"td",innerHTML:"周"+a.weeks[f]})}EG.CE({pn:this.mainHeadTableBody.childNodes[0],tn:"td"})},draw:function(){this.callSuper("draw");this.dChooserYear.innerHTML=this.curDate.getFullYear()+"年";this.dChooserMonth.innerHTML=(this.curDate.getMonth()+1)+"月"},drawCol:function(e,d,c){},addBlock:function(h,i,k){if(EG.Date.sameDay(h,i)){var g=h.getDay();var e=this.mainHeadTableBody.childNodes[0].childNodes[g];var l=EG.getSize(e);var c=l.innerWidth;var d=e.offsetLeft;var f=this.mainBodyTableBody.childNodes[this.getRowIdx(h)].offsetTop;var j=this.mainBodyTableBody.childNodes[this.getRowIdx(i)].offsetTop-f;alert(f+","+d+","+c+","+j);this.pMainBody.getElement().appendChild(k);Item.fit({element:k,pSize:{width:c,height:j}});EG.css(k,"position:absolute;top:"+f+"px;left:"+d+"px");EG.bindEvent(k,"onmouseover",function(){});EG.bindEvent(k,"onmouseout",function(){});EG.bindEvent(k,"onclick",function(){})}else{}},getRowIdx:function(c){return(c.getHours()*60+c.getMinutes())/this.scale},render:function(){this.callSuper("render");Item.fit({element:this.leftBodyTable});Item.fit({element:this.leftBodyTableBody});Item.fit({element:this.mainHeadTable});Item.fit({element:this.mainHeadTableBody});Item.fit({element:this.mainBodyTable});Item.fit({element:this.mainBodyTableBody});var m=EG.getSize(this.mainBodyTableBody);var k=EG.getSize(this.mainHeadTableBody);var d=m.innerWidth%7;var g=EG.getSize(this.mainBodyTableBody.childNodes[0].childNodes[0]);var h=parseInt(m.innerWidth/7)-(g.outerWidth-g.innerWidth);var l=this.mainBodyTableBody.childNodes[0].childNodes;var j=EG.getSize(this.mainHeadTableBody.childNodes[0].childNodes[0]);var c=parseInt(m.innerWidth/7)-(j.outerWidth-j.innerWidth);var e=this.mainHeadTableBody.childNodes[0].childNodes;for(var f=0;f<7;f++){EG.css(l[f],"width:"+(h+(f==6?d:0))+"px");EG.css(e[f],"width:"+(c+(f==6?d:0))+"px")}EG.getSize(e[7],"width:"+this.mainHeadTable.innerWidth-7*c+"px")},statics:{_events:{aYear:{click:function(){var c=EG.clone(this.item.curDate);c.setYear(parseInt(this.innerHTML));this.item.setDate(c)}},aMonth:{click:function(){var c=EG.clone(this.item.curDate);c.setMonth(parseInt(this.innerHTML)-1);this.item.setDate(c)}}}}});var b=EG.ui.calendar.Week;var a=EG.ui.Calendar})();(function(){EG.define("EG.ui.Slide",["EG.ui.Item"],function(a,b){return{alias:"slide",extend:a,config:{onchange:null,cls:"eg_slide",range_start:0,range_end:100,doubleEnable:false,showScale:false,showTip:false,tipStyle:null,onSlideUp:null},constructor:function(c){this.callSuper([c])},build:function(){this.element=EG.CE({tn:"div",cls:this.cls,item:this,cn:[this.el_tip=EG.CE({tn:"div",cls:this.cls+"-tip",style:"position:absolute;",item:this,innerHTML:this.range_start||" "}),this.el_background=EG.CE({tn:"div",cls:this.cls+"-background",style:"position:absolute;",item:this}),this.el_block=EG.CE({tn:"div",cls:this.cls+"-block",style:"position:absolute"})],onmousemove:b._events.element.onmousemove,onmouseup:b._events.element.onmouseup});if(this.tipStyle){EG.css(this.el_tip,this.tipStyle)}this.builded=true},setValue:function(f){this._value=f;var e=this.value2Size(f);EG.css(this.el_block,"margin-left:"+e+"px");if(this.showTip){var d=EG.getSize(this.el_block);var c=EG.getSize(this.el_tip);EG.css(this.el_tip,"margin-left:"+(e+(d.clientWidth/2-c.clientWidth/2))+"px");this.el_tip.innerHTML=f}},getValue:function(){return this._value},destroy:function(){},size2Value:function(e){var c=this.el_background.clientWidth;var d=this.range_start+(e/c)*(this.range_end-this.range_start);if(!this.doubleEnable){d=parseInt(d)}if(d<this.range_start){d=this.range_start}else{if(d>this.range_end){d=this.range_end}}return d},value2Size:function(e){var c=this.el_background.clientWidth;var d=e/(this.range_end-this.range_start);return c*d},render:function(){a.fit(this);var e=EG.getSize(this.getElement());var f=e.innerHeight/2;a.fit({element:this.el_background,pSize:e,dSize:{width:e.innerWidth-f*2,height:f}});EG.css(this.el_background,"margin-top:"+(f/2)+"px;border-radius:"+f/2+"px;margin-left:"+f+"px");var d=EG.getSize(this.el_block);EG.css(this.el_block,{width:e.innerHeight-(d.borderLeft+d.borderRight)+"px",height:e.innerHeight-(d.borderTop+d.borderBottom)+"px","border-radius":f+"px"});d=EG.getSize(this.el_block);if(this.showTip){EG.show(this.el_tip);EG.css(this.el_tip,"width:"+f+"px;height:"+f+"px;margin-top:-"+f+"px;line-height:"+f+"px");var c=EG.getSize(this.el_tip);EG.css(this.el_tip,"margin-left:"+(d.clientWidth/2-c.clientWidth/2)+"px")}else{EG.hide(this.el_tip)}},statics:{_events:{element:{onmousemove:function(h){var i=this.item;if(EG.Tools.isPressLeft(h)){if(!b._startMousePos){b._startMousePos=EG.Tools.getMousePos(h,this);b._startBlockSize=EG.getSize(i.el_block)}var j=EG.Tools.getMousePos(h,this);var f=j.x-b._startMousePos.x+b._startBlockSize.marginLeft;if(f<0){f=0}var l=EG.getSize(i.element);var d=EG.getSize(i.el_block);if(f>=(l.innerWidth-d.clientWidth)){f=l.innerWidth-d.clientWidth}EG.css(i.el_block,"margin-left:"+f+"px");var g=EG.getSize(i.el_background);var c=(f+d.clientWidth/2)-g.marginLeft;var k=i.size2Value(c);i.setValue(k)}EG.Event.stopPropagation(h)},onmouseup:function(f){var c=this.item;if(EG.Tools.isPressLeft(f)){var d=c.getValue();if(c.onSlideUp){c.onSlideUp.apply(this,[d])}}b._startPos=null}}}}}})})();(function(){EG.define("EG.ui.Switch",["EG.ui.Item"],function(a,b){return{alias:"switch",extend:a,config:{onchange:null,cls:"eg_switch",range_start:0,range_end:100,doubleEnable:false,textvalues:[],openValue:null,closeValue:null,openText:"打开",closeText:"关闭",showText:false,marginSpacing:3,textStyle:null},constructor:function(c){var d=c.textvalues;if(d){if(this.openValue==null){this.openValue=d[0][1]}if(this.closeValue==null){this.closeValue=d[1][1]}if(this.openText==null){this.openText=d[0][0]}if(this.closeText==null){this.closeText=d[1][0]}}this.callSuper([c])},build:function(){var c=this;this.element=EG.CE({tn:"div",cls:this.cls,item:this,cn:[this.el_background=EG.CE({tn:"div",cls:this.cls+"-background-close",style:"position:absolute;",item:this}),this.el_block=EG.CE({tn:"div",cls:this.cls+"-block",style:"position:absolute",onclick:function(){var d=c.getOppositeValue(c.getValue());c.setValue(d)}}),this.el_text=EG.CE({tn:"div",cls:this.cls+"-text",style:"position:absolute;text-align:center;",onclick:function(){}})]});if(this.textStyle){EG.css(this.el_text,this.textStyle)}this.builded=true},getOppositeValue:function(c){return c==this.openValue?this.closeValue:this.openValue},setValue:function(c){this._value=c;this.render()},getValue:function(){return this._value},destroy:function(){},doSwitch:function(){this.setValue(this.getOppositeValue(this.getValue()))},getTitle:function(e,d,c){if(e==this.openValue){return this.openText}else{if(e==this.closeValue){return this.closeText}}return""},render:function(){var g=this.getValue();a.fit(this);var e=EG.getSize(this.element);EG.Style.setCls(this.el_background,this.cls+"-background-"+(g==this.openValue?"open":"close"));a.fit({element:this.el_background,pSize:e});EG.css(this.el_background,"border-radius:"+e.innerHeight/2+"px");var f=EG.getSize(this.el_background);EG.css(this.el_block,{width:(e.innerHeight-this.marginSpacing*2)+"px",height:(e.innerHeight-this.marginSpacing*2)+"px","border-radius":e.innerHeight/2+"px","margin-top":this.marginSpacing+"px","margin-left":this.marginSpacing+"px",});var d=EG.getSize(this.el_block);EG.css(this.el_block,"margin-top:"+this.marginSpacing+"px;");var h=f.innerWidth-d.clientWidth;if(g==this.openValue){EG.css(this.el_block,"margin-left:"+(h-this.marginSpacing)+"px")}else{EG.css(this.el_block,"margin-left:"+(this.marginSpacing)+"px")}var c=(f.innerWidth-d.clientWidth-this.marginSpacing*2);EG.css(this.el_text,"width:"+c+"px;height:"+e.innerHeight+"px;line-height:"+e.innerHeight+"px");if(this.showText){EG.hide(this.el_text)}else{EG.show(this.el_text)}if(g==this.openValue){EG.CE({ele:this.el_text,cls:this.cls+"-text-open",style:"margin-left:"+(this.marginSpacing)+"px",innerHTML:this.openText})}else{EG.CE({ele:this.el_text,cls:this.cls+"-text-close",style:"margin-left:"+(d.clientWidth+this.marginSpacing*2)+"px",innerHTML:this.closeText})}}}})})();(function(){EG.define("EG.ui.JSONView",["EG.ui.Item"],function(a,b){return{alias:"jsonView",extend:a,constructor:function(c){this.callSuper([c])},build:function(){this.element=EG.CE({tn:"div"})},setValue:function(c){this.value=c;this.element.innerHTML=this.getObjHtml(c)},getValue:function(){return this.value},getIndentSpace:function(c){var e="";for(var d=0;d<c*4;d++){e+="&nbsp;"}return e},getObjHtml:function(g,d,r){if(d==null){d=0}if(r==null){r=false}var h="color:#00AA00";var q="color:#0033FF";var l="color:#AA00AA";var n="color:#007777";var p="color:#CC0000";var j="color:#AA00AA";var o="color:#0033FF";var c=EG.getType(g);var m="";if(g==null){m='<span style="'+l+'">null</span>'}else{if(typeof(g)=="string"){m='<span style="'+n+'">"'+g+'"</span>'}else{if(typeof(g)=="number"){m='<span style="'+j+'">'+g+"</span>"}else{if(typeof(g)=="boolean"){m='<span style="'+o+'">'+g+"</span>"}else{if(g instanceof Object){if(EG.Array.isArray(g)){m='<span style="'+q+'">[</span><br/>';for(var f=0;f<g.length;f++){if(f>0){m+=",<br/>"}m+=this.getIndentSpace(d+1)+this.getObjHtml(g[f],d+1)}m+="<br/>";m+='<span style="'+q+'">'+this.getIndentSpace(d)+"]</span>"}else{m='<span style="display-line:block;'+h+'">{</span><br/>';var f=0;for(var e in g){if(f>0){m+=",<br/>"}m+=this.getIndentSpace(d+1)+'<span style="'+p+'">"'+e+'"</span>:'+this.getObjHtml(g[e],d+1,true);f++}m+="<br/>";m+='<span style="'+h+'">'+this.getIndentSpace(d)+"}</span>"}}else{m="#δ֪��ʽgetObjHtml#"}}}}}return m},render:function(){a.fit(this)}}})})();