<!DOCTYPE html>
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>APIDOC</title>
<link href="styles/animate.css" rel="stylesheet" rev="stylesheet" type="text/css" media="screen"/>
<script type="text/javascript" src="scripts/eg/eg-all.src.js"></script>
<link type="text/css" href="scripts/eg/skins/blue/ui.css" rel="stylesheet" />
<style>
    *                       {margin:0px;padding:0px;font-family:'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace,"Microsoft Yahei",tahoma, arial, verdana, sans-seri;}
    html,body               {height:100%;font-size:14px;overflow:hidden;background-color:#464646}
    input                   {height:26px;line-height: 26px;padding:0px 3px 0px 3px }
</style>
</head>
<body>
<script type="text/javascript">
    <!--
    EG._markLoadPath=true;
    var _page_cache=true;


    var API=function(){};
    API.basePath=window.location.protocol+"//"+window.location.host+"/"+window.location.pathname.split("/")[1];
    API.loader=new EG.Loader();
    API.loader.getClassUrl=function(className){
        var ss=className.split(".");
        var classFileName=ss[ss.length-1];
        ss.pop();
        var uri="scripts/"+(ss.length>0?(ss.join("/").toLowerCase()):"");
        uri+="/"+classFileName+".js";
        if(!_page_cache){
            uri+="?random="+Math.floor(Math.random()*10000);
        }
        return uri;
    }


    API.define=function(){
        var args=EG.Array.addAll([],arguments);
        args.push(API.loader);
        return EG.define.apply(this,args);
    };

    EG.onload(function(){
        API.loader.require(["PAGE.Console"],function(_Console){
            window.$console=new _Console();
        });
    });

    window.onerror=EG.onError;
    //-->
</script>
</body>
