# 权限管理设计

## 概要:
1. 菜单就是权限，菜单包括模块，页面，功能等类型
2. 角色之间具有管理关系，比如说，分校校长角色可以管理分校老师角色。
管理的意思是他可以分校校长可以为用户分配分校老师的角色，并不是可以修改这个角色的内容。
3. 资源策略，即“我创建的”，“本部门的”等等，用来限制每个功能的数据范围，
每个功能应指明自己所支持的资源策略。如不指定，则认为此功能使用对所有使用者均可看到或操作所有的数据资源。
4. 角色与权限之间具有一对多关系，同时，角色下的权限可指定资源策略。
5. 用户可以有多个角色
6. 用户与部门，项目，客户来源等具有类似于资源策略的限制关系。

## 表
表设计依照简单高效的原则，尽量减少中间表，避免大量的关联关系拖慢速度。

1. 资源策略：
    
    不单独建表，使用字典。type：datapolicy
    
2. 菜单表（即权限）：
    * menuId
    * menuName  名字
    * menuType  类型 模块/菜单/功能
    * menuUrl   路径
    * menuSeq 排序号
    * menuCode  唯一code
    * menuDisablestatus  是否禁用
    * menuDatapolicy 资源策略 逗号分隔策略的code
    * menuMemo  描述
    * menuIcon  图标
    * menuLevelcode
3. 角色表
    * roleId  
    * roleName  名称
    * roleDesc  描述
    * roleSeq 排序
    * roleManagerole 可授权角色
4. 角色权限关系表
    * rolemenuId     
    * roleId       角色code
    * menuId        菜单code
    * roleDatapolicy  资源策略的code  逗号分隔
5. 用户关系表
    * userreId
    * userreType       关系类型 角色 部门 项目 客户来源等
    * userId           用户id
    * userreRelation   关联的关系，可能是id，可能是code，根据type来定
    
## 主要接口

凡是业务上使用时获取的接口，统一在uc/auth中，写一个UcAuthHandler
在后台进行配置时，具体的逻辑写在具体的handler中

### 根据用户获取对应的菜单（权限）   uc/auth/menus   V
  根据用户id，或当前用户获取他所有角色内的权限并集
  * 如果没有传用户id，获取当前登录的用户
  * 返回的是一个权限code列表
  * 合并时，功能上数据范围，也要取并集
### 根据用户获取其“用户范围”  uc/auth/userdatascope  V
  根据用户id，或当前用户获取他对应的部门范围，项目范围，客户来源范围
  * 如果没有传用户id，获取当前登录的用户
  * 用户范围作为一个参数传递进来   dpt  project  userSource  如果不传，则返回全部
   {
     data: [id...]
   }
   {
    data: {
        dpt:[id...],
        project:[id...],
        usersource:[id...]
    }
   }
   
### 根据用户获取他的角色、和他可以授权的角色  uc/auth/role   V
  主要用于判断当前用户是否可以为其他用户授予某某权限 
  * 关联用户关系表与角色表，返回角色列表
  {
    data:{
        role:[roleId...],
        manageRole:[roleIds]
    }
  }
  
### 为用户分配角色   
  * 为用户分配角色，重设   uc/auth/setUserRole   V
  * 批量为为用户添加一个角色  uc/auth/addUserRole  V

### 为用户分配部门关系，项目关系，客户来源关系   uc/auth/setUserRelation  V
  为用户分配关系，传入用户id，部门id集合，项目id集合，用户来源code集合，
  清楚原有数据关系，重新保存数据库即可
  
### 根据角色获取对应的所有权限  uc/role/getMenusByRoleId   V
  为角色配置权限时的回显
  * 查询角色关系表，返回信息即可
  {
    data:[
        {
            menuId: "xxx"
            roleDatapolicy:[code...] // 数据策略的code列表
        }
        ...
    ]
  }
  
### 为角色分配权限关系   uc/role/setRolemenu    V
  删除之前此角色下的所有权限关系并重新添加
 


### 根据权限获取对应的所有角色  uc/role/findUsersByMenu
  用于反查某个权限被那些角色所有，进而查出用户
### 根据角色获取所有的用户      uc/role/findUsersByRole
  反查
  
### 菜单（权限）增删改查   uc/menu/list,add,edit,delete   V
  自动生成代码
  * 如果是一个功能，则code必须要有
  * 如果是一个菜单或模块，code可以不传，不传的话code与id保持一致
### 角色增删改查     uc/role/list,add,edit,delete     V
  自动生成代码
  * 删除角色时，应删除角色对应的权限列表
### 资源策略的增加，查询  uc/menu/datapolicy/list,add,edit,delete    V
  调用字典的相关操作。用于在增加菜单功能是，选择资源策略或直接新增一个资源策略
  
### 菜单，角色，数据范围等信息的备份与还原  uc/menu/backup   uc/menu/rollback
  用于运维人员在测试服务上配置后直接导入正式环境
  其实相当于备份一下sql，直接使用了
  
  备份与还原应满足一下要求：
  * 角色，以及角色关系，用户角色等，是动态变的，不是备份的，备份只需备份菜单
  * 菜单的还原应保证原有的角色菜单关系可用，即菜单的id不能改变
  * 当菜单发生删除操作时，角色也应删除对应的菜单关联关系
  * 任何环境之间均可互相备份还原，即所有环境具有均等性
  
  方案：
  * 菜单上增加创建时间，修改时间字段
  * 菜单备份方式：
     * 备份时选择一个父节点进行备份，特别的，可以选择根节点进行备份
     * 选择的节点的父节点必须是存在的
     * 如果选中的节点在还原的服务器上没有，则会在还原机上创建新节点，所有的节点采用新增操作
     * 还原时，逐条根据id进行查询，如果id存在，判断createtime是否相同，父节点是否相同
        如果相同，认为是同一条数据，进行更新
        如果不相同，认为与其他人的备份冲突了（类似于svn），不允许还原
     * 对于此节点下原来存在，但此次还原过程中却没有它的，认为节点删除，需要删除对应的角色关系
     * 这样一来，可以多个模块分开备份
       
  页面：
    * 菜单管理上，应有一个备份按钮，点击备份按钮时，弹出菜单选择
    * 备份后，返回一个备份文件共前端下载
  
  备份文件：
    * 备份文件内容是一个json格式的字符串，使用gzip进行压缩后存储
    * json中应包括备份的模式和备份的数据
  
  还原：
    * 菜单管理上应有一个还原按钮，点击后上传文件
    * 解析文件为json后，首先解析备份的模式，根据备份的模式决定如何处理
    * 解析完成后，应返回前端 新增了那些菜单，删除了那些菜单，更新了那些菜单，前端确认后，后台方可更新
    
    
角色部分作出如下调整
1. 角色列表改为只能看到自己管理范围内的角色
2. 自己创建的角色自己必然在自己可管理的角色列表中，将此角色放入我的角色里面有“编辑角色”权限的“可授权角色”中
3. 角色编辑时，看到的菜单列表是自己权限范围内的列表
4. 角色增加品牌标识，一个角色只能有一个品牌
5. 角色列表根据当前登录人登录的品牌进行过滤
6. 新增角色时，角色的品牌与当前登录的品牌保持一致

给用户设置角色的调整
1. 一个用户可以有多个品牌下的角色，但是在使用时，只会用到一个品牌下的角色
2. 给用户设置角色时，也是只能设置本品牌下的角色（注意修改时的删除逻辑也要动）

品牌标识哪里来？
用户登录时，品牌缓存在redis中，需要时在网关使用CoreIdentity获取


角色备份与还原
1. why  周申拥有三个品牌的管理权限，优路，锅巴，环球，   他以优路的身份登录了系统，创建了一个分校财务的角色
然后，他想要在锅巴也创建一个分校财务的角色，两个角色的菜单权限是基本一致的。由于跨了品牌，他必须切换到锅巴品牌身份
才能创建锅巴角色，此时，他看不到已经在优路创建的分校财务，也就没法使用“复制角色”功能。给操作带来了极大的不便。
折衷之下，做一个角色导入导出功能，可以将优路的角色导出，导入到锅巴系统中
2. how 导出的时候，选择多个角色，传递id到后台，后台将对应的角色和角色的权限导出到文件（不包含id等信息）
3. how 导入的时候，选择文件，后台解析后，判断角色名称是否有重复，如果有，则拒绝导入。
另外，从环球导出的权限列表可能大于锅巴的权限，此时，必须将超出的权限部分进行移除。
导入的时候都是以新增的方式进行，所以这个功能并不是备份
