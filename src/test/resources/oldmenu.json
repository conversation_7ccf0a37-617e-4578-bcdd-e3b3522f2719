[{"menuId": "MENU20191009010000000005", "menuName": "支付中心", "menuLevelcode": "0000000027", "menuSeq": 7, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191009010000000006", "menuName": "支付方式配置", "menuLevelcode": "00000000270000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": "支付方式配置", "pageUrl": "pc/paymode"}, {"menuId": "MENU20191009010000000007", "menuName": "支付渠道配置", "menuLevelcode": "00000000270000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "S", "pageName": "支付渠道配置", "pageUrl": "pc/channel"}, {"menuId": "MENU20191009010000000008", "menuName": "支付单", "menuLevelcode": "00000000270000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "S", "pageName": "支付单", "pageUrl": "pc/payment"}, {"menuId": "MENU20191009010000000009", "menuName": "支付单交易记录", "menuLevelcode": "00000000270000000004", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "S", "pageName": "支付单交易记录", "pageUrl": "pc/traderecord"}, {"menuId": "MENU20191009010000000010", "menuName": "对账单", "menuLevelcode": "00000000270000000005", "menuSeq": 4, "menuIcon": "", "menuShowstatus": "S", "pageName": "对账单", "pageUrl": "pc/bill"}, {"menuId": "MENU20191009010000000011", "menuName": "对账记录", "menuLevelcode": "00000000270000000006", "menuSeq": 5, "menuIcon": "", "menuShowstatus": "S", "pageName": "对账记录", "pageUrl": "pc/billrecord"}, {"menuId": "MENU20191116590000000001", "menuName": "支付渠道配置中心", "menuLevelcode": "00000000270000000007", "menuSeq": 6, "menuIcon": "", "menuShowstatus": "S", "pageName": "支付渠道规则配置", "pageUrl": "pc/channelRule"}]}, {"menuId": "MENU20190927010000000004", "menuName": "学员服务", "menuLevelcode": "0000000022", "menuSeq": 8, "menuIcon": "", "menuShowstatus": "S", "pageName": "工作台", "pageUrl": "ss/index", "children": [{"menuId": "MENU20190929010000000012", "menuName": "回访列表页", "menuLevelcode": "00000000220000000016", "menuSeq": 10, "menuIcon": "", "menuShowstatus": "H", "pageName": "回访列表页", "pageUrl": "ss/workbench/returnVisit"}, {"menuId": "MENU20190929010000000013", "menuName": "面授课通知", "menuLevelcode": "00000000220000000017", "menuSeq": 11, "menuIcon": "", "menuShowstatus": "H", "pageName": "面授课通知", "pageUrl": "ss/workbench/notificationList"}, {"menuId": "MENU20190929010000000014", "menuName": "报名通知", "menuLevelcode": "00000000220000000018", "menuSeq": 12, "menuIcon": "", "menuShowstatus": "H", "pageName": "报名通知", "pageUrl": "ss/workbench/signUpNoticeList"}, {"menuId": "MENU20190929010000000015", "menuName": "准考证通知", "menuLevelcode": "00000000220000000019", "menuSeq": 13, "menuIcon": "", "menuShowstatus": "H", "pageName": "准考证通知", "pageUrl": "ss/workbench/admissionTicket"}, {"menuId": "MENU20190929010000000016", "menuName": "成绩查询", "menuLevelcode": "00000000220000000020", "menuSeq": 14, "menuIcon": "", "menuShowstatus": "H", "pageName": "成绩查询", "pageUrl": "ss/workbench/scoreQuery"}, {"menuId": "MENU20190929010000000017", "menuName": "办理重读", "menuLevelcode": "00000000220000000021", "menuSeq": 15, "menuIcon": "", "menuShowstatus": "H", "pageName": "办理重读", "pageUrl": "ss/workbench/reread"}, {"menuId": "MENU20190927010000000013", "menuName": "工作台", "menuLevelcode": "00000000220000000003", "menuSeq": 16, "menuIcon": "", "menuShowstatus": "S", "pageName": "工作台", "pageUrl": "ss/index"}, {"menuId": "MENU20191006010000000001", "menuName": "学员管理", "menuLevelcode": "00000000220000000015", "menuSeq": 18, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191006010000000002", "menuName": "学员项目列表", "menuLevelcode": "000000002200000000150000000002", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "学员项目列表", "pageUrl": "ss/studentManagement/studentList"}, {"menuId": "MENU20190927010000000015", "menuName": "学员班级列表", "menuLevelcode": "000000002200000000150000000001", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "学员班级列表", "pageUrl": "ss/studentManagement/classList"}]}, {"menuId": "MENU20191104590000000002", "menuName": "考勤名单管理", "menuLevelcode": "00000000220000000022", "menuSeq": 20, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191104590000000003", "menuName": "面授课考勤名单", "menuLevelcode": "000000002200000000220000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": "面授课考勤名单", "pageUrl": "ss/timeSheet/fl"}]}, {"menuId": "MENU20190927010000000016", "menuName": "考试成绩管理", "menuLevelcode": "00000000220000000004", "menuSeq": 21, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190927010000000017", "menuName": "分数线设置", "menuLevelcode": "000000002200000000040000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "分数线设置", "pageUrl": "ss/scoreManagement/fractionalLineSetting"}, {"menuId": "MENU20190927010000000018", "menuName": "考试成绩列表", "menuLevelcode": "000000002200000000040000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "考试成绩列表", "pageUrl": "ss/scoreManagement/scoreList"}]}, {"menuId": "MENU20190928010000000003", "menuName": "投诉建议管理", "menuLevelcode": "00000000220000000006", "menuSeq": 23, "menuIcon": "", "menuShowstatus": "H", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190928010000000005", "menuName": "投诉建议列表", "menuLevelcode": "000000002200000000060000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "投诉建议列表", "pageUrl": "ss/complaintAndSuggestionManagement/complaintAndSuggestionList"}, {"menuId": "MENU20190928010000000006", "menuName": "投诉建议统计", "menuLevelcode": "000000002200000000060000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "投诉建议统计", "pageUrl": "ss/complaintAndSuggestionManagement/complaintAndSuggestionStatistics"}]}, {"menuId": "MENU20190928010000000004", "menuName": "服务质量统计", "menuLevelcode": "00000000220000000007", "menuSeq": 24, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190928010000000007", "menuName": "已办任务清单", "menuLevelcode": "000000002200000000070000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "已办任务清单", "pageUrl": "ss/qualityOfServiceStatistics/listOfCompletedTasks"}, {"menuId": "MENU20190928010000000008", "menuName": "消息发送记录", "menuLevelcode": "000000002200000000070000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "消息发送记录", "pageUrl": "ss/qualityOfServiceStatistics/messageSendingRecord"}]}]}, {"menuId": "MENU20191009010000000001", "menuName": "教务中心", "menuLevelcode": "0000000026", "menuSeq": 10, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191108590000000001", "menuName": "基础设置", "menuLevelcode": "00000000260000000004", "menuSeq": 3, "menuIcon": "icon:pear", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191108590000000007", "menuName": "上课时段设置", "menuLevelcode": "000000002600000000040000000001", "menuSeq": 0, "menuIcon": "icon:coordinate", "menuShowstatus": "S", "pageName": "上课时段设置", "pageUrl": "ec/baseData/setOnTime"}, {"menuId": "MENU20191108590000000008", "menuName": "课酬规则设置", "menuLevelcode": "000000002600000000040000000002", "menuSeq": 1, "menuIcon": "icon:bank-card", "menuShowstatus": "S", "pageName": "课酬规则设置", "pageUrl": "ec/baseData/moneyRule"}, {"menuId": "MENU20191108590000000009", "menuName": "模块结束时间设置", "menuLevelcode": "000000002600000000040000000003", "menuSeq": 2, "menuIcon": "icon:s-promotion", "menuShowstatus": "S", "pageName": "模块结束时间设置", "pageUrl": "ec/baseData/modelEndTimeSet"}, {"menuId": "MENU20191108590000000037", "menuName": "双师教室设置", "menuLevelcode": "000000002600000000040000000004", "menuSeq": 3, "menuIcon": "icon:document-add", "menuShowstatus": "S", "pageName": "双师教室设置", "pageUrl": "ec/baseData/doubleTeachers"}]}, {"menuId": "MENU20191108590000000003", "menuName": "讲师管理", "menuLevelcode": "00000000260000000005", "menuSeq": 4, "menuIcon": "icon:s-operation", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191108590000000010", "menuName": "讲师信息管理", "menuLevelcode": "000000002600000000050000000001", "menuSeq": 0, "menuIcon": "icon:alarm-clock", "menuShowstatus": "S", "pageName": "讲师信息管理", "pageUrl": "ec/teachMeage/teachInfoMeage"}, {"menuId": "MENU20191108590000000011", "menuName": "讲师课酬管理", "menuLevelcode": "000000002600000000050000000002", "menuSeq": 1, "menuIcon": "icon:watch", "menuShowstatus": "S", "pageName": "讲师课酬管理", "pageUrl": "ec/teachMeage/teachMoneyMeage"}]}, {"menuId": "MENU20191108590000000004", "menuName": "排课管理", "menuLevelcode": "00000000260000000006", "menuSeq": 5, "menuIcon": "icon:picture", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191009010000000002", "menuName": "面授课程表", "menuLevelcode": "000000002600000000060000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": "面授课程表", "pageUrl": "ec/setTeach/faceTeach"}, {"menuId": "MENU20191108590000000012", "menuName": "企业定制课表", "menuLevelcode": "000000002600000000060000000002", "menuSeq": 1, "menuIcon": "icon:s-finance", "menuShowstatus": "S", "pageName": "企业定制课程表", "pageUrl": "ec/setTeach/compontySelfClass"}, {"menuId": "MENU20191108590000000013", "menuName": "公开课程表", "menuLevelcode": "000000002600000000060000000003", "menuSeq": 2, "menuIcon": "icon:box", "menuShowstatus": "S", "pageName": "公开课程表", "pageUrl": "ec/setTeach/commonClass"}, {"menuId": "MENU20191108590000000014", "menuName": "直播课程表", "menuLevelcode": "000000002600000000060000000004", "menuSeq": 3, "menuIcon": "icon:message-solid", "menuShowstatus": "S", "pageName": "直播课程表", "pageUrl": "ec/setTeach/liveBroadcastClass"}, {"menuId": "MENU20191108590000000015", "menuName": "双师课程表", "menuLevelcode": "000000002600000000060000000005", "menuSeq": 4, "menuIcon": "icon:star-on", "menuShowstatus": "S", "pageName": "双师课程表", "pageUrl": "ec/setTeach/doubleTeachClass"}]}, {"menuId": "MENU20191108590000000005", "menuName": "教学成本", "menuLevelcode": "00000000260000000007", "menuSeq": 6, "menuIcon": "icon:odometer", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191108590000000016", "menuName": "课程课酬成本", "menuLevelcode": "000000002600000000070000000001", "menuSeq": 0, "menuIcon": "icon:eleme", "menuShowstatus": "S", "pageName": "课程课酬成本", "pageUrl": "ec/teachMoney/classOutMoney"}, {"menuId": "MENU20191108590000000017", "menuName": "讲师出差成本", "menuLevelcode": "000000002600000000070000000002", "menuSeq": 1, "menuIcon": "icon:top-left", "menuShowstatus": "S", "pageName": "讲师出差成本", "pageUrl": "ec/teachMoney/goOutMoney"}, {"menuId": "MENU20191108590000000018", "menuName": "教材资料成本", "menuLevelcode": "000000002600000000070000000003", "menuSeq": 2, "menuIcon": "icon:info", "menuShowstatus": "S", "pageName": "教材资料成本", "pageUrl": "ec/teachMoney/teachBookOutMoney"}, {"menuId": "MENU20191108590000000019", "menuName": "教室租赁成本", "menuLevelcode": "000000002600000000070000000004", "menuSeq": 3, "menuIcon": "icon:warning", "menuShowstatus": "S", "pageName": "教室租赁成本", "pageUrl": "ec/teachMoney/teachRoomOutMoney"}]}, {"menuId": "MENU20191108590000000006", "menuName": "教学统计", "menuLevelcode": "00000000260000000008", "menuSeq": 7, "menuIcon": "icon:trophy-1", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191108590000000020", "menuName": "教务排课统计", "menuLevelcode": "000000002600000000080000000001", "menuSeq": 0, "menuIcon": "icon:lightning", "menuShowstatus": "S", "pageName": "教务排课统计", "pageUrl": "ec/teachStatistics/teachWorkStatistics"}, {"menuId": "MENU20191108590000000021", "menuName": "分校排课统计", "menuLevelcode": "000000002600000000080000000002", "menuSeq": 1, "menuIcon": "icon:shopping-bag-2", "menuShowstatus": "S", "pageName": "分校排课统计", "pageUrl": "ec/teachStatistics/schoolWorkStatistics"}, {"menuId": "MENU20191108590000000022", "menuName": "项目排课统计", "menuLevelcode": "000000002600000000080000000003", "menuSeq": 2, "menuIcon": "icon:s-operation", "menuShowstatus": "S", "pageName": "项目排课统计", "pageUrl": "ec/teachStatistics/projectClassStatistics"}, {"menuId": "MENU20191108590000000023", "menuName": "教学成本统计", "menuLevelcode": "000000002600000000080000000004", "menuSeq": 3, "menuIcon": "icon:trophy-1", "menuShowstatus": "S", "pageName": "教学成本统计", "pageUrl": "ec/teachStatistics/teachOutMoneyStatistics"}, {"menuId": "MENU20191108590000000024", "menuName": "分校成本统计", "menuLevelcode": "000000002600000000080000000005", "menuSeq": 4, "menuIcon": "icon:shopping-cart-full", "menuShowstatus": "S", "pageName": "分校成本统计", "pageUrl": "ec/teachStatistics/schoolOutMoneyStatistics"}, {"menuId": "MENU20191108590000000025", "menuName": "项目成本统计", "menuLevelcode": "000000002600000000080000000006", "menuSeq": 5, "menuIcon": "icon:sort", "menuShowstatus": "S", "pageName": "项目成本统计", "pageUrl": "ec/teachStatistics/projectOutMoneyStatistics"}, {"menuId": "MENU20191108590000000026", "menuName": "课程成本统计", "menuLevelcode": "000000002600000000080000000007", "menuSeq": 6, "menuIcon": "icon:eleme", "menuShowstatus": "S", "pageName": "课程成本统计", "pageUrl": "ec/teachStatistics/classOutMoneyStatistics"}, {"menuId": "MENU20191108590000000027", "menuName": "讲师成本统计", "menuLevelcode": "000000002600000000080000000008", "menuSeq": 7, "menuIcon": "icon:potato-strips", "menuShowstatus": "S", "pageName": "讲师成本统计", "pageUrl": "ec/teachStatistics/teachersOutMoneyStatistics"}]}]}, {"menuId": "MENU20190718010000000004", "menuName": "基础数据", "menuLevelcode": "0000000018", "menuSeq": 11, "menuIcon": "icon:platform-eleme", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190928010000000001", "menuName": "项目管理", "menuLevelcode": "00000000180000000014", "menuSeq": 4, "menuIcon": "", "menuShowstatus": "", "pageName": "项目周期列表", "pageUrl": "bd/bdPypList", "children": [{"menuId": "MENU20190929010000000007", "menuName": "项目设置", "menuLevelcode": "000000001800000000140000000002", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "项目设置", "pageUrl": "bd/bdProject"}, {"menuId": "MENU20190929010000000008", "menuName": "项目年份", "menuLevelcode": "000000001800000000140000000003", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "项目年份", "pageUrl": "bd/bdProjectYearList"}, {"menuId": "MENU20190928010000000009", "menuName": "项目周期", "menuLevelcode": "000000001800000000140000000001", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": "项目周期列表", "pageUrl": "bd/bdPypList"}, {"menuId": "MENU20190929010000000009", "menuName": "项目周期地区", "menuLevelcode": "000000001800000000140000000004", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "", "pageName": "项目周期地区", "pageUrl": "bd/bdPypaList"}]}, {"menuId": "MENU20190926010000000006", "menuName": "科目管理", "menuLevelcode": "00000000180000000006", "menuSeq": 6, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190929010000000010", "menuName": "科目列表", "menuLevelcode": "000000001800000000060000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "科目列表", "pageUrl": "bd/bdSubjectList"}, {"menuId": "MENU20191118590000000001", "menuName": "科目组合", "menuLevelcode": "000000001800000000060000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "科目组合", "pageUrl": "bd/bdSgList"}]}, {"menuId": "MENU20190926010000000007", "menuName": "模块管理", "menuLevelcode": "00000000180000000007", "menuSeq": 7, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190929010000000018", "menuName": "模块列表", "menuLevelcode": "000000001800000000070000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "模块列表", "pageUrl": "bd/bdModulList"}, {"menuId": "MENU20190930010000000001", "menuName": "模块实例", "menuLevelcode": "000000001800000000070000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "模块实例", "pageUrl": "bd/bdModulinstList"}]}, {"menuId": "MENU20190926010000000008", "menuName": "班级管理", "menuLevelcode": "00000000180000000008", "menuSeq": 8, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191007010000000001", "menuName": "班型列表", "menuLevelcode": "000000001800000000080000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "班型列表", "pageUrl": "bd/bdClasstypeList"}, {"menuId": "MENU20191007010000000002", "menuName": "班级列表", "menuLevelcode": "000000001800000000080000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "班级列表", "pageUrl": "bd/bdClassList"}]}, {"menuId": "MENU20190926010000000009", "menuName": "教师管理", "menuLevelcode": "00000000180000000009", "menuSeq": 9, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190930010000000002", "menuName": "教师列表", "menuLevelcode": "000000001800000000090000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "教师列表", "pageUrl": "bd/bdTeacher<PERSON>ist"}]}, {"menuId": "MENU20190926010000000010", "menuName": "分校管理", "menuLevelcode": "00000000180000000010", "menuSeq": 10, "menuIcon": "", "menuShowstatus": "", "pageName": "分校管理列表", "pageUrl": "bd/bdSchoolList", "children": [{"menuId": "MENU20190930010000000004", "menuName": "分校列表", "menuLevelcode": "000000001800000000100000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "分校管理列表", "pageUrl": "bd/bdSchoolList"}]}, {"menuId": "MENU20190926010000000011", "menuName": "学员管理", "menuLevelcode": "00000000180000000011", "menuSeq": 11, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190930010000000003", "menuName": "学员列表", "menuLevelcode": "000000001800000000110000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "学员列表", "pageUrl": "bd/bdStudentList"}]}, {"menuId": "MENU20190926010000000012", "menuName": "部门员工", "menuLevelcode": "00000000180000000012", "menuSeq": 12, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190930010000000005", "menuName": "部门设置", "menuLevelcode": "000000001800000000120000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "部门设置", "pageUrl": "bd/bdDpt"}, {"menuId": "MENU20190930010000000006", "menuName": "员工列表", "menuLevelcode": "000000001800000000120000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "部门员工列表", "pageUrl": "bd/bdEeList"}]}, {"menuId": "MENU20190926010000000013", "menuName": "物品管理", "menuLevelcode": "00000000180000000013", "menuSeq": 13, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190930010000000007", "menuName": "物品分类设置", "menuLevelcode": "000000001800000000130000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "物品分类设置", "pageUrl": "bd/bdGoodstype"}, {"menuId": "MENU20190930010000000008", "menuName": "物品列表", "menuLevelcode": "000000001800000000130000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "物品列表", "pageUrl": "bd/bdGoodsList"}]}]}, {"menuId": "MENU20191008010000000001", "menuName": "客户中心", "menuLevelcode": "0000000025", "menuSeq": 13, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191008010000000002", "menuName": "线索管理", "menuLevelcode": "00000000250000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191011010000000006", "menuName": "线索管理", "menuLevelcode": "000000002500000000010000000002", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": "线索管理", "pageUrl": "ct/cueManage/cueAcquisition"}, {"menuId": "MENU20191008010000000003", "menuName": "待分配线索", "menuLevelcode": "000000002500000000010000000001", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "S", "pageName": "待分配线索", "pageUrl": "ct/cueManage/toBeallocated"}, {"menuId": "MENU20191014010000000001", "menuName": "延迟分配线索", "menuLevelcode": "000000002500000000010000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "S", "pageName": "延迟分配线索", "pageUrl": "ct/cueManage/immature"}]}, {"menuId": "MENU20191014010000000002", "menuName": "客户管理", "menuLevelcode": "00000000250000000003", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191014010000000003", "menuName": "我的客户", "menuLevelcode": "000000002500000000030000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": "我的客户", "pageUrl": "ct/customerManage/myClien"}]}, {"menuId": "MENU20191011010000000003", "menuName": "客户营销", "menuLevelcode": "00000000250000000002", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191011010000000005", "menuName": "客户详情", "menuLevelcode": "000000002500000000020000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": "客户详情", "pageUrl": "ct/customerMarket/customerDetails"}]}, {"menuId": "MENU20191103590000000001", "menuName": "规则设置", "menuLevelcode": "00000000250000000004", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191103590000000002", "menuName": "线索分配规则", "menuLevelcode": "000000002500000000040000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": "线索分配规则", "pageUrl": "ct/setting/cuesConfig"}, {"menuId": "MENU20191107590000000001", "menuName": "员工权重计算规则", "menuLevelcode": "000000002500000000040000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "S", "pageName": "员工权重计算规则", "pageUrl": "ct/setting/wcr"}]}]}, {"menuId": "MENU20190131010000000001", "menuName": "安全", "menuLevelcode": "0000000019", "menuSeq": 15, "menuIcon": "icon:s-release", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190125010000000011", "menuName": "菜单管理", "menuLevelcode": "00000000190000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190125010000000017", "menuName": "角色管理", "menuLevelcode": "000000001900000000010000000008", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "", "pageName": "角色管理", "pageUrl": "uc/roleList"}, {"menuId": "MENU20190125010000000018", "menuName": "页面列表", "menuLevelcode": "000000001900000000010000000004", "menuSeq": 4, "menuIcon": "", "menuShowstatus": "", "pageName": "页面管理", "pageUrl": "uc/pageManagerList"}, {"menuId": "MENU20190131010000000005", "menuName": "权限列表", "menuLevelcode": "000000001900000000010000000005", "menuSeq": 5, "menuIcon": "", "menuShowstatus": "", "pageName": "权限列表", "pageUrl": "uc/authList"}, {"menuId": "MENU20190220010000000010", "menuName": "用户列表", "menuLevelcode": "000000001900000000010000000006", "menuSeq": 6, "menuIcon": "", "menuShowstatus": "", "pageName": "用户列表", "pageUrl": "uc/userList"}, {"menuId": "MENU20190319010000000001", "menuName": "配置管理", "menuLevelcode": "000000001900000000010000000007", "menuSeq": 7, "menuIcon": "", "menuShowstatus": "", "pageName": "配置管理", "pageUrl": "uc/configList"}]}, {"menuId": "MENU20191120590000000001", "menuName": "权限管理", "menuLevelcode": "00000000190000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191120590000000002", "menuName": "角色权限", "menuLevelcode": "000000001900000000020000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": "角色权限", "pageUrl": "sys/privile/role"}, {"menuId": "MENU20191120590000000003", "menuName": "菜单管理", "menuLevelcode": "000000001900000000020000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "菜单管理", "pageUrl": "sys/privile/menu"}]}]}, {"menuId": "MENU20190219010000000024", "menuName": "内容", "menuLevelcode": "0000000009", "menuSeq": 16, "menuIcon": "icon:goblet", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190221010000000001", "menuName": "banner列表", "menuLevelcode": "00000000090000000004", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "banner列表", "pageUrl": "cm/bannerList"}, {"menuId": "MENU20190219010000000027", "menuName": "栏目列表", "menuLevelcode": "00000000090000000002", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": "栏目列表", "pageUrl": "cm/lanmuList"}, {"menuId": "MENU20190219010000000028", "menuName": "资讯列表", "menuLevelcode": "00000000090000000003", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "", "pageName": "资讯列表", "pageUrl": "cm/consultList"}]}, {"menuId": "MENU20190125010000000008", "menuName": "合作推广", "menuLevelcode": "0000000002", "menuSeq": 17, "menuIcon": "icon:money", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190125010000000010", "menuName": "广告", "menuLevelcode": "00000000020000000003", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190125010000000015", "menuName": "文字广告", "menuLevelcode": "000000000200000000030000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "文字广告", "pageUrl": "sl/adList"}, {"menuId": "MENU20190130010000000002", "menuName": "图片广告", "menuLevelcode": "000000000200000000030000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "图片广告", "pageUrl": "sl/pictureList"}]}, {"menuId": "MENU20190129010000000001", "menuName": "代理商列表", "menuLevelcode": "00000000020000000001", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": "代理商列表", "pageUrl": "sl/agentList"}, {"menuId": "MENU20190125010000000009", "menuName": "合作项目", "menuLevelcode": "00000000020000000002", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190125010000000013", "menuName": "单个班级", "menuLevelcode": "000000000200000000020000000004", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "单个班级", "pageUrl": "sl/singleList"}, {"menuId": "MENU20190125010000000014", "menuName": "组合班级", "menuLevelcode": "000000000200000000020000000003", "menuSeq": 10, "menuIcon": "", "menuShowstatus": "", "pageName": "组合班级", "pageUrl": "sl/multiList"}]}]}, {"menuId": "MENU20190131010000000002", "menuName": "题库", "menuLevelcode": "0000000008", "menuSeq": 26, "menuIcon": "icon:s-management", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190716010000000001", "menuName": "试卷管理", "menuLevelcode": "00000000080000000012", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190716010000000004", "menuName": "试卷列表", "menuLevelcode": "000000000800000000120000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": "试卷列表", "pageUrl": "qb/paperManage/paperList"}, {"menuId": "MENU20190716010000000005", "menuName": "添加试卷", "menuLevelcode": "000000000800000000120000000004", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "", "pageName": "添加试卷", "pageUrl": "qb/paperManage/addPaper"}, {"menuId": "MENU20190716010000000006", "menuName": "考试记录管理", "menuLevelcode": "000000000800000000120000000005", "menuSeq": 4, "menuIcon": "", "menuShowstatus": "", "pageName": "考试记录管理", "pageUrl": "qb/paperManage/examList"}, {"menuId": "MENU20190716010000000007", "menuName": "试卷统计管理", "menuLevelcode": "000000000800000000120000000006", "menuSeq": 5, "menuIcon": "", "menuShowstatus": "", "pageName": "试卷统计管理", "pageUrl": "qb/paperManage/paperStatisticsList"}, {"menuId": "MENU20190716010000000008", "menuName": "会员练习记录", "menuLevelcode": "000000000800000000120000000007", "menuSeq": 6, "menuIcon": "", "menuShowstatus": "", "pageName": "会员练习记录", "pageUrl": "qb/paperManage/userPracticeList"}, {"menuId": "MENU20190716010000000009", "menuName": "报名列表", "menuLevelcode": "000000000800000000120000000008", "menuSeq": 7, "menuIcon": "", "menuShowstatus": "", "pageName": "报名列表", "pageUrl": "qb/paperManage/enrollList"}, {"menuId": "MENU20190722010000000001", "menuName": "用户试卷解锁列表", "menuLevelcode": "000000000800000000120000000009", "menuSeq": 8, "menuIcon": "", "menuShowstatus": "", "pageName": "用户试卷解锁列表", "pageUrl": "qb/paperManage/unlockList"}]}, {"menuId": "MENU20190716010000000002", "menuName": "题库管理", "menuLevelcode": "00000000080000000013", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190716010000000010", "menuName": "题库列表", "menuLevelcode": "000000000800000000130000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "题库列表", "pageUrl": "qb/questionManage/questionList"}, {"menuId": "MENU20190716010000000011", "menuName": "添加试题", "menuLevelcode": "000000000800000000130000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "添加试题", "pageUrl": "qb/questionManage/addQuestion"}, {"menuId": "MENU20190716010000000012", "menuName": "错题反馈列表", "menuLevelcode": "000000000800000000130000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": "错题反馈列表", "pageUrl": "qb/questionManage/questionFeedBack"}, {"menuId": "MENU20190716010000000013", "menuName": "试题排错", "menuLevelcode": "000000000800000000130000000004", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "", "pageName": "试题排错", "pageUrl": "qb/questionManage/erratumQuestion"}]}, {"menuId": "MENU20190716010000000003", "menuName": "系统管理", "menuLevelcode": "00000000080000000014", "menuSeq": 5, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190716010000000014", "menuName": "数据字典", "menuLevelcode": "000000000800000000140000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null}, {"menuId": "MENU20190716010000000015", "menuName": "试卷类型", "menuLevelcode": "000000000800000000140000000002", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": "试卷类型", "pageUrl": "qb/systemManage/paperType"}, {"menuId": "MENU20190902010000000001", "menuName": "分类设置", "menuLevelcode": "000000000800000000140000000008", "menuSeq": 4, "menuIcon": "", "menuShowstatus": "", "pageName": "分类设置", "pageUrl": "qb/systemManage/classSetting"}, {"menuId": "MENU20190716010000000018", "menuName": "章节设置", "menuLevelcode": "000000000800000000140000000004", "menuSeq": 5, "menuIcon": "", "menuShowstatus": "", "pageName": "章节设置", "pageUrl": "qb/systemManage/sectionSetting"}, {"menuId": "MENU20190716010000000019", "menuName": "知识点设置", "menuLevelcode": "000000000800000000140000000005", "menuSeq": 6, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null}, {"menuId": "MENU20190716010000000020", "menuName": "每日一练设置", "menuLevelcode": "000000000800000000140000000006", "menuSeq": 7, "menuIcon": "", "menuShowstatus": "H", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190716010000000022", "menuName": "科目设置", "menuLevelcode": "0000000008000000001400000000060000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null}]}, {"menuId": "MENU20190716010000000021", "menuName": "小程序考试设置", "menuLevelcode": "000000000800000000140000000007", "menuSeq": 8, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null}]}]}, {"menuId": "MENU20190803010000000001", "menuName": "消息中心", "menuLevelcode": "0000000016", "menuSeq": 29, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190803010000000002", "menuName": "消息管理", "menuLevelcode": "00000000160000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190803010000000006", "menuName": "手动消息", "menuLevelcode": "000000001600000000010000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190803010000000010", "menuName": "定时消息", "menuLevelcode": "0000000016000000000100000000010000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "定时消息列表", "pageUrl": "mc/timingList"}, {"menuId": "MENU20190803010000000011", "menuName": "即时消息", "menuLevelcode": "0000000016000000000100000000010000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "即时消息列表", "pageUrl": "mc/handList"}]}, {"menuId": "MENU20190803010000000007", "menuName": "自动消息", "menuLevelcode": "000000001600000000010000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "自动消息列表", "pageUrl": "mc/autoList"}, {"menuId": "MENU20190803010000000008", "menuName": "消息记录", "menuLevelcode": "000000001600000000010000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": "消息记录列表", "pageUrl": "mc/recordList"}, {"menuId": "MENU20190803010000000009", "menuName": "消息统计", "menuLevelcode": "000000001600000000010000000004", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "", "pageName": "消息统计列表", "pageUrl": "mc/statistics<PERSON>hart"}]}, {"menuId": "MENU20190803010000000003", "menuName": "模板管理", "menuLevelcode": "00000000160000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "模板管理列表", "pageUrl": "mc/templateList"}, {"menuId": "MENU20190803010000000004", "menuName": "通道管理", "menuLevelcode": "00000000160000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": "通道管理列表", "pageUrl": "mc/channelList"}, {"menuId": "MENU20190803010000000005", "menuName": "配置管理", "menuLevelcode": "00000000160000000004", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "H", "pageName": null, "pageUrl": null}]}, {"menuId": "MENU20190716010000000023", "menuName": "活动中心", "menuLevelcode": "0000000010", "menuSeq": 30, "menuIcon": "", "menuShowstatus": "", "pageName": "活动列表", "pageUrl": "ac/activityList", "children": [{"menuId": "MENU20190716010000000024", "menuName": "活动列表", "menuLevelcode": "00000000100000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "活动列表", "pageUrl": "ac/activityList"}, {"menuId": "MENU20190716010000000025", "menuName": "拼团统计", "menuLevelcode": "00000000100000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "拼团统计", "pageUrl": "ac/cliqueStatisticsList"}, {"menuId": "MENU20190716010000000026", "menuName": "评论列表", "menuLevelcode": "00000000100000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": "评论列表", "pageUrl": "ac/discussList"}, {"menuId": "MENU20190716010000000027", "menuName": "话题列表", "menuLevelcode": "00000000100000000004", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "", "pageName": "话题列表", "pageUrl": "ac/topicList"}]}, {"menuId": "MENU20190717010000000001", "menuName": "收入确认", "menuLevelcode": "0000000011", "menuSeq": 31, "menuIcon": "icon:watermelon", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190717010000000002", "menuName": "学员收入确认", "menuLevelcode": "00000000110000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "学员收入确认", "pageUrl": "ir/financialOrders"}]}, {"menuId": "MENU20190718010000000001", "menuName": "课程中心", "menuLevelcode": "0000000012", "menuSeq": 35, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190718010000000002", "menuName": "课程列表", "menuLevelcode": "00000000120000000001", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "S", "pageName": "cc课程列表", "pageUrl": "cc/courseList"}, {"menuId": "MENU20191029590000000001", "menuName": "往期通过学员管理", "menuLevelcode": "00000000120000000004", "menuSeq": 7, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191029590000000002", "menuName": "列表页", "menuLevelcode": "000000001200000000040000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "往期通过学员列表", "pageUrl": "cc/passStudentList"}, {"menuId": "MENU20191029590000000003", "menuName": "添加学员", "menuLevelcode": "000000001200000000040000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "添加学员", "pageUrl": "cc/passStudentAdd"}]}]}, {"menuId": "MENU20190722010000000002", "menuName": "第三方适配器", "menuLevelcode": "0000000015", "menuSeq": 38, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190722010000000003", "menuName": "微信账号列表", "menuLevelcode": "00000000150000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "微信账号列表", "pageUrl": "ta/wxAccoutlist"}, {"menuId": "MENU20190723010000000001", "menuName": "微信用户列表", "menuLevelcode": "00000000150000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "微信用户列表", "pageUrl": "ta/wxUserlist"}, {"menuId": "MENU20190727010000000001", "menuName": "MQ异常信息列表", "menuLevelcode": "00000000150000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": "MQ异常信息列表", "pageUrl": "ta/mqExcelist"}, {"menuId": "MENU20190727010000000004", "menuName": "微信异常信息列表", "menuLevelcode": "00000000150000000004", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "", "pageName": "微信异常信息列表", "pageUrl": "ta/wxExcelist"}]}, {"menuId": "MENU20190807010000000001", "menuName": "文件服务", "menuLevelcode": "0000000017", "menuSeq": 39, "menuIcon": "", "menuShowstatus": "", "pageName": "文件管理", "pageUrl": "/fs/fileManager", "children": [{"menuId": "MENU20190807010000000002", "menuName": "文件管理", "menuLevelcode": "00000000170000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "文件管理", "pageUrl": "/fs/fileManager"}]}, {"menuId": "MENU20190828010000000003", "menuName": "仓储中心", "menuLevelcode": "0000000020", "menuSeq": 42, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190927010000000014", "menuName": "书籍管理", "menuLevelcode": "00000000200000000005", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "书籍管理", "pageUrl": "st/bookManagement/bookManagement"}, {"menuId": "MENU20190903010000000004", "menuName": "仓储管理", "menuLevelcode": "00000000200000000002", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190903010000000005", "menuName": "仓位管理", "menuLevelcode": "000000002000000000020000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "仓位管理", "pageUrl": "st/warePlace/warePlaceList"}, {"menuId": "MENU20190904010000000001", "menuName": "货物码管理", "menuLevelcode": "000000002000000000020000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "货物码管理", "pageUrl": "st/goodsCode/goodsCodeList"}, {"menuId": "MENU20190905010000000001", "menuName": "入库管理", "menuLevelcode": "000000002000000000020000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": "入库管理", "pageUrl": "st/stockInManage/stockInManageList"}, {"menuId": "MENU20190906010000000001", "menuName": "出库管理", "menuLevelcode": "000000002000000000020000000004", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "", "pageName": "出库管理", "pageUrl": "st/stockOutManage/stockOutManageList"}]}, {"menuId": "MENU20190907010000000001", "menuName": "出入库记录查看", "menuLevelcode": "00000000200000000003", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "", "pageName": "出入库记录查看", "pageUrl": "st/stockRecordList"}, {"menuId": "MENU20190907010000000002", "menuName": "报表管理", "menuLevelcode": "00000000200000000004", "menuSeq": 4, "menuIcon": "", "menuShowstatus": "", "pageName": "报表管理", "pageUrl": "st/reportManagement/stockReportList"}]}, {"menuId": "MENU20190927010000000006", "menuName": "物流中心", "menuLevelcode": "0000000023", "menuSeq": 44, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190927010000000007", "menuName": "书籍申请", "menuLevelcode": "00000000230000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190927010000000008", "menuName": "发起申请", "menuLevelcode": "000000002300000000010000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "发起申请", "pageUrl": "lg/bookApply/bookApplyList"}, {"menuId": "MENU20190927010000000009", "menuName": "订单查看", "menuLevelcode": "000000002300000000010000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "订单查看", "pageUrl": "lg/bookApply/billList"}]}, {"menuId": "MENU20190927010000000010", "menuName": "审核管理", "menuLevelcode": "00000000230000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190927010000000011", "menuName": "未审核", "menuLevelcode": "000000002300000000020000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "未审核", "pageUrl": "lg/checkManagement/unCheckList"}, {"menuId": "MENU20190927010000000012", "menuName": "已审核", "menuLevelcode": "000000002300000000020000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "已审核", "pageUrl": "lg/checkManagement/checkedList"}]}]}, {"menuId": "MENU20190929010000000001", "menuName": "系统管理", "menuLevelcode": "0000000024", "menuSeq": 46, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190929010000000002", "menuName": "个人资料", "menuLevelcode": "00000000240000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "个人资料", "pageUrl": "sys/userInfo"}, {"menuId": "MENU20190929010000000003", "menuName": "客户端版本管理", "menuLevelcode": "00000000240000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20190929010000000004", "menuName": "客户端类型列表", "menuLevelcode": "000000002400000000020000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "客户端类型列表", "pageUrl": "sys/clientVersionManagement/clientTypeList"}, {"menuId": "MENU20190929010000000005", "menuName": "客户端版本管理", "menuLevelcode": "000000002400000000020000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "客户端版本管理", "pageUrl": "sys/clientVersionManagement/versionManagement"}, {"menuId": "MENU20190929010000000006", "menuName": "客户端灰度管理", "menuLevelcode": "000000002400000000020000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": "客户端灰度管理", "pageUrl": "sys/clientVersionManagement/clientReleaseManagement"}]}, {"menuId": "MENU20191012010000000001", "menuName": "基础设置", "menuLevelcode": "00000000240000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191012010000000002", "menuName": "学员服务项设置", "menuLevelcode": "000000002400000000030000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": "学员服务项设置", "pageUrl": "sys/basic/ssiSetting"}, {"menuId": "MENU20191012010000000003", "menuName": "服务分校设置", "menuLevelcode": "000000002400000000030000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "服务分校设置", "pageUrl": "sys/basic/sbSetting"}]}, {"menuId": "MENU20191012010000000004", "menuName": "规则设置", "menuLevelcode": "00000000240000000004", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191012010000000005", "menuName": "学员分配规则", "menuLevelcode": "000000002400000000040000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "学员分配规则", "pageUrl": "sys/ruleSetting/saSetting"}, {"menuId": "MENU20191012010000000006", "menuName": "脱离学习提醒规则", "menuLevelcode": "000000002400000000040000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "", "pageName": "脱离学习提醒规则", "pageUrl": "sys/ruleSetting/balrrSetting"}, {"menuId": "MENU20191012010000000007", "menuName": "学习督导频率设置", "menuLevelcode": "000000002400000000040000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "", "pageName": "学习督导频率设置", "pageUrl": "sys/ruleSetting/lsfSetting"}]}]}, {"menuId": "MENU20191021590000000001", "menuName": "公开课管理", "menuLevelcode": "0000000028", "menuSeq": 47, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191022590000000001", "menuName": "课程列表", "menuLevelcode": "00000000280000000002", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": "课程列表", "pageUrl": "os/course"}, {"menuId": "MENU20191021590000000002", "menuName": "公开课设置", "menuLevelcode": "00000000280000000001", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "S", "pageName": "公开课管理", "pageUrl": "os/setting"}, {"menuId": "MENU20191025590000000001", "menuName": "预约列表", "menuLevelcode": "00000000280000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "S", "pageName": "预约列表", "pageUrl": "os/order"}]}, {"menuId": "MENU20191025590000000002", "menuName": "外部数据", "menuLevelcode": "0000000029", "menuSeq": 48, "menuIcon": "", "menuShowstatus": "", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191025590000000003", "menuName": "手机号段管理", "menuLevelcode": "00000000290000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "", "pageName": "手机号段管理", "pageUrl": "ex/exmobileList"}]}, {"menuId": "MENU20191108590000000028", "menuName": "订单中心", "menuLevelcode": "0000000030", "menuSeq": 49, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191111590000000002", "menuName": "工作任务", "menuLevelcode": "00000000300000000012", "menuSeq": 12, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191111590000000004", "menuName": "我的任务", "menuLevelcode": "000000003000000000120000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": "我的任务", "pageUrl": "oc/task/myTask"}, {"menuId": "MENU20191111590000000005", "menuName": "进行中任务", "menuLevelcode": "000000003000000000120000000002", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "S", "pageName": "进行中任务", "pageUrl": "oc/task/onGoingTask"}, {"menuId": "MENU20191111590000000006", "menuName": "已完成任务", "menuLevelcode": "000000003000000000120000000003", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "S", "pageName": "已完成任务", "pageUrl": "oc/task/complatedTask"}]}, {"menuId": "MENU20191111590000000001", "menuName": "订单管理", "menuLevelcode": "00000000300000000011", "menuSeq": 13, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191108590000000030", "menuName": "订单列表", "menuLevelcode": "000000003000000000110000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": "订单列表", "pageUrl": "oc/orderManages/order"}, {"menuId": "MENU20191108590000000031", "menuName": "重读订单", "menuLevelcode": "000000003000000000110000000008", "menuSeq": 1, "menuIcon": "", "menuShowstatus": "S", "pageName": "重读订单", "pageUrl": "oc/orderManages/re"}, {"menuId": "MENU20191108590000000034", "menuName": "换班订单", "menuLevelcode": "000000003000000000110000000007", "menuSeq": 2, "menuIcon": "", "menuShowstatus": "S", "pageName": "换班订单", "pageUrl": "oc/orderManages/change"}, {"menuId": "MENU20191108590000000033", "menuName": "转让订单", "menuLevelcode": "000000003000000000110000000006", "menuSeq": 3, "menuIcon": "", "menuShowstatus": "S", "pageName": "转让订单", "pageUrl": "oc/orderManages/assign"}, {"menuId": "MENU20191108590000000032", "menuName": "顺延订单", "menuLevelcode": "000000003000000000110000000005", "menuSeq": 4, "menuIcon": "", "menuShowstatus": "S", "pageName": "顺延订单", "pageUrl": "oc/orderManages/postpone"}, {"menuId": "MENU20191108590000000036", "menuName": "退费订单", "menuLevelcode": "000000003000000000110000000004", "menuSeq": 5, "menuIcon": "", "menuShowstatus": "S", "pageName": "退费订单", "pageUrl": "oc/orderManages/refund"}, {"menuId": "MENU20191108590000000035", "menuName": "赠课订单", "menuLevelcode": "000000003000000000110000000003", "menuSeq": 6, "menuIcon": "", "menuShowstatus": "S", "pageName": "赠课订单", "pageUrl": "oc/orderManages/give"}, {"menuId": "MENU20191108590000000029", "menuName": "电商订单", "menuLevelcode": "000000003000000000110000000002", "menuSeq": 7, "menuIcon": "", "menuShowstatus": "S", "pageName": "电商订单", "pageUrl": "oc/orderManages/ec"}]}, {"menuId": "MENU20191111590000000003", "menuName": "学员管理", "menuLevelcode": "00000000300000000013", "menuSeq": 15, "menuIcon": "", "menuShowstatus": "S", "pageName": null, "pageUrl": null, "children": [{"menuId": "MENU20191111590000000007", "menuName": "学员列表", "menuLevelcode": "000000003000000000130000000001", "menuSeq": 0, "menuIcon": "", "menuShowstatus": "S", "pageName": "学员列表", "pageUrl": "oc/studentManages/student"}]}]}]