package com.niceloo.uc.role;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.uc.api.FdmApi;
import com.niceloo.uc.api.FdmApiclient;
import com.niceloo.uc.api.FdmApiparam;
import com.niceloo.uc.api.FdmApirtn;
import com.niceloo.uc.model.UcMenu;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.nobject.common.db.ConnectionPool;
import org.nobject.common.db.DBFactory;
import org.nobject.common.lang.StringUtils;
import org.nobject.common.log.Logger;
import org.testng.annotations.Test;

import java.io.FileInputStream;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Auther hepangui
 * @Date 2019/11/2 0002
 */
public class CreateFuncsByExcel {

	static Logger logger = Logger.getLogger("aa");

	private DBFactory dbFactory;


	CommonDao commonDao ;
	public void initDb() throws Exception {
		ConnectionPool pool = new ConnectionPool();

//		pool.setUsername("root");
//		pool.setPassword("123456");
//		pool.setUrl("*******************************************************************************************************************");

//		pool.setUsername("db_writer");
//		pool.setPassword("6qD^8mbNsd&j");
//		pool.setUrl("******************************************************************************************************************");

		pool.setUsername("db_writer");
		pool.setPassword("123456");
		pool.setUrl("*******************************************************************************************************************");


		pool.setDriverClassName("org.gjt.mm.mysql.Driver");
		pool.setMaxSize(2);
		pool.setMaxTimeout(1000L);
		dbFactory = new DBFactory();
		dbFactory.setPool(pool);
		dbFactory.setShowsql(true);

		dbFactory.buildORM(UcMenu.class);

		 commonDao = new CommonDao();
		commonDao.setDbFactory(dbFactory);

		String sql = "delete from UcMenu where menuCreatedate ='"+updateTime+"'";
		commonDao.execute(sql,new Object[]{});

	}

	private String updateTime = "2020-03-03 03:03:03";



	@Test
	public void test() throws Exception {
		this.initDb();
		HSSFWorkbook workbook = new HSSFWorkbook(new FileInputStream("F:/aaa.xls"));


		HSSFSheet sheet = workbook.getSheetAt(0);

		Set<String> repeatCodes = new HashSet<>();

		UcMenu currApp = null;
		UcMenu currmodel = null;
		UcMenu currPage = null;

		for (int i = 2; i < sheet.getLastRowNum(); i++) {
			HSSFRow row = sheet.getRow(i);
			HSSFCell cell = row.getCell(0);
			int currLevel = 1;

			String v = cell.getStringCellValue();
			if(!StringUtils.isEmpty(v)){
				if(v.equals("安全")){
					System.out.println(repeatCodes);
					return;
				}


				String sql = "select * from UcMenu where menuName = '"+v+"' and length(menuLevelcode) = 10";
				UcMenu ucMenu = commonDao.queryObject(sql, new Object[]{}, UcMenu.class);
				currApp = ucMenu;
			}

			cell = row.getCell(1);

			v = cell.getStringCellValue();
			if(!StringUtils.isEmpty(v)){
				String sql = "select * from UcMenu where menuName = '"+v+"' and length(menuLevelcode) = 20 and menuLevelcode like '"+currApp.getMenuLevelcode()+"%'";
				UcMenu ucMenu = commonDao.queryObject(sql, new Object[]{}, UcMenu.class);
				currmodel = ucMenu;
			}
			if(currmodel.getMenuType().equals("page")){
				currPage = currmodel;
				currLevel = 1;
			}else{
				cell = row.getCell(2);

				v = cell.getStringCellValue();
				if(!StringUtils.isEmpty(v)){
					String sql = "select * from UcMenu where menuName = '"+v+"' and length(menuLevelcode) = 30 and menuLevelcode like '"+currmodel.getMenuLevelcode()+"%'";
					UcMenu ucMenu = commonDao.queryObject(sql, new Object[]{}, UcMenu.class);
					currPage = ucMenu;
				}
				currLevel = 1;
			}


			cell = row.getCell(3);

			v = cell.getStringCellValue();
			if(currApp == null || currmodel == null || currPage == null){
				throw new Exception("hehhe");
			}

			if(!StringUtils.isEmpty(v)){
				currPage.setMenuCode(v);
				currPage.setMenuModifieddate(updateTime);
				commonDao.update(currPage);
			}

			cell = row.getCell(4);
			v = cell.getStringCellValue();

			UcMenu ucMenu = new UcMenu();
			ucMenu.setMenuName(v);

			cell = row.getCell(5);
			v =cell.getStringCellValue();

			ucMenu.setMenuCode(v);

			String s = "select * from UcMenu where menuCode = '"+v+"'";
			UcMenu o = commonDao.queryObject(s,new Object[]{},UcMenu.class);
			if(o !=null){
				if(o.getMenuType().equals("func")){
					o.setMenuName(o.getMenuName()+"/"+ucMenu.getMenuName());
					commonDao.update(o);
					continue;
				}else{
					continue;
				}
			}

			ucMenu.setMenuCreatedate(updateTime);
			ucMenu.setMenuModifieddate(updateTime);
			ucMenu.setMenuSeq(i);
			long a = 10000000002L + i;
			ucMenu.setMenuId("MENU20203030"+a);
			ucMenu.setMenuLevelcode(currPage.getMenuLevelcode()+StringUtils.fillEmptyWithStr(currLevel++,10,"0"));
			ucMenu.setMenuDisablestatus("E");
			ucMenu.setMenuType("func");
			commonDao.save(ucMenu);
		}

	}
}
