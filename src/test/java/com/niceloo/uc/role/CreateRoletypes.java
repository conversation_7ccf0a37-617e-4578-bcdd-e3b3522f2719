package com.niceloo.uc.role;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.dao.DoResult;
import com.niceloo.uc.model.UcMenu;
import org.nobject.common.db.ConnectionPool;
import org.nobject.common.db.DBFactory;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.log.Logger;
import org.testng.annotations.Test;

/**
 * @Auther hepangui
 * @Date 2020/3/4 0004
 */
public class CreateRoletypes {
	static Logger logger = Logger.getLogger("aa");

	private DBFactory dbFactory;


	CommonDao commonDao ;
	public void initDb() throws Exception {
		ConnectionPool pool = new ConnectionPool();

		pool.setUsername("root");
		pool.setPassword("123456");
		pool.setUrl("*******************************************************************************************************************");

//		pool.setUsername("db_writer");
//		pool.setPassword("6qD^8mbNsd&j");
//		pool.setUrl("******************************************************************************************************************");

		pool.setDriverClassName("org.gjt.mm.mysql.Driver");
		pool.setMaxSize(2);
		pool.setMaxTimeout(1000L);
		dbFactory = new DBFactory();
		dbFactory.setPool(pool);
		dbFactory.setShowsql(true);

		dbFactory.buildORM(UcMenu.class);

		commonDao = new CommonDao();
		commonDao.setDbFactory(dbFactory);

		String sql = "delete from UcDict where dictType ='roletype'";
		commonDao.execute(sql,new Object[]{});

	}

	@Test
	public void test1() throws Exception {
		this.initDb();
		String [][] codes = new String[][]{
				{"school_business","分校业务"},
				{"school_finance","分校财务"},
				{"school_headmaster","分校校长"},
				{"main_business","总部业务"},
				{"main_finance","总部财务"},
				{"main_cashier","总部出纳"},
				{"main_office","总经办"},
		};
		int i=1;
		for (String[] code : codes) {
			long aaa = 10000000000L +i++;
			String id = "DICT201905010"+aaa;

			String sql = "INSERT INTO `UcDict`(`dictId`, `dictType`, `dictName`, `dictCode`, `dictVal`, `dictMemo`, `dictLevelcode`, `dictSeq`) " +
					"VALUES (:id, 'roletype', :roleName, :roleCode, :roleCode, :roleName, '000000000"+i+"', "+i+");\n";

			DoResult execute = commonDao.execute(sql, MapUtils.toMap(new Object[][]{
					{"id", id},
					{"roleName", code[1]},
					{"roleCode", code[0]}
			}));

		}
	}
}
