package com.niceloo.uc.role;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.uc.model.UcMenu;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.nobject.common.db.ConnectionPool;
import org.nobject.common.db.DBFactory;
import org.nobject.common.exception.DBException;
import org.nobject.common.log.Logger;
import org.testng.annotations.Test;

import java.io.FileInputStream;
import java.util.*;

/**
 * @Auther hepangui
 * @Date 2019/11/2 0002
 */
public class CreateRolesByExcel {

	static Logger logger = Logger.getLogger("aa");

	private DBFactory dbFactory;


	CommonDao commonDao;

	public void initDb() throws Exception {
		ConnectionPool pool = new ConnectionPool();

		pool.setUsername("root");
		pool.setPassword("123456");
		pool.setUrl("*******************************************************************************************************************");

//		pool.setUsername("db_writer");
//		pool.setPassword("6qD^8mbNsd&j");
//		pool.setUrl("******************************************************************************************************************");

//		pool.setUsername("db_writer");
//		pool.setPassword("123456");
//		pool.setUrl("*******************************************************************************************************************");


		pool.setDriverClassName("org.gjt.mm.mysql.Driver");
		pool.setMaxSize(2);
		pool.setMaxTimeout(1000L);
		dbFactory = new DBFactory();
		dbFactory.setPool(pool);
		dbFactory.setShowsql(true);

		dbFactory.buildORM(UcMenu.class);

		commonDao = new CommonDao();
		commonDao.setDbFactory(dbFactory);

		String sql = "delete from UcRole where roleCreatedate ='"+updateTime+"'";
		commonDao.execute(sql,new Object[]{});
		sql = "delete from UcRolemenu where roleId like 'ROLE20200202'";
		commonDao.execute(sql,new Object[]{});
	}

	private String updateTime = "2020-03-03 03:03:03";


	/**
	 * 创建角色的步骤
	 * 解析excel，以列为标准，每一列为一个角色
	 * 当单元格上打对勾时，表示这个权限需要被赋予这个角色
	 * 当一个权限被勾选时，以为着他的父级菜单一定被勾选，因此，要根据code查询出父级菜单并勾选
	 * <p>
	 * 不想写的太复杂，算法写的简单一些，耗时长一点无所谓，反正就是个生成而已
	 *
	 * @throws Exception
	 */
	@Test
	public void test() throws Exception {
		this.initDb();
		HSSFWorkbook workbook = new HSSFWorkbook(new FileInputStream("F:/roles.xls"));


		HSSFSheet sheet = workbook.getSheetAt(0);

		HSSFRow row1 = sheet.getRow(2);
		HSSFCell cell1 = row1.getCell(6);
		System.out.println(cell1.getStringCellValue());

		Map<Integer, String> roleNames = new LinkedHashMap<>();
		for (int i = 6; i < row1.getLastCellNum(); i++) {
			String stringCellValue = row1.getCell(i).getStringCellValue();
//			System.out.println(stringCellValue);
			roleNames.put(i, stringCellValue);
		}

		Map<String, Set<String>> roleIds = new LinkedHashMap<>();

		for (int i = 3; i < sheet.getLastRowNum(); i++) {
			HSSFRow row = sheet.getRow(i);
			String code = row.getCell(5).getStringCellValue();

			if("uc/role/edit".equals(code)){
				break;
			}
			Set<String> menuIds = getMenuIds(code);
			System.out.println(menuIds);
			for (int j = 6; j < row.getLastCellNum(); j++) {
				Set<String> set = roleIds.get(roleNames.get(j));
				if(set == null){
					set = new LinkedHashSet<>();
					roleIds.put(roleNames.get(j),set);
				}

				String stringCellValue = row.getCell(j).getStringCellValue();
				if (stringCellValue == null || stringCellValue.equals("")) {
					continue;
				}
				if("√".equals(stringCellValue)){
					set.addAll(menuIds);
				}else{
					throw new Exception("excel格式错误");
				}
			}

		}

		int i =1;
		int j=1;
		for (Map.Entry<String, Set<String>> entry : roleIds.entrySet()) {
			String key = entry.getKey();
			Set<String> value = entry.getValue();
			long a = 200000000L +i++;

			String sql = "INSERT INTO `UcRole`(`roleId`," +
					" `roleName`, `roleSeq`, `roleMemo`, `roleManagerole`, `roleCreatedate`," +
					" `roleCreaterid`, `roleCreatername`, " +
					"`brandId`, `roleAdminstatus`, `roleManagescope`)" +
					" VALUES ('ROLE202002020"+a+"', '"+key+"', "+i+", '初始化角色', NULL, '"+updateTime+"', 'USER20181023010000005660', '管理员', 'YOULU', 'N', 'C');";

			commonDao.execute(sql,new Object[]{});

			for (String s : value) {
				long b = 200000000L +j++;
				sql = "INSERT INTO `usercenter`.`UcRolemenu`(`rolemenuId`, `roleId`, `menuId`, `roleDatapolicy`)" +
						" VALUES ('ROLEMENU202002020"+b+"', 'ROLE202002020"+a+"', '"+s+"', NULL);";
				commonDao.execute(sql,new Object[]{});
			}


		}

	}


	private Set<String> getMenuIds(String code) throws DBException {
		String sql = "select * from UcMenu where menuCode = ?";
		UcMenu ucMenu = commonDao.queryObject(sql, new Object[]{code}, UcMenu.class);
		if (ucMenu == null) {
//			throw new DBException("code不存在:"+code);
			return new HashSet<>();
		}
		Set<String> set = new HashSet<>();
		set.add(ucMenu.getMenuId());
		String menuLevelcode = ucMenu.getMenuLevelcode();
		while (menuLevelcode.length() > 10) {
			menuLevelcode = menuLevelcode.substring(0, menuLevelcode.length() - 10);
			sql = "select * from UcMenu where menuLevelcode = ?";
			ucMenu = commonDao.queryObject(sql, new Object[]{menuLevelcode}, UcMenu.class);
			set.add(ucMenu.getMenuId());
		}
		return set;

	}
}
