package com.niceloo.uc.role;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.dao.CommonDao;
import com.niceloo.uc.model.UcMenu;
import com.niceloo.uc.model.UcRolemenu;
import org.nobject.common.db.ConnectionPool;
import org.nobject.common.db.DBFactory;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.StringUtils;
import org.nobject.common.log.Logger;
import org.testng.annotations.Test;

import java.util.*;

import static org.nobject.common.lang.ObjectUtils.isEmpty;
public class CreateMenuByCode {

    static Logger logger = Logger.getLogger("aa");

    private DBFactory dbFactory;


    CommonDao commonDao;

    public void initDb() throws Exception {
        ConnectionPool pool = new ConnectionPool();

//		pool.setUsername("root");
//		pool.setPassword("123456");
//		pool.setUrl("*******************************************************************************************************************");

//		pool.setUsername("db_writer");
//		pool.setPassword("6qD^8mbNsd&j");
//		pool.setUrl("******************************************************************************************************************");

        pool.setUsername("root");
        pool.setPassword("123456");
        pool.setUrl("**************************************************************************************************************");


        pool.setDriverClassName("org.gjt.mm.mysql.Driver");
        pool.setMaxSize(2);
        pool.setMaxTimeout(1000L);
        dbFactory = new DBFactory();
        dbFactory.setPool(pool);
        dbFactory.setShowsql(true);

        dbFactory.buildORM(UcMenu.class);
        dbFactory.buildORM(UcRolemenu.class);

        commonDao = new CommonDao();
        commonDao.setDbFactory(dbFactory);

        //String sql = "delete from UcMenu where menuCreatedate ='"+updateTime+"'";
        //commonDao.execute(sql,new Object[]{});

    }

    private String updateTime = "2020-03-03 03:03:03";

    /**
     * 如果page的menucode不为空,增加列表功能
     * @throws Exception
     */
    @Test
    public void test2() throws Exception {
        this.initDb();
        String sql = "select * from UcMenu where menuType = 'page'";
        List<UcMenu> ucMenus = commonDao.queryObjects(sql, new Object[]{}, UcMenu.class);
        List<UcMenu> addList = new ArrayList<>();
        for (UcMenu ucMenu : ucMenus) {
            if (!isEmpty(ucMenu.getMenuCode())) {
                String menuCode = ucMenu.getMenuCode();
                String menuLevelcode = ucMenu.getMenuLevelcode();
                String sql2 = "select * from UcMenu where length(menuLevelcode) = " + (menuLevelcode.length() + 10)
                        + " and left(menuLevelcode, " + menuLevelcode.length() + ") = ? ";
                List<UcMenu> ucMenus1 = commonDao.queryObjects(sql2, new Object[]{menuLevelcode}, UcMenu.class);
                if (ucMenus1.size() == 0 || !check(ucMenus1, menuCode)) {
                    String sql3 = "select max(menuLevelCode) from UcMenu where length(menuLevelCode) = " + (menuLevelcode.length() + 10)
                            + " and left(menuLevelCode," + menuLevelcode.length() + ") = ? ";
                    String s = commonDao.queryString(sql3, new Object[]{menuLevelcode});
                    String addLevelcode;
                    if (StringUtils.isEmpty(s)) {
                        addLevelcode = menuLevelcode + "0000000001";
                    } else {
                        s = s.substring(menuLevelcode.length());
                        int l = Integer.parseInt(s);
                        String s1 = StringUtils.fillEmptyWithStr(l + 1, 10, "0");
                        addLevelcode = menuLevelcode + s1;
                    }
                    UcMenu menu = new UcMenu();
                    menu.setMenuLevelcode(addLevelcode);
                    menu.setMenuCode(menuCode);
                    menu.setMenuName("列表");
                    menu.setMenuCreatedate(DateUtils.getNowDString());
                    menu.setMenuDisablestatus("E");
                    menu.setMenuSeq(0);
                    menu.setMenuType("func");
                    menu.setMenuId(commonDao.genSerial("UcMenu", "menuId", "MENU", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
                    addList.add(menu);
                }
            }
        }
        if (addList.size() > 0) {
            commonDao.save(addList);
        }
    }

    public boolean check(List<UcMenu> ucMenus1, String menuCode) {
        if (ucMenus1.size() > 0) {
            for (UcMenu menu : ucMenus1) {
                if (menu.getMenuCode().equals(menuCode)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 角色拥有func类型的菜单，则拥有同级别的列表
     * @throws Exception
     */
    @Test
    public void testRoleMenu() throws Exception {
        this.initDb();

        List<UcRolemenu> addList = new ArrayList<>();
        String sql = "select t1.roleId,t2.menuLevelcode  from UcRolemenu t1  join UcMenu t2 on  t1.menuId= t2.menuId  where menuType='func' ";
        List list = commonDao.queryMaps(sql, new Object[]{}, null);
        HashMap<String, Set> hMap = new HashMap<>();
        for (Object o : list) {
            Map m = (Map) o;
            String menuLevelcode = (String) m.get("menuLevelcode");
            String substring = menuLevelcode.substring(0, menuLevelcode.length() - 10);
            String roleId = (String) m.get("roleId");
            if(hMap.get(roleId)==null){
                hMap.put(roleId,new HashSet());
            }
            hMap.get(roleId).add(substring);
        }

        for (Map.Entry<String, Set> entry : hMap.entrySet()) {
            String key = entry.getKey();
            Set value = entry.getValue();
            for (Object o : value) {
                String ss=(String)o;
                String sql2="select menuId from UcMenu where menuLevelcode like ? and length(menuLevelcode)= ? and menuName = '列表'";
                String s = commonDao.queryString(sql2, new Object[]{ss + '%', ss.length() + 10});
                if(!isEmpty(s)){
                    UcRolemenu ucRolemenu = new UcRolemenu();
                    ucRolemenu.setMenuId(s);
                    ucRolemenu.setRoleId(key);
                    ucRolemenu.setRolemenuId(commonDao.genSerial("UcRolemenu", "rolemenuId", "ROLEMENU", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
                    addList.add(ucRolemenu);
                }
            }
        }

        if(addList.size()>0){
            commonDao.save(addList);
        }
    }
}
