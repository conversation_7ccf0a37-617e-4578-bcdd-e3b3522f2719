package com.niceloo.uc;

import com.niceloo.uc.model.*;
import com.youlu.pdm.build.CodeBuild;
import com.youlu.pdm.model.Column;
import com.youlu.pdm.model.Table;
import org.dom4j.DocumentException;
import org.junit.Test;
import org.nobject.common.db.DBFactory;
import org.nobject.common.db.model.ORMPOJO;
import org.nobject.common.db.model.ORMPropertyModel;
import org.nobject.common.exception.DBException;
import org.nobject.common.lang.DateUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 生成测试代码
 *
 * <AUTHOR>
 */
public class CodeGenTest {
	@Test
	public void codeGen() throws DocumentException, IOException, DBException {


		List<Table> allTables = new LinkedList<Table>();
//		allTables.add(getTableByClass(UcMenu.class));
		allTables.add(getTableByClass(UcRole.class));
//		allTables.add(getTableByClass(UcRolemenu.class));
//		allTables.add(getTableByClass(UcUserrelation.class));
//		allTables.add(getTableByClass(UcDatapolicy.class));
//		allTables.add(getTableByClass(UcUserrole.class));
		//allTables.add(getTableByClass(CfgInstance.class));
		//allTables.add(getTableByClass(CfgDeploy.class));
		// 包括表 (key为表名 value为编号)
		Map<String, Object> includeTables = new HashMap<>();
//		includeTables.put("Configuration", 13);
//		includeTables.put("CfgInstance", 13);
//		includeTables.put("UcUserrole", 1);
		includeTables.put("UcRole", 1);
//		includeTables.put("UcRolemenu", 1);
//		includeTables.put("UcUserrelation", 1);
//		includeTables.put("UcDatapolicy", 1);
//		includeTables.put("CfgDeploy", 2);
		/**
		 * 排除列
		 */
		Map<String, Object> handlerExcludeCols = new HashMap<>();

		CodeBuild build = new CodeBuild().setIncludeTables(includeTables).setHandlerExcludeCols(handlerExcludeCols)
				.setModel("Uc").setAuthor("hepangui").setDateStr(DateUtils.getNowDString())
				.setBasePath("E:\\code\\").setBasePackage("com.niceloo.uc.auth").setNeedLogin(false);
		// 生成Java文件
		build.buildTablesCode(allTables);
		System.out.println("done");
	}

	private Table getTableByClass(Class clazz) throws DBException {

		StringBuilder sb = new StringBuilder("");

		Table table = new Table();
		DBFactory factory = new DBFactory();
		ORMPOJO ormpojo = factory.buildORM(clazz);
		table.setTableName(ormpojo.tableComment);
		table.setTableCode(ormpojo.tableName);

		sb.append("CREATE TABLE `"+ormpojo.tableName+"` (\n");
		ORMPropertyModel[] propertyModels = ormpojo.propertyModels;
		List<Column> columns = new LinkedList<>();
		for (ORMPropertyModel p : propertyModels) {
			Column column = new Column();
			column.setCode(p.column);
			column.setName(p.comment);
			column.setComment(p.comment);
			if(p.length>0){
				column.setLength(p.length);
			}
			column.setPkFlag(p.id);
			String simpleName = p.type.getSimpleName();
			switch (simpleName) {
				case "Integer":
					column.setType("int");
					sb.append("  `"+column.getCode()+"` integer ,\n");
					break;
				case "String":
					column.setType("String");
					sb.append("  `"+column.getCode()+"` varchar("+(column.getLength() == null?255:column.getLength())+")  ,\n");
					break;
			}
			if (p.id) {
				table.setPkColumn(column);
				table.setPkCode(p.column);
				table.setPkName(p.name);
			}
			columns.add(column);
		}

		sb.append("  PRIMARY KEY (`"+table.getPkCode()+"`)\n");
		sb.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;\n");
		System.out.println(sb.toString());
		table.setAllColumns(columns);
		return table;
	}
}
