package com.niceloo.uc.auth;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * 员工POJO类
 * <AUTHOR>
 * @Date 2019年11月2日 15:06:22
 */
@POJO
@ClassDesc(comment = "员工")
public class BdEe {

	@POJO(id = true , generator = ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length = 36 ,comment = "员工标识")
	private String eeId;
	@POJO
	@FieldDesc(length = 36 ,comment = "用户标识")
	private String userId;
	@POJO
	@FieldDesc(length = 50 ,comment = "用户名")
	private String userName;
	@POJO
	@FieldDesc(length = 36 ,comment = "分校标识")
	private String schoolId;
	@POJO
	@FieldDesc(length = 50 ,comment = "员工编号")
	private String eeNo;
	@POJO
	@FieldDesc(length = 1 ,comment = "员工权限")
	private String eePermission;
	@POJO
	@FieldDesc(length = 6 ,comment = "员工权重等级")
	private String eeWeightgrade;
	@POJO
	@FieldDesc(length = 1 ,comment = "员工在职状态(O:在职;L:离职;W:预入职;P:试用)")
	private String eeWorkstatus;
	@POJO
	@FieldDesc(length = 50 ,comment = "员工TQ账号")
	private String eeTqaccount;
	@POJO
	@FieldDesc(length = 50 ,comment = "员工TQ密码")
	private String eeTqpassword;
	@POJO
	@FieldDesc(length = 50 ,comment = "员工风云账号")
	private String eeFyaccount;
	@POJO
	@FieldDesc(length = 50 ,comment = "员工风云密码")
	private String eeFypassword;
	@POJO
	@FieldDesc(length = 1 ,comment = "员工外呼方式(T:TQ;F:风云)")
	private String eeCallouttype;
	@POJO
	@FieldDesc(length = 1 ,comment = "员工业绩核算状态(Y:核算;N:不核算)")
	private String eeAchvstatus;
	@POJO
	@FieldDesc(length = 1 ,comment = "员工职位(P:校长;M:经理;S:主管;E:员工)")
	private String eePostype;
	@POJO
	@FieldDesc(length = 1 ,comment = "员工教学身份(C:班主任;T:教研老师)")
	private String eeEdutype;
	@POJO
	@FieldDesc(length = 20 ,comment = "员工基本工资")
	private Long eeSalarybaseamount;
	@POJO
	@FieldDesc(length = 20 ,comment = "员工最低工资")
	private Long eeSalaryminamount;
	@POJO
	@FieldDesc(length = 20 ,comment = "员工浮动工资")
	private Long eeSalaryfloatamount;
	@POJO
	@FieldDesc(length = 11 ,comment = "员工订单达标数")
	private Integer eeOrdersuccessnum;
	@POJO
	@FieldDesc(length = 11 ,comment = "员工订单未达标数")
	private Integer eeOrderfailnum;
	@POJO
	@FieldDesc(length = 11 ,comment = "员工联系电话")
	private String eePhone;
	@POJO
	@FieldDesc(length = 255 ,comment = "员工企业邮箱")
	private String eeCompanyemail;
	@POJO
	@FieldDesc(length = 255 ,comment = "员工企业微信")
	private String eeCompanywechat;
	@POJO
	@FieldDesc(length = 255 ,comment = "员工现住地址")
	private String eeAdress;
	@POJO
	@FieldDesc(length = 19 ,comment = "员工入职日期")
	private String eeHiredate;
	@POJO
	@FieldDesc(length = 19 ,comment = "员工离职日期")
	private String eeTermdate;
	@POJO
	@FieldDesc(length = 2000 ,comment = "员工备注")
	private String eeMemo;
	@POJO
	@FieldDesc(length = 1 ,comment = "员工可用状态(Y:可用;N:不可用)")
	private String eeAvlstatus;
	@POJO
	@FieldDesc(length = 1 ,comment = "员工删除状态(Y:删除;N:未删除)")
	private String eeDelstatus;
	@POJO
	@FieldDesc(length = 36 ,comment = "员工创建人")
	private String eeCreater;
	@POJO
	@FieldDesc(length = 19 ,comment = "员工创建时间")
	private String eeCreateddate;
	@POJO
	@FieldDesc(length = 36 ,comment = "员工修改人")
	private String eeModifier;
	@POJO
	@FieldDesc(length = 19 ,comment = "员工修改时间")
	private String eeModifieddate;
	@POJO
	@FieldDesc(length = 36 ,comment = "员工来源标识")
	private String eeSourceid;
	@POJO
	@FieldDesc(length = 1 ,comment = "员工类别(E:员工;A:代理;P:公共库)")
	private String eeCategory;
	@POJO
	@FieldDesc(length = 500 ,comment = "角色集合，冗余")
	private String eeRole;

	/**
	 * 获取员工标识
	 * 
	 * @return 员工标识
	 */
	public String getEeId() {
		return this.eeId;
	}
	/**
	 * 设置员工标识
	 * 
	 * @param  eeId 员工标识
	 */
	public void setEeId(String eeId) {
		this.eeId = eeId;
	}
	/**
	 * 获取用户标识
	 * 
	 * @return 用户标识
	 */
	public String getUserId() {
		return this.userId;
	}
	/**
	 * 设置用户标识
	 * 
	 * @param  userId 用户标识
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}
	/**
	 * 获取用户名
	 * 
	 * @return 用户名
	 */
	public String getUserName() {
		return this.userName;
	}
	/**
	 * 设置用户名
	 * 
	 * @param  userName 用户名
	 */
	public void setUserName(String userName) {
		this.userName = userName;
	}
	/**
	 * 获取分校标识
	 * 
	 * @return 分校标识
	 */
	public String getSchoolId() {
		return this.schoolId;
	}
	/**
	 * 设置分校标识
	 * 
	 * @param  schoolId 分校标识
	 */
	public void setSchoolId(String schoolId) {
		this.schoolId = schoolId;
	}
	/**
	 * 获取员工编号
	 * 
	 * @return 员工编号
	 */
	public String getEeNo() {
		return this.eeNo;
	}
	/**
	 * 设置员工编号
	 * 
	 * @param  eeNo 员工编号
	 */
	public void setEeNo(String eeNo) {
		this.eeNo = eeNo;
	}
	/**
	 * 获取员工权限
	 * 
	 * @return 员工权限
	 */
	public String getEePermission() {
		return this.eePermission;
	}
	/**
	 * 设置员工权限
	 * 
	 * @param  eePermission 员工权限
	 */
	public void setEePermission(String eePermission) {
		this.eePermission = eePermission;
	}
	/**
	 * 获取员工权重等级
	 * 
	 * @return 员工权重等级
	 */
	public String getEeWeightgrade() {
		return this.eeWeightgrade;
	}
	/**
	 * 设置员工权重等级
	 * 
	 * @param  eeWeightgrade 员工权重等级
	 */
	public void setEeWeightgrade(String eeWeightgrade) {
		this.eeWeightgrade = eeWeightgrade;
	}
	/**
	 * 获取员工在职状态(O:在职;L:离职;W:预入职;P:试用)
	 * 
	 * @return 员工在职状态(O:在职;L:离职;W:预入职;P:试用)
	 */
	public String getEeWorkstatus() {
		return this.eeWorkstatus;
	}
	/**
	 * 设置员工在职状态(O:在职;L:离职;W:预入职;P:试用)
	 * 
	 * @param  eeWorkstatus 员工在职状态(O:在职;L:离职;W:预入职;P:试用)
	 */
	public void setEeWorkstatus(String eeWorkstatus) {
		this.eeWorkstatus = eeWorkstatus;
	}
	/**
	 * 获取员工TQ账号
	 * 
	 * @return 员工TQ账号
	 */
	public String getEeTqaccount() {
		return this.eeTqaccount;
	}
	/**
	 * 设置员工TQ账号
	 * 
	 * @param  eeTqaccount 员工TQ账号
	 */
	public void setEeTqaccount(String eeTqaccount) {
		this.eeTqaccount = eeTqaccount;
	}
	/**
	 * 获取员工TQ密码
	 * 
	 * @return 员工TQ密码
	 */
	public String getEeTqpassword() {
		return this.eeTqpassword;
	}
	/**
	 * 设置员工TQ密码
	 * 
	 * @param  eeTqpassword 员工TQ密码
	 */
	public void setEeTqpassword(String eeTqpassword) {
		this.eeTqpassword = eeTqpassword;
	}
	/**
	 * 获取员工风云账号
	 * 
	 * @return 员工风云账号
	 */
	public String getEeFyaccount() {
		return this.eeFyaccount;
	}
	/**
	 * 设置员工风云账号
	 * 
	 * @param  eeFyaccount 员工风云账号
	 */
	public void setEeFyaccount(String eeFyaccount) {
		this.eeFyaccount = eeFyaccount;
	}
	/**
	 * 获取员工风云密码
	 * 
	 * @return 员工风云密码
	 */
	public String getEeFypassword() {
		return this.eeFypassword;
	}
	/**
	 * 设置员工风云密码
	 * 
	 * @param  eeFypassword 员工风云密码
	 */
	public void setEeFypassword(String eeFypassword) {
		this.eeFypassword = eeFypassword;
	}
	/**
	 * 获取员工外呼方式(T:TQ;F:风云)
	 * 
	 * @return 员工外呼方式(T:TQ;F:风云)
	 */
	public String getEeCallouttype() {
		return this.eeCallouttype;
	}
	/**
	 * 设置员工外呼方式(T:TQ;F:风云)
	 * 
	 * @param  eeCallouttype 员工外呼方式(T:TQ;F:风云)
	 */
	public void setEeCallouttype(String eeCallouttype) {
		this.eeCallouttype = eeCallouttype;
	}
	/**
	 * 获取员工业绩核算状态(Y:核算;N:不核算)
	 * 
	 * @return 员工业绩核算状态(Y:核算;N:不核算)
	 */
	public String getEeAchvstatus() {
		return this.eeAchvstatus;
	}
	/**
	 * 设置员工业绩核算状态(Y:核算;N:不核算)
	 * 
	 * @param  eeAchvstatus 员工业绩核算状态(Y:核算;N:不核算)
	 */
	public void setEeAchvstatus(String eeAchvstatus) {
		this.eeAchvstatus = eeAchvstatus;
	}
	/**
	 * 获取员工职位(P:校长;M:经理;S:主管;E:员工)
	 * 
	 * @return 员工职位(P:校长;M:经理;S:主管;E:员工)
	 */
	public String getEePostype() {
		return this.eePostype;
	}
	/**
	 * 设置员工职位(P:校长;M:经理;S:主管;E:员工)
	 * 
	 * @param  eePostype 员工职位(P:校长;M:经理;S:主管;E:员工)
	 */
	public void setEePostype(String eePostype) {
		this.eePostype = eePostype;
	}
	/**
	 * 获取员工教学身份(C:班主任;T:教研老师)
	 * 
	 * @return 员工教学身份(C:班主任;T:教研老师)
	 */
	public String getEeEdutype() {
		return this.eeEdutype;
	}
	/**
	 * 设置员工教学身份(C:班主任;T:教研老师)
	 * 
	 * @param  eeEdutype 员工教学身份(C:班主任;T:教研老师)
	 */
	public void setEeEdutype(String eeEdutype) {
		this.eeEdutype = eeEdutype;
	}
	/**
	 * 获取员工基本工资
	 * 
	 * @return 员工基本工资
	 */
	public Long getEeSalarybaseamount() {
		return this.eeSalarybaseamount;
	}
	/**
	 * 设置员工基本工资
	 * 
	 * @param  eeSalarybaseamount 员工基本工资
	 */
	public void setEeSalarybaseamount(Long eeSalarybaseamount) {
		this.eeSalarybaseamount = eeSalarybaseamount;
	}
	/**
	 * 获取员工最低工资
	 * 
	 * @return 员工最低工资
	 */
	public Long getEeSalaryminamount() {
		return this.eeSalaryminamount;
	}
	/**
	 * 设置员工最低工资
	 * 
	 * @param  eeSalaryminamount 员工最低工资
	 */
	public void setEeSalaryminamount(Long eeSalaryminamount) {
		this.eeSalaryminamount = eeSalaryminamount;
	}
	/**
	 * 获取员工浮动工资
	 * 
	 * @return 员工浮动工资
	 */
	public Long getEeSalaryfloatamount() {
		return this.eeSalaryfloatamount;
	}
	/**
	 * 设置员工浮动工资
	 * 
	 * @param  eeSalaryfloatamount 员工浮动工资
	 */
	public void setEeSalaryfloatamount(Long eeSalaryfloatamount) {
		this.eeSalaryfloatamount = eeSalaryfloatamount;
	}
	/**
	 * 获取员工订单达标数
	 * 
	 * @return 员工订单达标数
	 */
	public Integer getEeOrdersuccessnum() {
		return this.eeOrdersuccessnum;
	}
	/**
	 * 设置员工订单达标数
	 * 
	 * @param  eeOrdersuccessnum 员工订单达标数
	 */
	public void setEeOrdersuccessnum(Integer eeOrdersuccessnum) {
		this.eeOrdersuccessnum = eeOrdersuccessnum;
	}
	/**
	 * 获取员工订单未达标数
	 * 
	 * @return 员工订单未达标数
	 */
	public Integer getEeOrderfailnum() {
		return this.eeOrderfailnum;
	}
	/**
	 * 设置员工订单未达标数
	 * 
	 * @param  eeOrderfailnum 员工订单未达标数
	 */
	public void setEeOrderfailnum(Integer eeOrderfailnum) {
		this.eeOrderfailnum = eeOrderfailnum;
	}
	/**
	 * 获取员工联系电话
	 * 
	 * @return 员工联系电话
	 */
	public String getEePhone() {
		return this.eePhone;
	}
	/**
	 * 设置员工联系电话
	 * 
	 * @param  eePhone 员工联系电话
	 */
	public void setEePhone(String eePhone) {
		this.eePhone = eePhone;
	}
	/**
	 * 获取员工企业邮箱
	 * 
	 * @return 员工企业邮箱
	 */
	public String getEeCompanyemail() {
		return this.eeCompanyemail;
	}
	/**
	 * 设置员工企业邮箱
	 * 
	 * @param  eeCompanyemail 员工企业邮箱
	 */
	public void setEeCompanyemail(String eeCompanyemail) {
		this.eeCompanyemail = eeCompanyemail;
	}
	/**
	 * 获取员工企业微信
	 * 
	 * @return 员工企业微信
	 */
	public String getEeCompanywechat() {
		return this.eeCompanywechat;
	}
	/**
	 * 设置员工企业微信
	 * 
	 * @param  eeCompanywechat 员工企业微信
	 */
	public void setEeCompanywechat(String eeCompanywechat) {
		this.eeCompanywechat = eeCompanywechat;
	}
	/**
	 * 获取员工现住地址
	 * 
	 * @return 员工现住地址
	 */
	public String getEeAdress() {
		return this.eeAdress;
	}
	/**
	 * 设置员工现住地址
	 * 
	 * @param  eeAdress 员工现住地址
	 */
	public void setEeAdress(String eeAdress) {
		this.eeAdress = eeAdress;
	}
	/**
	 * 获取员工入职日期
	 * 
	 * @return 员工入职日期
	 */
	public String getEeHiredate() {
		return this.eeHiredate;
	}
	/**
	 * 设置员工入职日期
	 * 
	 * @param  eeHiredate 员工入职日期
	 */
	public void setEeHiredate(String eeHiredate) {
		this.eeHiredate = eeHiredate;
	}
	/**
	 * 获取员工离职日期
	 * 
	 * @return 员工离职日期
	 */
	public String getEeTermdate() {
		return this.eeTermdate;
	}
	/**
	 * 设置员工离职日期
	 * 
	 * @param  eeTermdate 员工离职日期
	 */
	public void setEeTermdate(String eeTermdate) {
		this.eeTermdate = eeTermdate;
	}
	/**
	 * 获取员工备注
	 * 
	 * @return 员工备注
	 */
	public String getEeMemo() {
		return this.eeMemo;
	}
	/**
	 * 设置员工备注
	 * 
	 * @param  eeMemo 员工备注
	 */
	public void setEeMemo(String eeMemo) {
		this.eeMemo = eeMemo;
	}
	/**
	 * 获取员工可用状态(Y:可用;N:不可用)
	 * 
	 * @return 员工可用状态(Y:可用;N:不可用)
	 */
	public String getEeAvlstatus() {
		return this.eeAvlstatus;
	}
	/**
	 * 设置员工可用状态(Y:可用;N:不可用)
	 * 
	 * @param  eeAvlstatus 员工可用状态(Y:可用;N:不可用)
	 */
	public void setEeAvlstatus(String eeAvlstatus) {
		this.eeAvlstatus = eeAvlstatus;
	}
	/**
	 * 获取员工删除状态(Y:删除;N:未删除)
	 * 
	 * @return 员工删除状态(Y:删除;N:未删除)
	 */
	public String getEeDelstatus() {
		return this.eeDelstatus;
	}
	/**
	 * 设置员工删除状态(Y:删除;N:未删除)
	 * 
	 * @param  eeDelstatus 员工删除状态(Y:删除;N:未删除)
	 */
	public void setEeDelstatus(String eeDelstatus) {
		this.eeDelstatus = eeDelstatus;
	}
	/**
	 * 获取员工创建人
	 * 
	 * @return 员工创建人
	 */
	public String getEeCreater() {
		return this.eeCreater;
	}
	/**
	 * 设置员工创建人
	 * 
	 * @param  eeCreater 员工创建人
	 */
	public void setEeCreater(String eeCreater) {
		this.eeCreater = eeCreater;
	}
	/**
	 * 获取员工创建时间
	 * 
	 * @return 员工创建时间
	 */
	public String getEeCreateddate() {
		return this.eeCreateddate;
	}
	/**
	 * 设置员工创建时间
	 * 
	 * @param  eeCreateddate 员工创建时间
	 */
	public void setEeCreateddate(String eeCreateddate) {
		this.eeCreateddate = eeCreateddate;
	}
	/**
	 * 获取员工修改人
	 * 
	 * @return 员工修改人
	 */
	public String getEeModifier() {
		return this.eeModifier;
	}
	/**
	 * 设置员工修改人
	 * 
	 * @param  eeModifier 员工修改人
	 */
	public void setEeModifier(String eeModifier) {
		this.eeModifier = eeModifier;
	}
	/**
	 * 获取员工修改时间
	 * 
	 * @return 员工修改时间
	 */
	public String getEeModifieddate() {
		return this.eeModifieddate;
	}
	/**
	 * 设置员工修改时间
	 * 
	 * @param  eeModifieddate 员工修改时间
	 */
	public void setEeModifieddate(String eeModifieddate) {
		this.eeModifieddate = eeModifieddate;
	}
	/**
	 * 获取员工来源标识
	 * 
	 * @return 员工来源标识
	 */
	public String getEeSourceid() {
		return this.eeSourceid;
	}
	/**
	 * 设置员工来源标识
	 * 
	 * @param  eeSourceid 员工来源标识
	 */
	public void setEeSourceid(String eeSourceid) {
		this.eeSourceid = eeSourceid;
	}

	public String getEeCategory() {
		return eeCategory;
	}

	public void setEeCategory(String eeCategory) {
		this.eeCategory = eeCategory;
	}

	public String getEeRole() {
		return eeRole;
	}

	public void setEeRole(String eeRole) {
		this.eeRole = eeRole;
	}
}