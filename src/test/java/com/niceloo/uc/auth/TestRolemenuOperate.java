package com.niceloo.uc.auth;

import org.nobject.common.lang.MapUtils;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.*;

/**
 * 给角色分配权限
 * 1. 直接给角色分配权限
 * 2. 查出原有的角色权限，分配新的权限
 * 3. 给用户分配角色
 * 4. 查询用户的角色
 * 5. 给用户重新分配角色
 * 6. 查询用户拥有的权限信息
 * @Auther hepangui
 * @Date 2019/11/19 0019
 */
public class TestRolemenuOperate extends BaseAuthTest{
	private  TestMenuOperate testMenuOperate = new TestMenuOperate();
	private  TestRoleOperate testRoleOperate = new TestRoleOperate();
	private  List roleList=null;
	List menuList =null;
	@BeforeClass
	public void init() throws Exception {
		this.startServer();
		this.clearData();
		testMenuOperate.createMenus();//权限
		testRoleOperate.createRoles();//角色
	}

	/**
	 * 测试给角色分配权限
	 * roleId  ,menus   [{menuId:xxx,roleDatapolicy:["policyCode1","policyCode2"]},{……}]
	 */
	@Test(priority = 0)
	public void testSetRolemenu() throws Exception {

		Map roleData = request("ucauth/role/list", MapUtils.toMap(new Object[][]{}));
		Map menuData = request("ucauth/menu/tree", MapUtils.toMap(new Object[][]{
				{"containsFunc", "Y"},
				{"containsDisabled", "Y"}
		}));
		roleList = (List) roleData.get("data");
		menuList = (List) menuData.get("data");
		Map datapolicy = request("ucauth/datapolicy/list", MapUtils.toMap(new Object[][]{}));

		List data = (List) datapolicy.get("data");
		ArrayList<Object> datapolicyList = new ArrayList<>();
		for (Object datum : data) {
			Map datum1 = (Map) datum;
			String datapolicyCode = (String) datum1.get("datapolicyCode");
			datapolicyList.add(datapolicyCode);
		}

		for (Object o : roleList) {
			List<Map> menus=new ArrayList<>();
			Map roleMap= (Map) o;
			if(roleMap.get("roleName").equals("分校校长")){
				String roleId = (String) roleMap.get("roleId");
				Map cusMap = (Map) menuList.get(1);
				List children = (List) cusMap.remove("children");
				HashMap<Object, Object> map = new HashMap<>();
				map.put("menuId",cusMap.get("menuId"));
				menus.add(map);
				for (Object child : children) {
					Map child1 = (Map) child;
					String menuId = (String) child1.get("menuId");
					HashMap<Object, Object> map2 = new HashMap<>();
					map2.put("menuId",menuId);
					map2.put("datapolicyCode",datapolicyList);
					menus.add(map2);
				}
				request("ucauth/role/setRolemenu", MapUtils.toMap(new Object[][]{
						{"roleId",roleId},
						{"menus",menus}
				}));

			}
		}


		for (Object datum : roleList) {
			Map roleMap=(Map)datum;
			if(roleMap.get("roleName").equals("分校校长")){
				String roleId = (String) roleMap.get("roleId");
				Map request1 = request("ucauth/role/getMenusByRoleId", MapUtils.toMap(new Object[][]{
						{"roleId",roleId}
				}));
				List data1 = (List) request1.get("data");
				String menuNames="";
				StringBuilder sb=new StringBuilder(menuNames);
				for (Object o : data1) {
					Map map=(Map)o;
					Map rMap = request("ucauth/menu/info", MapUtils.toMap(new Object[][]{
							{"menuId",map.get("menuId")}
					}));
					String menuName = (String) rMap.get("menuName");
					sb.append(menuName).append(" ");
				}
				System.out.println(sb.toString());
			}
		}
	}

    /**
     * 给用户分配角色
     */
	@Test(priority = 1)
	public void testSetRoleUser() throws Exception {
		for (int i = 0; i <50 ; i+=10) {
			Map request = request("uc/user/list_mc", MapUtils.toMap(new Object[][]{
					{"pageIndex",i},
					{"pageSize",i+10}
			}));
			List data = (List) request.get("data");
			int k=0;

			for (Object datum : data) {
				if(k==4){
					k=0;
				}
				Map userMap=(Map) datum;
				String userId = (String) userMap.get("userId");
				Map roleMap = (Map) roleList.get(k);
				List list=new ArrayList();
				list.add(roleMap.get("roleId"));
				request("ucauth/auth/setUserRole", MapUtils.toMap(new Object[][]{
						{"userId",userId},
						{"roleIds",list}
				}));
				k++;
			}
		}
	}


    /**
     * 给用户重新分配角色
     */
//	@Test(priority = 2)
//	public void testResetRoleUser() throws Exception {
//		Map request = request("uc/user/list_mc", MapUtils.toMap(new Object[][]{}));
//		List data = (List) request.get("data");
//		List list=new ArrayList();
//		Map o = (Map) roleList.get(0);
//		String roleId = (String) o.get("roleId");
//		for (Object datum : data) {
//			Map map=(Map) datum;
//			String userId = (String) map.get("userId");
//			list.add(userId);
//		}
//		request("ucauth/auth/addUserRole", MapUtils.toMap(new Object[][]{
//				{"userIds",list},
//				{"roleId",roleId}
//		}));
//	}


    /**
     * 用户权限信息
     */
	@Test(priority = 3)
	public void testQueryUserMenuTree() throws Exception {
		Map request = request("ucauth/auth/getMenuTreeByUser", MapUtils.toMap(new Object[][]{
				{"userId", "USER20160405010000000010"}
		}));
	}

}
