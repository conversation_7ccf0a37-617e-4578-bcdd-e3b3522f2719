package com.niceloo.uc.auth;

import org.checkerframework.checker.units.qual.A;
import org.nobject.common.lang.MapUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Map;

/**
 * 测试角色操作
 * 1. 保存一批角色
 * 2. 查出这一批角色，看是否正确
 *
 * @Auther hepangui
 * @Date 2019/11/19 0019
 */
public class TestRoleOperate extends BaseAuthTest {

	@BeforeClass
	public void init() throws Exception {
		this.startServer();
		this.clearData();
	}

	/**
	 * 创建菜单
	 */
	@Test
	public void testCreate() throws Exception {
		createRoles();
		Map request = request("ucauth/role/list", MapUtils.toMap(new Object[][]{
//				{"containsFunc", "Y"},
//				{"containsDisabled", "Y"}
		}));
		List<Map> list = (List) request.get("data");
		System.out.println(list);
		Assert.assertEquals(list.size(),5);
		Assert.assertEquals(list.get(0).get("roleName"),"分校财务");

		Map map = list.get(1);
		Assert.assertEquals(map.get("roleName"),"分校校长");
		List<String> roleManagerole = (List)map.get("roleManagerole");
		for (String s : roleManagerole) {
			boolean flag = false;
			for (Map map1 : list) {
				if(map1.get("roleId").equals(s)){
					flag = true;
					Object roleName = map1.get("roleName");
					Assert.assertTrue(roleName.equals("分校财务") || roleName.equals("分校主管"));
				}
			}
			Assert.assertEquals(flag,true);
		}

	}


	public void createRoles() throws Exception {
		Map[] menus = new Map[]{
				MapUtils.toMap(new Object[][]{
						{"roleName", "分校校长"},
						{"roleSeq", "10"},
						{"roleMemo", "备注"},
				}),
				MapUtils.toMap(new Object[][]{
						{"roleName", "分校财务"},
						{"roleSeq", "4"},
						{"roleMemo", "备注"},
				}),
				MapUtils.toMap(new 	Object[][]{
						{"roleName", "分校主管"},
						{"roleSeq", "15"},
						{"roleMemo", "备注"},
				}),
				MapUtils.toMap(new 	Object[][]{
						{"roleName", "销售主管"},
						{"roleSeq", "20"},
						{"roleMemo", "备注"},
				}),
				MapUtils.toMap(new 	Object[][]{
						{"roleName", "运营主管"},
						{"roleSeq", "30"},
						{"roleMemo", "备注"},
				}),
		};
		String caiwuid = null;
		String xiaozhangId = null;
		String zhugaunid = null;
		for (Map map : menus) {

			if(map.get("roleName").equals("分校主管")){
				map.put("roleManagerole",new String []{caiwuid});
			}
			Map request = request("ucauth/role/add", map);
			Object menuId = request.get("menuId");
			if(map.get("roleName").equals("分校财务")){
				caiwuid = (String)request.get("roleId");
			}
			if(map.get("roleName").equals("分校校长")){
				xiaozhangId = (String)request.get("roleId");
			}
			if(map.get("roleName").equals("分校主管")){
				zhugaunid = (String)request.get("roleId");
			}
		}
		Map request = request("ucauth/role/info", MapUtils.toMap(new Object[][]{{"roleId", xiaozhangId}}));
		List roleManagerole = (List)request.get("roleManagerole");
		roleManagerole.add(caiwuid);
		roleManagerole.add(zhugaunid);
		Map request1 = request("ucauth/role/edit", request);

	}

//	@AfterClass
//	public void destory() throws Exception {
//		this.closeServer();
//		this.clearData();
//	}

}
