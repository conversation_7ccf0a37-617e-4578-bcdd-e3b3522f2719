package com.niceloo.uc.auth;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.dao.CommonDao;
import com.niceloo.uc.model.UcUserbrand;
import com.niceloo.uc.model.UcUserrole;
import org.nobject.common.db.ConnectionPool;
import org.nobject.common.db.DBFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 1. 给用户分配数据范围
 * 2. 查询用户的数据范围，
 * 3. 修改用户的数据范围后重新保存
 * 4.
 * @Auther hepangui
 * @Date 2019/11/19 0019
 */
public class TestUserOperate {

	public static void main(String[] args) throws Exception {
		ConnectionPool pool = new ConnectionPool();
		pool.setUsername("root");
		pool.setPassword("123456");
		pool.setUrl("****************************************************************************************************************");
		pool.setDriverClassName("org.gjt.mm.mysql.Driver");
		pool.setMaxSize(2);
		pool.setMaxTimeout(1000L);
		DBFactory dbFactory = new DBFactory();
		dbFactory.setPool(pool);
		dbFactory.setShowsql(true);
		dbFactory.buildORM(UcUserrole.class);
		dbFactory.buildORM(UcUserbrand.class);

		CommonDao dao = new CommonDao();
		dao.setDbFactory(dbFactory);

		String sql= "select userId from UcUser where userFlag = 'I' ";

		String[] strings = dao.queryStrings(sql, new Object[0]);
		List brands = new ArrayList();
		List roles = new ArrayList();
		for (String string : strings) {
			UcUserbrand ucUserbrand = new UcUserbrand();
			ucUserbrand.setUserbrId(dao.genSerial("UcUserbrand", "userbrId", "USERBR", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
			ucUserbrand.setBrandId("YOULU");
			ucUserbrand.setUserId(string);
			brands.add(ucUserbrand);
		}
		dao.save(brands);


	}
}
