package com.niceloo.uc.auth;

import org.nobject.common.js.JSONArray;
import org.nobject.common.js.JSONObject;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.StringUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试菜单操作
 * 1. 保存一批菜单
 * 2. 查询出这一批菜单是否正确
 * 3. 根据父节点查询下一级节点列表，看是否正确
 * @Auther hepangui
 * @Date 2019/11/19 0019
 */
public class TestMenuOperate extends BaseAuthTest {

	@BeforeClass
	public void init() throws Exception {
		this.startServer();
		this.clearData();
	}

	/**
	 * 创建菜单
	 */
	@Test
	public void testCreate() throws Exception {
		createMenus();
		Map request = request("ucauth/menu/tree", MapUtils.toMap(new Object[][]{
				{"containsFunc", "Y"},
				{"containsDisabled", "Y"}
		}));
		List<Map> list = (List) request.get("data");
		// 只有两个顶级菜单
		Assert.assertEquals(list.size(), 2);
		// 按照排序，客户管理中心排在第二个
		Map map = list.get(1);
		Assert.assertEquals(map.get("menuName"), "客户管理中心");
		Assert.assertEquals(map.get("menuCode"), "cust");
		Assert.assertEquals(map.get("menuSeq"), 10);
		Assert.assertEquals(map.get("menuType"), "app");
		Assert.assertEquals(map.get("menuIcon"), "this is a icon");
		Assert.assertEquals(map.get("menuUrl"), null);
		//检查一级child
		List<Map> children = (List) map.get("children");
		Assert.assertEquals(children.size(), 2);
		for (Map child : children) {
			Assert.assertEquals(child.get("menuType"), "page");
		}

		// 按照排序。第一个为订单gaunt里
		map = list.get(0);
		Assert.assertEquals(map.get("menuName"), "订单管理中心");
		children = (List) map.get("children");
		Assert.assertEquals(children.size(), 3);
		boolean flag = false;
		for (Map child : children) {
			Object menuName = child.get("menuName");
			if (menuName.equals("订单管理")) {
				flag = true;
				List<Map> funcs = (List) child.get("children");
				Assert.assertEquals(funcs.size(), 5);
				for (Map func : funcs) {
					Object menuType = func.get("menuType");
					Assert.assertEquals(menuType, "func");
					Assert.assertNotNull(func.get("menuUrl"));
					Assert.assertNotNull(func.get("menuCode"));
					Object menuDatapolicy = func.get("menuDatapolicy");
					Assert.assertTrue(List.class.isAssignableFrom(menuDatapolicy.getClass()));

				}
			}
		}
		Assert.assertEquals(flag, true);
	}


	public void createMenus() throws Exception {
		Map[] menus = new Map[]{
				MapUtils.toMap(new Object[][]{
						{"menuName", "客户管理中心"},
						{"menuCode", "cust"},
						{"menuSeq", "10"},
						{"menuType", "app"},
						{"menuIcon", "this is a icon"},
						{"children", new Map[]{
								MapUtils.toMap(new Object[][]{
										{"menuName", "我的客户"},
										{"menuCode", "mycus"},
										{"menuType", "page"},
										{"menuUrl", "/uc/mycus.html"},
								}),
								MapUtils.toMap(new Object[][]{
										{"menuName", "客户统计"},
										{"menuCode", "custong"},
										{"menuType", "page"},
										{"menuUrl", "/uc/mycus/ton.html"},
								})
						}}
				}),
				MapUtils.toMap(new Object[][]{
						{"menuName", "订单管理中心"},
						{"menuCode", "orderCenter"},
						{"menuSeq", "5"},
						{"menuType", "app"},
						{"menuIcon", "this is a icon"},
						{"children", new Map[]{
								MapUtils.toMap(new Object[][]{
										{"menuName", "工作任务"},
										{"menuCode", "jobs"},
										{"menuType", "page"},
										{"menuUrl", "/uc/jobs.html"},
								}),
								MapUtils.toMap(new Object[][]{
										{"menuName", "订单管理"},
										{"menuCode", "ordermanage"},
										{"menuType", "page"},
										{"menuUrl", "/uc/mycus/ordr.html"},
										{"children", new Map[]{
												MapUtils.toMap(new Object[][]{
														{"menuName", "添加订单"},
														{"menuSeq", "10"},
														{"menuCode", "uc/order/add"},
														{"menuType", "func"},
														{"menuUrl", "/uc/order/add"},
														{"menuDatapolicy", new String[]{"me", "dept", "school", "area"}}
												}),
												MapUtils.toMap(new Object[][]{
														{"menuName", "查看详情"},
														{"menuSeq", "5"},
														{"menuCode", "uc/order/detail"},
														{"menuType", "func"},
														{"menuUrl", "/uc/order/detail"},
														{"menuDatapolicy", new String[]{"me", "dept", "school"}}

												}),
												MapUtils.toMap(new Object[][]{
														{"menuName", "编辑"},
														{"menuSeq", "15"},
														{"menuCode", "uc/order/edit"},
														{"menuType", "func"},
														{"menuUrl", "/uc/order/edit"},
														{"menuDatapolicy", new String[]{"me", "dept"}}

												}),
												MapUtils.toMap(new Object[][]{
														{"menuName", "收款码"},
														{"menuSeq", "20"},
														{"menuCode", "uc/order/ma"},
														{"menuType", "func"},
														{"menuUrl", "/uc/order/ma"},
														{"menuDatapolicy", new String[]{"me", "dept", "school", "area"}}

												}),
												MapUtils.toMap(new Object[][]{
														{"menuName", "操作记录"},
														{"menuSeq", "25"},
														{"menuCode", "uc/order/record"},
														{"menuType", "func"},
														{"menuUrl", "/uc/order/record"},
														{"menuDatapolicy", new String[]{"me", "dept", "school", "area"}}

												}),
										}}
								}),
								MapUtils.toMap(new Object[][]{
										{"menuName", "订单统计"},
										{"menuCode", "ordertongji"},
										{"menuType", "page"},
										{"menuUrl", "/uc/mycus/orderton.html"},
								})
						}}
				})
		};
		for (Map map : menus) {
			Map[] children = (Map[]) map.remove("children");
			Map request = request("ucauth/menu/add", map);
			Object menuId = request.get("menuId");
			if (children != null) {
				for (Map child : children) {
					Map[] children1 = (Map[]) child.remove("children");
					child.put("pid", menuId);
					Map request1 = request("ucauth/menu/add", child);
					Object menuId1 = request1.get("menuId");
					if (children1 != null) {
						for (Map map1 : children1) {
							Map[] children2 = (Map[]) map1.remove("children");
							map1.put("pid", menuId1);
							Map request2 = request("ucauth/menu/add", map1);
						}
					}
				}
			}
		}
	}


	/**
	 * 将旧的菜单转移进来
	 */
//	@Test
	public void copyOldMenus() throws Exception {
		String getjson = this.getjson();
		JSONArray arr = JSONArray.toJSONArray(getjson);
		List<Map> list = arr.toList();
		saveMenu(list,null,"app");
	}

	private void saveMenu(List<Map> list,String pid , String type) throws Exception {
		for (Map map : list) {
			Map menu = new LinkedHashMap();
//			menu.put("menuCode","");
			menu.put("menuName",map.get("menuName"));
			Object pageUrl = map.get("pageUrl");
			menu.put("menuUrl", pageUrl);
//			menu.put("menuParams","");
			if(type == null){
				menu.put("menuType",StringUtils.isEmpty((String)pageUrl)?"model":"page");
			}else{
				menu.put("menuType",type);
			}
			if(pid !=null){
				menu.put("pid",pid);
			}
			menu.put("menuDisablestatus","H".equals(map.get("menuShowstatus"))?"D":"E");
			menu.put("menuIcon",map.get("menuIcon"));
			menu.put("menuSeq",map.get("menuSeq"));
//			menu.put("menuDatapolicy","");
			Map request = this.request("ucauth/menu/add", menu);
			Object menuId = request.get("menuId");
			Object children = map.get("children");
			if(children !=null){
				List<Map> ccc = (List)children;
				this.saveMenu(ccc,(String)menuId,null);
			}
		}
	}


	public String getjson() throws IOException {
		InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("oldmenu.json");
		BufferedReader br = new BufferedReader(new InputStreamReader(resourceAsStream));
		StringBuilder sb = new StringBuilder();
		while (true){
			String s = br.readLine();
			if(s == null){
				break;
			}
			sb.append(s);
		}
		return sb.toString();
	}
//	@AfterClass
//	public void destory() throws Exception {
//		this.closeServer();
//		this.clearData();
//	}



}
