package com.niceloo.uc.auth;

import com.niceloo.core.dao.CommonDao;
import com.niceloo.core.job.utils.JobUtils;
import com.niceloo.uc.model.UcRolemenu;
import org.nobject.common.db.ConnectionPool;
import org.nobject.common.db.DBFactory;
import org.nobject.common.lang.MapUtils;
import org.testng.annotations.Test;

import java.util.Map;
import java.util.UUID;

/**
 * @Auther hepangui
 * @Date 2019/11/30 0030
 */
public class CreateTestUsers {

	private CommonDao ucdao;
	private CommonDao bddao;

	public void cc() throws Exception {

		{
			ConnectionPool pool = new ConnectionPool();
			pool.setUsername("root");
			pool.setPassword("123456");
			pool.setUrl("*******************************************************************************************************************");
			pool.setDriverClassName("org.gjt.mm.mysql.Driver");
			pool.setMaxSize(2);
			pool.setMaxTimeout(1000L);
			DBFactory dbFactory = new DBFactory();
			dbFactory.setPool(pool);
			dbFactory.setShowsql(true);
			ucdao = new CommonDao();
			ucdao.setDbFactory(dbFactory);
			dbFactory.buildORM(UcRolemenu.class);
		}

		{
			ConnectionPool pool = new ConnectionPool();
			pool.setUsername("root");
			pool.setPassword("123456");
			pool.setUrl("******************************************************************************************************************");
			pool.setDriverClassName("org.gjt.mm.mysql.Driver");
			pool.setMaxSize(2);
			pool.setMaxTimeout(1000L);
			DBFactory dbFactory = new DBFactory();
			dbFactory.setPool(pool);
			dbFactory.setShowsql(true);
			bddao = new CommonDao();
			bddao.setDbFactory(dbFactory);
			dbFactory.buildORM(BdEe.class);
		}

	}

	@Test
	public void create() throws Exception {
		this.cc();

		Map map = MapUtils.toMap(new Object[][]{
				{"name","A杨铁铮"},
				{"account","yangtiezheng"},
				{"phone","***********"},
				{"id","USER201604051100000000"+11}
		});
		String sql = "INSERT INTO `usercenter`.`UcUser`(`userId`, `userName`, `userLoginname`, `userLoginpwd`, `userLoginpwdstatus`, `userLoginpwdtype`, `userFlag`, `userLockstatus`, `userLastloginip`, `userLastlogindate`, `userCreateddate`, `userCreatetype`, `userModifieddate`, `userModifiedtype`, `userMobile`, `userMobilestatus`, `userEmail`, `userEmailstatus`, `userIdcard`, `userGender`, `userOpenid`, `userQq`, `userWeixin`, `userDelstatus`, `userAvlstatus`, `userSourceid`, `userAreacode`, `userNickname`, `userMarrystatus`, `userPostcode`, `userAddress`, `userTel`, `userIdcardtype`, `userAvatar`, `userMemo`, `userBirthday`, `userSignature`, `userCheckmobile`, `userCheckemail`, `userSourcetype`, `userIdcardstatus`, `userWorkunit`, `userWorkyear`, `userEduschool`, `userEdulevel`, `userEdumajor`, `brandId`, `userJpushid`) VALUES (:id, :name, :account, '7c4a8d09ca3762af61e59520943dc26494f8941b', 'I', 'SHA1', 'I', 'N', '*************', '2019-12-02 08:32:57', '2019-04-12 12:42:20', NULL, '2019-12-02 08:32:57', NULL, :phone, 'Y', NULL, NULL, '', 'M', 'oLHz94-B5ZUjcOSp9_MfbR-Mi_ao', '', NULL, 'N', 'Y', '278D3EFA-BE9D-4591-AD62-CC7CCCA7F67A', '410300', '天下', 'Y', '', '中国河南洛阳', NULL, NULL, '201904/22/*****************.jpg', '510400,500000', NULL, NULL, 'Y', NULL, 'YOULU.WEB', NULL, NULL, NULL, NULL, NULL, NULL, 'SYSTEM', NULL);\n";

//		ucdao.execute(sql,map);


		Map map2 = MapUtils.toMap(new Object[][]{
				{"eeId","********************"+11},
				{"userId","USER201604051100000000"+11},
				{"userName","A杨铁铮"},
				{"eeSourceid", UUID.randomUUID().toString()},
		});
		sql = "INSERT INTO `basedata2`.`BdEe`(`eeId`, `userId`, `userName`, `schoolId`, `eeNo`, `eeCategory`, `eeWorkstatus`, `eeAchvstatus`, `eePostype`, `eeEdutype`, `eeTqaccount`, `eeTqpassword`, `eeFyaccount`, `eeFypassword`, `eeCallouttype`, `eeSalarybaseamount`, `eeSalaryminamount`, `eeSalaryfloatamount`, `eeOrdersuccessnum`, `eeOrderfailnum`, `eePhone`, `eeCompanyemail`, `eeCompanywechat`, `eeAdress`, `eeHiredate`, `eeTermdate`, `eeAvlstatus`, `eeDelstatus`, `eeMemo`, `eeCreater`, `eeCreateddate`, `eeModifier`, `eeModifieddate`, `eeSourceid`, `eePermission`, `eeWeightgrade`, `eeRole`) VALUES (:eeId, :userId, :userName, 'SCHOOL20190411010000000166', NULL, NULL, 'O', 'Y', NULL, 'T', NULL, 'zyj2019', NULL, NULL, 'T', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Y', 'N', NULL, NULL, NULL, NULL, '2019-11-30 14:20:07', :eeSourceid, NULL, NULL, NULL);\n";
//		bddao.execute(sql,map2);

		JobUtils.invokeLater(() -> {
			System.out.println(Thread.currentThread().getName());
		});

		synchronized (this){
			this.wait();
		}

	}

}
