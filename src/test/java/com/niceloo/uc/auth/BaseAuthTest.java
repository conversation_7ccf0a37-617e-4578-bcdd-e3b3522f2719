package com.niceloo.uc.auth;

import com.niceloo.core.utils.CoreUtils;
import org.nobject.common.db.ConnectionPool;
import org.nobject.common.db.DBFactory;
import org.testng.Assert;

import java.util.Map;

/**
 * @Auther hepangui
 * @Date 2019/11/19 0019
 */
public class BaseAuthTest {

	protected Thread serverThread;

	public void startServer() {
		Object object = new Object();

		serverThread = new Thread(new Runnable() {
			@Override
			public void run() {
				try {
					TestServer.start(9097, object);
				} catch (InterruptedException e) {
					return;
				} catch (Exception e) {
					Assert.fail();
				}
			}
		});
		serverThread.start();
		// 等待server启动
		synchronized (object) {
			try {
				object.wait();
			} catch (InterruptedException e) {
				Assert.fail();
			}
		}
	}

	public void closeServer() {
		this.serverThread.interrupt();
	}

	/**
	 * 清除原有数据
	 */
	public void clearData() throws Exception {
		ConnectionPool pool = new ConnectionPool();
		pool.setUsername("root");
		pool.setPassword("123456");
		pool.setUrl("*******************************************************************************************************************");
		pool.setDriverClassName("org.gjt.mm.mysql.Driver");
		pool.setMaxSize(2);
		pool.setMaxTimeout(1000L);
		DBFactory dbFactory = new DBFactory();
		dbFactory.setPool(pool);
		dbFactory.setShowsql(true);
		String[] sql = new String[]{
				"delete from UcMenu",
				"delete from UcRole",
				"delete from UcDatapolicy",
				"delete from UcRolemenu",
				"delete from UcUserrelation"
		};
		dbFactory.executeBatch(sql);
	}

	protected Map request(String uri, Map params) throws Exception {
		Object request = CoreUtils.request("http://127.0.0.1:9097/usercenter/api", uri, params, "", "");
		return (Map)request;

	}
}
