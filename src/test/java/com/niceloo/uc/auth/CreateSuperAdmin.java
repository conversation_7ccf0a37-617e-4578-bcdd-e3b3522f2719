package com.niceloo.uc.auth;

import com.niceloo.core.bean.CoreConfig;
import com.niceloo.core.dao.CommonDao;
import com.niceloo.uc.model.UcRolemenu;
import org.nobject.common.db.ConnectionPool;
import org.nobject.common.db.DBFactory;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Auther hepangui
 * @Date 2019/11/30 0030
 */
public class CreateSuperAdmin {

	private CommonDao dao;

	@Test
	public void test() throws Exception {
		this.cc();
		String sql = "select menuId from UcMenu";
		String[] strings = dao.queryStrings(sql, new Object[0]);
		System.out.println(Arrays.toString(strings));

		sql = "delete from UcRolemenu where roleId = ?";
		dao.execute(sql,new String[]{"ROLE20191121010000000011"});


		List<UcRolemenu> wait4save = new ArrayList<>();
		for (String string : strings) {
			UcRolemenu rolemenu =new UcRolemenu();
			rolemenu.setRoleId("ROLE20191121010000000011");
			rolemenu.setMenuId(string);
			rolemenu.setRolemenuId(dao.genSerial("UcRolemenu", "rolemenuId", "ROLEMENU", 10, "yyyyMMdd" + CoreConfig.Sys.clusterSeq));
			wait4save.add(rolemenu);
		}
		dao.save(wait4save);

	}

	public void cc() throws Exception{
		ConnectionPool pool = new ConnectionPool();
		pool.setUsername("root");
		pool.setPassword("123456");
		pool.setUrl("*******************************************************************************************************************");
		pool.setDriverClassName("org.gjt.mm.mysql.Driver");
		pool.setMaxSize(2);
		pool.setMaxTimeout(1000L);
		DBFactory dbFactory = new DBFactory();
		dbFactory.setPool(pool);
		dbFactory.setShowsql(true);
		 dao = new CommonDao();
		dao.setDbFactory(dbFactory);

		dbFactory.buildORM(UcRolemenu.class);
	}
}
