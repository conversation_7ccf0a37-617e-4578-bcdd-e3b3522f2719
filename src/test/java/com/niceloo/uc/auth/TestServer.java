package com.niceloo.uc.auth;

import com.niceloo.uc.servlet.InitServlet;
import io.undertow.Handlers;
import io.undertow.Undertow;
import io.undertow.server.HttpHandler;
import io.undertow.server.handlers.PathHandler;
import io.undertow.server.handlers.resource.ClassPathResourceManager;
import io.undertow.servlet.Servlets;
import io.undertow.servlet.api.DeploymentInfo;
import io.undertow.servlet.api.DeploymentManager;
import io.undertow.servlet.api.ServletContainer;
import io.undertow.servlet.api.ServletInfo;

import javax.servlet.ServletException;

/**
 * @Auther hepangui
 * @Date 2019/7/23 0023
 */
public class TestServer {


	public static void start(int port, Object obj) throws Exception {

//		WebResourceCollection webResourceCollection = Servlets.webResourceCollection();
//		webResourceCollection.addHttpMethod("http")
//				.addUrlPattern("/api_doc/**").addUrlPattern("/manager/**");


		ServletInfo servletInfo = Servlets.servlet("initServlet", InitServlet.class);
//		servletInfo.addInitParam("","");
		servletInfo.addMapping("/api/*");
		servletInfo.setLoadOnStartup(1);
		DeploymentInfo deploymentInfo = Servlets.deployment();

		deploymentInfo.setClassLoader(TestServer.class.getClassLoader());
		deploymentInfo.setContextPath("/usercenter");
		deploymentInfo.setDeploymentName("");
		deploymentInfo.addServlet(servletInfo);
		ServletContainer container = Servlets.defaultContainer();

		deploymentInfo.setResourceManager(new ClassPathResourceManager(TestServer.class.getClassLoader(),"static"));
//		deploymentInfo.setResourceManager(
//				new PathResourceManager(new File("D:\\YouLuWork\\MicroService\\ManagerCenter\\code\\branches\\1.0.0_20190509\\src\\main\\webapp").toPath()));
		DeploymentManager manager = container.addDeployment(deploymentInfo);
		manager.deploy();
		PathHandler pathHandler = Handlers.path();
		HttpHandler httpHandle = null;
		try {
			httpHandle = manager.start();
		} catch (ServletException e) {
			throw new RuntimeException("容器启动失败");
		}
		pathHandler.addPrefixPath(
				"/usercenter", httpHandle);
		Undertow server = Undertow.builder()
				.addHttpListener(port, "127.0.0.1")
				.setHandler(pathHandler).build();
		server.start();

		synchronized (obj) {
			obj.notify();
		}
		System.out.println("server 启动");
		synchronized (server) {
			server.wait();
		}
//		Thread.sleep(300000);
	}

}
