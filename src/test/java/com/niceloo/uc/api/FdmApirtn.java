package com.niceloo.uc.api;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * API返回表POJO类
 * @version 1.0
 */
@POJO
@ClassDesc(comment="API返回")
public class FdmApirtn{

	/** 返回标识 */
	@POJO(id=true,generator= ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length=36,comment="返回标识")
	private String rtnId;
	/** API标识 */
	@POJO
	@FieldDesc(length=36,comment="API标识")
	private String apiId;
	/** 返回编码 */
	@POJO
	@FieldDesc(length=100,comment="返回编码")
	private String rtnCode;
	/** 返回名称 */
	@POJO
	@FieldDesc(length=100,comment="返回名称")
	private String rtnName;
	/** 返回类型 */
	@POJO
	@FieldDesc(length=100,comment="返回类型")
	private String rtnType;
	/** 返回校验 */
	@POJO
	@FieldDesc(length=200,comment="返回校验")
	private String rtnValidate;
	/** 返回非空 */
	@POJO
	@FieldDesc(length=200,comment="返回非空")
	private String rtnRequired;
	/** 返回备注 */
	@POJO
	@FieldDesc(length=4000,comment="返回备注")
	private String rtnMemo;
	/** 返回序号 */
	@POJO
	@FieldDesc(comment="返回序号")
	private Integer rtnSeq;
	/** 设置返回标识 */
	public void setRtnId(String rtnId){this.rtnId=rtnId;}
	/** 设置API标识 */
	public void setApiId(String apiId){this.apiId=apiId;}
	/** 设置返回编码 */
	public void setRtnCode(String rtnCode){this.rtnCode=rtnCode;}
	/** 设置返回名称 */
	public void setRtnName(String rtnName){this.rtnName=rtnName;}
	/** 设置返回类型 */
	public void setRtnType(String rtnType){this.rtnType=rtnType;}
	/** 设置返回校验 */
	public void setRtnValidate(String rtnValidate){this.rtnValidate=rtnValidate;}
	/** 设置返回备注 */
	public void setRtnMemo(String rtnMemo){this.rtnMemo=rtnMemo;}
	/** 设置返回序号 */
	public void setRtnSeq(Integer rtnSeq){this.rtnSeq=rtnSeq;}
	/** 设置返回非空 */
	public void setRtnRequired(String rtnRequired) {this.rtnRequired = rtnRequired;}
	/** 获取返回标识 */
	public String getRtnId(){return this.rtnId;}
	/** 获取API标识 */
	public String getApiId(){return this.apiId;}
	/** 获取返回编码 */
	public String getRtnCode(){return this.rtnCode;}
	/** 获取返回名称 */
	public String getRtnName(){return this.rtnName;}
	/** 获取返回类型 */
	public String getRtnType(){return this.rtnType;}
	/** 获取返回校验 */
	public String getRtnValidate(){return this.rtnValidate;}
	/** 获取返回备注 */
	public String getRtnMemo(){return this.rtnMemo;}
	/** 获取返回序号 */
	public Integer getRtnSeq(){return this.rtnSeq;}
	/** 获取返回非空 */
	public String getRtnRequired() {return rtnRequired;}

}