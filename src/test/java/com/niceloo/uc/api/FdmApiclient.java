package com.niceloo.uc.api;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * API客户端POJO类
 * @version 1.0
 */
@POJO
@ClassDesc(comment="API客户端")
public class FdmApiclient{

	/** API客户端关系标识 */
	@POJO(id=true,generator= ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length=36,comment="API客户端关系标识")
	private String apiclientId;
	/** API标识 */
	@POJO
	@FieldDesc(length=36,comment="API标识")
	private String apiId;
	/** 客户端标识 */
	@POJO
	@FieldDesc(length=36,comment="客户端标识")
	private String clientId;
	/** API客户端关系开发者 */
	@POJO
	@FieldDesc(length=36,comment="API客户端关系开发者")
	private String apiclientDeveloper;
	/** API客户端关系开发者姓名 */
	@POJO
	@FieldDesc(length=50,comment="API客户端关系开发者姓名")
	private String apiclientDevelopername;
	/** API客户端关系创建时间 */
	@POJO
	@FieldDesc(length=19,comment="API客户端关系创建时间")
	private String apiclientCreatedate;
	/** API客户端关系备注 */
	@POJO
	@FieldDesc(length=2000,comment="API客户端关系备注")
	private String apiclientMemo;

	/** 设置API客户端关系标识 */
	public void setApiclientId(String apiclientId){this.apiclientId=apiclientId;}
	/** 设置API标识 */
	public void setApiId(String apiId){this.apiId=apiId;}
	/** 设置客户端标识 */
	public void setClientId(String clientId){this.clientId=clientId;}
	/** 设置API客户端关系开发者 */
	public void setApiclientDeveloper(String apiclientDeveloper){this.apiclientDeveloper=apiclientDeveloper;}
	/** 设置API客户端关系开发者姓名 */
	public void setApiclientDevelopername(String apiclientDevelopername){this.apiclientDevelopername=apiclientDevelopername;}
	/** 设置API客户端关系创建时间 */
	public void setApiclientCreatedate(String apiclientCreatedate){this.apiclientCreatedate=apiclientCreatedate;}
	/** 设置API客户端关系备注 */
	public void setApiclientMemo(String apiclientMemo){this.apiclientMemo=apiclientMemo;}
	/** 获取API客户端关系标识 */
	public String getApiclientId(){return this.apiclientId;}
	/** 获取API标识 */
	public String getApiId(){return this.apiId;}
	/** 获取客户端标识 */
	public String getClientId(){return this.clientId;}
	/** 获取API客户端关系开发者 */
	public String getApiclientDeveloper(){return this.apiclientDeveloper;}
	/** 获取API客户端关系开发者姓名 */
	public String getApiclientDevelopername(){return this.apiclientDevelopername;}
	/** 获取API客户端关系创建时间 */
	public String getApiclientCreatedate(){return this.apiclientCreatedate;}
	/** 获取API客户端关系备注 */
	public String getApiclientMemo(){return this.apiclientMemo;}


}