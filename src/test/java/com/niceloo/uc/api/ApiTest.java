package com.niceloo.uc.api;

import com.niceloo.core.utils.CoreRule;
import com.niceloo.uc.handler.UcAuthHandler;
import com.niceloo.uc.handler.UcDatapolicyHandler;
import com.niceloo.uc.handler.UcMenuHandler;
import com.niceloo.uc.handler.UcRoleHandler;
import org.nobject.common.code.describer.*;
import org.nobject.common.db.ConnectionPool;
import org.nobject.common.db.DBFactory;
import org.nobject.common.lang.ClassUtils_Javassit;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.StringUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.LinkedList;
import java.util.List;

/**
 * @Auther hepangui
 * @Date 2019/11/20 0020
 */
public class ApiTest {
	public static void main(String[] args) throws Exception {
		ApiTest apiTest = new ApiTest();
		apiTest.gen(UcMenuHandler.class, "CLIENT20191120010000000002");
		apiTest.gen(UcDatapolicyHandler.class, "CLIENT20191120010000000002");
		apiTest.gen(UcRoleHandler.class, "CLIENT20191120010000000003");
		apiTest.gen(UcAuthHandler.class, "CLIENT20191120010000000004");
		apiTest.initDb();
		apiTest.dbFactory.saves(apiTest.api4save.toArray(), apiTest.pool.getConnection(apiTest.dbFactory));
		apiTest.dbFactory.saves(apiTest.param4save.toArray(), apiTest.pool.getConnection(apiTest.dbFactory));
		apiTest.dbFactory.saves(apiTest.rtn4save.toArray(), apiTest.pool.getConnection(apiTest.dbFactory));
		apiTest.dbFactory.saves(apiTest.apiclient4save.toArray(), apiTest.pool.getConnection(apiTest.dbFactory));
		System.out.println("done");
	}

	int pro = 2001;
	private int apiId = 1;
	private int paraId = 1;
	private int rtnId = 1;
	private int clieId = 1;

	List<FdmApi> api4save = new LinkedList<>();
	List<FdmApiparam> param4save = new LinkedList<>();
	List<FdmApirtn> rtn4save = new LinkedList<>();
	List<FdmApiclient> apiclient4save = new LinkedList<>();

	public void gen(Class clazz, String clientId) {


		CoreRule coreRule = (CoreRule) clazz.getAnnotation(CoreRule.class);
		String rootProtocol = coreRule.protocol();


		Method[] methods = clazz.getDeclaredMethods();

		for (Method method : methods) {
			if (!Modifier.isPublic(method.getModifiers())) {
				continue;
			}
			CoreRule rule = method.getAnnotation(CoreRule.class);
			if (rule == null) {
				continue;
			}
			MethodDesc methodDesc = method.getAnnotation(MethodDesc.class);
			FdmApi api = new FdmApi();
			api4save.add(api);
			this.initApi(api);
			api.setApiPath(rootProtocol + "/" + rule.protocol());
			api.setApiProtocol("UC" + pro++);

			api.setApiIslogin("Y");
			if (clazz == UcAuthHandler.class) {
				api.setApiHandlecontent("" +
						"String brandId = (String)$coreIdentity.getAttribute(\"brandId\");\n" +
						"if(brandId == null || brandId.equals(\"\")){\n" +
						"brandId = \"YOULU\";\n" +
						"}\n" +
						" $params_desc.put(\"brandId\",brandId);\n" +
						" $params_desc.put(\"currUserId\",$coreIdentity.getUserId());\n" +
						"return $UC.request(\"api/" + rootProtocol + "/" + rule.protocol() + "\",$params_desc);");
			} else if (clazz == UcRoleHandler.class && (method.getName().equalsIgnoreCase("add")
					|| method.getName().equalsIgnoreCase("copy"))) {
				api.setApiHandlecontent("" +
						"String brandId = (String)$params.get(\"brandId\");\n" +
						"if(brandId == null || brandId.equals(\"\")){\n" +
						"\tbrandId = (String)$coreIdentity.getAttribute(\"brandId\");\n" +
						"}\n" +
						"if(brandId == null || brandId.equals(\"\")){\n" +
						"brandId = \"YOULU\";\n" +
						"}\n" +
						" $params_desc.put(\"brandId\",brandId);\n" +
						" $params_desc.put(\"roleCreaterid\",$coreIdentity.getUserId());\n" +
						" $params_desc.put(\"roleCreatername\",$coreIdentity.getUserName());\n" +
						"return $UC.request(\"api/" + rootProtocol + "/" + rule.protocol() + "\",$params_desc);");
//				api.setApiIslogin("Y");
			} else {
				api.setApiHandlecontent("" +
						"String brandId = (String)$coreIdentity.getAttribute(\"brandId\");\n" +
						"if(brandId == null || brandId.equals(\"\")){\n" +
						"brandId = \"YOULU\";\n" +
						"}\n" +
						" $params_desc.put(\"brandId\",brandId);\n" +
						"return $UC.request(\"api/" + rootProtocol + "/" + rule.protocol() + "\",$params_desc);");
//				api.setApiIslogin("N");
			}
			String auth = rule.auth();
			if (!StringUtils.isEmpty(auth)) {
				api.setApiHandlecontent("$AUTH.checkAuth(\"" + auth + "\",$coreIdentity);\n" + api.getApiHandlecontent());
			}

			api.setApiName(methodDesc.comment());
			api.setApiDeprecatedstatus("N");
			api.setApiCheckstatus("Y");

			Annotation[][] parameterAnnotations = method.getParameterAnnotations();
			String[] paramNames = ClassUtils_Javassit.getParamerterNames(method);
			Class<?>[] parameterTypes = method.getParameterTypes();
			for (int i = 0; i < parameterAnnotations.length; i++) {
				Annotation[] parameterAnnotation = parameterAnnotations[i];
				if (parameterAnnotation != null && parameterAnnotation.length > 0 && parameterAnnotation[0].annotationType() == ParamDesc.class) {
					ParamDesc de = (ParamDesc) parameterAnnotation[0];
					String name = paramNames[i];
					if ("currUserId".equalsIgnoreCase(name)) {
						continue;
					}
					if ("brandId".equalsIgnoreCase(name)) {
						continue;
					}
					if ("userId".equals(name) && de.memo().indexOf("网关") > -1) {
						api.setApiHandlecontent("$params_desc.put(\"userId\",$coreIdentity.getUserId());\n" + api.getApiHandlecontent());
						continue;
					}
					String typeName = parameterTypes[i].getSimpleName();
					FdmApiparam apiparam = buildParam(api, i, de, name, typeName);
					param4save.add(apiparam);
				}
			}

			ReturnDesc returns = methodDesc.returns();
			Return2Desc[] subs = returns.subs();
			for (int i = 0; i < subs.length; i++) {
				Return2Desc de = subs[i];
				FdmApirtn tn = new FdmApirtn();
				tn.setApiId(api.getApiId());
				tn.setRtnCode(de.name());
				String s = StringUtils.fillEmptyWithStr(rtnId++, 10, "0");
				String id = "RTN20191117" + s;
				tn.setRtnId(id);
				tn.setRtnName(de.comment());
				tn.setRtnRequired("Y");
				if (!StringUtils.isEmpty(de.memo())) {
					tn.setRtnMemo(de.memo());
				}
				tn.setRtnSeq(i * 10);
				tn.setRtnType(de.type().getSimpleName());
				rtn4save.add(tn);
			}
			FdmApiclient fdmApiclient = new FdmApiclient();
			fdmApiclient.setApiId(api.getApiId());
			fdmApiclient.setApiclientCreatedate(DateUtils.getNowDString());
			fdmApiclient.setApiclientDeveloper("USER20190916010000000795");
			fdmApiclient.setApiclientDevelopername("何攀贵");
			fdmApiclient.setClientId(clientId);
			String s = StringUtils.fillEmptyWithStr(clieId++, 10, "0");
			String id = "ACLI20191117" + s;
			fdmApiclient.setApiclientId(id);
			apiclient4save.add(fdmApiclient);
		}

	}

	private FdmApiparam buildParam(FdmApi api, int i, ParamDesc de, String name, String typeName) {
		FdmApiparam apiparam = new FdmApiparam();
		String s = StringUtils.fillEmptyWithStr(paraId++, 10, "0");
		String id = "PARAM20191117" + s;
		apiparam.setParamId(id);
		apiparam.setApiId(api.getApiId());
		apiparam.setParamCode(name);
//		if (!StringUtils.isEmpty(de.memo())) {
		apiparam.setParamMemo(de.memo());
//		}
//		if (!StringUtils.isEmpty(de.defVal())) {
		apiparam.setParamDefval(de.defVal());
//		}
		apiparam.setParamIsrequire(de.unnull() ? "Y" : "N");
		if (de.length() != -1) {
			apiparam.setParamLength(de.length() + "");
		}
		apiparam.setParamSeq(i * 10);
		apiparam.setParamName(de.comment());

		apiparam.setParamType(typeName);
		if (!StringUtils.isEmpty(de.validate())) {
			apiparam.setParamValidate(getValidate(de));
		} else {
			apiparam.setParamValidate("");
		}
		return apiparam;
	}

	private String getValidate(ParamDesc de) {
		String validate = de.validate();
		switch (validate) {
			case Validate.M_SAFE:
				return "M:org.nobject.common.regex.ValidatorUtils#isSafe";
			case Validate.R_NUM:
				return "M:org.nobject.common.regex.ValidatorUtils#isNUM";
			case Validate.R_ID:
				return "M:org.nobject.common.regex.ValidatorUtils#isID";
		}
		if (validate.startsWith("R:")) {
			return validate;
		}
		throw new RuntimeException("不支持的Validate");
	}


	private void initApi(FdmApi api) {
		String s = StringUtils.fillEmptyWithStr(apiId++, 10, "0");
		String id = "API20191117" + s;
		// 用户中心
		api.setApiId(id);
		api.setModuId("MODU201803160000000001");
		api.setApiAvlstatus("Y");
		api.setApiHandletype("D");
		api.setApiManager("USER20190916010000000795");
		api.setApiManagername("何攀贵");
	}

	private DBFactory dbFactory;

	ConnectionPool pool = new ConnectionPool();

	public void initDb() throws Exception {
		pool.setUsername("root");
		pool.setPassword("123456");
		pool.setUrl("*******************************************************************************************************************");

//		pool.setUsername("db_writer");
//		pool.setPassword("6qD^8mbNsd&j");
//		pool.setUrl("jdbc:mysql://*************:3306/midmon_msa?characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&useSSL=false");

		pool.setDriverClassName("org.gjt.mm.mysql.Driver");
		pool.setMaxSize(2);
		pool.setMaxTimeout(1000L);
		dbFactory = new DBFactory();
		dbFactory.setPool(pool);
		dbFactory.setShowsql(true);

		dbFactory.buildORM(FdmApi.class);
		dbFactory.buildORM(FdmApiclient.class);
		dbFactory.buildORM(FdmApiparam.class);
		dbFactory.buildORM(FdmApirtn.class);

		String sql = "delete from FdmApi where apiId like 'API20191117%'";
		dbFactory.execute(sql, new Object[]{}, pool.getConnection(dbFactory));
		sql = "delete from FdmApiclient where apiId like 'API20191117%'";
		dbFactory.execute(sql, new Object[]{}, pool.getConnection(dbFactory));
		sql = "delete from FdmApiparam where apiId like 'API20191117%'";
		dbFactory.execute(sql, new Object[]{}, pool.getConnection(dbFactory));
		sql = "delete from FdmApirtn where apiId like 'API20191117%'";
		dbFactory.execute(sql, new Object[]{}, pool.getConnection(dbFactory));
	}
}
