package com.niceloo.uc.api;
import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * API参数表POJO类
 * @version 1.0
 */
@POJO
@ClassDesc(comment="API参数")
public class FdmApiparam{

	/** 参数标识 */
	@POJO(id=true,generator= ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length=36,comment="参数标识")
	private String paramId;
	/** API标识 */
	@POJO
	@FieldDesc(length=36,comment="API标识")
	private String apiId;
	/** 参数编码 */
	@POJO
	@FieldDesc(length=100,comment="参数编码")
	private String paramCode;
	/** 参数名称 */
	@POJO
	@FieldDesc(length=100,comment="参数名称")
	private String paramName;
	/** 参数类型 */
	@POJO
	@FieldDesc(length=100,comment="参数类型")
	private String paramType;
	/** 参数非空 */
	@POJO
	@FieldDesc(length=1,comment="参数非空")
	private String paramIsrequire;
	/** 参数校验 */
	@POJO
	@FieldDesc(length=200,comment="参数校验")
	private String paramValidate;
	/** 参数默认值 */
	@POJO
	@FieldDesc(length=4000,comment="参数默认值")
	private String paramDefval;
	/** 参数备注 */
	@POJO
	@FieldDesc(length=4000,comment="参数备注")
	private String paramMemo;
	/** 参数序号 */
	@POJO
	@FieldDesc(comment="参数序号")
	private Integer paramSeq;
	/** 参数长度 */
	@POJO
	@FieldDesc(length=10,comment="参数长度")
	private String paramLength;
	/** 设置参数标识 */
	public void setParamId(String paramId){this.paramId=paramId;}
	/** 设置API标识 */
	public void setApiId(String apiId){this.apiId=apiId;}
	/** 设置参数编码 */
	public void setParamCode(String paramCode){this.paramCode=paramCode;}
	/** 设置参数名称 */
	public void setParamName(String paramName){this.paramName=paramName;}
	/** 设置参数类型 */
	public void setParamType(String paramType){this.paramType=paramType;}
	/** 设置参数非空 */
	public void setParamIsrequire(String paramIsrequire){this.paramIsrequire=paramIsrequire;}
	/** 设置参数校验 */
	public void setParamValidate(String paramValidate){this.paramValidate=paramValidate;}
	/** 设置参数默认值 */
	public void setParamDefval(String paramDefval){this.paramDefval=paramDefval;}
	/** 设置参数备注 */
	public void setParamMemo(String paramMemo){this.paramMemo=paramMemo;}
	/** 设置参数序号 */
	public void setParamSeq(Integer paramSeq){this.paramSeq=paramSeq;}
	/** 设置参数长度 */
	public void setParamLength(String paramLength){this.paramLength=paramLength;}
	/** 获取参数标识 */
	public String getParamId(){return this.paramId;}
	/** 获取API标识 */
	public String getApiId(){return this.apiId;}
	/** 获取参数编码 */
	public String getParamCode(){return this.paramCode;}
	/** 获取参数名称 */
	public String getParamName(){return this.paramName;}
	/** 获取参数类型 */
	public String getParamType(){return this.paramType;}
	/** 获取参数非空 */
	public String getParamIsrequire(){return this.paramIsrequire;}
	/** 获取参数校验 */
	public String getParamValidate(){return this.paramValidate;}
	/** 获取参数默认值 */
	public String getParamDefval(){return this.paramDefval;}
	/** 获取参数备注 */
	public String getParamMemo(){return this.paramMemo;}
	/** 获取参数序号 */
	public Integer getParamSeq(){return this.paramSeq;}
	/** 获取参数长度 */
	public String getParamLength(){return this.paramLength;}


}