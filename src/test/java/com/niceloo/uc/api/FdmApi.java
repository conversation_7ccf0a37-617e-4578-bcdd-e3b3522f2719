package com.niceloo.uc.api;

import org.nobject.common.code.describer.ClassDesc;
import org.nobject.common.code.describer.FieldDesc;
import org.nobject.common.db.POJO;
import org.nobject.common.db.model.ORMGeneratorModel;

/**
 * API表POJO类
 * @version 1.0
 */
@POJO
@ClassDesc(comment="API")
public class FdmApi{

	/** API标识 */
	@POJO(id=true,generator= ORMGeneratorModel.TYPE_ASSIGNED)
	@FieldDesc(length=36,comment="API标识")
	private String apiId;
	/** 模块标识 */
	@POJO
	@FieldDesc(length=36,comment="模块标识")
	private String moduId;
	/** API名称 */
	@POJO
	@FieldDesc(length=100,comment="API名称")
	private String apiName;
	/** API协议号 */
	@POJO
	@FieldDesc(length=20,comment="API协议号")
	private String apiProtocol;
	/** API登陆标识 */
	@POJO
	@FieldDesc(length=1,comment="API登陆标识")
	private String apiIslogin;
	/** API可用状态 */
	@POJO
	@FieldDesc(length=1,comment="API可用状态")
	private String apiAvlstatus;
	/** API路径 */
	@POJO
	@FieldDesc(length=100,comment="API路径")
	private String apiPath;
	/** API处理模式 */
	@POJO
	@FieldDesc(length=1,comment="API处理模式")
	private String apiHandletype;
	/** API处理内容 */
	@POJO
	@FieldDesc(comment="API处理内容")
	private String apiHandlecontent;
	/** API绑定类 */
	@POJO
	@FieldDesc(length=200,comment="API绑定类")
	private String apiBindclass;
	/** API绑定资源 */
	@POJO
	@FieldDesc(length=36,comment="API绑定资源")
	private String resId;
	/** API备注 */
	@POJO
	@FieldDesc(length=4000,comment="API备注")
	private String apiMemo;
	/** API负责人 */
	@POJO
	@FieldDesc(length=36,comment="API负责人")
	private String apiManager;
	/** API负责人名称 */
	@POJO
	@FieldDesc(length=50,comment="API负责人名称")
	private String apiManagername;
	
	/** API审核状态 */
	@POJO
	@FieldDesc(length=1,comment="API审核状态")
	private String apiCheckstatus;

	@POJO
	@FieldDesc(length=1,comment="API状态")
	private String apiDeprecatedstatus;
	
	/** 设置API标识 */
	public void setApiId(String apiId){this.apiId=apiId;}
	/** 设置模块标识 */
	public void setModuId(String moduId){this.moduId=moduId;}
	/** 设置API名称 */
	public void setApiName(String apiName){this.apiName=apiName;}
	/** 设置API协议号 */
	public void setApiProtocol(String apiProtocol){this.apiProtocol=apiProtocol;}
	/** 设置API登陆标识 */
	public void setApiIslogin(String apiIslogin){this.apiIslogin=apiIslogin;}
	/** 设置API可用状态 */
	public void setApiAvlstatus(String apiAvlstatus){this.apiAvlstatus=apiAvlstatus;}
	/** 设置API路径 */
	public void setApiPath(String apiPath){this.apiPath=apiPath;}
	/** 设置API处理模式 */
	public void setApiHandletype(String apiHandletype){this.apiHandletype=apiHandletype;}
	/** 设置API处理内容 */
	public void setApiHandlecontent(String apiHandlecontent){this.apiHandlecontent=apiHandlecontent;}
	/** 设置API绑定类 */
	public void setApiBindclass(String apiBindclass){this.apiBindclass=apiBindclass;}
	/** 设置API绑定资源 */
	public void setResId(String resId){this.resId=resId;}
	/** 设置API备注 */
	public void setApiMemo(String apiMemo){this.apiMemo=apiMemo;}
	/** 获取API标识 */
	public String getApiId(){return this.apiId;}
	/** 获取模块标识 */
	public String getModuId(){return this.moduId;}
	/** 获取API名称 */
	public String getApiName(){return this.apiName;}
	/** 获取API协议号 */
	public String getApiProtocol(){return this.apiProtocol;}
	/** 获取API登陆标识 */
	public String getApiIslogin(){return this.apiIslogin;}
	/** 获取API可用状态 */
	public String getApiAvlstatus(){return this.apiAvlstatus;}
	/** 获取API路径 */
	public String getApiPath() {
		return apiPath;
	}
	/** 获取API处理模式 */
	public String getApiHandletype(){return this.apiHandletype;}
	/** 获取API处理内容 */
	public String getApiHandlecontent(){return this.apiHandlecontent;}
	/** 获取API绑定类 */
	public String getApiBindclass(){return this.apiBindclass;}
	/** 获取API绑定资源 */
	public String getResId(){return this.resId;}
	/** 获取API备注 */
	public String getApiMemo(){return this.apiMemo;}
	/** 获取API负责人 */
	public String getApiManager() {
		return apiManager;
	}
	/** 设置API负责人 */
	public void setApiManager(String apiManager) {
		this.apiManager = apiManager;
	}
	/** 获取API负责人名称 */
	public String getApiManagername() {
		return apiManagername;
	}
	/** 设置API负责人名称 */
	public void setApiManagername(String apiManagername) {
		this.apiManagername = apiManagername;
	}
	/** 获取API审核状态 */
	public String getApiCheckstatus() {
		return apiCheckstatus;
	}
	/** 设置API审核状态 */
	public void setApiCheckstatus(String apiCheckstatus) {
		this.apiCheckstatus = apiCheckstatus;
	}

	public String getApiDeprecatedstatus() {
		return apiDeprecatedstatus;
	}

	public void setApiDeprecatedstatus(String apiDeprecatedstatus) {
		this.apiDeprecatedstatus = apiDeprecatedstatus;
	}
}