package com.niceloo.uc;

import com.youlu.pdm.build.CodeBuild;
import com.youlu.pdm.model.Table;
import com.youlu.pdm.parse.PdmParse;
import org.dom4j.DocumentException;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther hepangui
 * @Date 2019/12/24 0024
 */
public class PdnCodeGen {
	public static void main(String[] args) throws IOException, DocumentException {
		PdmParse parsePDM = new PdmParse("d://test.pdm");
		List<Table> allTables = parsePDM.loadAllTables();
		// 包括表 (key为表名 value为编号)
		Map<String, Object> includeTables = new HashMap<>();
		includeTables.put("StWareplace", "1");
		includeTables.put("StWarehouse", "2");
		//............
		/**
		 * 排除列
		 */
		Map<String, Object> handlerExcludeCols = new HashMap<>();
		handlerExcludeCols.put("*Createddate", null); // 排除常见的字段
		handlerExcludeCols.put("*Modifieddate", null); // 排除常见的字段
		//........
		CodeBuild build = new CodeBuild()
				.setIncludeTables(includeTables)
				.setHandlerExcludeCols(handlerExcludeCols)
				.setModel("ST") // model 是指项目前缀，比如用户中心UC，订单中心OC，此处ST指仓储物流中心
				.setAuthor("authorName")  // 作者名称，请天自己的名称
				                          // 创建时间
				.setDateStr(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
				.setBasePath("E:\\code\\") // 生成代码的位置
				.setBasePackage("com.niceloo.st")  // 包
				.setNeedLogin(false);        // api接口是否需要登录
		// 生成Java文件
		build.buildTablesCode(allTables);

		System.out.println("======build done ======");
	}
}
