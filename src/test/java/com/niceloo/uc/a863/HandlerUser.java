//package com.niceloo.uc.a863;
//
//import com.niceloo.core.dao.CommonDao;
//import com.niceloo.uc.model.UcUser;
//import net.sourceforge.pinyin4j.PinyinHelper;
//import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
//import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
//import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
//import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
//import org.nobject.common.db.ConnectionPool;
//import org.nobject.common.db.DBFactory;
//import org.nobject.common.lang.StringUtils;
//import org.testng.annotations.Test;
//
//import java.io.File;
//import java.io.PrintWriter;
//import java.util.*;
//
///**
// * @Auther hepangui
// * @Date 2019/12/27 0027
// */
//public class HandlerUser {
//
//	private DBFactory dbFactory;
//	ConnectionPool pool;
//
//	CommonDao commonDao;
//	public void clearData() throws Exception {
//		 pool = new ConnectionPool();
//		pool.setUsername("root");
//		pool.setPassword("LR5zqY1RXkJa65fE");
//		pool.setUrl("******************************************************************************************************************");
//		pool.setDriverClassName("org.gjt.mm.mysql.Driver");
//		pool.setMaxSize(2);
//		pool.setMaxTimeout(1000L);
//		dbFactory = new DBFactory();
//		dbFactory.setPool(pool);
//		dbFactory.setShowsql(true);
//		commonDao = new CommonDao();
//		commonDao.setDbFactory(dbFactory);
//
//	}
//
//	@Test
//	public void handlerUser() throws Exception {
//		this.clearData();
//
//
//		int index = 0;
//
//		List<String> names = new ArrayList<>();
//		String [] num = {"一","二","三","四","五","六","七","八","九"};
//		for(int i=0;i<xing.length();i++){
//			for(int j=0;j<num.length;j++){
//				for (int k = 0; k < num.length; k++) {
//					String name;
//					if(j==k){
//						name = xing.charAt(i)+num[j];
//					}else{
//						name = xing.charAt(i)+num[j]+num[k];
//					}
//					names.add(name);
//				}
//			}
//		}
//
//		int count = commonDao.queryInt("select count(*) from UcUser",new Object[]{});
//
//		int queryI = 0;
//
//		File file = new File("F:createUser.sql");
//		if(!file.exists()){
//			file.createNewFile();
//		}
//		PrintWriter printWriter = new PrintWriter(file);
//		while (queryI<count){
//			String sql = "select userId,username,userLoginname,userMobile from UcUser order by userId limit "+queryI+",1000";
//			queryI +=1000;
//			List<Map> maps = dbFactory.queryMaps(sql, new Object[]{}, null, pool.getConnection(dbFactory));
//			for (Map map : maps) {
////				String ss = "update UcUser set username = ? ,userLoginname=?,userMobile=? where userId = ? ;";
//				String ss = "update BdEe set username = ? where userId = ? ;";
//				String s = names.get(index++);
//				String phone = "13100000000"+ StringUtils.fillEmptyWithStr(index,8,"0");
//				ss = ss.replaceFirst("\\?","'"+s+"'");
////				ss = ss.replaceFirst("\\?","'"+toPinyin(s)+"'");
////				ss = ss.replaceFirst("\\?","'"+phone+"'");
//				ss = ss.replaceFirst("\\?","'"+map.get("userId")+"'");
//				printWriter.println(ss);
//			}
//
//
////
////			String sql = "select userId,userName,userLoginname,userMobile from UcUser_copy1 " +
////					"where userId in(select userId from UcUser) order by userId limit "+queryI+",1000";
////			queryI +=1000;
////			List<Map> maps = dbFactory.queryMaps(sql, new Object[]{}, null, pool.getConnection(dbFactory));
////			for (Map map : maps) {
////				String ss = "update UcUser set userName = ? ,userLoginname=?,userMobile=? where userId = ? ;";
//////				String ss = "update BdEe set userName = ? where userId = ? ;";
//////				String s = names.get(index++);
//////				String phone = "13100000000"+ StringUtils.fillEmptyWithStr(index,8,"0");
//////				if(map.get("userName")==null){
//////					ss = ss.replaceFirst("\\?","null");
//////				}else{
////					ss = ss.replaceFirst("\\?","'"+(map.get("userName")==null?"":map.get("userName"))+"'");
//////				}
////				ss = ss.replaceFirst("\\?","'"+(map.get("userLoginname")==null?"":map.get("userLoginname"))+"'");
////				ss = ss.replaceFirst("\\?","'"+(map.get("userMobile")==null?"":map.get("userMobile"))+"'");
////				ss = ss.replaceFirst("\\?","'"+map.get("userId")+"'");
////				printWriter.println(ss);
////			}
//		}
//		printWriter.close();
//
//
//
//	}
//
//
//	static String xing = "赵钱孙李周吴郑王冯陈褚卫蒋沈韩杨朱秦尤许何吕施张孔曹严华金陶戚谢邹喻柏水窦云苏潘葛奚范彭郎鲁昌马苗方任袁柳鲍唐费廉岑薛雷倪滕殷罗毕郝安乐傅皮卞康卜顾孟平黄穆萧姚邵湛毛狄米贝明臧计成戴谈宋庞熊舒屈项董梁杜阮蓝闵强贾娄童郭梅盛林刁钟邱高夏蔡田胡凌霍万支柯昝管莫经缪干解应宗丁宣邓单杭洪左崔钮龚邢裴荣翁荀惠甄芮羿邴段焦巴山车侯全班宁仇栾钭祖龙叶司白怀蒲邰从鄂索咸赖卓屠池乔能苍双闻莘党翟劳冉宰雍卻桑桂寿尚农别庄柴瞿充茹宦艾廖衡耿满匡寇广欧沃越夔厍聂晁勾敖冷訾阚那简饶曾沙乜蒯查盖淳颛壤拓法南门海帅佴哈笪年";
//
//	public static void main(String[] args) {
//
//		StringBuilder sb = new StringBuilder("");
//		Set set = new LinkedHashSet();
//		for (char c : xing.toCharArray()) {
//			String s = toPinyin(c+"");
//			boolean add = set.add(s);
//			if(!add){
//				System.out.println(s);
//			}else{
//				sb.append(c);
//			}
//		}
//		System.out.println(sb);
//
//	}
//
//	public static String toPinyin(String chinese){
//
//		String pinyinStr = "";
//		char[] newChar = chinese.toCharArray();
//		HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
//		defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
//		defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
//		for (int i = 0; i < newChar.length; i++) {
//			if (newChar[i] > 128) {
//				try {
//					pinyinStr += PinyinHelper.toHanyuPinyinStringArray(newChar[i], defaultFormat)[0];
//				} catch (BadHanyuPinyinOutputFormatCombination e) {
//					e.printStackTrace();
//				}
//			}else{
//				pinyinStr += newChar[i];
//			}
//		}
//		return pinyinStr;
//	}
//
//
//}
