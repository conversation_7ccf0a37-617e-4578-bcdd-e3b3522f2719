package com.niceloo.uc.log;

import com.niceloo.core.es.EsService;
import org.nobject.common.lang.DateUtils;

import java.io.IOException;
import java.util.UUID;

/**
 * @Auther hepangui
 * @Date 2020/2/10 0010
 */
public class LoginLogCreater {
	public static void main(String[] args) throws IOException {
		EsService esService = new EsService();
		esService.setUrl("http://192.168.11.253:9200");

		for (int i=1000;i<2000;i++){

			String errorMsg = "";
			if(i %10 ==0){
				int j = i %10;
				if(j % 3 ==0){
					errorMsg = "密码错误";
				}
				if(j % 3 ==1){
					errorMsg = "账号被禁用";
				}
				if(j % 3 ==2){
					errorMsg = "账号被锁定";
				}
			}

			String json = "{\"logLogintype\":\""+(i%3==0?"PM":"AM")
					+"\",\"logLoginstatus\":\""+(i%10==0?"E":"S")
					+"\",\"logDate\":\""+ DateUtils.getNowDString()
					+"\",\"userMobile\":\"1373333"+i+"\",\"userLoginname\":\"admin"+i%7+"\",\"logLoginerrormsg\":\""+errorMsg
					+"\",\"userName\":\"管理员"+(i % 7)+"\",\"logLoginip\":\"1.2.3."+i%255+"\",\"userId\":\"USER2018102301000000566"+i%7+"\"}";


			System.out.println(json);
			esService.save("uc_adminloginlog", UUID.randomUUID().toString(),json);
		}
	}
}
