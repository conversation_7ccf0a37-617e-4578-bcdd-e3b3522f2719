package test;

import org.nobject.common.js.JSONUtils;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * @Auther hepangui
 * @Date 2020/2/18 0018
 */
public class CreateJson {
	public static void main(String[] args) {
		ArrayList<Object> arrayList = new ArrayList<>();
		for (int i = 0; i < 1; i++) {
			HashMap<String,String> hashMap = new HashMap<>();
			if(i<10) {
				hashMap.put("userMobile", "1384803709"+i);
			}else {
				hashMap.put("userMobile", "138480370"+i);
			}
			hashMap.put("userName", "测试"+i);
			hashMap.put("schoolId", "SCHOOL20190411010000000175");
			hashMap.put("brandId", "YOULU");
			hashMap.put("sourceChannel", "03109");
			arrayList.add(hashMap);
		}
		System.out.println(JSONUtils.toString(arrayList));
	}


	public static void main1(String[] args) {
		test(new String []{"aaa","bbb"});
	}

	public  static void test(String [] aaa){

	}

}
