package test;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.nobject.common.file.FileUtils;
import org.nobject.common.js.JSONUtils;
import org.nobject.common.lang.MapUtils;
import org.nobject.common.lang.NumberUtils;
import org.nobject.common.lang.StringUtils;

public class DemoData {
	
	public static void main(String[] args) {
		main_mr(args);
	}
	
	public static void main_mr(String[] args) {
		String [] ss=FileUtils.read2String("c:/a.csv").split("[\r\n]+");
		
		List datas=new LinkedList();
		
		for(String s:ss){
			String [] es=s.split(",");
			datas.add(MapUtils.toMap(new Object[][]{
				{"lnt"		,NumberUtils.toDouble(es[0])},
				{"lat"		,NumberUtils.toDouble(es[1])},
				{"rsrp"		,NumberUtils.toDouble(es[2])},
			}));
		}
		
		System.out.println(JSONUtils.toString(datas));
	}
	
	public static void main_station(String[] args) {
		
		String [] ss=FileUtils.read2String("c:/a.csv").split("[\r\n]+");
		
		List datas=new LinkedList();
		
		Map<String,Map> m_sign=new HashMap();
		
		for(String s:ss){
			String [] es=s.split(",");
			String code	=es[2];
			
			if(!m_sign.containsKey(code)){
				Map d=MapUtils.toMap(new Object[][]{
					{"code"		,code},
					{"name"		,es[3]},
					{"lnt"		,NumberUtils.toDouble(es[5])},
					{"lat"		,NumberUtils.toDouble(es[6])},
					{"cellcount",NumberUtils.toDouble(es[7])},
					{"code"		,code},
					{"cell"		,new LinkedList()},
					{"cellrange",NumberUtils.toDouble(es[11])},
				});
				m_sign.put(code, d);
				datas.add(d);
			}
				
			Map data	=m_sign.get(code);
			List cell	=(List)data.get("cell");
			
			cell.add(MapUtils.toMap(new Object[][]{
				{"cell_code"	,es[8]},
				{"cell_high"	,es[9]},
				{"cell_arc"		,NumberUtils.toDouble(es[10])},
				{"cell_rscp0"	,NumberUtils.toDouble(StringUtils.removeEnd(es[12], "%"))},
				{"cell_rscp1"	,NumberUtils.toDouble(StringUtils.removeEnd(es[13], "%"))},
				{"cell_rscp2"	,NumberUtils.toDouble(StringUtils.removeEnd(es[14], "%"))},
				{"cell_rscp3"	,NumberUtils.toDouble(StringUtils.removeEnd(es[15], "%"))},
				{"cell_rscp4"	,NumberUtils.toDouble(StringUtils.removeEnd(es[16], "%"))}
			}));
			
			
		}
		
		System.out.println(JSONUtils.toString(datas));
		
	}
	
}
