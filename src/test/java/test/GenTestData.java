package test;

import org.nobject.common.db.ConnectionPool;
import org.nobject.common.db.DBFactory;

public class GenTestData implements Runnable {
	
	int thread_batchsize;
	int idx;
	public GenTestData(int idx,int thread_batchsize){
		this.thread_batchsize	=thread_batchsize;
		this.idx				=idx;
	}
	
	/**
	 * @see java.lang.Runnable#run()
	 */
	@Override
	public void run() {
		for (int i = 0; i < thread_batchsize; i++) {
			
		}
	}

	static ConnectionPool pool=new ConnectionPool();
	
	public static void main(String[] args) throws Exception{
		
		int thread_count		=100;
		final int thread_batchsize	=10000*10;//100W
		
		pool.setUsername		("root");
		pool.setPassword		("123456");
		pool.setUrl				("**************************************************************************************************************");
		pool.setDriverClassName	("org.gjt.mm.mysql.Driver");
		pool.setMaxSize			(50);
		pool.setMaxTimeout		(1000l);
		final DBFactory dbFactory=new DBFactory();
		dbFactory.setPool(pool);
		dbFactory.setShowsql(false);
		
		
		for (int i = 0; i < 100; i++) {
			new Thread(new GenTestData(i,thread_batchsize)).start();
		}
		
	}
	
}
