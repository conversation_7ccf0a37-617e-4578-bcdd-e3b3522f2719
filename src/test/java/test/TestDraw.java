package test;

import java.awt.AlphaComposite;
import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.Random;

import javax.imageio.ImageIO;

public class TestDraw {
	public static void main(String[] args) throws Exception{
		BufferedImage image 	= new BufferedImage(1000, 1000,     BufferedImage.TYPE_INT_ARGB);
		Graphics2D g2d 			= image.createGraphics();
		
		Integer alpha=100;
		g2d.setStroke(new BasicStroke(100));
		g2d.setColor(new Color(0,255,0,alpha));
		g2d.drawRect(0, 0, 100, 100);
		g2d.dispose();
		// 保存文件    
		ImageIO.write(image, "png", new File("c:/test2.png"));
	}
}
