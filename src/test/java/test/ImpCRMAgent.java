package test;

import org.nobject.common.db.ConnectionPool;
import org.nobject.common.db.DBFactory;
import org.nobject.common.file.FileUtils;
import org.nobject.common.lang.DateUtils;
import org.nobject.common.lang.StringUtils;


public class ImpCRMAgent {
	
	public static void main(String[] args) throws Exception{
		
		String file=FileUtils.read2String("C:/a.csv");
		
		String[] lines=file.split("[\r\n]+");
		
		StringBuffer sql_uc=new StringBuffer();
		StringBuffer sql_sl=new StringBuffer();
		
		for (int i = 0; i < lines.length; i++) {
			
			String[] cols=lines[i].split(",");
			String userId		="USER20181122"+StringUtils.fillEmptyWithStr(i, 10, "0");
			String agentId		="AGENT20181122"+StringUtils.fillEmptyWithStr(i, 10, "0");
			String agentSourceid=cols[0];
			String agentName	=cols[1];
			String userLoginnname=cols[2];
			String userMobile	=cols[3];
			String userName		=cols[4];
			String userQq		=cols[5];
			String userLoginpwd	=cols[11];
			String userAvlstatus="0".equals(cols[16])?"Y":"N";
			String userDelstatus="1".equals(cols[17])?"Y":"N";
			
			String userLoginpwdtype	="MD5";
			String userFlag			="A";
			String userCreatedate	=DateUtils.getNowDString();
			String userCreatetype	="CRM_IMP";
			String userModifieddate	=DateUtils.getNowDString();
			String userModifiedtype	="CRM_IMP";
			
			String agentAvlstatus	=userAvlstatus;
			String agentDelstatus	=userDelstatus;
			String agentCreatetime	=DateUtils.getNowDString();
			String agentModifiedtime=DateUtils.getNowDString();
			
			sql_uc.append("INSERT INTO UcUser (userId,userName,userLoginname,userLoginpwd,userMobile,userQq,userAvlstatus,userDelstatus,userLoginpwdtype,userFlag,userCreatedate,userCreatetype,userModifieddate,userModifiedtype) VALUES ('"+userId+"','"+userName+"','"+userLoginnname+"','"+userLoginpwd+"','"+userMobile+"','"+userQq+"','"+userAvlstatus+"','"+userDelstatus+"','"+userLoginpwdtype+"','"+userFlag+"','"+userCreatedate+"','"+userCreatetype+"','"+userModifieddate+"','"+userModifiedtype+"')").append(";\n");
			
			sql_sl.append("INSERT INTO SlAgent (agentId,agentName,agentSourceid,agentAvlstatus,agentDelstatus,agentCreatetime,agentModifiedtime,userId,userName,userLoginname,userMobile) VALUES ('"+agentId+"','"+agentName+"','"+agentSourceid+"','"+agentAvlstatus+"','"+agentDelstatus+"','"+agentCreatetime+"','"+agentModifiedtime+"','"+userId+"','"+userName+"','"+userLoginnname+"','"+userMobile+"')").append(";\n");
			
			
			
		}
		
		System.out.println(sql_uc);
		System.out.println(sql_sl);
		
	}
}
