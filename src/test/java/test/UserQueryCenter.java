package test;

import java.io.File;
import java.io.FileOutputStream;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.nobject.common.fan.datagen.RandomGen;

public class UserQueryCenter {
	
	public List users=new LinkedList();
	
	public void load(){
		
		int count=1000*10000;
		for (int i = 0; i < count; i++) {
			Map user=new HashMap();
			user.put("userId"		, "USER"+i);
			user.put("userLoginname", "USER"+i);
			user.put("userName"		, "USER"+i);
			users.add(user);
			if(i%10000==0){
				System.out.println((i+0d)/count);
			}
		}
	}
	
	public static void main2(String[] args) throws Exception {
		
		UserQueryCenter center=new UserQueryCenter();
		center.load();
		
		while(true){
			
			long start=System.currentTimeMillis();
			int idx=RandomGen.random.nextInt(1000*10000);
			for (int i = 0; i < center.users.size(); i++) {
				Map user=(Map)center.users.get(i);
				if(user.get("userId").equals("USER"+idx)){
					break;
				}
			}
			System.out.println("cost:"+(System.currentTimeMillis()-start));
			Thread.sleep(1000);
			
		}
		
	}
	
	public static void main(String[] args) throws Exception {
			
		int count=1000*10000;
		FileOutputStream fos=new FileOutputStream(new File("c:/user.txt"));
		for (int i = 0; i < count; i++) {
			Map user=new HashMap();
			user.put("userId"		, "USER"+i);
			user.put("userLoginname", "USER"+i);
			user.put("userName"		, "USER"+i);
			fos.write((user.toString()+"\n").getBytes());
			if(i%10000==0){
				System.out.println((i+0d)/count);
			}
			fos.flush();
		}
		fos.close();
		
	}
	
}
