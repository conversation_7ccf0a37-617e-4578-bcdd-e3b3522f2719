#Persisted by DefaultConfig
#Sat Mar 26 17:30:12 CST 2022
content=server\:\r\n  port\: 8000\r\n  servlet\:\r\n    context-path\: /usercenter\r\nspring\:\r\n  application\:\r\n    name\: usercenter\r\n  datasource\:\r\n    dynamic\:\r\n      hikari\:\r\n        initSize\: 1\r\n        maxSize\: 50\r\n        maxTimeout\: 10000\r\n        maxIdleTime\: 60000\r\n      datasource\:\r\n        uc_master\:\r\n          driverClassName\: com.mysql.jdbc.Driver\r\n          url\: jdbc\:mysql\://dev-ms.data\:3306/usercenter?characterEncoding\=UTF-8&zeroDateTimeBehavior\=convertToNull&useSSL\=false&rewriteBatchedStatements\=true\r\n          username\: db_writer\r\n          password\: 123456\r\n          testInterval\: 10000\r\n          testQuery\: select 1\r\n        uc_slave\:\r\n          url\: jdbc\:mysql\://dev-ms.data\:3306/usercenter?characterEncoding\=UTF-8&zeroDateTimeBehavior\=convertToNull&useSSL\=false&rewriteBatchedStatements\=true\r\n          username\: db_reader\r\n          password\: 123456\r\n          testInterval\: 10000\r\n          testQuery\: select 1\r\n        bd_master\:\r\n          driverClassName\: com.mysql.jdbc.Driver\r\n          url\: jdbc\:mysql\://dev-ms.data\:3306/basedata?characterEncoding\=UTF-8&zeroDateTimeBehavior\=convertToNull&useSSL\=false&rewriteBatchedStatements\=true\r\n          username\: db_writer\r\n          password\: 123456\r\n          testInterval\: 10000\r\n          testQuery\: select 1\r\n  mq\:\r\n    rabbit\:\r\n      client\:\r\n        uc\:\r\n          host\: dev-ms.data\r\n          port\: 5672\r\n          group\: usercenter-\r\n          username\: username\r\n          password\: password\r\n          consumer\:\r\n            channel\:\r\n              basicQos\: 10\r\n          connection\:\r\n            connectionTimeout\: 10000\r\n        crm\:\r\n          host\: dev-ms.data\r\n          port\: 5672\r\n          group\: usercenter\r\n          username\: username\r\n          password\: password\r\n          consumer\:\r\n            channel\:\r\n              basicQos\: 10\r\n          connection\:\r\n            connectionTimeout\: 10000\r\n  redis\:\r\n    multi\:\r\n      uc\:\r\n        primary\: true\r\n        host\: dev-ms.data\r\n        port\: 6379\r\n        password\: 123456\r\n        timeout\: 2000\r\n        lettuce\:\r\n          pool\:\r\n            max-idle\: 8\r\n            min-idle\: 2\r\n            max-active\: 8\r\n            max-wait\: 2000\r\n      uk\:\r\n        host\: **************\r\n        port\: 6380\r\n        password\: redis2017\r\n        timeout\: 2000\r\n        lettuce\:\r\n          pool\:\r\n            max-idle\: 8\r\n            min-idle\: 2\r\n            max-active\: 8\r\n            max-wait\: 2000\r\n  data\:\r\n    mongodb\:\r\n      uri\: mongodb\://root\:zpaCCfcOPiBM@**************\:10011/logcenter\r\n      auto-index-creation\: false\r\n\# 日志配置\r\nlogging\:\r\n  file\:\r\n    name\: /data/glusterfs/logs/usercenter.log\r\n  path\: classpath\:logback-spring.xml\r\n  pattern\:\r\n    file\: "[%d{yyyy-MM-dd HH\:mm\:ss.SSS}][%-5level]%X{apilog}%msg%n"\r\n  level\:\r\n    com.niceloo.mq\: DEBUG\r\n    MQ_CLIENT_MONITOR\: WARN\r\n\r\nmybatis-plus\:\r\n  mapper-locations\: classpath*\:/mapper/**/*.xml\r\n  typeAliasesPackage\: com.niceloo.uc.entity\r\n  check-config-location\: true\r\n  configuration\:\r\n    call-setters-on-nulls\: true\r\nid-client\:\r\n  service-code\: UC\r\n  tags\:\r\n    - BdDpt\r\n    - BdDptee\r\n    - BdEdutype\r\n    - BdEe\r\n    - BdPersonalinfo\r\n    - BdRecvaddr\r\n    - BdSchool\r\n    - UcCltgray\r\n    - UcClttype\r\n    - UcCltver\r\n    - UcConfig\r\n    - UcCustomform\r\n    - UcDatapolicy\r\n    - UcDevice\r\n    - UcDict\r\n    - UcLoginip\r\n    - UcMenu\r\n    - UcOperationlog\r\n    - UcPrivacycount\r\n    - UcPwrecord\r\n    - UcRole\r\n    - UcRolemenu\r\n    - UcUser\r\n    - UcUserauth\r\n    - UcUserbrand\r\n    - UcUserregistctype\r\n    - UcUserrelation\r\n    - UcUserrole\r\n    - UcUsertag\r\n  id-sever-address\: http\://id-server-dev.kube.com/id-server \r\n
