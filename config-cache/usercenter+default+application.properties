#Persisted by DefaultConfig
#Sat Mar 26 17:30:13 CST 2022
mongo.maxIdleTime=600000
jdbc.uc_write.password=123456
jdbc.uc_write.showsql=true
jdbc.fd_read.username=db_reader
jdbc.crm_read.initSize=1
jdbc.uc_read.username=db_reader
jdbc.crm_read.maxTimeout=10000
jdbc.fd_read.maxTimeout=10000
jdbc.crm_write.username=db_writer
jdbc.crm_read.driverClassName=org.gjt.mm.mysql.Driver
mq.crm.password=password
cr.url=http\://corpservice/corpservice
tx.manager.server=http\://dev-ms.app\:9810/servicemanager/
ed.mobile=api/ex/mobile/attribute
regist.server.carenames=all
mongo.initSize=0
jdbc.fd_read.driverClassName=org.gjt.mm.mysql.Driver
server.http.port=8000
ed.identify=api/ex/idcard/identify
jdbc.crm_read.url=jdbc\:mysql\://dev-ms.data\:3306/test1
ed.address=http\://externaldata/externaldata
jdbc.ipg_read.maxSize=10
uk.iteration=20
cas.enc.iteration=20
mq.connectionTimeout=10000
jdbc.crm_read.username=db_reader
stb.source.id=356
mongo.maxTimeout=120000
tx.enable=true
fs.url=http\://fileservice/fileservice
uk.fire_key=hrh5g72m
jdbc.uc_read.maxIdleTime=60000
regist.center.url=dev-ms.app\:9811
jdbc.crm_write.url=jdbc\:mysql\://dev-ms.data\:3306/test1
mq.type=rabbitmq
jdbc.crm_write.initSize=1
jdbc.crm_write.maxIdleTime=60000
sb.url=http\://studentbase/studentbase
jdbc.uc_read.maxSize=50
net.address=http\://**************\:8056
cache.redis.host=dev-ms.data
mq.password=password
jdbc.uc_read.url=jdbc\:mysql\://dev-ms.data\:3306/usercenter?characterEncoding\=UTF-8&zeroDateTimeBehavior\=convertToNull&useSSL\=false
bd.url=http\://basedata/basedata
uk.android_salt=h1234v8rbILoBYpQ
log.write.enable=true
jdbc.fd_read.testQuery=select 1
jdbc.uc_write.driverClassName=org.gjt.mm.mysql.Driver
cache.type=redis
uk.ios_key=Kdu3KL921GcmKweSjvlfpksdnjfhzo423532
crm.rsa.public_key=<RSAKeyValue><Modulus>s0FcazUQRuVzaCoqIvMnqJtBrdIIS3YyjAiyEamoQQuIKvfzo0x4RaT1f8zeaoqFL0hBbT50X5005zFmgt8VEqtrBhmQ46Fbi7ihOopHPHmmYoUOK+3i7qMf3IxCwSXWV1EcYCZdbCXHbvt6QrU7sbmpoqmCwrYIvax8Pz8cuQc\=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>
crm.rsa.private_key=<RSAKeyValue><Modulus>s0FcazUQRuVzaCoqIvMnqJtBrdIIS3YyjAiyEamoQQuIKvfzo0x4RaT1f8zeaoqFL0hBbT50X5005zFmgt8VEqtrBhmQ46Fbi7ihOopHPHmmYoUOK+3i7qMf3IxCwSXWV1EcYCZdbCXHbvt6QrU7sbmpoqmCwrYIvax8Pz8cuQc\=</Modulus><Exponent>AQAB</Exponent><P>8tLX0I4quQ4Lf0G2IURPSEe+REhbzXzl7oqXHBkx7OueGZppczV3a7qj0G4rZDWtRm1UzYdLQCCmmSg1qt6kwQ\=\=</P><Q>vPt3WQXbuoiNmsc11ZZ+0oPxK2UAK6sdzRMasv7+LkfKUFoaM/J/ay/8DcT8LD9xi6AnsgIZZDvuGgUG8wFnxw\=\=</Q><DP>s7L9K8ZKL7EJKSAEHRJkaeVOFVJHPgcUUjgWJCExJVgpJGF1cGE9A2iYGoOtcPeYR+pZO9DIC6keIXH0ZYNnAQ\=\=</DP><DQ>Tw0JcW2n6jEwuqvV8xBO4JRcj4BiP+IGR0BUjMdpX8ab9FycVVp8vkOGNeROb9viDTvjHu0N3gXLpYqwy3mtBQ\=\=</DQ><InverseQ>TbK4r5DXt2UwtH1Ov+eAk6LkCwE0MOFOIyF6q0kwayd+tLmNwYR9FzRJMaLj2nQjbxYN2xrjNRvj4W08AA/ApA\=\=</InverseQ><D>YYaGCa3tFSVxDJMkKa+xPAJ8UV2cNbIw/g8dEo8Dg6AKjZYpcAcCyKFfEBEeUUjb/UWKky0Pk5WsWrx90WYdyAec08ruAEgOhxHSO63pqILi6dwPuBSmPXeHUklpB1CKJQCOp2BubUxixixfq1zuzK5GPl1H1u42hHRfkEa5BAE\=</D></RSAKeyValue>
jdbc.crm_read.password=123456
jdbc.crm_write.maxTimeout=10000
cr.corpuser_add=api/cs/corpuser/add
net.user.ukey=api/users/ukey
net.uk.address=http\://**************\:8056/api/users/ukey
redis.uk.maxTotal=10
jdbc.uc_read.testQuery=select 1
mq.channel.basicQos=10
esearch.password=123456
jdbc.bd_write.testInterval=5000
jdbc.uc_write.testQuery=select 1
passwd.encrypkey=yl2019\#$
esearch.url=http\://dev-ms.data\:9200
jdbc.uc_write.maxTimeout=10000
jdbc.bd_write.maxSize=50
mq.crm.host=dev-ms.data
net.user.pwd_edit=api/users/password/edit
jdbc.fd_read.testInterval=10000
jdbc.fd_read.initSize=1
redis.uk.password=redis2017
uk.client_key=sdnjfhzo423KKdupkL921GcmKweSjvlf3532
jdbc.uc_read.initSize=1
jdbc.crm_write.unsMillis=60000
uk.ios_salt=hQboK8B2vY3rLp2I
redis.uk.port=6380
uk.android_key=mKweSjdu3Kp678KLsdlfoPKiumjvghz98512
jdbc.fd_read.showsql=true
jdbc.crm_read.showsql=true
mongo.url=mongodb\://root\:zpaCCfcOPiBM@**************\:10011/
uk.client_iteration=20
jdbc.fd_read.url=jdbc\:mysql\://dev-ms.data\:3306/usercenter
crm.api.modify_Information=api/customers/Microservice/ModifyInformation
jdbc.crm_read.maxSize=50
jdbc.bd_write.initSize=1
uk.rediskey=Niceloo_UCenter
sl.url=http\://siteleague/siteleague
uk.salt=hY3rLpQboK8B2v2I
mq.host=dev-ms.data
jdbc.uc_read.driverClassName=org.gjt.mm.mysql.Driver
mongo.basePackages=com/niceloo/*/model/mongo/orm/**
redis.uk.host=**************
jdbc.ipg_read.password=oOVMu34K9T\#A0Li
uk.client_salt=hY3rLpQboK8B2v2I
mq.crm.connectionTimeout=10000
stb.project.id=5a68167c-afdd-4de8-9fe9-9827f20ba89e
jdbc.ipg_read.showsql=true
net.user.info=api/User/GetUserInfo
jdbc.uc_write.maxIdleTime=60000
jdbc.bd_write.driverClassName=org.gjt.mm.mysql.Driver
jdbc.crm_write.driverClassName=org.gjt.mm.mysql.Driver
lock.enable=true
jdbc.ipg_read.maxTimeout=10000
uk.android_iteration=10
jdbc.bd_write.username=db_writer
mongo.maxSize=100
jdbc.crm_read.unsMillis=60000
cas.expire.time=86400
jdbc.uc_write.username=db_writer
jdbc.bd_write.showsql=true
uk.key=sdnjfhzo423KKdupkL921GcmKweSjvlf3532
net.user.regist=api/users/register
jdbc.fd_read.maxSize=50
jdbc.ipg_read.maxIdleTime=60000
mq.crm.channel.basicQos=10
jdbc.ipg_read.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
tx.commit_timeout=30000
aes.key=sdnjfhzo423KKdup
jdbc.crm_write.password=123456
cas.login.name=caskeyframe
jdbc.crm_write.maxSize=50
mq.crm.username=username
uk.ios_iteration=10
stb.register.url=http\://www.niceloo.com/UserNormalAjax.ashx?Action\=region
jdbc.ipg_read.url=jdbc\:sqlserver\://************\:1433;databaseName\=OCULAR3
cas.enc.salt=ES9nNJZ9kUxzCcG7
mq.crm.port=5672
jdbc.bd_write.testQuery=SELECT 1
mongo.maxWaitTime=120000
net.user.recvaddr_count=99
uc-ex.url=http\://usercenter-ex/usercenter-ex
jdbc.uc_write.url=jdbc\:mysql\://dev-ms.data\:3306/usercenter?characterEncoding\=UTF-8&zeroDateTimeBehavior\=convertToNull&useSSL\=false&rewriteBatchedStatements\=true
mq.port=5672
jdbc.uc_write.initSize=1
jdbc.ipg_read.initSize=1
mq.crm.type=rabbitmq
sys.msu.address=http\://**************\:7000
uk.sync.user.address=http\://**************\:8056/api/User/GetUserInfo
stb.encrypt=1
esearch.username=my_admin2
jdbc.ipg_read.username=bigdataReader
regist.server.ip=dev-ms.app
jdbc.bd_write.url=jdbc\:mysql\://dev-ms.data\:3306/basedata?characterEncoding\=UTF-8&zeroDateTimeBehavior\=convertToNull&useSSL\=false
mongo.autoIndex=false
jdbc.bd_write.password=123456
stb.behavior.action=0
jdbc.uc_write.testInterval=10000
lock.key.prefix=_userlock_
regist.server.name=usercenter
aes.vi=hY3rLpQboK8B2v2I
mq.crm.exchange.name=marketing
jdbc.uc_write.maxSize=50
stb.register.enable=true
cr.corpuser_edit=api/cs/corpuser/edit
fd.url=http\://fundsystem/fundsystem
jdbc.crm_write.showsql=true
cache.redis.password=123456
mq.username=username
jdbc.fd_read.maxIdleTime=60000
jdbc.uc_read.testInterval=10000
jdbc.fd_read.password=123456
crm.address=http\://**************\:8010
jdbc.uc_read.password=123456
cas.enc.key=PUSvITe89DzGHtuU9J0NsAgRvjoki3cl
jdbc.crm_read.maxIdleTime=60000
cache.redis.port=6379
jdbc.uc_read.maxTimeout=10000
jdbc.bd_write.maxTimeout=10000
jdbc.uc_read.showsql=true
mongo.db=logcenter
