# 选择tomcat
FROM **************:12345/tomcat:8.5-jre8
RUN rm -rf /usr/local/tomcat/webapps/*
ADD ./target/1.0.0-SNAPSHOT/usercenter.war /usr/local/tomcat/webapps/ROOT.war
RUN bash -c 'touch /usr/local/tomcat/webapps/ROOT.war'
EXPOSE 8080
ENTRYPOINT ["catalina.sh", "run"]
# 命令行
# 构建 docker build -t **************:12345/usercenter:1.0.0 .
# 测试运行 docker run --net=host **************:12345/usercenter:1.0.0 /bin/bash
# 进入bash docker exec -it [container_id] bash
# 后台运行(开发) docker run -d --net=host **************:12345/usercenter:1.0.0
